System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, misc, JsonAsset, MoveBase, PathData, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _crd, degreesToRadians, radiansToDegrees, ccclass, property, executeInEditMode, PathMove;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _reportPossibleCrUseOfMoveBase(extras) {
    _reporterNs.report("MoveBase", "./IMovable", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathData(extras) {
    _reporterNs.report("PathData", "../data/PathData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathPoint(extras) {
    _reporterNs.report("PathPoint", "../data/PathData", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      misc = _cc.misc;
      JsonAsset = _cc.JsonAsset;
    }, function (_unresolved_2) {
      MoveBase = _unresolved_2.MoveBase;
    }, function (_unresolved_3) {
      PathData = _unresolved_3.PathData;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "0793c9EHOFKg6LLrnZPLKct", "PathMove", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Enum', 'misc', 'Node', 'UITransform', 'Vec2', 'Vec3', 'JsonAsset']);

      ({
        degreesToRadians,
        radiansToDegrees
      } = misc);
      ({
        ccclass,
        property,
        executeInEditMode
      } = _decorator);

      _export("PathMove", PathMove = (_dec = ccclass('PathMove'), _dec2 = executeInEditMode(), _dec3 = property({
        type: JsonAsset,
        displayName: "路径数据(预览用)"
      }), _dec4 = property({
        displayName: "循环移动"
      }), _dec5 = property({
        displayName: "反向移动"
      }), _dec6 = property({
        displayName: "振荡偏移速度",
        tooltip: "控制倾斜振荡的频率"
      }), _dec7 = property({
        displayName: "振荡偏移幅度",
        tooltip: "控制倾斜振荡的幅度"
      }), _dec(_class = _dec2(_class = (_class2 = class PathMove extends (_crd && MoveBase === void 0 ? (_reportPossibleCrUseOfMoveBase({
        error: Error()
      }), MoveBase) : MoveBase) {
        constructor(...args) {
          super(...args);
          this._pathAsset = null;

          _initializerDefineProperty(this, "loop", _descriptor, this);

          _initializerDefineProperty(this, "reverse", _descriptor2, this);

          _initializerDefineProperty(this, "tiltSpeed", _descriptor3, this);

          _initializerDefineProperty(this, "tiltOffset", _descriptor4, this);

          // 路径相关数据
          this._pathData = null;
          this._subdivided = [];
          // 细分后的路径点（包含完整信息）
          // 移动状态
          this._currentPointIndex = 0;
          // 当前所在的细分点索引
          this._nextPointIndex = 0;
          this._segmentT = 0;
          // 在当前段内的插值参数 [0,1]
          this._tiltTime = 0;
          // 停留状态
          this._isStaying = false;
          // 是否正在停留
          this._stayTimer = 0;
          // 停留计时器（秒）
          this._stayPointIndex = -1;
          // 停留的点索引
          this._updateInEditor = false;
        }

        get pathAsset() {
          return this._pathAsset;
        }

        set pathAsset(value) {
          this._pathAsset = value;

          if (value) {
            this.setPath((_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
              error: Error()
            }), PathData) : PathData).fromJSON(value.json));
          }
        }

        onFocusInEditor() {
          this._updateInEditor = true;
          this._isMovable = true;
        }

        onLostFocusInEditor() {
          this._updateInEditor = false;
          this._isMovable = false;
          this.resetToStart();
        }

        update(dt) {
          if (this._updateInEditor) {
            this.tick(dt);
          }
        }
        /**
         * 加载路径数据（使用新的细分点方法）
         */


        setPath(pathData) {
          this._pathData = pathData; // 使用新的细分点方法，直接获取包含完整信息的PathPoint数组

          this._subdivided = this._pathData.getSubdividedPoints();
        }
        /**
         * 主要的移动更新逻辑
         */


        tick(dt) {
          if (!this._isMovable) return;

          if (this._nextPointIndex !== this._currentPointIndex) {
            this.tickMovement(dt);
          }

          this.tickTilting(dt);
        }

        tickMovement(dt) {}

        tickTilting(dt) {}

        setNext(pathPointIndex) {}

      }, (_applyDecoratedDescriptor(_class2.prototype, "pathAsset", [_dec3], Object.getOwnPropertyDescriptor(_class2.prototype, "pathAsset"), _class2.prototype), _descriptor = _applyDecoratedDescriptor(_class2.prototype, "loop", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return false;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "reverse", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return false;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "tiltSpeed", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "tiltOffset", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      })), _class2)) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=794ce2bb3f0615a636a10a8c3f025e15162051e7.js.map