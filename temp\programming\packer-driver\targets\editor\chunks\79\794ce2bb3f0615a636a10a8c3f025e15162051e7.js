System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, misc, Enum, JsonAsset, eOrientationType, PathData, DefaultMove, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _class, _class2, _descriptor, _descriptor2, _class3, _crd, degreesToRadians, radiansToDegrees, ccclass, property, executeInEditMode, PathMove;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _reportPossibleCrUseOfeOrientationType(extras) {
    _reporterNs.report("eOrientationType", "./IMovable", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathData(extras) {
    _reporterNs.report("PathData", "../data/PathData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathPoint(extras) {
    _reporterNs.report("PathPoint", "../data/PathData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDefaultMove(extras) {
    _reporterNs.report("DefaultMove", "./DefaultMove", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      misc = _cc.misc;
      Enum = _cc.Enum;
      JsonAsset = _cc.JsonAsset;
    }, function (_unresolved_2) {
      eOrientationType = _unresolved_2.eOrientationType;
    }, function (_unresolved_3) {
      PathData = _unresolved_3.PathData;
    }, function (_unresolved_4) {
      DefaultMove = _unresolved_4.DefaultMove;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "0793c9EHOFKg6LLrnZPLKct", "PathMove", undefined);

      __checkObsolete__(['_decorator', 'Component', 'misc', 'Enum', 'Node', 'UITransform', 'Vec2', 'Vec3', 'JsonAsset']);

      ({
        degreesToRadians,
        radiansToDegrees
      } = misc);
      ({
        ccclass,
        property,
        executeInEditMode
      } = _decorator);

      _export("PathMove", PathMove = (_dec = ccclass('PathMove'), _dec2 = executeInEditMode(), _dec3 = property({
        type: JsonAsset,
        displayName: "路径数据(预览用)"
      }), _dec4 = property({
        displayName: "循环移动"
      }), _dec5 = property({
        displayName: "反向移动"
      }), _dec6 = property({
        type: Enum(_crd && eOrientationType === void 0 ? (_reportPossibleCrUseOfeOrientationType({
          error: Error()
        }), eOrientationType) : eOrientationType),
        displayName: "朝向类型"
      }), _dec7 = property({
        displayName: "朝向参数"
      }), _dec(_class = _dec2(_class = (_class2 = (_class3 = class PathMove extends (_crd && DefaultMove === void 0 ? (_reportPossibleCrUseOfDefaultMove({
        error: Error()
      }), DefaultMove) : DefaultMove) {
        constructor(...args) {
          super(...args);
          this._pathAsset = null;

          _initializerDefineProperty(this, "loop", _descriptor, this);

          _initializerDefineProperty(this, "reverse", _descriptor2, this);

          // 路径相关数据
          this._pathData = null;
          this._subdivided = [];
          // 细分后的路径点（包含完整信息）
          // 路径偏移
          this._offsetX = 0;
          this._offsetY = 0;
          // 移动状态
          this._currentPointIndex = 0;
          // 当前所在的细分点索引
          this._nextPointIndex = 0;
          this._remainDistance = 0;
          // 距离下一个点的剩余距离
          // 停留状态
          this._stayTimer = 0;
          // 停留计时器（秒）
          this._updateInEditor = false;
        }

        get pathAsset() {
          return this._pathAsset;
        }

        set pathAsset(value) {
          this._pathAsset = value;

          if (value) {
            this.setPath((_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
              error: Error()
            }), PathData) : PathData).fromJSON(value.json));
          }
        }

        get editor_orientationType() {
          return this.orientationType;
        }

        set editor_orientationType(value) {
          this.setOrientation(value, this.editor_orientationParam);
        }

        get editor_orientationParam() {
          return this.orientationParam;
        }

        set editor_orientationParam(value) {
          this.setOrientation(this.orientationType, value);
        }

        onFocusInEditor() {
          this._updateInEditor = true;
          this._isMovable = true;
        }

        onLostFocusInEditor() {
          this._updateInEditor = false;
          this._isMovable = false;
          this.resetToStart();
        }

        update(dt) {
          if (this._updateInEditor) {
            this.tick(dt);
          }
        } // 注意调用顺序,先调用setOffset,再调用setPath


        setOffset(x, y) {
          this._offsetX = x;
          this._offsetY = y;
          return this;
        }

        setPath(pathData) {
          this._pathData = pathData; // 使用新的细分点方法，直接获取包含完整信息的PathPoint数组

          this._subdivided = this._pathData.getSubdividedPoints();
          this.resetToStart();
          return this;
        }
        /**
         * 主要的移动更新逻辑
         */


        tick(dt) {
          if (!this._isMovable) return;

          if (!this._pathData) {
            super.tick(dt);
            return;
          } // 处理停留逻辑


          if (this._stayTimer > 0) {
            this._stayTimer -= dt;

            if (this._stayTimer <= 0) {
              this._stayTimer = 0; // 停留结束，继续移动到下一个点

              this.moveToNextPoint();
            }
          } else if (this._nextPointIndex !== this._currentPointIndex) {
            this.tickMovement(dt);
          }

          this.updateTilting(this.speedAngle, dt, this._position); // 设置节点位置

          this.node.setPosition(this._position);
          this.checkVisibility(); // 更新朝向

          this.updateOrientation(dt);
        }

        tickMovement(dt) {
          // 使用匀加速直线运动更新位置
          const v0 = this.speed; // s = v0*t + 0.5*a*t^2

          const s = v0 * dt + 0.5 * this.acceleration * dt * dt;
          this.speed += this.acceleration * dt; // console.log(`v0: ${v0.toFixed(2)}, a: ${this.acceleration.toFixed(2)}, s: ${s.toFixed(2)}`);
          // 计算移动向量

          const angleRad = this.speedAngle;
          const deltaX = Math.cos(angleRad) * s;
          const deltaY = Math.sin(angleRad) * s; // 更新位置

          this._position.x += deltaX;
          this._position.y += deltaY; // 检查是否到达目标点

          if (this._remainDistance > 0) {
            this._remainDistance -= s;

            if (this._remainDistance <= 0) {
              this.onReachPoint(this._nextPointIndex);
            }
          }
        }

        updateOrientation(dt) {
          if (this.orientationType === (_crd && eOrientationType === void 0 ? (_reportPossibleCrUseOfeOrientationType({
            error: Error()
          }), eOrientationType) : eOrientationType).Path) {
            // PathMove这里speedAngle是radians
            this.orientation = radiansToDegrees(PathMove.lerp(degreesToRadians(this.orientation), this.speedAngle, PathMove.kLerpFactor, dt));
            this.node.setRotationFromEuler(0, 0, this.orientation + this.forwardOrientation);
            return;
          }

          super.updateOrientation(dt);
        }

        onReachPoint(pointIndex) {
          // 更新当前点索引
          this._currentPointIndex = pointIndex; // 检查是否需要停留

          const currentPoint = this.getPathPoint(pointIndex);

          if (currentPoint) {
            this._basePosition.x = currentPoint.x + this._offsetX;
            this._basePosition.y = currentPoint.y + this._offsetY;

            this._position.set(this._basePosition);

            this.node.setPosition(this._position); // 设置初始速度

            this.speed = currentPoint.speed; // console.log(`到达点 ${pointIndex}, 停留时间: ${currentPoint.stayDuration}ms`);

            if (currentPoint.stayDuration > 0) {
              this._stayTimer = currentPoint.stayDuration / 1000.0;
              return;
            }
          } // 继续移动到下一个点


          this.moveToNextPoint();
        }

        moveToNextPoint() {
          const nextIndex = this._currentPointIndex + 1;

          if (nextIndex >= this._subdivided.length) {
            // 到达路径终点
            if (this.loop) {
              // 循环模式，回到起点
              this.setNext(0);
            } else {
              // 停止移动
              this._nextPointIndex = this._currentPointIndex;
            }
          } else {
            // 移动到下一个点
            this.setNext(nextIndex);
          }
        }

        setNext(pathPointIndex) {
          this._nextPointIndex = pathPointIndex;
          const currentPoint = this.getPathPoint(this._currentPointIndex);
          const nextPoint = this.getPathPoint(this._nextPointIndex);

          if (currentPoint && nextPoint) {
            const dirX = nextPoint.x - currentPoint.x;
            const dirY = nextPoint.y - currentPoint.y;
            this._remainDistance = Math.sqrt(dirX * dirX + dirY * dirY);

            if (this._remainDistance > 1) {
              // 计算目标移动角度
              this.speedAngle = Math.atan2(dirY, dirX); // 计算加速度：使用匀加速直线运动公式 v1^2 = v0^2 + 2*a*x
              // 解出 a = (v1^2 - v0^2) / (2*x)

              const v0 = currentPoint.speed;
              const v1 = nextPoint.speed;
              this.acceleration = (v1 * v1 - v0 * v0) / (2 * this._remainDistance); // console.log(`设置下一段: ${this._currentPointIndex} -> ${this._nextPointIndex}, 距离: ${this._remainDistance.toFixed(2)}, 初速度: ${v0}, 末速度: ${v1}, 加速度: ${this.acceleration.toFixed(2)}, 角度: ${radiansToDegrees(targetAngle).toFixed(2)}`);
            }
          }
        }

        getPathPoint(pathPointIndex) {
          if (pathPointIndex < 0 || pathPointIndex >= this._subdivided.length) {
            return null;
          }

          return this._subdivided[pathPointIndex];
        }

        resetToStart() {
          this._currentPointIndex = 0;
          this._nextPointIndex = 0;
          this._stayTimer = 0;
          this._tiltTime = 0;
          this.onReachPoint(0);
        }

        isStaying() {
          return this._stayTimer > 0;
        }

        getRemainingStayTime() {
          return this._stayTimer;
        }

        static lerp(a, b, decay, dt) {
          return (a - b) * Math.exp(-decay * dt) + b;
        }

      }, _class3.kLerpFactor = 5, _class3), (_applyDecoratedDescriptor(_class2.prototype, "pathAsset", [_dec3], Object.getOwnPropertyDescriptor(_class2.prototype, "pathAsset"), _class2.prototype), _descriptor = _applyDecoratedDescriptor(_class2.prototype, "loop", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return false;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "reverse", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return false;
        }
      }), _applyDecoratedDescriptor(_class2.prototype, "editor_orientationType", [_dec6], Object.getOwnPropertyDescriptor(_class2.prototype, "editor_orientationType"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "editor_orientationParam", [_dec7], Object.getOwnPropertyDescriptor(_class2.prototype, "editor_orientationParam"), _class2.prototype)), _class2)) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=794ce2bb3f0615a636a10a8c3f025e15162051e7.js.map