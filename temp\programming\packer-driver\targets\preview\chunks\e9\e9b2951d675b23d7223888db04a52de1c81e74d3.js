System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, _crd, win, languages;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "e250cwunxFH2o5Yei+0u5z6", "zh", undefined);

      win = window;

      _export("languages", languages = {
        BTN_REFRESH: "刷 新",
        FIGHT_LV: "LV",
        LOADING_MODE_TIP: "进入{0}模式",
        LOADING_TIP_TITLE: "小TIPS:",
        ROUGE_LEFT_TIMES: "剩余次数：{0}/{1}",
        ROUGE_SKILL_TITLE: "已选技能"
      });

      if (!win.languages) {
        win.languages = {};
      }

      win.languages.zh = languages;

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=e9b2951d675b23d7223888db04a52de1c81e74d3.js.map