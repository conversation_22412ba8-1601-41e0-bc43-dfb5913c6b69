{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelLayerUI.ts"], "names": ["_decorator", "Component", "instantiate", "Node", "UITransform", "view", "MyApp", "LayerSplicingMode", "GameMapRun", "LevelEventRun", "Tools", "GameIns", "ccclass", "TerrainsNodeName", "DynamicNodeName", "ScrollsNodeName", "EmittiersNodeName", "LevelLayerUI", "backgrounds", "_offSetY", "_bTrackBackground", "terrainsNode", "dynamicNode", "scrollsNode", "emittiersNode", "events", "eventRunners", "TrackBackground", "value", "onLoad", "initByLevelData", "data", "offSetY", "node", "setPosition", "_getOrAddNode", "terrains", "for<PERSON>ach", "terrain", "path", "resMgr", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultBundleName", "uuid", "load", "err", "prefab", "console", "error", "terrainNode", "position", "x", "y", "setScale", "scale", "setRotationFromEuler", "rotation", "<PERSON><PERSON><PERSON><PERSON>", "_initDynamicsByLevelData", "_initEmittierLevelData", "_initScorllsByLevelData", "sort", "a", "b", "event", "push", "dynamics", "length", "loadPromises", "index", "dynaNode", "weights", "group", "dynamic", "weight", "dynaIndex", "getRandomIndexByWeights", "battleManager", "random", "terrainIndex", "loadPromise", "Promise", "resolve", "name", "randomOffsetX", "offSetX", "max", "min", "randomOffsetY", "all", "emittiers", "emittier", "emittierNode", "scrolls", "element", "srocllIndex", "scroll", "prefabList", "uuids", "totalHeight", "speed", "totalTime", "posOffsetY", "height", "prefabIndex", "curPrefab", "child", "offY", "splicingMode", "node_height", "getComponent", "contentSize", "fix_height", "random_height", "Math", "tick", "deltaTime", "posY", "topPosY", "getVisibleSize", "scrollY", "VIEWPORT_TOP", "i", "eventRunner", "isTriggered", "splice", "_instantiateTerrain", "node_parent", "getChildByName", "getEventByElemID", "elemID"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;AAAcC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;;AAC/DC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,iB,iBAAAA,iB;;AACFC,MAAAA,U;;AACEC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,O,iBAAAA,O;;;;;;;;;OAEH;AAAEC,QAAAA;AAAF,O,GAAcZ,U;AAEda,MAAAA,gB,GAAmB,U;AACnBC,MAAAA,e,GAAkB,S;AAClBC,MAAAA,e,GAAkB,S;AAClBC,MAAAA,iB,GAAoB,W;;8BAGbC,Y,WADZL,OAAO,CAAC,cAAD,C,gBAAR,MACaK,YADb,SACkChB,SADlC,CAC4C;AAAA;AAAA;AAAA,eACjCiB,WADiC,GACT,EADS;AAAA,eAEhCC,QAFgC,GAEb,CAFa;AAEV;AAFU,eAGhCC,iBAHgC,GAGH,IAHG;AAGG;AAHH,eAKhCC,YALgC,GAKJ,IALI;AAAA,eAMhCC,WANgC,GAML,IANK;AAAA,eAOhCC,WAPgC,GAOL,IAPK;AAAA,eAQhCC,aARgC,GAQH,IARG;AAAA,eAShCC,MATgC,GASL,EATK;AAAA,eAUhCC,YAVgC,GAUA,EAVA;AAAA;;AAYd,YAAfC,eAAe,GAAY;AAClC,iBAAO,KAAKP,iBAAZ;AACH;;AACyB,YAAfO,eAAe,CAACC,KAAD,EAAiB;AACvC,eAAKR,iBAAL,GAAyBQ,KAAzB;AACH;;AAEDC,QAAAA,MAAM,GAAS,CAEd;;AAEYC,QAAAA,eAAe,CAACC,IAAD,EAAuBC,OAAvB,EAAuD;AAAA;;AAAA;AAAA;;AAC/E,YAAA,KAAI,CAACb,QAAL,GAAgBa,OAAhB;;AACA,YAAA,KAAI,CAACC,IAAL,CAAUC,WAAV,CAAsB,CAAtB,EAAyBF,OAAzB,EAAkC,CAAlC;;AAEA,YAAA,KAAI,CAACX,YAAL,GAAoB,KAAI,CAACc,aAAL,CAAmB,KAAI,CAACF,IAAxB,EAA8BpB,gBAA9B,CAApB;AACA,YAAA,KAAI,CAACS,WAAL,GAAmB,KAAI,CAACa,aAAL,CAAmB,KAAI,CAACF,IAAxB,EAA8BnB,eAA9B,CAAnB;AACA,YAAA,KAAI,CAACS,WAAL,GAAmB,KAAI,CAACY,aAAL,CAAmB,KAAI,CAACF,IAAxB,EAA8BlB,eAA9B,CAAnB;AACA,YAAA,KAAI,CAACS,aAAL,GAAqB,KAAI,CAACW,aAAL,CAAmB,KAAI,CAACF,IAAxB,EAA8BjB,iBAA9B,CAArB;AAEA,YAAA,KAAI,CAACE,WAAL,GAAmB,EAAnB;AAEA,8BAAAa,IAAI,CAACK,QAAL,4BAAeC,OAAf,CAAwBC,OAAD,IAAa;AAChC,kBAAMC,IAAI,GAAG;AAAA;AAAA,kCAAMC,MAAN,CAAaC,YAAb,CAA0B;AAAA;AAAA,kCAAMD,MAAN,CAAaE,iBAAvC,EAA0DJ,OAAO,CAACK,IAAlE,CAAb;AACA;AAAA;AAAA,kCAAMH,MAAN,CAAaI,IAAb,CAAkBL,IAAlB,EAAwB,CAACM,GAAD,EAAoBC,MAApB,KAAuC;AAC3D,oBAAID,GAAJ,EAAS;AACLE,kBAAAA,OAAO,CAACC,KAAR,CAAc,cAAd,EAA8B,0CAA9B,EAA0EH,GAA1E;AACA;AACH;;AACD,oBAAII,WAAW,GAAG/C,WAAW,CAAC4C,MAAD,CAA7B;AACAG,gBAAAA,WAAW,CAACf,WAAZ,CAAwBI,OAAO,CAACY,QAAR,CAAiBC,CAAzC,EAA4Cb,OAAO,CAACY,QAAR,CAAiBE,CAA7D,EAAgE,CAAhE;AACAH,gBAAAA,WAAW,CAACI,QAAZ,CAAqBf,OAAO,CAACgB,KAAR,CAAcH,CAAnC,EAAsCb,OAAO,CAACgB,KAAR,CAAcF,CAApD,EAAuD,CAAvD;AACAH,gBAAAA,WAAW,CAACM,oBAAZ,CAAiC,CAAjC,EAAoC,CAApC,EAAuCjB,OAAO,CAACkB,QAA/C;;AACA,gBAAA,KAAI,CAACnC,YAAL,CAAmBoC,QAAnB,CAA4BR,WAA5B;AACH,eAVD;AAWH,aAbD;AAeA,kBAAM,KAAI,CAACS,wBAAL,CAA8B3B,IAA9B,CAAN;AACA,kBAAM,KAAI,CAAC4B,sBAAL,CAA4B5B,IAA5B,CAAN;AACA,kBAAM,KAAI,CAAC6B,uBAAL,CAA6B7B,IAA7B,CAAN;AAEA,YAAA,KAAI,CAACN,MAAL,GAAc,CAAC,GAAGM,IAAI,CAACN,MAAT,CAAd;;AACA,YAAA,KAAI,CAACA,MAAL,CAAYoC,IAAZ,CAAiB,CAACC,CAAD,EAAIC,CAAJ,KAAUD,CAAC,CAACZ,QAAF,CAAWE,CAAX,GAAeW,CAAC,CAACb,QAAF,CAAWE,CAArD;;AACA,YAAA,KAAI,CAAC3B,MAAL,CAAYY,OAAZ,CAAqB2B,KAAD,IAAW;AAC3B,cAAA,KAAI,CAACtC,YAAL,CAAkBuC,IAAlB,CAAuB;AAAA;AAAA,kDAAkBD,KAAlB,EAAyB,KAAzB,CAAvB;AACH,aAFD;AAhC+E;AAmClF;;AAEaN,QAAAA,wBAAwB,CAAC3B,IAAD,EAAsC;AAAA;;AAAA;AAAA;;AACxE,gBAAI,CAACA,IAAD,IAAS,MAAI,CAACT,WAAL,KAAqB,IAA9B,IAAsCS,IAAI,CAACmC,QAAL,CAAcC,MAAd,KAAyB,CAAnE,EAAsE;AAAE;AAAS;;AAEjF,gBAAMC,YAA6B,GAAG,EAAtC;AACA,8BAAArC,IAAI,CAACmC,QAAL,4BAAe7B,OAAf,CAAuB,CAAC6B,QAAD,EAAUG,KAAV,KAAoB;AACvC,kBAAIC,QAAQ,GAAG,MAAI,CAACnC,aAAL,CAAmB,MAAI,CAACb,WAAxB,YAA8C+C,KAA9C,CAAf;;AACA,kBAAIE,OAAiB,GAAG,EAAxB;AACAL,cAAAA,QAAQ,CAACM,KAAT,CAAenC,OAAf,CAAwBoC,OAAD,IAAa;AAChCF,gBAAAA,OAAO,CAACN,IAAR,CAAaQ,OAAO,CAACC,MAArB;AACH,eAFD;AAGA,kBAAMC,SAAS,GAAG;AAAA;AAAA,kCAAMC,uBAAN,CAA8BL,OAA9B,EAAuC;AAAA;AAAA,sCAAQM,aAAR,CAAsBC,MAAtB,EAAvC,CAAlB;AACA,kBAAML,OAAO,GAAGP,QAAQ,CAACM,KAAT,CAAeG,SAAf,CAAhB;AAEAL,cAAAA,QAAQ,CAACpC,WAAT,CAAqBuC,OAAO,CAACvB,QAAR,CAAiBC,CAAtC,EAAyCsB,OAAO,CAACvB,QAAR,CAAiBE,CAA1D,EAA6D,CAA7D;AACAkB,cAAAA,QAAQ,CAACjB,QAAT,CAAkBoB,OAAO,CAACnB,KAAR,CAAcH,CAAhC,EAAmCsB,OAAO,CAACnB,KAAR,CAAcF,CAAjD,EAAoD,CAApD;AACAkB,cAAAA,QAAQ,CAACf,oBAAT,CAA8B,CAA9B,EAAiC,CAAjC,EAAoCkB,OAAO,CAACjB,QAA5C;AAEAe,cAAAA,OAAO,GAAG,EAAV;AACAE,cAAAA,OAAO,CAACrC,QAAR,CAAiBC,OAAjB,CAA0BC,OAAD,IAAa;AAClCiC,gBAAAA,OAAO,CAACN,IAAR,CAAa3B,OAAO,CAACoC,MAArB;AACH,eAFD;AAGA,kBAAMK,YAAY,GAAG;AAAA;AAAA,kCAAMH,uBAAN,CAA8BL,OAA9B,EAAuC;AAAA;AAAA,sCAAQM,aAAR,CAAsBC,MAAtB,EAAvC,CAArB;AACA,kBAAMxC,OAAO,GAAGmC,OAAO,CAACrC,QAAR,CAAiB2C,YAAjB,CAAhB;AAEA,kBAAMC,WAAW,GAAG,IAAIC,OAAJ,CAAmBC,OAAD,IAAa;AAC/C,oBAAM3C,IAAI,GAAG;AAAA;AAAA,oCAAMC,MAAN,CAAaC,YAAb,CAA0B;AAAA;AAAA,oCAAMD,MAAN,CAAaE,iBAAvC,EAA0DJ,OAAO,CAACK,IAAlE,CAAb;AACA;AAAA;AAAA,oCAAMH,MAAN,CAAaI,IAAb,CAAkBL,IAAlB,EAAwB,CAACM,GAAD,EAAoBC,MAApB,KAAuC;AAC3D,sBAAID,GAAJ,EAAS;AACLE,oBAAAA,OAAO,CAACC,KAAR,CAAc,4DAAd,EAA4EH,GAA5E;AACAqC,oBAAAA,OAAO;AACP;AACH;;AACD,sBAAI5D,WAAW,GAAGpB,WAAW,CAAC4C,MAAD,CAA7B;AACAxB,kBAAAA,WAAW,CAAC6D,IAAZ,aAA2BR,SAA3B,SAAwCI,YAAxC;AACAT,kBAAAA,QAAQ,CAAEb,QAAV,CAAmBnC,WAAnB;AACA,sBAAM8D,aAAa,GAAG;AAAA;AAAA,0CAAQP,aAAR,CAAsBC,MAAtB,MAAkCxC,OAAO,CAAC+C,OAAR,CAAgBC,GAAhB,GAAsBhD,OAAO,CAAC+C,OAAR,CAAgBE,GAAxE,IAA+EjD,OAAO,CAAC+C,OAAR,CAAgBE,GAArH;AACA,sBAAMC,aAAa,GAAG;AAAA;AAAA,0CAAQX,aAAR,CAAsBC,MAAtB,MAAkCxC,OAAO,CAACN,OAAR,CAAgBsD,GAAhB,GAAsBhD,OAAO,CAACN,OAAR,CAAgBuD,GAAxE,IAA+EjD,OAAO,CAACN,OAAR,CAAgBuD,GAArH;AACAjE,kBAAAA,WAAW,CAACY,WAAZ,CAAwBkD,aAAxB,EAAuCI,aAAvC,EAAsD,CAAtD;AACAN,kBAAAA,OAAO;AACV,iBAbD;AAcH,eAhBmB,CAApB;AAiBAd,cAAAA,YAAY,CAACH,IAAb,CAAkBe,WAAlB;AACH,aAtCD;AAwCA,kBAAMC,OAAO,CAACQ,GAAR,CAAYrB,YAAZ,CAAN;AA5CwE;AA6C3E;;AAEaT,QAAAA,sBAAsB,CAAC5B,IAAD,EAAsC;AAAA;;AAAA;AAAA;;AACtE,gBAAI,CAACA,IAAD,IAAS,MAAI,CAACP,aAAL,KAAuB,IAAhC,IAAwCO,IAAI,CAAC2D,SAAL,CAAevB,MAAf,KAA0B,CAAtE,EAAyE;AAAE;AAAS;;AAEpF,gBAAMC,YAA6B,GAAG,EAAtC;AACA,+BAAArC,IAAI,CAAC2D,SAAL,6BAAgBrD,OAAhB,CAAwB,CAACsD,QAAD,EAAWtB,KAAX,KAAqB;AACzC,kBAAMW,WAAW,GAAG,IAAIC,OAAJ,CAAmBC,OAAD,IAAa;AAC/C,oBAAM3C,IAAI,GAAG;AAAA;AAAA,oCAAMC,MAAN,CAAaC,YAAb,CAA0B;AAAA;AAAA,oCAAMD,MAAN,CAAaE,iBAAvC,EAA0DiD,QAAQ,CAAChD,IAAnE,CAAb;AACA;AAAA;AAAA,oCAAMH,MAAN,CAAaI,IAAb,CAAkBL,IAAlB,EAAwB,CAACM,GAAD,EAAoBC,MAApB,KAAuC;AAC3D,sBAAID,GAAJ,EAAS;AACLqC,oBAAAA,OAAO;AACP;AACH;;AACD,sBAAIU,YAAY,GAAG1F,WAAW,CAAC4C,MAAD,CAA9B;AACA8C,kBAAAA,YAAY,CAACT,IAAb,iBAAgCd,KAAhC;AACAuB,kBAAAA,YAAY,CAAC1D,WAAb,CAAyByD,QAAQ,CAACzC,QAAT,CAAkBC,CAA3C,EAA8CwC,QAAQ,CAACzC,QAAT,CAAkBE,CAAhE,EAAmE,CAAnE;AACAwC,kBAAAA,YAAY,CAACvC,QAAb,CAAsBsC,QAAQ,CAACrC,KAAT,CAAeH,CAArC,EAAwCwC,QAAQ,CAACrC,KAAT,CAAeF,CAAvD,EAA0D,CAA1D;AACAwC,kBAAAA,YAAY,CAACrC,oBAAb,CAAkC,CAAlC,EAAqC,CAArC,EAAwCoC,QAAQ,CAACnC,QAAjD;;AACA,kBAAA,MAAI,CAAChC,aAAL,CAAoBiC,QAApB,CAA6BmC,YAA7B;;AACAV,kBAAAA,OAAO;AACV,iBAZD;AAaH,eAfmB,CAApB;AAgBAd,cAAAA,YAAY,CAACH,IAAb,CAAkBe,WAAlB;AACH,aAlBD;AAoBA,kBAAMC,OAAO,CAACQ,GAAR,CAAYrB,YAAZ,CAAN;AAxBsE;AAyBzE;;AAEYR,QAAAA,uBAAuB,CAAC7B,IAAD,EAAqC;AAAA;;AAAA;AACrE,gBAAI,CAACA,IAAD,IAAS,MAAI,CAACR,WAAL,KAAqB,IAA9B,IAAsCQ,IAAI,CAAC8D,OAAL,CAAa1B,MAAb,KAAwB,CAAlE,EAAqE;AAAE;AAAS,aADX,CAGrE;;;AACA,gBAAMI,OAAiB,GAAG,EAA1B;AACAxC,YAAAA,IAAI,CAAC8D,OAAL,CAAaxD,OAAb,CAAqByD,OAAO,IAAI;AAC5BvB,cAAAA,OAAO,CAACN,IAAR,CAAa6B,OAAO,CAACpB,MAArB;AACH,aAFD;AAGA,gBAAMqB,WAAW,GAAG;AAAA;AAAA,gCAAMnB,uBAAN,CAA8BL,OAA9B,EAAuC;AAAA;AAAA,oCAAQM,aAAR,CAAsBC,MAAtB,EAAvC,CAApB;AACA,gBAAMkB,MAAM,GAAGjE,IAAI,CAAC8D,OAAL,CAAaE,WAAb,CAAf;AAEA,gBAAM3B,YAA6B,GAAG,EAAtC;AACA,gBAAI6B,UAAoB,GAAG,EAA3B,CAZqE,CAarE;;AACAD,YAAAA,MAAM,CAACE,KAAP,CAAa7D,OAAb,CAAsBM,IAAD,IAAU;AAC3B,kBAAMqC,WAAW,GAAG,IAAIC,OAAJ,CAAmBC,OAAD,IAAa;AAC/C,oBAAM3C,IAAI,GAAG;AAAA;AAAA,oCAAMC,MAAN,CAAaC,YAAb,CAA0B;AAAA;AAAA,oCAAMD,MAAN,CAAaE,iBAAvC,EAA0DC,IAA1D,CAAb;AACA;AAAA;AAAA,oCAAMH,MAAN,CAAaI,IAAb,CAAkBL,IAAlB,EAAwB,CAACM,GAAD,EAAoBC,MAApB,KAAuC;AAC3D,sBAAID,GAAJ,EAAS;AACLqC,oBAAAA,OAAO;AACP;AACH;;AACDe,kBAAAA,UAAU,CAAChC,IAAX,CAAgBnB,MAAhB;AACAoC,kBAAAA,OAAO;AACV,iBAPD;AAQH,eAVmB,CAApB;AAWAd,cAAAA,YAAY,CAACH,IAAb,CAAkBe,WAAlB;AACH,aAbD;AAeA,kBAAMC,OAAO,CAACQ,GAAR,CAAYrB,YAAZ,CAAN;;AAEA,gBAAM7C,WAAW,GAAG,MAAI,CAACY,aAAL,CAAmB,MAAI,CAACZ,WAAxB,cAAgDwE,WAAhD,CAApB;;AACA,gBAAII,WAAW,GAAGpE,IAAI,CAACqE,KAAL,GAAarE,IAAI,CAACsE,SAAlB,GAA8B,IAAhD;AACA,gBAAIC,UAAU,GAAG,CAAjB;AACA,gBAAIC,MAAM,GAAG,CAAb;AACA,gBAAIC,WAAW,GAAG,CAAlB,CAnCqE,CAmChD;;AACrB,mBAAOD,MAAM,GAAGJ,WAAhB,EAA6B;AACzB;AACA,kBAAMM,SAAS,GAAGR,UAAU,CAACO,WAAD,CAA5B;AACA,kBAAME,KAAK,GAAGxG,WAAW,CAACuG,SAAD,CAAzB;AACA,kBAAMrB,aAAa,GAAG;AAAA;AAAA,sCAAQP,aAAR,CAAsBC,MAAtB,MAAkCkB,MAAM,CAACX,OAAP,CAAgBC,GAAhB,GAAsBU,MAAM,CAACX,OAAP,CAAgBE,GAAxE,IAA+ES,MAAM,CAACX,OAAP,CAAgBE,GAArH;AACAmB,cAAAA,KAAK,CAACxE,WAAN,CAAkBkD,aAAlB,EAAiCkB,UAAjC,EAA6C,CAA7C;AACA,kBAAIK,IAAI,GAAG,CAAX;;AACA,kBAAIX,MAAM,CAACY,YAAP,KAAwB;AAAA;AAAA,0DAAkBC,WAA9C,EAA2D;AACvDF,gBAAAA,IAAI,GAAGD,KAAK,CAACI,YAAN,CAAmB1G,WAAnB,EAAiC2G,WAAjC,CAA6CR,MAApD;AACH,eAFD,MAEO,IAAIP,MAAM,CAACY,YAAP,KAAwB;AAAA;AAAA,0DAAkBI,UAA9C,EAA0D;AAC7DL,gBAAAA,IAAI,GAAG,IAAP;AACH,eAFM,MAEA,IAAIX,MAAM,CAACY,YAAP,KAAwB;AAAA;AAAA,0DAAkBK,aAA9C,EAA6D;AAChEN,gBAAAA,IAAI,GAAGO,IAAI,CAAC5B,GAAL,CAASU,MAAM,CAAChE,OAAP,CAAgBuD,GAAzB,EAA6BS,MAAM,CAAChE,OAAP,CAAgBsD,GAA7C,IAAoDoB,KAAK,CAACI,YAAN,CAAmB1G,WAAnB,EAAiC2G,WAAjC,CAA6CR,MAAxG;AACH;;AACDhF,cAAAA,WAAW,CAACkC,QAAZ,CAAqBiD,KAArB;AACAJ,cAAAA,UAAU,IAAIK,IAAd;AACAJ,cAAAA,MAAM,IAAII,IAAV;AACAH,cAAAA,WAAW,GAAG,CAACA,WAAW,GAAG,CAAf,IAAoBR,MAAM,CAACE,KAAP,CAAa/B,MAA/C;AACH;AAtDoE;AAuDxE;;AAEMgD,QAAAA,IAAI,CAACC,SAAD,EAAoBhB,KAApB,EAAyC;AAChD,cAAIiB,IAAI,GAAG,KAAKpF,IAAL,CAAUiB,QAAV,CAAmBE,CAA9B;;AACA,cAAI,KAAKzB,eAAL,KAAyB,IAA7B,EAAmC;AAC/B,gBAAM2F,OAAO,GAAGjH,IAAI,CAACkH,cAAL,GAAsBhB,MAAtB,GAA+B,CAA/C;;AACA,gBAAIc,IAAI,GAAGC,OAAX,EAAoB;AAChB,mBAAKlG,iBAAL,GAAyB,KAAzB;AACH;AACJ;;AACDiG,UAAAA,IAAI,IAAID,SAAS,GAAGhB,KAApB;AACA,eAAKnE,IAAL,CAAUC,WAAV,CAAsB,CAAtB,EAAyBmF,IAAzB,EAA+B,CAA/B,EATgD,CAWhD;AACA;;AACA,cAAMG,OAAO,GAAI,CAACH,IAAD,GAAQ,KAAKlG,QAAb,GAAwB;AAAA;AAAA,wCAAWsG,YAApD,CAbgD,CAchD;;AACA,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKhG,YAAL,CAAkByC,MAAtC,EAA8CuD,CAAC,EAA/C,EAAmD;AAC/C,gBAAMC,WAAW,GAAG,KAAKjG,YAAL,CAAkBgG,CAAlB,CAApB;AACAC,YAAAA,WAAW,CAACR,IAAZ,CAAiBK,OAAjB;;AACA,gBAAIG,WAAW,CAACC,WAAhB,EAA6B;AACzB;AACA,mBAAKlG,YAAL,CAAkBmG,MAAlB,CAAyBH,CAAzB,EAA4B,CAA5B;AACAA,cAAAA,CAAC;AACJ;AACJ;AACJ,SAvNuC,CAyNxC;;;AACQI,QAAAA,mBAAmB,GAAG,CAE7B;;AAEO3F,QAAAA,aAAa,CAAC4F,WAAD,EAAoB5C,IAApB,EAAwC;AACzD,cAAIlD,IAAI,GAAG8F,WAAW,CAACC,cAAZ,CAA2B7C,IAA3B,CAAX;;AACA,cAAIlD,IAAI,IAAI,IAAZ,EAAkB;AACdA,YAAAA,IAAI,GAAG,IAAI9B,IAAJ,CAASgF,IAAT,CAAP;AACA4C,YAAAA,WAAW,CAACtE,QAAZ,CAAqBxB,IAArB;AACH;;AACD,iBAAOA,IAAP;AACH;;AAEMgG,QAAAA,gBAAgB,CAACC,MAAD,EAAwC;AAC3D,eAAK,IAAIlE,KAAT,IAAkB,KAAKvC,MAAvB,EAA+B;AAC3B,gBAAIuC,KAAK,CAACkE,MAAN,IAAgBA,MAApB,EAA4B;AACxB,qBAAOlE,KAAP;AACH;AACJ;;AACD,iBAAO,IAAP;AACH;;AA9OuC,O", "sourcesContent": ["import { _decorator, Component, instantiate, Node, Prefab, UITransform, view } from \"cc\";\r\nimport { MyApp } from 'db://assets/bundles/common/script/app/MyApp';\r\nimport { LayerSplicingMode, LevelDataEvent, LevelDataLayer } from \"../../../leveldata/leveldata\";\r\nimport GameMapRun from \"./GameMapRun\";\r\nimport { LevelEventRun } from \"./LevelEventRun\"\r\nimport { Tools } from \"../../utils/Tools\";\r\nimport { GameIns } from \"../../GameIns\";\r\n\r\nconst { ccclass } = _decorator;\r\n\r\nconst TerrainsNodeName = \"terrains\";\r\nconst DynamicNodeName = \"dynamic\";\r\nconst ScrollsNodeName = \"scrolls\";\r\nconst EmittiersNodeName = \"emittiers\";\r\n\r\n@ccclass('LevelLayerUI')\r\nexport class LevelLayerUI extends Component {\r\n    public backgrounds: Prefab[] = [];\r\n    private _offSetY: number = 0; // 当前关卡的偏移量\r\n    private _bTrackBackground: boolean = true; // 是否跟随背景移动（预加载关卡未在显示区域的时候跟随）\r\n\r\n    private terrainsNode: Node | null = null;\r\n    private dynamicNode: Node | null = null;\r\n    private scrollsNode: Node | null = null;\r\n    private emittiersNode: Node | null = null;\r\n    private events: LevelDataEvent[] = [];\r\n    private eventRunners: LevelEventRun[] = [];\r\n\r\n    public get TrackBackground(): boolean {\r\n        return this._bTrackBackground;\r\n    }\r\n    public set TrackBackground(value: boolean) {\r\n        this._bTrackBackground = value;\r\n    }\r\n\r\n    onLoad(): void {\r\n\r\n    }\r\n\r\n    public async initByLevelData(data: LevelDataLayer, offSetY: number): Promise<void> {\r\n        this._offSetY = offSetY;\r\n        this.node.setPosition(0, offSetY, 0);\r\n\r\n        this.terrainsNode = this._getOrAddNode(this.node, TerrainsNodeName);\r\n        this.dynamicNode = this._getOrAddNode(this.node, DynamicNodeName);\r\n        this.scrollsNode = this._getOrAddNode(this.node, ScrollsNodeName);\r\n        this.emittiersNode = this._getOrAddNode(this.node, EmittiersNodeName);\r\n\r\n        this.backgrounds = [];\r\n\r\n        data.terrains?.forEach((terrain) => {\r\n            const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrain.uuid)\r\n            MyApp.resMgr.load(path, (err: Error | null, prefab: Prefab) => {\r\n                if (err) {\r\n                    console.error('LevelLayerUI', \" initByLevelData load terrain prefab err\", err);\r\n                    return;\r\n                }\r\n                var terrainNode = instantiate(prefab);\r\n                terrainNode.setPosition(terrain.position.x, terrain.position.y, 0);\r\n                terrainNode.setScale(terrain.scale.x, terrain.scale.y, 1);\r\n                terrainNode.setRotationFromEuler(0, 0, terrain.rotation);\r\n                this.terrainsNode!.addChild(terrainNode);\r\n            });\r\n        });\r\n\r\n        await this._initDynamicsByLevelData(data);\r\n        await this._initEmittierLevelData(data);\r\n        await this._initScorllsByLevelData(data);\r\n        \r\n        this.events = [...data.events]\r\n        this.events.sort((a, b) => a.position.y - b.position.y);\r\n        this.events.forEach((event) => {\r\n            this.eventRunners.push(new LevelEventRun(event, this));\r\n        });\r\n    }\r\n\r\n    private async _initDynamicsByLevelData(data: LevelDataLayer): Promise<void> {\r\n        if (!data || this.dynamicNode === null || data.dynamics.length === 0) { return; } \r\n\r\n        const loadPromises: Promise<void>[] = [];\r\n        data.dynamics?.forEach((dynamics,index) => {  \r\n            var dynaNode = this._getOrAddNode(this.dynamicNode!, `dyna_${index}`);\r\n            let weights: number[] = [];\r\n            dynamics.group.forEach((dynamic) => {\r\n                weights.push(dynamic.weight);\r\n            });\r\n            const dynaIndex = Tools.getRandomIndexByWeights(weights, GameIns.battleManager.random());\r\n            const dynamic = dynamics.group[dynaIndex];\r\n             \r\n            dynaNode.setPosition(dynamic.position.x, dynamic.position.y, 0);\r\n            dynaNode.setScale(dynamic.scale.x, dynamic.scale.y, 1);\r\n            dynaNode.setRotationFromEuler(0, 0, dynamic.rotation);\r\n\r\n            weights = [];\r\n            dynamic.terrains.forEach((terrain) => {\r\n                weights.push(terrain.weight);\r\n            });\r\n            const terrainIndex = Tools.getRandomIndexByWeights(weights, GameIns.battleManager.random());\r\n            const terrain = dynamic.terrains[terrainIndex];\r\n            \r\n            const loadPromise = new Promise<void>((resolve) => {\r\n                const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrain.uuid)\r\n                MyApp.resMgr.load(path, (err: Error | null, prefab: Prefab) => {\r\n                    if (err) {\r\n                        console.error(\"LevelEditorLayerUI initByLevelData load dynamic prefab err\", err);\r\n                        resolve();\r\n                        return\r\n                    } \r\n                    var dynamicNode = instantiate(prefab);\r\n                    dynamicNode.name = `rand_${dynaIndex}_${terrainIndex}`;\r\n                    dynaNode!.addChild(dynamicNode); \r\n                    const randomOffsetX = GameIns.battleManager.random() * (terrain.offSetX.max - terrain.offSetX.min) + terrain.offSetX.min;   \r\n                    const randomOffsetY = GameIns.battleManager.random() * (terrain.offSetY.max - terrain.offSetY.min) + terrain.offSetY.min;\r\n                    dynamicNode.setPosition(randomOffsetX, randomOffsetY, 0);\r\n                    resolve();\r\n                });\r\n            });\r\n            loadPromises.push(loadPromise);\r\n        });\r\n\r\n        await Promise.all(loadPromises);\r\n    }\r\n\r\n    private async _initEmittierLevelData(data: LevelDataLayer): Promise<void> {\r\n        if (!data || this.emittiersNode === null || data.emittiers.length === 0) { return; } \r\n\r\n        const loadPromises: Promise<void>[] = [];    \r\n        data.emittiers?.forEach((emittier, index) => {\r\n            const loadPromise = new Promise<void>((resolve) => {\r\n                const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, emittier.uuid);\r\n                MyApp.resMgr.load(path, (err: Error | null, prefab: Prefab) => {\r\n                    if (err) {\r\n                        resolve();\r\n                        return\r\n                    }           \r\n                    var emittierNode = instantiate(prefab);\r\n                    emittierNode.name = `emittier_${index}`;\r\n                    emittierNode.setPosition(emittier.position.x, emittier.position.y, 0);\r\n                    emittierNode.setScale(emittier.scale.x, emittier.scale.y, 1);\r\n                    emittierNode.setRotationFromEuler(0, 0, emittier.rotation);\r\n                    this.emittiersNode!.addChild(emittierNode); \r\n                    resolve();\r\n                });\r\n            });\r\n            loadPromises.push(loadPromise);\r\n        });\r\n\r\n        await Promise.all(loadPromises);  \r\n    }\r\n\r\n    public async _initScorllsByLevelData(data: LevelDataLayer):Promise<void> {\r\n        if (!data || this.scrollsNode === null || data.scrolls.length === 0) { return; } \r\n\r\n        // 根据权重随机出一个滚动组\r\n        const weights: number[] = [];\r\n        data.scrolls.forEach(element => {\r\n            weights.push(element.weight);\r\n        });\r\n        const srocllIndex = Tools.getRandomIndexByWeights(weights, GameIns.battleManager.random());\r\n        const scroll = data.scrolls[srocllIndex];\r\n\r\n        const loadPromises: Promise<void>[] = [];\r\n        let prefabList: Prefab[] = [];\r\n        // 先加载成功所有预制体\r\n        scroll.uuids.forEach((uuid) => {\r\n            const loadPromise = new Promise<void>((resolve) => {\r\n                const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, uuid);\r\n                MyApp.resMgr.load(path, (err: Error | null, prefab: Prefab) => {\r\n                    if (err) {\r\n                        resolve();\r\n                        return;\r\n                    }        \r\n                    prefabList.push(prefab);   \r\n                    resolve();\r\n                });\r\n            });\r\n            loadPromises.push(loadPromise);\r\n        });\r\n\r\n        await Promise.all(loadPromises); \r\n        \r\n        const scrollsNode = this._getOrAddNode(this.scrollsNode!, `scroll_${srocllIndex}`);\r\n        var totalHeight = data.speed * data.totalTime / 1000;\r\n        var posOffsetY = 0;\r\n        var height = 0;\r\n        let prefabIndex = 0; // 当前使用的 prefab 索引\r\n        while (height < totalHeight) {\r\n            // 循环使用 prefab\r\n            const curPrefab = prefabList[prefabIndex];\r\n            const child = instantiate(curPrefab);\r\n            const randomOffsetX = GameIns.battleManager.random() * (scroll.offSetX!.max - scroll.offSetX!.min) + scroll.offSetX!.min;\r\n            child.setPosition(randomOffsetX, posOffsetY, 0);\r\n            var offY = 0;\r\n            if (scroll.splicingMode === LayerSplicingMode.node_height) {    \r\n                offY = child.getComponent(UITransform)!.contentSize.height;\r\n            } else if (scroll.splicingMode === LayerSplicingMode.fix_height) {\r\n                offY = 1334;\r\n            } else if (scroll.splicingMode === LayerSplicingMode.random_height) {\r\n                offY = Math.max(scroll.offSetY!.min,scroll.offSetY!.max) + child.getComponent(UITransform)!.contentSize.height;\r\n            }\r\n            scrollsNode.addChild(child);\r\n            posOffsetY += offY;\r\n            height += offY;\r\n            prefabIndex = (prefabIndex + 1) % scroll.uuids.length;\r\n        }\r\n    }\r\n\r\n    public tick(deltaTime: number, speed: number): void {\r\n        let posY = this.node.position.y;\r\n        if (this.TrackBackground === true) {\r\n            const topPosY = view.getVisibleSize().height / 2;\r\n            if (posY < topPosY) {\r\n                this._bTrackBackground = false;\r\n            }\r\n        }\r\n        posY -= deltaTime * speed;\r\n        this.node.setPosition(0, posY, 0);\r\n\r\n        // 说明: event的激活，是从进入世界范围开始。\r\n        // 又：因为游戏是从上往下滚动，因为我们用一个固定大小变量定义上边界，一旦进入这个范围，就激活。\r\n        const scrollY =  -posY + this._offSetY + GameMapRun.VIEWPORT_TOP;\r\n        // console.log('LevelLayerUI tick scrollY', scrollY, -posY, this._offSetY, GameMapRun.VIEWPORT_TOP);\r\n        for (let i = 0; i < this.eventRunners.length; i++) {\r\n            const eventRunner = this.eventRunners[i];\r\n            eventRunner.tick(scrollY);\r\n            if (eventRunner.isTriggered) {\r\n                // 条件已触发\r\n                this.eventRunners.splice(i, 1);\r\n                i--;\r\n            }\r\n        }\r\n    }\r\n\r\n    // 动态实例化场景元素，当元素的位置在在一个屏幕以上位置是，就实例化\r\n    private _instantiateTerrain() {\r\n        \r\n    }\r\n\r\n    private _getOrAddNode(node_parent: Node, name: string): Node {\r\n        var node = node_parent.getChildByName(name);\r\n        if (node == null) {\r\n            node = new Node(name);\r\n            node_parent.addChild(node);\r\n        }\r\n        return node;\r\n    }\r\n\r\n    public getEventByElemID(elemID: string): LevelDataEvent | null {\r\n        for (let event of this.events) {\r\n            if (event.elemID == elemID) {\r\n                return event;\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n}"]}