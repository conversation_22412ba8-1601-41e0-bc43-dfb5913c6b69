{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/LevelEditorLayerUI.ts"], "names": ["_decorator", "Component", "Node", "assetManager", "instantiate", "Vec2", "UITransform", "v2", "LayerSplicingMode", "LayerType", "LevelDataEvent", "LevelEditorUtils", "LevelScrollLayerUI", "LevelEditorEventUI", "LevelPrefabParse", "ccclass", "property", "executeInEditMode", "TerrainsNodeName", "ScrollsNodeName", "DynamicNodeName", "EmittiersNodeName", "EventNodeName", "LevelEditorLayerUI", "terrainsNode", "scrollsNode", "dynamicNode", "emittiersNode", "eventsNode", "_loadScrollNode", "onLoad", "getOrAddNode", "node", "initByLevelData", "data", "console", "log", "terrains", "for<PERSON>ach", "terrain", "loadAny", "uuid", "err", "prefab", "error", "terrainNode", "setPosition", "position", "x", "y", "setScale", "scale", "setRotationFromEuler", "rotation", "<PERSON><PERSON><PERSON><PERSON>", "dynamics", "index", "dynaNode", "group", "dynamic", "dynaIndex", "terrainIndex", "name", "randomOffsetX", "Math", "random", "offSetX", "max", "min", "randomOffsetY", "offSetY", "events", "event", "eventUIComp", "addComponent", "initEmittierLevelData", "layerData", "loadPromises", "emittierLayers", "emittiers", "emittier", "loadPromise", "Promise", "resolve", "push", "emittierNode", "all", "initScorllsByLevelData", "scrollLayers", "scrolls", "scroll", "weight", "uuids", "splicingMode", "splicingOffsetX", "splicingOffsetY", "scrollPrefabs", "totalHeight", "speed", "totalTime", "posOffsetY", "height", "prefabIndex", "curPrefab", "child", "offY", "node_height", "getComponent", "contentSize", "fix_height", "random_height", "length", "fillLevelData", "children", "_prefab", "asset", "_uuid", "z", "type", "Random", "dyna", "match", "groupIndex", "parseInt", "<PERSON><PERSON><PERSON>", "eventNode", "tick", "progress", "play", "bPlay"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAcC,MAAAA,Y,OAAAA,Y;AAAcC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,E,OAAAA,E;;AAGnFC,MAAAA,iB,iBAAAA,iB;AAAmBC,MAAAA,S,iBAAAA,S;AAAWC,MAAAA,c,iBAAAA,c;;AAE9BC,MAAAA,gB,iBAAAA,gB;AAA8BC,MAAAA,kB,iBAAAA,kB;;AAC9BC,MAAAA,kB,iBAAAA,kB;;AACAC,MAAAA,gB,iBAAAA,gB;;;;;;;;;OANH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CjB,U;AAQ3CkB,MAAAA,gB,GAAmB,U;AACnBC,MAAAA,e,GAAkB,S;AAClBC,MAAAA,e,GAAkB,S;AAClBC,MAAAA,iB,GAAoB,W;AACpBC,MAAAA,a,GAAgB,Q;;oCAITC,kB,WAFZR,OAAO,CAAC,oBAAD,C,UACPE,iBAAiB,E,+BADlB,MAEaM,kBAFb,SAEwCtB,SAFxC,CAEkD;AAAA;AAAA;AAAA,eACvCuB,YADuC,GACb,IADa;AAAA,eAEvCC,WAFuC,GAEd,IAFc;AAAA,eAGvCC,WAHuC,GAGd,IAHc;AAAA,eAIvCC,aAJuC,GAIZ,IAJY;AAAA,eAKvCC,UALuC,GAKf,IALe;AAAA,eAOtCC,eAPsC,GAOX,KAPW;AAAA;;AAS9CC,QAAAA,MAAM,GAAS;AACX,eAAKN,YAAL,GAAoB;AAAA;AAAA,oDAAiBO,YAAjB,CAA8B,KAAKC,IAAnC,EAAyCd,gBAAzC,CAApB;AACA,eAAKO,WAAL,GAAmB;AAAA;AAAA,oDAAiBM,YAAjB,CAA8B,KAAKC,IAAnC,EAAyCb,eAAzC,CAAnB;AACA,eAAKO,WAAL,GAAmB;AAAA;AAAA,oDAAiBK,YAAjB,CAA8B,KAAKC,IAAnC,EAAyCZ,eAAzC,CAAnB;AACA,eAAKO,aAAL,GAAqB;AAAA;AAAA,oDAAiBI,YAAjB,CAA8B,KAAKC,IAAnC,EAAyCX,iBAAzC,CAArB;AACA,eAAKO,UAAL,GAAkB;AAAA;AAAA,oDAAiBG,YAAjB,CAA8B,KAAKC,IAAnC,EAAyCV,aAAzC,CAAlB;AACH;;AAEMW,QAAAA,eAAe,CAACC,IAAD,EAA4B;AAAA;;AAC9CC,UAAAA,OAAO,CAACC,GAAR,CAAY,oCAAZ;;AACA,cAAI,CAACF,IAAL,EAAW;AACP;AACH;;AAED,cAAI,KAAKV,YAAL,KAAsB,IAA1B,EAAgC;AAC5B;AACH;;AACD,4BAAAU,IAAI,CAACG,QAAL,4BAAeC,OAAf,CAAwBC,OAAD,IAAa;AAChCpC,YAAAA,YAAY,CAACqC,OAAb,CAAqB;AAACC,cAAAA,IAAI,EAACF,OAAO,CAACE;AAAd,aAArB,EAA0C,CAACC,GAAD,EAAaC,MAAb,KAA+B;AACrE,kBAAID,GAAJ,EAAS;AACLP,gBAAAA,OAAO,CAACS,KAAR,CAAc,4DAAd,EAA4EF,GAA5E;AACA;AACH;;AACD,kBAAIG,WAAW,GAAGzC,WAAW,CAACuC,MAAD,CAA7B;AACAE,cAAAA,WAAW,CAACC,WAAZ,CAAwBP,OAAO,CAACQ,QAAR,CAAiBC,CAAzC,EAA4CT,OAAO,CAACQ,QAAR,CAAiBE,CAA7D,EAAgE,CAAhE;AACAJ,cAAAA,WAAW,CAACK,QAAZ,CAAqBX,OAAO,CAACY,KAAR,CAAcH,CAAnC,EAAsCT,OAAO,CAACY,KAAR,CAAcF,CAApD,EAAuD,CAAvD;AACAJ,cAAAA,WAAW,CAACO,oBAAZ,CAAiC,CAAjC,EAAoC,CAApC,EAAuCb,OAAO,CAACc,QAA/C;AACA,mBAAK7B,YAAL,CAAmB8B,QAAnB,CAA4BT,WAA5B;AACH,aAVD;AAWH,WAZD;AAcA,4BAAAX,IAAI,CAACqB,QAAL,4BAAejB,OAAf,CAAuB,CAACiB,QAAD,EAAUC,KAAV,KAAoB;AACvC,gBAAIC,QAAQ,GAAG;AAAA;AAAA,sDAAiB1B,YAAjB,CAA8B,KAAKL,WAAnC,YAAyD8B,KAAzD,CAAf;AACAD,YAAAA,QAAQ,CAACG,KAAT,CAAepB,OAAf,CAAuB,CAACqB,OAAD,EAAUC,SAAV,KAAwB;AAC3CH,cAAAA,QAAQ,CAACX,WAAT,CAAqBa,OAAO,CAACZ,QAAR,CAAiBC,CAAtC,EAAyCW,OAAO,CAACZ,QAAR,CAAiBE,CAA1D,EAA6D,CAA7D;AACAQ,cAAAA,QAAQ,CAACP,QAAT,CAAkBS,OAAO,CAACR,KAAR,CAAcH,CAAhC,EAAmCW,OAAO,CAACR,KAAR,CAAcF,CAAjD,EAAoD,CAApD;AACAQ,cAAAA,QAAQ,CAACL,oBAAT,CAA8B,CAA9B,EAAiC,CAAjC,EAAoCO,OAAO,CAACN,QAA5C;AACAM,cAAAA,OAAO,CAACtB,QAAR,CAAiBC,OAAjB,CAAyB,CAACC,OAAD,EAASsB,YAAT,KAAwB;AAC7C1D,gBAAAA,YAAY,CAACqC,OAAb,CAAqB;AAACC,kBAAAA,IAAI,EAACF,OAAO,CAACE;AAAd,iBAArB,EAA0C,CAACC,GAAD,EAAMC,MAAN,KAAwB;AAC9D,sBAAID,GAAJ,EAAS;AACLP,oBAAAA,OAAO,CAACS,KAAR,CAAc,4DAAd,EAA4EF,GAA5E;AACA;AACH;;AACD,sBAAIhB,WAAW,GAAGtB,WAAW,CAACuC,MAAD,CAA7B;AACAjB,kBAAAA,WAAW,CAACoC,IAAZ,aAA2BF,SAA3B,SAAwCC,YAAxC;AACAJ,kBAAAA,QAAQ,CAAEH,QAAV,CAAmB5B,WAAnB;AACA,sBAAMqC,aAAa,GAAGC,IAAI,CAACC,MAAL,MAAiB1B,OAAO,CAAC2B,OAAR,CAAgBC,GAAhB,GAAsB5B,OAAO,CAAC2B,OAAR,CAAgBE,GAAvD,IAA8D7B,OAAO,CAAC2B,OAAR,CAAgBE,GAApG;AACA,sBAAMC,aAAa,GAAGL,IAAI,CAACC,MAAL,MAAiB1B,OAAO,CAAC+B,OAAR,CAAgBH,GAAhB,GAAsB5B,OAAO,CAAC+B,OAAR,CAAgBF,GAAvD,IAA8D7B,OAAO,CAAC+B,OAAR,CAAgBF,GAApG;AACA1C,kBAAAA,WAAW,CAACoB,WAAZ,CAAwBiB,aAAxB,EAAuCM,aAAvC,EAAsD,CAAtD;AACH,iBAXD;AAYH,eAbD;AAcH,aAlBD;AAmBH,WArBD;AAuBA,0BAAAnC,IAAI,CAACqC,MAAL,0BAAajC,OAAb,CAAsBkC,KAAD,IAAS;AAC1B,gBAAIxC,IAAI,GAAG,IAAI9B,IAAJ,EAAX;AACA,gBAAIuE,WAAW,GAAGzC,IAAI,CAAC0C,YAAL;AAAA;AAAA,yDAAlB;AACAD,YAAAA,WAAW,CAACxC,eAAZ,CAA4BuC,KAA5B;AACA,iBAAK5C,UAAL,CAAiB0B,QAAjB,CAA0BtB,IAA1B;AACH,WALD;AAMH;;AAEY2C,QAAAA,qBAAqB,CAACC,SAAD,EAAwB1C,IAAxB,EAA4D;AAAA;;AAAA;AAAA;;AAC1F,gBAAI,CAACA,IAAD,IAAS,KAAI,CAACP,aAAL,KAAuB,IAApC,EAA0C;AACtC;AACH;;AAED,gBAAMkD,YAA6B,GAAG,EAAtC;AACAD,YAAAA,SAAS,CAACE,cAAV,GAA2B,EAA3B;AAEA,+BAAA5C,IAAI,CAAC6C,SAAL,6BAAgBzC,OAAhB,CAAwB,CAAC0C,QAAD,EAAWxB,KAAX,KAAqB;AACzC,kBAAMyB,WAAW,GAAG,IAAIC,OAAJ,CAAmBC,OAAD,IAAa;AAC/ChF,gBAAAA,YAAY,CAACqC,OAAb,CAAqB;AAACC,kBAAAA,IAAI,EAAEuC,QAAQ,CAACvC;AAAhB,iBAArB,EAA4C,CAACC,GAAD,EAAMC,MAAN,KAAwB;AAChE,sBAAID,GAAJ,EAAS;AACLyC,oBAAAA,OAAO;AACP;AACH;;AACDP,kBAAAA,SAAS,CAACE,cAAV,CAAyBM,IAAzB,CAA8BzC,MAA9B;AACA,sBAAI0C,YAAY,GAAGjF,WAAW,CAACuC,MAAD,CAA9B;AACA0C,kBAAAA,YAAY,CAACvB,IAAb,iBAAgCN,KAAhC;AACA6B,kBAAAA,YAAY,CAACvC,WAAb,CAAyBkC,QAAQ,CAACjC,QAAT,CAAkBC,CAA3C,EAA8CgC,QAAQ,CAACjC,QAAT,CAAkBE,CAAhE,EAAmE,CAAnE;AACAoC,kBAAAA,YAAY,CAACnC,QAAb,CAAsB8B,QAAQ,CAAC7B,KAAT,CAAeH,CAArC,EAAwCgC,QAAQ,CAAC7B,KAAT,CAAeF,CAAvD,EAA0D,CAA1D;AACAoC,kBAAAA,YAAY,CAACjC,oBAAb,CAAkC,CAAlC,EAAqC,CAArC,EAAwC4B,QAAQ,CAAC3B,QAAjD;;AACA,kBAAA,KAAI,CAAC1B,aAAL,CAAoB2B,QAApB,CAA6B+B,YAA7B;;AACAF,kBAAAA,OAAO;AACV,iBAbD;AAcH,eAfmB,CAApB;AAgBAN,cAAAA,YAAY,CAACO,IAAb,CAAkBH,WAAlB;AACH,aAlBD;AAoBA,kBAAMC,OAAO,CAACI,GAAR,CAAYT,YAAZ,CAAN;AA5B0F;AA6B7F;;AAEYU,QAAAA,sBAAsB,CAACX,SAAD,EAAwB1C,IAAxB,EAA4D;AAAA;;AAAA;AAAA;;AAC3F,gBAAI,CAACA,IAAD,IAAS,MAAI,CAACT,WAAL,KAAqB,IAA9B,IAAsC,MAAI,CAACI,eAA/C,EAAgE;AAC5D;AACH;;AAED,gBAAMgD,YAA6B,GAAG,EAAtC;AACAD,YAAAA,SAAS,CAACY,YAAV,GAAyB,EAAzB;AACA,YAAA,MAAI,CAAC3D,eAAL,GAAuB,IAAvB;AACA,6BAAAK,IAAI,CAACuD,OAAL,2BAAcnD,OAAd,CAAuBoD,MAAD,IAAY;AAC9B,kBAAMF,YAAY,GAAG;AAAA;AAAA,6DAArB;AACAA,cAAAA,YAAY,CAACG,MAAb,GAAsBD,MAAM,CAACC,MAA7B;AACA,kBAAMC,KAAK,GAAGF,MAAM,CAACE,KAAP,IAAgB,EAA9B;AACAJ,cAAAA,YAAY,CAACK,YAAb,GAA4BH,MAAM,CAACG,YAAnC;AACAL,cAAAA,YAAY,CAACM,eAAb,CAA8B1B,GAA9B,GAAoCsB,MAAM,CAACxB,OAAP,CAAeE,GAAnD;AACAoB,cAAAA,YAAY,CAACM,eAAb,CAA8B3B,GAA9B,GAAoCuB,MAAM,CAACxB,OAAP,CAAeC,GAAnD;AACAqB,cAAAA,YAAY,CAACO,eAAb,CAA8B3B,GAA9B,GAAoCsB,MAAM,CAACpB,OAAP,CAAeF,GAAnD;AACAoB,cAAAA,YAAY,CAACO,eAAb,CAA8B5B,GAA9B,GAAoCuB,MAAM,CAACpB,OAAP,CAAeH,GAAnD;AACAqB,cAAAA,YAAY,CAACQ,aAAb,GAA6B,EAA7B;AACAJ,cAAAA,KAAK,CAACtD,OAAN,CAAeG,IAAD,IAAU;AACpB,oBAAMwC,WAAW,GAAG,IAAIC,OAAJ,CAAmBC,OAAD,IAAa;AAC/ChF,kBAAAA,YAAY,CAACqC,OAAb,CAAqB;AAACC,oBAAAA,IAAI,EAAEA;AAAP,mBAArB,EAAmC,CAACC,GAAD,EAAaC,MAAb,KAA+B;AAC9D,wBAAID,GAAJ,EAAS;AACLyC,sBAAAA,OAAO;AACP;AACH;;AACDK,oBAAAA,YAAY,CAACQ,aAAb,CAA2BZ,IAA3B,CAAgCzC,MAAhC;AACAwC,oBAAAA,OAAO;AACV,mBAPD;AAQH,iBATmB,CAApB;AAUAN,gBAAAA,YAAY,CAACO,IAAb,CAAkBH,WAAlB;AACH,eAZD;AAaAL,cAAAA,SAAS,CAACY,YAAV,CAAuBJ,IAAvB,CAA4BI,YAA5B;AACH,aAxBD;AA0BA,kBAAMN,OAAO,CAACI,GAAR,CAAYT,YAAZ,CAAN;AACA,YAAA,MAAI,CAAChD,eAAL,GAAuB,KAAvB;AACA+C,YAAAA,SAAS,CAACY,YAAV,CAAuBlD,OAAvB,CAA+B,CAACoD,MAAD,EAASlC,KAAT,KAAmB;AAC9C,kBAAM/B,WAAW,GAAG;AAAA;AAAA,wDAAiBM,YAAjB,CAA8B,MAAI,CAACN,WAAnC,cAA2D+B,KAA3D,CAApB;AACA,kBAAIyC,WAAW,GAAG/D,IAAI,CAACgE,KAAL,GAAahE,IAAI,CAACiE,SAAlB,GAA8B,IAAhD;AACAhE,cAAAA,OAAO,CAACC,GAAR,qDAA8D6D,WAA9D,mBAAuF/D,IAAI,CAACiE,SAA5F,eAA+GjE,IAAI,CAACgE,KAApH;AACA,kBAAIE,UAAU,GAAG,CAAjB;AACA,kBAAIC,MAAM,GAAG,CAAb;AACA,kBAAIC,WAAW,GAAG,CAAlB,CAN8C,CAMzB;;AACrB,qBAAOD,MAAM,GAAGJ,WAAhB,EAA6B;AACzB;AACA,oBAAMM,SAAS,GAAGb,MAAM,CAACM,aAAP,CAAqBM,WAArB,CAAlB;AACA,oBAAME,KAAK,GAAGpG,WAAW,CAACmG,SAAD,CAAzB;AACA,oBAAMxC,aAAa,GAAGC,IAAI,CAACC,MAAL,MAAiByB,MAAM,CAACI,eAAP,CAAwB3B,GAAxB,GAA8BuB,MAAM,CAACI,eAAP,CAAwB1B,GAAvE,IAA8EsB,MAAM,CAACI,eAAP,CAAwB1B,GAA5H;AACAoC,gBAAAA,KAAK,CAAC1D,WAAN,CAAkBiB,aAAlB,EAAiCqC,UAAjC,EAA6C,CAA7C;AACA,oBAAIK,IAAI,GAAG,CAAX;;AACA,oBAAIf,MAAM,CAACG,YAAP,KAAwB;AAAA;AAAA,4DAAkBa,WAA9C,EAA2D;AACvDD,kBAAAA,IAAI,GAAGD,KAAK,CAACG,YAAN,CAAmBrG,WAAnB,EAAiCsG,WAAjC,CAA6CP,MAApD;AACH,iBAFD,MAEO,IAAIX,MAAM,CAACG,YAAP,KAAwB;AAAA;AAAA,4DAAkBgB,UAA9C,EAA0D;AAC7DJ,kBAAAA,IAAI,GAAG,IAAP;AACH,iBAFM,MAEA,IAAIf,MAAM,CAACG,YAAP,KAAwB;AAAA;AAAA,4DAAkBiB,aAA9C,EAA6D;AAChEL,kBAAAA,IAAI,GAAGzC,IAAI,CAACG,GAAL,CAASuB,MAAM,CAACK,eAAP,CAAwB3B,GAAjC,EAAqCsB,MAAM,CAACK,eAAP,CAAwB5B,GAA7D,IAAoEqC,KAAK,CAACG,YAAN,CAAmBrG,WAAnB,EAAiCsG,WAAjC,CAA6CP,MAAxH;AACH;;AACD5E,gBAAAA,WAAW,CAAC6B,QAAZ,CAAqBkD,KAArB;AACAJ,gBAAAA,UAAU,IAAIK,IAAd;AACAJ,gBAAAA,MAAM,IAAII,IAAV;AACAH,gBAAAA,WAAW,GAAG,CAACA,WAAW,GAAG,CAAf,IAAoBZ,MAAM,CAACM,aAAP,CAAqBe,MAAvD;AACH;AACJ,aA1BD;AApC2F;AA+D9F;;AAEMC,QAAAA,aAAa,CAAC9E,IAAD,EAA4B;AAC5CA,UAAAA,IAAI,CAACG,QAAL,GAAgB,EAAhB;AACA,eAAKb,YAAL,CAAmByF,QAAnB,CAA4B3E,OAA5B,CAAqCO,WAAD,IAAiB;AACjD;AACA,gBAAIJ,IAAI,GAAGI,WAAW,CAACqE,OAAZ,CAAoBC,KAApB,CAA0BC,KAArC;;AACA,gBAAIvE,WAAW,CAAC8D,YAAZ;AAAA;AAAA,qDAAJ,EAAgD;AAC5ClE,cAAAA,IAAI,GAAMA,IAAN,UAAJ;AACH;;AACDP,YAAAA,IAAI,CAACG,QAAL,CAAc+C,IAAd,CAAmB;AACf3C,cAAAA,IAAI,EAAEA,IADS;AAEfM,cAAAA,QAAQ,EAAE,IAAI1C,IAAJ,CAASwC,WAAW,CAACE,QAAZ,CAAqBC,CAA9B,EAAiCH,WAAW,CAACE,QAAZ,CAAqBE,CAAtD,CAFK;AAGfE,cAAAA,KAAK,EAAE,IAAI9C,IAAJ,CAASwC,WAAW,CAACM,KAAZ,CAAkBH,CAA3B,EAA8BH,WAAW,CAACM,KAAZ,CAAkBF,CAAhD,CAHQ;AAIfI,cAAAA,QAAQ,EAAER,WAAW,CAACQ,QAAZ,CAAqBgE;AAJhB,aAAnB;AAMH,WAZD,EAF4C,CAgB5C;;AACA,cAAInF,IAAI,CAACoF,IAAL,KAAc;AAAA;AAAA,sCAAUC,MAA5B,EAAoC;AAChC,iBAAK7F,WAAL,CAAkBuF,QAAlB,CAA2B3E,OAA3B,CAAmC,CAACqB,OAAD,EAAUH,KAAV,KAAoB;AACnDG,cAAAA,OAAO,CAACsD,QAAR,CAAiB3E,OAAjB,CAAyB,CAACkF,IAAD,EAAO5D,SAAP,KAAqB;AAC1C,oBAAM6D,KAAK,GAAGD,IAAI,CAAC1D,IAAL,CAAU2D,KAAV,CAAgB,oBAAhB,CAAd;AACA,oBAAIC,UAAU,GAAG,CAAjB;;AACA,oBAAID,KAAJ,EAAW;AACPC,kBAAAA,UAAU,GAAGC,QAAQ,CAACF,KAAK,CAAC,CAAD,CAAN,CAArB;AACH;;AAED,oBAAID,IAAI,CAACb,YAAL;AAAA;AAAA,yDAAJ,EAAyC;AACrC;AACAzE,kBAAAA,IAAI,CAACqB,QAAL,CAAcC,KAAd,EAAsBE,KAAtB,CAA4BgE,UAA5B,EAAyCrF,QAAzC,CAAkDuB,SAAlD,EAA6DnB,IAA7D,GAAuE+E,IAAI,CAACN,OAAL,CAAaC,KAAb,CAAmBC,KAA1F;AACH,iBAVyC,CAY1C;;;AACAlF,gBAAAA,IAAI,CAACqB,QAAL,CAAcC,KAAd,EAAsBE,KAAtB,CAA4BgE,UAA5B,EAAyC3E,QAAzC,GAAoDxC,EAAE,CAACoD,OAAO,CAACZ,QAAR,CAAiBC,CAAlB,EAAqBW,OAAO,CAACZ,QAAR,CAAiBE,CAAtC,CAAtD;AACAf,gBAAAA,IAAI,CAACqB,QAAL,CAAcC,KAAd,EAAsBE,KAAtB,CAA4BgE,UAA5B,EAAyCvE,KAAzC,GAAiD5C,EAAE,CAACoD,OAAO,CAACR,KAAR,CAAcH,CAAf,EAAkBW,OAAO,CAACR,KAAR,CAAcF,CAAhC,CAAnD;AACAf,gBAAAA,IAAI,CAACqB,QAAL,CAAcC,KAAd,EAAsBE,KAAtB,CAA4BgE,UAA5B,EAAyCrE,QAAzC,GAAoDM,OAAO,CAACN,QAAR,CAAiBgE,CAArE;AACH,eAhBD;AAiBH,aAlBD;AAmBH;;AAED,cAAInF,IAAI,CAACoF,IAAL,KAAc;AAAA;AAAA,sCAAUM,QAA5B,EAAsC;AAClC1F,YAAAA,IAAI,CAAC6C,SAAL,GAAiB,EAAjB;AACA,iBAAKpD,aAAL,CAAoBsF,QAApB,CAA6B3E,OAA7B,CAAsC+C,YAAD,IAAkB;AACnDnD,cAAAA,IAAI,CAAC6C,SAAL,CAAeK,IAAf,CAAoB;AAChB;AACA3C,gBAAAA,IAAI,EAAE4C,YAAY,CAAC6B,OAAb,CAAqBC,KAArB,CAA2BC,KAFjB;AAGhBrE,gBAAAA,QAAQ,EAAExC,EAAE,CAAC8E,YAAY,CAACtC,QAAb,CAAsBC,CAAvB,EAA0BqC,YAAY,CAACtC,QAAb,CAAsBE,CAAhD,CAHI;AAIhBE,gBAAAA,KAAK,EAAE5C,EAAE,CAAC8E,YAAY,CAAClC,KAAb,CAAmBH,CAApB,EAAuBqC,YAAY,CAAClC,KAAb,CAAmBF,CAA1C,CAJO;AAKhBI,gBAAAA,QAAQ,EAAEgC,YAAY,CAAChC,QAAb,CAAsBgE;AALhB,eAApB;AAOH,aARD;AASH;;AAEDnF,UAAAA,IAAI,CAACqC,MAAL,GAAc,EAAd;AACA,eAAK3C,UAAL,CAAiBqF,QAAjB,CAA0B3E,OAA1B,CAAmCuF,SAAD,IAAe;AAC7C,gBAAIrD,KAAK,GAAG;AAAA;AAAA,mDAAZ;AACA,gBAAIC,WAAW,GAAGoD,SAAS,CAAClB,YAAV;AAAA;AAAA,yDAAlB;AACAlC,YAAAA,WAAW,CAAEuC,aAAb,CAA2BxC,KAA3B;AACAtC,YAAAA,IAAI,CAACqC,MAAL,CAAYa,IAAZ,CAAiBZ,KAAjB;AACH,WALD;AAMH;;AAEMsD,QAAAA,IAAI,CAACC,QAAD,EAAmB5B,SAAnB,EAAqCD,KAArC,EAAwD;AAC/D,eAAKlE,IAAL,CAAUc,WAAV,CAAsB,CAAtB,EAAyB,CAAEiF,QAAF,GAAa5B,SAAb,GAAyBD,KAAzB,GAAiC,IAA1D,EAAgE,CAAhE;AACH,SAtO6C,CAwO9C;AACA;;;AACO8B,QAAAA,IAAI,CAACC,KAAD,EAAiBF,QAAjB,EAAmC;AAAA;;AAC1C;AACA,mCAAKnG,UAAL,8BAAiBqF,QAAjB,CAA0B3E,OAA1B,CAAmCuF,SAAD,IAAe;AAC7C,gBAAIpD,WAAW,GAAGoD,SAAS,CAAClB,YAAV;AAAA;AAAA,yDAAlB;AACAlC,YAAAA,WAAW,CAAEuD,IAAb,CAAkBC,KAAlB,EAAyBF,QAAzB;AACH,WAHD;AAIH;;AAhP6C,O", "sourcesContent": ["import { _decorator, Component, Node, Prefab, assetManager, instantiate, Vec2, UITransform, v2 } from 'cc';\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\n\r\nimport { LayerSplicingMode, LayerType, LevelDataEvent, LevelDataLayer } from 'db://assets/bundles/common/script/leveldata/leveldata';\r\n\r\nimport { LevelEditorUtils, LevelLayer, LevelScrollLayerUI } from './utils';\r\nimport { LevelEditorEventUI } from './LevelEditorEventUI';\r\nimport { LevelPrefabParse } from './LevelPrefabParse';\r\n\r\nconst TerrainsNodeName = \"terrains\";\r\nconst ScrollsNodeName = \"scrolls\";\r\nconst DynamicNodeName = \"dynamic\";\r\nconst EmittiersNodeName = \"emittiers\";\r\nconst EventNodeName = \"events\"\r\n\r\n@ccclass('LevelEditorLayerUI')\r\n@executeInEditMode()\r\nexport class LevelEditorLayerUI extends Component {\r\n    public terrainsNode: Node|null = null;\r\n    public scrollsNode: Node|null = null;\r\n    public dynamicNode: Node|null = null;\r\n    public emittiersNode: Node|null = null;\r\n    public eventsNode: Node|null = null;\r\n\r\n    private _loadScrollNode: boolean = false;\r\n\r\n    onLoad(): void {\r\n        this.terrainsNode = LevelEditorUtils.getOrAddNode(this.node, TerrainsNodeName);\r\n        this.scrollsNode = LevelEditorUtils.getOrAddNode(this.node, ScrollsNodeName);\r\n        this.dynamicNode = LevelEditorUtils.getOrAddNode(this.node, DynamicNodeName);\r\n        this.emittiersNode = LevelEditorUtils.getOrAddNode(this.node, EmittiersNodeName);\r\n        this.eventsNode = LevelEditorUtils.getOrAddNode(this.node, EventNodeName);\r\n    }\r\n\r\n    public initByLevelData(data: LevelDataLayer):void {\r\n        console.log(\"LevelEditorLayerUI initByLevelData\");\r\n        if (!data) {\r\n            return;\r\n        }\r\n\r\n        if (this.terrainsNode === null) {\r\n            return;\r\n        }\r\n        data.terrains?.forEach((terrain) => {\r\n            assetManager.loadAny({uuid:terrain.uuid}, (err: Error, prefab:Prefab) => {\r\n                if (err) {\r\n                    console.error(\"LevelEditorLayerUI initByLevelData load terrain prefab err\", err);\r\n                    return\r\n                } \r\n                var terrainNode = instantiate(prefab);\r\n                terrainNode.setPosition(terrain.position.x, terrain.position.y, 0);\r\n                terrainNode.setScale(terrain.scale.x, terrain.scale.y, 1);\r\n                terrainNode.setRotationFromEuler(0, 0, terrain.rotation);\r\n                this.terrainsNode!.addChild(terrainNode);                \r\n            });\r\n        });\r\n\r\n        data.dynamics?.forEach((dynamics,index) => {\r\n            var dynaNode = LevelEditorUtils.getOrAddNode(this.dynamicNode!, `dyna_${index}`);\r\n            dynamics.group.forEach((dynamic, dynaIndex) => {\r\n                dynaNode.setPosition(dynamic.position.x, dynamic.position.y, 0);\r\n                dynaNode.setScale(dynamic.scale.x, dynamic.scale.y, 1);\r\n                dynaNode.setRotationFromEuler(0, 0, dynamic.rotation);\r\n                dynamic.terrains.forEach((terrain,terrainIndex)=>{\r\n                    assetManager.loadAny({uuid:terrain.uuid}, (err, prefab:Prefab) => {\r\n                        if (err) {\r\n                            console.error(\"LevelEditorLayerUI initByLevelData load dynamic prefab err\", err);\r\n                            return\r\n                        } \r\n                        var dynamicNode = instantiate(prefab);\r\n                        dynamicNode.name = `rand_${dynaIndex}_${terrainIndex}`;\r\n                        dynaNode!.addChild(dynamicNode); \r\n                        const randomOffsetX = Math.random() * (terrain.offSetX.max - terrain.offSetX.min) + terrain.offSetX.min;   \r\n                        const randomOffsetY = Math.random() * (terrain.offSetY.max - terrain.offSetY.min) + terrain.offSetY.min;\r\n                        dynamicNode.setPosition(randomOffsetX, randomOffsetY, 0);\r\n                    });\r\n                });\r\n            });\r\n        });\r\n\r\n        data.events?.forEach((event)=>{\r\n            var node = new Node();\r\n            var eventUIComp = node.addComponent(LevelEditorEventUI);\r\n            eventUIComp.initByLevelData(event);\r\n            this.eventsNode!.addChild(node);\r\n        })\r\n    }\r\n\r\n    public async initEmittierLevelData(layerData: LevelLayer, data: LevelDataLayer):Promise<void> {\r\n        if (!data || this.emittiersNode === null) {\r\n            return;\r\n        } \r\n\r\n        const loadPromises: Promise<void>[] = [];\r\n        layerData.emittierLayers = [];\r\n        \r\n        data.emittiers?.forEach((emittier, index) => {\r\n            const loadPromise = new Promise<void>((resolve) => {\r\n                assetManager.loadAny({uuid: emittier.uuid}, (err, prefab:Prefab) => {\r\n                    if (err) {\r\n                        resolve();\r\n                        return\r\n                    }           \r\n                    layerData.emittierLayers.push(prefab);  \r\n                    var emittierNode = instantiate(prefab);\r\n                    emittierNode.name = `emittier_${index}`;\r\n                    emittierNode.setPosition(emittier.position.x, emittier.position.y, 0);\r\n                    emittierNode.setScale(emittier.scale.x, emittier.scale.y, 1);\r\n                    emittierNode.setRotationFromEuler(0, 0, emittier.rotation);\r\n                    this.emittiersNode!.addChild(emittierNode); \r\n                    resolve();\r\n                });\r\n            });\r\n            loadPromises.push(loadPromise);\r\n        });\r\n\r\n        await Promise.all(loadPromises);  \r\n    }\r\n\r\n    public async initScorllsByLevelData(layerData: LevelLayer, data: LevelDataLayer):Promise<void> {\r\n        if (!data || this.scrollsNode === null || this._loadScrollNode) {\r\n            return;\r\n        } \r\n\r\n        const loadPromises: Promise<void>[] = [];\r\n        layerData.scrollLayers = [];\r\n        this._loadScrollNode = true;\r\n        data.scrolls?.forEach((scroll) => {\r\n            const scrollLayers = new LevelScrollLayerUI(); \r\n            scrollLayers.weight = scroll.weight;\r\n            const uuids = scroll.uuids || []; \r\n            scrollLayers.splicingMode = scroll.splicingMode;\r\n            scrollLayers.splicingOffsetX!.min = scroll.offSetX.min;\r\n            scrollLayers.splicingOffsetX!.max = scroll.offSetX.max;\r\n            scrollLayers.splicingOffsetY!.min = scroll.offSetY.min;\r\n            scrollLayers.splicingOffsetY!.max = scroll.offSetY.max;\r\n            scrollLayers.scrollPrefabs = [];\r\n            uuids.forEach((uuid) => {\r\n                const loadPromise = new Promise<void>((resolve) => {\r\n                    assetManager.loadAny({uuid: uuid}, (err: Error, prefab:Prefab) => {\r\n                        if (err) {\r\n                            resolve();\r\n                            return\r\n                        }           \r\n                        scrollLayers.scrollPrefabs.push(prefab);  \r\n                        resolve();\r\n                    });\r\n                });\r\n                loadPromises.push(loadPromise);\r\n            });\r\n            layerData.scrollLayers.push(scrollLayers);\r\n        });\r\n\r\n        await Promise.all(loadPromises);  \r\n        this._loadScrollNode = false;\r\n        layerData.scrollLayers.forEach((scroll, index) => { \r\n            const scrollsNode = LevelEditorUtils.getOrAddNode(this.scrollsNode!, `scroll_${index}`);\r\n            var totalHeight = data.speed * data.totalTime / 1000;\r\n            console.log(`LevelEditorBaseUI _checkScrollNode totalHeight ${totalHeight} totalTime ${data.totalTime} speed ${data.speed}`);\r\n            var posOffsetY = 0;\r\n            var height = 0;\r\n            let prefabIndex = 0; // 当前使用的 prefab 索引\r\n            while (height < totalHeight) {\r\n                // 循环使用 prefab\r\n                const curPrefab = scroll.scrollPrefabs[prefabIndex];\r\n                const child = instantiate(curPrefab);\r\n                const randomOffsetX = Math.random() * (scroll.splicingOffsetX!.max - scroll.splicingOffsetX!.min) + scroll.splicingOffsetX!.min;\r\n                child.setPosition(randomOffsetX, posOffsetY, 0);\r\n                var offY = 0;\r\n                if (scroll.splicingMode === LayerSplicingMode.node_height) {    \r\n                    offY = child.getComponent(UITransform)!.contentSize.height;\r\n                } else if (scroll.splicingMode === LayerSplicingMode.fix_height) {\r\n                    offY = 1334;\r\n                } else if (scroll.splicingMode === LayerSplicingMode.random_height) {\r\n                    offY = Math.max(scroll.splicingOffsetY!.min,scroll.splicingOffsetY!.max) + child.getComponent(UITransform)!.contentSize.height;\r\n                }\r\n                scrollsNode.addChild(child);\r\n                posOffsetY += offY;\r\n                height += offY;\r\n                prefabIndex = (prefabIndex + 1) % scroll.scrollPrefabs.length;\r\n            }\r\n        });\r\n    }\r\n\r\n    public fillLevelData(data: LevelDataLayer):void {\r\n        data.terrains = []\r\n        this.terrainsNode!.children.forEach((terrainNode) => {\r\n            // @ts-ignore\r\n            let uuid = terrainNode._prefab.asset._uuid;\r\n            if (terrainNode.getComponent(LevelPrefabParse)) {\r\n                uuid = `${uuid}.json`;\r\n            }\r\n            data.terrains.push({\r\n                uuid: uuid,\r\n                position: new Vec2(terrainNode.position.x, terrainNode.position.y),\r\n                scale: new Vec2(terrainNode.scale.x, terrainNode.scale.y),  \r\n                rotation: terrainNode.rotation.z\r\n            })\r\n        })\r\n\r\n        //data.dynamics = [] 在上层有保存其它信息，所以这里不清空\r\n        if (data.type === LayerType.Random) {\r\n            this.dynamicNode!.children.forEach((dynamic, index) => { \r\n                dynamic.children.forEach((dyna, dynaIndex) => {\r\n                    const match = dyna.name.match(/^rand_(\\d+)_(\\d+)$/); \r\n                    let groupIndex = 0;\r\n                    if (match) {\r\n                        groupIndex = parseInt(match[1]);\r\n                    }\r\n\r\n                    if (dyna.getComponent(LevelPrefabParse)) {\r\n                        // @ts-ignore\r\n                        data.dynamics[index]!.group[groupIndex]!.terrains[dynaIndex].uuid = `${dyna._prefab.asset._uuid}.json`;\r\n                    }\r\n                    \r\n                    //console.log(\"LevelEditorLayerUI fillLevelData data.dynamics\", data.dynamics.length);\r\n                    data.dynamics[index]!.group[groupIndex]!.position = v2(dynamic.position.x, dynamic.position.y);\r\n                    data.dynamics[index]!.group[groupIndex]!.scale = v2(dynamic.scale.x, dynamic.scale.y);\r\n                    data.dynamics[index]!.group[groupIndex]!.rotation = dynamic.rotation.z;\r\n                }) \r\n            });\r\n        }\r\n\r\n        if (data.type === LayerType.Emittier) {\r\n            data.emittiers = [];\r\n            this.emittiersNode!.children.forEach((emittierNode) => {\r\n                data.emittiers.push({ \r\n                    // @ts-ignore\r\n                    uuid: emittierNode._prefab.asset._uuid,\r\n                    position: v2(emittierNode.position.x, emittierNode.position.y),\r\n                    scale: v2(emittierNode.scale.x, emittierNode.scale.y),\r\n                    rotation: emittierNode.rotation.z,\r\n                })\r\n            })\r\n        }\r\n\r\n        data.events = [];\r\n        this.eventsNode!.children.forEach((eventNode) => {\r\n            var event = new LevelDataEvent();\r\n            var eventUIComp = eventNode.getComponent(LevelEditorEventUI);\r\n            eventUIComp!.fillLevelData(event);\r\n            data.events.push(event);\r\n        })\r\n    }\r\n\r\n    public tick(progress: number, totalTime:number, speed:number):void {\r\n        this.node.setPosition(0, - progress * totalTime * speed / 1000, 0);\r\n    }\r\n\r\n    // tick 一直在执行，play则是开启预览了执行\r\n    // 这里的progress和上面tick里的progress不同\r\n    public play(bPlay: boolean, progress: number) {\r\n        // tick eventsNode\r\n        this.eventsNode?.children.forEach((eventNode) => {\r\n            var eventUIComp = eventNode.getComponent(LevelEditorEventUI);\r\n            eventUIComp!.play(bPlay, progress);\r\n        })\r\n    }\r\n}"]}