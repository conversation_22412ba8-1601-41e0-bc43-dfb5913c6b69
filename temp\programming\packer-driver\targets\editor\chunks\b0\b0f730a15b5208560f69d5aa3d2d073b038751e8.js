System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, instantiate, Prefab, MyApp, SingletonBase, GameResourceList, Tools, EnemyData, BattleLayer, GameIns, BossManager, _crd;

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/bundles/common/script/app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfSingletonBase(extras) {
    _reporterNs.report("SingletonBase", "../../../../../scripts/core/base/SingletonBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameResourceList(extras) {
    _reporterNs.report("GameResourceList", "../const/GameResourceList", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "../utils/Tools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyData(extras) {
    _reporterNs.report("EnemyData", "../data/EnemyData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBattleLayer(extras) {
    _reporterNs.report("BattleLayer", "../ui/layer/BattleLayer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBossPlane(extras) {
    _reporterNs.report("BossPlane", "../ui/plane/boss/BossPlane", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../GameIns", _context.meta, extras);
  }

  _export("BossManager", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      instantiate = _cc.instantiate;
      Prefab = _cc.Prefab;
    }, function (_unresolved_2) {
      MyApp = _unresolved_2.MyApp;
    }, function (_unresolved_3) {
      SingletonBase = _unresolved_3.SingletonBase;
    }, function (_unresolved_4) {
      GameResourceList = _unresolved_4.default;
    }, function (_unresolved_5) {
      Tools = _unresolved_5.Tools;
    }, function (_unresolved_6) {
      EnemyData = _unresolved_6.EnemyData;
    }, function (_unresolved_7) {
      BattleLayer = _unresolved_7.default;
    }, function (_unresolved_8) {
      GameIns = _unresolved_8.GameIns;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "369abrLRSFI4qwLaw5SnC7H", "BossManager", undefined);

      __checkObsolete__(['instantiate', 'Node', 'Prefab']);

      _export("BossManager", BossManager = class BossManager extends (_crd && SingletonBase === void 0 ? (_reportPossibleCrUseOfSingletonBase({
        error: Error()
      }), SingletonBase) : SingletonBase) {
        constructor() {
          super();
          this._bossArr = [];
          this._pfBoss = null;
        }

        async preLoad() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.addLoadCount(1);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.load((_crd && GameResourceList === void 0 ? (_reportPossibleCrUseOfGameResourceList({
            error: Error()
          }), GameResourceList) : GameResourceList).PrefabBoss, Prefab, (error, prefab) => {
            this._pfBoss = prefab;
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.checkLoadFinish();
          });
        }

        mainReset() {
          this.subReset();
        }
        /**
         * 重置子关卡
         */


        subReset() {
          for (const boss of this._bossArr) {
            boss.node.parent = null;
            setTimeout(() => {
              boss.node.destroy();
            }, 1000);
          }

          this._bossArr = [];
        }
        /**
         * 添加 Boss
         * @param bossType Boss 类型
         * @param bossId Boss ID
         */


        addBoss(bossId) {
          const planeData = new (_crd && EnemyData === void 0 ? (_reportPossibleCrUseOfEnemyData({
            error: Error()
          }), EnemyData) : EnemyData)();
          planeData.planeId = bossId;

          if (!this._pfBoss) {
            throw new Error("Boss prefab is not initialized. Call preLoad() first.");
          }

          const node = instantiate(this._pfBoss);
          node.name = "boss";
          (_crd && BattleLayer === void 0 ? (_reportPossibleCrUseOfBattleLayer({
            error: Error()
          }), BattleLayer) : BattleLayer).instance.addEnemy(node);
          const boss = node.getComponent("BossPlane");
          boss.initPlane(planeData);

          this._bossArr.push(boss);

          return boss;
        }
        /**
         * 移除 Boss
         * @param boss 要移除的 Boss
         */


        removeBoss(boss) {
          (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).arrRemove(this._bossArr, boss);
          boss.node.parent = null;
          boss.node.destroy();
        }
        /**
         * 更新游戏逻辑
         * @param deltaTime 每帧时间
         */


        updateGameLogic(deltaTime) {
          for (let i = 0; i < this._bossArr.length; i++) {
            const boss = this._bossArr[i];

            if (boss.removeAble) {
              this.removeBoss(boss);
              i--;
            } else {
              boss.updateGameLogic(deltaTime);
            }
          }
        }
        /**
         * 开始 Boss 战斗
         */


        bossFightStart() {
          for (const boss of this._bossArr) {
            if (!boss.isDead) {
              boss.startBattle();
              break;
            }
          }
        }
        /**
         * 获取所有 Boss
         */


        get bosses() {
          return this._bossArr;
        }
        /**
         * 检查是否所有 Boss 已结束
         */


        isBossOver() {
          return this._bossArr.length === 0;
        }
        /**
         * 检查是否所有 Boss 已死亡
         */


        isBossDead() {
          for (const boss of this._bossArr) {
            if (!boss.isDead) {
              return false;
            }
          }

          return true;
        }

        setAnimSpeed(speed) {
          for (const boss of this._bossArr) {
            boss.setAnimSpeed(speed);
          }
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=b0f730a15b5208560f69d5aa3d2d073b038751e8.js.map