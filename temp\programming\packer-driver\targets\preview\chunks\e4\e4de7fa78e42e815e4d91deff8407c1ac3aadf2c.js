System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, GameEnum, PlaneBase, Bullet, Plane, Weapon, AttributeConst, eMoveEvent, DefaultMove, GameIns, _dec, _dec2, _class, _class2, _descriptor, _crd, ccclass, property, EnemyPlaneBase;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfGameEnum(extras) {
    _reporterNs.report("GameEnum", "../../../const/GameEnum", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneBase(extras) {
    _reporterNs.report("PlaneBase", "db://assets/bundles/common/script/game/ui/plane/PlaneBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFCollider(extras) {
    _reporterNs.report("FCollider", "../../../collider-system/FCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBullet(extras) {
    _reporterNs.report("Bullet", "../../../bullet/Bullet", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlane(extras) {
    _reporterNs.report("Plane", "db://assets/bundles/common/script/ui/Plane", _context.meta, extras);
  }

  function _reportPossibleCrUseOfWeapon(extras) {
    _reporterNs.report("Weapon", "db://assets/bundles/common/script/game/ui/plane/weapon/Weapon", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyData(extras) {
    _reporterNs.report("EnemyData", "../../../data/EnemyData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAttributeConst(extras) {
    _reporterNs.report("AttributeConst", "db://assets/bundles/common/script/const/AttributeConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeMoveEvent(extras) {
    _reporterNs.report("eMoveEvent", "db://assets/bundles/common/script/game/move/IMovable", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDefaultMove(extras) {
    _reporterNs.report("DefaultMove", "db://assets/bundles/common/script/game/move/DefaultMove", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../../GameIns", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_unresolved_2) {
      GameEnum = _unresolved_2.GameEnum;
    }, function (_unresolved_3) {
      PlaneBase = _unresolved_3.default;
    }, function (_unresolved_4) {
      Bullet = _unresolved_4.Bullet;
    }, function (_unresolved_5) {
      Plane = _unresolved_5.Plane;
    }, function (_unresolved_6) {
      Weapon = _unresolved_6.Weapon;
    }, function (_unresolved_7) {
      AttributeConst = _unresolved_7.AttributeConst;
    }, function (_unresolved_8) {
      eMoveEvent = _unresolved_8.eMoveEvent;
    }, function (_unresolved_9) {
      DefaultMove = _unresolved_9.DefaultMove;
    }, function (_unresolved_10) {
      GameIns = _unresolved_10.GameIns;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "d7856c6OStISpoCTCG+UjsB", "EnemyPlaneBase", undefined);

      __checkObsolete__(['_decorator']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", EnemyPlaneBase = (_dec = ccclass('EnemyPlaneBase'), _dec2 = property(_crd && Plane === void 0 ? (_reportPossibleCrUseOfPlane({
        error: Error()
      }), Plane) : Plane), _dec(_class = (_class2 = class EnemyPlaneBase extends (_crd && PlaneBase === void 0 ? (_reportPossibleCrUseOfPlaneBase({
        error: Error()
      }), PlaneBase) : PlaneBase) {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "plane", _descriptor, this);

          this._enemyData = null;
          this._moveCom = null;
          this.removeAble = false;
          this.bullets = [];
          this._weapons = [];
        }

        get moveCom() {
          return this._moveCom;
        }

        initPlane(data) {
          this.enemy = true;
          this._enemyData = data;
          super.init();
          this._moveCom = this.getComponent(_crd && DefaultMove === void 0 ? (_reportPossibleCrUseOfDefaultMove({
            error: Error()
          }), DefaultMove) : DefaultMove) || this.addComponent(_crd && DefaultMove === void 0 ? (_reportPossibleCrUseOfDefaultMove({
            error: Error()
          }), DefaultMove) : DefaultMove);

          this._moveCom.removeAllListeners();

          this._moveCom.on((_crd && eMoveEvent === void 0 ? (_reportPossibleCrUseOfeMoveEvent({
            error: Error()
          }), eMoveEvent) : eMoveEvent).onBecomeInvisible, () => this.onBecameInvisible());

          this._moveCom.on((_crd && eMoveEvent === void 0 ? (_reportPossibleCrUseOfeMoveEvent({
            error: Error()
          }), eMoveEvent) : eMoveEvent).onBecomeVisible, () => this.onBecameVisible()); // 初始化武器


          this._weapons = this.getComponentsInChildren(_crd && Weapon === void 0 ? (_reportPossibleCrUseOfWeapon({
            error: Error()
          }), Weapon) : Weapon);

          if (this._weapons.length > 0) {
            for (var weapon of this._weapons) {
              weapon.init();
              weapon.setOwner(this).setTarget((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).mainPlaneManager.mainPlane);
            }
          }

          this.refreshProperty();
        }

        initMove(angle) {
          var _config, _config2;

          if (!this._moveCom) {
            return;
          } // 速度从表格里读取


          this._moveCom.speed = ((_config = this._enemyData.config) == null ? void 0 : _config.moveSpeed) || 100;
          this._moveCom.speedAngle = angle;
          this._moveCom.turnSpeed = ((_config2 = this._enemyData.config) == null ? void 0 : _config2.turnSpeed) || 0;

          this._moveCom.setMovable(true);
        }

        _dieWhenOffScreen() {
          this.toDie((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).EnemyDestroyType.Leave);
        }

        onBecameInvisible() {
          // TODO: 从表格里增加延时销毁的配置
          this._dieWhenOffScreen(); // this.scheduleOnce(this._dieWhenOffScreen, delayDestroy);

        }

        onBecameVisible() {// this.unschedule(this._dieWhenOffScreen);
        }

        refreshProperty() {
          var _this$_enemyData;

          var config = (_this$_enemyData = this._enemyData) == null ? void 0 : _this$_enemyData.config;

          if (!config) {
            return;
          }

          this.attribute.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).MaxHPOutAdd, config.baseHp);
          this.attribute.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).AttackOutAdd, config.baseAtk);
          this.attribute.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).StatusImmuneBulletHurt, config.immuneBulletDamage ? 1 : 0);
          this.attribute.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).StatusImmuneCollisionHurt, config.immuneCollideDamage ? 1 : 0);
          this.attribute.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).StatusIgnoreBullet, config.ignoreBullet ? 1 : 0);
          this.attribute.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).StatusIgnoreCollision, config.ignoreCollide ? 1 : 0);
          this.attribute.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).StatusImmuneNuclearHurt, config.immuneNuke ? 1 : 0);
          this.attribute.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).StatusImmuneActiveSkillHurt, config.immuneActiveSkill ? 1 : 0);
          this.curHp = this.maxHp;
        }

        getAttack() {
          return this._enemyData.getAttack();
        }

        toDie(destroyType) {
          if (destroyType === void 0) {
            destroyType = (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyDestroyType.Die;
          }

          if (!super.toDie(destroyType)) {
            return false;
          }

          this.colliderEnabled = false;
          this.onDie(destroyType);
          return true;
        }

        onDie(destroyType) {
          this.willRemove();

          switch (destroyType) {
            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyDestroyType.Die:
              this.playDieAnim(() => {
                this.removeAble = true;
              });
              break;

            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyDestroyType.Leave:
            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyDestroyType.TrackOver:
            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyDestroyType.TimeOver:
              this.removeAble = true;
              break;
          }
        }

        playDieAnim(callBack) {
          // if (this.plane) {
          //     this.plane.playDieAnim(callBack);   
          // }
          callBack == null || callBack();
        }

        get collisionLevel() {
          var _config3;

          return ((_config3 = this._enemyData.config) == null ? void 0 : _config3.collideLevel) || 0;
        }

        get collisionHurt() {
          var _config4;

          return ((_config4 = this._enemyData.config) == null ? void 0 : _config4.collideDamage) || 0;
        }

        onCollide(collider) {
          if (this.isDead) {
            return;
          }

          if (collider.entity instanceof (_crd && Bullet === void 0 ? (_reportPossibleCrUseOfBullet({
            error: Error()
          }), Bullet) : Bullet)) {
            if (this.attribute.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
              error: Error()
            }), AttributeConst) : AttributeConst).StatusImmuneBulletHurt) == 0) {
              var damage = collider.entity.calcDamage(this);
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).hurtEffectManager.createHurtNumByType(collider.entity.node.getPosition(), damage);
              this.hurt(damage);
            }
          } else if (collider.entity instanceof (_crd && PlaneBase === void 0 ? (_reportPossibleCrUseOfPlaneBase({
            error: Error()
          }), PlaneBase) : PlaneBase) && !collider.entity.enemy) {
            this.collisionPlane(collider.entity);
          }
        }
        /**
         * 准备移除敌机
         */


        willRemove() {}

        addBullet(bullet) {
          if (this.bullets) {
            this.bullets.push(bullet);
          }
        }
        /**
         * 从敌人移除子弹
         * @param {Bullet} bullet 子弹对象
         */


        removeBullet(bullet) {
          if (this.bullets) {
            var index = this.bullets.indexOf(bullet);

            if (index >= 0) {
              this.bullets.splice(index, 1);
            }
          }
        }

        setPos(x, y) {
          this.node.setPosition(x, y);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "plane", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=e4de7fa78e42e815e4d91deff8407c1ac3aadf2c.js.map