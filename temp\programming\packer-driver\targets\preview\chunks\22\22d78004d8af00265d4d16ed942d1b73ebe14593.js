System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Label, v3, BaseUI, UILayer, UIMgr, logDebug, BundleName, DragButton, GmUI, _dec, _class, _crd, ccclass, property, GmButtonUI;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _reportPossibleCrUseOfBaseUI(extras) {
    _reporterNs.report("BaseUI", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUILayer(extras) {
    _reporterNs.report("UILayer", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogDebug(extras) {
    _reporterNs.report("logDebug", "../../../../scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBundleName(extras) {
    _reporterNs.report("BundleName", "../../../common/script/const/BundleConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDragButton(extras) {
    _reporterNs.report("DragButton", "../../../common/script/ui/common/components/button/DragButton", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGmUI(extras) {
    _reporterNs.report("GmUI", "./GmUI", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Label = _cc.Label;
      v3 = _cc.v3;
    }, function (_unresolved_2) {
      BaseUI = _unresolved_2.BaseUI;
      UILayer = _unresolved_2.UILayer;
      UIMgr = _unresolved_2.UIMgr;
    }, function (_unresolved_3) {
      logDebug = _unresolved_3.logDebug;
    }, function (_unresolved_4) {
      BundleName = _unresolved_4.BundleName;
    }, function (_unresolved_5) {
      DragButton = _unresolved_5.DragButton;
    }, function (_unresolved_6) {
      GmUI = _unresolved_6.GmUI;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "35dbawQJcdFsqER5mulOPUs", "GmButtonUI", undefined);

      __checkObsolete__(['_decorator', 'Label', 'v3']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("GmButtonUI", GmButtonUI = (_dec = ccclass('GmButtonUI'), _dec(_class = class GmButtonUI extends (_crd && BaseUI === void 0 ? (_reportPossibleCrUseOfBaseUI({
        error: Error()
      }), BaseUI) : BaseUI) {
        static getUrl() {
          return "prefab/ui/GmButtonUI";
        }

        static getLayer() {
          return (_crd && UILayer === void 0 ? (_reportPossibleCrUseOfUILayer({
            error: Error()
          }), UILayer) : UILayer).Top;
        }

        static getBundleName() {
          return (_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).Gm;
        }

        start() {
          this.node.position = v3(315, 12, 0);
          this.getComponent(_crd && DragButton === void 0 ? (_reportPossibleCrUseOfDragButton({
            error: Error()
          }), DragButton) : DragButton).addClick(this.onClick, this);
        }

        onClick() {
          return _asyncToGenerator(function* () {
            (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
              error: Error()
            }), logDebug) : logDebug)("GmButtonUI onClick", "aaaaaa");
            yield (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).openUI(_crd && GmUI === void 0 ? (_reportPossibleCrUseOfGmUI({
              error: Error()
            }), GmUI) : GmUI);
            yield (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).hideUI(GmButtonUI);
          })();
        }

        onShow(extraText) {
          var _this = this;

          return _asyncToGenerator(function* () {
            if (extraText) {
              _this.getComponentInChildren(Label).string = "GM" + "\n(" + extraText + ")";
            } else {
              _this.getComponentInChildren(Label).string = "GM";
            }
          })();
        }

        onHide() {
          return _asyncToGenerator(function* () {})();
        }

        onClose() {
          return _asyncToGenerator(function* () {})();
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=22d78004d8af00265d4d16ed942d1b73ebe14593.js.map