[{"id": 80000101, "name": "测试道具#1", "icon": "icon1", "quality": 1, "quality_sub": 0, "use_type": 0, "effect_id": 0, "effect_param1": 0, "effect_param2": 0, "max_stack_num": 9999}, {"id": 80000102, "name": "测试道具#2", "icon": "icon2", "quality": 3, "quality_sub": 0, "use_type": 0, "effect_id": 0, "effect_param1": 0, "effect_param2": 0, "max_stack_num": 9999}, {"id": 80000103, "name": "测试道具#3", "icon": "icon3", "quality": 2, "quality_sub": 0, "use_type": 0, "effect_id": 0, "effect_param1": 0, "effect_param2": 0, "max_stack_num": 9999}, {"id": 80000104, "name": "测试道具#4", "icon": "icon4", "quality": 4, "quality_sub": 0, "use_type": 0, "effect_id": 0, "effect_param1": 0, "effect_param2": 0, "max_stack_num": 9999}, {"id": 80000105, "name": "测试道具#5", "icon": "icon5", "quality": 5, "quality_sub": 0, "use_type": 0, "effect_id": 0, "effect_param1": 0, "effect_param2": 0, "max_stack_num": 9999}, {"id": 80000106, "name": "测试道具#6", "icon": "icon6", "quality": 6, "quality_sub": 0, "use_type": 0, "effect_id": 0, "effect_param1": 0, "effect_param2": 0, "max_stack_num": 9999}, {"id": 80100101, "name": "周末宝箱", "icon": "icon2", "quality": 3, "quality_sub": 0, "use_type": 1, "effect_id": 1, "effect_param1": 30100101, "effect_param2": 0, "max_stack_num": 100}, {"id": 80010201, "name": "剧情关掉落装备宝箱", "icon": "icon3", "quality": 3, "quality_sub": 0, "use_type": 1, "effect_id": 1, "effect_param1": 30100103, "effect_param2": 0, "max_stack_num": 100}, {"id": 80200101, "name": "稀有战机1碎片", "icon": "icon1", "quality": 3, "quality_sub": 0, "use_type": 0, "effect_id": 0, "effect_param1": 0, "effect_param2": 0, "max_stack_num": 9999}, {"id": 80200102, "name": "稀有战机2碎片", "icon": "icon1", "quality": 3, "quality_sub": 0, "use_type": 0, "effect_id": 0, "effect_param1": 0, "effect_param2": 0, "max_stack_num": 9999}, {"id": 80200103, "name": "稀有战机3碎片", "icon": "icon1", "quality": 3, "quality_sub": 0, "use_type": 0, "effect_id": 0, "effect_param1": 0, "effect_param2": 0, "max_stack_num": 9999}, {"id": 80200104, "name": "稀有战机4碎片", "icon": "icon1", "quality": 3, "quality_sub": 0, "use_type": 0, "effect_id": 0, "effect_param1": 0, "effect_param2": 0, "max_stack_num": 9999}, {"id": 80200105, "name": "稀有战机5碎片", "icon": "icon1", "quality": 3, "quality_sub": 0, "use_type": 0, "effect_id": 0, "effect_param1": 0, "effect_param2": 0, "max_stack_num": 9999}, {"id": 80200106, "name": "稀有战机6碎片", "icon": "icon1", "quality": 3, "quality_sub": 0, "use_type": 0, "effect_id": 0, "effect_param1": 0, "effect_param2": 0, "max_stack_num": 9999}, {"id": 80200107, "name": "稀有战机7碎片", "icon": "icon1", "quality": 3, "quality_sub": 0, "use_type": 0, "effect_id": 0, "effect_param1": 0, "effect_param2": 0, "max_stack_num": 9999}, {"id": 80200201, "name": "史诗战机1碎片", "icon": "icon8", "quality": 4, "quality_sub": 0, "use_type": 0, "effect_id": 0, "effect_param1": 0, "effect_param2": 0, "max_stack_num": 9999}, {"id": 80200202, "name": "史诗战机2碎片", "icon": "icon9", "quality": 4, "quality_sub": 0, "use_type": 0, "effect_id": 0, "effect_param1": 0, "effect_param2": 0, "max_stack_num": 9999}, {"id": 80200203, "name": "史诗战机3碎片", "icon": "icon10", "quality": 4, "quality_sub": 0, "use_type": 0, "effect_id": 0, "effect_param1": 0, "effect_param2": 0, "max_stack_num": 9999}, {"id": 80200301, "name": "传说战机1碎片", "icon": "icon11", "quality": 5, "quality_sub": 0, "use_type": 0, "effect_id": 0, "effect_param1": 0, "effect_param2": 0, "max_stack_num": 9999}, {"id": 80200302, "name": "传说战机2碎片", "icon": "icon12", "quality": 5, "quality_sub": 0, "use_type": 0, "effect_id": 0, "effect_param1": 0, "effect_param2": 0, "max_stack_num": 9999}, {"id": 80203101, "name": "合成材料1", "icon": "icon4", "quality": 1, "quality_sub": 0, "use_type": 0, "effect_id": 0, "effect_param1": 0, "effect_param2": 0, "max_stack_num": 9999}, {"id": 80203201, "name": "合成材料2", "icon": "icon5", "quality": 3, "quality_sub": 0, "use_type": 0, "effect_id": 0, "effect_param1": 0, "effect_param2": 0, "max_stack_num": 9999}, {"id": 80203301, "name": "合成材料3", "icon": "icon6", "quality": 2, "quality_sub": 0, "use_type": 0, "effect_id": 0, "effect_param1": 0, "effect_param2": 0, "max_stack_num": 9999}, {"id": 80203401, "name": "合成材料4", "icon": "icon7", "quality": 4, "quality_sub": 0, "use_type": 0, "effect_id": 0, "effect_param1": 0, "effect_param2": 0, "max_stack_num": 9999}, {"id": 80203501, "name": "合成材料5", "icon": "icon8", "quality": 5, "quality_sub": 0, "use_type": 0, "effect_id": 0, "effect_param1": 0, "effect_param2": 0, "max_stack_num": 9999}, {"id": 80203601, "name": "合成材料6", "icon": "icon9", "quality": 6, "quality_sub": 0, "use_type": 0, "effect_id": 0, "effect_param1": 0, "effect_param2": 0, "max_stack_num": 9999}, {"id": 80220199, "name": "升级材料1", "icon": "icon10", "quality": 1, "quality_sub": 0, "use_type": 0, "effect_id": 0, "effect_param1": 0, "effect_param2": 0, "max_stack_num": 9999}, {"id": 80220299, "name": "升级材料2", "icon": "icon11", "quality": 1, "quality_sub": 0, "use_type": 0, "effect_id": 0, "effect_param1": 0, "effect_param2": 0, "max_stack_num": 9999}, {"id": 80220399, "name": "升级材料3", "icon": "icon12", "quality": 1, "quality_sub": 0, "use_type": 0, "effect_id": 0, "effect_param1": 0, "effect_param2": 0, "max_stack_num": 9999}, {"id": 80220499, "name": "升级材料4", "icon": "icon13", "quality": 1, "quality_sub": 0, "use_type": 0, "effect_id": 0, "effect_param1": 0, "effect_param2": 0, "max_stack_num": 9999}, {"id": 80300101, "name": "主武器仓零件", "icon": "icon1", "quality": 1, "quality_sub": 0, "use_type": 0, "effect_id": 0, "effect_param1": 0, "effect_param2": 0, "max_stack_num": 9999}, {"id": 80300102, "name": "战术模块零件", "icon": "icon2", "quality": 1, "quality_sub": 0, "use_type": 0, "effect_id": 0, "effect_param1": 0, "effect_param2": 0, "max_stack_num": 9999}, {"id": 80300103, "name": "探测系统零件", "icon": "icon3", "quality": 1, "quality_sub": 0, "use_type": 0, "effect_id": 0, "effect_param1": 0, "effect_param2": 0, "max_stack_num": 9999}, {"id": 80300104, "name": "能源核心零件", "icon": "icon4", "quality": 1, "quality_sub": 0, "use_type": 0, "effect_id": 0, "effect_param1": 0, "effect_param2": 0, "max_stack_num": 9999}, {"id": 80300105, "name": "机身装甲零件", "icon": "icon5", "quality": 1, "quality_sub": 0, "use_type": 0, "effect_id": 0, "effect_param1": 0, "effect_param2": 0, "max_stack_num": 9999}, {"id": 80300106, "name": "动力引擎零件", "icon": "icon6", "quality": 1, "quality_sub": 0, "use_type": 0, "effect_id": 0, "effect_param1": 0, "effect_param2": 0, "max_stack_num": 9999}, {"id": 89999999, "name": "金币", "icon": "icon_coin", "quality": 1, "quality_sub": 0, "use_type": 2, "effect_id": 2, "effect_param1": 1, "effect_param2": 0, "max_stack_num": 99999999}, {"id": 89999998, "name": "钻石", "icon": "icon_daimond", "quality": 3, "quality_sub": 0, "use_type": 2, "effect_id": 3, "effect_param1": 1, "effect_param2": 0, "max_stack_num": 99999999}, {"id": 89999997, "name": "体力", "icon": "icon_energy", "quality": 1, "quality_sub": 0, "use_type": 2, "effect_id": 5, "effect_param1": 1, "effect_param2": 0, "max_stack_num": 99999999}, {"id": 89990011, "name": "金币袋子666", "icon": "icon_coinpackage", "quality": 3, "quality_sub": 0, "use_type": 1, "effect_id": 2, "effect_param1": 666, "effect_param2": 0, "max_stack_num": 9999}, {"id": 89999996, "name": "经验", "icon": "icon_xp", "quality": 1, "quality_sub": 0, "use_type": 2, "effect_id": 4, "effect_param1": 1, "effect_param2": 0, "max_stack_num": 99999999}, {"id": 89999901, "name": "月卡10101", "icon": "icon_vip", "quality": 1, "quality_sub": 0, "use_type": 2, "effect_id": 7, "effect_param1": 10101, "effect_param2": 0, "max_stack_num": 1}, {"id": 89999801, "name": "任务轨道门票", "icon": "icon1", "quality": 1, "quality_sub": 0, "use_type": 2, "effect_id": 11, "effect_param1": 401, "effect_param2": 0, "max_stack_num": 1}]