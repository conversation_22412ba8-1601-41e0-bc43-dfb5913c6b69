{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/data/game_logic/GameLogic.ts"], "names": ["GameLogic", "csproto", "MessageBox", "log<PERSON>arn", "<PERSON>", "MyApp", "EventManager", "GameEvent", "DataMgr", "gameID", "ZERO", "chapterID", "result", "init", "netMgr", "registerHandler", "cs", "CS_CMD", "CS_CMD_GAME_START", "onGameStart", "CS_CMD_GAME_END", "onGameEnd", "CS_CMD_GAME_GET_INFO", "onGameGetInfo", "CS_CMD_GAME_GET_REWARD", "onGameGetReward", "update", "msg", "ret_code", "comm", "RET_CODE", "RET_CODE_NO_ERROR", "errorCode", "data", "body", "game_start", "start_info", "base", "game_id", "mode_type", "chapter_id", "Instance", "emit", "onNetGameStart", "game_end", "onNetGameOver", "game_get_info", "status", "ROLE_GAME_STATUS", "ROLE_GAME_STATUS_FIGHTING", "role", "cmdGetClientSettingBattle", "ROLE_GAME_STATUS_END", "game_get_reward", "gain_items", "cmdGameStart", "modeID", "partnerUin", "sendMessage", "mode_id", "partner_uin", "cmdGameEnd", "result_info", "level_result_info", "level_result", "cmdGameGetInfo", "cmdGameGetReward", "is_double", "checkGameID"], "mappings": ";;;2GAUaA,S;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAVNC,MAAAA,O;;AACEC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,I;;AACEC,MAAAA,K,iBAAAA,K;;AAEFC,MAAAA,Y;;AACEC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,O,iBAAAA,O;;;;;;;2BAEIR,S,GAAN,MAAMA,SAAN,CAAiC;AAAA;AAAA,eACpCS,MADoC,GACrB;AAAA;AAAA,4BAAKC,IADgB;AAAA,eAEpCC,SAFoC,GAEhB,CAFgB;AAAA,eAGpCC,MAHoC,GAGM,IAHN;AAAA;;AAIpCC,QAAAA,IAAI,GAAG;AACH;AAAA;AAAA,8BAAMC,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBC,iBAA/C,EAAkE,KAAKC,WAAvE,EAAoF,IAApF;AACA;AAAA;AAAA,8BAAML,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBG,eAA/C,EAAgE,KAAKC,SAArE,EAAgF,IAAhF;AACA;AAAA;AAAA,8BAAMP,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBK,oBAA/C,EAAqE,KAAKC,aAA1E,EAAyF,IAAzF;AACA;AAAA;AAAA,8BAAMT,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBO,sBAA/C,EAAuE,KAAKC,eAA5E,EAA6F,IAA7F;AACH;;AACDC,QAAAA,MAAM,GAAS,CACd,CAXmC,CAYpC;;;AACAP,QAAAA,WAAW,CAACQ,GAAD,EAAgC;AAAA;;AACvC;AACA,cAAIA,GAAG,CAACC,QAAJ,IAAgB;AAAA;AAAA,kCAAQC,IAAR,CAAaC,QAAb,CAAsBC,iBAA1C,EAA6D;AACzD;AAAA;AAAA,oCAAQ,QAAR,0BAAwCJ,GAAG,CAACC,QAA5C;AACA;AAAA;AAAA,0CAAWI,SAAX,CAAqBL,GAAG,CAACC,QAAzB;AACA;AACH;;AACD,cAAIK,IAAI,gBAAGN,GAAG,CAACO,IAAP,qBAAG,UAAUC,UAArB;;AACA,cAAI,CAACF,IAAL,EAAW;AACP;AAAA;AAAA,oCAAQ,QAAR,EAAkB,0BAAlB;AACA;AACH;;AACD,eAAKxB,MAAL,uBAAcwB,IAAI,CAACG,UAAnB,iCAAc,iBAAiBC,IAA/B,qBAAc,iBAAuBC,OAArC;AACA,cAAIC,SAAmB,wBAAGN,IAAI,CAACG,UAAR,kCAAG,kBAAiBC,IAApB,qBAAG,kBAAuBE,SAAjD;AACA,eAAK5B,SAAL,wBAAiBsB,IAAI,CAACG,UAAtB,kCAAiB,kBAAiBC,IAAlC,qBAAiB,kBAAuBG,UAAxC;AAEA;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,IAAtB,CAA2B;AAAA;AAAA,sCAAUC,cAArC;AACH;;AAEDtB,QAAAA,SAAS,CAACM,GAAD,EAAgC;AAAA;;AACrC;AACA,cAAIA,GAAG,CAACC,QAAJ,IAAgB;AAAA;AAAA,kCAAQC,IAAR,CAAaC,QAAb,CAAsBC,iBAA1C,EAA6D;AACzD;AAAA;AAAA,oCAAQ,QAAR,wBAAsCJ,GAAG,CAACC,QAA1C;AACA;AAAA;AAAA,0CAAWI,SAAX,CAAqBL,GAAG,CAACC,QAAzB;AACA;AACH;;AACD,cAAIK,IAAI,iBAAGN,GAAG,CAACO,IAAP,qBAAG,WAAUU,QAArB;;AACA,cAAI,CAACX,IAAL,EAAW;AACP;AAAA;AAAA,oCAAQ,QAAR,EAAkB,wBAAlB;AACA;AACH;;AACDA,UAAAA,IAAI,CAACrB,MAAL,GAAcqB,IAAI,CAACrB,MAAnB;AACA;AAAA;AAAA,4CAAa6B,QAAb,CAAsBC,IAAtB,CAA2B;AAAA;AAAA,sCAAUG,aAArC;AACH;;AAEDtB,QAAAA,aAAa,CAACI,GAAD,EAAgC;AAAA;;AACzC,cAAIA,GAAG,CAACC,QAAJ,IAAgB;AAAA;AAAA,kCAAQC,IAAR,CAAaC,QAAb,CAAsBC,iBAA1C,EAA6D;AACzD;AAAA;AAAA,oCAAQ,QAAR,4BAA0CJ,GAAG,CAACC,QAA9C;AACA;AACH;;AACD,cAAIK,IAAI,iBAAGN,GAAG,CAACO,IAAP,qBAAG,WAAUY,aAArB;;AACA,cAAI,CAACb,IAAL,EAAW;AACP;AAAA;AAAA,oCAAQ,QAAR,EAAkB,4BAAlB;AACA;AACH;;AACD,cAAIc,MAAM,GAAGd,IAAI,CAACc,MAAlB;;AACA,cAAIA,MAAM,IAAI;AAAA;AAAA,kCAAQlB,IAAR,CAAamB,gBAAb,CAA8BC,yBAA5C,EAAuE;AACnE;AACA,gBAAIb,UAAU,GAAGH,IAAI,CAACG,UAAtB;AACA;AAAA;AAAA,oCAAQc,IAAR,CAAaC,yBAAb;AACH,WAJD,MAKK,IAAIJ,MAAM,IAAI;AAAA;AAAA,kCAAQlB,IAAR,CAAamB,gBAAb,CAA8BI,oBAA5C,EAAkE;AACnE,gBAAIxC,MAAM,GAAGqB,IAAI,CAACrB,MAAlB;AACA,gBAAI0B,OAAO,GAAG1B,MAAH,oBAAGA,MAAM,CAAE0B,OAAtB,CAFmE,CAGnE;AACH;AACJ;;AAEDb,QAAAA,eAAe,CAACE,GAAD,EAAgC;AAAA;;AAC3C,cAAIA,GAAG,CAACC,QAAJ,IAAgB;AAAA;AAAA,kCAAQC,IAAR,CAAaC,QAAb,CAAsBC,iBAA1C,EAA6D;AACzD;AAAA;AAAA,oCAAQ,QAAR,8BAA4CJ,GAAG,CAACC,QAAhD;AACA;AACH;;AACD,cAAIK,IAAI,iBAAGN,GAAG,CAACO,IAAP,qBAAG,WAAUmB,eAArB;;AACA,cAAI,CAACpB,IAAL,EAAW;AACP;AAAA;AAAA,oCAAQ,QAAR,EAAkB,8BAAlB;AACA;AACH;;AACD,cAAIK,OAAO,GAAGL,IAAI,CAACK,OAAnB;AACA,cAAIgB,UAAU,GAAGrB,IAAI,CAACqB,UAAtB;AACH,SAnFmC,CAoFpC;AACA;;AACA;AACJ;AACA;AACA;AACA;;;AACIC,QAAAA,YAAY,CAACC,MAAD,EAAiBC,UAAjB,EAA+C;AAAA,cAA9BA,UAA8B;AAA9BA,YAAAA,UAA8B,GAAX;AAAA;AAAA,8BAAK/C,IAAM;AAAA;;AACvD;AAAA;AAAA,8BAAMI,MAAN,CAAa4C,WAAb,CAAyB;AAAA;AAAA,kCAAQ1C,EAAR,CAAWC,MAAX,CAAkBC,iBAA3C,EAA8D;AAC1DiB,YAAAA,UAAU,EAAE;AACRwB,cAAAA,OAAO,EAAEH,MADD;AAERI,cAAAA,WAAW,EAAEH;AAFL;AAD8C,WAA9D;AAMH;;AACDI,QAAAA,UAAU,CAACC,WAAD,EAA+CC,iBAA/C,EAA0G;AAChH,cAAI,CAAC,KAAKtD,MAAV,EAAkB;AACd;AAAA;AAAA,oCAAQ,WAAR,EAAqB,gBAArB;AACA;AACH;;AACD;AAAA;AAAA,8BAAMK,MAAN,CAAa4C,WAAb,CAAyB;AAAA;AAAA,kCAAQ1C,EAAR,CAAWC,MAAX,CAAkBG,eAA3C,EAA4D;AACxDwB,YAAAA,QAAQ,EAAE;AACNN,cAAAA,OAAO,EAAE,KAAK7B,MADR;AAENG,cAAAA,MAAM,EAAEkD,WAFF;AAGNE,cAAAA,YAAY,EAAED;AAHR;AAD8C,WAA5D;AAOH;;AACDE,QAAAA,cAAc,GAAG;AACb;AAAA;AAAA,8BAAMnD,MAAN,CAAa4C,WAAb,CAAyB;AAAA;AAAA,kCAAQ1C,EAAR,CAAWC,MAAX,CAAkBK,oBAA3C,EAAiE;AAC7DwB,YAAAA,aAAa,EAAE;AAD8C,WAAjE;AAGH;;AACDoB,QAAAA,gBAAgB,CAACzD,MAAD,EAAe0D,SAAf,EAA2C;AAAA,cAA5BA,SAA4B;AAA5BA,YAAAA,SAA4B,GAAP,KAAO;AAAA;;AACvD;AAAA;AAAA,8BAAMrD,MAAN,CAAa4C,WAAb,CAAyB;AAAA;AAAA,kCAAQ1C,EAAR,CAAWC,MAAX,CAAkBO,sBAA3C,EAAmE;AAC/D6B,YAAAA,eAAe,EAAE;AACbf,cAAAA,OAAO,EAAE7B,MADI;AAEb0D,cAAAA,SAAS,EAAGA,SAAS,GAAG,CAAH,GAAO;AAFf;AAD8C,WAAnE;AAMH,SA5HmC,CA6HpC;AACA;;;AACAC,QAAAA,WAAW,CAAC3D,MAAD,EAAe,CACtB;AACA;AACA;AACA;AACH,SApImC,CAqIpC;;;AArIoC,O", "sourcesContent": ["import csproto from 'db://assets/bundles/common/script/autogen/pb/cs_proto.js';\r\nimport { MessageBox } from \"db://assets/bundles/common/script/ui/common/components/messagebox/MessageBox\";\r\nimport { logWarn } from \"db://assets/scripts/utils/Logger\";\r\nimport Long from \"long\";\r\nimport { MyApp } from \"../../app/MyApp\";\r\nimport { ModeType } from \"../../autogen/luban/schema\";\r\nimport EventManager from '../../event/EventManager';\r\nimport { GameEvent } from '../../game/event/GameEvent';\r\nimport { DataMgr, IData } from \"../DataManager\";\r\n\r\nexport class GameLogic implements IData {\r\n    gameID: Long = Long.ZERO;\r\n    chapterID: number = 0;\r\n    result: csproto.comm.IGameResult | null = null;\r\n    init() {\r\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GAME_START, this.onGameStart, this);\r\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GAME_END, this.onGameEnd, this);\r\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GAME_GET_INFO, this.onGameGetInfo, this);\r\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GAME_GET_REWARD, this.onGameGetReward, this);\r\n    }\r\n    update(): void {\r\n    }\r\n    //#region 收协议相关\r\n    onGameStart(msg: csproto.cs.IS2CMsg): void {\r\n        // MessageBox.testToast(\"收到协议返回，开始战斗\");\r\n        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {\r\n            logWarn(\"NetMgr\", `onGameStart failed ${msg.ret_code}`);\r\n            MessageBox.errorCode(msg.ret_code!);\r\n            return;\r\n        }\r\n        var data = msg.body?.game_start;\r\n        if (!data) {\r\n            logWarn(\"NetMgr\", \"onGameStart data is null\");\r\n            return;\r\n        }\r\n        this.gameID = data.start_info?.base?.game_id!;\r\n        var mode_type: ModeType = data.start_info?.base?.mode_type!;\r\n        this.chapterID = data.start_info?.base?.chapter_id!;\r\n\r\n        EventManager.Instance.emit(GameEvent.onNetGameStart);\r\n    }\r\n\r\n    onGameEnd(msg: csproto.cs.IS2CMsg): void {\r\n        // MessageBox.testToast(\"收到协议返回，战斗结束\");\r\n        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {\r\n            logWarn(\"NetMgr\", `onGameEnd failed ${msg.ret_code}`);\r\n            MessageBox.errorCode(msg.ret_code!);\r\n            return;\r\n        }\r\n        var data = msg.body?.game_end;\r\n        if (!data) {\r\n            logWarn(\"NetMgr\", \"onGameEnd data is null\");\r\n            return;\r\n        }\r\n        data.result = data.result!;\r\n        EventManager.Instance.emit(GameEvent.onNetGameOver);\r\n    }\r\n\r\n    onGameGetInfo(msg: csproto.cs.IS2CMsg): void {\r\n        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {\r\n            logWarn(\"NetMgr\", `onGameGetInfo failed ${msg.ret_code}`);\r\n            return;\r\n        }\r\n        var data = msg.body?.game_get_info;\r\n        if (!data) {\r\n            logWarn(\"NetMgr\", \"onGameGetInfo data is null\");\r\n            return;\r\n        }\r\n        let status = data.status;\r\n        if (status == csproto.comm.ROLE_GAME_STATUS.ROLE_GAME_STATUS_FIGHTING) {\r\n            //todo  战斗未结束，获取 之前存 的keys\r\n            let start_info = data.start_info;\r\n            DataMgr.role.cmdGetClientSettingBattle();\r\n        }\r\n        else if (status == csproto.comm.ROLE_GAME_STATUS.ROLE_GAME_STATUS_END) {\r\n            let result = data.result;\r\n            let game_id = result?.game_id;\r\n            //this.cmdGameGetReward(game_id!);\r\n        }\r\n    }\r\n\r\n    onGameGetReward(msg: csproto.cs.IS2CMsg): void {\r\n        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {\r\n            logWarn(\"NetMgr\", `onGameGetReward failed ${msg.ret_code}`);\r\n            return;\r\n        }\r\n        var data = msg.body?.game_get_reward;\r\n        if (!data) {\r\n            logWarn(\"NetMgr\", \"onGameGetReward data is null\");\r\n            return;\r\n        }\r\n        let game_id = data.game_id;\r\n        let gain_items = data.gain_items;\r\n    }\r\n    //#endregion\r\n    //#region 发送协议相关\r\n    /** 开始游戏\r\n     * \r\n     * @param modeID 模式id--无尽=101，来自模式表\r\n     * @param partnerUin 合体好友的uin，默认为0，无合体\r\n     */\r\n    cmdGameStart(modeID: number, partnerUin: Long = Long.ZERO) {\r\n        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_GAME_START, {\r\n            game_start: {\r\n                mode_id: modeID,\r\n                partner_uin: partnerUin,\r\n            }\r\n        })\r\n    }\r\n    cmdGameEnd(result_info: csproto.comm.IGameResult | null, level_result_info: csproto.comm.IGameLevelResult[] | null) {\r\n        if (!this.gameID) {\r\n            logWarn(\"GameLogic\", \"gameID is null\");\r\n            return;\r\n        }\r\n        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_GAME_END, {\r\n            game_end: {\r\n                game_id: this.gameID,\r\n                result: result_info,\r\n                level_result: level_result_info,\r\n            }\r\n        })\r\n    }\r\n    cmdGameGetInfo() {\r\n        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_GAME_GET_INFO, {\r\n            game_get_info: {}\r\n        });\r\n    }\r\n    cmdGameGetReward(gameID: Long, is_double: boolean = false) {\r\n        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_GAME_GET_REWARD, {\r\n            game_get_reward: {\r\n                game_id: gameID,\r\n                is_double: (is_double ? 2 : 1),\r\n            }\r\n        })\r\n    }\r\n    //#endregion\r\n    //#region 外部调用相关\r\n    checkGameID(gameID: Long) {\r\n        // if (gameID.isZero()) {\r\n        //     return;\r\n        // }\r\n        //this.cmdGameGetInfo();\r\n    }\r\n    //#endregion\r\n}"]}