System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, assetManager, CCFloat, CCInteger, Enum, Node, Prefab, LayerSplicingMode, LayerType, LevelEditorUtils, _dec, _dec2, _dec3, _class, _class2, _descriptor, _descriptor2, _dec4, _dec5, _dec6, _dec7, _dec8, _dec9, _dec10, _class4, _class5, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _dec11, _dec12, _dec13, _dec14, _dec15, _class7, _class8, _descriptor8, _descriptor9, _descriptor10, _descriptor11, _dec16, _dec17, _dec18, _class10, _class11, _descriptor12, _descriptor13, _dec19, _dec20, _class13, _class14, _descriptor14, _dec21, _dec22, _dec23, _dec24, _dec25, _dec26, _dec27, _dec28, _dec29, _class16, _class17, _descriptor15, _descriptor16, _descriptor17, _descriptor18, _descriptor19, _descriptor20, _descriptor21, _dec30, _dec31, _class19, _class20, _descriptor22, _crd, ccclass, property, LayerTypeZh, LayerSplicingModeZh, LayerEditorRandomRange, LevelScrollLayerUI, LevelRandTerrainUI, LevelRandTerrainsLayerUI, LevelRandTerrainsLayersUI, LevelLayer, LevelBackgroundLayer;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfLayerSplicingMode(extras) {
    _reporterNs.report("LayerSplicingMode", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLayerType(extras) {
    _reporterNs.report("LayerType", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  _export("LevelEditorUtils", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      assetManager = _cc.assetManager;
      CCFloat = _cc.CCFloat;
      CCInteger = _cc.CCInteger;
      Enum = _cc.Enum;
      Node = _cc.Node;
      Prefab = _cc.Prefab;
    }, function (_unresolved_2) {
      LayerSplicingMode = _unresolved_2.LayerSplicingMode;
      LayerType = _unresolved_2.LayerType;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "afa21Rw2yVKupu5NJaG6p2/", "utils", undefined);

      __checkObsolete__(['__private', '_decorator', 'assetManager', 'Asset', 'CCFloat', 'CCInteger', 'Component', 'Enum', 'Node', 'Prefab', 'TerrainLayer', 'CCBoolean']);

      ({
        ccclass,
        property
      } = _decorator);

      LayerTypeZh = function (LayerTypeZh) {
        LayerTypeZh[LayerTypeZh["\u80CC\u666F"] = (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
          error: Error()
        }), LayerType) : LayerType).Background] = "\u80CC\u666F";
        LayerTypeZh[LayerTypeZh["\u968F\u673A"] = (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
          error: Error()
        }), LayerType) : LayerType).Random] = "\u968F\u673A";
        LayerTypeZh[LayerTypeZh["\u6EDA\u52A8"] = (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
          error: Error()
        }), LayerType) : LayerType).Scroll] = "\u6EDA\u52A8";
        LayerTypeZh[LayerTypeZh["\u53D1\u5C04"] = (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
          error: Error()
        }), LayerType) : LayerType).Emittier] = "\u53D1\u5C04";
        return LayerTypeZh;
      }(LayerTypeZh || {});

      LayerSplicingModeZh = function (LayerSplicingModeZh) {
        LayerSplicingModeZh[LayerSplicingModeZh["\u8282\u70B9\u9AD8\u5EA6\u62FC\u63A5"] = (_crd && LayerSplicingMode === void 0 ? (_reportPossibleCrUseOfLayerSplicingMode({
          error: Error()
        }), LayerSplicingMode) : LayerSplicingMode).node_height] = "\u8282\u70B9\u9AD8\u5EA6\u62FC\u63A5";
        LayerSplicingModeZh[LayerSplicingModeZh["\u56FA\u5B9A\u9AD8\u5EA6\u62FC\u63A5"] = (_crd && LayerSplicingMode === void 0 ? (_reportPossibleCrUseOfLayerSplicingMode({
          error: Error()
        }), LayerSplicingMode) : LayerSplicingMode).fix_height] = "\u56FA\u5B9A\u9AD8\u5EA6\u62FC\u63A5";
        LayerSplicingModeZh[LayerSplicingModeZh["\u968F\u673A\u9AD8\u5EA6\u62FC\u63A5"] = (_crd && LayerSplicingMode === void 0 ? (_reportPossibleCrUseOfLayerSplicingMode({
          error: Error()
        }), LayerSplicingMode) : LayerSplicingMode).random_height] = "\u968F\u673A\u9AD8\u5EA6\u62FC\u63A5";
        return LayerSplicingModeZh;
      }(LayerSplicingModeZh || {});

      _export("LayerEditorRandomRange", LayerEditorRandomRange = (_dec = ccclass('LayerEditorRandomRange'), _dec2 = property({
        displayName: "最小值"
      }), _dec3 = property({
        displayName: "最大值"
      }), _dec(_class = (_class2 = class LayerEditorRandomRange {
        constructor(min, max) {
          if (min === void 0) {
            min = 0;
          }

          if (max === void 0) {
            max = 0;
          }

          _initializerDefineProperty(this, "min", _descriptor, this);

          _initializerDefineProperty(this, "max", _descriptor2, this);

          this.min = min;
          this.max = max;
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "min", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "max", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      })), _class2)) || _class));

      _export("LevelScrollLayerUI", LevelScrollLayerUI = (_dec4 = ccclass('LevelEditorScrollLayerUI'), _dec5 = property({
        type: [Prefab],
        displayName: '滚动体'
      }), _dec6 = property({
        type: CCInteger,
        displayName: "权重"
      }), _dec7 = property({
        visible: false
      }), _dec8 = property({
        type: Enum(LayerSplicingModeZh),
        displayName: "拼接模式"
      }), _dec9 = property({
        type: LayerEditorRandomRange,
        displayName: "X偏移范围",
        visible: function visible() {
          return this.splicingMode === (_crd && LayerSplicingMode === void 0 ? (_reportPossibleCrUseOfLayerSplicingMode({
            error: Error()
          }), LayerSplicingMode) : LayerSplicingMode).random_height;
        }
      }), _dec10 = property({
        type: LayerEditorRandomRange,
        displayName: "Y偏移范围",
        visible: function visible() {
          return this.splicingMode === (_crd && LayerSplicingMode === void 0 ? (_reportPossibleCrUseOfLayerSplicingMode({
            error: Error()
          }), LayerSplicingMode) : LayerSplicingMode).random_height;
        }
      }), _dec4(_class4 = (_class5 = class LevelScrollLayerUI {
        get splicingModeZh() {
          return this.splicingMode;
        }

        set splicingModeZh(value) {
          this.splicingMode = value;
        }

        constructor() {
          _initializerDefineProperty(this, "scrollPrefabs", _descriptor3, this);

          _initializerDefineProperty(this, "weight", _descriptor4, this);

          _initializerDefineProperty(this, "splicingMode", _descriptor5, this);

          _initializerDefineProperty(this, "splicingOffsetX", _descriptor6, this);

          _initializerDefineProperty(this, "splicingOffsetY", _descriptor7, this);

          this.splicingOffsetX = new LayerEditorRandomRange(0, 0);
          this.splicingOffsetY = new LayerEditorRandomRange(0, 0);
        }

      }, (_descriptor3 = _applyDecoratedDescriptor(_class5.prototype, "scrollPrefabs", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return [];
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class5.prototype, "weight", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 100;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class5.prototype, "splicingMode", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return (_crd && LayerSplicingMode === void 0 ? (_reportPossibleCrUseOfLayerSplicingMode({
            error: Error()
          }), LayerSplicingMode) : LayerSplicingMode).node_height;
        }
      }), _applyDecoratedDescriptor(_class5.prototype, "splicingModeZh", [_dec8], Object.getOwnPropertyDescriptor(_class5.prototype, "splicingModeZh"), _class5.prototype), _descriptor6 = _applyDecoratedDescriptor(_class5.prototype, "splicingOffsetX", [_dec9], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor7 = _applyDecoratedDescriptor(_class5.prototype, "splicingOffsetY", [_dec10], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class5)) || _class4));

      _export("LevelRandTerrainUI", LevelRandTerrainUI = (_dec11 = ccclass('LevelEditorRandTerrainUI'), _dec12 = property({
        type: CCInteger,
        displayName: "权重"
      }), _dec13 = property({
        type: Prefab,
        displayName: "地形组预制体"
      }), _dec14 = property({
        type: LayerEditorRandomRange,
        displayName: "X偏移范围"
      }), _dec15 = property({
        type: LayerEditorRandomRange,
        displayName: "Y偏移范围"
      }), _dec11(_class7 = (_class8 = class LevelRandTerrainUI {
        constructor() {
          _initializerDefineProperty(this, "weight", _descriptor8, this);

          _initializerDefineProperty(this, "terrainElement", _descriptor9, this);

          // 可以是TerrainElem、DynamicTerrains的预制体
          _initializerDefineProperty(this, "offSetX", _descriptor10, this);

          _initializerDefineProperty(this, "offSetY", _descriptor11, this);

          this.offSetX = new LayerEditorRandomRange(0, 0);
          this.offSetY = new LayerEditorRandomRange(0, 0);
        }

      }, (_descriptor8 = _applyDecoratedDescriptor(_class8.prototype, "weight", [_dec12], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 100;
        }
      }), _descriptor9 = _applyDecoratedDescriptor(_class8.prototype, "terrainElement", [_dec13], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor10 = _applyDecoratedDescriptor(_class8.prototype, "offSetX", [_dec14], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor11 = _applyDecoratedDescriptor(_class8.prototype, "offSetY", [_dec15], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class8)) || _class7));

      _export("LevelRandTerrainsLayerUI", LevelRandTerrainsLayerUI = (_dec16 = ccclass('LevelEditorRandTerrainsLayerUI'), _dec17 = property({
        type: CCInteger,
        displayName: "权重"
      }), _dec18 = property({
        type: [LevelRandTerrainUI],
        displayName: "地形策略"
      }), _dec16(_class10 = (_class11 = class LevelRandTerrainsLayerUI {
        constructor() {
          _initializerDefineProperty(this, "weight", _descriptor12, this);

          _initializerDefineProperty(this, "dynamicTerrain", _descriptor13, this);

          this.dynamicTerrain = [];
        }

      }, (_descriptor12 = _applyDecoratedDescriptor(_class11.prototype, "weight", [_dec17], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 100;
        }
      }), _descriptor13 = _applyDecoratedDescriptor(_class11.prototype, "dynamicTerrain", [_dec18], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return [];
        }
      })), _class11)) || _class10));

      _export("LevelRandTerrainsLayersUI", LevelRandTerrainsLayersUI = (_dec19 = ccclass('LevelEditorRandTerrainsLayersUI'), _dec20 = property({
        type: [LevelRandTerrainsLayerUI],
        displayName: "地形策略组"
      }), _dec19(_class13 = (_class14 = class LevelRandTerrainsLayersUI {
        constructor() {
          _initializerDefineProperty(this, "dynamicTerrains", _descriptor14, this);

          this.dynamicTerrains = [];
        }

      }, (_descriptor14 = _applyDecoratedDescriptor(_class14.prototype, "dynamicTerrains", [_dec20], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return [];
        }
      })), _class14)) || _class13));

      _export("LevelLayer", LevelLayer = (_dec21 = ccclass('LevelEditorLayer'), _dec22 = property({
        displayName: "备注",
        displayOrder: 0
      }), _dec23 = property({
        type: CCInteger,
        displayName: "层级顺序",
        displayOrder: 1
      }), _dec24 = property({
        type: Node,
        displayOrder: 2
      }), _dec25 = property({
        type: CCFloat,
        displayName: "速度",
        displayOrder: 3
      }), _dec26 = property({
        type: Enum(LayerTypeZh),
        displayName: "地形类型",
        displayOrder: 4
      }), _dec27 = property({
        type: [LevelScrollLayerUI],
        displayName: "滚动组",
        visible: function visible() {
          return this.type === (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
            error: Error()
          }), LayerType) : LayerType).Scroll;
        },
        displayOrder: 5
      }), _dec28 = property({
        type: [LevelRandTerrainsLayersUI],
        displayName: "随机组",
        visible: function visible() {
          return this.type === (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
            error: Error()
          }), LayerType) : LayerType).Random;
        },
        displayOrder: 6
      }), _dec29 = property({
        type: [Prefab],
        displayName: "发射器",
        visible: function visible() {
          return this.type === (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
            error: Error()
          }), LayerType) : LayerType).Emittier;
        },
        displayOrder: 7
      }), _dec21(_class16 = (_class17 = class LevelLayer {
        get typeZh() {
          return this.type;
        }

        set typeZh(value) {
          this.type = value;
        }

        constructor() {
          _initializerDefineProperty(this, "remark", _descriptor15, this);

          _initializerDefineProperty(this, "zIndex", _descriptor16, this);

          _initializerDefineProperty(this, "node", _descriptor17, this);

          _initializerDefineProperty(this, "speed", _descriptor18, this);

          this.type = (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
            error: Error()
          }), LayerType) : LayerType).Background;

          _initializerDefineProperty(this, "scrollLayers", _descriptor19, this);

          _initializerDefineProperty(this, "randomLayers", _descriptor20, this);

          _initializerDefineProperty(this, "emittierLayers", _descriptor21, this);

          this.type = (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
            error: Error()
          }), LayerType) : LayerType).Background;
          this.scrollLayers = [];
          this.randomLayers = [];
          this.emittierLayers = [];
        }

      }, (_descriptor15 = _applyDecoratedDescriptor(_class17.prototype, "remark", [_dec22], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return "";
        }
      }), _descriptor16 = _applyDecoratedDescriptor(_class17.prototype, "zIndex", [_dec23], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor17 = _applyDecoratedDescriptor(_class17.prototype, "node", [_dec24], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor18 = _applyDecoratedDescriptor(_class17.prototype, "speed", [_dec25], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 10;
        }
      }), _applyDecoratedDescriptor(_class17.prototype, "typeZh", [_dec26], Object.getOwnPropertyDescriptor(_class17.prototype, "typeZh"), _class17.prototype), _descriptor19 = _applyDecoratedDescriptor(_class17.prototype, "scrollLayers", [_dec27], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return [];
        }
      }), _descriptor20 = _applyDecoratedDescriptor(_class17.prototype, "randomLayers", [_dec28], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return [];
        }
      }), _descriptor21 = _applyDecoratedDescriptor(_class17.prototype, "emittierLayers", [_dec29], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return [];
        }
      })), _class17)) || _class16));

      _export("LevelBackgroundLayer", LevelBackgroundLayer = (_dec30 = ccclass('LevelEditorBackgroundLayer'), _dec31 = property({
        type: [Prefab],
        displayName: '背景组'
      }), _dec30(_class19 = (_class20 = class LevelBackgroundLayer extends LevelLayer {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "backgrounds", _descriptor22, this);

          this.backgroundsNode = null;
        }

      }, (_descriptor22 = _applyDecoratedDescriptor(_class20.prototype, "backgrounds", [_dec31], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return [];
        }
      })), _class20)) || _class19));

      _export("LevelEditorUtils", LevelEditorUtils = class LevelEditorUtils {
        static getOrAddNode(node_parent, name) {
          var node = node_parent.getChildByName(name);

          if (node == null) {
            node = new Node(name);
            node_parent.addChild(node);
          }

          return node;
        }

        static getOrAddComp(node, classConstructor) {
          var comp = node.getComponent(classConstructor);

          if (comp == null) {
            comp = node.addComponent(classConstructor);
          }

          return comp;
        } // 仅在编辑器中使用


        static loadByPath(path) {
          return _asyncToGenerator(function* () {
            if (path == null || path == "") {
              return Promise.resolve(null);
            } // @ts-ignore


            var uuid = yield Editor.Message.request('asset-db', 'query-uuid', path);

            if (!uuid) {
              return Promise.resolve(null);
            }

            return new Promise(resolve => {
              assetManager.loadAny(uuid, (err, asset) => {
                if (err) {
                  console.warn("Failed to load by path: " + path, err);
                  resolve(null);
                  return;
                }

                resolve(asset);
              });
            });
          })();
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=7c8164ef85ecc97742f9c4c1a1cb99eb36d4908a.js.map