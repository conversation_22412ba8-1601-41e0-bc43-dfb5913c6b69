{"version": 3, "sources": ["cce:/internal/x/prerequisite-imports"], "names": ["requests", "request", "_err"], "mappings": ";;;;;;AACA;AAEA,YAAM,CAAC,YAAY;AACf,cAAMA,QAAQ,GAAG,CAAC,uCAAD,EAA4J,uCAA5J,EAA4T,uCAA5T,EAAge,uCAAhe,EAAioB,uCAAjoB,EAA4xB,uCAA5xB,EAAg7B,uCAAh7B,EAAsgC,uCAAtgC,EAA0lC,uCAA1lC,EAAurC,uCAAvrC,EAAsxC,wCAAtxC,EAAo3C,wCAAp3C,EAAm9C,wCAAn9C,EAA+iD,wCAA/iD,EAA0oD,wCAA1oD,EAAiuD,wCAAjuD,EAAm0D,wCAAn0D,EAAg6D,wCAAh6D,EAAy/D,wCAAz/D,EAAolE,wCAAplE,EAAsrE,wCAAtrE,EAAsxE,wCAAtxE,EAAi3E,wCAAj3E,EAA88E,wCAA98E,EAAkjF,wCAAljF,EAAspF,wCAAtpF,EAAwvF,wCAAxvF,EAA60F,wCAA70F,EAAs6F,wCAAt6F,EAA2/F,wCAA3/F,EAA+lG,wCAA/lG,EAA8rG,wCAA9rG,EAAuxG,wCAAvxG,EAAi3G,wCAAj3G,EAA88G,wCAA98G,EAA0iH,wCAA1iH,EAAuoH,wCAAvoH,EAA8tH,wCAA9tH,EAA0zH,wCAA1zH,EAAu5H,wCAAv5H,EAAsgI,wCAAtgI,EAAymI,wCAAzmI,EAAusI,wCAAvsI,EAAwyI,wCAAxyI,EAAy4I,wCAAz4I,EAAi/I,wCAAj/I,EAAkmJ,wCAAlmJ,EAAotJ,wCAAptJ,EAA20J,wCAA30J,EAAm8J,wCAAn8J,EAA+iK,wCAA/iK,EAA8pK,wCAA9pK,EAAuwK,wCAAvwK,EAAu3K,wCAAv3K,EAAu+K,wCAAv+K,EAAmlL,wCAAnlL,EAA2rL,wCAA3rL,EAA0xL,wCAA1xL,EAAw3L,wCAAx3L,EAA89L,wCAA99L,EAA2jM,wCAA3jM,EAA+pM,wCAA/pM,EAA6vM,wCAA7vM,EAA01M,wCAA11M,EAAu7M,wCAAv7M,EAA6hN,wCAA7hN,EAAooN,wCAApoN,EAA+uN,wCAA/uN,EAA61N,wCAA71N,EAAu8N,wCAAv8N,EAAkjO,wCAAljO,EAA6pO,wCAA7pO,EAAowO,wCAApwO,EAAm2O,wCAAn2O,EAAo8O,wCAAp8O,EAA2iP,wCAA3iP,EAAqpP,wCAArpP,EAA2vP,wCAA3vP,EAAw2P,wCAAx2P,EAAu8P,wCAAv8P,EAA2iQ,wCAA3iQ,EAAgpQ,wCAAhpQ,EAAmvQ,wCAAnvQ,EAAu1Q,wCAAv1Q,EAA87Q,wCAA97Q,EAAsiR,wCAAtiR,EAA8oR,wCAA9oR,EAAuvR,wCAAvvR,EAAg2R,wCAAh2R,EAAw8R,wCAAx8R,EAA2iS,wCAA3iS,EAA0oS,wCAA1oS,EAA0uS,wCAA1uS,EAAu0S,wCAAv0S,EAAo6S,wCAAp6S,EAAmgT,wCAAngT,EAAomT,wCAApmT,EAAssT,wCAAtsT,EAAyyT,wCAAzyT,EAAy4T,wCAAz4T,EAA2+T,yCAA3+T,EAAykU,yCAAzkU,EAA8qU,yCAA9qU,EAAkxU,yCAAlxU,EAAs3U,yCAAt3U,EAA+9U,yCAA/9U,EAAgkV,yCAAhkV,EAAiqV,yCAAjqV,EAAmwV,yCAAnwV,EAAw2V,yCAAx2V,EAA08V,yCAA18V,EAA8iW,yCAA9iW,EAAipW,yCAAjpW,EAAovW,yCAApvW,EAAs1W,yCAAt1W,EAAw7W,yCAAx7W,EAA+hX,yCAA/hX,EAAsoX,yCAAtoX,EAA+uX,yCAA/uX,EAA41X,yCAA51X,EAA88X,yCAA98X,EAA4jY,yCAA5jY,EAAwqY,yCAAxqY,EAAqxY,yCAArxY,EAAk4Y,yCAAl4Y,EAA8+Y,yCAA9+Y,EAA+lZ,yCAA/lZ,EAA+sZ,yCAA/sZ,EAAkzZ,yCAAlzZ,EAAy5Z,yCAAz5Z,EAAmga,yCAAnga,EAA8ma,yCAA9ma,EAAsta,yCAAtta,EAA4za,yCAA5za,EAAw5a,yCAAx5a,EAAi/a,yCAAj/a,EAA2kb,yCAA3kb,EAAsqb,yCAAtqb,EAAmwb,yCAAnwb,EAA41b,yCAA51b,EAAi8b,yCAAj8b,EAAyic,yCAAzic,EAA4oc,yCAA5oc,EAAiwc,yCAAjwc,EAAm4c,yCAAn4c,EAAigd,yCAAjgd,EAA0nd,yCAA1nd,EAAqud,yCAArud,EAAm0d,yCAAn0d,EAAq7d,yCAAr7d,EAA4ie,yCAA5ie,EAAiqe,yCAAjqe,EAA+xe,yCAA/xe,EAAq5e,yCAAr5e,EAA4/e,yCAA5/e,EAAqlf,yCAArlf,EAA8qf,yCAA9qf,EAA2wf,yCAA3wf,EAAu2f,yCAAv2f,EAAo8f,yCAAp8f,EAAmigB,yCAAnigB,EAAsogB,yCAAtogB,EAAyugB,yCAAzugB,EAAu0gB,yCAAv0gB,EAA05gB,yCAA15gB,EAA6/gB,yCAA7/gB,EAA2lhB,yCAA3lhB,EAAurhB,yCAAvrhB,EAAoxhB,yCAApxhB,EAA23hB,yCAA33hB,EAA49hB,yCAA59hB,EAAmkiB,yCAAnkiB,EAA2qiB,yCAA3qiB,EAA4wiB,yCAA5wiB,EAAu2iB,yCAAv2iB,EAAm8iB,yCAAn8iB,EAAuijB,yCAAvijB,EAA0pjB,yCAA1pjB,EAAixjB,yCAAjxjB,EAAi4jB,yCAAj4jB,EAAi/jB,yCAAj/jB,EAAkmkB,yCAAlmkB,EAAmtkB,yCAAntkB,EAAo0kB,yCAAp0kB,EAA66kB,yCAA76kB,EAA0hlB,yCAA1hlB,EAA0nlB,yCAA1nlB,EAA2tlB,yCAA3tlB,EAA4zlB,yCAA5zlB,EAAi6lB,yCAAj6lB,EAA8/lB,yCAA9/lB,EAA6lmB,yCAA7lmB,EAA2rmB,yCAA3rmB,EAA2xmB,yCAA3xmB,EAAg4mB,yCAAh4mB,EAAs+mB,yCAAt+mB,EAAuknB,yCAAvknB,EAAwqnB,yCAAxqnB,EAAownB,yCAApwnB,EAA+1nB,yCAA/1nB,EAAw7nB,yCAAx7nB,EAAshoB,yCAAthoB,EAA8moB,yCAA9moB,EAA+soB,yCAA/soB,EAAqzoB,yCAArzoB,EAA65oB,yCAA75oB,EAA6/oB,yCAA7/oB,EAA0lpB,yCAA1lpB,EAAmrpB,yCAAnrpB,EAA4wpB,yCAA5wpB,EAA42pB,yCAA52pB,EAAw8pB,yCAAx8pB,EAAqiqB,yCAAriqB,EAA0nqB,yCAA1nqB,EAAkuqB,yCAAluqB,EAAs0qB,yCAAt0qB,EAAo6qB,yCAAp6qB,EAA+/qB,yCAA//qB,EAA+mrB,yCAA/mrB,EAA+trB,yCAA/trB,EAAw1rB,yCAAx1rB,EAAq8rB,yCAAr8rB,EAA0jsB,yCAA1jsB,EAA6qsB,yCAA7qsB,EAAswsB,yCAAtwsB,EAAy2sB,yCAAz2sB,EAAs8sB,yCAAt8sB,EAAwitB,yCAAxitB,EAAmotB,yCAAnotB,EAAgutB,yCAAhutB,EAA4ztB,yCAA5ztB,EAAq5tB,yCAAr5tB,EAAgguB,yCAAhguB,EAAsmuB,yCAAtmuB,EAA+ruB,yCAA/ruB,EAA6wuB,yCAA7wuB,EAAi2uB,yCAAj2uB,EAA+6uB,yCAA/6uB,EAAggvB,yCAAhgvB,EAAglvB,yCAAhlvB,EAA8pvB,yCAA9pvB,EAA6uvB,yCAA7uvB,EAA2zvB,yCAA3zvB,EAA04vB,yCAA14vB,EAAu9vB,yCAAv9vB,EAAyiwB,yCAAziwB,EAA4nwB,yCAA5nwB,EAAktwB,yCAAltwB,EAAqywB,yCAArywB,EAAy3wB,yCAAz3wB,EAA68wB,yCAA78wB,EAA4hxB,yCAA5hxB,EAAknxB,yCAAlnxB,EAAusxB,yCAAvsxB,EAA8wxB,yCAA9wxB,EAAo2xB,yCAAp2xB,EAA+7xB,yCAA/7xB,EAAghyB,yCAAhhyB,EAAsmyB,yCAAtmyB,EAAqryB,yCAArryB,EAA2vyB,yCAA3vyB,EAAi0yB,yCAAj0yB,EAA+4yB,yCAA/4yB,EAA49yB,yCAA59yB,EAAuizB,yCAAvizB,EAAwnzB,yCAAxnzB,EAAyszB,yCAAzszB,EAA6xzB,yCAA7xzB,EAAy2zB,yCAAz2zB,EAAy7zB,yCAAz7zB,EAA2g0B,yCAA3g0B,EAAol0B,yCAApl0B,EAAqq0B,yCAArq0B,EAAwv0B,yCAAxv0B,CAAjB;;AACA,aAAK,MAAMC,OAAX,IAAsBD,QAAtB,EAAgC;AAC5B,cAAI;AACA,kBAAMC,OAAO,EAAb;AACH,WAFD,CAEE,OAAOC,IAAP,EAAa,CACX;AACH;AACJ;AACJ,OATK,GAAN", "sourcesContent": ["\n// Auto generated represents the prerequisite imports of project modules.\n\nawait (async () => {\n    const requests = [() => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/audio/audioManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/autogen/luban/schema.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/const/AttributeConst.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/const/BundleConst.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/DataManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/bag/Bag.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/base/AttributeData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/base/BaseInfo.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/base/Role.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/equip/Equip.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/equip/EquipCombine.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/equip/EquipSlots.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/fight/Rogue.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/friend/Friend.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/game_level/GameLevel.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/game_logic/GameLogic.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/game_mode/GameMode.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/gm/GM.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/mail/Mail.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/pk/PK.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/plane/PlaneCacheInfo.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/plane/PlaneData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/task/Task.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/event/DataEvent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/event/EventManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/event/HomeUIEvent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/event/PlaneUIEvent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/GameIns.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/GameInsStart.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/Bullet.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/BulletPerformanceMonitor.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/BulletSystem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/Emitter.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/EventGroup.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/ObjectPool.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/PropertyContainer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/actions/BulletEventActions.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/actions/EmitterEventActions.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/conditions/BulletEventConditions.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/conditions/EmitterEventConditions.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/FBoxCollider.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/FCircleCollider.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/FCollider.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/FColliderManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/FPolygonCollider.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/Intersection.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/QuadTree.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/const/GameConst.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/const/GameEnum.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/const/GameResourceList.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/data/BossData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/data/BulletEventData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/data/EnemyData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/data/PathData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/data/WaveData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/data/bullet/BulletData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/data/bullet/EmitterData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/data/bullet/EventActionType.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/data/bullet/EventConditionType.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/data/bullet/EventGroupData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/data/bullet/ExpressionValue.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/dyncTerrain/EmittierTerrain.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/dyncTerrain/RandTerrain.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/event/GameEvent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/eventgroup/Easing.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/eventgroup/IEventAction.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/eventgroup/IEventCondition.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/eventgroup/IEventGroup.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/eventgroup/IEventGroupContext.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/level/LevelItem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/level/LevelItemEvent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/BattleManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/BossManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/EnemyManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/GameDataManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/GamePlaneManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/GameStateManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/GlobalDataManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/HurtEffectManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/MainPlaneManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/WaveManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/move/CameraMove.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/move/DefaultMove.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/move/IMovable.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/move/PathMove.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/scenes/GameMain.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/test/ColliderTest.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/test/PathCurveTest.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/test/PathSystemTest.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/base/BaseComp.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/base/Controller.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/base/Entity.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/base/UIAnimMethods.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/layer/BattleLayer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/layer/EffectLayer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/layer/EnemyEffectLayer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/layer/GameInUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/GameMapRun.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelBaseUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelCondition.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelElemUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelEventRun.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelEventUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelLayerUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelWaveUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/PlaneBase.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/PlaneBaseDebug.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/boss/BossPlane.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/enemy/EnemyPlane.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/enemy/EnemyPlaneBase.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/enemy/EnemyPlaneBaseDebug.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/enemy/EnemyPlaneDebug.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/event/EventGroupCom.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/event/PlaneEventComp.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/event/PlaneEventType.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/mainPlane/MainPlane.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/mainPlane/MainPlaneDebug.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/mainPlane/MainPlaneStat.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/skill/Buff.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/skill/BuffComp.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/skill/ExCondition.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/skill/SearchTarget.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/skill/SkillComp.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/weapon/Weapon.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/utils/Helper.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/utils/RPN.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/utils/Rand.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/utils/Tools.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/utils/UITools.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/wave/Wave.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/wave/WaveEventActions.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/wave/WaveEventConditions.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/wave/WaveEventGroup.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/condition/LevelDataEventCondtion.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/condition/LevelDataEventCondtionDelayDistance.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/condition/LevelDataEventCondtionDelayTime.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/condition/LevelDataEventCondtionWave.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/condition/newCondition.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/leveldata.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/trigger/LevelDataEventTrigger.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerAudio.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerLog.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerSpecialEvent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerWave.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/trigger/newTrigger.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/luban/LubanMgr.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/network/NetMgr.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/plane/PlaneManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/plane/StateDefine.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/plane/StateMachine.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/DevLogin.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/DevLoginData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/IPlatformSDK.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/WXLogin.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/Plane.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/AnnouncementUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/MarqueeUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/PopupUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/RewardUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/SettlementResultUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/SettlementUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/StatisticsHurtCell.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/StatisticsScoreCell.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/StatisticsUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/TextUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/ToastUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/TopBlockInputUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/SelectList/uiSelect.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/SelectList/uiSelectItem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/base/ItemQuaIcon.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/base/StateSprite.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/button/ButtonPlus.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/button/DragButton.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/dropdown/DropDown.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/list/List.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/list/ListItem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendAddUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendCellUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendListUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendStrangerUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/DevLoginUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/LoadingUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/RatioScaler.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/game/GamePauseUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/game/GameReviveUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/game/MBoomUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/res/PlaneRes.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/DialogueUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/RogueSelectIcon.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/RogueUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/mail/MailCellUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/mail/MailUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/main/MainUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/PKHistoryCellUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/PKHistoryUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/PKRewardIcon.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/PKUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneCombineResultUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneEquipInfoUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneTypes.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/back_pack/BagGrid.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/back_pack/BagItem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/back_pack/SortTypeDropdown.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/back_pack/Tabs.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/display/CombineDisplay.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/display/EquipDisplay.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/shop/ShopUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/skyisland/SkyIslandUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/story/BuidingUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/story/BuildingInfoUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/story/StoryUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/talent/TalentUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/task/TaskTipUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/task/TaskUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/task/components/ProgressPanel.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/task/components/TaskItem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/utils/TTFUtils.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/gm/script/GmEntry.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/gm/script/ui/GmButtonUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/gm/script/ui/GmUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/emitter/EmitterEditor.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/enum-gen/EmitterEnum.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/enum-gen/EnemyEnum.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/gizmos/EmitterGizmo.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/gizmos/GizmoDrawer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/gizmos/GizmoManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/gizmos/GizmoUtils.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/gizmos/LevelEventGizmo.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelEditorBaseUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelEditorCondition.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelEditorElemUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelEditorEventUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelEditorLayerUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelEditorUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelEditorWaveParam.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/preview/WavePreview.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/utils.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/wave/FormationEditor.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/wave/FormationPointEditor.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/wave/PathEditor.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/wave/PathPointEditor.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/planeview/PlaneView.ts\"), () => import(\"file:///E:/M2Game/Client/assets/resources/i18n/en.ts\"), () => import(\"file:///E:/M2Game/Client/assets/resources/i18n/zh.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/AAA/init_cs_proto.js\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/core/base/Bundle.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/core/base/IMgr.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/core/base/MessageBox.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/core/base/ResManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/core/base/SingletonBase.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/core/base/UIMgr.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/resupdate/ResUpdate.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/resupdate/RootPersist.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/utils/Logger.ts\"), () => import(\"file:///E:/M2Game/Client/extensions/i18n/assets/LanguageData.ts\"), () => import(\"file:///E:/M2Game/Client/extensions/i18n/assets/LocalizedLabel.ts\"), () => import(\"file:///E:/M2Game/Client/extensions/i18n/assets/LocalizedSprite.ts\")];\n    for (const request of requests) {\n        try {\n            await request();\n        } catch (_err) {\n            // The error should have been caught by executor.\n        }\n    }\n})();\n    "]}