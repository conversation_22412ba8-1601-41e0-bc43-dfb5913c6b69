import { LevelDataEventCondtion, LevelDataEventCondtionComb, LevelDataEventCondtionType } from "./LevelDataEventCondtion";

export enum LevelDataEventConditionWaveType {
    WaveSpawned = 0,   // Wave已完成(敌机出生完)
    WaveCleared = 1,     // Wave产生的敌机已清完
    WaveTriggered = 2,   // Wave已触发
}

export class LevelDataEventCondtionWave extends LevelDataEventCondtion {
    // 这里的elemID对应上一个LevelEvent
    public targetElemID = "";
    public waveCondType: LevelDataEventConditionWaveType = LevelDataEventConditionWaveType.WaveCleared;

    constructor(comb: LevelDataEventCondtionComb) {
        super(comb, LevelDataEventCondtionType.Wave);
    }
}