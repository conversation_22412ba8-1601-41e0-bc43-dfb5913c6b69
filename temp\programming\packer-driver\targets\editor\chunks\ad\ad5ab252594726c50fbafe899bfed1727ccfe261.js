System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, view, _GameConst, _crd, GameConst;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      view = _cc.view;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "86dea8H4flPMosr9fpMflgS", "GameConst", undefined);

      __checkObsolete__(['view']);

      _GameConst = class _GameConst {
        constructor() {
          this.ColliderDraw = false;
          this.ActionFrameTime = 0.0333;
          this.designWidth = 750;
          // 设计分辨率
          this.designHeight = 1334;
          // 设计分辨率
          this.offsetWidth = 200;
        }

        // 宽屏的时候，宽度最高多显示200像素
        get ViewHeight() {
          return view.getVisibleSize().height;
        }

        get ViewWidth() {
          return view.getVisibleSize().width;
        }

        get ViewBattleWidth() {
          let width = view.getVisibleSize().width;
          return width + this.offsetWidth;
        }

      };

      _export("GameConst", GameConst = new _GameConst());

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=ad5ab252594726c50fbafe899bfed1727ccfe261.js.map