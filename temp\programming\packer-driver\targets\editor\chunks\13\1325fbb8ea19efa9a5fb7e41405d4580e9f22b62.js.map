{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/test/PathSystemTest.ts"], "names": ["_decorator", "Component", "log", "PathData", "PathPoint", "ccclass", "property", "PathSystemTest", "onLoad", "testRecursiveDepth", "testCurveGeneration", "testSubdividedPoints", "testAdaptiveSubdivision", "testSpeedSampling", "pathData", "point1", "speed", "smoothness", "point2", "point3", "point4", "points", "length", "push", "subdivided", "getSubdividedPoints", "hasSmoothing", "i", "prev", "curr", "next", "linearX", "x", "linearY", "y", "deviation", "Math", "sqrt", "pow", "start", "mid", "floor", "end", "toFixed", "linearMidX", "linearMidY", "min", "point", "pathData1", "point1a", "point1b", "subdivided1", "pathData2", "point2a", "point2b", "point2c", "subdivided2", "pathData3", "point3a", "point3b", "point3c", "subdivided3", "pathData4", "point4a", "point4b", "point4c", "subdivided4", "checkIndices", "index", "progress", "speedJumps", "speedDiff", "abs"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,G,OAAAA,G;;AACvBC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,S,iBAAAA,S;;;;;;;;;OAEb;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBN,U;AAE9B;AACA;AACA;;gCAEaO,c,WADZF,OAAO,CAAC,gBAAD,C,gBAAR,MACaE,cADb,SACoCN,SADpC,CAC8C;AAE1CO,QAAAA,MAAM,GAAG;AACL,eAAKC,kBAAL;AACA,eAAKC,mBAAL;AACA,eAAKC,oBAAL;AACA,eAAKC,uBAAL;AACA,eAAKC,iBAAL;AACH;AAED;AACJ;AACA;;;AACYJ,QAAAA,kBAAkB,GAAG;AACzBP,UAAAA,GAAG,CAAC,kBAAD,CAAH;AAEA,gBAAMY,QAAQ,GAAG;AAAA;AAAA,qCAAjB,CAHyB,CAKzB;;AACA,gBAAMC,MAAM,GAAG;AAAA;AAAA,sCAAc,CAAd,EAAiB,CAAjB,CAAf;AACAA,UAAAA,MAAM,CAACC,KAAP,GAAe,GAAf;AACAD,UAAAA,MAAM,CAACE,UAAP,GAAoB,GAApB,CARyB,CAQA;;AAEzB,gBAAMC,MAAM,GAAG;AAAA;AAAA,sCAAc,EAAd,EAAkB,CAAlB,CAAf;AACAA,UAAAA,MAAM,CAACF,KAAP,GAAe,GAAf;AACAE,UAAAA,MAAM,CAACD,UAAP,GAAoB,GAApB;AAEA,gBAAME,MAAM,GAAG;AAAA;AAAA,sCAAc,EAAd,EAAkB,EAAlB,CAAf;AACAA,UAAAA,MAAM,CAACH,KAAP,GAAe,GAAf;AACAG,UAAAA,MAAM,CAACF,UAAP,GAAoB,GAApB;AAEA,gBAAMG,MAAM,GAAG;AAAA;AAAA,sCAAc,CAAd,EAAiB,EAAjB,CAAf;AACAA,UAAAA,MAAM,CAACJ,KAAP,GAAe,GAAf;AACAI,UAAAA,MAAM,CAACH,UAAP,GAAoB,GAApB;AAEAH,UAAAA,QAAQ,CAACO,MAAT,CAAgBC,MAAhB,GAAyB,CAAzB;AACAR,UAAAA,QAAQ,CAACO,MAAT,CAAgBE,IAAhB,CAAqBR,MAArB,EAA6BG,MAA7B,EAAqCC,MAArC,EAA6CC,MAA7C;AAEA,gBAAMI,UAAU,GAAGV,QAAQ,CAACW,mBAAT,EAAnB;AACAvB,UAAAA,GAAG,CAAE,eAAcsB,UAAU,CAACF,MAAO,EAAlC,CAAH;;AAEA,cAAIE,UAAU,CAACF,MAAX,GAAoB,CAAxB,EAA2B;AACvBpB,YAAAA,GAAG,CAAC,sBAAD,CAAH,CADuB,CAGvB;;AACA,gBAAIwB,YAAY,GAAG,KAAnB;;AACA,iBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,UAAU,CAACF,MAAX,GAAoB,CAAxC,EAA2CK,CAAC,EAA5C,EAAgD;AAC5C,oBAAMC,IAAI,GAAGJ,UAAU,CAACG,CAAC,GAAG,CAAL,CAAvB;AACA,oBAAME,IAAI,GAAGL,UAAU,CAACG,CAAD,CAAvB;AACA,oBAAMG,IAAI,GAAGN,UAAU,CAACG,CAAC,GAAG,CAAL,CAAvB,CAH4C,CAK5C;;AACA,oBAAMI,OAAO,GAAGH,IAAI,CAACI,CAAL,GAAS,CAACF,IAAI,CAACE,CAAL,GAASJ,IAAI,CAACI,CAAf,IAAoB,GAA7C;AACA,oBAAMC,OAAO,GAAGL,IAAI,CAACM,CAAL,GAAS,CAACJ,IAAI,CAACI,CAAL,GAASN,IAAI,CAACM,CAAf,IAAoB,GAA7C;AACA,oBAAMC,SAAS,GAAGC,IAAI,CAACC,IAAL,CAAUD,IAAI,CAACE,GAAL,CAAST,IAAI,CAACG,CAAL,GAASD,OAAlB,EAA2B,CAA3B,IAAgCK,IAAI,CAACE,GAAL,CAAST,IAAI,CAACK,CAAL,GAASD,OAAlB,EAA2B,CAA3B,CAA1C,CAAlB;;AAEA,kBAAIE,SAAS,GAAG,GAAhB,EAAqB;AACjBT,gBAAAA,YAAY,GAAG,IAAf;AACA;AACH;AACJ;;AAED,gBAAIA,YAAJ,EAAkB;AACdxB,cAAAA,GAAG,CAAC,aAAD,CAAH;AACH,aAFD,MAEO;AACHA,cAAAA,GAAG,CAAC,eAAD,CAAH;AACH;AACJ,WA1BD,MA0BO;AACHA,YAAAA,GAAG,CAAC,sBAAD,CAAH;AACH;AACJ;AAED;AACJ;AACA;;;AACYQ,QAAAA,mBAAmB,GAAG;AAC1BR,UAAAA,GAAG,CAAC,gBAAD,CAAH;AAEA,gBAAMY,QAAQ,GAAG;AAAA;AAAA,qCAAjB,CAH0B,CAK1B;;AACA,gBAAMC,MAAM,GAAG;AAAA;AAAA,sCAAc,CAAd,EAAiB,CAAjB,CAAf;AACAA,UAAAA,MAAM,CAACC,KAAP,GAAe,GAAf;AACAD,UAAAA,MAAM,CAACE,UAAP,GAAoB,GAApB,CAR0B,CAQD;;AAEzB,gBAAMC,MAAM,GAAG;AAAA;AAAA,sCAAc,GAAd,EAAmB,CAAnB,CAAf;AACAA,UAAAA,MAAM,CAACF,KAAP,GAAe,GAAf;AACAE,UAAAA,MAAM,CAACD,UAAP,GAAoB,GAApB;AAEA,gBAAME,MAAM,GAAG;AAAA;AAAA,sCAAc,GAAd,EAAmB,GAAnB,CAAf;AACAA,UAAAA,MAAM,CAACH,KAAP,GAAe,GAAf;AACAG,UAAAA,MAAM,CAACF,UAAP,GAAoB,GAApB;AAEAH,UAAAA,QAAQ,CAACO,MAAT,CAAgBC,MAAhB,GAAyB,CAAzB;AACAR,UAAAA,QAAQ,CAACO,MAAT,CAAgBE,IAAhB,CAAqBR,MAArB,EAA6BG,MAA7B,EAAqCC,MAArC;AAEA,gBAAMK,UAAU,GAAGV,QAAQ,CAACW,mBAAT,EAAnB;AACAvB,UAAAA,GAAG,CAAE,YAAWsB,UAAU,CAACF,MAAO,EAA/B,CAAH,CAtB0B,CAwB1B;;AACA,cAAIE,UAAU,CAACF,MAAX,IAAqB,CAAzB,EAA4B;AACxB,kBAAMiB,KAAK,GAAGf,UAAU,CAAC,CAAD,CAAxB;AACA,kBAAMgB,GAAG,GAAGhB,UAAU,CAACY,IAAI,CAACK,KAAL,CAAWjB,UAAU,CAACF,MAAX,GAAoB,CAA/B,CAAD,CAAtB;AACA,kBAAMoB,GAAG,GAAGlB,UAAU,CAACA,UAAU,CAACF,MAAX,GAAoB,CAArB,CAAtB;AAEApB,YAAAA,GAAG,CAAE,QAAOqC,KAAK,CAACP,CAAN,CAAQW,OAAR,CAAgB,CAAhB,CAAmB,KAAIJ,KAAK,CAACL,CAAN,CAAQS,OAAR,CAAgB,CAAhB,CAAmB,GAAnD,CAAH;AACAzC,YAAAA,GAAG,CAAE,QAAOsC,GAAG,CAACR,CAAJ,CAAMW,OAAN,CAAc,CAAd,CAAiB,KAAIH,GAAG,CAACN,CAAJ,CAAMS,OAAN,CAAc,CAAd,CAAiB,GAA/C,CAAH;AACAzC,YAAAA,GAAG,CAAE,QAAOwC,GAAG,CAACV,CAAJ,CAAMW,OAAN,CAAc,CAAd,CAAiB,KAAID,GAAG,CAACR,CAAJ,CAAMS,OAAN,CAAc,CAAd,CAAiB,GAA/C,CAAH,CAPwB,CASxB;;AACA,kBAAMC,UAAU,GAAG,CAACL,KAAK,CAACP,CAAN,GAAUU,GAAG,CAACV,CAAf,IAAoB,CAAvC;AACA,kBAAMa,UAAU,GAAG,CAACN,KAAK,CAACL,CAAN,GAAUQ,GAAG,CAACR,CAAf,IAAoB,CAAvC;AACA,kBAAMC,SAAS,GAAGC,IAAI,CAACC,IAAL,CAAUD,IAAI,CAACE,GAAL,CAASE,GAAG,CAACR,CAAJ,GAAQY,UAAjB,EAA6B,CAA7B,IAAkCR,IAAI,CAACE,GAAL,CAASE,GAAG,CAACN,CAAJ,GAAQW,UAAjB,EAA6B,CAA7B,CAA5C,CAAlB;AAEA3C,YAAAA,GAAG,CAAE,aAAYiC,SAAS,CAACQ,OAAV,CAAkB,CAAlB,CAAqB,EAAnC,CAAH;;AAEA,gBAAIR,SAAS,GAAG,CAAhB,EAAmB;AACfjC,cAAAA,GAAG,CAAC,UAAD,CAAH;AACH,aAFD,MAEO;AACHA,cAAAA,GAAG,CAAC,aAAD,CAAH;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACYS,QAAAA,oBAAoB,GAAG;AAC3BT,UAAAA,GAAG,CAAC,iCAAD,CAAH;AAEA,gBAAMY,QAAQ,GAAG;AAAA;AAAA,qCAAjB,CAH2B,CAK3B;;AACA,gBAAMC,MAAM,GAAG;AAAA;AAAA,sCAAc,CAAd,EAAiB,CAAjB,CAAf;AACAA,UAAAA,MAAM,CAACC,KAAP,GAAe,GAAf;AACAD,UAAAA,MAAM,CAACE,UAAP,GAAoB,GAApB;AAEA,gBAAMC,MAAM,GAAG;AAAA;AAAA,sCAAc,GAAd,EAAmB,CAAnB,CAAf;AACAA,UAAAA,MAAM,CAACF,KAAP,GAAe,GAAf;AACAE,UAAAA,MAAM,CAACD,UAAP,GAAoB,GAApB;AAEA,gBAAME,MAAM,GAAG;AAAA;AAAA,sCAAc,GAAd,EAAmB,GAAnB,CAAf;AACAA,UAAAA,MAAM,CAACH,KAAP,GAAe,GAAf;AACAG,UAAAA,MAAM,CAACF,UAAP,GAAoB,GAApB;AAEAH,UAAAA,QAAQ,CAACO,MAAT,CAAgBC,MAAhB,GAAyB,CAAzB;AACAR,UAAAA,QAAQ,CAACO,MAAT,CAAgBE,IAAhB,CAAqBR,MAArB,EAA6BG,MAA7B,EAAqCC,MAArC,EAnB2B,CAqB3B;;AACA,gBAAMK,UAAU,GAAGV,QAAQ,CAACW,mBAAT,EAAnB;AACAvB,UAAAA,GAAG,CAAE,YAAWY,QAAQ,CAACO,MAAT,CAAgBC,MAAO,EAApC,CAAH;AACApB,UAAAA,GAAG,CAAE,WAAUsB,UAAU,CAACF,MAAO,EAA9B,CAAH,CAxB2B,CA0B3B;;AACA,eAAK,IAAIK,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGS,IAAI,CAACU,GAAL,CAAS,CAAT,EAAYtB,UAAU,CAACF,MAAvB,CAApB,EAAoDK,CAAC,EAArD,EAAyD;AACrD,kBAAMoB,KAAK,GAAGvB,UAAU,CAACG,CAAD,CAAxB;AACAzB,YAAAA,GAAG,CAAE,OAAMyB,CAAE,SAAQoB,KAAK,CAACf,CAAN,CAAQW,OAAR,CAAgB,CAAhB,CAAmB,KAAII,KAAK,CAACb,CAAN,CAAQS,OAAR,CAAgB,CAAhB,CAAmB,SAAQI,KAAK,CAAC/B,KAAN,CAAY2B,OAAZ,CAAoB,CAApB,CAAuB,SAAQI,KAAK,CAAC9B,UAAN,CAAiB0B,OAAjB,CAAyB,CAAzB,CAA4B,EAA/H,CAAH;AACH;AACJ;AAED;AACJ;AACA;;;AACY/B,QAAAA,uBAAuB,GAAG;AAC9BV,UAAAA,GAAG,CAAC,qBAAD,CAAH,CAD8B,CAG9B;;AACAA,UAAAA,GAAG,CAAC,sBAAD,CAAH;AACA,gBAAM8C,SAAS,GAAG;AAAA;AAAA,qCAAlB;AACA,gBAAMC,OAAO,GAAG;AAAA;AAAA,sCAAc,CAAd,EAAiB,CAAjB,CAAhB;AACAA,UAAAA,OAAO,CAAChC,UAAR,GAAqB,GAArB,CAP8B,CAOJ;;AAC1B,gBAAMiC,OAAO,GAAG;AAAA;AAAA,sCAAc,GAAd,EAAmB,CAAnB,CAAhB;AACAA,UAAAA,OAAO,CAACjC,UAAR,GAAqB,GAArB,CAT8B,CASJ;;AAC1B+B,UAAAA,SAAS,CAAC3B,MAAV,CAAiBC,MAAjB,GAA0B,CAA1B;AACA0B,UAAAA,SAAS,CAAC3B,MAAV,CAAiBE,IAAjB,CAAsB0B,OAAtB,EAA+BC,OAA/B;AAEA,gBAAMC,WAAW,GAAGH,SAAS,CAACvB,mBAAV,EAApB;AACAvB,UAAAA,GAAG,CAAE,kBAAiBiD,WAAW,CAAC7B,MAAO,SAAQ,CAAC6B,WAAW,CAAC7B,MAAZ,GAAqB,CAAtB,EAAyBqB,OAAzB,CAAiC,CAAjC,CAAoC,EAAlF,CAAH,CAd8B,CAgB9B;;AACAzC,UAAAA,GAAG,CAAC,qBAAD,CAAH;AACA,gBAAMkD,SAAS,GAAG;AAAA;AAAA,qCAAlB;AACA,gBAAMC,OAAO,GAAG;AAAA;AAAA,sCAAc,CAAd,EAAiB,CAAjB,CAAhB;AACAA,UAAAA,OAAO,CAACpC,UAAR,GAAqB,GAArB;AACA,gBAAMqC,OAAO,GAAG;AAAA;AAAA,sCAAc,GAAd,EAAmB,CAAnB,CAAhB;AACAA,UAAAA,OAAO,CAACrC,UAAR,GAAqB,GAArB;AACA,gBAAMsC,OAAO,GAAG;AAAA;AAAA,sCAAc,GAAd,EAAmB,GAAnB,CAAhB;AACAA,UAAAA,OAAO,CAACtC,UAAR,GAAqB,GAArB;AACAmC,UAAAA,SAAS,CAAC/B,MAAV,CAAiBC,MAAjB,GAA0B,CAA1B;AACA8B,UAAAA,SAAS,CAAC/B,MAAV,CAAiBE,IAAjB,CAAsB8B,OAAtB,EAA+BC,OAA/B,EAAwCC,OAAxC;AAEA,gBAAMC,WAAW,GAAGJ,SAAS,CAAC3B,mBAAV,EAApB;AACAvB,UAAAA,GAAG,CAAE,kBAAiBsD,WAAW,CAAClC,MAAO,SAAQ,CAACkC,WAAW,CAAClC,MAAZ,GAAqB,CAAtB,EAAyBqB,OAAzB,CAAiC,CAAjC,CAAoC,EAAlF,CAAH,CA7B8B,CA+B9B;;AACAzC,UAAAA,GAAG,CAAC,aAAD,CAAH;AACA,gBAAMuD,SAAS,GAAG;AAAA;AAAA,qCAAlB;AACA,gBAAMC,OAAO,GAAG;AAAA;AAAA,sCAAc,CAAd,EAAiB,CAAjB,CAAhB;AACAA,UAAAA,OAAO,CAACzC,UAAR,GAAqB,GAArB,CAnC8B,CAmCJ;;AAC1B,gBAAM0C,OAAO,GAAG;AAAA;AAAA,sCAAc,GAAd,EAAmB,EAAnB,CAAhB;AACAA,UAAAA,OAAO,CAAC1C,UAAR,GAAqB,GAArB;AACA,gBAAM2C,OAAO,GAAG;AAAA;AAAA,sCAAc,GAAd,EAAmB,CAAnB,CAAhB;AACAA,UAAAA,OAAO,CAAC3C,UAAR,GAAqB,GAArB;AACAwC,UAAAA,SAAS,CAACpC,MAAV,CAAiBC,MAAjB,GAA0B,CAA1B;AACAmC,UAAAA,SAAS,CAACpC,MAAV,CAAiBE,IAAjB,CAAsBmC,OAAtB,EAA+BC,OAA/B,EAAwCC,OAAxC;AAEA,gBAAMC,WAAW,GAAGJ,SAAS,CAAChC,mBAAV,EAApB;AACAvB,UAAAA,GAAG,CAAE,kBAAiB2D,WAAW,CAACvC,MAAO,SAAQ,CAACuC,WAAW,CAACvC,MAAZ,GAAqB,CAAtB,EAAyBqB,OAAzB,CAAiC,CAAjC,CAAoC,EAAlF,CAAH,CA5C8B,CA8C9B;;AACAzC,UAAAA,GAAG,CAAC,cAAD,CAAH;AACA,gBAAM4D,SAAS,GAAG;AAAA;AAAA,qCAAlB;AACA,gBAAMC,OAAO,GAAG;AAAA;AAAA,sCAAc,CAAd,EAAiB,CAAjB,CAAhB;AACAA,UAAAA,OAAO,CAAC9C,UAAR,GAAqB,GAArB,CAlD8B,CAkDJ;;AAC1B,gBAAM+C,OAAO,GAAG;AAAA;AAAA,sCAAc,GAAd,EAAmB,CAAnB,CAAhB;AACAA,UAAAA,OAAO,CAAC/C,UAAR,GAAqB,GAArB,CApD8B,CAoDJ;;AAC1B,gBAAMgD,OAAO,GAAG;AAAA;AAAA,sCAAc,GAAd,EAAmB,GAAnB,CAAhB;AACAA,UAAAA,OAAO,CAAChD,UAAR,GAAqB,GAArB;AACA6C,UAAAA,SAAS,CAACzC,MAAV,CAAiBC,MAAjB,GAA0B,CAA1B;AACAwC,UAAAA,SAAS,CAACzC,MAAV,CAAiBE,IAAjB,CAAsBwC,OAAtB,EAA+BC,OAA/B,EAAwCC,OAAxC;AAEA,gBAAMC,WAAW,GAAGJ,SAAS,CAACrC,mBAAV,EAApB;AACAvB,UAAAA,GAAG,CAAE,kBAAiBgE,WAAW,CAAC5C,MAAO,SAAQ,CAAC4C,WAAW,CAAC5C,MAAZ,GAAqB,CAAtB,EAAyBqB,OAAzB,CAAiC,CAAjC,CAAoC,EAAlF,CAAH;AAEAzC,UAAAA,GAAG,CAAC,2BAAD,CAAH;AACH;AAED;AACJ;AACA;;;AACYW,QAAAA,iBAAiB,GAAG;AACxBX,UAAAA,GAAG,CAAC,mBAAD,CAAH;AAEA,gBAAMY,QAAQ,GAAG;AAAA;AAAA,qCAAjB,CAHwB,CAKxB;;AACA,gBAAMC,MAAM,GAAG;AAAA;AAAA,sCAAc,CAAd,EAAiB,CAAjB,CAAf;AACAA,UAAAA,MAAM,CAACC,KAAP,GAAe,GAAf;AAEA,gBAAME,MAAM,GAAG;AAAA;AAAA,sCAAc,GAAd,EAAmB,CAAnB,CAAf;AACAA,UAAAA,MAAM,CAACF,KAAP,GAAe,GAAf;AAEA,gBAAMG,MAAM,GAAG;AAAA;AAAA,sCAAc,GAAd,EAAmB,CAAnB,CAAf;AACAA,UAAAA,MAAM,CAACH,KAAP,GAAe,GAAf;AAEAF,UAAAA,QAAQ,CAACO,MAAT,GAAkB,CAACN,MAAD,EAASG,MAAT,EAAiBC,MAAjB,CAAlB;AAEA,gBAAMK,UAAU,GAAGV,QAAQ,CAACW,mBAAT,EAAnB,CAjBwB,CAmBxB;;AACAvB,UAAAA,GAAG,CAAC,SAAD,CAAH;AACA,gBAAMiE,YAAY,GAAG,CAAC,CAAD,EAAI/B,IAAI,CAACK,KAAL,CAAWjB,UAAU,CAACF,MAAX,GAAoB,IAA/B,CAAJ,EAA0Cc,IAAI,CAACK,KAAL,CAAWjB,UAAU,CAACF,MAAX,GAAoB,GAA/B,CAA1C,EAA+Ec,IAAI,CAACK,KAAL,CAAWjB,UAAU,CAACF,MAAX,GAAoB,IAA/B,CAA/E,EAAqHE,UAAU,CAACF,MAAX,GAAoB,CAAzI,CAArB;;AAEA,eAAK,MAAM8C,KAAX,IAAoBD,YAApB,EAAkC;AAC9B,gBAAIC,KAAK,GAAG5C,UAAU,CAACF,MAAvB,EAA+B;AAC3B,oBAAMyB,KAAK,GAAGvB,UAAU,CAAC4C,KAAD,CAAxB;AACA,oBAAMC,QAAQ,GAAG,CAACD,KAAK,IAAI5C,UAAU,CAACF,MAAX,GAAoB,CAAxB,CAAL,GAAkC,GAAnC,EAAwCqB,OAAxC,CAAgD,CAAhD,CAAjB;AACAzC,cAAAA,GAAG,CAAE,OAAMmE,QAAS,UAAStB,KAAK,CAACf,CAAN,CAAQW,OAAR,CAAgB,CAAhB,CAAmB,KAAII,KAAK,CAACb,CAAN,CAAQS,OAAR,CAAgB,CAAhB,CAAmB,SAAQI,KAAK,CAAC/B,KAAN,CAAY2B,OAAZ,CAAoB,CAApB,CAAuB,EAAnG,CAAH;AACH;AACJ,WA7BuB,CA+BxB;;;AACA,cAAI2B,UAAU,GAAG,CAAjB;;AACA,eAAK,IAAI3C,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,UAAU,CAACF,MAA/B,EAAuCK,CAAC,EAAxC,EAA4C;AACxC,kBAAM4C,SAAS,GAAGnC,IAAI,CAACoC,GAAL,CAAShD,UAAU,CAACG,CAAD,CAAV,CAAcX,KAAd,GAAsBQ,UAAU,CAACG,CAAC,GAAC,CAAH,CAAV,CAAgBX,KAA/C,CAAlB;;AACA,gBAAIuD,SAAS,GAAG,EAAhB,EAAoB;AAAE;AAClBD,cAAAA,UAAU;AACb;AACJ;;AACDpE,UAAAA,GAAG,CAAE,eAAcoE,UAAW,EAA3B,CAAH;;AAEA,cAAIA,UAAU,KAAK,CAAnB,EAAsB;AAClBpE,YAAAA,GAAG,CAAC,aAAD,CAAH;AACH,WAFD,MAEO;AACHA,YAAAA,GAAG,CAAC,mBAAD,CAAH;AACH;AACJ;;AApRyC,O", "sourcesContent": ["import { _decorator, Component, log } from 'cc';\nimport { PathData, PathPoint } from '../data/PathData';\n\nconst { ccclass, property } = _decorator;\n\n/**\n * 路径系统测试类 - 验证新的getSubdividedPoints方法\n */\n@ccclass('PathSystemTest')\nexport class PathSystemTest extends Component {\n\n    onLoad() {\n        this.testRecursiveDepth();\n        this.testCurveGeneration();\n        this.testSubdividedPoints();\n        this.testAdaptiveSubdivision();\n        this.testSpeedSampling();\n    }\n\n    /**\n     * 测试递归深度是否正常工作\n     */\n    private testRecursiveDepth() {\n        log(\"=== 测试递归深度修复 ===\");\n\n        const pathData = new PathData();\n\n        // 创建一个需要大量细分的急转弯\n        const point1 = new PathPoint(0, 0);\n        point1.speed = 300;\n        point1.smoothness = 1.0; // 最高平滑度\n\n        const point2 = new PathPoint(50, 0);\n        point2.speed = 300;\n        point2.smoothness = 1.0;\n\n        const point3 = new PathPoint(50, 50);\n        point3.speed = 300;\n        point3.smoothness = 1.0;\n\n        const point4 = new PathPoint(0, 50);\n        point4.speed = 300;\n        point4.smoothness = 1.0;\n\n        pathData.points.length = 0;\n        pathData.points.push(point1, point2, point3, point4);\n\n        const subdivided = pathData.getSubdividedPoints();\n        log(`急转弯路径细分点数量: ${subdivided.length}`);\n\n        if (subdivided.length > 4) {\n            log(\"✓ 递归细分正常工作，产生了额外的细分点\");\n\n            // 检查细分点是否形成平滑曲线\n            let hasSmoothing = false;\n            for (let i = 1; i < subdivided.length - 1; i++) {\n                const prev = subdivided[i - 1];\n                const curr = subdivided[i];\n                const next = subdivided[i + 1];\n\n                // 检查是否有曲线特征（不在直线上）\n                const linearX = prev.x + (next.x - prev.x) * 0.5;\n                const linearY = prev.y + (next.y - prev.y) * 0.5;\n                const deviation = Math.sqrt(Math.pow(curr.x - linearX, 2) + Math.pow(curr.y - linearY, 2));\n\n                if (deviation > 0.5) {\n                    hasSmoothing = true;\n                    break;\n                }\n            }\n\n            if (hasSmoothing) {\n                log(\"✓ 检测到曲线平滑效果\");\n            } else {\n                log(\"⚠ 细分点可能仍然在直线上\");\n            }\n        } else {\n            log(\"⚠ 递归细分可能仍有问题，细分点数量过少\");\n        }\n    }\n\n    /**\n     * 测试曲线生成是否正常\n     */\n    private testCurveGeneration() {\n        log(\"=== 测试曲线生成 ===\");\n\n        const pathData = new PathData();\n\n        // 创建一个简单的弯曲路径\n        const point1 = new PathPoint(0, 0);\n        point1.speed = 300;\n        point1.smoothness = 1.0; // 高平滑度\n\n        const point2 = new PathPoint(100, 0);\n        point2.speed = 400;\n        point2.smoothness = 1.0;\n\n        const point3 = new PathPoint(100, 100);\n        point3.speed = 300;\n        point3.smoothness = 1.0;\n\n        pathData.points.length = 0;\n        pathData.points.push(point1, point2, point3);\n\n        const subdivided = pathData.getSubdividedPoints();\n        log(`曲线路径点数量: ${subdivided.length}`);\n\n        // 检查是否真的是曲线（中间点不应该在直线上）\n        if (subdivided.length >= 3) {\n            const start = subdivided[0];\n            const mid = subdivided[Math.floor(subdivided.length / 2)];\n            const end = subdivided[subdivided.length - 1];\n\n            log(`起点: (${start.x.toFixed(1)}, ${start.y.toFixed(1)})`);\n            log(`中点: (${mid.x.toFixed(1)}, ${mid.y.toFixed(1)})`);\n            log(`终点: (${end.x.toFixed(1)}, ${end.y.toFixed(1)})`);\n\n            // 检查中点是否偏离直线\n            const linearMidX = (start.x + end.x) / 2;\n            const linearMidY = (start.y + end.y) / 2;\n            const deviation = Math.sqrt(Math.pow(mid.x - linearMidX, 2) + Math.pow(mid.y - linearMidY, 2));\n\n            log(`中点偏离直线距离: ${deviation.toFixed(2)}`);\n\n            if (deviation > 1) {\n                log(\"✓ 曲线生成正常\");\n            } else {\n                log(\"⚠ 曲线可能退化为直线\");\n            }\n        }\n    }\n\n    /**\n     * 测试新的细分点方法\n     */\n    private testSubdividedPoints() {\n        log(\"=== 测试getSubdividedPoints方法 ===\");\n        \n        const pathData = new PathData();\n        \n        // 创建具有不同速度的路径点\n        const point1 = new PathPoint(0, 0);\n        point1.speed = 200;\n        point1.smoothness = 1.0;\n        \n        const point2 = new PathPoint(100, 0);\n        point2.speed = 600;\n        point2.smoothness = 1.0;\n        \n        const point3 = new PathPoint(200, 100);\n        point3.speed = 300;\n        point3.smoothness = 0.5;\n        \n        pathData.points.length = 0;\n        pathData.points.push(point1, point2, point3);\n        \n        // 获取细分点\n        const subdivided = pathData.getSubdividedPoints();\n        log(`原始路径点数量: ${pathData.points.length}`);\n        log(`细分后点数量: ${subdivided.length}`);\n        \n        // 检查前几个细分点的信息\n        for (let i = 0; i < Math.min(5, subdivided.length); i++) {\n            const point = subdivided[i];\n            log(`细分点 ${i}: 位置=(${point.x.toFixed(1)}, ${point.y.toFixed(1)}), 速度=${point.speed.toFixed(1)}, 平滑度=${point.smoothness.toFixed(2)}`);\n        }\n    }\n\n    /**\n     * 测试自适应细分算法的效果\n     */\n    private testAdaptiveSubdivision() {\n        log(\"=== 测试自适应细分算法效果 ===\");\n\n        // 测试案例1：水平直线（高平滑度但低曲率）\n        log(\"案例1: 水平直线 (高平滑度，低曲率)\");\n        const pathData1 = new PathData();\n        const point1a = new PathPoint(0, 0);\n        point1a.smoothness = 1.0; // 高平滑度\n        const point1b = new PathPoint(200, 0);\n        point1b.smoothness = 1.0; // 高平滑度\n        pathData1.points.length = 0;\n        pathData1.points.push(point1a, point1b);\n\n        const subdivided1 = pathData1.getSubdividedPoints();\n        log(`  原始点: 2, 细分后: ${subdivided1.length}, 倍率: ${(subdivided1.length / 2).toFixed(2)}`);\n\n        // 测试案例2：急转弯（高平滑度且高曲率）\n        log(\"案例2: 急转弯 (高平滑度，高曲率)\");\n        const pathData2 = new PathData();\n        const point2a = new PathPoint(0, 0);\n        point2a.smoothness = 1.0;\n        const point2b = new PathPoint(100, 0);\n        point2b.smoothness = 1.0;\n        const point2c = new PathPoint(100, 100);\n        point2c.smoothness = 1.0;\n        pathData2.points.length = 0;\n        pathData2.points.push(point2a, point2b, point2c);\n\n        const subdivided2 = pathData2.getSubdividedPoints();\n        log(`  原始点: 3, 细分后: ${subdivided2.length}, 倍率: ${(subdivided2.length / 3).toFixed(2)}`);\n\n        // 测试案例3：低平滑度路径\n        log(\"案例3: 低平滑度路径\");\n        const pathData3 = new PathData();\n        const point3a = new PathPoint(0, 0);\n        point3a.smoothness = 0.2; // 低平滑度\n        const point3b = new PathPoint(100, 50);\n        point3b.smoothness = 0.2;\n        const point3c = new PathPoint(200, 0);\n        point3c.smoothness = 0.2;\n        pathData3.points.length = 0;\n        pathData3.points.push(point3a, point3b, point3c);\n\n        const subdivided3 = pathData3.getSubdividedPoints();\n        log(`  原始点: 3, 细分后: ${subdivided3.length}, 倍率: ${(subdivided3.length / 3).toFixed(2)}`);\n\n        // 测试案例4：混合平滑度\n        log(\"案例4: 混合平滑度路径\");\n        const pathData4 = new PathData();\n        const point4a = new PathPoint(0, 0);\n        point4a.smoothness = 0.0; // 直线\n        const point4b = new PathPoint(100, 0);\n        point4b.smoothness = 1.0; // 高平滑度\n        const point4c = new PathPoint(200, 100);\n        point4c.smoothness = 1.0;\n        pathData4.points.length = 0;\n        pathData4.points.push(point4a, point4b, point4c);\n\n        const subdivided4 = pathData4.getSubdividedPoints();\n        log(`  原始点: 3, 细分后: ${subdivided4.length}, 倍率: ${(subdivided4.length / 3).toFixed(2)}`);\n\n        log(\"✓ 自适应细分算法能够根据实际曲率智能调整细分密度\");\n    }\n\n    /**\n     * 测试速度采样的准确性\n     */\n    private testSpeedSampling() {\n        log(\"=== 测试速度采样准确性 ===\");\n        \n        const pathData = new PathData();\n        \n        // 创建速度变化明显的路径\n        const point1 = new PathPoint(0, 0);\n        point1.speed = 100;\n        \n        const point2 = new PathPoint(150, 0);\n        point2.speed = 500;\n        \n        const point3 = new PathPoint(300, 0);\n        point3.speed = 200;\n        \n        pathData.points = [point1, point2, point3];\n        \n        const subdivided = pathData.getSubdividedPoints();\n        \n        // 检查速度插值的合理性\n        log(\"速度变化检查:\");\n        const checkIndices = [0, Math.floor(subdivided.length * 0.25), Math.floor(subdivided.length * 0.5), Math.floor(subdivided.length * 0.75), subdivided.length - 1];\n        \n        for (const index of checkIndices) {\n            if (index < subdivided.length) {\n                const point = subdivided[index];\n                const progress = (index / (subdivided.length - 1) * 100).toFixed(1);\n                log(`  进度${progress}%: 位置=(${point.x.toFixed(1)}, ${point.y.toFixed(1)}), 速度=${point.speed.toFixed(1)}`);\n            }\n        }\n        \n        // 验证速度插值的连续性\n        let speedJumps = 0;\n        for (let i = 1; i < subdivided.length; i++) {\n            const speedDiff = Math.abs(subdivided[i].speed - subdivided[i-1].speed);\n            if (speedDiff > 50) { // 如果速度跳跃超过50，认为是异常\n                speedJumps++;\n            }\n        }\n        log(`检测到的速度跳跃次数: ${speedJumps}`);\n        \n        if (speedJumps === 0) {\n            log(\"✓ 速度插值连续性良好\");\n        } else {\n            log(\"⚠ 速度插值存在跳跃，可能需要调整\");\n        }\n    }\n}\n"]}