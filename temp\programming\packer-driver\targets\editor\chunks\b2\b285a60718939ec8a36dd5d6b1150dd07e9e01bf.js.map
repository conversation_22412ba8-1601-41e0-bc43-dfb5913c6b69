{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/RogueUI.ts"], "names": ["_decorator", "find", "Label", "Node", "tween", "Vec3", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "BundleName", "EventMgr", "ButtonPlus", "ccclass", "property", "RogueUI", "activeTweens", "activeTimeouts", "_callFunc", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "getBundleName", "Common", "onLoad", "nodeFresh", "getComponentInChildren", "addClick", "onFresh", "nodeExclude", "onCancel", "showNodesSequentiallyWithScale", "children", "nodeAbility", "length", "for<PERSON>ach", "child", "active", "showNodeWithHalfScale", "index", "setScale", "halfScaleTween", "to", "scale", "easing", "call", "start", "fullScaleTween", "push", "showNodesSequentially", "delay", "interval", "timeoutId", "setTimeout", "t", "closeUI", "onDestroy", "targetOff", "onShow", "callFunc", "refreshUI", "len", "i", "item", "rogueSelectNodes", "iconBg", "rogueIcon", "rogueName", "rogueDesc", "rogueType", "getComponent", "onHide", "args", "onClose", "stopAllEffects", "stop", "clearTimeout"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAaC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;;AAC7CC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,U,iBAAAA,U;;AAEAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;;;OAGH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBb,U;;yBAGjBc,O,WADZF,OAAO,CAAC,SAAD,C,UAGHC,QAAQ,CAACV,IAAD,C,UAERU,QAAQ,CAACV,IAAD,C,UAERU,QAAQ,CAACV,IAAD,C,UAERU,QAAQ,CAAC,CAACV,IAAD,CAAD,C,UAERU,QAAQ,CAACX,KAAD,C,UAERW,QAAQ,CAACX,KAAD,C,2BAbb,MACaY,OADb;AAAA;AAAA,4BACoC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAexBC,YAfwB,GAeM,EAfN;AAAA,eAgBxBC,cAhBwB,GAgB0B,EAhB1B;AAAA,eAiBhCC,SAjBgC,GAiBH,IAjBG;AAAA;;AAmBZ,eAANC,MAAM,GAAW;AAAE,iBAAO,yBAAP;AAAmC;;AAC9C,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,OAAf;AAAwB;;AACjC,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,MAAlB;AAA0B;;AACxDC,QAAAA,MAAM,GAAS;AACrB,eAAKC,SAAL,CAAgBC,sBAAhB;AAAA;AAAA,wCAAoDC,QAApD,CAA6D,KAAKC,OAAlE,EAA2E,IAA3E;AACA,eAAKC,WAAL,CAAkBH,sBAAlB;AAAA;AAAA,wCAAsDC,QAAtD,CAA+D,KAAKG,QAApE,EAA8E,IAA9E;AAEA,eAAKC,8BAAL;AACH;;AAEOA,QAAAA,8BAA8B,GAAS;AAC3C,gBAAMC,QAAQ,GAAG,KAAKC,WAAL,CAAkBD,QAAnC;AACA,cAAI,CAACA,QAAD,IAAaA,QAAQ,CAACE,MAAT,KAAoB,CAArC,EAAwC,OAFG,CAI3C;;AACAF,UAAAA,QAAQ,CAACG,OAAT,CAAkBC,KAAD,IAAW;AACxBA,YAAAA,KAAK,CAACC,MAAN,GAAe,KAAf;AACH,WAFD,EAL2C,CAS3C;;AACA,eAAKC,qBAAL,CAA2BN,QAA3B,EAAqC,CAArC;AACH;;AACOM,QAAAA,qBAAqB,CAACN,QAAD,EAAmBO,KAAnB,EAAwC;AACjE,cAAIA,KAAK,IAAIP,QAAQ,CAACE,MAAtB,EAA8B;AAE9B,gBAAME,KAAK,GAAGJ,QAAQ,CAACO,KAAD,CAAtB;AACAH,UAAAA,KAAK,CAACC,MAAN,GAAe,IAAf;AACAD,UAAAA,KAAK,CAACI,QAAN,CAAe,IAAIlC,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,CAAf,EALiE,CAOjE;;AACA,gBAAMmC,cAAc,GAAGpC,KAAK,CAAC+B,KAAD,CAAL,CAClBM,EADkB,CACf,IADe,EACT;AAAEC,YAAAA,KAAK,EAAE,IAAIrC,IAAJ,CAAS,GAAT,EAAc,GAAd,EAAmB,CAAnB;AAAT,WADS,EACyB;AAAEsC,YAAAA,MAAM,EAAE;AAAV,WADzB,EAElBC,IAFkB,CAEb,MAAM;AACR;AACA,iBAAKP,qBAAL,CAA2BN,QAA3B,EAAqCO,KAAK,GAAG,CAA7C;AACH,WALkB,EAMlBO,KANkB,EAAvB,CARiE,CAgBjE;;AACA,gBAAMC,cAAc,GAAG1C,KAAK,CAAC+B,KAAD,CAAL,CAClBM,EADkB,CACf,IADe,EACT;AAAEC,YAAAA,KAAK,EAAE,IAAIrC,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf;AAAT,WADS,EACqB;AAAEsC,YAAAA,MAAM,EAAE;AAAV,WADrB,EAElBE,KAFkB,EAAvB;AAIA,eAAK9B,YAAL,CAAkBgC,IAAlB,CAAuBP,cAAvB,EAAuCM,cAAvC;AACH;;AAEOE,QAAAA,qBAAqB,GAAS;AAClC,gBAAMjB,QAAQ,GAAG,KAAKC,WAAL,CAAkBD,QAAnC;AACA,cAAI,CAACA,QAAD,IAAaA,QAAQ,CAACE,MAAT,KAAoB,CAArC,EAAwC,OAFN,CAIlC;;AACAF,UAAAA,QAAQ,CAACG,OAAT,CAAkBC,KAAD,IAAW;AACxBA,YAAAA,KAAK,CAACC,MAAN,GAAe,KAAf;AACH,WAFD,EALkC,CASlC;;AACA,cAAIa,KAAK,GAAG,CAAZ;AACA,gBAAMC,QAAQ,GAAG,GAAjB,CAXkC,CAWZ;;AACtBnB,UAAAA,QAAQ,CAACG,OAAT,CAAiB,CAACC,KAAD,EAAQG,KAAR,KAAkB;AAC/B,kBAAMa,SAAS,GAAGC,UAAU,CAAC,MAAM;AAC/BjB,cAAAA,KAAK,CAACC,MAAN,GAAe,IAAf,CAD+B,CAE/B;;AACAD,cAAAA,KAAK,CAACI,QAAN,CAAe,IAAIlC,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,CAAf,EAH+B,CAI/B;;AACA,oBAAMgD,CAAC,GAAGjD,KAAK,CAAC+B,KAAD,CAAL,CACLM,EADK,CACF,GADE,EACG;AAAEC,gBAAAA,KAAK,EAAE,IAAIrC,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf;AAAT,eADH,EACiC;AAAEsC,gBAAAA,MAAM,EAAE;AAAV,eADjC,EAELE,KAFK,EAAV;AAGA,mBAAK9B,YAAL,CAAkBgC,IAAlB,CAAuBM,CAAvB;AACH,aAT2B,EASzBJ,KAAK,GAAG,IATiB,CAA5B;AAUA,iBAAKjC,cAAL,CAAoB+B,IAApB,CAAyBI,SAAzB;AACAF,YAAAA,KAAK,IAAIC,QAAT;AACH,WAbD;AAcH;;AAEDvB,QAAAA,OAAO,GAAG,CACN;AACA;AACA;AACH;;AACDE,QAAAA,QAAQ,GAAG,CACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACH;;AAEY,cAAPyB,OAAO,GAAG;AAAA;;AACZ;AAAA;AAAA,8BAAMA,OAAN,CAAcxC,OAAd;AACA,kCAAKG,SAAL;AACH;;AACSsC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AAEW,cAANC,MAAM,CAACC,QAAwB,GAAG,IAA5B,EAAiD;AACzD,eAAKzC,SAAL,GAAiByC,QAAjB;AACA,eAAKC,SAAL;AACH;;AAEDA,QAAAA,SAAS,GAAE;AACP,cAAIC,GAAG,GAAG,CAAV;;AACA,eAAI,IAAIC,CAAC,GAAE,CAAX,EAAaA,CAAC,GAACD,GAAf,EAAoBC,CAAC,EAArB,EAAwB;AACpB,gBAAIC,IAAI,GAAG,KAAKC,gBAAL,CAAsBF,CAAtB,CAAX;AAEA,gBAAIG,MAAM,GAAG/D,IAAI,CAAC,QAAD,EAAU6D,IAAV,CAAjB;AACA,gBAAIG,SAAS,GAAGhE,IAAI,CAAC,WAAD,EAAa6D,IAAb,CAApB;AACA,gBAAII,SAAS,GAAGjE,IAAI,CAAC,WAAD,EAAa6D,IAAb,CAApB;AACA,gBAAIK,SAAS,GAAGlE,IAAI,CAAC,WAAD,EAAa6D,IAAb,CAApB;AACA,gBAAIM,SAAS,GAAGnE,IAAI,CAAC,WAAD,EAAa6D,IAAb,CAApB;AACAA,YAAAA,IAAI,CAACO,YAAL;AAAA;AAAA,0CAA+B3C,QAA/B,CAAwC,MAAI;AACxC,mBAAK4B,OAAL;AACH,aAFD,EAEE,IAFF;AAGH;AACJ;;AAEW,cAANgB,MAAM,CAAC,GAAGC,IAAJ,EAAgC,CAAG;;AAClC,cAAPC,OAAO,CAAC,GAAGD,IAAJ,EAAgC;AACzC,eAAKE,cAAL,GADyC,CAClB;AACvB;;AACA,gBAAM1C,QAAQ,GAAG,KAAKC,WAAL,CAAkBD,QAAnC;;AACA,cAAIA,QAAJ,EAAc;AACVA,YAAAA,QAAQ,CAACG,OAAT,CAAkBC,KAAD,IAAW;AACxBA,cAAAA,KAAK,CAACC,MAAN,GAAe,KAAf;AACAD,cAAAA,KAAK,CAACI,QAAN,CAAe,IAAIlC,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,CAAf,EAFwB,CAEW;AACtC,aAHD;AAIH;AACJ;;AACOoE,QAAAA,cAAc,GAAS;AAC3B;AACA,eAAK1D,YAAL,CAAkBmB,OAAlB,CAA2B9B,KAAD,IAAW;AACjCA,YAAAA,KAAK,CAACsE,IAAN;AACH,WAFD;AAGA,eAAK3D,YAAL,GAAoB,EAApB,CAL2B,CAO3B;;AACA,eAAKC,cAAL,CAAoBkB,OAApB,CAA6BiB,SAAD,IAAe;AACvCwB,YAAAA,YAAY,CAACxB,SAAD,CAAZ;AACH,WAFD;AAGA,eAAKnC,cAAL,GAAsB,EAAtB;AACH;;AAzK+B,O;;;;;iBAGP,I;;;;;;;iBAEE,I;;;;;;;iBAEA,I;;;;;;;iBAEA,E;;;;;;;iBAEA,I;;;;;;;iBAEE,I", "sourcesContent": ["import { _decorator, find, Label, Node, Tween, tween, Vec3 } from 'cc';\r\nimport { BaseUI, UILayer, UIMgr } from 'db://assets/scripts/core/base/UIMgr';\r\nimport { BundleName } from '../../../const/BundleConst';\r\nimport { DataEvent } from '../../../event/DataEvent';\r\nimport { EventMgr } from '../../../event/EventManager';\r\nimport { ButtonPlus } from '../../common/components/button/ButtonPlus';\r\n\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass(\"RogueUI\")\r\nexport class RogueUI extends BaseUI {\r\n\r\n    @property(Node)\r\n    nodeFresh: Node | null = null;\r\n    @property(Node)\r\n    nodeExclude: Node | null = null;\r\n    @property(Node)\r\n    nodeAbility: Node | null = null;\r\n    @property([Node])\r\n    rogueSelectNodes: Node[] = [];\r\n    @property(Label)\r\n    freshTimes: Label | null = null;\r\n    @property(Label)\r\n    excludeTimes: Label | null = null;\r\n\r\n    private activeTweens: Tween<Node>[] = [];\r\n    private activeTimeouts: ReturnType<typeof setTimeout>[] = [];\r\n    _callFunc: Function | null = null;\r\n\r\n    public static getUrl(): string { return \"prefab/ui/fight/RogueUI\"; };\r\n    public static getLayer(): UILayer { return UILayer.Default }\r\n    public static getBundleName(): string { return BundleName.Common }\r\n    protected onLoad(): void {\r\n        this.nodeFresh!.getComponentInChildren(ButtonPlus)!.addClick(this.onFresh, this);\r\n        this.nodeExclude!.getComponentInChildren(ButtonPlus)!.addClick(this.onCancel, this);\r\n\r\n        this.showNodesSequentiallyWithScale();\r\n    }\r\n\r\n    private showNodesSequentiallyWithScale(): void {\r\n        const children = this.nodeAbility!.children;\r\n        if (!children || children.length === 0) return;\r\n\r\n        // 初始隐藏所有子节点\r\n        children.forEach((child) => {\r\n            child.active = false;\r\n        });\r\n\r\n        // 显示第一个节点并启动动画\r\n        this.showNodeWithHalfScale(children, 0);\r\n    }\r\n    private showNodeWithHalfScale(children: Node[], index: number): void {\r\n        if (index >= children.length) return;\r\n\r\n        const child = children[index];\r\n        child.active = true;\r\n        child.setScale(new Vec3(0, 0, 1));\r\n\r\n        // 前半部分动画：缩放到一半\r\n        const halfScaleTween = tween(child)\r\n            .to(0.05, { scale: new Vec3(0.5, 0.5, 1) }, { easing: 'quadOut' })\r\n            .call(() => {\r\n                // 缩放到一半时，触发下一个节点\r\n                this.showNodeWithHalfScale(children, index + 1);\r\n            })\r\n            .start();\r\n\r\n        // 后半部分动画：从一半缩放到完整\r\n        const fullScaleTween = tween(child)\r\n            .to(0.05, { scale: new Vec3(1, 1, 1) }, { easing: 'quadOut' })\r\n            .start();\r\n\r\n        this.activeTweens.push(halfScaleTween, fullScaleTween);\r\n    }\r\n\r\n    private showNodesSequentially(): void {\r\n        const children = this.nodeAbility!.children;\r\n        if (!children || children.length === 0) return;\r\n\r\n        // 初始隐藏所有子节点\r\n        children.forEach((child) => {\r\n            child.active = false;\r\n        });\r\n\r\n        // 逐个显示子节点\r\n        let delay = 0;\r\n        const interval = 0.1; // 每个节点显示的间隔时间（秒）\r\n        children.forEach((child, index) => {\r\n            const timeoutId = setTimeout(() => {\r\n                child.active = true;\r\n                // 初始缩放为 0（使用 Vec3 类型）\r\n                child.setScale(new Vec3(0, 0, 1));\r\n                // 缩放动画（使用 Vec3 类型）\r\n                const t = tween(child)\r\n                    .to(0.1, { scale: new Vec3(1, 1, 1) }, { easing: 'quadOut' })\r\n                    .start();\r\n                this.activeTweens.push(t);\r\n            }, delay * 1000);\r\n            this.activeTimeouts.push(timeoutId);\r\n            delay += interval;\r\n        });\r\n    }\r\n\r\n    onFresh() {\r\n        // this.rogueSelectIcons.forEach(element => {\r\n        //     element.updateActive(0);\r\n        // });\r\n    }\r\n    onCancel() {\r\n        // this.rogueSelectIcons.forEach(element => {\r\n        //     element.updateActive(0);\r\n        // });\r\n        // let btn = this.nodeExclude!.getComponentInChildren(ButtonPlus);\r\n        // if (btn!.getComponentInChildren(Label)!.string == \"排除\") {\r\n        //     btn!.getComponentInChildren(Label)!.string = \"取消\";\r\n        //     this.rogueSelectIcons.forEach(element => {\r\n        //         element.updateStatus(2);\r\n        //     });\r\n        // } else {\r\n        //     btn!.getComponentInChildren(Label)!.string = \"排除\";\r\n        //     this.rogueSelectIcons.forEach(element => {\r\n        //         element.updateStatus(1);\r\n        //     });\r\n        // }\r\n    }\r\n\r\n    async closeUI() {\r\n        UIMgr.closeUI(RogueUI)\r\n        this._callFunc?.();\r\n    }\r\n    protected onDestroy(): void {\r\n        EventMgr.targetOff(this)\r\n    }\r\n\r\n    async onShow(callFunc:Function | null = null): Promise<void> {\r\n        this._callFunc = callFunc;\r\n        this.refreshUI();\r\n    }\r\n\r\n    refreshUI(){\r\n        let len = 3;\r\n        for(let i= 0;i<len; i++){\r\n            let item = this.rogueSelectNodes[i];\r\n\r\n            let iconBg = find(\"iconBg\",item);\r\n            let rogueIcon = find(\"rogueIcon\",item);\r\n            let rogueName = find(\"rogueName\",item);\r\n            let rogueDesc = find(\"rogueDesc\",item);\r\n            let rogueType = find(\"rogueType\",item);\r\n            item.getComponent(ButtonPlus)!.addClick(()=>{\r\n                this.closeUI()\r\n            },this)\r\n        }\r\n    }\r\n\r\n    async onHide(...args: any[]): Promise<void> { }\r\n    async onClose(...args: any[]): Promise<void> {\r\n        this.stopAllEffects(); // 停止所有动画\r\n        // 可选：重置节点状态\r\n        const children = this.nodeAbility!.children;\r\n        if (children) {\r\n            children.forEach((child) => {\r\n                child.active = false;\r\n                child.setScale(new Vec3(1, 1, 1)); // 恢复默认缩放\r\n            });\r\n        }\r\n    }\r\n    private stopAllEffects(): void {\r\n        // 停止所有 tween 动画\r\n        this.activeTweens.forEach((tween) => {\r\n            tween.stop();\r\n        });\r\n        this.activeTweens = [];\r\n\r\n        // 清除所有 setTimeout\r\n        this.activeTimeouts.forEach((timeoutId) => {\r\n            clearTimeout(timeoutId as unknown as number);\r\n        });\r\n        this.activeTimeouts = [];\r\n    }\r\n}\r\n"]}