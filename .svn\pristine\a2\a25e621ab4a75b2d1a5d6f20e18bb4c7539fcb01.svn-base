import { _decorator} from 'cc';
import { GameEnum } from '../../../const/GameEnum';
import PlaneBase from 'db://assets/bundles/common/script/game/ui/plane/PlaneBase';
import FCollider from '../../../collider-system/FCollider';
import { Bullet } from '../../../bullet/Bullet';
import { Plane } from 'db://assets/bundles/common/script/ui/Plane';
import { Weapon } from 'db://assets/bundles/common/script/game/ui/plane/weapon/Weapon';
import { EnemyData } from '../../../data/EnemyData';
import { AttributeConst } from 'db://assets/bundles/common/script/const/AttributeConst';
import { eMoveEvent } from 'db://assets/bundles/common/script/game/move/IMovable'
import { DefaultMove } from 'db://assets/bundles/common/script/game/move/DefaultMove';
import { GameIns } from '../../../GameIns';

const { ccclass, property } = _decorator;

@ccclass('EnemyPlaneBase')
export default class EnemyPlaneBase extends PlaneBase {
    @property(Plane)//敌机显示组件
    plane: Plane | null = null;
    _enemyData: EnemyData | null = null;
    _moveCom: DefaultMove | null = null;
    public get moveCom() { return this._moveCom; }

    removeAble:boolean = false;
    bullets: Bullet[] = [];

    private _weapons: Weapon[] = [];

    initPlane(data: EnemyData) {
        this.enemy = true
        this._enemyData = data;
        super.init();

        this._moveCom = this.getComponent(DefaultMove) || this.addComponent(DefaultMove);
        this._moveCom!.removeAllListeners();
        this._moveCom!.on(eMoveEvent.onBecomeInvisible, () => this.onBecameInvisible());
        this._moveCom!.on(eMoveEvent.onBecomeVisible, () => this.onBecameVisible());

        // 初始化武器
        this._weapons = this.getComponentsInChildren(Weapon);
        if (this._weapons.length > 0) {
            for (let weapon of this._weapons) {
                weapon.init();
                weapon.setOwner(this).setTarget(GameIns.mainPlaneManager.mainPlane);
            }
        }

        this.refreshProperty();
    }

    initMove(angle: number) {
        if (!this._moveCom) {
            return;
        }

        // 速度从表格里读取
        this._moveCom!.speed = this._enemyData!.config?.moveSpeed || 100;
        this._moveCom!.speedAngle = angle;
        this._moveCom!.turnSpeed = this._enemyData!.config?.turnSpeed || 0;
        this._moveCom!.setMovable(true);
    }

    _dieWhenOffScreen() {
        this.toDie(GameEnum.EnemyDestroyType.Leave);
    }

    private onBecameInvisible() {
        // TODO: 从表格里增加延时销毁的配置
        this._dieWhenOffScreen();
        // this.scheduleOnce(this._dieWhenOffScreen, delayDestroy);
    }

    private onBecameVisible() {
        // this.unschedule(this._dieWhenOffScreen);
    }

    refreshProperty() {
        const config = this._enemyData?.config;
        if (!config) {
            return;
        }
        this.attribute.addBaseAttribute(AttributeConst.MaxHPOutAdd, config.baseHp);
        this.attribute.addBaseAttribute(AttributeConst.AttackOutAdd, config.baseAtk);
        this.attribute.addBaseAttribute(AttributeConst.StatusImmuneBulletHurt, config.immuneBulletDamage?1:0);
        this.attribute.addBaseAttribute(AttributeConst.StatusImmuneCollisionHurt, config.immuneCollideDamage?1:0);
        this.attribute.addBaseAttribute(AttributeConst.StatusIgnoreBullet, config.ignoreBullet?1:0);
        this.attribute.addBaseAttribute(AttributeConst.StatusIgnoreCollision, config.ignoreCollide?1:0);
        this.attribute.addBaseAttribute(AttributeConst.StatusImmuneNuclearHurt, config.immuneNuke?1:0);
        this.attribute.addBaseAttribute(AttributeConst.StatusImmuneActiveSkillHurt, config.immuneActiveSkill?1:0);

        this.curHp = this.maxHp
    }

    getAttack():number {
        return this._enemyData!.getAttack();
    }

    toDie(destroyType: GameEnum.EnemyDestroyType = GameEnum.EnemyDestroyType.Die): boolean  {
        if (!super.toDie(destroyType)) {
            return false;
        }
        this.colliderEnabled = false;

        this.onDie(destroyType);
        return true;
    }

    onDie(destroyType: number) {
        this.willRemove();

        switch (destroyType) {
            case GameEnum.EnemyDestroyType.Die:
                this.playDieAnim(()=>{
                    this.removeAble = true;
                });
                break;

            case GameEnum.EnemyDestroyType.Leave:
            case GameEnum.EnemyDestroyType.TrackOver:
            case GameEnum.EnemyDestroyType.TimeOver:
                this.removeAble = true;
                break;
        }
    }

    playDieAnim(callBack: Function) {
        // if (this.plane) {
        //     this.plane.playDieAnim(callBack);   
        // }
        callBack?.();
    }

    get collisionLevel() {
        return this._enemyData!.config?.collideLevel || 0;
    }
    get collisionHurt() {
        return this._enemyData!.config?.collideDamage || 0;
    }

    onCollide(collider: FCollider) {
        if (this.isDead) {
            return
        }
        if (collider.entity instanceof Bullet) {
            if (this.attribute.getFinalAttributeByKey(AttributeConst.StatusImmuneBulletHurt) == 0) {
                const damage = collider.entity.calcDamage(this);
                GameIns.hurtEffectManager.createHurtNumByType(collider.entity.node.getPosition(), damage);
                this.hurt(damage)
            }
        } else if (collider.entity instanceof PlaneBase && !collider.entity.enemy) {
            this.collisionPlane(collider.entity);
        }
    }

    /**
     * 准备移除敌机
     */
    willRemove() {

    }

    addBullet(bullet: Bullet) {
        if (this.bullets) {
            this.bullets.push(bullet);
        }
    }

    /**
     * 从敌人移除子弹
     * @param {Bullet} bullet 子弹对象
     */
    removeBullet(bullet: Bullet) {
        if (this.bullets) {
            const index = this.bullets.indexOf(bullet);
            if (index >= 0) {
                this.bullets.splice(index, 1);
            }
        }
    }

    setPos(x: number, y: number) {
        this.node.setPosition(x, y);
    }
}