[{"modeID": 1, "modeType": 3, "chapterID": 1100001, "order": 1, "resourceID": 9, "description": "第一关", "conList": [{"cond_type": 1, "params": [20]}], "periodType": 2, "times": 2, "costEnergy": 0, "monType": 3, "costParam1": 111, "costParam2": 1, "rebirthTimes": 0, "rebirthCost": 0, "power": 0, "rogueID": 9, "LevelLimit": 30, "rogueFirst": 5, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2, "modeType": 3, "chapterID": 1100001, "order": 2, "resourceID": 10, "description": "第二关", "conList": [{"cond_type": 2, "params": [1]}], "periodType": 2, "times": 2, "costEnergy": 0, "monType": 3, "costParam1": 111, "costParam2": 1, "rebirthTimes": 0, "rebirthCost": 0, "power": 0, "rogueID": 10, "LevelLimit": 30, "rogueFirst": 5, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3, "modeType": 3, "chapterID": 1100001, "order": 3, "resourceID": 11, "description": "第三关", "conList": [{"cond_type": 2, "params": [2]}], "periodType": 2, "times": 2, "costEnergy": 0, "monType": 3, "costParam1": 111, "costParam2": 1, "rebirthTimes": 0, "rebirthCost": 0, "power": 0, "rogueID": 11, "LevelLimit": 30, "rogueFirst": 5, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 4, "modeType": 3, "chapterID": 1100001, "order": 4, "resourceID": 12, "description": "第四关", "conList": [{"cond_type": 2, "params": [3]}], "periodType": 2, "times": 2, "costEnergy": 0, "monType": 3, "costParam1": 111, "costParam2": 1, "rebirthTimes": 0, "rebirthCost": 0, "power": 0, "rogueID": 12, "LevelLimit": 30, "rogueFirst": 5, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 5, "modeType": 3, "chapterID": 1100001, "order": 5, "resourceID": 13, "description": "第五关", "conList": [{"cond_type": 2, "params": [4]}], "periodType": 2, "times": 2, "costEnergy": 0, "monType": 3, "costParam1": 111, "costParam2": 1, "rebirthTimes": 0, "rebirthCost": 0, "power": 0, "rogueID": 13, "LevelLimit": 30, "rogueFirst": 5, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 101, "modeType": 1, "chapterID": 1100001, "order": 1, "resourceID": 1, "description": "主游戏模式，打分", "conList": [], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 0, "rogueID": 1, "LevelLimit": 25, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 102, "modeType": 5, "chapterID": 1100001, "order": 1, "resourceID": 2, "description": "邀请好友PK", "conList": [{"cond_type": 1, "params": [10]}], "periodType": 1, "times": 5, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 0, "rogueID": 2, "LevelLimit": 25, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 103, "modeType": 4, "chapterID": 1100001, "order": 1, "resourceID": 3, "description": "金币PK", "conList": [{"cond_type": 1, "params": [10]}], "periodType": 1, "times": 10, "costEnergy": 0, "monType": 1, "costParam1": 1000, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 0, "rogueID": 3, "LevelLimit": 25, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 104, "modeType": 4, "chapterID": 1100001, "order": 2, "resourceID": 4, "description": "钻石PK", "conList": [{"cond_type": 1, "params": [10]}], "periodType": 1, "times": 10, "costEnergy": 0, "monType": 2, "costParam1": 200, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 0, "rogueID": 4, "LevelLimit": 25, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 105, "modeType": 4, "chapterID": 1100001, "order": 3, "resourceID": 5, "description": "高级钻石PK", "conList": [{"cond_type": 1, "params": [10]}], "periodType": 1, "times": 10, "costEnergy": 0, "monType": 2, "costParam1": 500, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 0, "rogueID": 5, "LevelLimit": 25, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2001, "modeType": 2, "chapterID": 2000001, "order": 1, "resourceID": 6, "description": "简单·第1章", "conList": [], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 693, "rogueID": 6, "LevelLimit": 15, "rogueFirst": 0, "sweepLimit": 2, "rewardID": 0, "ratingList": []}, {"modeID": 2002, "modeType": 2, "chapterID": 2000002, "order": 2, "resourceID": 7, "description": "简单·第2章", "conList": [{"cond_type": 2, "params": [2001]}, {"cond_type": 1, "params": [1]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 796, "rogueID": 7, "LevelLimit": 15, "rogueFirst": 0, "sweepLimit": 2, "rewardID": 0, "ratingList": []}, {"modeID": 2003, "modeType": 2, "chapterID": 2000003, "order": 3, "resourceID": 8, "description": "简单·第3章", "conList": [{"cond_type": 2, "params": [2002]}, {"cond_type": 1, "params": [2]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 817, "rogueID": 8, "LevelLimit": 15, "rogueFirst": 0, "sweepLimit": 5, "rewardID": 0, "ratingList": []}, {"modeID": 2004, "modeType": 2, "chapterID": 2000004, "order": 0, "resourceID": 0, "description": "简单·第4章", "conList": [{"cond_type": 2, "params": [2003]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 838, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2005, "modeType": 2, "chapterID": 2000005, "order": 0, "resourceID": 0, "description": "简单·第5章", "conList": [{"cond_type": 2, "params": [2004]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 974, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2006, "modeType": 2, "chapterID": 2000006, "order": 0, "resourceID": 0, "description": "简单·第6章", "conList": [{"cond_type": 2, "params": [2005]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 998, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2007, "modeType": 2, "chapterID": 2000007, "order": 0, "resourceID": 0, "description": "简单·第7章", "conList": [{"cond_type": 2, "params": [2006]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 1037, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2008, "modeType": 2, "chapterID": 2000008, "order": 0, "resourceID": 0, "description": "简单·第8章", "conList": [{"cond_type": 2, "params": [2007]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 1061, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2009, "modeType": 2, "chapterID": 2000009, "order": 0, "resourceID": 0, "description": "简单·第9章", "conList": [{"cond_type": 2, "params": [2008]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 1444, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2010, "modeType": 2, "chapterID": 2000010, "order": 0, "resourceID": 0, "description": "简单·第10章", "conList": [{"cond_type": 2, "params": [2009]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 1477, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2011, "modeType": 2, "chapterID": 2000011, "order": 0, "resourceID": 0, "description": "简单·第11章", "conList": [{"cond_type": 2, "params": [2010]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 1507, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2012, "modeType": 2, "chapterID": 2000012, "order": 0, "resourceID": 0, "description": "简单·第12章", "conList": [{"cond_type": 2, "params": [2011]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 1561, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2013, "modeType": 2, "chapterID": 2000013, "order": 0, "resourceID": 0, "description": "简单·第13章", "conList": [{"cond_type": 2, "params": [2012]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 1595, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2014, "modeType": 2, "chapterID": 2000014, "order": 0, "resourceID": 0, "description": "简单·第14章", "conList": [{"cond_type": 2, "params": [2013]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 1628, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2015, "modeType": 2, "chapterID": 2000015, "order": 0, "resourceID": 0, "description": "简单·第15章", "conList": [{"cond_type": 2, "params": [2014]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 2162, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2016, "modeType": 2, "chapterID": 2000016, "order": 0, "resourceID": 0, "description": "简单·第16章", "conList": [{"cond_type": 2, "params": [2015]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 2205, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2017, "modeType": 2, "chapterID": 2000017, "order": 0, "resourceID": 0, "description": "简单·第17章", "conList": [{"cond_type": 2, "params": [2016]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 2249, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2018, "modeType": 2, "chapterID": 2000018, "order": 0, "resourceID": 0, "description": "简单·第18章", "conList": [{"cond_type": 2, "params": [2017]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 2288, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2019, "modeType": 2, "chapterID": 2000019, "order": 0, "resourceID": 0, "description": "简单·第19章", "conList": [{"cond_type": 2, "params": [2018]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 2364, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2020, "modeType": 2, "chapterID": 2000020, "order": 0, "resourceID": 0, "description": "简单·第20章", "conList": [{"cond_type": 2, "params": [2019]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 2626, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2021, "modeType": 2, "chapterID": 2000021, "order": 0, "resourceID": 0, "description": "简单·第21章", "conList": [{"cond_type": 2, "params": [2020]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 2675, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2022, "modeType": 2, "chapterID": 2000022, "order": 0, "resourceID": 0, "description": "简单·第22章", "conList": [{"cond_type": 2, "params": [2021]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 2723, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2023, "modeType": 2, "chapterID": 2000023, "order": 0, "resourceID": 0, "description": "简单·第23章", "conList": [{"cond_type": 2, "params": [2022]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 2771, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2024, "modeType": 2, "chapterID": 2000024, "order": 0, "resourceID": 0, "description": "简单·第24章", "conList": [{"cond_type": 2, "params": [2023]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 2848, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2025, "modeType": 2, "chapterID": 2000025, "order": 0, "resourceID": 0, "description": "简单·第25章", "conList": [{"cond_type": 2, "params": [2024]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 2897, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2026, "modeType": 2, "chapterID": 2000026, "order": 0, "resourceID": 0, "description": "简单·第26章", "conList": [{"cond_type": 2, "params": [2025]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 3207, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2027, "modeType": 2, "chapterID": 2000027, "order": 0, "resourceID": 0, "description": "简单·第27章", "conList": [{"cond_type": 2, "params": [2026]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 3260, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2028, "modeType": 2, "chapterID": 2000028, "order": 0, "resourceID": 0, "description": "简单·第28章", "conList": [{"cond_type": 2, "params": [2027]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 3314, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2029, "modeType": 2, "chapterID": 2000029, "order": 0, "resourceID": 0, "description": "简单·第29章", "conList": [{"cond_type": 2, "params": [2028]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 3367, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2030, "modeType": 2, "chapterID": 2000030, "order": 0, "resourceID": 0, "description": "简单·第30章", "conList": [{"cond_type": 2, "params": [2029]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 3420, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2031, "modeType": 2, "chapterID": 2000031, "order": 0, "resourceID": 0, "description": "简单·第31章", "conList": [{"cond_type": 2, "params": [2030]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 3510, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2032, "modeType": 2, "chapterID": 2000032, "order": 0, "resourceID": 0, "description": "简单·第32章", "conList": [{"cond_type": 2, "params": [2031]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 3564, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2033, "modeType": 2, "chapterID": 2000033, "order": 0, "resourceID": 0, "description": "简单·第33章", "conList": [{"cond_type": 2, "params": [2032]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 4628, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2034, "modeType": 2, "chapterID": 2000034, "order": 0, "resourceID": 0, "description": "简单·第34章", "conList": [{"cond_type": 2, "params": [2033]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 4697, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2035, "modeType": 2, "chapterID": 2000035, "order": 0, "resourceID": 0, "description": "简单·第35章", "conList": [{"cond_type": 2, "params": [2034]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 4884, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2036, "modeType": 2, "chapterID": 2000036, "order": 0, "resourceID": 0, "description": "简单·第36章", "conList": [{"cond_type": 2, "params": [2035]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 4955, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2037, "modeType": 2, "chapterID": 2000037, "order": 0, "resourceID": 0, "description": "简单·第37章", "conList": [{"cond_type": 2, "params": [2036]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 5019, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2038, "modeType": 2, "chapterID": 2000038, "order": 0, "resourceID": 0, "description": "简单·第38章", "conList": [{"cond_type": 2, "params": [2037]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 5090, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2039, "modeType": 2, "chapterID": 2000039, "order": 0, "resourceID": 0, "description": "简单·第39章", "conList": [{"cond_type": 2, "params": [2038]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 5239, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2040, "modeType": 2, "chapterID": 2000040, "order": 0, "resourceID": 0, "description": "简单·第40章", "conList": [{"cond_type": 2, "params": [2039]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 5312, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2041, "modeType": 2, "chapterID": 2000041, "order": 0, "resourceID": 0, "description": "简单·第41章", "conList": [{"cond_type": 2, "params": [2040]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 5384, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2042, "modeType": 2, "chapterID": 2000042, "order": 0, "resourceID": 0, "description": "简单·第42章", "conList": [{"cond_type": 2, "params": [2041]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 5951, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2043, "modeType": 2, "chapterID": 2000043, "order": 0, "resourceID": 0, "description": "简单·第43章", "conList": [{"cond_type": 2, "params": [2042]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 6030, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2044, "modeType": 2, "chapterID": 2000044, "order": 0, "resourceID": 0, "description": "简单·第44章", "conList": [{"cond_type": 2, "params": [2043]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 6110, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2045, "modeType": 2, "chapterID": 2000045, "order": 0, "resourceID": 0, "description": "简单·第45章", "conList": [{"cond_type": 2, "params": [2044]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 6189, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2046, "modeType": 2, "chapterID": 2000046, "order": 0, "resourceID": 0, "description": "简单·第46章", "conList": [{"cond_type": 2, "params": [2045]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 6268, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2047, "modeType": 2, "chapterID": 2000047, "order": 0, "resourceID": 0, "description": "简单·第47章", "conList": [{"cond_type": 2, "params": [2046]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 6347, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2048, "modeType": 2, "chapterID": 2000048, "order": 0, "resourceID": 0, "description": "简单·第48章", "conList": [{"cond_type": 2, "params": [2047]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 7515, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2049, "modeType": 2, "chapterID": 2000049, "order": 0, "resourceID": 0, "description": "简单·第49章", "conList": [{"cond_type": 2, "params": [2048]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 7624, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2050, "modeType": 2, "chapterID": 2000050, "order": 0, "resourceID": 0, "description": "简单·第50章", "conList": [{"cond_type": 2, "params": [2049]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 7884, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2051, "modeType": 2, "chapterID": 2000051, "order": 0, "resourceID": 0, "description": "简单·第51章", "conList": [{"cond_type": 2, "params": [2050]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 8634, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2052, "modeType": 2, "chapterID": 2000052, "order": 0, "resourceID": 0, "description": "简单·第52章", "conList": [{"cond_type": 2, "params": [2051]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 8767, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2053, "modeType": 2, "chapterID": 2000053, "order": 0, "resourceID": 0, "description": "简单·第53章", "conList": [{"cond_type": 2, "params": [2052]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 8997, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2054, "modeType": 2, "chapterID": 2000054, "order": 0, "resourceID": 0, "description": "简单·第54章", "conList": [{"cond_type": 2, "params": [2053]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 9134, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2055, "modeType": 2, "chapterID": 2000055, "order": 0, "resourceID": 0, "description": "简单·第55章", "conList": [{"cond_type": 2, "params": [2054]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 9248, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2056, "modeType": 2, "chapterID": 2000056, "order": 0, "resourceID": 0, "description": "简单·第56章", "conList": [{"cond_type": 2, "params": [2055]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 9362, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2057, "modeType": 2, "chapterID": 2000057, "order": 0, "resourceID": 0, "description": "简单·第57章", "conList": [{"cond_type": 2, "params": [2056]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 9476, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2058, "modeType": 2, "chapterID": 2000058, "order": 0, "resourceID": 0, "description": "简单·第58章", "conList": [{"cond_type": 2, "params": [2057]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 9860, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2059, "modeType": 2, "chapterID": 2000059, "order": 0, "resourceID": 0, "description": "简单·第59章", "conList": [{"cond_type": 2, "params": [2058]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 9978, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2060, "modeType": 2, "chapterID": 2000060, "order": 0, "resourceID": 0, "description": "简单·第60章", "conList": [{"cond_type": 2, "params": [2059]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 10096, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2061, "modeType": 2, "chapterID": 2000061, "order": 0, "resourceID": 0, "description": "简单·第61章", "conList": [{"cond_type": 2, "params": [2060]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 10439, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2062, "modeType": 2, "chapterID": 2000062, "order": 0, "resourceID": 0, "description": "简单·第62章", "conList": [{"cond_type": 2, "params": [2061]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 12522, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2063, "modeType": 2, "chapterID": 2000063, "order": 0, "resourceID": 0, "description": "简单·第63章", "conList": [{"cond_type": 2, "params": [2062]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 12664, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2064, "modeType": 2, "chapterID": 2000064, "order": 0, "resourceID": 0, "description": "简单·第64章", "conList": [{"cond_type": 2, "params": [2063]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 13107, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2065, "modeType": 2, "chapterID": 2000065, "order": 0, "resourceID": 0, "description": "简单·第65章", "conList": [{"cond_type": 2, "params": [2064]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 13254, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2066, "modeType": 2, "chapterID": 2000066, "order": 0, "resourceID": 0, "description": "简单·第66章", "conList": [{"cond_type": 2, "params": [2065]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 13402, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2067, "modeType": 2, "chapterID": 2000067, "order": 0, "resourceID": 0, "description": "简单·第67章", "conList": [{"cond_type": 2, "params": [2066]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 13549, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2068, "modeType": 2, "chapterID": 2000068, "order": 0, "resourceID": 0, "description": "简单·第68章", "conList": [{"cond_type": 2, "params": [2067]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 13696, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2069, "modeType": 2, "chapterID": 2000069, "order": 0, "resourceID": 0, "description": "简单·第69章", "conList": [{"cond_type": 2, "params": [2068]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 13810, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2070, "modeType": 2, "chapterID": 2000070, "order": 0, "resourceID": 0, "description": "简单·第70章", "conList": [{"cond_type": 2, "params": [2069]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 14102, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2071, "modeType": 2, "chapterID": 2000071, "order": 0, "resourceID": 0, "description": "简单·第71章", "conList": [{"cond_type": 2, "params": [2070]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 14251, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2072, "modeType": 2, "chapterID": 2000072, "order": 0, "resourceID": 0, "description": "简单·第72章", "conList": [{"cond_type": 2, "params": [2071]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 14401, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2073, "modeType": 2, "chapterID": 2000073, "order": 0, "resourceID": 0, "description": "简单·第73章", "conList": [{"cond_type": 2, "params": [2072]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 14833, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2074, "modeType": 2, "chapterID": 2000074, "order": 0, "resourceID": 0, "description": "简单·第74章", "conList": [{"cond_type": 2, "params": [2073]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 16928, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2075, "modeType": 2, "chapterID": 2000075, "order": 0, "resourceID": 0, "description": "简单·第75章", "conList": [{"cond_type": 2, "params": [2074]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 17101, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2076, "modeType": 2, "chapterID": 2000076, "order": 0, "resourceID": 0, "description": "简单·第76章", "conList": [{"cond_type": 2, "params": [2075]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 17235, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2077, "modeType": 2, "chapterID": 2000077, "order": 0, "resourceID": 0, "description": "简单·第77章", "conList": [{"cond_type": 2, "params": [2076]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 17408, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2078, "modeType": 2, "chapterID": 2000078, "order": 0, "resourceID": 0, "description": "简单·第78章", "conList": [{"cond_type": 2, "params": [2077]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 17581, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2079, "modeType": 2, "chapterID": 2000079, "order": 0, "resourceID": 0, "description": "简单·第79章", "conList": [{"cond_type": 2, "params": [2078]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 17754, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2080, "modeType": 2, "chapterID": 2000080, "order": 0, "resourceID": 0, "description": "简单·第80章", "conList": [{"cond_type": 2, "params": [2079]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 17927, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2081, "modeType": 2, "chapterID": 2000081, "order": 0, "resourceID": 0, "description": "简单·第81章", "conList": [{"cond_type": 2, "params": [2080]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 18100, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2082, "modeType": 2, "chapterID": 2000082, "order": 0, "resourceID": 0, "description": "简单·第82章", "conList": [{"cond_type": 2, "params": [2081]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 18235, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2083, "modeType": 2, "chapterID": 2000083, "order": 0, "resourceID": 0, "description": "简单·第83章", "conList": [{"cond_type": 2, "params": [2082]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 18704, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2084, "modeType": 2, "chapterID": 2000084, "order": 0, "resourceID": 0, "description": "简单·第84章", "conList": [{"cond_type": 2, "params": [2083]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 18882, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2085, "modeType": 2, "chapterID": 2000085, "order": 0, "resourceID": 0, "description": "简单·第85章", "conList": [{"cond_type": 2, "params": [2084]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 19370, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2086, "modeType": 2, "chapterID": 2000086, "order": 0, "resourceID": 0, "description": "简单·第86章", "conList": [{"cond_type": 2, "params": [2085]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 19897, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2087, "modeType": 2, "chapterID": 2000087, "order": 0, "resourceID": 0, "description": "简单·第87章", "conList": [{"cond_type": 2, "params": [2086]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 20082, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2088, "modeType": 2, "chapterID": 2000088, "order": 0, "resourceID": 0, "description": "简单·第88章", "conList": [{"cond_type": 2, "params": [2087]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 22059, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2089, "modeType": 2, "chapterID": 2000089, "order": 0, "resourceID": 0, "description": "简单·第89章", "conList": [{"cond_type": 2, "params": [2088]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 22259, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2090, "modeType": 2, "chapterID": 2000090, "order": 0, "resourceID": 0, "description": "简单·第90章", "conList": [{"cond_type": 2, "params": [2089]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 22460, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2091, "modeType": 2, "chapterID": 2000091, "order": 0, "resourceID": 0, "description": "简单·第91章", "conList": [{"cond_type": 2, "params": [2090]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 22618, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2092, "modeType": 2, "chapterID": 2000092, "order": 0, "resourceID": 0, "description": "简单·第92章", "conList": [{"cond_type": 2, "params": [2091]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 22818, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2093, "modeType": 2, "chapterID": 2000093, "order": 0, "resourceID": 0, "description": "简单·第93章", "conList": [{"cond_type": 2, "params": [2092]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 23019, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2094, "modeType": 2, "chapterID": 2000094, "order": 0, "resourceID": 0, "description": "简单·第94章", "conList": [{"cond_type": 2, "params": [2093]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 23221, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2095, "modeType": 2, "chapterID": 2000095, "order": 0, "resourceID": 0, "description": "简单·第95章", "conList": [{"cond_type": 2, "params": [2094]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 23379, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2096, "modeType": 2, "chapterID": 2000096, "order": 0, "resourceID": 0, "description": "简单·第96章", "conList": [{"cond_type": 2, "params": [2095]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 23558, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2097, "modeType": 2, "chapterID": 2000097, "order": 0, "resourceID": 0, "description": "简单·第97章", "conList": [{"cond_type": 2, "params": [2096]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 23759, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2098, "modeType": 2, "chapterID": 2000098, "order": 0, "resourceID": 0, "description": "简单·第98章", "conList": [{"cond_type": 2, "params": [2097]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 23961, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2099, "modeType": 2, "chapterID": 2000099, "order": 0, "resourceID": 0, "description": "简单·第99章", "conList": [{"cond_type": 2, "params": [2098]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 24935, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2100, "modeType": 2, "chapterID": 2000100, "order": 0, "resourceID": 0, "description": "简单·第100章", "conList": [{"cond_type": 2, "params": [2099]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 25577, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3001, "modeType": 2, "chapterID": 2001001, "order": 1, "resourceID": 6, "description": "困难·第1章", "conList": [{"cond_type": 2, "params": []}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 3510, "rogueID": 6, "LevelLimit": 20, "rogueFirst": 0, "sweepLimit": 2, "rewardID": 0, "ratingList": []}, {"modeID": 3002, "modeType": 2, "chapterID": 2001002, "order": 2, "resourceID": 7, "description": "困难·第2章", "conList": [{"cond_type": 2, "params": [3001]}, {"cond_type": 1, "params": [1]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 3564, "rogueID": 7, "LevelLimit": 20, "rogueFirst": 0, "sweepLimit": 2, "rewardID": 0, "ratingList": []}, {"modeID": 3003, "modeType": 2, "chapterID": 2001003, "order": 3, "resourceID": 8, "description": "困难·第3章", "conList": [{"cond_type": 2, "params": [3002]}, {"cond_type": 1, "params": [2]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 4628, "rogueID": 8, "LevelLimit": 20, "rogueFirst": 0, "sweepLimit": 5, "rewardID": 0, "ratingList": []}, {"modeID": 3004, "modeType": 2, "chapterID": 2001004, "order": 0, "resourceID": 0, "description": "困难·第4章", "conList": [{"cond_type": 2, "params": [3003]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 4697, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3005, "modeType": 2, "chapterID": 2001005, "order": 0, "resourceID": 0, "description": "困难·第5章", "conList": [{"cond_type": 2, "params": [3004]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 4884, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3006, "modeType": 2, "chapterID": 2001006, "order": 0, "resourceID": 0, "description": "困难·第6章", "conList": [{"cond_type": 2, "params": [3005]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 4955, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3007, "modeType": 2, "chapterID": 2001007, "order": 0, "resourceID": 0, "description": "困难·第7章", "conList": [{"cond_type": 2, "params": [3006]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 5019, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3008, "modeType": 2, "chapterID": 2001008, "order": 0, "resourceID": 0, "description": "困难·第8章", "conList": [{"cond_type": 2, "params": [3007]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 5090, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3009, "modeType": 2, "chapterID": 2001009, "order": 0, "resourceID": 0, "description": "困难·第9章", "conList": [{"cond_type": 2, "params": [3008]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 5239, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3010, "modeType": 2, "chapterID": 2001010, "order": 0, "resourceID": 0, "description": "困难·第10章", "conList": [{"cond_type": 2, "params": [3009]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 5312, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3011, "modeType": 2, "chapterID": 2001011, "order": 0, "resourceID": 0, "description": "困难·第11章", "conList": [{"cond_type": 2, "params": [3010]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 5384, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3012, "modeType": 2, "chapterID": 2001012, "order": 0, "resourceID": 0, "description": "困难·第12章", "conList": [{"cond_type": 2, "params": [3011]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 5951, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3013, "modeType": 2, "chapterID": 2001013, "order": 0, "resourceID": 0, "description": "困难·第13章", "conList": [{"cond_type": 2, "params": [3012]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 6030, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3014, "modeType": 2, "chapterID": 2001014, "order": 0, "resourceID": 0, "description": "困难·第14章", "conList": [{"cond_type": 2, "params": [3013]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 6110, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3015, "modeType": 2, "chapterID": 2001015, "order": 0, "resourceID": 0, "description": "困难·第15章", "conList": [{"cond_type": 2, "params": [3014]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 6189, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3016, "modeType": 2, "chapterID": 2001016, "order": 0, "resourceID": 0, "description": "困难·第16章", "conList": [{"cond_type": 2, "params": [3015]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 6268, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3017, "modeType": 2, "chapterID": 2001017, "order": 0, "resourceID": 0, "description": "困难·第17章", "conList": [{"cond_type": 2, "params": [3016]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 6347, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3018, "modeType": 2, "chapterID": 2001018, "order": 0, "resourceID": 0, "description": "困难·第18章", "conList": [{"cond_type": 2, "params": [3017]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 7515, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3019, "modeType": 2, "chapterID": 2001019, "order": 0, "resourceID": 0, "description": "困难·第19章", "conList": [{"cond_type": 2, "params": [3018]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 7624, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3020, "modeType": 2, "chapterID": 2001020, "order": 0, "resourceID": 0, "description": "困难·第20章", "conList": [{"cond_type": 2, "params": [3019]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 7884, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3021, "modeType": 2, "chapterID": 2001021, "order": 0, "resourceID": 0, "description": "困难·第21章", "conList": [{"cond_type": 2, "params": [3020]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 8634, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3022, "modeType": 2, "chapterID": 2001022, "order": 0, "resourceID": 0, "description": "困难·第22章", "conList": [{"cond_type": 2, "params": [3021]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 8767, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3023, "modeType": 2, "chapterID": 2001023, "order": 0, "resourceID": 0, "description": "困难·第23章", "conList": [{"cond_type": 2, "params": [3022]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 8997, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3024, "modeType": 2, "chapterID": 2001024, "order": 0, "resourceID": 0, "description": "困难·第24章", "conList": [{"cond_type": 2, "params": [3023]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 9134, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3025, "modeType": 2, "chapterID": 2001025, "order": 0, "resourceID": 0, "description": "困难·第25章", "conList": [{"cond_type": 2, "params": [3024]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 9248, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3026, "modeType": 2, "chapterID": 2001026, "order": 0, "resourceID": 0, "description": "困难·第26章", "conList": [{"cond_type": 2, "params": [3025]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 9362, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3027, "modeType": 2, "chapterID": 2001027, "order": 0, "resourceID": 0, "description": "困难·第27章", "conList": [{"cond_type": 2, "params": [3026]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 9476, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3028, "modeType": 2, "chapterID": 2001028, "order": 0, "resourceID": 0, "description": "困难·第28章", "conList": [{"cond_type": 2, "params": [3027]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 9860, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3029, "modeType": 2, "chapterID": 2001029, "order": 0, "resourceID": 0, "description": "困难·第29章", "conList": [{"cond_type": 2, "params": [3028]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 9978, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3030, "modeType": 2, "chapterID": 2001030, "order": 0, "resourceID": 0, "description": "困难·第30章", "conList": [{"cond_type": 2, "params": [3029]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 10096, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3031, "modeType": 2, "chapterID": 2001031, "order": 0, "resourceID": 0, "description": "困难·第31章", "conList": [{"cond_type": 2, "params": [3030]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 10439, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3032, "modeType": 2, "chapterID": 2001032, "order": 0, "resourceID": 0, "description": "困难·第32章", "conList": [{"cond_type": 2, "params": [3031]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 12522, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3033, "modeType": 2, "chapterID": 2001033, "order": 0, "resourceID": 0, "description": "困难·第33章", "conList": [{"cond_type": 2, "params": [3032]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 12664, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3034, "modeType": 2, "chapterID": 2001034, "order": 0, "resourceID": 0, "description": "困难·第34章", "conList": [{"cond_type": 2, "params": [3033]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 13107, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3035, "modeType": 2, "chapterID": 2001035, "order": 0, "resourceID": 0, "description": "困难·第35章", "conList": [{"cond_type": 2, "params": [3034]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 13254, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3036, "modeType": 2, "chapterID": 2001036, "order": 0, "resourceID": 0, "description": "困难·第36章", "conList": [{"cond_type": 2, "params": [3035]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 13402, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3037, "modeType": 2, "chapterID": 2001037, "order": 0, "resourceID": 0, "description": "困难·第37章", "conList": [{"cond_type": 2, "params": [3036]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 13549, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3038, "modeType": 2, "chapterID": 2001038, "order": 0, "resourceID": 0, "description": "困难·第38章", "conList": [{"cond_type": 2, "params": [3037]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 13696, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3039, "modeType": 2, "chapterID": 2001039, "order": 0, "resourceID": 0, "description": "困难·第39章", "conList": [{"cond_type": 2, "params": [3038]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 13810, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3040, "modeType": 2, "chapterID": 2001040, "order": 0, "resourceID": 0, "description": "困难·第40章", "conList": [{"cond_type": 2, "params": [3039]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 14102, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3041, "modeType": 2, "chapterID": 2001041, "order": 0, "resourceID": 0, "description": "困难·第41章", "conList": [{"cond_type": 2, "params": [3040]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 14251, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3042, "modeType": 2, "chapterID": 2001042, "order": 0, "resourceID": 0, "description": "困难·第42章", "conList": [{"cond_type": 2, "params": [3041]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 14401, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3043, "modeType": 2, "chapterID": 2001043, "order": 0, "resourceID": 0, "description": "困难·第43章", "conList": [{"cond_type": 2, "params": [3042]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 14833, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3044, "modeType": 2, "chapterID": 2001044, "order": 0, "resourceID": 0, "description": "困难·第44章", "conList": [{"cond_type": 2, "params": [3043]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 16928, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3045, "modeType": 2, "chapterID": 2001045, "order": 0, "resourceID": 0, "description": "困难·第45章", "conList": [{"cond_type": 2, "params": [3044]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 17101, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3046, "modeType": 2, "chapterID": 2001046, "order": 0, "resourceID": 0, "description": "困难·第46章", "conList": [{"cond_type": 2, "params": [3045]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 17235, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3047, "modeType": 2, "chapterID": 2001047, "order": 0, "resourceID": 0, "description": "困难·第47章", "conList": [{"cond_type": 2, "params": [3046]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 17408, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3048, "modeType": 2, "chapterID": 2001048, "order": 0, "resourceID": 0, "description": "困难·第48章", "conList": [{"cond_type": 2, "params": [3047]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 17581, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3049, "modeType": 2, "chapterID": 2001049, "order": 0, "resourceID": 0, "description": "困难·第49章", "conList": [{"cond_type": 2, "params": [3048]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 17754, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3050, "modeType": 2, "chapterID": 2001050, "order": 0, "resourceID": 0, "description": "困难·第50章", "conList": [{"cond_type": 2, "params": [3049]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 17927, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3051, "modeType": 2, "chapterID": 2001051, "order": 0, "resourceID": 0, "description": "困难·第51章", "conList": [{"cond_type": 2, "params": [3050]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 18100, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3052, "modeType": 2, "chapterID": 2001052, "order": 0, "resourceID": 0, "description": "困难·第52章", "conList": [{"cond_type": 2, "params": [3051]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 18235, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3053, "modeType": 2, "chapterID": 2001053, "order": 0, "resourceID": 0, "description": "困难·第53章", "conList": [{"cond_type": 2, "params": [3052]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 18704, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3054, "modeType": 2, "chapterID": 2001054, "order": 0, "resourceID": 0, "description": "困难·第54章", "conList": [{"cond_type": 2, "params": [3053]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 18882, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3055, "modeType": 2, "chapterID": 2001055, "order": 0, "resourceID": 0, "description": "困难·第55章", "conList": [{"cond_type": 2, "params": [3054]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 19370, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3056, "modeType": 2, "chapterID": 2001056, "order": 0, "resourceID": 0, "description": "困难·第56章", "conList": [{"cond_type": 2, "params": [3055]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 19897, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3057, "modeType": 2, "chapterID": 2001057, "order": 0, "resourceID": 0, "description": "困难·第57章", "conList": [{"cond_type": 2, "params": [3056]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 20082, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3058, "modeType": 2, "chapterID": 2001058, "order": 0, "resourceID": 0, "description": "困难·第58章", "conList": [{"cond_type": 2, "params": [3057]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 22059, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3059, "modeType": 2, "chapterID": 2001059, "order": 0, "resourceID": 0, "description": "困难·第59章", "conList": [{"cond_type": 2, "params": [3058]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 22259, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3060, "modeType": 2, "chapterID": 2001060, "order": 0, "resourceID": 0, "description": "困难·第60章", "conList": [{"cond_type": 2, "params": [3059]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 22460, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3061, "modeType": 2, "chapterID": 2001061, "order": 0, "resourceID": 0, "description": "困难·第61章", "conList": [{"cond_type": 2, "params": [3060]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 22618, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3062, "modeType": 2, "chapterID": 2001062, "order": 0, "resourceID": 0, "description": "困难·第62章", "conList": [{"cond_type": 2, "params": [3061]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 22818, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3063, "modeType": 2, "chapterID": 2001063, "order": 0, "resourceID": 0, "description": "困难·第63章", "conList": [{"cond_type": 2, "params": [3062]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 23019, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3064, "modeType": 2, "chapterID": 2001064, "order": 0, "resourceID": 0, "description": "困难·第64章", "conList": [{"cond_type": 2, "params": [3063]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 23221, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3065, "modeType": 2, "chapterID": 2001065, "order": 0, "resourceID": 0, "description": "困难·第65章", "conList": [{"cond_type": 2, "params": [3064]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 23379, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3066, "modeType": 2, "chapterID": 2001066, "order": 0, "resourceID": 0, "description": "困难·第66章", "conList": [{"cond_type": 2, "params": [3065]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 23558, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3067, "modeType": 2, "chapterID": 2001067, "order": 0, "resourceID": 0, "description": "困难·第67章", "conList": [{"cond_type": 2, "params": [3066]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 23759, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3068, "modeType": 2, "chapterID": 2001068, "order": 0, "resourceID": 0, "description": "困难·第68章", "conList": [{"cond_type": 2, "params": [3067]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 23961, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3069, "modeType": 2, "chapterID": 2001069, "order": 0, "resourceID": 0, "description": "困难·第69章", "conList": [{"cond_type": 2, "params": [3068]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 24935, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3070, "modeType": 2, "chapterID": 2001070, "order": 0, "resourceID": 0, "description": "困难·第70章", "conList": [{"cond_type": 2, "params": [3069]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 25577, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3071, "modeType": 2, "chapterID": 2001071, "order": 0, "resourceID": 0, "description": "困难·第71章", "conList": [{"cond_type": 2, "params": [3070]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 25792, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3072, "modeType": 2, "chapterID": 2001072, "order": 0, "resourceID": 0, "description": "困难·第72章", "conList": [{"cond_type": 2, "params": [3071]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 26007, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3073, "modeType": 2, "chapterID": 2001073, "order": 0, "resourceID": 0, "description": "困难·第73章", "conList": [{"cond_type": 2, "params": [3072]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 27568, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3074, "modeType": 2, "chapterID": 2001074, "order": 0, "resourceID": 0, "description": "困难·第74章", "conList": [{"cond_type": 2, "params": [3073]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 30277, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3075, "modeType": 2, "chapterID": 2001075, "order": 0, "resourceID": 0, "description": "困难·第75章", "conList": [{"cond_type": 2, "params": [3074]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 30527, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3076, "modeType": 2, "chapterID": 2001076, "order": 0, "resourceID": 0, "description": "困难·第76章", "conList": [{"cond_type": 2, "params": [3075]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 30728, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3077, "modeType": 2, "chapterID": 2001077, "order": 0, "resourceID": 0, "description": "困难·第77章", "conList": [{"cond_type": 2, "params": [3076]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 30949, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3078, "modeType": 2, "chapterID": 2001078, "order": 0, "resourceID": 0, "description": "困难·第78章", "conList": [{"cond_type": 2, "params": [3077]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 31199, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3079, "modeType": 2, "chapterID": 2001079, "order": 0, "resourceID": 0, "description": "困难·第79章", "conList": [{"cond_type": 2, "params": [3078]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 31433, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3080, "modeType": 2, "chapterID": 2001080, "order": 0, "resourceID": 0, "description": "困难·第80章", "conList": [{"cond_type": 2, "params": [3079]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 31635, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3081, "modeType": 2, "chapterID": 2001081, "order": 0, "resourceID": 0, "description": "困难·第81章", "conList": [{"cond_type": 2, "params": [3080]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 31885, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3082, "modeType": 2, "chapterID": 2001082, "order": 0, "resourceID": 0, "description": "困难·第82章", "conList": [{"cond_type": 2, "params": [3081]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 32741, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3083, "modeType": 2, "chapterID": 2001083, "order": 0, "resourceID": 0, "description": "困难·第83章", "conList": [{"cond_type": 2, "params": [3082]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 32931, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3084, "modeType": 2, "chapterID": 2001084, "order": 0, "resourceID": 0, "description": "困难·第84章", "conList": [{"cond_type": 2, "params": [3083]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 33186, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3085, "modeType": 2, "chapterID": 2001085, "order": 0, "resourceID": 0, "description": "困难·第85章", "conList": [{"cond_type": 2, "params": [3084]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 33442, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3086, "modeType": 2, "chapterID": 2001086, "order": 0, "resourceID": 0, "description": "困难·第86章", "conList": [{"cond_type": 2, "params": [3085]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 35033, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3087, "modeType": 2, "chapterID": 2001087, "order": 0, "resourceID": 0, "description": "困难·第87章", "conList": [{"cond_type": 2, "params": [3086]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 35245, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3088, "modeType": 2, "chapterID": 2001088, "order": 0, "resourceID": 0, "description": "困难·第88章", "conList": [{"cond_type": 2, "params": [3087]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 35498, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3089, "modeType": 2, "chapterID": 2001089, "order": 0, "resourceID": 0, "description": "困难·第89章", "conList": [{"cond_type": 2, "params": [3088]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 36003, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3090, "modeType": 2, "chapterID": 2001090, "order": 0, "resourceID": 0, "description": "困难·第90章", "conList": [{"cond_type": 2, "params": [3089]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 36218, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3091, "modeType": 2, "chapterID": 2001091, "order": 0, "resourceID": 0, "description": "困难·第91章", "conList": [{"cond_type": 2, "params": [3090]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 36491, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3092, "modeType": 2, "chapterID": 2001092, "order": 0, "resourceID": 0, "description": "困难·第92章", "conList": [{"cond_type": 2, "params": [3091]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 39800, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3093, "modeType": 2, "chapterID": 2001093, "order": 0, "resourceID": 0, "description": "困难·第93章", "conList": [{"cond_type": 2, "params": [3092]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 40095, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3094, "modeType": 2, "chapterID": 2001094, "order": 0, "resourceID": 0, "description": "困难·第94章", "conList": [{"cond_type": 2, "params": [3093]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 40331, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3095, "modeType": 2, "chapterID": 2001095, "order": 0, "resourceID": 0, "description": "困难·第95章", "conList": [{"cond_type": 2, "params": [3094]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 40626, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3096, "modeType": 2, "chapterID": 2001096, "order": 0, "resourceID": 0, "description": "困难·第96章", "conList": [{"cond_type": 2, "params": [3095]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 40902, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3097, "modeType": 2, "chapterID": 2001097, "order": 0, "resourceID": 0, "description": "困难·第97章", "conList": [{"cond_type": 2, "params": [3096]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 41140, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3098, "modeType": 2, "chapterID": 2001098, "order": 0, "resourceID": 0, "description": "困难·第98章", "conList": [{"cond_type": 2, "params": [3097]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 41435, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3099, "modeType": 2, "chapterID": 2001099, "order": 0, "resourceID": 0, "description": "困难·第99章", "conList": [{"cond_type": 2, "params": [3098]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 41731, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3100, "modeType": 2, "chapterID": 2001100, "order": 0, "resourceID": 0, "description": "困难·第100章", "conList": [{"cond_type": 2, "params": [3099]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 41971, "rogueID": 0, "LevelLimit": 0, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}]