{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/utils/UITools.ts"], "names": ["UIToolMgr", "formatTime", "totalSeconds", "hours", "Math", "floor", "minutes", "seconds", "pad", "num", "toString", "padStart", "modifyColorTag", "passText", "index", "options", "string", "originalString", "parts", "split", "filteredParts", "filter", "part", "length", "Error", "tagIndex", "valueIndex", "color", "value", "join", "modifyNumber", "txt", "undefined", "replacement", "match", "replace", "UITools"], "mappings": ";;;kEAEMA,S;;;;;;;;;;;;;;;AAAAA,MAAAA,S,GAAN,MAAMA,SAAN,CAAgB;AACZ;AACJ;AACA;AACA;AACA;AACIC,QAAAA,UAAU,CAACC,YAAD,EAA+B;AACrC,cAAI,OAAOA,YAAP,KAAwB,QAAxB,IAAoCA,YAAY,GAAG,CAAvD,EAA0D;AACtD,mBAAO,OAAP;AACH;;AACD,gBAAMC,KAAK,GAAGC,IAAI,CAACC,KAAL,CAAWH,YAAY,GAAG,IAA1B,CAAd;AACA,gBAAMI,OAAO,GAAGF,IAAI,CAACC,KAAL,CAAYH,YAAY,GAAG,IAAhB,GAAwB,EAAnC,CAAhB;AACA,gBAAMK,OAAO,GAAGL,YAAY,GAAG,EAA/B;;AACA,gBAAMM,GAAG,GAAIC,GAAD,IAAiBA,GAAG,CAACC,QAAJ,GAAeC,QAAf,CAAwB,CAAxB,EAA2B,GAA3B,CAA7B;;AACA,cAAIR,KAAK,KAAK,CAAd,EAAiB;AACb,mBAAQ,GAAEK,GAAG,CAACF,OAAD,CAAU,IAAGE,GAAG,CAACD,OAAD,CAAU,EAAvC;AACH;;AACD,iBAAQ,GAAEC,GAAG,CAACL,KAAD,CAAQ,IAAGK,GAAG,CAACF,OAAD,CAAU,IAAGE,GAAG,CAACD,OAAD,CAAU,EAArD;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACIK,QAAAA,cAAc,CAACC,QAAD,EAAqBC,KAArB,EAAoCC,OAApC,EAAiF;AAC3F,cAAIF,QAAQ,IAAI,IAAZ,IAAoBA,QAAQ,CAACG,MAAT,IAAmB,IAA3C,EAAiD;AACjD,gBAAMC,cAAc,GAAGJ,QAAQ,CAACG,MAAhC;AACA,gBAAME,KAAK,GAAGD,cAAc,CAACE,KAAf,CAAqB,oCAArB,CAAd;AACA,gBAAMC,aAAa,GAAGF,KAAK,CAACG,MAAN,CAAaC,IAAI,IAAIA,IAAI,KAAK,EAA9B,CAAtB;;AACA,cAAIR,KAAK,GAAG,CAAR,IAAaA,KAAK,IAAIM,aAAa,CAACG,MAAd,GAAuB,CAAjD,EAAoD;AAChD,kBAAM,IAAIC,KAAJ,CAAW,kBAAiBV,KAAM,EAAlC,CAAN;AACH;;AACD,gBAAMW,QAAQ,GAAG,IAAIX,KAArB;AACA,gBAAMY,UAAU,GAAGD,QAAQ,GAAG,CAA9B;;AACA,cAAIV,OAAO,CAACY,KAAZ,EAAmB;AACfP,YAAAA,aAAa,CAACK,QAAD,CAAb,GAA2B,UAASV,OAAO,CAACY,KAAM,GAAlD;AACH;;AACD,cAAIZ,OAAO,CAACa,KAAZ,EAAmB;AACfR,YAAAA,aAAa,CAACM,UAAD,CAAb,GAA4BX,OAAO,CAACa,KAApC;AACH;;AACDf,UAAAA,QAAQ,CAACG,MAAT,GAAkBI,aAAa,CAACS,IAAd,CAAmB,EAAnB,CAAlB;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIC,QAAAA,YAAY,CAACC,GAAD,EAAoBtB,GAApB,EAAuD;AAC/D,cAAI,CAACsB,GAAD,IAAQtB,GAAG,KAAK,IAAhB,IAAwBA,GAAG,KAAKuB,SAApC,EAA+C;AAC/C,gBAAMC,WAAW,GAAGxB,GAAG,CAACC,QAAJ,EAApB;;AACA,cAAIqB,GAAG,CAACf,MAAJ,CAAWkB,KAAX,CAAiB,MAAjB,CAAJ,EAA8B;AAC1BH,YAAAA,GAAG,CAACf,MAAJ,GAAae,GAAG,CAACf,MAAJ,CAAWmB,OAAX,CAAmB,MAAnB,EAA2BF,WAA3B,CAAb;AACH;AACJ;;AAtDW,O;;yBAwDHG,O,GAAU,IAAIpC,SAAJ,E", "sourcesContent": ["import { Label, RichText } from \"cc\";\r\n\r\nclass UIToolMgr {\r\n    /**\r\n     * 将秒数格式化为 \"时:分:秒\" 的字符串\r\n     * @param totalSeconds 总秒数（非负数）\r\n     * @returns 格式化后的时间字符串，如 \"00:00:00\"\r\n     */\r\n    formatTime(totalSeconds: number): string {\r\n        if (typeof totalSeconds !== 'number' || totalSeconds < 0) {\r\n            return '00:00';\r\n        }\r\n        const hours = Math.floor(totalSeconds / 3600);\r\n        const minutes = Math.floor((totalSeconds % 3600) / 60);\r\n        const seconds = totalSeconds % 60;\r\n        const pad = (num: number) => num.toString().padStart(2, '0');\r\n        if (hours === 0) {\r\n            return `${pad(minutes)}:${pad(seconds)}`;\r\n        }\r\n        return `${pad(hours)}:${pad(minutes)}:${pad(seconds)}`;\r\n    }\r\n    /**\r\n     * 修改富文本原字符串中指定 <color> 标签内的数值\r\n     * @param passText RichText\r\n     * @param index 要修改的 <color> 标签的索引（从 0 开始）\r\n     * @param newValue 新的数值\r\n     */\r\n    modifyColorTag(passText: RichText, index: number, options: { value?: string; color?: string }) {\r\n        if (passText == null || passText.string == null) return;\r\n        const originalString = passText.string;\r\n        const parts = originalString.split(/(<color=[^>]+>)([^<]+)(<\\/color>)/g);\r\n        const filteredParts = parts.filter(part => part !== '');\r\n        if (index < 0 || index >= filteredParts.length / 3) {\r\n            throw new Error(`Invalid index: ${index}`);\r\n        }\r\n        const tagIndex = 3 * index;\r\n        const valueIndex = tagIndex + 1;\r\n        if (options.color) {\r\n            filteredParts[tagIndex] = `<color=${options.color}>`;\r\n        }\r\n        if (options.value) {\r\n            filteredParts[valueIndex] = options.value;\r\n        }\r\n        passText.string = filteredParts.join('');\r\n    }\r\n    /**\r\n     * 修改 Label 文本中的数字部分为指定值\r\n     * @param txt 目标 Label 组件\r\n     * @param num 替换的数字（支持 number 或 string 类型）\r\n     */\r\n    modifyNumber(txt: Label | null, num: number | string | null): void {\r\n        if (!txt || num === null || num === undefined) return;\r\n        const replacement = num.toString();\r\n        if (txt.string.match(/\\d+/g)) {\r\n            txt.string = txt.string.replace(/\\d+/g, replacement);\r\n        }\r\n    }\r\n}\r\nexport const UITools = new UIToolMgr();"]}