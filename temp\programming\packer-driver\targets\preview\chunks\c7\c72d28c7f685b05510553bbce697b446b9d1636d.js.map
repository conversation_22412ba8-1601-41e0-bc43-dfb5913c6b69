{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/luban/LubanMgr.ts"], "names": ["_decorator", "assetManager", "JsonAsset", "EDITOR", "IMgr", "cfg", "BundleName", "ccclass", "property", "LubanMgr", "_table", "init", "load", "Promise", "resolve", "reject", "bundle", "bundles", "get", "<PERSON><PERSON>", "loadDir", "err", "assets", "dataMap", "Map", "asset", "set", "name", "Tables", "file", "has", "json", "console", "warn", "initInEditor", "root_dir", "pattern", "res", "Editor", "Message", "request", "arr", "Array", "isArray", "files", "map", "a", "path", "filter", "startsWith", "LoadedCount", "uuid", "filePath", "loadAny", "jsonAsset", "fileError", "length", "setTimeout", "error", "table", "LUBAN_PATH"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,Y,OAAAA,Y;AAAcC,MAAAA,S,OAAAA,S;;AAC1BC,MAAAA,M,UAAAA,M;;AACAC,MAAAA,I,iBAAAA,I;;AACGC,MAAAA,G;;AACHC,MAAAA,U,iBAAAA,U;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBR,U;;0BAEjBS,Q,WADZF,OAAO,CAAC,UAAD,C,2BAAR,MACaE,QADb;AAAA;AAAA,wBACmC;AAAA;AAAA;AAAA,eACvBC,MADuB,GACK,IADL;AAAA;;AAI/BC,QAAAA,IAAI,GAAS;AACT,gBAAMA,IAAN;AACH;;AAEDC,QAAAA,IAAI,GAAkB;AAClB,iBAAO,IAAIC,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAqB;AACpC,gBAAMC,MAAM,GAAGf,YAAY,CAACgB,OAAb,CAAqBC,GAArB,CAAyB;AAAA;AAAA,0CAAWC,KAApC,CAAf;AACAH,YAAAA,MAAM,CAAEI,OAAR,CAAgB,IAAhB,EAAsBlB,SAAtB,EAAiC,CAACmB,GAAD,EAAMC,MAAN,KAA8B;AAC3D,kBAAID,GAAJ,EAAS;AACLN,gBAAAA,MAAM,CAACM,GAAD,CAAN;AACA;AACH;;AACD,kBAAIE,OAAO,GAAG,IAAIC,GAAJ,EAAd;;AACA,mBAAK,IAAIC,KAAT,IAAkBH,MAAlB,EAA0B;AACtBC,gBAAAA,OAAO,CAACG,GAAR,CAAYD,KAAK,CAACE,IAAlB,EAAwBF,KAAxB;AACH;;AACD,mBAAKf,MAAL,GAAc,IAAIL,GAAG,CAACuB,MAAR,CAAgBC,IAAD,IAAkB;AAC3C,oBAAIN,OAAO,CAACO,GAAR,CAAYD,IAAZ,CAAJ,EAAuB;AACnB,yBAAON,OAAO,CAACL,GAAR,CAAYW,IAAZ,EAAmBE,IAA1B;AACH;;AACDC,gBAAAA,OAAO,CAACC,IAAR,qBAA+BJ,IAA/B;AACA,uBAAO,IAAP;AACH,eANa,CAAd;AAOAf,cAAAA,OAAO;AACV,aAjBD;AAkBH,WApBM,CAAP;AAqBH,SA9B8B,CAgC/B;;;AACMoB,QAAAA,YAAY,GAAkB;AAAA;;AAAA;AAChC,gBAAI,KAAI,CAACxB,MAAL,IAAe,IAAf,IAAuB,CAACP,MAA5B,EAAoC;AAChC;AACH,aAH+B,CAKhC;;;AACA,gBAAMgC,QAAQ,GAAG,4BAAjB;AACA,gBAAMC,OAAO,GAAGD,QAAQ,GAAG,WAA3B;;AAEA,gBAAI;AACA;AACA,kBAAME,GAAG,SAASC,MAAM,CAACC,OAAP,CAAeC,OAAf,CAAuB,UAAvB,EAAmC,cAAnC,EAAmD;AAAEJ,gBAAAA;AAAF,eAAnD,CAAlB;AACA,kBAAMK,GAAG,GAAGC,KAAK,CAACC,OAAN,CAAcN,GAAd,IAAqBA,GAArB,GAA4BK,KAAK,CAACC,OAAN,CAAcN,GAAG,CAAC,CAAD,CAAjB,IAAwBA,GAAG,CAAC,CAAD,CAA3B,GAAiC,EAAzE;AACA,kBAAMO,KAAK,GAAGH,GAAG,CAACI,GAAJ,CAASC,CAAD,IAAYA,CAAC,CAACC,IAAtB,EAA4BC,MAA5B,CAAoCF,CAAD,IAAYA,CAAC,CAACG,UAAF,CAAad,QAAb,CAA/C,CAAd,CAJA,CAMA;;AACA,kBAAIe,WAAW,GAAG,CAAlB;AAEA,kBAAI3B,OAAO,GAAG,IAAIC,GAAJ,EAAd,CATA,CAUA;;AAVA,oDAW4B;AACxB,oBAAI;AACA;AACA,sBAAM2B,IAAI,SAASb,MAAM,CAACC,OAAP,CAAeC,OAAf,CAAuB,UAAvB,EAAmC,YAAnC,EAAiDY,QAAQ,GAAG,OAA5D,CAAnB;;AACA,sBAAID,IAAI,IAAI,IAAZ,EAAkB;AACdnB,oBAAAA,OAAO,CAACC,IAAR,8CAAwDmB,QAAxD;AADc;AAGjB;;AACDnD,kBAAAA,YAAY,CAACoD,OAAb,CAAgCF,IAAhC,EAAsC,CAAC9B,GAAD,EAAiBiC,SAAjB,KAAyC;AAC3E,wBAAIjC,GAAJ,EAAS;AACLW,sBAAAA,OAAO,CAACC,IAAR,qCAA+CmB,QAA/C,QAA4D/B,GAA5D;AACA;AACH;;AACDE,oBAAAA,OAAO,CAACG,GAAR,CAAY4B,SAAS,CAAC3B,IAAtB,EAA4B2B,SAA5B;AACAJ,oBAAAA,WAAW;AACd,mBAPD;AAQH,iBAfD,CAeE,OAAOK,SAAP,EAAkB;AAChBvB,kBAAAA,OAAO,CAACC,IAAR,oCAA8CmB,QAA9C,QAA2DG,SAA3D;AACH;AACJ,eA9BD;;AAWA,mBAAK,IAAIH,QAAT,IAAqBR,KAArB;AAAA,4CAMY;AANZ,eAXA,CAgCA;;;AACA,qBAAOM,WAAW,GAAGN,KAAK,CAACY,MAA3B,EAAmC;AAC/B,sBAAM,IAAI3C,OAAJ,CAAYwB,GAAG,IAAIoB,UAAU,CAACpB,GAAD,EAAM,GAAN,CAA7B,CAAN,CAD+B,CACiB;AACnD;;AAED,cAAA,KAAI,CAAC3B,MAAL,GAAc,IAAIL,GAAG,CAACuB,MAAR,CAAgBC,IAAD,IAAkB;AAC3C,oBAAIN,OAAO,CAACO,GAAR,CAAYD,IAAZ,CAAJ,EAAuB;AACnB,yBAAON,OAAO,CAACL,GAAR,CAAYW,IAAZ,EAAmBE,IAA1B;AACH;;AAED,uBAAO,IAAP;AACH,eANa,CAAd;AAQH,aA7CD,CA6CE,OAAO2B,KAAP,EAAc;AACZ1B,cAAAA,OAAO,CAAC0B,KAAR,CAAc,2CAAd,EAA2DA,KAA3D;AACA,oBAAMA,KAAN;AACH;AAzD+B;AA0DnC;;AAEQ,YAALC,KAAK,GAAe;AACpB,iBAAO,KAAKjD,MAAZ;AACH;;AA/F8B,O,UAEPkD,U,GAAa,Q", "sourcesContent": ["import { _decorator, assetManager, JsonAsset } from \"cc\";\nimport { EDITOR } from 'cc/env';\nimport { IMgr } from '../../../../scripts/core/base/IMgr';\nimport * as cfg from '../autogen/luban/schema';\nimport { BundleName } from \"../const/BundleConst\";\nconst { ccclass, property } = _decorator;\n@ccclass(\"LubanMgr\")\nexport class LubanMgr extends IMgr {\n    private _table: cfg.Tables | null = null;\n    private static readonly LUBAN_PATH = 'luban/';\n\n    init(): void {\n        super.init();\n    }\n\n    load(): Promise<void> {\n        return new Promise((resolve, reject) => {\n            const bundle = assetManager.bundles.get(BundleName.Luban);\n            bundle!.loadDir(\"./\", JsonAsset, (err, assets: JsonAsset[]) => {\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                var dataMap = new Map<string, JsonAsset>();\n                for (let asset of assets) {\n                    dataMap.set(asset.name, asset);\n                }\n                this._table = new cfg.Tables((file: string) => {\n                    if (dataMap.has(file)) {\n                        return dataMap.get(file)!.json;\n                    }\n                    console.warn(`LubanMgr: File ${file} not found in loaded assets.`);\n                    return null;\n                });\n                resolve();\n            });\n        });\n    }\n\n    // 仅在编辑器环境下使用\n    async initInEditor(): Promise<void> {\n        if (this._table != null || !EDITOR) {\n            return;\n        }\n\n        // 遍历assets/bundles/luban/ 目录下的json文件\n        const root_dir = 'db://assets/bundles/luban/';\n        const pattern = root_dir + '**/*.json';\n\n        try {\n            // @ts-ignore\n            const res = await Editor.Message.request('asset-db', 'query-assets', { pattern });\n            const arr = Array.isArray(res) ? res : (Array.isArray(res[0]) ? res[0] : []);\n            const files = arr.map((a: any) => a.path).filter((a: any) => a.startsWith(root_dir));\n            \n            // console.log('LubanMgr: Found files:', files);\n            let LoadedCount = 0;\n\n            var dataMap = new Map<string, any>();\n            // Load each JSON file's content\n            for (let filePath of files) {\n                try {\n                    // @ts-ignore\n                    const uuid = await Editor.Message.request('asset-db', 'query-uuid', filePath + '.json');\n                    if (uuid == null) {\n                        console.warn(`LubanMgr: Failed to query uuid for file ${filePath}`);\n                        continue;\n                    }\n                    assetManager.loadAny<JsonAsset>(uuid, (err:Error|null, jsonAsset:JsonAsset) => {\n                        if (err) {\n                            console.warn(`LubanMgr: Failed to load asset ${filePath}:`, err);\n                            return;\n                        }\n                        dataMap.set(jsonAsset.name, jsonAsset);\n                        LoadedCount++;\n                    });\n                } catch (fileError) {\n                    console.warn(`LubanMgr: Failed to load file ${filePath}:`, fileError);\n                }\n            }\n\n            // Wait until all files are loaded\n            while (LoadedCount < files.length) {\n                await new Promise(res => setTimeout(res, 100)); // wait 100ms\n            }\n\n            this._table = new cfg.Tables((file: string) => {\n                if (dataMap.has(file)) {\n                    return dataMap.get(file)!.json;\n                }\n                \n                return null;\n            });\n\n        } catch (error) {\n            console.error(\"LubanMgr: Failed to initialize in editor:\", error);\n            throw error;\n        }\n    }\n\n    get table(): cfg.Tables {\n        return this._table!;\n    }\n}"]}