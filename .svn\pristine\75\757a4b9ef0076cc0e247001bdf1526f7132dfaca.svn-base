import { _decorator, Component, EventTouch, NodeEventType, Vec3 } from 'cc';
import { GameConst } from 'db://assets/scripts/core/base/GameConst';
import { GameIns } from '../../GameIns';
import { GameEnum } from '../../const/GameEnum';
import { type MainPlane } from '../plane/mainPlane/MainPlane';

const { ccclass, property } = _decorator;

@ccclass('Controller')
export class Controller extends Component {

    target: MainPlane | null = null; // 目标对象（主飞机）
    _targetStartPos: Vec3 = new Vec3(0, 0); // 目标起始位置

    /**
     * 加载时初始化
     */
    onLoad() {

    }

    /**
     * 开始时绑定触摸事件
     */
    start() {
        this.node.on(NodeEventType.TOUCH_START, this.onTouchStart, this);
        this.node.on(NodeEventType.TOUCH_END, this.onTouchEnd, this);
        this.node.on(NodeEventType.TOUCH_CANCEL, this.onTouchEnd, this);
        this.node.on(NodeEventType.TOUCH_MOVE, this.onTouchMove, this);
    }

    /**
     * 触摸开始事件
     * @param {Event.EventTouch} event 触摸事件
     */
    onTouchStart(event: EventTouch) {
        let target = GameIns.mainPlaneManager.mainPlane; // 获取主飞机
        if (!target) {
            return; // 如果主飞机不存在，则不处理
        }

        if (GameIns.gameStateManager.gameState == GameEnum.GameState.WillOver || GameIns.gameStateManager.gameState == GameEnum.GameState.Over) {
            return;
        }

        this._targetStartPos = target.node.getPosition(); // 记录主飞机的起始位置
        GameIns.battleManager.setTouchState(true);
    }

    /**
     * 触摸移动事件
     * @param {Event.EventTouch} event 触摸事件
     */
    onTouchMove(event: EventTouch) {
        if (GameIns.gameStateManager.gameState != GameEnum.GameState.Battle) {
            return; // 游戏未进入战斗状态时不处理
        }

        let target = GameIns.mainPlaneManager.mainPlane; // 获取主飞机
        if (!target) {
            return; // 如果主飞机不存在，则不处理
        }

        let startPos = event.getUIStartLocation(); // UI坐标系（左下角原点）
        let location = event.getUILocation();     // UI坐标系（左下角原点）

        // 直接计算偏移量（UI坐标系）
        const deltaX = location.x - startPos.x;
        const deltaY = location.y - startPos.y;

        // 计算UI坐标系到战斗坐标系的转换比例
        const uiWidth = GameConst.designWidth; // UI坐标系宽度 (750)
        const battleWidth = GameConst.ViewBattleWidth; // 战斗坐标系宽度 (950)
        const scaleFactor = battleWidth / uiWidth;

        // 将UI偏移量转换为战斗坐标系偏移量
        const battleDeltaX = deltaX * scaleFactor;
        const battleDeltaY = deltaY * scaleFactor;

        // 应用偏移量到飞机初始位置（战斗坐标系）
        const newX = this._targetStartPos.x + battleDeltaX;
        const newY = this._targetStartPos.y + battleDeltaY;

        target.onControl(newX, newY);

        // 调试日志
        //logInfo("Controller", `UI坐标: 开始(${startPos.x},${startPos.y}) 当前(${location.x},${location.y})`);
        //logInfo("Controller", `UI偏移量: (${deltaX},${deltaY}), 战斗偏移量: (${battleDeltaX},${battleDeltaY})`);
    }

    /**
     * 触摸结束事件
     * @param {Event.EventTouch} event 触摸事件
     */
    onTouchEnd(event: EventTouch) {
        if (GameIns.gameStateManager.gameState == GameEnum.GameState.WillOver || GameIns.gameStateManager.gameState == GameEnum.GameState.Over) {
            return;
        }
        GameIns.battleManager.setTouchState(false);
    }
}