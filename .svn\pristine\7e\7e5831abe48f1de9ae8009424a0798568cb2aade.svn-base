{"skeleton": {"hash": "NrRS184ylTFuB1hcFAQs+y3qEdY", "spine": "3.8.85", "x": -184.12, "y": 1.87, "width": 368, "height": 306, "images": "./images/", "audio": "D:/Temporary/09.08/GQ/飞机"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 225.34, "scaleX": 0.34, "scaleY": 0.34}, {"name": "dan2", "parent": "bone", "y": 177.29}, {"name": "dan8", "parent": "dan2", "x": -104.46, "y": -7.02}, {"name": "luoxuan2", "parent": "dan8", "x": -1.35, "y": 79.99}, {"name": "dao1", "parent": "dan2", "x": 106.34, "y": -6.07}, {"name": "luoxuan1", "parent": "dao1", "x": -1.49, "y": 80.42}, {"name": "dan3", "parent": "dan2", "length": 41.49, "rotation": 93.77, "x": -29.34, "y": -16.83}, {"name": "dan4", "parent": "dan2", "length": 42.77, "rotation": 90, "x": 23.9, "y": -11.82, "color": "abe323ff"}, {"name": "dan5", "parent": "dan2", "length": 59.1, "rotation": 178.42, "x": -99.53, "y": 1.73}, {"name": "dan6", "parent": "dan2", "length": 59.09, "rotation": 1.05, "x": 96.68, "y": 0.64}, {"name": "dan9", "parent": "dan5", "rotation": -178.42, "x": 67.58, "y": 1.7}, {"name": "dan10", "parent": "dan6", "rotation": -1.05, "x": 71.04, "y": -0.44}], "slots": [{"name": "jiwei", "bone": "dan2", "attachment": "jiwei"}, {"name": "dan4", "bone": "dan9", "attachment": "dan4"}, {"name": "dan3", "bone": "dan10", "attachment": "dan3"}, {"name": "dan2", "bone": "dan8", "attachment": "dan2"}, {"name": "dao1", "bone": "dao1", "attachment": "dao1"}, {"name": "luoxuan2", "bone": "luoxuan2", "attachment": "luoxuan2"}, {"name": "luoxuan1", "bone": "luoxuan1", "attachment": "luoxuan1"}, {"name": "jishen", "bone": "dan2", "attachment": "jishen"}], "transform": [{"name": "1", "bones": ["dan3"], "target": "dan4", "rotation": 3.77, "x": -5, "y": 53.23, "rotateMix": 0, "translateMix": -1, "scaleMix": 0, "shearMix": 0}, {"name": "2", "order": 1, "bones": ["dan10"], "target": "dan4", "rotation": -90, "x": 13.33, "y": -143.82, "shearY": 360, "rotateMix": 0, "translateMix": -0.03, "scaleMix": 0, "shearMix": 0}, {"name": "3", "order": 2, "bones": ["dan9"], "target": "dan4", "rotation": -90, "x": 13.71, "y": 191.03, "shearY": 360, "rotateMix": 0, "translateMix": -0.03, "scaleMix": 0, "shearMix": 0}, {"name": "4", "order": 3, "bones": ["dao1"], "target": "dan4", "rotation": -90, "x": 5.76, "y": -82.44, "shearY": 360, "rotateMix": 0, "translateMix": -0.02, "scaleMix": 0, "shearMix": 0}, {"name": "5", "order": 4, "bones": ["dan8"], "target": "dan4", "rotation": -90, "x": 4.8, "y": 128.36, "shearY": 360, "rotateMix": 0, "translateMix": -0.02, "scaleMix": 0, "shearMix": 0}], "skins": [{"name": "default", "attachments": {"dan2": {"dan2": {"type": "mesh", "uvs": [0.46945, 0, 0.59968, 0, 0.70914, 0, 0.78105, 0.06653, 0.87351, 0.12764, 0.96939, 0.22146, 1, 0.37781, 1, 0.58664, 0.96254, 0.80696, 0.85639, 0.91214, 0.71942, 0.99884, 0.61308, 0.99935, 0.4763, 1, 0.35987, 0.91214, 0.25714, 0.80411, 0.19893, 0.61223, 0.18866, 0.37912, 0.25714, 0.20287, 0.37014, 0.10906, 0.45233, 0.0522, 0.61127, 0.86393, 0.60766, 0.59544, 0.60446, 0.3568, 0.60191, 0.16647, 0.60046, 0.05854, 0.42151, 0.16328, 0.39069, 0.37505, 0.40439, 0.61236, 0.45575, 0.8809, 0.78105, 0.16187, 0.82557, 0.3722, 0.82557, 0.60524, 0.78448, 0.8809], "triangles": [24, 2, 3, 29, 3, 4, 19, 0, 24, 17, 18, 25, 24, 18, 19, 29, 4, 5, 30, 5, 6, 16, 17, 26, 30, 6, 7, 27, 15, 16, 14, 15, 27, 8, 31, 7, 13, 14, 28, 9, 32, 8, 10, 32, 9, 12, 13, 28, 24, 1, 2, 3, 23, 24, 18, 23, 25, 0, 1, 24, 29, 23, 3, 23, 18, 24, 22, 23, 29, 30, 22, 29, 5, 30, 29, 25, 22, 26, 17, 25, 26, 22, 25, 23, 21, 22, 30, 31, 21, 30, 26, 22, 21, 7, 31, 30, 26, 27, 16, 21, 27, 26, 20, 21, 31, 32, 20, 31, 27, 21, 20, 28, 14, 27, 8, 32, 31, 20, 28, 27, 32, 11, 20, 10, 11, 32, 28, 20, 11, 11, 12, 28], "vertices": [1, 3, -8.67, 80.81, 1, 1, 3, -0.08, 80.68, 1, 1, 3, 7.15, 80.58, 1, 1, 3, 11.9, 70, 1, 1, 3, 18, 60.28, 1, 1, 3, 24.33, 45.37, 1, 1, 3, 26.35, 20.51, 1, 1, 3, 26.35, -12.7, 1, 1, 3, 23.87, -47.73, 1, 1, 3, 16.87, -64.45, 1, 1, 3, 7.83, -78.24, 1, 1, 3, 0.81, -78.32, 1, 1, 3, -8.22, -78.42, 1, 1, 3, -15.9, -64.45, 1, 1, 3, -22.68, -47.27, 1, 1, 3, -26.52, -16.76, 1, 1, 3, -27.2, 20.3, 1, 1, 3, -22.68, 48.32, 1, 1, 3, -15.22, 63.24, 1, 1, 3, -9.8, 72.28, 1, 1, 3, 0.69, -56.77, 1, 1, 3, 0.45, -14.05, 1, 1, 3, 0.24, 23.92, 1, 1, 3, 0.07, 54.2, 1, 1, 3, -0.02, 71.37, 1, 1, 3, -11.83, 54.65, 1, 1, 3, -13.87, 20.98, 1, 1, 3, -12.96, -16.76, 1, 1, 3, -9.57, -59.48, 1, 1, 3, 11.9, 54.88, 1, 1, 3, 14.83, 21.43, 1, 1, 3, 14.83, -15.63, 1, 1, 3, 12.12, -59.48, 1], "hull": 20, "edges": [4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 0, 38, 0, 2, 2, 4, 20, 22, 22, 24, 40, 22, 42, 40, 44, 42, 46, 44, 2, 48, 48, 46, 50, 52, 52, 54, 54, 56, 58, 60, 60, 62, 62, 64], "width": 66, "height": 159}}, "dan3": {"dan3": {"type": "mesh", "uvs": [0, 0.08192, 0.19269, 0, 0.55349, 0, 0.83229, 0, 1, 0.11, 1, 0.92108, 0.85689, 1, 0.56169, 1, 0.20909, 1, 0.06149, 1, 0, 0.87117], "triangles": [5, 6, 4, 6, 3, 4, 3, 6, 7, 8, 1, 2, 2, 7, 8, 0, 1, 10, 10, 8, 9, 3, 7, 2, 10, 1, 8], "vertices": [1, 12, -18.83, 36.52, 1, 1, 12, -12.09, 44.06, 1, 1, 12, 0.54, 44.06, 1, 1, 12, 10.3, 44.06, 1, 1, 12, 16.17, 33.94, 1, 1, 12, 16.17, -40.68, 1, 1, 12, 11.16, -47.94, 1, 1, 12, 0.83, -47.94, 1, 1, 12, -11.52, -47.94, 1, 1, 12, -16.68, -47.94, 1, 1, 12, -18.83, -36.09, 1], "hull": 11, "edges": [0, 2, 6, 8, 8, 10, 10, 12, 18, 20, 0, 20, 16, 18, 2, 16, 6, 12, 2, 4, 4, 6, 12, 14, 14, 16, 4, 14], "width": 35, "height": 92}}, "dan4": {"dan4": {"type": "mesh", "uvs": [0.01363, 0.21673, 0.01608, 0.09336, 0.17772, 0.00811, 0.46404, 0.00413, 0.76103, 0, 1, 0.06861, 1, 0.20571, 1, 0.93211, 0.76103, 1, 0.44478, 1, 0.08636, 1, 0, 0.90146, 0.44478, 0.86296, 0.45925, 0.16475, 0.76796, 0.8769, 0.79566, 0.18473, 0.18754, 0.18462, 0.1706, 0.88456], "triangles": [13, 2, 3, 16, 1, 2, 13, 16, 2, 15, 4, 5, 13, 3, 4, 15, 13, 4, 15, 5, 6, 0, 1, 16, 12, 16, 13, 12, 13, 15, 14, 12, 15, 17, 0, 16, 6, 14, 15, 7, 14, 6, 16, 12, 17, 17, 11, 0, 10, 11, 17, 9, 12, 14, 17, 12, 9, 10, 17, 9, 8, 9, 14, 8, 14, 7], "vertices": [1, 11, -16.5, 23.73, 1, 1, 11, -16.41, 35.08, 1, 1, 11, -10.59, 42.92, 1, 1, 11, -0.28, 43.29, 1, 1, 11, 10.41, 43.67, 1, 1, 11, 19.01, 37.36, 1, 1, 11, 19.01, 24.74, 1, 1, 11, 19.01, -42.09, 1, 1, 11, 10.41, -48.33, 1, 1, 11, -0.98, -48.33, 1, 1, 11, -13.88, -48.33, 1, 1, 11, -16.99, -39.27, 1, 1, 11, -0.98, -35.72, 1, 1, 11, -0.46, 28.51, 1, 1, 11, 10.66, -37.01, 1, 1, 11, 11.65, 26.67, 1, 1, 11, -10.24, 26.68, 1, 1, 11, -10.85, -37.71, 1], "hull": 12, "edges": [2, 4, 8, 10, 14, 16, 20, 22, 4, 6, 6, 8, 16, 18, 18, 20, 24, 18, 2, 0, 0, 22, 6, 26, 26, 24, 10, 12, 12, 14, 14, 28, 28, 24, 12, 30, 30, 26, 28, 30, 0, 32, 32, 26, 22, 34, 34, 24, 32, 34], "width": 36, "height": 92}}, "dao1": {"dao1": {"type": "mesh", "uvs": [0.28213, 0, 0.40686, 0, 0.51822, 0, 0.7454, 0.17233, 0.81668, 0.36093, 0.77213, 0.71595, 0.62959, 0.9027, 0.48704, 1, 0.38013, 1, 0.25986, 1, 0.0995, 0.89531, 0, 0.69376, 0, 0.30731, 0.09059, 0.16308, 0.2465, 0.16123, 0.17968, 0.30731, 0.18413, 0.69376, 0.26432, 0.87127, 0.61177, 0.15939, 0.62959, 0.31471, 0.58504, 0.70116, 0.52713, 0.86757, 0.40237, 0.16813, 0.38814, 0.70047], "triangles": [9, 17, 8, 7, 8, 21, 10, 16, 17, 21, 20, 5, 4, 20, 19, 11, 15, 16, 19, 18, 3, 15, 13, 14, 2, 18, 22, 14, 0, 1, 22, 1, 2, 6, 7, 21, 9, 10, 17, 6, 21, 5, 10, 11, 16, 5, 20, 4, 11, 12, 15, 19, 3, 4, 12, 13, 15, 14, 13, 0, 3, 18, 2, 21, 8, 23, 8, 17, 23, 17, 16, 23, 21, 23, 20, 19, 20, 23, 22, 19, 23, 23, 16, 15, 23, 15, 22, 22, 18, 19, 15, 14, 22, 1, 22, 14], "vertices": [1, 5, -9.83, 79.62, 1, 1, 5, -1.6, 79.62, 1, 1, 5, 5.75, 79.62, 1, 1, 5, 20.74, 52.22, 1, 1, 5, 25.45, 22.24, 1, 1, 5, 22.51, -34.21, 1, 1, 5, 13.1, -63.91, 1, 1, 5, 3.69, -79.38, 1, 1, 5, -3.36, -79.38, 1, 1, 5, -11.3, -79.38, 1, 1, 5, -21.89, -62.73, 1, 1, 5, -28.45, -30.68, 1, 1, 5, -28.45, 30.76, 1, 1, 5, -22.47, 53.69, 1, 1, 5, -12.18, 53.99, 1, 1, 5, -16.59, 30.76, 1, 1, 5, -16.3, -30.68, 1, 1, 5, -11.01, -58.91, 1, 1, 5, 11.92, 54.28, 1, 1, 5, 13.1, 29.59, 1, 1, 5, 10.16, -31.86, 1, 1, 5, 6.34, -58.32, 1, 1, 5, -1.9, 52.89, 1, 1, 5, -2.83, -31.75, 1], "hull": 14, "edges": [4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26, 0, 2, 2, 4, 14, 16, 16, 18, 28, 30, 30, 32, 32, 34, 36, 38, 38, 40, 40, 42, 2, 44, 16, 46, 46, 44], "width": 66, "height": 159}}, "jishen": {"jishen": {"type": "mesh", "uvs": [0.00388, 0.32003, 0.17266, 0.28954, 0.33223, 0.24943, 0.37903, 0.13788, 0.4381, 0.04083, 0.48107, 0, 0.50101, 0, 0.53017, 0, 0.5762, 0.05207, 0.63297, 0.15797, 0.66064, 0.25988, 0.85238, 0.29275, 1, 0.32163, 1, 0.54627, 0.82783, 0.58799, 0.65292, 0.6265, 0.60228, 0.64736, 0.58387, 0.76289, 0.64985, 0.79017, 0.7281, 0.81905, 0.72657, 0.90249, 0.65445, 0.92656, 0.57159, 0.9394, 0.54286, 0.97007, 0.51482, 1, 0.49747, 1, 0.48107, 1, 0.46044, 0.97915, 0.42429, 0.9426, 0.34604, 0.92656, 0.2816, 0.9073, 0.27699, 0.82868, 0.41049, 0.75808, 0.40128, 0.65539, 0.34911, 0.6265, 0.15578, 0.58639, 0, 0.54467, 0.33727, 0.36195, 0.43893, 0.30175, 0.49996, 0.29714, 0.56248, 0.29944, 0.66385, 0.34738, 0.34663, 0.57116, 0.39898, 0.58578, 0.49975, 0.61623, 0.59833, 0.58984, 0.65785, 0.56901, 0.44465, 0.12081, 0.55712, 0.12461, 0.39228, 0.32937, 0.43377, 0.59775, 0.55181, 0.604, 0.62201, 0.32604], "triangles": [14, 11, 13, 11, 12, 13, 35, 36, 1, 1, 36, 0, 24, 25, 23, 44, 23, 25, 25, 26, 27, 44, 51, 23, 22, 23, 17, 51, 16, 17, 28, 29, 32, 22, 18, 21, 22, 17, 18, 28, 32, 27, 20, 21, 19, 32, 29, 31, 29, 30, 31, 19, 21, 18, 44, 25, 27, 23, 51, 17, 44, 27, 50, 33, 50, 32, 32, 50, 27, 34, 43, 33, 33, 43, 50, 51, 45, 16, 16, 45, 15, 15, 46, 14, 15, 45, 46, 35, 42, 34, 34, 42, 43, 51, 44, 39, 40, 45, 51, 50, 39, 44, 46, 45, 41, 52, 45, 40, 46, 41, 14, 14, 41, 11, 35, 37, 42, 35, 1, 37, 40, 51, 39, 39, 50, 38, 49, 38, 50, 43, 37, 49, 49, 50, 43, 43, 42, 37, 41, 45, 52, 1, 2, 37, 37, 2, 49, 52, 10, 41, 41, 10, 11, 49, 2, 38, 52, 40, 10, 2, 3, 38, 38, 47, 39, 38, 3, 47, 10, 40, 9, 9, 40, 48, 40, 39, 48, 48, 39, 6, 48, 8, 9, 6, 39, 47, 6, 4, 5, 48, 6, 8, 3, 4, 47, 8, 6, 7, 6, 47, 4], "vertices": [2, 9, 60.56, -29.27, 0.97, 7, 57.92, 126.38, 0.03, 3, 2, -105.25, 41.98, 0.4752, 9, 6.83, -40.08, 0.5148, 8, 53.8, 129.15, 0.01, 1, 2, -54.18, 54.25, 1, 2, 2, -39.21, 88.39, 0.99, 7, 105.64, 2.93, 0.01, 2, 2, -20.3, 118.08, 0.99, 7, 134.02, -17.89, 0.01, 2, 2, -6.56, 130.58, 0.99, 8, 142.4, 30.46, 0.01, 2, 2, -0.17, 130.58, 0.99, 8, 142.4, 24.07, 0.01, 2, 2, 9.16, 130.58, 0.99, 8, 142.4, 14.74, 0.01, 2, 2, 23.89, 114.65, 0.99, 7, 127.69, -61.76, 0.01, 2, 2, 42.05, 82.24, 0.99, 7, 94.16, -77.75, 0.01, 1, 2, 50.91, 51.06, 1, 3, 2, 112.27, 41, 0.53856, 10, 16.32, 40.06, 0.45144, 8, 52.82, -88.37, 0.01, 2, 10, 63.39, 30.36, 0.97, 7, 36.46, -191.65, 0.03, 2, 10, 62.13, -38.37, 0.97, 7, -32.13, -187.13, 0.03, 3, 2, 104.41, -49.35, 0.53856, 10, 6.81, -50.12, 0.45144, 8, -37.52, -80.51, 0.01, 2, 2, 48.44, -61.13, 0.98, 8, -49.31, -24.54, 0.02, 2, 2, 32.23, -67.51, 0.98, 8, -55.69, -8.33, 0.02, 2, 2, 26.34, -102.87, 0.9798, 8, -91.04, -2.44, 0.0202, 2, 2, 47.45, -111.21, 0.98, 8, -99.39, -23.56, 0.02, 2, 2, 72.49, -120.05, 0.98, 7, -109.7, -94.82, 0.02, 2, 2, 72, -145.58, 0.98, 7, -135.14, -92.65, 0.02, 2, 2, 48.93, -152.95, 0.99, 8, -141.13, -25.03, 0.01, 2, 2, 22.41, -156.88, 0.98, 8, -145.05, 1.49, 0.02, 2, 2, 13.22, -166.26, 0.97, 8, -154.44, 10.68, 0.03, 2, 2, 4.25, -175.42, 0.97, 8, -163.6, 19.65, 0.03, 2, 2, -1.31, -175.42, 0.97, 8, -163.6, 25.2, 0.03, 2, 2, -6.56, -175.42, 0.97, 8, -163.6, 30.45, 0.03, 2, 2, -13.16, -169.04, 0.97, 8, -157.22, 37.05, 0.03, 2, 2, -24.72, -157.86, 0.98, 8, -146.04, 48.62, 0.02, 2, 2, -49.76, -152.95, 0.99, 8, -141.13, 73.66, 0.01, 2, 2, -70.39, -147.06, 0.98, 7, -127.25, 49.53, 0.02, 2, 2, -71.86, -123, 0.98, 7, -103.14, 49.42, 0.02, 2, 2, -29.14, -101.39, 0.9798, 8, -89.57, 53.04, 0.0202, 2, 2, -32.09, -69.97, 0.98, 8, -58.15, 55.99, 0.02, 2, 2, -48.78, -61.13, 0.98, 8, -49.31, 72.68, 0.02, 3, 2, -110.65, -48.86, 0.4752, 9, 9.73, 50.87, 0.5148, 8, -37.03, 134.55, 0.01, 2, 9, 59.91, 39.48, 0.97, 7, -10.59, 132.14, 0.03, 2, 2, -52.57, 19.82, 0.98, 8, 31.64, 76.47, 0.02, 2, 2, -20.04, 38.24, 0.96, 8, 50.07, 43.94, 0.04, 2, 2, -0.51, 39.65, 0.94, 8, 51.48, 24.41, 0.06, 2, 2, 19.49, 38.95, 0.96, 8, 50.77, 4.4, 0.04, 2, 2, 51.93, 24.28, 0.98, 8, 36.1, -28.04, 0.02, 2, 2, -49.57, -44.2, 0.98, 8, -32.37, 73.47, 0.02, 2, 2, -32.83, -48.67, 0.97, 8, -36.85, 56.72, 0.03, 2, 2, -0.58, -57.99, 0.94, 8, -46.16, 24.48, 0.06, 2, 2, 30.97, -49.91, 0.97, 8, -38.09, -7.07, 0.03, 2, 2, 50.01, -43.54, 0.98, 8, -31.72, -26.12, 0.02, 2, 2, -18.21, 93.61, 0.96, 8, 105.43, 42.11, 0.04, 2, 2, 17.78, 92.45, 0.96, 8, 104.27, 6.12, 0.04, 2, 2, -34.97, 29.79, 0.97, 8, 41.61, 58.87, 0.03, 2, 2, -21.69, -52.33, 0.96, 8, -40.51, 45.59, 0.04, 2, 2, 16.08, -54.25, 0.96, 8, -42.42, 7.82, 0.04, 2, 2, 38.54, 30.81, 0.97, 8, 42.63, -14.65, 0.03], "hull": 37, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 0, 72, 2, 70, 48, 50, 50, 52, 22, 28, 4, 74, 12, 78, 76, 78, 78, 80, 20, 82, 68, 84, 84, 74, 84, 86, 50, 88, 88, 78, 30, 92, 92, 82, 90, 92, 76, 94, 96, 80, 34, 44, 64, 56, 74, 98, 98, 76, 98, 86, 86, 100, 100, 88, 76, 100, 88, 102, 102, 90, 80, 102, 80, 104, 104, 82, 90, 104, 52, 54, 54, 56, 100, 54, 44, 46, 46, 48, 102, 46], "width": 320, "height": 306}}, "jiwei": {"jiwei": {"type": "mesh", "uvs": [0.01214, 0.07961, 0.2088, 0.04858, 0.51669, 0, 0.81914, 0.06132, 0.99999, 0.09799, 1, 0.69243, 0.63619, 1, 0.49678, 1, 0.33745, 1, 0.01214, 0.71081, 0.50388, 0.6433, 0.20823, 0.68389, 0.81098, 0.67371], "triangles": [6, 12, 5, 12, 6, 10, 6, 7, 10, 7, 8, 10, 9, 11, 8, 8, 11, 10, 9, 0, 11, 12, 4, 5, 11, 1, 10, 11, 0, 1, 12, 3, 4, 12, 10, 3, 10, 2, 3, 10, 1, 2], "vertices": [2, 2, -17.68, -138.55, 0.99, 7, -122.22, -3.62, 0.01, 2, 2, -10.6, -137.34, 0.98, 8, -125.51, 34.5, 0.02, 2, 2, 0.48, -135.44, 0.98, 8, -123.62, 23.41, 0.02, 2, 2, 11.37, -137.83, 0.98, 8, -126.01, 12.53, 0.02, 2, 2, 17.88, -139.26, 0.99, 7, -125.28, -39.06, 0.01, 2, 2, 17.88, -162.45, 0.99, 7, -148.41, -37.54, 0.01, 1, 2, 4.79, -174.44, 1, 2, 2, -0.23, -174.44, 0.99, 8, -162.62, 24.13, 0.01, 1, 2, -5.97, -174.44, 1, 2, 2, -17.68, -163.16, 0.99, 7, -146.79, -2, 0.01, 2, 2, 0.02, -160.53, 0.99, 8, -148.71, 23.88, 0.01, 2, 2, -10.62, -162.11, 0.99, 8, -150.29, 34.52, 0.01, 2, 2, 11.08, -161.72, 0.99, 8, -149.89, 12.82, 0.01], "hull": 10, "edges": [8, 10, 10, 12, 16, 18, 0, 18, 12, 14, 14, 16, 4, 20, 20, 14, 0, 2, 2, 4, 18, 22, 22, 20, 2, 22, 4, 6, 6, 8, 10, 24, 24, 20, 6, 24], "width": 36, "height": 39}}, "luoxuan1": {"luoxuan1": {"x": 0.04, "y": 0.7, "width": 82, "height": 17}}, "luoxuan2": {"luoxuan2": {"x": 1.2, "y": 2.09, "width": 81, "height": 17}}}}], "animations": {"Dodge": {"bones": {"dan4": {"translate": [{"x": -18.14, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -266.35, "y": 124.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 268.54, "y": -89.64, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -18.14}]}, "dan8": {"translate": [{"y": 3.99, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -5.24, "y": 4.16}, {"time": 0.4333, "x": 1.75, "y": 4.36}, {"time": 0.6667, "y": 3.99}]}, "dao1": {"translate": [{"y": 3.99, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -5.24, "y": 4.16}, {"time": 0.4333, "x": 1.75, "y": 4.36}, {"time": 0.6667, "y": 3.99}]}, "dan2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 1.92, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -1.82, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}], "translate": [{}, {"time": 0.2, "x": -22.72}, {"time": 0.5, "x": 8.74}, {"time": 0.6667}], "scale": [{}, {"time": 0.1667, "y": 0.943}, {"time": 0.4, "y": 1.059}, {"time": 0.6667}]}}}, "Entry": {"bones": {"luoxuan1": {"scale": [{"x": -0.064, "curve": "stepped"}, {"time": 1, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -0.064}]}, "luoxuan2": {"scale": [{"x": 0.107, "curve": "stepped"}, {"time": 1, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 0.107}]}, "bone": {"translate": [{"y": -308.73, "curve": 0, "c2": 0.74, "c3": 0.321}, {"time": 0.5, "y": 8.84, "curve": 0.25, "c3": 0.75}, {"time": 0.6333}]}, "dan2": {"translate": [{"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 1.18, "y": 1.77, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "y": 2, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "y": -5.35, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "dan8": {"translate": [{"time": 0.5667}, {"time": 0.6667, "y": -9.4}, {"time": 0.7333}], "scale": [{"x": 0.972, "y": 0.346, "curve": "stepped"}, {"time": 0.5667, "x": 0.972, "y": 0.346}, {"time": 0.6667}]}, "dao1": {"translate": [{"time": 0.5667, "curve": 0.272, "c2": 0.1, "c3": 0.753}, {"time": 0.6667, "y": -9.59, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}], "scale": [{"x": 0.972, "y": 0.346, "curve": "stepped"}, {"time": 0.5667, "x": 0.972, "y": 0.346}, {"time": 0.6667}]}, "dan5": {"translate": [{"x": 11.42, "y": -0.09, "curve": "stepped"}, {"time": 1, "x": 11.42, "y": -0.09}, {"time": 1.1}]}, "dan6": {"translate": [{"x": -10.86, "y": -0.1, "curve": "stepped"}, {"time": 1, "x": -10.86, "y": -0.1}, {"time": 1.1}]}, "dan9": {"translate": [{"x": -50.55, "y": -1.39, "curve": "stepped"}, {"time": 1.0667, "x": -50.55, "y": -1.39}, {"time": 1.1333, "x": 2, "y": 0.06, "curve": 0.014, "c2": 0.26, "c3": 0.75}, {"time": 1.2667}], "scale": [{"y": 0.825, "curve": "stepped"}, {"time": 1.0667, "y": 0.825}, {"time": 1.1333}]}, "dan10": {"translate": [{"x": -50.95, "y": 0.93, "curve": "stepped"}, {"time": 1.0667, "x": -50.95, "y": 0.93}, {"time": 1.1333, "x": 2, "y": -0.04, "curve": 0.019, "c2": 0.24, "c3": 0.75}, {"time": 1.2667}], "scale": [{"y": 0.825, "curve": "stepped"}, {"time": 1.0667, "y": 0.825}, {"time": 1.1333}]}}}, "Hurt": {"slots": {"dan2": {"color": [{"time": 0.1, "color": "ffffffff"}, {"time": 0.1333, "color": "ff0000ff", "curve": "stepped"}, {"time": 0.2, "color": "ff0000ff"}, {"time": 0.2667, "color": "ffffffff"}]}, "dan3": {"color": [{"time": 0.1, "color": "ffffffff"}, {"time": 0.1333, "color": "ff0000ff", "curve": "stepped"}, {"time": 0.2, "color": "ff0000ff"}, {"time": 0.2667, "color": "ffffffff"}]}, "dan4": {"color": [{"time": 0.1, "color": "ffffffff"}, {"time": 0.1333, "color": "ff0000ff", "curve": "stepped"}, {"time": 0.2, "color": "ff0000ff"}, {"time": 0.2667, "color": "ffffffff"}]}, "dao1": {"color": [{"time": 0.1, "color": "ffffffff"}, {"time": 0.1333, "color": "ff0000ff", "curve": "stepped"}, {"time": 0.2, "color": "ff0000ff"}, {"time": 0.2667, "color": "ffffffff"}]}, "jishen": {"color": [{"time": 0.1, "color": "ffffffff"}, {"time": 0.1333, "color": "ff0000ff", "curve": "stepped"}, {"time": 0.2, "color": "ff0000ff"}, {"time": 0.2667, "color": "ffffffff"}]}, "jiwei": {"color": [{"time": 0.1, "color": "ffffffff"}, {"time": 0.1333, "color": "ff0000ff", "curve": "stepped"}, {"time": 0.2, "color": "ff0000ff"}, {"time": 0.2667, "color": "ffffffff"}]}, "luoxuan1": {"color": [{"time": 0.1, "color": "ffffffff"}, {"time": 0.1333, "color": "ff0000ff"}]}, "luoxuan2": {"color": [{"time": 0.1, "color": "ffffffff"}, {"time": 0.1333, "color": "ff0000ff"}]}}, "bones": {"luoxuan2": {"scale": [{"x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.107}]}, "luoxuan1": {"scale": [{"x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -0.064}]}, "dan4": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": 94.85, "y": 7.49, "curve": 0, "c2": 0.6, "c3": 0.317}, {"time": 0.2667, "x": -200.93, "y": -138.53, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 109.82, "y": -76.13, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "dan8": {"translate": [{"y": 3.99, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 1.75}, {"time": 0.2667, "x": -1.75, "y": 3.59}, {"time": 0.5333, "y": 3.99}]}, "dao1": {"translate": [{"y": 3.99, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 1.75}, {"time": 0.2667, "x": -1.75, "y": 3.59}, {"time": 0.5333, "y": 3.99}]}, "dan2": {"translate": [{}, {"time": 0.0667, "x": 14.98, "y": 9.98}, {"time": 0.1333, "x": -7.49, "y": -7.49}, {"time": 0.2667, "x": 4.99, "y": 4.99}, {"time": 0.3333}]}}}, "Idle": {"bones": {"dan2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": 15.2, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "luoxuan2": {"scale": [{"x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.107}]}, "luoxuan1": {"scale": [{"x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -0.064}]}, "dan4": {"translate": [{"x": -18.14, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "x": -55.28, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": 66.68, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "x": -18.14}]}, "dan8": {"translate": [{"y": 3.99}]}, "dao1": {"translate": [{"y": 3.99}]}}}, "MoveLeft": {"bones": {"dan4": {"translate": [{"curve": 0, "c2": 0.76, "c3": 0.174}, {"time": 0.1667, "x": 261.49, "curve": "stepped"}, {"time": 0.5, "x": 261.49}, {"time": 0.6667}]}, "luoxuan2": {"scale": [{"x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.107}]}, "luoxuan1": {"scale": [{"x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -0.064}]}, "dao1": {"translate": [{}, {"time": 0.1, "x": -4.83}, {"time": 0.3333, "x": 1.25, "curve": 0.381, "c2": 0.54, "c3": 0.743}, {"time": 0.6667}]}, "dan8": {"translate": [{}, {"time": 0.1, "x": -4.83}, {"time": 0.3333, "x": 1.25, "curve": 0.381, "c2": 0.54, "c3": 0.743}, {"time": 0.6667}]}, "dan2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 27.46, "y": -11.23, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1, "y": -11.23, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}}}, "MoveRight": {"bones": {"luoxuan2": {"scale": [{"x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.107}]}, "luoxuan1": {"scale": [{"x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -0.064}]}, "dan4": {"translate": [{}, {"time": 0.1667, "x": -272.58, "curve": "stepped"}, {"time": 0.5, "x": -272.58}, {"time": 0.6667}]}, "dan2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -37.44, "y": -11.23, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1, "y": -11.23, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "dan8": {"translate": [{}, {"time": 0.1, "x": 4.8}, {"time": 0.3333, "x": -1.34, "curve": 0.812, "c3": 0.75}, {"time": 0.6667}]}, "dao1": {"translate": [{}, {"time": 0.1, "x": 4.8}, {"time": 0.3333, "x": -1.34, "curve": 0.812, "c3": 0.75}, {"time": 0.6667}]}}}, "Super": {"bones": {"dan2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "y": -37.94}, {"time": 0.2667, "y": 102.82}, {"time": 0.3333, "y": 88.13}, {"time": 0.4, "y": 96.7}, {"time": 0.4667, "y": 88.13}, {"time": 0.5333, "y": 96.7}, {"time": 0.6, "y": 88.13}, {"time": 0.6667, "y": 96.7}, {"time": 0.7333, "y": 88.13}, {"time": 0.8, "y": 96.7}, {"time": 0.8667, "y": 88.13}, {"time": 0.9333, "y": 96.7}, {"time": 1, "y": 88.13}, {"time": 1.0667, "y": 96.7}, {"time": 1.1333, "y": 88.13}, {"time": 1.2, "y": 96.7}, {"time": 1.3333}], "scale": [{"time": 0.1333}, {"time": 0.2667, "x": 1.185, "y": 1.185, "curve": "stepped"}, {"time": 1.2, "x": 1.185, "y": 1.185}, {"time": 1.3333}]}, "luoxuan2": {"scale": [{"x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "x": 0.107, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.107}]}, "luoxuan1": {"scale": [{"x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "x": -0.064, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -0.064}]}, "dan4": {"translate": [{"x": -18.14, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "x": -55.28, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": 66.68, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "x": -18.14}]}, "dan5": {"scale": [{"time": 0.1667}, {"time": 0.3333, "x": 1.315, "y": 1.315, "curve": "stepped"}, {"time": 1.1667, "x": 1.315, "y": 1.315}, {"time": 1.3333}]}, "dan6": {"scale": [{"time": 0.1667}, {"time": 0.3333, "x": 1.315, "y": 1.315, "curve": "stepped"}, {"time": 1.1667, "x": 1.315, "y": 1.315}, {"time": 1.3333}]}, "dan10": {"translate": [{}, {"time": 0.0667, "x": -9.79, "y": 0.18, "curve": "stepped"}, {"time": 0.3, "x": -9.79, "y": 0.18}, {"time": 0.4}], "scale": [{}, {"time": 0.1333, "x": 0.813, "y": 0.896, "curve": "stepped"}, {"time": 0.2667, "x": 0.813, "y": 0.896}, {"time": 0.3333, "x": 1.134, "y": 1.134, "curve": "stepped"}, {"time": 1.1667, "x": 1.134, "y": 1.134}, {"time": 1.3333}]}, "dan9": {"translate": [{}, {"time": 0.0667, "x": -8.56, "y": -0.24, "curve": "stepped"}, {"time": 0.3, "x": -8.56, "y": -0.24}, {"time": 0.4}], "scale": [{}, {"time": 0.1333, "x": 0.813, "y": 0.896, "curve": "stepped"}, {"time": 0.2667, "x": 0.813, "y": 0.896}, {"time": 0.3333, "x": 1.134, "y": 1.134, "curve": "stepped"}, {"time": 1.1667, "x": 1.134, "y": 1.134}, {"time": 1.3333}]}, "dan8": {"translate": [{"y": 3.99, "curve": 0.335, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 0.1333, "y": -18.04, "curve": 0.335, "c2": 0.34, "c3": 0.668, "c4": 0.67}, {"time": 0.3333, "y": 18.45, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.4333, "y": 3.99}], "scale": [{"curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.2, "x": 0.877, "y": 0.877, "curve": 0.321, "c2": 0.29, "c3": 0.656, "c4": 0.63}, {"time": 0.3333, "x": 1.17, "y": 1.17, "curve": "stepped"}, {"time": 1.1667, "x": 1.17, "y": 1.17, "curve": 0.343, "c2": 0.37, "c3": 0.757}, {"time": 1.3333}]}, "dao1": {"translate": [{"y": 3.99, "curve": 0.335, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 0.1333, "y": -18.04, "curve": 0.335, "c2": 0.34, "c3": 0.668, "c4": 0.67}, {"time": 0.2, "y": 3.99, "curve": 0.336, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.3333, "y": 18.45, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 0.4333, "y": 3.99}], "scale": [{"curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.2, "x": 0.877, "y": 0.877, "curve": 0.321, "c2": 0.29, "c3": 0.656, "c4": 0.63}, {"time": 0.3333, "x": 1.17, "y": 1.17, "curve": "stepped"}, {"time": 1.1667, "x": 1.17, "y": 1.17, "curve": 0.343, "c2": 0.37, "c3": 0.757}, {"time": 1.3333}]}}}}}