import { WXLogin } from "./WXLogin";
import { DevLogin } from "./DevLogin";
import { WECHAT } from "cc/env";
import { LoginInfo } from "../network/NetMgr";


export class PlatformSDKUserInfo {
    name : string = "";
    icon : string = "";
}

export interface IPlatformSDK {
    login:(cb:(err:string|null, req:LoginInfo)=>void)=>void
    getUserInfo:(cb:(err:string, userInfo:PlatformSDKUserInfo|null, hasTap:boolean)=>void, param:any)=>void
    showUserInfoButton:()=>void
    hideUserInfoButton:()=>void
    getStatusBarHeight:()=>number
}

export function CreateLoginSDK()
{
    if (WECHAT)
    {
        return new WXLogin();
    }
    else
    {
        return new DevLogin();
    }
}
