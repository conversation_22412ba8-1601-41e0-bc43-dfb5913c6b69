import { _decorator, Label, v3 } from 'cc';

import { BaseUI, UILayer, UIMgr } from "db://assets/scripts/core/base/UIMgr";
import { logDebug } from '../../../../scripts/utils/Logger';
import { BundleName } from '../../../common/script/const/BundleConst';
import { DragButton } from '../../../common/script/ui/common/components/button/DragButton';
import { GmUI } from './GmUI';
const { ccclass, property } = _decorator;

@ccclass('GmButtonUI')
export class GmButtonUI extends BaseUI {
    public static getUrl(): string { return "prefab/ui/GmButtonUI"; }
    public static getLayer(): UILayer { return UILayer.Top }
    public static getBundleName(): string { return BundleName.Gm }

    protected start(): void {
        this.node.position = v3(315, 12, 0)
        this.getComponent(DragButton)!.addClick(this.onClick, this)
    }

    async onClick() {
        logDebug("GmButtonUI onClick", "aaaaaa")
        await UIMgr.openUI(GmUI)
        await UIMgr.hideUI(GmButtonUI)
    }

    async onShow(extraText?: string): Promise<void> {
        if (extraText) {
            this.getComponentInChildren(Label)!.string = "GM" + "\n(" + extraText + ")"
        } else {
            this.getComponentInChildren(Label)!.string = "GM"
        }
    }

    async onHide(...args: any[]): Promise<void> {
    }

    async onClose(...args: any[]): Promise<void> {
    }
}