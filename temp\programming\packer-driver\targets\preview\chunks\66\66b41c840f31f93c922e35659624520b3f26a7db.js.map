{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/plane/PlaneManager.ts"], "names": ["PlaneManager", "instantiate", "NodePool", "Prefab", "Plane", "IMgr", "MyApp", "BundleName", "_planePreFab", "_planePoor", "load", "plane", "resMgr", "loadAsync", "Common", "initPlanePreFab", "prefab", "Error", "getPlane", "planeData", "planeNode", "size", "node", "get", "planeComponent", "getComponent", "initPlane", "recyclePlane", "active", "setPosition", "setSiblingIndex", "setScale", "put"], "mappings": ";;;8IAOaA,Y;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAPJC,MAAAA,W,OAAAA,W;AAAmBC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,M,OAAAA,M;;AAE7BC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;;;8BAEIP,Y,GAAN,MAAMA,YAAN;AAAA;AAAA,wBAAgC;AAAA;AAAA;AAAA,eACnCQ,YADmC,GACL,IADK;AAAA,eAEnCC,UAFmC,GAEZ,IAAIP,QAAJ,EAFY;AAAA;;AAI7BQ,QAAAA,IAAI,GAAE;AAAA;;AAAA;AACR,gBAAIC,KAAK,SAAS;AAAA;AAAA,gCAAMC,MAAN,CAAaC,SAAb,CAAuB;AAAA;AAAA,0CAAWC,MAAlC,EAA0C,cAA1C,EAA0DX,MAA1D,CAAlB;;AACA,YAAA,KAAI,CAACY,eAAL,CAAqBJ,KAArB;AAFQ;AAGX;;AAEDI,QAAAA,eAAe,CAACC,MAAD,EAAiB;AAC5B,cAAI,CAACA,MAAL,EAAa;AACT,kBAAM,IAAIC,KAAJ,CAAU,8CAAV,CAAN;AACH;;AACD,eAAKT,YAAL,GAAoBQ,MAApB;AACH;;AAEDE,QAAAA,QAAQ,CAACC,SAAD,EAAuB;AAC3B,cAAIC,SAAJ;;AACA,cAAI,KAAKX,UAAL,CAAgBY,IAAhB,KAAyB,CAA7B,EAAgC;AAC5B,gBAAMC,IAAI,GAAG,KAAKb,UAAL,CAAgBc,GAAhB,EAAb;;AACA,gBAAI,CAACD,IAAL,EAAW;AACP,oBAAM,IAAIL,KAAJ,CAAU,wDAAV,CAAN;AACH;;AACDG,YAAAA,SAAS,GAAGE,IAAZ;AACH,WAND,MAMO;AACH,gBAAI,CAAC,KAAKd,YAAV,EAAwB;AACpB,oBAAM,IAAIS,KAAJ,CAAU,8DAAV,CAAN;AACH;;AACDG,YAAAA,SAAS,GAAGnB,WAAW,CAAC,KAAKO,YAAN,CAAvB;AACH;;AAED,cAAMgB,cAAc,GAAGJ,SAAS,CAACK,YAAV;AAAA;AAAA,6BAAvB;AACAD,UAAAA,cAAc,CAAEE,SAAhB,CAA0BP,SAA1B;AAEA,iBAAOC,SAAP;AACH,SAnCkC,CAqCnC;;;AACAO,QAAAA,YAAY,CAACP,SAAD,EAAkB;AAC1BA,UAAAA,SAAS,CAACQ,MAAV,GAAmB,IAAnB;AACAR,UAAAA,SAAS,CAACS,WAAV,CAAsB,CAAtB,EAAyB,CAAzB;AACAT,UAAAA,SAAS,CAACU,eAAV,CAA0B,CAA1B;AACAV,UAAAA,SAAS,CAACW,QAAV,CAAmB,CAAnB,EAAsB,CAAtB;;AACA,eAAKtB,UAAL,CAAgBuB,GAAhB,CAAoBZ,SAApB;AACH;;AA5CkC,O", "sourcesContent": ["import { instantiate, Node, NodePool, Prefab } from \"cc\";\r\nimport { PlaneData } from \"db://assets/bundles/common/script/data/plane/PlaneData\";\r\nimport { Plane } from \"db://assets/bundles/common/script/ui/Plane\";\r\nimport { IMgr } from \"db://assets/scripts/core/base/IMgr\";\r\nimport { MyApp } from \"../app/MyApp\";\r\nimport { BundleName } from \"../const/BundleConst\";\r\n\r\nexport class PlaneManager extends IMgr {\r\n    _planePreFab: Prefab | null = null;\r\n    _planePoor: NodePool = new NodePool();\r\n\r\n    async load(){\r\n        let plane = await MyApp.resMgr.loadAsync(BundleName.Common, \"prefab/Plane\", Prefab)\r\n        this.initPlanePreFab(plane)\r\n    }\r\n\r\n    initPlanePreFab(prefab: Prefab) {\r\n        if (!prefab) {\r\n            throw new Error(\"Invalid prefab: prefab is null or undefined.\");\r\n        }\r\n        this._planePreFab = prefab;\r\n    }\r\n\r\n    getPlane(planeData: PlaneData) {\r\n        let planeNode: Node;\r\n        if (this._planePoor.size() > 0) {\r\n            const node = this._planePoor.get();\r\n            if (!node) {\r\n                throw new Error(\"NodePool returned null despite having available nodes.\");\r\n            }\r\n            planeNode = node;\r\n        } else {\r\n            if (!this._planePreFab) {\r\n                throw new Error(\"Plane prefab is not initialized. Call initPlanePreFab first.\");\r\n            }\r\n            planeNode = instantiate(this._planePreFab);\r\n        }\r\n\r\n        const planeComponent = planeNode.getComponent(Plane);\r\n        planeComponent!.initPlane(planeData);\r\n\r\n        return planeNode;\r\n    }\r\n\r\n    //回收英雄，不直接调用，调用英雄的remove方法\r\n    recyclePlane(planeNode: Node) {\r\n        planeNode.active = true;\r\n        planeNode.setPosition(0, 0);\r\n        planeNode.setSiblingIndex(0);\r\n        planeNode.setScale(1, 1);\r\n        this._planePoor.put(planeNode);\r\n    }\r\n}"]}