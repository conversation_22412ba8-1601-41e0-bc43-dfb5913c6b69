System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, log, PathData, PathPoint, _dec, _class, _crd, ccclass, property, PathSystemTest;

  function _reportPossibleCrUseOfPathData(extras) {
    _reporterNs.report("PathData", "../data/PathData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathPoint(extras) {
    _reporterNs.report("PathPoint", "../data/PathData", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      log = _cc.log;
    }, function (_unresolved_2) {
      PathData = _unresolved_2.PathData;
      PathPoint = _unresolved_2.PathPoint;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "5963cFWwU5G/qBNKse04xbD", "PathSystemTest", undefined);

      __checkObsolete__(['_decorator', 'Component', 'log']);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * 路径系统测试类 - 验证新的getSubdividedPoints方法
       */

      _export("PathSystemTest", PathSystemTest = (_dec = ccclass('PathSystemTest'), _dec(_class = class PathSystemTest extends Component {
        onLoad() {
          this.testRecursiveDepth();
          this.testCurveGeneration();
          this.testSubdividedPoints();
          this.testAdaptiveSubdivision();
          this.testSpeedSampling();
        }
        /**
         * 测试递归深度是否正常工作
         */


        testRecursiveDepth() {
          log("=== 测试递归深度修复 ===");
          var pathData = new (_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
            error: Error()
          }), PathData) : PathData)(); // 创建一个需要大量细分的急转弯

          var point1 = new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(0, 0);
          point1.speed = 300;
          point1.smoothness = 1.0; // 最高平滑度

          var point2 = new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(50, 0);
          point2.speed = 300;
          point2.smoothness = 1.0;
          var point3 = new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(50, 50);
          point3.speed = 300;
          point3.smoothness = 1.0;
          var point4 = new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(0, 50);
          point4.speed = 300;
          point4.smoothness = 1.0;
          pathData.points.length = 0;
          pathData.points.push(point1, point2, point3, point4);
          var subdivided = pathData.getSubdividedPoints();
          log("\u6025\u8F6C\u5F2F\u8DEF\u5F84\u7EC6\u5206\u70B9\u6570\u91CF: " + subdivided.length);

          if (subdivided.length > 4) {
            log("✓ 递归细分正常工作，产生了额外的细分点"); // 检查细分点是否形成平滑曲线

            var hasSmoothing = false;

            for (var i = 1; i < subdivided.length - 1; i++) {
              var prev = subdivided[i - 1];
              var curr = subdivided[i];
              var next = subdivided[i + 1]; // 检查是否有曲线特征（不在直线上）

              var linearX = prev.x + (next.x - prev.x) * 0.5;
              var linearY = prev.y + (next.y - prev.y) * 0.5;
              var deviation = Math.sqrt(Math.pow(curr.x - linearX, 2) + Math.pow(curr.y - linearY, 2));

              if (deviation > 0.5) {
                hasSmoothing = true;
                break;
              }
            }

            if (hasSmoothing) {
              log("✓ 检测到曲线平滑效果");
            } else {
              log("⚠ 细分点可能仍然在直线上");
            }
          } else {
            log("⚠ 递归细分可能仍有问题，细分点数量过少");
          }
        }
        /**
         * 测试曲线生成是否正常
         */


        testCurveGeneration() {
          log("=== 测试曲线生成 ===");
          var pathData = new (_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
            error: Error()
          }), PathData) : PathData)(); // 创建一个简单的弯曲路径

          var point1 = new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(0, 0);
          point1.speed = 300;
          point1.smoothness = 1.0; // 高平滑度

          var point2 = new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(100, 0);
          point2.speed = 400;
          point2.smoothness = 1.0;
          var point3 = new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(100, 100);
          point3.speed = 300;
          point3.smoothness = 1.0;
          pathData.points.length = 0;
          pathData.points.push(point1, point2, point3);
          var subdivided = pathData.getSubdividedPoints();
          log("\u66F2\u7EBF\u8DEF\u5F84\u70B9\u6570\u91CF: " + subdivided.length); // 检查是否真的是曲线（中间点不应该在直线上）

          if (subdivided.length >= 3) {
            var start = subdivided[0];
            var mid = subdivided[Math.floor(subdivided.length / 2)];
            var end = subdivided[subdivided.length - 1];
            log("\u8D77\u70B9: (" + start.x.toFixed(1) + ", " + start.y.toFixed(1) + ")");
            log("\u4E2D\u70B9: (" + mid.x.toFixed(1) + ", " + mid.y.toFixed(1) + ")");
            log("\u7EC8\u70B9: (" + end.x.toFixed(1) + ", " + end.y.toFixed(1) + ")"); // 检查中点是否偏离直线

            var linearMidX = (start.x + end.x) / 2;
            var linearMidY = (start.y + end.y) / 2;
            var deviation = Math.sqrt(Math.pow(mid.x - linearMidX, 2) + Math.pow(mid.y - linearMidY, 2));
            log("\u4E2D\u70B9\u504F\u79BB\u76F4\u7EBF\u8DDD\u79BB: " + deviation.toFixed(2));

            if (deviation > 1) {
              log("✓ 曲线生成正常");
            } else {
              log("⚠ 曲线可能退化为直线");
            }
          }
        }
        /**
         * 测试新的细分点方法
         */


        testSubdividedPoints() {
          log("=== 测试getSubdividedPoints方法 ===");
          var pathData = new (_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
            error: Error()
          }), PathData) : PathData)(); // 创建具有不同速度的路径点

          var point1 = new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(0, 0);
          point1.speed = 200;
          point1.smoothness = 1.0;
          var point2 = new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(100, 0);
          point2.speed = 600;
          point2.smoothness = 1.0;
          var point3 = new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(200, 100);
          point3.speed = 300;
          point3.smoothness = 0.5;
          pathData.points.length = 0;
          pathData.points.push(point1, point2, point3); // 获取细分点

          var subdivided = pathData.getSubdividedPoints();
          log("\u539F\u59CB\u8DEF\u5F84\u70B9\u6570\u91CF: " + pathData.points.length);
          log("\u7EC6\u5206\u540E\u70B9\u6570\u91CF: " + subdivided.length); // 检查前几个细分点的信息

          for (var i = 0; i < Math.min(5, subdivided.length); i++) {
            var point = subdivided[i];
            log("\u7EC6\u5206\u70B9 " + i + ": \u4F4D\u7F6E=(" + point.x.toFixed(1) + ", " + point.y.toFixed(1) + "), \u901F\u5EA6=" + point.speed.toFixed(1) + ", \u5E73\u6ED1\u5EA6=" + point.smoothness.toFixed(2));
          }
        }
        /**
         * 测试自适应细分算法的效果
         */


        testAdaptiveSubdivision() {
          log("=== 测试自适应细分算法效果 ==="); // 测试案例1：水平直线（高平滑度但低曲率）

          log("案例1: 水平直线 (高平滑度，低曲率)");
          var pathData1 = new (_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
            error: Error()
          }), PathData) : PathData)();
          var point1a = new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(0, 0);
          point1a.smoothness = 1.0; // 高平滑度

          var point1b = new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(200, 0);
          point1b.smoothness = 1.0; // 高平滑度

          pathData1.points.length = 0;
          pathData1.points.push(point1a, point1b);
          var subdivided1 = pathData1.getSubdividedPoints();
          log("  \u539F\u59CB\u70B9: 2, \u7EC6\u5206\u540E: " + subdivided1.length + ", \u500D\u7387: " + (subdivided1.length / 2).toFixed(2)); // 测试案例2：急转弯（高平滑度且高曲率）

          log("案例2: 急转弯 (高平滑度，高曲率)");
          var pathData2 = new (_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
            error: Error()
          }), PathData) : PathData)();
          var point2a = new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(0, 0);
          point2a.smoothness = 1.0;
          var point2b = new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(100, 0);
          point2b.smoothness = 1.0;
          var point2c = new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(100, 100);
          point2c.smoothness = 1.0;
          pathData2.points.length = 0;
          pathData2.points.push(point2a, point2b, point2c);
          var subdivided2 = pathData2.getSubdividedPoints();
          log("  \u539F\u59CB\u70B9: 3, \u7EC6\u5206\u540E: " + subdivided2.length + ", \u500D\u7387: " + (subdivided2.length / 3).toFixed(2)); // 测试案例3：低平滑度路径

          log("案例3: 低平滑度路径");
          var pathData3 = new (_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
            error: Error()
          }), PathData) : PathData)();
          var point3a = new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(0, 0);
          point3a.smoothness = 0.2; // 低平滑度

          var point3b = new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(100, 50);
          point3b.smoothness = 0.2;
          var point3c = new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(200, 0);
          point3c.smoothness = 0.2;
          pathData3.points.length = 0;
          pathData3.points.push(point3a, point3b, point3c);
          var subdivided3 = pathData3.getSubdividedPoints();
          log("  \u539F\u59CB\u70B9: 3, \u7EC6\u5206\u540E: " + subdivided3.length + ", \u500D\u7387: " + (subdivided3.length / 3).toFixed(2)); // 测试案例4：混合平滑度

          log("案例4: 混合平滑度路径");
          var pathData4 = new (_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
            error: Error()
          }), PathData) : PathData)();
          var point4a = new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(0, 0);
          point4a.smoothness = 0.0; // 直线

          var point4b = new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(100, 0);
          point4b.smoothness = 1.0; // 高平滑度

          var point4c = new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(200, 100);
          point4c.smoothness = 1.0;
          pathData4.points.length = 0;
          pathData4.points.push(point4a, point4b, point4c);
          var subdivided4 = pathData4.getSubdividedPoints();
          log("  \u539F\u59CB\u70B9: 3, \u7EC6\u5206\u540E: " + subdivided4.length + ", \u500D\u7387: " + (subdivided4.length / 3).toFixed(2));
          log("✓ 自适应细分算法能够根据实际曲率智能调整细分密度");
        }
        /**
         * 测试速度采样的准确性
         */


        testSpeedSampling() {
          log("=== 测试速度采样准确性 ===");
          var pathData = new (_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
            error: Error()
          }), PathData) : PathData)(); // 创建速度变化明显的路径

          var point1 = new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(0, 0);
          point1.speed = 100;
          var point2 = new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(150, 0);
          point2.speed = 500;
          var point3 = new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(300, 0);
          point3.speed = 200;
          pathData.points = [point1, point2, point3];
          var subdivided = pathData.getSubdividedPoints(); // 检查速度插值的合理性

          log("速度变化检查:");
          var checkIndices = [0, Math.floor(subdivided.length * 0.25), Math.floor(subdivided.length * 0.5), Math.floor(subdivided.length * 0.75), subdivided.length - 1];

          for (var index of checkIndices) {
            if (index < subdivided.length) {
              var point = subdivided[index];
              var progress = (index / (subdivided.length - 1) * 100).toFixed(1);
              log("  \u8FDB\u5EA6" + progress + "%: \u4F4D\u7F6E=(" + point.x.toFixed(1) + ", " + point.y.toFixed(1) + "), \u901F\u5EA6=" + point.speed.toFixed(1));
            }
          } // 验证速度插值的连续性


          var speedJumps = 0;

          for (var i = 1; i < subdivided.length; i++) {
            var speedDiff = Math.abs(subdivided[i].speed - subdivided[i - 1].speed);

            if (speedDiff > 50) {
              // 如果速度跳跃超过50，认为是异常
              speedJumps++;
            }
          }

          log("\u68C0\u6D4B\u5230\u7684\u901F\u5EA6\u8DF3\u8DC3\u6B21\u6570: " + speedJumps);

          if (speedJumps === 0) {
            log("✓ 速度插值连续性良好");
          } else {
            log("⚠ 速度插值存在跳跃，可能需要调整");
          }
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=1325fbb8ea19efa9a5fb7e41405d4580e9f22b62.js.map