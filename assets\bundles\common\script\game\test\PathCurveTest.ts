import { _decorator, Component, log } from 'cc';
import { PathData, PathPoint } from '../data/PathData';

const { ccclass, property } = _decorator;

/**
 * 专门测试路径曲线生成的组件
 */
@ccclass('PathCurveTest')
export class PathCurveTest extends Component {

    onLoad() {
        this.testCurveVsLine();
    }

    /**
     * 测试曲线与直线的区别
     */
    private testCurveVsLine() {
        log("=== 测试曲线与直线的区别 ===");
        
        // 创建一个L形路径
        const pathData = new PathData();
        
        const point1 = new PathPoint(0, 0);
        point1.speed = 300;
        point1.smoothness = 1.0; // 高平滑度，应该产生曲线
        
        const point2 = new PathPoint(100, 0);
        point2.speed = 300;
        point2.smoothness = 1.0;
        
        const point3 = new PathPoint(100, 100);
        point3.speed = 300;
        point3.smoothness = 1.0;
        
        pathData.points.length = 0;
        pathData.points.push(point1, point2, point3);
        
        const subdivided = pathData.getSubdividedPoints();
        log(`总细分点数: ${subdivided.length}`);
        
        // 分析第一段（从point1到point2）
        let segment1Points = [];
        for (let i = 0; i < subdivided.length; i++) {
            const point = subdivided[i];
            if (point.x <= 100 && point.y <= 10) { // 第一段的点
                segment1Points.push(point);
            }
        }
        
        log(`第一段点数: ${segment1Points.length}`);
        
        // 分析第二段（从point2到point3）
        let segment2Points = [];
        for (let i = 0; i < subdivided.length; i++) {
            const point = subdivided[i];
            if (point.x >= 90 && point.y >= 0) { // 第二段的点
                segment2Points.push(point);
            }
        }
        
        log(`第二段点数: ${segment2Points.length}`);
        
        // 检查转角处是否有平滑过渡
        const cornerPoints = subdivided.filter(p => 
            p.x > 80 && p.x < 120 && p.y > -20 && p.y < 20
        );
        
        log(`转角区域点数: ${cornerPoints.length}`);
        
        if (cornerPoints.length > 2) {
            log("转角区域的点:");
            cornerPoints.forEach((p, i) => {
                log(`  点${i}: (${p.x.toFixed(2)}, ${p.y.toFixed(2)})`);
            });
            
            // 检查是否有平滑过渡（Y坐标应该有变化）
            const hasYVariation = cornerPoints.some(p => Math.abs(p.y) > 0.1);
            if (hasYVariation) {
                log("✓ 检测到曲线平滑过渡");
            } else {
                log("⚠ 转角处可能仍然是直线");
            }
        }
        
        // 输出前几个点的详细信息
        log("前10个点的详细信息:");
        for (let i = 0; i < Math.min(10, subdivided.length); i++) {
            const p = subdivided[i];
            log(`  点${i}: (${p.x.toFixed(2)}, ${p.y.toFixed(2)}) 速度:${p.speed.toFixed(1)}`);
        }
    }
}
