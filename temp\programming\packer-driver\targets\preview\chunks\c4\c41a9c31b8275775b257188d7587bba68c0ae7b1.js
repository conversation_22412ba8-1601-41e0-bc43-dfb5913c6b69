System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Node, assetManager, instantiate, Vec2, UITransform, v2, LayerSplicingMode, LayerType, LevelDataEvent, LevelEditorUtils, LevelScrollLayerUI, LevelEditorEventUI, LevelPrefabParse, _dec, _dec2, _class, _crd, ccclass, property, executeInEditMode, TerrainsNodeName, ScrollsNodeName, DynamicNodeName, EmittiersNodeName, EventNodeName, LevelEditorLayerUI;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _reportPossibleCrUseOfLayerSplicingMode(extras) {
    _reporterNs.report("LayerSplicingMode", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLayerType(extras) {
    _reporterNs.report("LayerType", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEvent(extras) {
    _reporterNs.report("LevelDataEvent", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataLayer(extras) {
    _reporterNs.report("LevelDataLayer", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelEditorUtils(extras) {
    _reporterNs.report("LevelEditorUtils", "./utils", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelLayer(extras) {
    _reporterNs.report("LevelLayer", "./utils", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelScrollLayerUI(extras) {
    _reporterNs.report("LevelScrollLayerUI", "./utils", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelEditorEventUI(extras) {
    _reporterNs.report("LevelEditorEventUI", "./LevelEditorEventUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelPrefabParse(extras) {
    _reporterNs.report("LevelPrefabParse", "./LevelPrefabParse", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Node = _cc.Node;
      assetManager = _cc.assetManager;
      instantiate = _cc.instantiate;
      Vec2 = _cc.Vec2;
      UITransform = _cc.UITransform;
      v2 = _cc.v2;
    }, function (_unresolved_2) {
      LayerSplicingMode = _unresolved_2.LayerSplicingMode;
      LayerType = _unresolved_2.LayerType;
      LevelDataEvent = _unresolved_2.LevelDataEvent;
    }, function (_unresolved_3) {
      LevelEditorUtils = _unresolved_3.LevelEditorUtils;
      LevelScrollLayerUI = _unresolved_3.LevelScrollLayerUI;
    }, function (_unresolved_4) {
      LevelEditorEventUI = _unresolved_4.LevelEditorEventUI;
    }, function (_unresolved_5) {
      LevelPrefabParse = _unresolved_5.LevelPrefabParse;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "92d5epe+XRMm6NUwiCSJBKR", "LevelEditorLayerUI", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'Prefab', 'assetManager', 'instantiate', 'Vec2', 'UITransform', 'v2']);

      ({
        ccclass,
        property,
        executeInEditMode
      } = _decorator);
      TerrainsNodeName = "terrains";
      ScrollsNodeName = "scrolls";
      DynamicNodeName = "dynamic";
      EmittiersNodeName = "emittiers";
      EventNodeName = "events";

      _export("LevelEditorLayerUI", LevelEditorLayerUI = (_dec = ccclass('LevelEditorLayerUI'), _dec2 = executeInEditMode(), _dec(_class = _dec2(_class = class LevelEditorLayerUI extends Component {
        constructor() {
          super(...arguments);
          this.terrainsNode = null;
          this.scrollsNode = null;
          this.dynamicNode = null;
          this.emittiersNode = null;
          this.eventsNode = null;
          this._loadScrollNode = false;
        }

        onLoad() {
          this.terrainsNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
            error: Error()
          }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(this.node, TerrainsNodeName);
          this.scrollsNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
            error: Error()
          }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(this.node, ScrollsNodeName);
          this.dynamicNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
            error: Error()
          }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(this.node, DynamicNodeName);
          this.emittiersNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
            error: Error()
          }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(this.node, EmittiersNodeName);
          this.eventsNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
            error: Error()
          }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(this.node, EventNodeName);
        }

        initByLevelData(data) {
          var _data$terrains, _data$dynamics, _data$events;

          console.log("LevelEditorLayerUI initByLevelData");

          if (!data) {
            return;
          }

          if (this.terrainsNode === null) {
            return;
          }

          (_data$terrains = data.terrains) == null || _data$terrains.forEach(terrain => {
            assetManager.loadAny({
              uuid: terrain.uuid
            }, (err, prefab) => {
              if (err) {
                console.error("LevelEditorLayerUI initByLevelData load terrain prefab err", err);
                return;
              }

              var terrainNode = instantiate(prefab);
              terrainNode.setPosition(terrain.position.x, terrain.position.y, 0);
              terrainNode.setScale(terrain.scale.x, terrain.scale.y, 1);
              terrainNode.setRotationFromEuler(0, 0, terrain.rotation);
              this.terrainsNode.addChild(terrainNode);
            });
          });
          (_data$dynamics = data.dynamics) == null || _data$dynamics.forEach((dynamics, index) => {
            var dynaNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
              error: Error()
            }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(this.dynamicNode, "dyna_" + index);
            dynamics.group.forEach((dynamic, dynaIndex) => {
              dynaNode.setPosition(dynamic.position.x, dynamic.position.y, 0);
              dynaNode.setScale(dynamic.scale.x, dynamic.scale.y, 1);
              dynaNode.setRotationFromEuler(0, 0, dynamic.rotation);
              dynamic.terrains.forEach((terrain, terrainIndex) => {
                assetManager.loadAny({
                  uuid: terrain.uuid
                }, (err, prefab) => {
                  if (err) {
                    console.error("LevelEditorLayerUI initByLevelData load dynamic prefab err", err);
                    return;
                  }

                  var dynamicNode = instantiate(prefab);
                  dynamicNode.name = "rand_" + dynaIndex + "_" + terrainIndex;
                  dynaNode.addChild(dynamicNode);
                  var randomOffsetX = Math.random() * (terrain.offSetX.max - terrain.offSetX.min) + terrain.offSetX.min;
                  var randomOffsetY = Math.random() * (terrain.offSetY.max - terrain.offSetY.min) + terrain.offSetY.min;
                  dynamicNode.setPosition(randomOffsetX, randomOffsetY, 0);
                });
              });
            });
          });
          (_data$events = data.events) == null || _data$events.forEach(event => {
            var node = new Node();
            var eventUIComp = node.addComponent(_crd && LevelEditorEventUI === void 0 ? (_reportPossibleCrUseOfLevelEditorEventUI({
              error: Error()
            }), LevelEditorEventUI) : LevelEditorEventUI);
            eventUIComp.initByLevelData(event);
            this.eventsNode.addChild(node);
          });
        }

        initEmittierLevelData(layerData, data) {
          var _this = this;

          return _asyncToGenerator(function* () {
            var _data$emittiers;

            if (!data || _this.emittiersNode === null) {
              return;
            }

            var loadPromises = [];
            layerData.emittierLayers = [];
            (_data$emittiers = data.emittiers) == null || _data$emittiers.forEach((emittier, index) => {
              var loadPromise = new Promise(resolve => {
                assetManager.loadAny({
                  uuid: emittier.uuid
                }, (err, prefab) => {
                  if (err) {
                    resolve();
                    return;
                  }

                  layerData.emittierLayers.push(prefab);
                  var emittierNode = instantiate(prefab);
                  emittierNode.name = "emittier_" + index;
                  emittierNode.setPosition(emittier.position.x, emittier.position.y, 0);
                  emittierNode.setScale(emittier.scale.x, emittier.scale.y, 1);
                  emittierNode.setRotationFromEuler(0, 0, emittier.rotation);

                  _this.emittiersNode.addChild(emittierNode);

                  resolve();
                });
              });
              loadPromises.push(loadPromise);
            });
            yield Promise.all(loadPromises);
          })();
        }

        initScorllsByLevelData(layerData, data) {
          var _this2 = this;

          return _asyncToGenerator(function* () {
            var _data$scrolls;

            if (!data || _this2.scrollsNode === null || _this2._loadScrollNode) {
              return;
            }

            var loadPromises = [];
            layerData.scrollLayers = [];
            _this2._loadScrollNode = true;
            (_data$scrolls = data.scrolls) == null || _data$scrolls.forEach(scroll => {
              var scrollLayers = new (_crd && LevelScrollLayerUI === void 0 ? (_reportPossibleCrUseOfLevelScrollLayerUI({
                error: Error()
              }), LevelScrollLayerUI) : LevelScrollLayerUI)();
              scrollLayers.weight = scroll.weight;
              var uuids = scroll.uuids || [];
              scrollLayers.splicingMode = scroll.splicingMode;
              scrollLayers.splicingOffsetX.min = scroll.offSetX.min;
              scrollLayers.splicingOffsetX.max = scroll.offSetX.max;
              scrollLayers.splicingOffsetY.min = scroll.offSetY.min;
              scrollLayers.splicingOffsetY.max = scroll.offSetY.max;
              scrollLayers.scrollPrefabs = [];
              uuids.forEach(uuid => {
                var loadPromise = new Promise(resolve => {
                  assetManager.loadAny({
                    uuid: uuid
                  }, (err, prefab) => {
                    if (err) {
                      resolve();
                      return;
                    }

                    scrollLayers.scrollPrefabs.push(prefab);
                    resolve();
                  });
                });
                loadPromises.push(loadPromise);
              });
              layerData.scrollLayers.push(scrollLayers);
            });
            yield Promise.all(loadPromises);
            _this2._loadScrollNode = false;
            layerData.scrollLayers.forEach((scroll, index) => {
              var scrollsNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
                error: Error()
              }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(_this2.scrollsNode, "scroll_" + index);
              var totalHeight = data.speed * data.totalTime / 1000;
              console.log("LevelEditorBaseUI _checkScrollNode totalHeight " + totalHeight + " totalTime " + data.totalTime + " speed " + data.speed);
              var posOffsetY = 0;
              var height = 0;
              var prefabIndex = 0; // 当前使用的 prefab 索引

              while (height < totalHeight) {
                // 循环使用 prefab
                var curPrefab = scroll.scrollPrefabs[prefabIndex];
                var child = instantiate(curPrefab);
                var randomOffsetX = Math.random() * (scroll.splicingOffsetX.max - scroll.splicingOffsetX.min) + scroll.splicingOffsetX.min;
                child.setPosition(randomOffsetX, posOffsetY, 0);
                var offY = 0;

                if (scroll.splicingMode === (_crd && LayerSplicingMode === void 0 ? (_reportPossibleCrUseOfLayerSplicingMode({
                  error: Error()
                }), LayerSplicingMode) : LayerSplicingMode).node_height) {
                  offY = child.getComponent(UITransform).contentSize.height;
                } else if (scroll.splicingMode === (_crd && LayerSplicingMode === void 0 ? (_reportPossibleCrUseOfLayerSplicingMode({
                  error: Error()
                }), LayerSplicingMode) : LayerSplicingMode).fix_height) {
                  offY = 1334;
                } else if (scroll.splicingMode === (_crd && LayerSplicingMode === void 0 ? (_reportPossibleCrUseOfLayerSplicingMode({
                  error: Error()
                }), LayerSplicingMode) : LayerSplicingMode).random_height) {
                  offY = Math.max(scroll.splicingOffsetY.min, scroll.splicingOffsetY.max) + child.getComponent(UITransform).contentSize.height;
                }

                scrollsNode.addChild(child);
                posOffsetY += offY;
                height += offY;
                prefabIndex = (prefabIndex + 1) % scroll.scrollPrefabs.length;
              }
            });
          })();
        }

        fillLevelData(data) {
          data.terrains = [];
          this.terrainsNode.children.forEach(terrainNode => {
            // @ts-ignore
            var uuid = terrainNode._prefab.asset._uuid;

            if (terrainNode.getComponent(_crd && LevelPrefabParse === void 0 ? (_reportPossibleCrUseOfLevelPrefabParse({
              error: Error()
            }), LevelPrefabParse) : LevelPrefabParse)) {
              uuid = uuid + ".json";
            }

            data.terrains.push({
              uuid: uuid,
              position: new Vec2(terrainNode.position.x, terrainNode.position.y),
              scale: new Vec2(terrainNode.scale.x, terrainNode.scale.y),
              rotation: terrainNode.rotation.z
            });
          }); //data.dynamics = [] 在上层有保存其它信息，所以这里不清空

          if (data.type === (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
            error: Error()
          }), LayerType) : LayerType).Random) {
            this.dynamicNode.children.forEach((dynamic, index) => {
              dynamic.children.forEach((dyna, dynaIndex) => {
                var match = dyna.name.match(/^rand_(\d+)_(\d+)$/);
                var groupIndex = 0;

                if (match) {
                  groupIndex = parseInt(match[1]);
                }

                if (dyna.getComponent(_crd && LevelPrefabParse === void 0 ? (_reportPossibleCrUseOfLevelPrefabParse({
                  error: Error()
                }), LevelPrefabParse) : LevelPrefabParse)) {
                  // @ts-ignore
                  data.dynamics[index].group[groupIndex].terrains[dynaIndex].uuid = dyna._prefab.asset._uuid + ".json";
                } //console.log("LevelEditorLayerUI fillLevelData data.dynamics", data.dynamics.length);


                data.dynamics[index].group[groupIndex].position = v2(dynamic.position.x, dynamic.position.y);
                data.dynamics[index].group[groupIndex].scale = v2(dynamic.scale.x, dynamic.scale.y);
                data.dynamics[index].group[groupIndex].rotation = dynamic.rotation.z;
              });
            });
          }

          if (data.type === (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
            error: Error()
          }), LayerType) : LayerType).Emittier) {
            data.emittiers = [];
            this.emittiersNode.children.forEach(emittierNode => {
              data.emittiers.push({
                // @ts-ignore
                uuid: emittierNode._prefab.asset._uuid,
                position: v2(emittierNode.position.x, emittierNode.position.y),
                scale: v2(emittierNode.scale.x, emittierNode.scale.y),
                rotation: emittierNode.rotation.z
              });
            });
          }

          data.events = [];
          this.eventsNode.children.forEach(eventNode => {
            var event = new (_crd && LevelDataEvent === void 0 ? (_reportPossibleCrUseOfLevelDataEvent({
              error: Error()
            }), LevelDataEvent) : LevelDataEvent)();
            var eventUIComp = eventNode.getComponent(_crd && LevelEditorEventUI === void 0 ? (_reportPossibleCrUseOfLevelEditorEventUI({
              error: Error()
            }), LevelEditorEventUI) : LevelEditorEventUI);
            eventUIComp.fillLevelData(event);
            data.events.push(event);
          });
        }

        tick(progress, totalTime, speed) {
          this.node.setPosition(0, -progress * totalTime * speed / 1000, 0);
        } // tick 一直在执行，play则是开启预览了执行
        // 这里的progress和上面tick里的progress不同


        play(bPlay, progress) {
          var _this$eventsNode;

          // tick eventsNode
          (_this$eventsNode = this.eventsNode) == null || _this$eventsNode.children.forEach(eventNode => {
            var eventUIComp = eventNode.getComponent(_crd && LevelEditorEventUI === void 0 ? (_reportPossibleCrUseOfLevelEditorEventUI({
              error: Error()
            }), LevelEditorEventUI) : LevelEditorEventUI);
            eventUIComp.play(bPlay, progress);
          });
        }

      }) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=c41a9c31b8275775b257188d7587bba68c0ae7b1.js.map