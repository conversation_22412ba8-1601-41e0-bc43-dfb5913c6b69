{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/StatisticsUI.ts"], "names": ["_decorator", "Node", "csproto", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "BundleName", "DataMgr", "ButtonPlus", "List", "StatisticsHurtCell", "StatisticsScoreCell", "ccclass", "property", "StatisticsUI", "cells", "statsScore", "statsHurt", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Top", "getBundleName", "Home", "getUIOption", "isClickBgCloseUI", "onLoad", "btnOK", "addClick", "onOKClick", "btnNext", "onNextClick", "INIT_SCORE_DATA", "type", "val", "score", "INIT_HURT_DATA", "totalScore", "result", "gameLogic", "game_stats", "for<PERSON>ach", "stat", "id", "comm", "GAME_STAT_ID", "GAME_STAT_ID_SCORE_KILL", "value", "toString", "GAME_STAT_ID_SCORE_GEM", "GAME_STAT_ID_SCORE_DISTANCE", "GAME_STAT_ID_SCORE_NBOMB", "GAME_STAT_ID_SCORE_TRANS", "GAME_STAT_ID_KILL_MONSTER", "replace", "GAME_STAT_ID_PICKUP_GEM", "GAME_STAT_ID_DISTANCE", "GAME_STAT_ID_REMAIM_NBOMB", "GAME_STAT_ID_DISTANCE_TRANS", "GAME_STAT_ID_HURT_PLANE", "Number", "GAME_STAT_ID_HURT_SECOND_WEAPON", "GAME_STAT_ID_HURT_WINGPLANE", "GAME_STAT_ID_HURT_NBOMB", "GAME_STAT_ID_HURT_OTHER", "list", "numItems", "length", "idx", "cellsNode", "children", "node", "cell", "getComponent", "setType", "closeUI", "onShow", "onHide", "onClose", "onList<PERSON>ender", "listItem", "row"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;;AACdC,MAAAA,O;;AACEC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,U,iBAAAA,U;;AACFC,MAAAA,I;;AACEC,MAAAA,kB,iBAAAA,kB;;AACAC,MAAAA,mB,iBAAAA,mB;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBb,U;;8BAIjBc,Y,WADZF,OAAO,CAAC,cAAD,C,UAGHC,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,mC,UAGRA,QAAQ;AAAA;AAAA,uB,UAGRA,QAAQ,CAACZ,IAAD,C,2BAXb,MACaa,YADb;AAAA;AAAA,4BACyC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAarCC,KAbqC,GAaP,EAbO;AAAA,eAerCC,UAfqC,GAewB,EAfxB;AAAA,eAgBrCC,SAhBqC,GAgBU,EAhBV;AAAA;;AAkBjB,eAANC,MAAM,GAAW;AAAE,iBAAO,wBAAP;AAAkC;;AAC7C,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,GAAf;AAAoB;;AAC7B,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,IAAlB;AAAwB;;AACvC,eAAXC,WAAW,GAAU;AAC/B,iBAAO;AAAEC,YAAAA,gBAAgB,EAAE;AAApB,WAAP;AACH;;AAESC,QAAAA,MAAM,GAAS;AACrB,eAAKC,KAAL,CAAYC,QAAZ,CAAqB,KAAKC,SAA1B,EAAqC,IAArC;AACA,eAAKC,OAAL,CAAcF,QAAd,CAAuB,KAAKG,WAA5B,EAAyC,IAAzC;AAEA,cAAMC,eAAe,GAAG,CACpB;AAAEC,YAAAA,IAAI,EAAE,MAAR;AAAgBC,YAAAA,GAAG,EAAE,KAArB;AAA4BC,YAAAA,KAAK,EAAE;AAAnC,WADoB,EAEpB;AAAEF,YAAAA,IAAI,EAAE,MAAR;AAAgBC,YAAAA,GAAG,EAAE,IAArB;AAA2BC,YAAAA,KAAK,EAAE;AAAlC,WAFoB,EAGpB;AAAEF,YAAAA,IAAI,EAAE,MAAR;AAAgBC,YAAAA,GAAG,EAAE,KAArB;AAA4BC,YAAAA,KAAK,EAAE;AAAnC,WAHoB,EAIpB;AAAEF,YAAAA,IAAI,EAAE,OAAR;AAAiBC,YAAAA,GAAG,EAAE,KAAtB;AAA6BC,YAAAA,KAAK,EAAE;AAApC,WAJoB,EAKpB;AAAEF,YAAAA,IAAI,EAAE,MAAR;AAAgBC,YAAAA,GAAG,EAAE,KAArB;AAA4BC,YAAAA,KAAK,EAAE;AAAnC,WALoB,CAAxB;AAOA,cAAMC,cAAc,GAAG,CACnB;AAAEH,YAAAA,IAAI,EAAE,IAAR;AAAcE,YAAAA,KAAK,EAAE;AAArB,WADmB,EAEnB;AAAEF,YAAAA,IAAI,EAAE,IAAR;AAAcE,YAAAA,KAAK,EAAE;AAArB,WAFmB,EAGnB;AAAEF,YAAAA,IAAI,EAAE,IAAR;AAAcE,YAAAA,KAAK,EAAE;AAArB,WAHmB,EAInB;AAAEF,YAAAA,IAAI,EAAE,IAAR;AAAcE,YAAAA,KAAK,EAAE;AAArB,WAJmB,EAKnB;AAAEF,YAAAA,IAAI,EAAE,IAAR;AAAcE,YAAAA,KAAK,EAAE;AAArB,WALmB,CAAvB;AAQA,eAAKlB,UAAL,GAAkB,CAAC,GAAGe,eAAJ,CAAlB;AACA,eAAKd,SAAL,GAAiB,CAAC,GAAGkB,cAAJ,CAAjB;AAEA,cAAIC,UAAU,GAAG,CAAjB;AACA,cAAIC,MAAM,GAAG;AAAA;AAAA,kCAAQC,SAAR,CAAkBD,MAA/B;;AACA,cAAIA,MAAM,IAAIA,MAAM,CAACE,UAArB,EAAiC;AAC7BF,YAAAA,MAAM,CAACE,UAAP,CAAkBC,OAAlB,CAA0BC,IAAI,IAAI;AAC9B,kBAAIA,IAAI,CAACC,EAAL,IAAW;AAAA;AAAA,sCAAQC,IAAR,CAAaC,YAAb,CAA0BC,uBAAzC,EAAkE;AAC9D,qBAAK7B,UAAL,CAAgB,CAAhB,EAAmBkB,KAAnB,GAA2BO,IAAI,CAACK,KAAL,CAAYC,QAAZ,EAA3B;AACH,eAFD,MAGK,IAAIN,IAAI,CAACC,EAAL,IAAW;AAAA;AAAA,sCAAQC,IAAR,CAAaC,YAAb,CAA0BI,sBAAzC,EAAiE;AAClE,qBAAKhC,UAAL,CAAgB,CAAhB,EAAmBkB,KAAnB,GAA2BO,IAAI,CAACK,KAAL,CAAYC,QAAZ,EAA3B;AACH,eAFI,MAGA,IAAIN,IAAI,CAACC,EAAL,IAAW;AAAA;AAAA,sCAAQC,IAAR,CAAaC,YAAb,CAA0BK,2BAAzC,EAAsE;AACvE,qBAAKjC,UAAL,CAAgB,CAAhB,EAAmBkB,KAAnB,GAA2BO,IAAI,CAACK,KAAL,CAAYC,QAAZ,EAA3B;AACH,eAFI,MAGA,IAAIN,IAAI,CAACC,EAAL,IAAW;AAAA;AAAA,sCAAQC,IAAR,CAAaC,YAAb,CAA0BM,wBAAzC,EAAmE;AACpE,qBAAKlC,UAAL,CAAgB,CAAhB,EAAmBkB,KAAnB,GAA2BO,IAAI,CAACK,KAAL,CAAYC,QAAZ,EAA3B;AACH,eAFI,MAGA,IAAIN,IAAI,CAACC,EAAL,IAAW;AAAA;AAAA,sCAAQC,IAAR,CAAaC,YAAb,CAA0BO,wBAAzC,EAAmE;AACpE,qBAAKnC,UAAL,CAAgB,CAAhB,EAAmBkB,KAAnB,GAA2BO,IAAI,CAACK,KAAL,CAAYC,QAAZ,EAA3B;AACH,eAFI,MAIA,IAAIN,IAAI,CAACC,EAAL,IAAW;AAAA;AAAA,sCAAQC,IAAR,CAAaC,YAAb,CAA0BQ,yBAAzC,EAAoE;AACrE,qBAAKpC,UAAL,CAAgB,CAAhB,EAAmBiB,GAAnB,GAAyB,KAAKjB,UAAL,CAAgB,CAAhB,EAAmBiB,GAAnB,CAAuBoB,OAAvB,CAA+B,MAA/B,EAAuCZ,IAAI,CAACK,KAAL,CAAYC,QAAZ,EAAvC,CAAzB;AACH,eAFI,MAGA,IAAIN,IAAI,CAACC,EAAL,IAAW;AAAA;AAAA,sCAAQC,IAAR,CAAaC,YAAb,CAA0BU,uBAAzC,EAAkE;AACnE,qBAAKtC,UAAL,CAAgB,CAAhB,EAAmBiB,GAAnB,GAAyB,KAAKjB,UAAL,CAAgB,CAAhB,EAAmBiB,GAAnB,CAAuBoB,OAAvB,CAA+B,MAA/B,EAAuCZ,IAAI,CAACK,KAAL,CAAYC,QAAZ,EAAvC,CAAzB;AACH,eAFI,MAGA,IAAIN,IAAI,CAACC,EAAL,IAAW;AAAA;AAAA,sCAAQC,IAAR,CAAaC,YAAb,CAA0BW,qBAAzC,EAAgE;AACjE,qBAAKvC,UAAL,CAAgB,CAAhB,EAAmBiB,GAAnB,GAAyB,KAAKjB,UAAL,CAAgB,CAAhB,EAAmBiB,GAAnB,CAAuBoB,OAAvB,CAA+B,MAA/B,EAAuCZ,IAAI,CAACK,KAAL,CAAYC,QAAZ,EAAvC,CAAzB;AACH,eAFI,MAGA,IAAIN,IAAI,CAACC,EAAL,IAAW;AAAA;AAAA,sCAAQC,IAAR,CAAaC,YAAb,CAA0BY,yBAAzC,EAAoE;AACrE,qBAAKxC,UAAL,CAAgB,CAAhB,EAAmBiB,GAAnB,GAAyB,KAAKjB,UAAL,CAAgB,CAAhB,EAAmBiB,GAAnB,CAAuBoB,OAAvB,CAA+B,MAA/B,EAAuCZ,IAAI,CAACK,KAAL,CAAYC,QAAZ,EAAvC,CAAzB;AACH,eAFI,MAGA,IAAIN,IAAI,CAACC,EAAL,IAAW;AAAA;AAAA,sCAAQC,IAAR,CAAaC,YAAb,CAA0Ba,2BAAzC,EAAsE;AACvE,qBAAKzC,UAAL,CAAgB,CAAhB,EAAmBiB,GAAnB,GAAyB,KAAKjB,UAAL,CAAgB,CAAhB,EAAmBiB,GAAnB,CAAuBoB,OAAvB,CAA+B,MAA/B,EAAuCZ,IAAI,CAACK,KAAL,CAAYC,QAAZ,EAAvC,CAAzB;AACH,eAFI,MAIA,IAAIN,IAAI,CAACC,EAAL,IAAW;AAAA;AAAA,sCAAQC,IAAR,CAAaC,YAAb,CAA0Bc,uBAAzC,EAAkE;AACnE,qBAAKzC,SAAL,CAAe,CAAf,EAAkBiB,KAAlB,GAA0BO,IAAI,CAACK,KAAL,CAAYC,QAAZ,EAA1B;AACAX,gBAAAA,UAAU,IAAIuB,MAAM,CAAClB,IAAI,CAACK,KAAN,CAApB;AACH,eAHI,MAIA,IAAIL,IAAI,CAACC,EAAL,IAAW;AAAA;AAAA,sCAAQC,IAAR,CAAaC,YAAb,CAA0BgB,+BAAzC,EAA0E;AAC3E,qBAAK3C,SAAL,CAAe,CAAf,EAAkBiB,KAAlB,GAA0BO,IAAI,CAACK,KAAL,CAAYC,QAAZ,EAA1B;AACAX,gBAAAA,UAAU,IAAIuB,MAAM,CAAClB,IAAI,CAACK,KAAN,CAApB;AACH,eAHI,MAIA,IAAIL,IAAI,CAACC,EAAL,IAAW;AAAA;AAAA,sCAAQC,IAAR,CAAaC,YAAb,CAA0BiB,2BAAzC,EAAsE;AACvE,qBAAK5C,SAAL,CAAe,CAAf,EAAkBiB,KAAlB,GAA0BO,IAAI,CAACK,KAAL,CAAYC,QAAZ,EAA1B;AACAX,gBAAAA,UAAU,IAAIuB,MAAM,CAAClB,IAAI,CAACK,KAAN,CAApB;AACH,eAHI,MAIA,IAAIL,IAAI,CAACC,EAAL,IAAW;AAAA;AAAA,sCAAQC,IAAR,CAAaC,YAAb,CAA0BkB,uBAAzC,EAAkE;AACnE,qBAAK7C,SAAL,CAAe,CAAf,EAAkBiB,KAAlB,GAA0BO,IAAI,CAACK,KAAL,CAAYC,QAAZ,EAA1B;AACAX,gBAAAA,UAAU,IAAIuB,MAAM,CAAClB,IAAI,CAACK,KAAN,CAApB;AACH,eAHI,MAIA,IAAIL,IAAI,CAACC,EAAL,IAAW;AAAA;AAAA,sCAAQC,IAAR,CAAaC,YAAb,CAA0BmB,uBAAzC,EAAkE;AACnE,qBAAK9C,SAAL,CAAe,CAAf,EAAkBiB,KAAlB,GAA0BO,IAAI,CAACK,KAAL,CAAYC,QAAZ,EAA1B;AACAX,gBAAAA,UAAU,IAAIuB,MAAM,CAAClB,IAAI,CAACK,KAAN,CAApB;AACH;AACJ,aArDD;AAsDH;;AAED,eAAKkB,IAAL,CAAWC,QAAX,GAAsB,KAAKjD,UAAL,CAAgBkD,MAAtC,CAjFqB,CAiFwB;;AAE7C,cAAIC,GAAW,GAAG,CAAlB;AACA,eAAKC,SAAL,CAAgBC,QAAhB,CAAyB7B,OAAzB,CAAiC8B,IAAI,IAAI;AACrC,gBAAMC,IAAI,GAAGD,IAAI,CAACE,YAAL;AAAA;AAAA,yDAAb;;AACA,gBAAID,IAAJ,EAAU;AACNA,cAAAA,IAAI,CAACE,OAAL,CAAa,KAAKxD,SAAL,CAAekD,GAAG,EAAlB,CAAb,EAAoC,GAApC;AACH;AACJ,WALD;AAOH;;AACKvC,QAAAA,SAAS,GAAG;AAAA;AACd;AAAA;AAAA,gCAAM8C,OAAN,CAAc5D,YAAd;AADc;AAEjB;;AACKgB,QAAAA,WAAW,GAAG;AAAA;AAChB;AAAA;AAAA,gCAAM4C,OAAN,CAAc5D,YAAd;AADgB;AAEnB;;AACK6D,QAAAA,MAAM,GAAkB;AAAA;AAG7B;;AACKC,QAAAA,MAAM,GAAkB;AAAA;AAC7B;;AACKC,QAAAA,OAAO,GAAkB;AAAA;AAE9B;;AACDC,QAAAA,YAAY,CAACC,QAAD,EAAiBC,GAAjB,EAA8B;AAAC;AACvC,cAAMT,IAAI,GAAGQ,QAAQ,CAACP,YAAT;AAAA;AAAA,yDAAb;;AACA,cAAID,IAAI,KAAK,IAAb,EAAmB;AACfA,YAAAA,IAAI,CAACE,OAAL,CAAa,KAAKzD,UAAL,CAAgBgE,GAAhB,CAAb;AACH;AACJ;;AAzIoC,O;;;;;iBAGV,I;;;;;;;iBAEE,I;;;;;;;iBAGT,I;;;;;;;iBAGK,I", "sourcesContent": ["import { _decorator, Node } from 'cc';\r\nimport csproto from 'db://assets/bundles/common/script/autogen/pb/cs_proto.js';\r\nimport { BaseUI, UILayer, UIMgr, UIOpt } from \"db://assets/scripts/core/base/UIMgr\";\r\nimport { BundleName } from '../../const/BundleConst';\r\nimport { DataMgr } from '../../data/DataManager';\r\nimport { ButtonPlus } from './components/button/ButtonPlus';\r\nimport List from './components/list/List';\r\nimport { StatisticsHurtCell } from './StatisticsHurtCell';\r\nimport { StatisticsScoreCell } from './StatisticsScoreCell';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n\r\n@ccclass('StatisticsUI')\r\nexport class StatisticsUI extends BaseUI {\r\n\r\n    @property(ButtonPlus)\r\n    btnOK: ButtonPlus | null = null;\r\n    @property(ButtonPlus)\r\n    btnNext: ButtonPlus | null = null;\r\n\r\n    @property(List)\r\n    list: List | null = null;\r\n\r\n    @property(Node)\r\n    cellsNode: Node | null = null;\r\n\r\n    cells: StatisticsHurtCell[] = [];\r\n\r\n    statsScore: { type: string; val: string; score: string }[] = [];\r\n    statsHurt: { type: string; score: string }[] = [];\r\n\r\n    public static getUrl(): string { return \"prefab/ui/StatisticsUI\"; }\r\n    public static getLayer(): UILayer { return UILayer.Top }\r\n    public static getBundleName(): string { return BundleName.Home }\r\n    public static getUIOption(): UIOpt {\r\n        return { isClickBgCloseUI: false }\r\n    }\r\n\r\n    protected onLoad(): void {\r\n        this.btnOK!.addClick(this.onOKClick, this);\r\n        this.btnNext!.addClick(this.onNextClick, this);\r\n\r\n        const INIT_SCORE_DATA = [\r\n            { type: '杀怪得分', val: '0击落', score: '0' },\r\n            { type: '宝石得分', val: '0颗', score: '0' },\r\n            { type: '飞行距离', val: '0KM', score: '0' },\r\n            { type: '剩余核弹数', val: '余0颗', score: '0' },\r\n            { type: '传送得分', val: '前0关', score: '0' }\r\n        ];\r\n        const INIT_HURT_DATA = [\r\n            { type: '战机', score: '0' },\r\n            { type: '装备', score: '0' },\r\n            { type: '武器', score: '0' },\r\n            { type: '道具', score: '0' },\r\n            { type: '其他', score: '0' }\r\n        ];\r\n\r\n        this.statsScore = [...INIT_SCORE_DATA];\r\n        this.statsHurt = [...INIT_HURT_DATA];\r\n\r\n        let totalScore = 0;\r\n        let result = DataMgr.gameLogic.result;\r\n        if (result && result.game_stats) {\r\n            result.game_stats.forEach(stat => {\r\n                if (stat.id == csproto.comm.GAME_STAT_ID.GAME_STAT_ID_SCORE_KILL) {\r\n                    this.statsScore[0].score = stat.value!.toString();\r\n                }\r\n                else if (stat.id == csproto.comm.GAME_STAT_ID.GAME_STAT_ID_SCORE_GEM) {\r\n                    this.statsScore[1].score = stat.value!.toString();\r\n                }\r\n                else if (stat.id == csproto.comm.GAME_STAT_ID.GAME_STAT_ID_SCORE_DISTANCE) {\r\n                    this.statsScore[2].score = stat.value!.toString();\r\n                }\r\n                else if (stat.id == csproto.comm.GAME_STAT_ID.GAME_STAT_ID_SCORE_NBOMB) {\r\n                    this.statsScore[3].score = stat.value!.toString();\r\n                }\r\n                else if (stat.id == csproto.comm.GAME_STAT_ID.GAME_STAT_ID_SCORE_TRANS) {\r\n                    this.statsScore[4].score = stat.value!.toString();\r\n                }\r\n\r\n                else if (stat.id == csproto.comm.GAME_STAT_ID.GAME_STAT_ID_KILL_MONSTER) {\r\n                    this.statsScore[0].val = this.statsScore[0].val.replace(/\\d+/g, stat.value!.toString()) ;\r\n                }\r\n                else if (stat.id == csproto.comm.GAME_STAT_ID.GAME_STAT_ID_PICKUP_GEM) {\r\n                    this.statsScore[1].val = this.statsScore[1].val.replace(/\\d+/g, stat.value!.toString()) ;\r\n                }\r\n                else if (stat.id == csproto.comm.GAME_STAT_ID.GAME_STAT_ID_DISTANCE) {\r\n                    this.statsScore[2].val = this.statsScore[2].val.replace(/\\d+/g, stat.value!.toString()) ;\r\n                }\r\n                else if (stat.id == csproto.comm.GAME_STAT_ID.GAME_STAT_ID_REMAIM_NBOMB) {\r\n                    this.statsScore[3].val = this.statsScore[3].val.replace(/\\d+/g, stat.value!.toString()) ;\r\n                }\r\n                else if (stat.id == csproto.comm.GAME_STAT_ID.GAME_STAT_ID_DISTANCE_TRANS) {\r\n                    this.statsScore[4].val = this.statsScore[4].val.replace(/\\d+/g, stat.value!.toString()) ;\r\n                }\r\n\r\n                else if (stat.id == csproto.comm.GAME_STAT_ID.GAME_STAT_ID_HURT_PLANE) {\r\n                    this.statsHurt[0].score = stat.value!.toString();\r\n                    totalScore += Number(stat.value);\r\n                }\r\n                else if (stat.id == csproto.comm.GAME_STAT_ID.GAME_STAT_ID_HURT_SECOND_WEAPON) {\r\n                    this.statsHurt[1].score = stat.value!.toString();\r\n                    totalScore += Number(stat.value);\r\n                }\r\n                else if (stat.id == csproto.comm.GAME_STAT_ID.GAME_STAT_ID_HURT_WINGPLANE) {\r\n                    this.statsHurt[2].score = stat.value!.toString();\r\n                    totalScore += Number(stat.value);\r\n                }\r\n                else if (stat.id == csproto.comm.GAME_STAT_ID.GAME_STAT_ID_HURT_NBOMB) {\r\n                    this.statsHurt[3].score = stat.value!.toString();\r\n                    totalScore += Number(stat.value);\r\n                }\r\n                else if (stat.id == csproto.comm.GAME_STAT_ID.GAME_STAT_ID_HURT_OTHER) {\r\n                    this.statsHurt[4].score = stat.value!.toString();\r\n                    totalScore += Number(stat.value);\r\n                }\r\n            });\r\n        }\r\n\r\n        this.list!.numItems = this.statsScore.length;//目前只有5个\r\n\r\n        let idx: number = 0;\r\n        this.cellsNode!.children.forEach(node => {\r\n            const cell = node.getComponent(StatisticsHurtCell);\r\n            if (cell) {\r\n                cell.setType(this.statsHurt[idx++], 150);\r\n            }\r\n        });\r\n\r\n    }\r\n    async onOKClick() {\r\n        UIMgr.closeUI(StatisticsUI);\r\n    }\r\n    async onNextClick() {\r\n        UIMgr.closeUI(StatisticsUI);\r\n    }\r\n    async onShow(): Promise<void> {\r\n\r\n\r\n    }\r\n    async onHide(): Promise<void> {\r\n    }\r\n    async onClose(): Promise<void> {\r\n\r\n    }\r\n    onListRender(listItem: Node, row: number) {//这个函数在onShow()之前调用\r\n        const cell = listItem.getComponent(StatisticsScoreCell);\r\n        if (cell !== null) {\r\n            cell.setType(this.statsScore[row]);\r\n        }\r\n    }\r\n}\r\n"]}