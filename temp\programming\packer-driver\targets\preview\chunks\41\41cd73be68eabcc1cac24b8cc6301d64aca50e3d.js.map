{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/preview/WavePreview.ts"], "names": ["_decorator", "Component", "Vec2", "instantiate", "assetManager", "LubanMgr", "EnemyPlane", "LevelEditorUtils", "EnemyData", "eMoveEvent", "ccclass", "property", "executeInEditMode", "menu", "WavePreview", "_luban", "_bindingMap", "Map", "previewWave", "planePool", "activePlane", "instance", "_instance", "luban", "onLoad", "console", "warn", "reset", "node", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clear", "update", "dt", "for<PERSON>ach", "nodes", "wave", "updateWave", "tickPreview", "setupWave", "waveData", "spawnGroups", "length", "planeId", "i", "planeID", "table", "initInEditor", "then", "createPlane", "planeData", "TbResEnemy", "get", "fullPath", "prefab", "loadByPath", "<PERSON><PERSON><PERSON><PERSON>", "set", "push", "wavePos", "worldPosition", "nodePos", "x", "spawnPosX", "eval", "y", "spawnPosY", "nodeAngle", "spawnAngle", "setWorldPosition", "setRotationFromEuler", "addPreview", "posX", "posY", "setCreatePlaneDelegate", "pos", "angle", "createDummyPlane", "trigger", "dtInMiliseconds", "tick", "plane", "moveCom", "clearPreview", "destroy", "dummy_plane_uuid", "loadAny", "uuid", "err", "error", "pop", "active", "planeNode", "getComponent", "setPos", "initPlane", "removeAllListeners", "on", "onBecomeInvisible", "initMove"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACSA,MAAAA,U,OAAAA,U;AAAyDC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,Y,OAAAA,Y;;AAIvFC,MAAAA,Q,iBAAAA,Q;;AAEFC,MAAAA,U;;AACEC,MAAAA,gB,iBAAAA,gB;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;;;OARH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA,iBAArB;AAAwCC,QAAAA;AAAxC,O,GAAiDb,U;;AAUvD;6BAIac,W,WAHZJ,OAAO,CAAC,aAAD,C,UACPG,IAAI,CAAC,aAAD,C,UACJD,iBAAiB,E,yDAFlB,MAGaE,WAHb,SAGiCb,SAHjC,CAG2C;AAAA;AAAA;AAAA,eAO/Bc,MAP+B,GAOP,IAPO;AAevC;AAfuC,eAgB/BC,WAhB+B,GAgBE,IAAIC,GAAJ,EAhBF;AAoGvC;AApGuC,eAqG/BC,WArG+B,GAqGT,EArGS;AAAA,eAsG/BC,SAtG+B,GAsGL,EAtGK;AAAA,eAuG/BC,WAvG+B,GAuGH,EAvGG;AAAA;;AAGb,mBAARC,QAAQ,GAAqB;AAC3C,iBAAO,KAAKC,SAAZ;AACH;;AAGe,YAALC,KAAK,GAAkB;AAC9B,cAAI,KAAKR,MAAL,IAAe,IAAnB,EAAyB;AACrB,iBAAKA,MAAL,GAAc;AAAA;AAAA,uCAAd;AACH;;AACD,iBAAO,KAAKA,MAAZ;AACH;;AAKDS,QAAAA,MAAM,GAAG;AACL,cAAIV,WAAW,CAACQ,SAAZ,IAAyB,IAA7B,EAAmC;AAC/BR,YAAAA,WAAW,CAACQ,SAAZ,GAAwB,IAAxB;AACH,WAFD,MAEO;AACHG,YAAAA,OAAO,CAACC,IAAR,CAAa,+BAAb;AACH;AACJ;;AAEDC,QAAAA,KAAK,GAAG;AACJ,eAAKC,IAAL,CAAUC,iBAAV;;AACA,eAAKb,WAAL,CAAiBc,KAAjB;AACH;;AAEDC,QAAAA,MAAM,CAACC,EAAD,EAAa;AACf,eAAKhB,WAAL,CAAiBiB,OAAjB,CAAyB,CAACC,KAAD,EAAQC,IAAR,KAAiB;AACtC,iBAAKC,UAAL,CAAgBD,IAAhB,EAAsBD,KAAtB;AACH,WAFD;;AAIA,eAAKG,WAAL,CAAiBL,EAAjB;AACH;;AAEDM,QAAAA,SAAS,CAACH,IAAD,EAAa;AAClB,cAAMI,QAAQ,GAAGJ,IAAI,CAACI,QAAtB;;AACA,cAAIA,QAAQ,CAACC,WAAT,IAAwBD,QAAQ,CAACC,WAAT,CAAqBC,MAArB,GAA8B,CAA1D,EAA6D;AAAA;;AACzD,gBAAIC,OAAO,GAAG,CAAd;;AACA,iBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGJ,QAAQ,CAACC,WAAT,CAAqBC,MAAzC,EAAiDE,CAAC,EAAlD,EAAsD;AAClD,kBAAIJ,QAAQ,CAACC,WAAT,CAAqBG,CAArB,EAAwBC,OAAxB,GAAkC,CAAtC,EAAyC;AACrCF,gBAAAA,OAAO,GAAGH,QAAQ,CAACC,WAAT,CAAqBG,CAArB,EAAwBC,OAAlC;AACA;AACH;AACJ;;AAED,gBAAIF,OAAO,IAAI,CAAf,EAAkB;AACdjB,cAAAA,OAAO,CAACC,IAAR,CAAa,yDAAb;AACA;AACH;;AAED,gBAAI,qBAAKH,KAAL,iCAAYsB,KAAZ,KAAqB,IAAzB,EAA+B;AAAA;;AAC3B,mCAAKtB,KAAL,0BAAYuB,YAAZ,GAA2BC,IAA3B,CAAgC,MAAM;AAClC,qBAAKC,WAAL,CAAiBb,IAAjB,EAAuBO,OAAvB;AACH,eAFD;AAGH;AACJ;AACJ;;AAEOM,QAAAA,WAAW,CAACb,IAAD,EAAaO,OAAb,EAA8B;AAAA;;AAC7C,cAAMO,SAAS,mBAAG,KAAK1B,KAAR,qBAAG,aAAYsB,KAAZ,CAAkBK,UAAlB,CAA6BC,GAA7B,CAAiCT,OAAjC,CAAlB;;AACA,cAAIO,SAAS,IAAI,IAAjB,EAAuB;AACnBxB,YAAAA,OAAO,CAACC,IAAR,CAAa,8CAAb,EAA6DgB,OAA7D;AACA;AACH;;AAED,cAAMU,QAAQ,GAAG,2BAA2BH,SAAS,CAACI,MAArC,GAA8C,SAA/D;AACA;AAAA;AAAA,oDAAiBC,UAAjB,CAAoCF,QAApC,EAA8CL,IAA9C,CAAoDM,MAAD,IAAY;AAC3D,gBAAIA,MAAJ,EAAY;AACR,kBAAMzB,IAAI,GAAGzB,WAAW,CAACkD,MAAD,CAAxB;;AACA,kBAAIzB,IAAJ,EAAU;AACN,qBAAKA,IAAL,CAAU2B,QAAV,CAAmB3B,IAAnB;;AACA,oBAAIM,KAAK,GAAG,KAAKlB,WAAL,CAAiBmC,GAAjB,CAAqBhB,IAArB,CAAZ;;AACA,oBAAID,KAAK,IAAI,IAAb,EAAmB;AACfA,kBAAAA,KAAK,GAAG,EAAR;;AACA,uBAAKlB,WAAL,CAAiBwC,GAAjB,CAAqBrB,IAArB,EAA2BD,KAA3B;AACH;;AACDA,gBAAAA,KAAK,CAACuB,IAAN,CAAW7B,IAAX;AACH;AACJ;AACJ,WAbD;AAcH;;AAEDQ,QAAAA,UAAU,CAACD,IAAD,EAAaD,KAAb,EAA4B;AAClC,cAAMwB,OAAO,GAAGvB,IAAI,CAACP,IAAL,CAAU+B,aAA1B;AACA,cAAMpB,QAAQ,GAAGJ,IAAI,CAACI,QAAtB;AACA,cAAMqB,OAAO,GAAG,IAAI1D,IAAJ,CAASwD,OAAO,CAACG,CAAR,GAAYtB,QAAQ,CAACuB,SAAT,CAAmBC,IAAnB,EAArB,EAAgDL,OAAO,CAACM,CAAR,GAAYzB,QAAQ,CAAC0B,SAAT,CAAmBF,IAAnB,EAA5D,CAAhB;AACA,cAAMG,SAAS,GAAG3B,QAAQ,CAAC4B,UAAT,CAAoBJ,IAApB,EAAlB;;AAEA,eAAK,IAAIpB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGT,KAAK,CAACO,MAA1B,EAAkCE,CAAC,EAAnC,EAAuC;AACnC,gBAAMf,IAAI,GAAGM,KAAK,CAACS,CAAD,CAAlB;AACAf,YAAAA,IAAI,CAACwC,gBAAL,CAAsBR,OAAO,CAACC,CAA9B,EAAiCD,OAAO,CAACI,CAAzC,EAA4C,CAA5C;AACApC,YAAAA,IAAI,CAACyC,oBAAL,CAA0B,CAA1B,EAA6B,CAA7B,EAAgCH,SAAhC;AACH;AACJ;;AAODI,QAAAA,UAAU,CAACnC,IAAD,EAAaoC,IAAb,EAA2BC,IAA3B,EAAyC;AAC/CrC,UAAAA,IAAI,CAACsC,sBAAL,CAA4B,CAAC/B,OAAD,EAAkBgC,GAAlB,EAA6BC,KAA7B,KAA+C;AACvE,iBAAKC,gBAAL,CAAsBzC,IAAtB,EAA4BO,OAA5B,EAAqCgC,GAArC,EAA0CC,KAA1C;AACH,WAFD;AAGAxC,UAAAA,IAAI,CAAC0C,OAAL,CAAaN,IAAb,EAAmBC,IAAnB;AACA,eAAKtD,WAAL,CAAiBuC,IAAjB,CAAsBtB,IAAtB;AACH;;AAEDE,QAAAA,WAAW,CAACL,EAAD,EAAa;AACpB,cAAI,KAAKd,WAAL,CAAiBuB,MAAjB,IAA2B,CAA/B,EAAkC;AAC9B;AACH;;AAED,cAAMqC,eAAe,GAAG9C,EAAE,GAAG,IAA7B;AACA,eAAKd,WAAL,CAAiBe,OAAjB,CAA0BE,IAAD,IAAU;AAC/B;AACAA,YAAAA,IAAI,CAAC4C,IAAL,CAAUD,eAAV;AACH,WAHD;AAKA,eAAK1D,WAAL,CAAiBa,OAAjB,CAA0B+C,KAAD,IAAW;AAChCA,YAAAA,KAAK,CAACC,OAAN,CAAeF,IAAf,CAAoB/C,EAApB;AACH,WAFD;AAGH;;AAEDkD,QAAAA,YAAY,GAAG;AACX;AACA,eAAKhE,WAAL,CAAiBe,OAAjB,CAA0BE,IAAD,IAAU;AAC/BA,YAAAA,IAAI,CAACP,IAAL,CAAUuD,OAAV;AACH,WAFD;AAGA,eAAK/D,WAAL,CAAiBa,OAAjB,CAA0B+C,KAAD,IAAW;AAChCA,YAAAA,KAAK,CAACpD,IAAN,CAAWuD,OAAX;AACH,WAFD;AAGA,eAAKhE,SAAL,CAAec,OAAf,CAAwB+C,KAAD,IAAW;AAC9BA,YAAAA,KAAK,CAACpD,IAAN,CAAWuD,OAAX;AACH,WAFD;AAIA,eAAK/D,WAAL,GAAmB,EAAnB;AACA,eAAKD,SAAL,GAAiB,EAAjB;AACA,eAAKD,WAAL,GAAmB,EAAnB;AACH;;AAEO0D,QAAAA,gBAAgB,CAACzC,IAAD,EAAaO,OAAb,EAA8BgC,GAA9B,EAAyCC,KAAzC,EAAwD;AAC5E;AACA;AACA,cAAMS,gBAAwB,GAAG,sCAAjC;AACAhF,UAAAA,YAAY,CAACiF,OAAb,CAAqB;AAACC,YAAAA,IAAI,EAACF;AAAN,WAArB,EAA8C,CAACG,GAAD,EAAMlC,MAAN,KAAwB;AAClE,gBAAIkC,GAAJ,EAAS;AACL9D,cAAAA,OAAO,CAAC+D,KAAR,CAAc,8CAAd,EAA8DD,GAA9D;AACA;AACH,aAJiE,CAMlE;;;AACA,gBAAIP,KAAsB,GAAG,IAA7B;;AACA,gBAAI,KAAK7D,SAAL,CAAesB,MAAf,GAAwB,CAA5B,EAA+B;AAC3BuC,cAAAA,KAAK,GAAG,KAAK7D,SAAL,CAAesE,GAAf,EAAR;AACAT,cAAAA,KAAK,CAACpD,IAAN,CAAW8D,MAAX,GAAoB,IAApB;AACH,aAHD,MAIK;AACD,kBAAMC,SAAS,GAAGxF,WAAW,CAACkD,MAAD,CAA7B;;AACA,kBAAM2B,MAAK,GAAGW,SAAS,CAAEC,YAAX;AAAA;AAAA,2CAAd;;AACA,kBAAIZ,MAAJ,EAAW;AACPA,gBAAAA,MAAK,CAACa,MAAN,CAAanB,GAAG,CAACb,CAAjB,EAAoBa,GAAG,CAACV,CAAxB;;AACAgB,gBAAAA,MAAK,CAACc,SAAN,CAAgB;AAAA;AAAA,6CAAhB;;AACAd,gBAAAA,MAAK,CAACC,OAAN,CAAec,kBAAf;;AACAf,gBAAAA,MAAK,CAACC,OAAN,CAAee,EAAf,CAAkB;AAAA;AAAA,8CAAWC,iBAA7B,EAAgD,MAAM;AAClDjB,kBAAAA,MAAK,CAACpD,IAAN,CAAW8D,MAAX,GAAoB,KAApB;AACA,uBAAKvE,SAAL,CAAesC,IAAf,CAAoBuB,MAApB;AACH,iBAHD;;AAIAA,gBAAAA,MAAK,CAACkB,QAAN,CAAevB,KAAf;;AACA,qBAAK/C,IAAL,CAAU2B,QAAV,CAAmBoC,SAAnB;AACA,qBAAKvE,WAAL,CAAiBqC,IAAjB,CAAsBuB,MAAtB;AACH,eAXD,MAWO;AACHW,gBAAAA,SAAS,CAACR,OAAV;AACH;AACJ;AACJ,WA9BD;AA+BH;;AArLsC,O,UAExB7D,S,GAA8B,I", "sourcesContent": ["\r\nimport { _decorator, Node, Prefab, CCBoolean, CCFloat, CCInteger, Component, Vec2, instantiate, assetManager } from 'cc';\r\nconst { ccclass, property, executeInEditMode, menu } = _decorator;\r\nimport { WaveData, eSpawnOrder, eWaveCompletion } from 'db://assets/bundles/common/script/game/data/WaveData';\r\nimport { Wave } from 'db://assets/bundles/common/script/game/wave/Wave';\r\nimport { LubanMgr } from 'db://assets/bundles/common/script/luban/LubanMgr';\r\nimport { ObjectPool } from 'db://assets/bundles/common/script/game/bullet/ObjectPool';\r\nimport EnemyPlane from 'db://assets/bundles/common/script/game/ui/plane/enemy/EnemyPlane';\r\nimport { LevelEditorUtils } from '../utils';\r\nimport { EnemyData } from 'db://assets/bundles/common/script/game/data/EnemyData';\r\nimport { eMoveEvent } from 'db://assets/bundles/common/script/game/move/IMovable';\r\n\r\n/// 用来创建和管理波次的所有飞机对象\r\n@ccclass('WavePreview')\r\n@menu(\"怪物/编辑器/波次预览\")\r\n@executeInEditMode()\r\nexport class WavePreview extends Component {\r\n\r\n    private static _instance: WavePreview|null = null;\r\n    public static get instance(): WavePreview|null {\r\n        return this._instance;\r\n    }\r\n\r\n    private _luban: LubanMgr|null = null;\r\n    public get luban(): LubanMgr|null {\r\n        if (this._luban == null) {\r\n            this._luban = new LubanMgr();\r\n        }\r\n        return this._luban;\r\n    }\r\n\r\n    // 这里的wave是编辑时的示意\r\n    private _bindingMap: Map<Wave, Node[]> = new Map();\r\n\r\n    onLoad() {\r\n        if (WavePreview._instance == null) {\r\n            WavePreview._instance = this;\r\n        } else {\r\n            console.warn(\"WavePreview multiple instance\");\r\n        }\r\n    }\r\n\r\n    reset() {\r\n        this.node.removeAllChildren();\r\n        this._bindingMap.clear();\r\n    }\r\n\r\n    update(dt: number) {\r\n        this._bindingMap.forEach((nodes, wave) => {\r\n            this.updateWave(wave, nodes);\r\n        });\r\n\r\n        this.tickPreview(dt);\r\n    }\r\n\r\n    setupWave(wave: Wave) {\r\n        const waveData = wave.waveData;\r\n        if (waveData.spawnGroups && waveData.spawnGroups.length > 0) {\r\n            let planeId = 0;\r\n            for (let i = 0; i < waveData.spawnGroups.length; i++) {\r\n                if (waveData.spawnGroups[i].planeID > 0) {\r\n                    planeId = waveData.spawnGroups[i].planeID;\r\n                    break;\r\n                }\r\n            }\r\n\r\n            if (planeId == 0) {\r\n                console.warn(\"WavePreview createPlane no valid planeId in spawnGroups\");\r\n                return;\r\n            }\r\n\r\n            if (this.luban?.table == null) {\r\n                this.luban?.initInEditor().then(() => {\r\n                    this.createPlane(wave, planeId);\r\n                });\r\n            }\r\n        }\r\n    }\r\n\r\n    private createPlane(wave: Wave, planeId: number) {\r\n        const planeData = this.luban?.table.TbResEnemy.get(planeId);\r\n        if (planeData == null) {\r\n            console.warn(\"WavePreview createPlane no planeData for id:\", planeId);\r\n            return;\r\n        }\r\n\r\n        const fullPath = \"db://assets/resources/\" + planeData.prefab + \".prefab\";\r\n        LevelEditorUtils.loadByPath<Prefab>(fullPath).then((prefab) => {\r\n            if (prefab) {\r\n                const node = instantiate(prefab);\r\n                if (node) {\r\n                    this.node.addChild(node);\r\n                    let nodes = this._bindingMap.get(wave);\r\n                    if (nodes == null) {\r\n                        nodes = [];\r\n                        this._bindingMap.set(wave, nodes);\r\n                    }\r\n                    nodes.push(node);\r\n                }\r\n            }\r\n        });\r\n    }\r\n\r\n    updateWave(wave: Wave, nodes: Node[]) {\r\n        const wavePos = wave.node.worldPosition;\r\n        const waveData = wave.waveData;\r\n        const nodePos = new Vec2(wavePos.x + waveData.spawnPosX.eval(), wavePos.y + waveData.spawnPosY.eval());\r\n        const nodeAngle = waveData.spawnAngle.eval();\r\n\r\n        for (let i = 0; i < nodes.length; i++) {\r\n            const node = nodes[i];\r\n            node.setWorldPosition(nodePos.x, nodePos.y, 0);\r\n            node.setRotationFromEuler(0, 0, nodeAngle);\r\n        }\r\n    }\r\n\r\n    // 这里的wave时编辑器play时，用来动态创建小怪的wave。\r\n    private previewWave: Wave[] = [];\r\n    private planePool: EnemyPlane[] = []\r\n    private activePlane: EnemyPlane[] = [];\r\n\r\n    addPreview(wave: Wave, posX: number, posY: number) {\r\n        wave.setCreatePlaneDelegate((planeId: number, pos: Vec2, angle: number) => {\r\n            this.createDummyPlane(wave, planeId, pos, angle);\r\n        });\r\n        wave.trigger(posX, posY);\r\n        this.previewWave.push(wave);\r\n    }\r\n\r\n    tickPreview(dt: number) {\r\n        if (this.previewWave.length == 0) {\r\n            return;\r\n        }\r\n\r\n        const dtInMiliseconds = dt * 1000;\r\n        this.previewWave.forEach((wave) => {\r\n            // console.log('isWaveCompleted: ', wave.isCompleted);\r\n            wave.tick(dtInMiliseconds);\r\n        });\r\n\r\n        this.activePlane.forEach((plane) => {\r\n            plane.moveCom!.tick(dt);\r\n        });\r\n    }\r\n\r\n    clearPreview() {\r\n        // destroy preivewWave\r\n        this.previewWave.forEach((wave) => {\r\n            wave.node.destroy();\r\n        });\r\n        this.activePlane.forEach((plane) => {\r\n            plane.node.destroy();\r\n        });\r\n        this.planePool.forEach((plane) => {\r\n            plane.node.destroy();\r\n        });\r\n\r\n        this.activePlane = [];\r\n        this.planePool = [];\r\n        this.previewWave = [];\r\n    }\r\n\r\n    private createDummyPlane(wave: Wave, planeId: number, pos: Vec2, angle: number) {\r\n        // 对应\"assets/editor/level/prefab/dummy_plane\";\r\n        // console.log('createDummyPlane: ');\r\n        const dummy_plane_uuid: string = \"698c56c6-6603-4e69-abaf-421b721ef307\";\r\n        assetManager.loadAny({uuid:dummy_plane_uuid}, (err, prefab:Prefab) => {\r\n            if (err) {\r\n                console.error(\"WavePreview createDummyPlane load prefab err\", err);\r\n                return;\r\n            }\r\n\r\n            // 从对象池里拿一个dummy plane\r\n            let plane: EnemyPlane|null = null;\r\n            if (this.planePool.length > 0) {\r\n                plane = this.planePool.pop()!;\r\n                plane.node.active = true;\r\n            }\r\n            else {\r\n                const planeNode = instantiate(prefab);\r\n                const plane = planeNode!.getComponent(EnemyPlane);\r\n                if (plane) {\r\n                    plane.setPos(pos.x, pos.y);\r\n                    plane.initPlane(new EnemyData());\r\n                    plane.moveCom!.removeAllListeners();\r\n                    plane.moveCom!.on(eMoveEvent.onBecomeInvisible, () => {\r\n                        plane.node.active = false;\r\n                        this.planePool.push(plane);\r\n                    });\r\n                    plane.initMove(angle);\r\n                    this.node.addChild(planeNode);\r\n                    this.activePlane.push(plane);\r\n                } else {\r\n                    planeNode.destroy();\r\n                }\r\n            }\r\n        });\r\n    }\r\n}"]}