import csproto from 'db://assets/bundles/common/script/autogen/pb/cs_proto.js';
import { MyApp } from "../../app/MyApp";
import { IData } from "../DataManager";
export class Friend implements IData {

    public init(): void {
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GET_FRIEND_LIST, this.onGetFriendInfoMsg, this);
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GET_VIP_INFO, this.onGetVIPInfoMsg, this);

        //MessageBox.show("错误码：\n" + csproto.comm.RET_CODE[csproto.comm.RET_CODE.RET_CODE_CARD_GROUP_FORCE_ID_INVALID]);
        //MessageBox.show("错误码：\n" + csproto.comm.RET_CODE[10111]);
    }
    private onGetFriendInfoMsg(msg: csproto.cs.IS2CMsg) {
    }
    private onGetVIPInfoMsg(msg: csproto.cs.IS2CMsg) {
    }
    public update(): void {
    }
}
