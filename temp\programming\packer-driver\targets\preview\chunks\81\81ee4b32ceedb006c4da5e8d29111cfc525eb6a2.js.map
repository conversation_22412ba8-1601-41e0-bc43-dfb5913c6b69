{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/move/LevelDebug.ts"], "names": ["_decorator", "Camera", "Component", "ccclass", "property", "LevelDebug", "start", "update", "deltaTime", "node", "setPosition", "camera", "position"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,S,OAAAA,S;;;;;;;;;OACvB;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBJ,U;;4BAGjBK,U,WADZF,OAAO,CAAC,YAAD,C,UAGHC,QAAQ,CAACH,MAAD,C,2BAHb,MACaI,UADb,SACgCH,SADhC,CAC0C;AAAA;AAAA;;AAAA;AAAA;;AAKtCI,QAAAA,KAAK,GAAG,CAEP;;AAEDC,QAAAA,MAAM,CAACC,SAAD,EAAoB;AACtB,eAAKC,IAAL,CAAUC,WAAV,CAAsB,KAAKC,MAAL,CAAaF,IAAb,CAAkBG,QAAxC;AACH;;AAXqC,O;;;;;iBAGd,I", "sourcesContent": ["import { _decorator, Camera, Component } from 'cc';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('LevelDebug')\r\nexport class LevelDebug extends Component {\r\n\r\n    @property(Camera)\r\n    camera: Camera | null = null;\r\n\r\n    start() {\r\n\r\n    }\r\n\r\n    update(deltaTime: number) {\r\n        this.node.setPosition(this.camera!.node.position);\r\n    }\r\n}\r\n\r\n\r\n"]}