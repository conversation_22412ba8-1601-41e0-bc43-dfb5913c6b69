{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/BattleManager.ts"], "names": ["BattleManager", "director", "Rect", "logInfo", "log<PERSON>arn", "<PERSON>", "GameConst", "SingletonBase", "UIMgr", "MyApp", "ModeType", "DataMgr", "EventManager", "EventMgr", "HomeUIEvent", "GameReviveUI", "MBoomUI", "LoadingUI", "BottomUI", "RogueUI", "HomeUI", "TopUI", "BulletSystem", "GameEvent", "GameIns", "GameMapRun", "lcgRand", "GameMain", "RAND_STRATEGY", "chapterConfig", "_chapterConfig", "levelList", "_levelList", "modeConfig", "_modeConfig", "constructor", "initBattleEnd", "isGameStart", "animSpeed", "curLevel", "_loadTotal", "_loadCount", "_rand", "Instance", "on", "onNetGameStart", "onNetGameOver", "startGameByMode", "modeID", "randSeed", "Date", "now", "seed", "fromNumber", "lubanTables", "TbResGameMode", "get", "gameLogic", "cmdGameStart", "openUI", "emit", "Leave", "mainPlaneManager", "setPlaneData", "planeInfo", "getPlaneInfoById", "_initLevelList", "chapterID", "loadScene", "TbResChapter", "levelGroupList", "_randomSelection", "strategyList", "levelGroupCount", "strategy", "length", "levelGroupID", "levelGroupData", "TbResLevelGroup", "push", "normSTList", "normLevelCount", "normLevelST", "bossSTList", "bossLevelCount", "bossLevelST", "mainReset", "enemyManager", "boss<PERSON><PERSON><PERSON>", "waveManager", "reset", "instance", "clear", "hurtEffectManager", "gameStateManager", "destroy", "subReset", "checkLoadFinish", "closeUI", "GameLoadEnd", "initBattle", "addLoadCount", "count", "startLoading", "preload", "preLoad", "initData", "mainPlane", "planeIn", "init", "ViewBattleWidth", "ViewHeight", "onPlaneIn", "GameMainPlaneIn", "beginBattle", "GameStart", "gameStart", "begine", "updateGameLogic", "dt", "isGameOver", "gamePlaneManager", "enemyTarget", "isInBattle", "isGameWillOver", "gameDataManager", "gameTime", "tick", "fColliderManager", "setTouchState", "is<PERSON><PERSON>ch", "setAnimSpeed", "GameFightUI", "relifeBattle", "gameResume", "revive", "setGameEnd", "isWin", "checkNextlevel", "startNextBattle", "gamePause", "checkCanRevive", "hpNode", "active", "endBattle", "showGameResult", "cmdGameEnd", "getGameResultData", "getGameLevelResultData", "modeType", "ENDLESS", "levelCount", "gameOver", "quitBattle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tip", "bossWillEnter", "bossFightStart", "setFireEnable", "setMoveAble", "random", "STList", "results", "WEIGHT_PRUE", "totalWeight", "reduce", "sum", "item", "Weight", "i", "randomIndex", "Math", "floor", "battleManager", "ID", "randomValue", "cumulativeWeight", "WEIGHT_NO_REPEAT", "tempList", "lastSelectedId", "lastSelectedIndex", "findIndex", "selectedIndex", "j", "selectedId", "ORDER", "currentIndex"], "mappings": ";;;4VAgCaA,a;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA/BJC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,I,OAAAA,I;;AACVC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,O,iBAAAA,O;;AACXC,MAAAA,I;;AACEC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,Y;AAAgBC,MAAAA,Q,iBAAAA,Q;;AACdC,MAAAA,W,kBAAAA,W;;AACAC,MAAAA,Y,kBAAAA,Y;;AACAC,MAAAA,O,kBAAAA,O;;AACAC,MAAAA,S,kBAAAA,S;;AACAC,MAAAA,Q,kBAAAA,Q;;AACAC,MAAAA,O,kBAAAA,O;;AACAC,MAAAA,M,kBAAAA,M;;AACAC,MAAAA,K,kBAAAA,K;;AACAC,MAAAA,Y,kBAAAA,Y;;AACAC,MAAAA,S,kBAAAA,S;;AACAC,MAAAA,O,kBAAAA,O;;AACFC,MAAAA,U;;AACEC,MAAAA,O,kBAAAA,O;;AACAC,MAAAA,Q,kBAAAA,Q;;;;;;;;;AAEJC,MAAAA,a,0BAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;eAAAA,a;QAAAA,a;;+BAMQ5B,a,GAAN,MAAMA,aAAN;AAAA;AAAA,0CAAyD;AASpC,YAAb6B,aAAa,GAAsB;AAAE,iBAAO,KAAKC,cAAZ;AAA6B;;AAC1C;AACf,YAATC,SAAS,GAAa;AAAE,iBAAO,KAAKC,UAAZ;AAAyB;;AACvC,YAAVC,UAAU,GAAuB;AAAE,iBAAO,KAAKC,WAAZ;AAA0B;;AAMxEC,QAAAA,WAAW,GAAG;AACV;AADU,eAhBdC,aAgBc,GAhBW,KAgBX;AAAA,eAfdC,WAec,GAfS,KAeT;AAAA,eAddC,SAcc,GAdM,CAcN;AAAA,eAZdJ,WAYc,GAZoB,IAYpB;AAAA,eAXdK,QAWc,GAXK,CAWL;AAXO;AAWP,eAVdT,cAUc,GAVsB,IAUtB;AAAA,eARNE,UAQM,GARiB,EAQjB;AAAA,eAJdQ,UAIc,GAJD,CAIC;AAAA,eAHdC,UAGc,GAHD,CAGC;AAAA,eAFdC,KAEc,GAFG;AAAA;AAAA,mCAEH;AAEV;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,EAAtB,CAAyB;AAAA;AAAA,sCAAUC,cAAnC,EAAmD,KAAKA,cAAxD,EAAwE,IAAxE;AACA;AAAA;AAAA,4CAAaF,QAAb,CAAsBC,EAAtB,CAAyB;AAAA;AAAA,sCAAUE,aAAnC,EAAkD,KAAKA,aAAvD,EAAsE,IAAtE;AACH,SAtB2D,CAwB5D;;;AACAC,QAAAA,eAAe,CAACC,MAAD,EAAiBT,QAAgB,GAAG,CAApC,EAAuCU,QAAgB,GAAGC,IAAI,CAACC,GAAL,EAA1D,EAAsE;AACjF,eAAKT,KAAL,CAAWU,IAAX,GAAkB;AAAA;AAAA,4BAAKC,UAAL,CAAgBJ,QAAhB,CAAlB;AACA,cAAIhB,UAAU,GAAG;AAAA;AAAA,8BAAMqB,WAAN,CAAkBC,aAAlB,CAAgCC,GAAhC,CAAoCR,MAApC,CAAjB;;AACA,cAAIf,UAAU,IAAI,IAAlB,EAAwB;AACpB;AAAA;AAAA,oCAAQ,eAAR,EAA0B,kCAAiCe,MAAO,EAAlE;AACA;AACH;;AACD,eAAKd,WAAL,GAAmBD,UAAnB;AACA,eAAKM,QAAL,GAAgBA,QAAhB;AAEA;AAAA;AAAA,kCAAQkB,SAAR,CAAkBC,YAAlB,CAA+BV,MAA/B;AACH;;AAEmB,cAAdH,cAAc,GAAG;AACnB,gBAAM;AAAA;AAAA,8BAAMc,MAAN;AAAA;AAAA,qCAAN;AACA;AAAA;AAAA,oCAASC,IAAT,CAAc;AAAA;AAAA,0CAAYC,KAA1B;AACA;AAAA;AAAA,kCAAQC,gBAAR,CAAyBC,YAAzB,CAAsC;AAAA;AAAA,kCAAQC,SAAR,CAAkBC,gBAAlB,EAAtC;;AACA,eAAKC,cAAL,CAAoB,KAAKhC,WAAL,CAAkBiC,SAAtC;;AAEAlE,UAAAA,QAAQ,CAACmE,SAAT,CAAmB,MAAnB;AACH;;AAEDtB,QAAAA,aAAa,GAAG,CAEf,CAjD2D,CAmD5D;;;AACQoB,QAAAA,cAAc,CAACC,SAAD,EAAoB;AACtC,eAAKrC,cAAL,GAAsB;AAAA;AAAA,8BAAMwB,WAAN,CAAkBe,YAAlB,CAA+Bb,GAA/B,CAAmC,KAAnC,CAAtB;AAAiE;;AAAe;;AAChF,cAAI,KAAK1B,cAAL,IAAuB,IAA3B,EAAiC;AAC7B;AAAA;AAAA,oCAAQ,eAAR,EAA0B,qCAAoCqC,SAAU,EAAxE;AACA;AACH,WALqC,CAOtC;;;AACA,gBAAMG,cAAc,GAAG,KAAKC,gBAAL,CAAsB,KAAKzC,cAAL,CAAoB0C,YAA1C,EAAwD,KAAK1C,cAAL,CAAoB2C,eAA5E,EAA6F,KAAK3C,cAAL,CAAoB4C,QAAjH,CAAvB;;AACA,cAAIJ,cAAc,CAACK,MAAf,KAA0B,CAA9B,EAAiC;AAC7B;AAAA;AAAA,oCAAQ,eAAR,EAAyB,yBAAzB;AACA;AACH,WAZqC,CActC;;;AACA,eAAK3C,UAAL,GAAkB,EAAlB;;AACA,eAAK,MAAM4C,YAAX,IAA2BN,cAA3B,EAA2C;AACvC,kBAAMO,cAAc,GAAG;AAAA;AAAA,gCAAMvB,WAAN,CAAkBwB,eAAlB,CAAkCtB,GAAlC,CAAsCoB,YAAtC,CAAvB;;AACA,gBAAIC,cAAc,IAAI,IAAtB,EAA4B;AACxB;AAAA;AAAA,sCAAQ,eAAR,EAAyB,yBAAzB;AACA;AACH;;AAED,iBAAK7C,UAAL,CAAgB+C,IAAhB,CAAqB,GAAG,KAAKR,gBAAL,CAAsBM,cAAc,CAACG,UAArC,EAAiDH,cAAc,CAACI,cAAhE,EAAgFJ,cAAc,CAACK,WAA/F,CAAxB;;AACA,iBAAKlD,UAAL,CAAgB+C,IAAhB,CAAqB,GAAG,KAAKR,gBAAL,CAAsBM,cAAc,CAACM,UAArC,EAAiDN,cAAc,CAACO,cAAhE,EAAgFP,cAAc,CAACQ,WAA/F,CAAxB;AACH;;AACD;AAAA;AAAA,kCAAQ,eAAR,EAA0B,gBAAe,KAAKrD,UAAW,EAAzD;AACH;;AAEDsD,QAAAA,SAAS,GAAG;AACR;AAAA;AAAA,kCAAQC,YAAR,CAAqBD,SAArB;AACA;AAAA;AAAA,kCAAQE,WAAR,CAAoBF,SAApB;AACA;AAAA;AAAA,kCAAQG,WAAR,CAAoBC,KAApB;AACA;AAAA;AAAA,kCAAQ5B,gBAAR,CAAyBwB,SAAzB;AACA;AAAA;AAAA,wCAAWK,QAAX,CAAqBD,KAArB;AACA;AAAA;AAAA,wCAAWC,QAAX,CAAqBC,KAArB;AACA;AAAA;AAAA,kCAAQC,iBAAR,CAA0BD,KAA1B;AACA;AAAA;AAAA,kCAAQE,gBAAR,CAAyBJ,KAAzB;AACA;AAAA;AAAA,4CAAaK,OAAb;AACA,eAAK1D,WAAL,GAAmB,KAAnB;AACH;;AAED2D,QAAAA,QAAQ,GAAG;AACP,eAAK1D,SAAL,GAAiB,CAAjB;AACA,eAAKD,WAAL,GAAmB,KAAnB;AACA,eAAKD,aAAL,GAAqB,KAArB;AAEA;AAAA;AAAA,kCAAQ0B,gBAAR,CAAyBkC,QAAzB;AACA;AAAA;AAAA,kCAAQF,gBAAR,CAAyBJ,KAAzB;AACA;AAAA;AAAA,kCAAQD,WAAR,CAAoBC,KAApB;AACA;AAAA;AAAA,kCAAQH,YAAR,CAAqBS,QAArB;AACA;AAAA;AAAA,kCAAQR,WAAR,CAAoBQ,QAApB;AACH;AAED;AACJ;AACA;;;AACyB,cAAfC,eAAe,GAAG;AACpB,eAAKxD,UAAL,GADoB,CAEpB;AACA;;AACA,cAAI,KAAKA,UAAL,IAAmB,KAAKD,UAA5B,EAAwC;AACpC,kBAAM;AAAA;AAAA,gCAAM0D,OAAN;AAAA;AAAA,uCAAN;AACA;AAAA;AAAA,8CAAavD,QAAb,CAAsBiB,IAAtB,CAA2B;AAAA;AAAA,wCAAUuC,WAArC;AACA,iBAAKC,UAAL;AACH;AACJ;;AAEDC,QAAAA,YAAY,CAACC,KAAD,EAAgB;AACxB,eAAK9D,UAAL,IAAmB8D,KAAnB;AACH;;AAEDC,QAAAA,YAAY,GAAG;AACX;AAAA;AAAA,kCAAQzC,gBAAR,CAAyB0C,OAAzB;AACA;AAAA;AAAA,kCAAQX,iBAAR,CAA0BY,OAA1B,GAFW,CAEyB;;AACpC;AAAA;AAAA,wCAAWd,QAAX,CAAqBe,QAArB,GAHW,CAGqB;;AAChC;AAAA;AAAA,kCAAQnB,YAAR,CAAqBkB,OAArB,GAJW,CAIoB;;AAC/B;AAAA;AAAA,kCAAQjB,WAAR,CAAoBiB,OAApB,GALW,CAKmB;AACjC;;AAEDL,QAAAA,UAAU,GAAG;AACT;AAAA;AAAA,kCAAQtC,gBAAR,CAAyB6C,SAAzB,CAAoCC,OAApC;AACA;AAAA;AAAA,4CAAaC,IAAb,CAAkB,IAAI3G,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe;AAAA;AAAA,sCAAU4G,eAAzB,EAA0C;AAAA;AAAA,sCAAUC,UAApD,CAAlB;AACH;;AAEDC,QAAAA,SAAS,GAAG;AACR,eAAK5E,aAAL,GAAqB,IAArB;AACA;AAAA;AAAA,4CAAaO,QAAb,CAAsBiB,IAAtB,CAA2B;AAAA;AAAA,sCAAUqD,eAArC;AACH;;AAEDC,QAAAA,WAAW,GAAG;AACV,cAAI,KAAK9E,aAAL,IAAsB,CAAC,KAAKC,WAAhC,EAA6C;AACzC,iBAAKA,WAAL,GAAmB,IAAnB;AACA;AAAA;AAAA,8CAAaM,QAAb,CAAsBiB,IAAtB,CAA2B;AAAA;AAAA,wCAAUuD,SAArC;AACA;AAAA;AAAA,oCAAQ1B,WAAR,CAAoB2B,SAApB;AACA;AAAA;AAAA,oCAAQtB,gBAAR,CAAyBsB,SAAzB;AAEA;AAAA;AAAA,oCAAQtD,gBAAR,CAAyB6C,SAAzB,CAAoCU,MAApC;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,eAAe,CAACC,EAAD,EAAa;AACxBA,UAAAA,EAAE,GAAGA,EAAE,GAAG,KAAKjF,SAAf;;AACA,cAAI;AAAA;AAAA,kCAAQwD,gBAAR,CAAyB0B,UAAzB,EAAJ,EAA2C;AACvC,gBAAI;AAAA;AAAA,oCAAQC,gBAAZ,EAA8B;AAC1B;AAAA;AAAA,sCAAQA,gBAAR,CAAyBC,WAAzB,GAAuC,IAAvC;AACH;;AACD;AACH;;AAED,cAAI;AAAA;AAAA,kCAAQ5B,gBAAR,CAAyB6B,UAAzB,MAAyC;AAAA;AAAA,kCAAQ7B,gBAAR,CAAyB8B,cAAzB,EAA7C,EAAwF;AAAA;;AACpF;AAAA;AAAA,oCAAQC,eAAR,CAAwBC,QAAxB,IAAoCP,EAApC;AACA;AAAA;AAAA,0CAAW5B,QAAX,uBAAqB2B,eAArB,CAAqCC,EAArC;AACA;AAAA;AAAA,oCAAQE,gBAAR,CAAyBH,eAAzB,CAAyCC,EAAzC;AACA;AAAA;AAAA,oCAAQzD,gBAAR,CAAyBwD,eAAzB,CAAyCC,EAAzC;AACA;AAAA;AAAA,oCAAQ9B,WAAR,CAAoB6B,eAApB,CAAoCC,EAApC;AACA;AAAA;AAAA,oCAAQhC,YAAR,CAAqB+B,eAArB,CAAqCC,EAArC;AACA;AAAA;AAAA,oCAAQ/B,WAAR,CAAoB8B,eAApB,CAAoCC,EAApC;AACA;AAAA;AAAA,oCAAQzB,gBAAR,CAAyBwB,eAAzB,CAAyCC,EAAzC,EARoF,CAUpF;;AACA;AAAA;AAAA,8CAAaQ,IAAb,CAAkBR,EAAlB;AAEA;AAAA;AAAA,oCAAQS,gBAAR,CAAyBV,eAAzB,CAAyCC,EAAzC;AACH,WAdD,MAcO,IAAI;AAAA;AAAA,kCAAQE,gBAAZ,EAA8B;AACjC;AAAA;AAAA,oCAAQA,gBAAR,CAAyBC,WAAzB,GAAuC,IAAvC;AACH;AACJ;;AAEDO,QAAAA,aAAa,CAACC,OAAD,EAAmB;AAAA;;AAC5B,cAAIA,OAAJ,EAAa;AACT,iBAAKhB,WAAL;AACA,iBAAK5E,SAAL,GAAiB,CAAjB;AACH,WAHD,MAGO;AACH,iBAAKA,SAAL,GAAiB,GAAjB;AACH;;AACD;AAAA;AAAA,kCAAQiD,YAAR,CAAqB4C,YAArB,CAAkC,KAAK7F,SAAvC;AACA;AAAA;AAAA,kCAAQkD,WAAR,CAAoB2C,YAApB,CAAiC,KAAK7F,SAAtC;AACA;AAAA;AAAA,kCAAQwB,gBAAR,CAAyB6C,SAAzB,CAAoCwB,YAApC,CAAiD,KAAK7F,SAAtD;AACA;AAAA;AAAA,oCAASqD,QAAT,wBAAmByC,WAAnB,CAAgCH,aAAhC,CAA8CC,OAA9C;AACH;AAED;AACJ;AACA;;;AACIG,QAAAA,YAAY,GAAG;AACX;AAAA;AAAA,kCAAQvC,gBAAR,CAAyBwC,UAAzB;AACA;AAAA;AAAA,kCAAQxE,gBAAR,CAAyByE,MAAzB;AACH;;AAEDC,QAAAA,UAAU,CAACC,KAAD,EAAiB;AACvB,cAAIA,KAAJ,EAAW;AACP,gBAAI,KAAKC,cAAL,EAAJ,EAA2B;AAAC;AACxB;AAAA;AAAA,kCAAM/E,MAAN;AAAA;AAAA,sCAAsB,MAAM;AACxB,qBAAKgF,eAAL;AACH,eAFD;AAGA;AACH;AACJ,WAPD,MAOO;AACH;AAAA;AAAA,oCAAQ7C,gBAAR,CAAyB8C,SAAzB;;AACA,gBAAI;AAAA;AAAA,oCAAQ9E,gBAAR,CAAyB+E,cAAzB,EAAJ,EAA+C;AAAC;AAC5C;AAAA;AAAA,kCAAMlF,MAAN;AAAA;AAAA;AACA;AACH;AACJ;;AACD;AAAA;AAAA,kCAAQG,gBAAR,CAAyB6C,SAAzB,CAAoCmC,MAApC,CAA4CC,MAA5C,GAAqD,KAArD;AACA,eAAKC,SAAL;AACA;AAAA;AAAA,oCAASrD,QAAT,CAAmBsD,cAAnB,CAAkCR,KAAlC;AACA;AAAA;AAAA,kCAAQhF,SAAR,CAAkByF,UAAlB,CAA6B;AAAA;AAAA,kCAAQrB,eAAR,CAAwBsB,iBAAxB,EAA7B,EAA0E;AAAA;AAAA,kCAAQtB,eAAR,CAAwBuB,sBAAxB,EAA1E;AACH;;AAEDV,QAAAA,cAAc,GAAG;AACb,cAAI,KAAKxG,WAAL,CAAkBmH,QAAlB,IAA8B;AAAA;AAAA,oCAASC,OAA3C,EAAoD;AAChD,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAK/G,QAAL,GAAgB,CAAhB,IAAqB,KAAKT,cAAL,CAAqByH,UAAjD;AACH;AACD;AACJ;AACA;;;AACIZ,QAAAA,eAAe,GAAG;AACd,eAAK3C,QAAL;AACA,eAAKzD,QAAL,IAAiB,CAAjB;AACA,eAAK6D,UAAL;AACH;AAED;AACJ;AACA;;;AACI4C,QAAAA,SAAS,GAAG;AAAA;;AACR;AAAA;AAAA,4CAAajD,OAAb,CAAqB,KAArB,EAA4B,KAA5B;AACA;AAAA;AAAA,oCAASJ,QAAT,wBAAmByC,WAAnB,CAAgC1C,KAAhC;AACA;AAAA;AAAA,kCAAQI,gBAAR,CAAyB0D,QAAzB;AACH;;AAGe,cAAVC,UAAU,GAAG;AACf,eAAKnE,SAAL;AACA;AAAA;AAAA,8BAAMY,OAAN;AAAA;AAAA;AACA,gBAAM;AAAA;AAAA,8BAAMvC,MAAN;AAAA;AAAA,+BAAN;AACA,gBAAM;AAAA;AAAA,8BAAMA,MAAN;AAAA;AAAA,mCAAN;AACA,gBAAM;AAAA;AAAA,8BAAMA,MAAN;AAAA;AAAA,6BAAN;AACA1D,UAAAA,QAAQ,CAACmE,SAAT,CAAmB,MAAnB;AACH;;AAEDsF,QAAAA,gBAAgB,CAACC,GAAD,EAAc,CAC1B;AACA;AACA;AACA;AACA;AACA;AACH;;AAEDC,QAAAA,aAAa,GAAG,CACZ;AACA;AACH;AACD;AACJ;AACA;;;AACIC,QAAAA,cAAc,GAAG;AACb;AAAA;AAAA,kCAAQ/F,gBAAR,CAAyB6C,SAAzB,CAAoCmD,aAApC,CAAkD,IAAlD;AACA;AAAA;AAAA,kCAAQhG,gBAAR,CAAyB6C,SAAzB,CAAoCoD,WAApC,CAAgD,IAAhD;AACA;AAAA;AAAA,kCAAQvE,WAAR,CAAoBqE,cAApB;AACH;;AAEDG,QAAAA,MAAM,GAAW;AACb,iBAAO,KAAKtH,KAAL,CAAWsH,MAAX,EAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACYzF,QAAAA,gBAAgB,CAAC0F,MAAD,EAAyB3D,KAAzB,EAAwC5B,QAAxC,EAA2E;AAC/F,cAAIuF,MAAM,CAACtF,MAAP,KAAkB,CAAlB,IAAuB2B,KAAK,IAAI,CAApC,EAAuC,OAAO,EAAP;AAEvC,gBAAM4D,OAAiB,GAAG,EAA1B;;AACA,cAAIxF,QAAQ,KAAK9C,aAAa,CAACuI,WAA/B,EAA4C;AACxC;AACA,kBAAMC,WAAW,GAAGH,MAAM,CAACI,MAAP,CAAc,CAACC,GAAD,EAAMC,IAAN,KAAeD,GAAG,GAAGC,IAAI,CAACC,MAAxC,EAAgD,CAAhD,CAApB,CAFwC,CAIxC;;AACA,gBAAIJ,WAAW,KAAK,CAApB,EAAuB;AACnB,mBAAK,IAAIK,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGnE,KAApB,EAA2BmE,CAAC,EAA5B,EAAgC;AAC5B,sBAAMC,WAAW,GAAGC,IAAI,CAACC,KAAL,CAAW;AAAA;AAAA,wCAAQC,aAAR,CAAsBb,MAAtB,KAAiCC,MAAM,CAACtF,MAAnD,CAApB;AACAuF,gBAAAA,OAAO,CAACnF,IAAR,CAAakF,MAAM,CAACS,WAAD,CAAN,CAAoBI,EAAjC;AACH;;AACD,qBAAOZ,OAAP;AACH,aAXuC,CAaxC;;;AACA,iBAAK,IAAIO,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGnE,KAApB,EAA2BmE,CAAC,EAA5B,EAAgC;AAC5B;AACA,oBAAMM,WAAW,GAAG;AAAA;AAAA,sCAAQF,aAAR,CAAsBb,MAAtB,KAAiCI,WAArD,CAF4B,CAI5B;;AACA,kBAAIY,gBAAgB,GAAG,CAAvB;;AACA,mBAAK,MAAMT,IAAX,IAAmBN,MAAnB,EAA2B;AACvBe,gBAAAA,gBAAgB,IAAIT,IAAI,CAACC,MAAzB;;AACA,oBAAIO,WAAW,GAAGC,gBAAlB,EAAoC;AAChCd,kBAAAA,OAAO,CAACnF,IAAR,CAAawF,IAAI,CAACO,EAAlB;AACA;AACH;AACJ;AACJ;AACJ,WA5BD,MA4BO,IAAIpG,QAAQ,KAAK9C,aAAa,CAACqJ,gBAA/B,EAAiD;AACpD;AACA,kBAAMb,WAAW,GAAGH,MAAM,CAACI,MAAP,CAAc,CAACC,GAAD,EAAMC,IAAN,KAAeD,GAAG,GAAGC,IAAI,CAACC,MAAxC,EAAgD,CAAhD,CAApB,CAFoD,CAIpD;;AACA,gBAAIJ,WAAW,KAAK,CAApB,EAAuB;AACnB,mBAAK,IAAIK,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGnE,KAApB,EAA2BmE,CAAC,EAA5B,EAAgC;AAC5B,oBAAIC,WAAW,GAAGC,IAAI,CAACC,KAAL,CAAW;AAAA;AAAA,wCAAQC,aAAR,CAAsBb,MAAtB,KAAiCC,MAAM,CAACtF,MAAnD,CAAlB,CAD4B,CAE5B;;AACA,oBAAI8F,CAAC,GAAG,CAAJ,IAASR,MAAM,CAACS,WAAD,CAAN,CAAoBI,EAApB,KAA2BZ,OAAO,CAACO,CAAC,GAAG,CAAL,CAA/C,EAAwD;AACpD;AACAC,kBAAAA,WAAW,GAAG,CAACA,WAAW,GAAG,CAAf,IAAoBT,MAAM,CAACtF,MAAzC;AACH;;AACDuF,gBAAAA,OAAO,CAACnF,IAAR,CAAakF,MAAM,CAACS,WAAD,CAAN,CAAoBI,EAAjC;AACH;;AACD,qBAAOZ,OAAP;AACH,aAhBmD,CAkBpD;;;AACA,kBAAMgB,QAAQ,GAAG,CAAC,GAAGjB,MAAJ,CAAjB;AAEA,gBAAIkB,cAAc,GAAG,CAAC,CAAtB;;AACA,iBAAK,IAAIV,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAInE,KAArB,EAA4BmE,CAAC,EAA7B,EAAiC;AAC7B;AACA,kBAAIU,cAAc,KAAK,CAAC,CAAxB,EAA2B;AACvB,sBAAMC,iBAAiB,GAAGF,QAAQ,CAACG,SAAT,CAAmBd,IAAI,IAAIA,IAAI,CAACO,EAAL,KAAYK,cAAvC,CAA1B;;AACA,oBAAIC,iBAAiB,KAAK,CAAC,CAAvB,IAA4BA,iBAAiB,GAAGF,QAAQ,CAACvG,MAAT,GAAkB,CAAtE,EAAyE;AACrE;AACA,mBAACuG,QAAQ,CAACE,iBAAD,CAAT,EAA8BF,QAAQ,CAACE,iBAAiB,GAAG,CAArB,CAAtC,IACI,CAACF,QAAQ,CAACE,iBAAiB,GAAG,CAArB,CAAT,EAAkCF,QAAQ,CAACE,iBAAD,CAA1C,CADJ;AAEH;AACJ,eAT4B,CAW7B;;;AACA,oBAAML,WAAW,GAAG;AAAA;AAAA,sCAAQF,aAAR,CAAsBb,MAAtB,KAAiCI,WAArD,CAZ6B,CAc7B;;AACA,kBAAIY,gBAAgB,GAAG,CAAvB;AACA,kBAAIM,aAAa,GAAG,CAAC,CAArB;;AAEA,mBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGL,QAAQ,CAACvG,MAA7B,EAAqC4G,CAAC,EAAtC,EAA0C;AACtCP,gBAAAA,gBAAgB,IAAIE,QAAQ,CAACK,CAAD,CAAR,CAAYf,MAAhC;;AACA,oBAAIO,WAAW,GAAGC,gBAAlB,EAAoC;AAChCM,kBAAAA,aAAa,GAAGC,CAAhB;AACA;AACH;AACJ,eAxB4B,CA0B7B;;;AACA,kBAAID,aAAa,KAAK,CAAC,CAAvB,EAA0B;AACtBA,gBAAAA,aAAa,GAAGJ,QAAQ,CAACvG,MAAT,GAAkB,CAAlC;AACH,eA7B4B,CA+B7B;;;AACA,oBAAM6G,UAAU,GAAGN,QAAQ,CAACI,aAAD,CAAR,CAAwBR,EAA3C;AACAZ,cAAAA,OAAO,CAACnF,IAAR,CAAayG,UAAb,EAjC6B,CAmC7B;;AACAL,cAAAA,cAAc,GAAGK,UAAjB;AACH;AACJ,WA5DM,MA4DA,IAAI9G,QAAQ,KAAK9C,aAAa,CAAC6J,KAA/B,EAAsC;AACzC;AACA,gBAAIC,YAAY,GAAG,CAAnB;;AAEA,iBAAK,IAAIjB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGnE,KAApB,EAA2BmE,CAAC,EAA5B,EAAgC;AAC5B;AACA,kBAAIR,MAAM,CAACyB,YAAD,CAAN,CAAqBZ,EAArB,KAA4B,CAAhC,EAAmC;AAC/BY,gBAAAA,YAAY,GAAG,CAAf,CAD+B,CAG/B;;AACA,uBAAOA,YAAY,GAAGzB,MAAM,CAACtF,MAAtB,IAAgCsF,MAAM,CAACyB,YAAD,CAAN,CAAqBZ,EAArB,KAA4B,CAAnE,EAAsE;AAClEY,kBAAAA,YAAY;AACf,iBAN8B,CAQ/B;;;AACA,oBAAIA,YAAY,IAAIzB,MAAM,CAACtF,MAA3B,EAAmC;AAC/B;AACH;AACJ,eAd2B,CAgB5B;;;AACAuF,cAAAA,OAAO,CAACnF,IAAR,CAAakF,MAAM,CAACyB,YAAD,CAAN,CAAqBZ,EAAlC,EAjB4B,CAmB5B;;AACAY,cAAAA,YAAY,GApBgB,CAsB5B;;AACA,kBAAIA,YAAY,IAAIzB,MAAM,CAACtF,MAA3B,EAAmC;AAC/B+G,gBAAAA,YAAY,GAAG,CAAf;AACH;AACJ;AACJ;;AAED,iBAAOxB,OAAP;AACH;;AAta2D,O", "sourcesContent": ["\r\nimport { director, Rect } from \"cc\";\r\nimport { logInfo, logWarn } from \"db://assets/scripts/utils/Logger\";\r\nimport Long from \"long\";\r\nimport { GameConst } from \"../../../../../scripts/core/base/GameConst\";\r\nimport { SingletonBase } from \"../../../../../scripts/core/base/SingletonBase\";\r\nimport { UIMgr } from \"../../../../../scripts/core/base/UIMgr\";\r\nimport { MyApp } from \"../../app/MyApp\";\r\nimport { ModeType, randStrategy, ResChapter, ResGameMode } from \"../../autogen/luban/schema\";\r\nimport { DataMgr } from \"../../data/DataManager\";\r\nimport EventManager, { EventMgr } from \"../../event/EventManager\";\r\nimport { HomeUIEvent } from \"../../event/HomeUIEvent\";\r\nimport { GameReviveUI } from \"../../ui/gameui/game/GameReviveUI\";\r\nimport { MBoomUI } from \"../../ui/gameui/game/MBoomUI\";\r\nimport { LoadingUI } from \"../../ui/gameui/LoadingUI\";\r\nimport { BottomUI } from \"../../ui/home/<USER>\";\r\nimport { RogueUI } from \"../../ui/home/<USER>/RogueUI\";\r\nimport { HomeUI } from \"../../ui/home/<USER>\";\r\nimport { TopUI } from \"../../ui/home/<USER>\";\r\nimport { BulletSystem } from \"../bullet/BulletSystem\";\r\nimport { GameEvent } from \"../event/GameEvent\";\r\nimport { GameIns } from \"../GameIns\";\r\nimport GameMapRun from \"../ui/map/GameMapRun\";\r\nimport { lcgRand } from \"../utils/Rand\";\r\nimport { GameMain } from \"../scenes/GameMain\";\r\n\r\nenum RAND_STRATEGY {\r\n    WEIGHT_PRUE = 1, // 纯权重随机\r\n    WEIGHT_NO_REPEAT = 2, //权重随机，不重复\r\n    ORDER = 3 // 按顺序\r\n}\r\n\r\nexport class BattleManager extends SingletonBase<BattleManager> {\r\n\r\n    initBattleEnd: boolean = false;\r\n    isGameStart: boolean = false;\r\n    animSpeed: number = 1;\r\n\r\n    _modeConfig: ResGameMode | null = null;\r\n    curLevel: number = 0;//小阶段\r\n    _chapterConfig: ResChapter | null = null;\r\n    public get chapterConfig(): ResChapter | null { return this._chapterConfig; }\r\n    private _levelList: number[] = []; // 随机出来的关卡列表（这个列表长度可能会大于章节表中的关卡数量）\r\n    public get levelList(): number[] { return this._levelList; }\r\n    public get modeConfig(): ResGameMode | null { return this._modeConfig; }\r\n\r\n    _loadTotal = 0;\r\n    _loadCount = 0;\r\n    _rand: lcgRand = new lcgRand();\r\n\r\n    constructor() {\r\n        super();\r\n        EventManager.Instance.on(GameEvent.onNetGameStart, this.onNetGameStart, this);\r\n        EventManager.Instance.on(GameEvent.onNetGameOver, this.onNetGameOver, this);\r\n    }\r\n\r\n    //战斗开始接口\r\n    startGameByMode(modeID: number, curLevel: number = 0, randSeed: number = Date.now()) {\r\n        this._rand.seed = Long.fromNumber(randSeed);\r\n        let modeConfig = MyApp.lubanTables.TbResGameMode.get(modeID);\r\n        if (modeConfig == null) {\r\n            logWarn(\"BattleManager\", `can not find mode config by id ${modeID}`);\r\n            return;\r\n        }\r\n        this._modeConfig = modeConfig;\r\n        this.curLevel = curLevel;\r\n\r\n        DataMgr.gameLogic.cmdGameStart(modeID);\r\n    }\r\n\r\n    async onNetGameStart() {\r\n        await UIMgr.openUI(LoadingUI)\r\n        EventMgr.emit(HomeUIEvent.Leave)\r\n        GameIns.mainPlaneManager.setPlaneData(DataMgr.planeInfo.getPlaneInfoById());\r\n        this._initLevelList(this._modeConfig!.chapterID);\r\n\r\n        director.loadScene(\"Game\")\r\n    }\r\n\r\n    onNetGameOver() {\r\n\r\n    }\r\n\r\n    // 根据策略随机出关卡列表\r\n    private _initLevelList(chapterID: number) {\r\n        this._chapterConfig = MyApp.lubanTables.TbResChapter.get(70001)!;/*(chapterID)*/;\r\n        if (this._chapterConfig == null) {\r\n            logWarn(\"BattleManager\", `can not find chapter config by id ${chapterID}`);\r\n            return;\r\n        }\r\n\r\n        // 随机出关卡组\r\n        const levelGroupList = this._randomSelection(this._chapterConfig.strategyList, this._chapterConfig.levelGroupCount, this._chapterConfig.strategy);\r\n        if (levelGroupList.length === 0) {\r\n            logWarn('BattleManager', \" levelGroupList is null\");\r\n            return;\r\n        }\r\n\r\n        // 随机出关卡\r\n        this._levelList = [];\r\n        for (const levelGroupID of levelGroupList) {\r\n            const levelGroupData = MyApp.lubanTables.TbResLevelGroup.get(levelGroupID);\r\n            if (levelGroupData == null) {\r\n                logWarn('BattleManager', \" levelGroupData is null\");\r\n                continue;\r\n            }\r\n\r\n            this._levelList.push(...this._randomSelection(levelGroupData.normSTList, levelGroupData.normLevelCount, levelGroupData.normLevelST));\r\n            this._levelList.push(...this._randomSelection(levelGroupData.bossSTList, levelGroupData.bossLevelCount, levelGroupData.bossLevelST));\r\n        }\r\n        logInfo('BattleManager', ` _levelList: ${this._levelList}`);\r\n    }\r\n\r\n    mainReset() {\r\n        GameIns.enemyManager.mainReset();\r\n        GameIns.bossManager.mainReset();\r\n        GameIns.waveManager.reset();\r\n        GameIns.mainPlaneManager.mainReset();\r\n        GameMapRun.instance!.reset();\r\n        GameMapRun.instance!.clear();\r\n        GameIns.hurtEffectManager.clear();\r\n        GameIns.gameStateManager.reset();\r\n        BulletSystem.destroy();\r\n        this.isGameStart = false;\r\n    }\r\n\r\n    subReset() {\r\n        this.animSpeed = 1;\r\n        this.isGameStart = false;\r\n        this.initBattleEnd = false;\r\n\r\n        GameIns.mainPlaneManager.subReset();\r\n        GameIns.gameStateManager.reset();\r\n        GameIns.waveManager.reset();\r\n        GameIns.enemyManager.subReset();\r\n        GameIns.bossManager.subReset();\r\n    }\r\n\r\n    /**\r\n     * 检查所有资源是否加载完成\r\n     */\r\n    async checkLoadFinish() {\r\n        this._loadCount++;\r\n        // let loadingUI = UIMgr.get(LoadingUI)\r\n        // loadingUI.updateProgress(this._loadCount / this._loadTotal)\r\n        if (this._loadCount >= this._loadTotal) {\r\n            await UIMgr.closeUI(LoadingUI)\r\n            EventManager.Instance.emit(GameEvent.GameLoadEnd)\r\n            this.initBattle();\r\n        }\r\n    }\r\n\r\n    addLoadCount(count: number) {\r\n        this._loadTotal += count;\r\n    }\r\n\r\n    startLoading() {\r\n        GameIns.mainPlaneManager.preload();\r\n        GameIns.hurtEffectManager.preLoad();//伤害特效资源\r\n        GameMapRun.instance!.initData();//地图背景初始化\r\n        GameIns.enemyManager.preLoad();//敌人资源\r\n        GameIns.bossManager.preLoad();//boss资源\r\n    }\r\n\r\n    initBattle() {\r\n        GameIns.mainPlaneManager.mainPlane!.planeIn();\r\n        BulletSystem.init(new Rect(0, 0, GameConst.ViewBattleWidth, GameConst.ViewHeight));\r\n    }\r\n\r\n    onPlaneIn() {\r\n        this.initBattleEnd = true;\r\n        EventManager.Instance.emit(GameEvent.GameMainPlaneIn)\r\n    }\r\n\r\n    beginBattle() {\r\n        if (this.initBattleEnd && !this.isGameStart) {\r\n            this.isGameStart = true;\r\n            EventManager.Instance.emit(GameEvent.GameStart)\r\n            GameIns.waveManager.gameStart();\r\n            GameIns.gameStateManager.gameStart();\r\n\r\n            GameIns.mainPlaneManager.mainPlane!.begine();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 更新游戏逻辑\r\n     * @param {number} dt 每帧的时间间隔\r\n     */\r\n    updateGameLogic(dt: number) {\r\n        dt = dt * this.animSpeed;\r\n        if (GameIns.gameStateManager.isGameOver()) {\r\n            if (GameIns.gamePlaneManager) {\r\n                GameIns.gamePlaneManager.enemyTarget = null;\r\n            }\r\n            return;\r\n        }\r\n\r\n        if (GameIns.gameStateManager.isInBattle() || GameIns.gameStateManager.isGameWillOver()) {\r\n            GameIns.gameDataManager.gameTime += dt;\r\n            GameMapRun.instance?.updateGameLogic(dt)\r\n            GameIns.gamePlaneManager.updateGameLogic(dt);\r\n            GameIns.mainPlaneManager.updateGameLogic(dt);\r\n            GameIns.waveManager.updateGameLogic(dt);\r\n            GameIns.enemyManager.updateGameLogic(dt);\r\n            GameIns.bossManager.updateGameLogic(dt);\r\n            GameIns.gameStateManager.updateGameLogic(dt);\r\n\r\n            //子弹发射器系统\r\n            BulletSystem.tick(dt);\r\n\r\n            GameIns.fColliderManager.updateGameLogic(dt);\r\n        } else if (GameIns.gamePlaneManager) {\r\n            GameIns.gamePlaneManager.enemyTarget = null;\r\n        }\r\n    }\r\n\r\n    setTouchState(isTouch: boolean) {\r\n        if (isTouch) {\r\n            this.beginBattle();\r\n            this.animSpeed = 1;\r\n        } else {\r\n            this.animSpeed = 0.2;\r\n        }\r\n        GameIns.enemyManager.setAnimSpeed(this.animSpeed);\r\n        GameIns.bossManager.setAnimSpeed(this.animSpeed);\r\n        GameIns.mainPlaneManager.mainPlane!.setAnimSpeed(this.animSpeed);\r\n        GameMain.instance?.GameFightUI!.setTouchState(isTouch);\r\n    }\r\n\r\n    /**\r\n     * 战斗复活逻辑\r\n     */\r\n    relifeBattle() {\r\n        GameIns.gameStateManager.gameResume();\r\n        GameIns.mainPlaneManager.revive();\r\n    }\r\n\r\n    setGameEnd(isWin: boolean) {\r\n        if (isWin) {\r\n            if (this.checkNextlevel()) {//判断是否有下一关\r\n                UIMgr.openUI(RogueUI, () => {\r\n                    this.startNextBattle();\r\n                });\r\n                return;\r\n            }\r\n        } else {\r\n            GameIns.gameStateManager.gamePause();\r\n            if (GameIns.mainPlaneManager.checkCanRevive()) {// 判断是否可以复活\r\n                UIMgr.openUI(GameReviveUI);\r\n                return;\r\n            }\r\n        }\r\n        GameIns.mainPlaneManager.mainPlane!.hpNode!.active = false;\r\n        this.endBattle();\r\n        GameMain.instance!.showGameResult(isWin);\r\n        DataMgr.gameLogic.cmdGameEnd(GameIns.gameDataManager.getGameResultData(), GameIns.gameDataManager.getGameLevelResultData());\r\n    }\r\n\r\n    checkNextlevel() {\r\n        if (this._modeConfig!.modeType == ModeType.ENDLESS) {\r\n            return true;\r\n        }\r\n        return this.curLevel + 1 <= this._chapterConfig!.levelCount;\r\n    }\r\n    /**\r\n     * 继续下一场战斗\r\n     */\r\n    startNextBattle() {\r\n        this.subReset();\r\n        this.curLevel += 1;\r\n        this.initBattle();\r\n    }\r\n\r\n    /**\r\n     * 结束战斗\r\n     */\r\n    endBattle() {\r\n        BulletSystem.destroy(false, false);\r\n        GameMain.instance?.GameFightUI!.reset();\r\n        GameIns.gameStateManager.gameOver();\r\n    }\r\n\r\n\r\n    async quitBattle() {\r\n        this.mainReset();\r\n        UIMgr.closeUI(MBoomUI)\r\n        await UIMgr.openUI(HomeUI)\r\n        await UIMgr.openUI(BottomUI)\r\n        await UIMgr.openUI(TopUI)\r\n        director.loadScene(\"Main\");\r\n    }\r\n\r\n    bossChangeFinish(tip: string) {\r\n        // const bossEnterDialog = GameIns.uiManager.getDialog(BossEnterDialog.default);\r\n        // if (bossEnterDialog) {\r\n        //     bossEnterDialog.node.active = true;\r\n        //     GameIns.mainPlaneManager.moveAble = false;\r\n        //     bossEnterDialog.showTips(bossName);\r\n        // }\r\n    }\r\n\r\n    bossWillEnter() {\r\n        // GameIns.mainPlaneManager.mainPlane!.setFireEnable(false);\r\n        // GameIns.mainPlaneManager.moveAble = false;\r\n    }\r\n    /**\r\n     * 开始Boss战斗\r\n     */\r\n    bossFightStart() {\r\n        GameIns.mainPlaneManager.mainPlane!.setFireEnable(true);\r\n        GameIns.mainPlaneManager.mainPlane!.setMoveAble(true);\r\n        GameIns.bossManager.bossFightStart();\r\n    }\r\n\r\n    random(): number {\r\n        return this._rand.random();\r\n    }\r\n\r\n    /**\r\n     * 策略：\r\n     * 1.严格按权重比例随机选择元素\r\n     * 2.严格按权重比例随机选择元素，不重复\r\n     * 3.按顺序选择元素\r\n     * @param STList 带权重的元素数组\r\n     * @param count 需要选择的元素数量\r\n     * @returns 选中元素的ID数组\r\n     */\r\n    private _randomSelection(STList: randStrategy[], count: number, strategy: RAND_STRATEGY): number[] {\r\n        if (STList.length === 0 || count <= 0) return [];\r\n\r\n        const results: number[] = [];\r\n        if (strategy === RAND_STRATEGY.WEIGHT_PRUE) {\r\n            // 计算总权重\r\n            const totalWeight = STList.reduce((sum, item) => sum + item.Weight, 0);\r\n\r\n            // 如果所有权重都为0，则转为均匀随机\r\n            if (totalWeight === 0) {\r\n                for (let i = 0; i < count; i++) {\r\n                    const randomIndex = Math.floor(GameIns.battleManager.random() * STList.length);\r\n                    results.push(STList[randomIndex].ID);\r\n                }\r\n                return results;\r\n            }\r\n\r\n            // 严格按权重比例随机选择\r\n            for (let i = 0; i < count; i++) {\r\n                // 生成[0, totalWeight)区间的随机数\r\n                const randomValue = GameIns.battleManager.random() * totalWeight;\r\n\r\n                // 遍历查找随机数对应的元素\r\n                let cumulativeWeight = 0;\r\n                for (const item of STList) {\r\n                    cumulativeWeight += item.Weight;\r\n                    if (randomValue < cumulativeWeight) {\r\n                        results.push(item.ID);\r\n                        break;\r\n                    }\r\n                }\r\n            }\r\n        } else if (strategy === RAND_STRATEGY.WEIGHT_NO_REPEAT) {\r\n            // 计算总权重\r\n            const totalWeight = STList.reduce((sum, item) => sum + item.Weight, 0);\r\n\r\n            // 如果所有权重都为0，则转为均匀随机\r\n            if (totalWeight === 0) {\r\n                for (let i = 0; i < count; i++) {\r\n                    let randomIndex = Math.floor(GameIns.battleManager.random() * STList.length);\r\n                    // 避免重复选择相同的ID\r\n                    if (i > 0 && STList[randomIndex].ID === results[i - 1]) {\r\n                        // 如果与上一次选择的相同，选择下一个（循环）\r\n                        randomIndex = (randomIndex + 1) % STList.length;\r\n                    }\r\n                    results.push(STList[randomIndex].ID);\r\n                }\r\n                return results;\r\n            }\r\n\r\n            // 创建副本以避免修改原始数据\r\n            const tempList = [...STList];\r\n\r\n            let lastSelectedId = -1;\r\n            for (let i = 0; i <= count; i++) {\r\n                // 如果上一次选择的ID存在，且它在当前列表中，调整其位置\r\n                if (lastSelectedId !== -1) {\r\n                    const lastSelectedIndex = tempList.findIndex(item => item.ID === lastSelectedId);\r\n                    if (lastSelectedIndex !== -1 && lastSelectedIndex < tempList.length - 1) {\r\n                        // 将上一次选择的ID与下一个元素交换位置\r\n                        [tempList[lastSelectedIndex], tempList[lastSelectedIndex + 1]] =\r\n                            [tempList[lastSelectedIndex + 1], tempList[lastSelectedIndex]];\r\n                    }\r\n                }\r\n\r\n                // 生成[0, totalWeight)区间的随机数\r\n                const randomValue = GameIns.battleManager.random() * totalWeight;\r\n\r\n                // 遍历查找随机数对应的元素\r\n                let cumulativeWeight = 0;\r\n                let selectedIndex = -1;\r\n\r\n                for (let j = 0; j < tempList.length; j++) {\r\n                    cumulativeWeight += tempList[j].Weight;\r\n                    if (randomValue < cumulativeWeight) {\r\n                        selectedIndex = j;\r\n                        break;\r\n                    }\r\n                }\r\n\r\n                // 如果未找到有效索引，选择最后一个元素\r\n                if (selectedIndex === -1) {\r\n                    selectedIndex = tempList.length - 1;\r\n                }\r\n\r\n                // 获取选中的ID\r\n                const selectedId = tempList[selectedIndex].ID;\r\n                results.push(selectedId);\r\n\r\n                // 更新上一次选择的ID\r\n                lastSelectedId = selectedId;\r\n            }\r\n        } else if (strategy === RAND_STRATEGY.ORDER) {\r\n            // 按顺序选择元素，遇到ID为0时从数组开头重新开始\r\n            let currentIndex = 0;\r\n\r\n            for (let i = 0; i < count; i++) {\r\n                // 如果当前元素的ID为0，则重置到数组开头\r\n                if (STList[currentIndex].ID === 0) {\r\n                    currentIndex = 0;\r\n\r\n                    // 如果数组开头的元素ID也为0，则跳过所有ID为0的元素\r\n                    while (currentIndex < STList.length && STList[currentIndex].ID === 0) {\r\n                        currentIndex++;\r\n                    }\r\n\r\n                    // 如果所有元素ID都为0，则无法选择，跳出循环\r\n                    if (currentIndex >= STList.length) {\r\n                        break;\r\n                    }\r\n                }\r\n\r\n                // 选择当前元素\r\n                results.push(STList[currentIndex].ID);\r\n\r\n                // 移动到下一个元素\r\n                currentIndex++;\r\n\r\n                // 如果到达数组末尾，回到开头\r\n                if (currentIndex >= STList.length) {\r\n                    currentIndex = 0;\r\n                }\r\n            }\r\n        }\r\n\r\n        return results;\r\n    }\r\n\r\n}"]}