System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, ResolutionPolicy, view, GameConst, IMgr, _dec, _class, _crd, ccclass, property, GlobalDataManager;

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "db://assets/scripts/core/base/GameConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIMgr(extras) {
    _reporterNs.report("IMgr", "../../../../../scripts/core/base/IMgr", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      ResolutionPolicy = _cc.ResolutionPolicy;
      view = _cc.view;
    }, function (_unresolved_2) {
      GameConst = _unresolved_2.GameConst;
    }, function (_unresolved_3) {
      IMgr = _unresolved_3.IMgr;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "971a3KOX+5C6Kt/bNxAAq8l", "GlobalDataManager", undefined);

      __checkObsolete__(['_decorator', 'ResolutionPolicy', 'view']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("GlobalDataManager", GlobalDataManager = (_dec = ccclass("GlobalDataManager"), _dec(_class = class GlobalDataManager extends (_crd && IMgr === void 0 ? (_reportPossibleCrUseOfIMgr({
        error: Error()
      }), IMgr) : IMgr) {
        constructor(...args) {
          super(...args);
          this._chapterID = 0;
        }

        get chapterID() {
          return this._chapterID;
        }

        set chapterID(value) {
          this._chapterID = value;
        }

        init() {
          this.setUIResolution();
        }

        setUIResolution() {
          let fitType = ResolutionPolicy.SHOW_ALL;

          if (view.getVisibleSize().height / view.getVisibleSize().width * (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).designWidth >= (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).designHeight) {
            //高度大于 16:9
            fitType = ResolutionPolicy.FIXED_WIDTH; //宽度全部显示，高度做适配拉长
          } else {
            //宽屏,比如平板或者电脑
            fitType = ResolutionPolicy.SHOW_ALL; //两边黑边       //FIXED_HEIGHT// 全部铺满
          }

          view.setResolutionPolicy(fitType);
          view.resizeWithBrowserSize(true);
        }

        setBattleResolution() {
          // 设置分辨率适配策略
          let fitType = ResolutionPolicy.SHOW_ALL; // if (view.getVisibleSize().height / view.getVisibleSize().width * GameConst.designWidth >= GameConst.designHeight){//高度大于 16:9
          //     fitType = ResolutionPolicy.SHOW_ALL
          // }else{//宽屏,比如平板或者电脑
          //     fitType = ResolutionPolicy.FIXED_HEIGHT
          // }

          view.setResolutionPolicy(fitType);
          view.resizeWithBrowserSize(true);
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=2d356fec0aa65258fa1f53cbd10fc356b80e71e6.js.map