{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/LevelEditorBaseUI.ts"], "names": ["_decorator", "CCFloat", "Component", "Node", "CCString", "assetManager", "instantiate", "UITransform", "view", "Graphics", "Color", "Rect", "Label", "Vec3", "<PERSON>uat", "LayerRandomRange", "LayerSplicingMode", "LayerType", "LevelDataBackgroundLayer", "LevelDataLayer", "LevelDataRandTerrain", "LevelDataRandTerrains", "LevelDataRandTerrainsGroup", "LevelDataScroll", "LevelEditorLayerUI", "LayerEditorRandomRange", "LevelBackgroundLayer", "LevelEditorUtils", "<PERSON><PERSON><PERSON><PERSON>", "LevelRandTerrainsLayersUI", "LevelRandTerrainsLayerUI", "LevelRandTerrainUI", "RandTerrain", "WavePreview", "EmittierTerrain", "ccclass", "property", "executeInEditMode", "BackgroundsNodeName", "LevelEditorBaseUI", "type", "displayName", "min", "_totalHeight", "backgroundLayerNode", "floorLayersNode", "skyLayersNode", "_isLoadingScrollNodes", "_play", "_drawNode", "onLoad", "console", "log", "getOrAddNode", "node", "uuid", "update", "dt", "checkLayerNode", "floorLayers", "skyLayers", "setBackgroundNodePosition", "yOff", "height", "getComponent", "contentSize", "setPosition", "getVisibleSize", "tick", "progress", "i", "background<PERSON>ayer", "backgroundsNode", "children", "length", "bg", "backgrounds", "bgIndex", "prefab", "addComponent", "<PERSON><PERSON><PERSON><PERSON>", "position", "y", "removeFromParent", "totalTime", "speed", "for<PERSON>ach", "layer", "add<PERSON><PERSON>er", "parentNode", "name", "layerNode", "layerCom", "layers", "removeLayerNodes", "push", "find", "element", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "_checkScrollNode", "Random", "_checkRandTerrainNode", "<PERSON><PERSON><PERSON>", "_checkEmittierNode", "_updateEmittierNode", "data", "scrollsNode", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isCountMatch", "scrollLayers", "loadPromises", "scroll", "index", "scrollPrefabs", "loadPromise", "Promise", "resolve", "loadAny", "err", "loadedPrefab", "error", "all", "scrollNode", "totalHeight", "posOffsetY", "prefabIndex", "curPrefab", "child", "randomOffsetX", "Math", "random", "splicingOffsetX", "max", "offY", "splicingMode", "node_height", "fix_height", "random_height", "splicingOffsetY", "dynamicNode", "currentDynaNodes", "match", "parseInt", "randomLayers", "needRebuild", "rebuildList", "randTerrains", "dynaNode", "expected<PERSON><PERSON>dCount", "terrains", "dynamicTerrains", "dynamicTerrain", "childIndex", "isUUIDMatch", "terrain", "childNode", "childPrefabUUID", "_prefab", "asset", "_uuid", "terrainUUID", "terrainElement", "j", "k", "offSetX", "randomOffsetY", "offSetY", "emittierNode", "emittierLayers", "emitterData", "existingNode", "needReload", "oldPosition", "clone", "oldRotation", "rotation", "oldScale", "scale", "emitterNode", "setSiblingIndex", "lastIndex", "_playEmittierNode", "value", "emittier", "play", "initByLevelData", "levelname", "background", "remark", "initLayers", "instance", "reset", "_drawNodeGraphics", "dataLayers", "<PERSON><PERSON>ayer", "zIndex", "levelEditorLayerUI", "initScorllsByLevelData", "dynamics", "dynamic", "group", "x", "random<PERSON>ayer", "weight", "initEmittierLevelData", "_fillLevelLayerData", "dataLayer", "scrolls", "scroll<PERSON>ayer", "dataScroll", "scrollPrefab", "uuids", "datas", "terrainData", "fillLevelData", "_fillLevelLayersData", "sort", "a", "b", "playLevel", "bPlay", "_setTimeNode", "_playLayer", "_drawMask", "_drawMaskClear", "_randLayerActive", "getChildByName", "active", "groupIndex", "layerRandIndex", "groupRandIndex", "layersTotalWeight", "randomTerrain", "layersRandWeight", "layersAccWeight", "groupTotalWeight", "parent", "groupRandWeight", "groupAccWeight", "terrainNode", "itemIndex", "totalWeight", "randomWeight", "selectedIndex", "graphics", "drawTransform", "drawport", "getPosition", "width", "strokeColor", "BLUE", "lineWidth", "rect", "stroke", "graphicsView", "drawview", "RED", "maskGraphics", "<PERSON><PERSON><PERSON><PERSON>", "maskHeight", "fillColor", "BLACK", "fillRect", "timeNode", "progressNode", "string", "toFixed", "clear"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,O,OAAAA,O;AAASC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,Q,OAAAA,Q;AAAkBC,MAAAA,Y,OAAAA,Y;AAAcC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;;AAE1IC,MAAAA,gB,iBAAAA,gB;AAAkBC,MAAAA,iB,iBAAAA,iB;AAAmBC,MAAAA,S,iBAAAA,S;AAAsBC,MAAAA,wB,iBAAAA,wB;AAA0BC,MAAAA,c,iBAAAA,c;AAAgBC,MAAAA,oB,iBAAAA,oB;AAAsBC,MAAAA,qB,iBAAAA,qB;AAAuBC,MAAAA,0B,iBAAAA,0B;AAA4BC,MAAAA,e,iBAAAA,e;;AAC9KC,MAAAA,kB,iBAAAA,kB;;AACAC,MAAAA,sB,iBAAAA,sB;AAAwBC,MAAAA,oB,iBAAAA,oB;AAAsBC,MAAAA,gB,iBAAAA,gB;AAAkBC,MAAAA,U,iBAAAA,U;AAAYC,MAAAA,yB,iBAAAA,yB;AAA2BC,MAAAA,wB,iBAAAA,wB;AAA0BC,MAAAA,kB,iBAAAA,kB;;AACjIC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,e,iBAAAA,e;;;;;;;;;OANH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CrC,U;AAQ3CsC,MAAAA,mB,GAAsB,a;;mCAIfC,iB,WAFZJ,OAAO,CAAC,mBAAD,C,UACPE,iBAAiB,E,UAEbD,QAAQ,CAAChC,QAAD,C,UAERgC,QAAQ,CAAC;AAACI,QAAAA,IAAI,EAACvC,OAAN;AAAewC,QAAAA,WAAW,EAAC,UAA3B;AAAsCC,QAAAA,GAAG,EAAC;AAA1C,OAAD,C,UAIRN,QAAQ,CAAC;AAACI,QAAAA,IAAI;AAAA;AAAA,wDAAL;AAA4BC,QAAAA,WAAW,EAAC;AAAxC,OAAD,C,UAERL,QAAQ,CAAC;AAACI,QAAAA,IAAI,EAAC;AAAA;AAAA,qCAAN;AAAoBC,QAAAA,WAAW,EAAC;AAAhC,OAAD,C,UAERL,QAAQ,CAAC;AAACI,QAAAA,IAAI,EAAC;AAAA;AAAA,qCAAN;AAAoBC,QAAAA,WAAW,EAAC;AAAhC,OAAD,C,0CAbb,MAEaF,iBAFb,SAEuCrC,SAFvC,CAEiD;AAAA;AAAA;;AAAA;;AAAA;;AAAA,eAKrCyC,YALqC,GAKd,CALc;;AAAA;;AAAA;;AAAA;;AAAA,eAcrCC,mBAdqC,GAcL,IAdK;AAAA,eAerCC,eAfqC,GAeT,IAfS;AAAA,eAgBrCC,aAhBqC,GAgBX,IAhBW;AAAA,eAkBrCC,qBAlBqC,GAkBJ,KAlBI;AAAA,eAmBrCC,KAnBqC,GAmBpB,KAnBoB;AAAA,eAoBrCC,SApBqC,GAoBZ,IApBY;AAAA;;AAsB7CC,QAAAA,MAAM,GAAQ;AAAA;;AACVC,UAAAA,OAAO,CAACC,GAAR,CAAa,0BAAb;AACA,eAAKR,mBAAL,GAA2B;AAAA;AAAA,oDAAiBS,YAAjB,CAA8B,KAAKC,IAAnC,EAAyC,iBAAzC,CAA3B;AACA,eAAKT,eAAL,GAAuB;AAAA;AAAA,oDAAiBQ,YAAjB,CAA8B,KAAKC,IAAnC,EAAyC,aAAzC,CAAvB;AACA,eAAKR,aAAL,GAAqB;AAAA;AAAA,oDAAiBO,YAAjB,CAA8B,KAAKC,IAAnC,EAAyC,WAAzC,CAArB;AAEA,eAAKL,SAAL,GAAiB;AAAA;AAAA,oDAAiBI,YAAjB,CAA8B,KAAKC,IAAnC,EAAyC,UAAzC,CAAjB;AAEAH,UAAAA,OAAO,CAACC,GAAR,CAAa,2BAAD,yBAA2B,KAAKP,eAAhC,qBAA2B,sBAAsBU,IAAK,EAAlE;AACH;;AACDC,QAAAA,MAAM,CAACC,EAAD,EAAiB;AACnB,eAAKC,cAAL,CAAoB,KAAKb,eAAzB,EAA2C,KAAKc,WAAhD,EAA4DF,EAA5D;AACA,eAAKC,cAAL,CAAoB,KAAKZ,aAAzB,EAAyC,KAAKc,SAA9C,EAAwDH,EAAxD;AACH;;AACOI,QAAAA,yBAAyB,CAACP,IAAD,EAAYQ,IAAZ,EAAgC;AAC7D,gBAAMC,MAAM,GAAGT,IAAI,CAACU,YAAL,CAAkBzD,WAAlB,EAAgC0D,WAAhC,CAA4CF,MAA3D;AACAT,UAAAA,IAAI,CAACY,WAAL,CAAiB,CAAjB,EAAoBJ,IAAI,GAACtD,IAAI,CAAC2D,cAAL,GAAsBJ,MAAtB,GAA+B,CAApC,GAAwCA,MAAM,GAAG,CAArE;AACA,iBAAOA,MAAP;AAEH;;AACMK,QAAAA,IAAI,CAACC,QAAD,EAAwB;AAC/B,cAAIP,IAAI,GAAG,CAAX;;AACA,eAAK,IAAIQ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,eAAL,CAAqBC,eAArB,CAAsCC,QAAtC,CAA+CC,MAAnE,EAA2EJ,CAAC,EAA5E,EAAgF;AAC5E,gBAAIK,EAAE,GAAG,KAAKJ,eAAL,CAAqBC,eAArB,CAAsCC,QAAtC,CAA+CH,CAA/C,CAAT;AACAR,YAAAA,IAAI,IAAI,KAAKD,yBAAL,CAA+Bc,EAA/B,EAAmCb,IAAnC,CAAR;AACH;;AACD,iBAAM,KAAKS,eAAL,CAAqBK,WAArB,CAAiCF,MAAjC,GAA0C,CAA1C,IAA+CZ,IAAI,GAAG,KAAKnB,YAAjE,EAA+E;AAC3E,gBAAIgC,EAAY,GAAG,IAAnB;AACA,gBAAIE,OAAO,GAAG,KAAKN,eAAL,CAAqBC,eAArB,CAAsCC,QAAtC,CAA+CC,MAA/C,GAAwD,KAAKH,eAAL,CAAqBK,WAArB,CAAiCF,MAAvG;AACA,kBAAMI,MAAM,GAAG,KAAKP,eAAL,CAAqBK,WAArB,CAAiCC,OAAjC,CAAf;;AACA,gBAAIC,MAAM,IAAI,IAAd,EAAoB;AAChBH,cAAAA,EAAE,GAAGrE,WAAW,CAACwE,MAAD,CAAhB;AACH;;AACD,gBAAIH,EAAE,IAAI,IAAV,EAAgB;AACZA,cAAAA,EAAE,GAAG,IAAIxE,IAAJ,CAAS,OAAT,CAAL;AACAwE,cAAAA,EAAE,CAACI,YAAH,CAAgBxE,WAAhB,EAA6BwD,MAA7B,GAAsC,IAAtC;AACH;;AACD,iBAAKQ,eAAL,CAAqBC,eAArB,CAAsCQ,QAAtC,CAA+CL,EAA/C;AACAb,YAAAA,IAAI,IAAI,KAAKD,yBAAL,CAA+Bc,EAA/B,EAAmCb,IAAnC,CAAR;AACH;;AACD,eAAK,IAAIQ,CAAC,GAAG,KAAKC,eAAL,CAAqBC,eAArB,CAAsCC,QAAtC,CAA+CC,MAA/C,GAAwD,CAArE,EAAwEJ,CAAC,IAAI,CAA7E,EAAgFA,CAAC,EAAjF,EAAqF;AACjF,kBAAMK,EAAE,GAAG,KAAKJ,eAAL,CAAqBC,eAArB,CAAsCC,QAAtC,CAA+CH,CAA/C,CAAX;;AACA,gBAAIK,EAAE,CAACM,QAAH,CAAYC,CAAZ,GAAgBP,EAAE,CAACX,YAAH,CAAgBzD,WAAhB,EAA8BwD,MAA9B,GAAqC,CAArD,GAAyD,KAAKpB,YAAlE,EAAgF;AAC5EgC,cAAAA,EAAE,CAACQ,gBAAH;AACH,aAFD,MAEO;AACH;AACH;AACJ;;AAED,eAAKZ,eAAL,CAAsBjB,IAAtB,CAA4BU,YAA5B;AAAA;AAAA,wDAAkFI,IAAlF,CACIC,QADJ,EACc,KAAKe,SADnB,EAC8B,KAAKb,eAAL,CAAqBc,KADnD;AAEA,eAAK1B,WAAL,CAAiB2B,OAAjB,CAA0BC,KAAD,IAAW;AAChC,gBAAIA,KAAK,CAACjC,IAAN,IAAciC,KAAK,CAACjC,IAAN,CAAWU,YAAX;AAAA;AAAA,yDAAlB,EAAmF;AAC/EuB,cAAAA,KAAK,CAACjC,IAAN,CAAYU,YAAZ;AAAA;AAAA,4DAAkEI,IAAlE,CAAuEC,QAAvE,EAAiF,KAAKe,SAAtF,EAAiGG,KAAK,CAACF,KAAvG;AACH;AACJ,WAJD;AAKA,eAAKzB,SAAL,CAAe0B,OAAf,CAAwBC,KAAD,IAAW;AAC9B,gBAAIA,KAAK,CAACjC,IAAN,IAAciC,KAAK,CAACjC,IAAN,CAAWU,YAAX;AAAA;AAAA,yDAAlB,EAAmF;AAC/EuB,cAAAA,KAAK,CAACjC,IAAN,CAAYU,YAAZ;AAAA;AAAA,4DAAkEI,IAAlE,CAAuEC,QAAvE,EAAiF,KAAKe,SAAtF,EAAiGG,KAAK,CAACF,KAAvG;AACH;AACJ,WAJD;AAKH;;AAEsB,eAARG,QAAQ,CAACC,UAAD,EAAmBC,IAAnB,EAAqD;AACxE,cAAIC,SAAS,GAAG,IAAIxF,IAAJ,CAASuF,IAAT,CAAhB;AACA,cAAIE,QAAQ,GAAGD,SAAS,CAACZ,YAAV;AAAA;AAAA,uDAAf;AACAU,UAAAA,UAAU,CAACT,QAAX,CAAoBW,SAApB;AACA,iBAAOC,QAAP;AACH;;AAEOlC,QAAAA,cAAc,CAAC+B,UAAD,EAAmBI,MAAnB,EAAyCpC,EAAzC,EAA0D;AAC5E,cAAIqC,gBAAwB,GAAG,EAA/B;AACAL,UAAAA,UAAU,CAAChB,QAAX,CAAoBa,OAApB,CAA4BhC,IAAI,IAAI;AAChC,gBAAIsC,QAAQ,GAAGtC,IAAI,CAACU,YAAL;AAAA;AAAA,yDAAf;;AACA,gBAAI4B,QAAQ,IAAI,IAAhB,EAAsB;AAClBzC,cAAAA,OAAO,CAACC,GAAR,CAAa,+BAA8BE,IAAI,CAACoC,IAAK,4BAArD;AACAI,cAAAA,gBAAgB,CAACC,IAAjB,CAAsBzC,IAAtB;AACA;AACH;;AACD,gBAAIuC,MAAM,CAACG,IAAP,CAAaT,KAAD,IAAWA,KAAK,CAACjC,IAAN,IAAcA,IAArC,KAA8C,IAAlD,EAAwD;AACpDH,cAAAA,OAAO,CAACC,GAAR,CAAa,+BAA8BE,IAAI,CAACoC,IAAK,yBAArD;AACAI,cAAAA,gBAAgB,CAACC,IAAjB,CAAsBzC,IAAtB;AACA;AACH;AACJ,WAZD;AAaAwC,UAAAA,gBAAgB,CAACR,OAAjB,CAAyBW,OAAO,IAAI;AAChCA,YAAAA,OAAO,CAACd,gBAAR;AACH,WAFD;AAGAU,UAAAA,MAAM,CAACP,OAAP,CAAe,CAACC,KAAD,EAAQjB,CAAR,KAAc;AACzB,gBAAIiB,KAAK,CAACjC,IAAN,IAAc,IAAd,IAAsBiC,KAAK,CAACjC,IAAN,CAAW4C,OAAX,IAAsB,KAAhD,EAAuD;AACnD/C,cAAAA,OAAO,CAACC,GAAR,CAAa,gDAAb;AACAmC,cAAAA,KAAK,CAACjC,IAAN,GAAaf,iBAAiB,CAACiD,QAAlB,CAA2BC,UAA3B,EAAwC,SAAQnB,CAAE,EAAlD,EAAqDhB,IAAlE;AACH;;AAED,gBAAIiC,KAAK,CAAC/C,IAAN,KAAe;AAAA;AAAA,wCAAU2D,MAA7B,EAAqC;AACjC,mBAAKC,gBAAL,CAAsBb,KAAtB,EAA6BA,KAAK,CAAEjC,IAApC;AACH,aAFD,MAEO,IAAIiC,KAAK,CAAC/C,IAAN,KAAe;AAAA;AAAA,wCAAU6D,MAA7B,EAAqC;AACxC,mBAAKC,qBAAL,CAA2Bf,KAA3B,EAAkCA,KAAK,CAAEjC,IAAzC;AACH,aAFM,MAEA,IAAIiC,KAAK,CAAC/C,IAAN,KAAe;AAAA;AAAA,wCAAU+D,QAA7B,EAAuC;AAC1C,mBAAKC,kBAAL,CAAwBjB,KAAxB,EAA+BA,KAAK,CAAEjC,IAAtC;;AACA,mBAAKmD,mBAAL,CAAyBhD,EAAzB,EAA6B8B,KAAK,CAAEjC,IAApC;AACH;AACJ,WAdD;AAeH;;AAE6B,cAAhB8C,gBAAgB,CAACM,IAAD,EAAmBjB,UAAnB,EAAmD;AAC7E,gBAAMkB,WAAW,GAAG;AAAA;AAAA,oDAAiBtD,YAAjB,CAA8BoC,UAA9B,EAA0C,SAA1C,CAApB;;AACA,cAAIiB,IAAI,CAAClE,IAAL,IAAa;AAAA;AAAA,sCAAU2D,MAA3B,EAAmC;AAC/BQ,YAAAA,WAAW,CAACC,iBAAZ;AACA;AACH;;AAED,cAAI,KAAK7D,qBAAT,EAAgC;AAC5B;AACA;AACH;;AAED,gBAAM8D,YAAY,GAAGF,WAAW,CAAClC,QAAZ,CAAqBC,MAArB,KAAgCgC,IAAI,CAACI,YAAL,CAAkBpC,MAAvE,CAZ6E,CAa7E;;AACA,cAAI,CAACmC,YAAL,EAAmB;AACf,kBAAME,YAA6B,GAAG,EAAtC;AACAJ,YAAAA,WAAW,CAACC,iBAAZ;AACA,iBAAK7D,qBAAL,GAA6B,IAA7B,CAHe,CAGoB;;AAEnC2D,YAAAA,IAAI,CAACI,YAAL,CAAkBxB,OAAlB,CAA0B,CAAC0B,MAAD,EAASC,KAAT,KAAmB;AACzC,kBAAID,MAAM,CAACE,aAAP,CAAqBxC,MAArB,IAA+B,CAAnC,EAAsC;AAElCsC,cAAAA,MAAM,CAACE,aAAP,CAAqB5B,OAArB,CAA6BR,MAAM,IAAI;AACnC,sBAAMqC,WAAW,GAAG,IAAIC,OAAJ,CAAmBC,OAAD,IAAa;AAC/ChH,kBAAAA,YAAY,CAACiH,OAAb,CAAqB;AAAE/D,oBAAAA,IAAI,EAAEuB,MAAM,CAAEvB;AAAhB,mBAArB,EAA6C,CAACgE,GAAD,EAAaC,YAAb,KAAsC;AAC/E,wBAAID,GAAJ,EAAS;AACLpE,sBAAAA,OAAO,CAACsE,KAAR,CAAc,wBAAd,EAAwCF,GAAxC;AACAF,sBAAAA,OAAO;AACV,qBAHD,MAGO;AACHA,sBAAAA,OAAO;AACV;AACJ,mBAPD;AAQP,iBATuB,CAApB;AAWJN,gBAAAA,YAAY,CAAChB,IAAb,CAAkBoB,WAAlB;AACH,eAbG;AAcP,aAjBD;AAmBA,kBAAMC,OAAO,CAACM,GAAR,CAAYX,YAAZ,CAAN,CAxBe,CAyBf;;AACAL,YAAAA,IAAI,CAACI,YAAL,CAAkBxB,OAAlB,CAA0B,CAAC0B,MAAD,EAASC,KAAT,KAAmB;AACzC,oBAAMU,UAAU,GAAG;AAAA;AAAA,wDAAiBtE,YAAjB,CAA8BsD,WAA9B,EAA4C,UAASM,KAAM,EAA3D,CAAnB;AACA,oBAAMW,WAAW,GAAGlB,IAAI,CAACrB,KAAL,GAAa,KAAKD,SAAlB,GAA8B,IAAlD;AACAjC,cAAAA,OAAO,CAACC,GAAR,CAAa,kDAAiDwE,WAAY,EAA1E;AACA,kBAAIC,UAAU,GAAG,CAAjB;AACA,kBAAI9D,MAAM,GAAG,CAAb;AACA,kBAAI+D,WAAW,GAAG,CAAlB;;AAEA,qBAAO/D,MAAM,GAAG6D,WAAhB,EAA6B;AACzB,sBAAMG,SAAS,GAAGf,MAAM,CAACE,aAAP,CAAqBY,WAArB,CAAlB;AACA,sBAAME,KAAK,GAAG1H,WAAW,CAACyH,SAAD,CAAzB;AACA,sBAAME,aAAa,GAAGC,IAAI,CAACC,MAAL,MAAiBnB,MAAM,CAACoB,eAAP,CAAwBC,GAAxB,GAA8BrB,MAAM,CAACoB,eAAP,CAAwB1F,GAAvE,IAA8EsE,MAAM,CAACoB,eAAP,CAAwB1F,GAA5H;AACAsF,gBAAAA,KAAK,CAAC9D,WAAN,CAAkB+D,aAAlB,EAAiCJ,UAAjC,EAA6C,CAA7C;AAEA,oBAAIS,IAAI,GAAG,CAAX;;AACA,oBAAItB,MAAM,CAACuB,YAAP,KAAwB;AAAA;AAAA,4DAAkBC,WAA9C,EAA2D;AACvDF,kBAAAA,IAAI,GAAGN,KAAK,CAAChE,YAAN,CAAmBzD,WAAnB,EAAiC0D,WAAjC,CAA6CF,MAApD;AACH,iBAFD,MAEO,IAAIiD,MAAM,CAACuB,YAAP,KAAwB;AAAA;AAAA,4DAAkBE,UAA9C,EAA0D;AAC7DH,kBAAAA,IAAI,GAAG,IAAP;AACH,iBAFM,MAEA,IAAItB,MAAM,CAACuB,YAAP,KAAwB;AAAA;AAAA,4DAAkBG,aAA9C,EAA6D;AAChEJ,kBAAAA,IAAI,GAAGJ,IAAI,CAACG,GAAL,CAASrB,MAAM,CAAC2B,eAAP,CAAwBjG,GAAjC,EAAsCsE,MAAM,CAAC2B,eAAP,CAAwBN,GAA9D,IAAqEL,KAAK,CAAChE,YAAN,CAAmBzD,WAAnB,EAAiC0D,WAAjC,CAA6CF,MAAzH;AACH;;AAED4D,gBAAAA,UAAU,CAAC3C,QAAX,CAAoBgD,KAApB;AACAH,gBAAAA,UAAU,IAAIS,IAAd;AACAvE,gBAAAA,MAAM,IAAIuE,IAAV;AACAR,gBAAAA,WAAW,GAAG,CAACA,WAAW,GAAG,CAAf,IAAoBd,MAAM,CAACE,aAAP,CAAqBxC,MAAvD;AACH;AACJ,aA5BD;AA6BA,iBAAK3B,qBAAL,GAA6B,KAA7B;AACH;AACJ;;AAEOuD,QAAAA,qBAAqB,CAACI,IAAD,EAAmBjB,UAAnB,EAA0C;AACnE,gBAAMmD,WAAW,GAAG;AAAA;AAAA,oDAAiBvF,YAAjB,CAA8BoC,UAA9B,EAA0C,SAA1C,CAApB,CADmE,CAGnE;;AACA,gBAAMoD,gBAAgB,GAAGD,WAAW,CAACnE,QAArC;;AACA,eAAK,IAAIH,CAAC,GAAGuE,gBAAgB,CAACnE,MAAjB,GAA0B,CAAvC,EAA0CJ,CAAC,IAAI,CAA/C,EAAkDA,CAAC,EAAnD,EAAuD;AACnD,kBAAMhB,IAAI,GAAGuF,gBAAgB,CAACvE,CAAD,CAA7B;AACA,kBAAMwE,KAAK,GAAGxF,IAAI,CAACoC,IAAL,CAAUoD,KAAV,CAAgB,cAAhB,CAAd;;AACA,gBAAIA,KAAJ,EAAW;AACP,oBAAM7B,KAAK,GAAG8B,QAAQ,CAACD,KAAK,CAAC,CAAD,CAAN,CAAtB;;AACA,kBAAI7B,KAAK,IAAIP,IAAI,CAACsC,YAAL,CAAkBtE,MAA/B,EAAuC;AACnCpB,gBAAAA,IAAI,CAAC6B,gBAAL;AACH;AACJ;AACJ;;AAED,cAAIuB,IAAI,CAAClE,IAAL,IAAa;AAAA;AAAA,sCAAU6D,MAAvB,IAAiCK,IAAI,CAACsC,YAAL,CAAkBtE,MAAlB,KAA6B,CAAlE,EAAqE;AACjEkE,YAAAA,WAAW,CAAChC,iBAAZ;AACA;AACH;;AAED,cAAIqC,WAAW,GAAG,KAAlB;AACA,gBAAMC,WAAqB,GAAG,EAA9B;;AAEA,eAAK,IAAI5E,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGoC,IAAI,CAACsC,YAAL,CAAkBtE,MAAtC,EAA8CJ,CAAC,EAA/C,EAAmD;AAC/C,kBAAM6E,YAAY,GAAGzC,IAAI,CAACsC,YAAL,CAAkB1E,CAAlB,CAArB;AACA,kBAAM8E,QAAQ,GAAG;AAAA;AAAA,sDAAiB/F,YAAjB,CAA8BuF,WAA9B,EAA4C,QAAOtE,CAAE,EAArD,CAAjB,CAF+C,CAI/C;;AACA,gBAAI+E,kBAAkB,GAAG,CAAzB;;AACA,iBAAK,MAAMC,QAAX,IAAuBH,YAAY,CAACI,eAApC,EAAqD;AACjDF,cAAAA,kBAAkB,IAAIC,QAAQ,CAACE,cAAT,CAAwB9E,MAA9C;AACH,aAR8C,CAU/C;;;AACA,gBAAI0E,QAAQ,CAAC3E,QAAT,CAAkBC,MAAlB,KAA6B2E,kBAAjC,EAAqD;AACjDJ,cAAAA,WAAW,GAAG,IAAd;AACAC,cAAAA,WAAW,CAACnD,IAAZ,CAAiBzB,CAAjB;AACA;AACH,aAf8C,CAiB/C;;;AACA,gBAAImF,UAAU,GAAG,CAAjB;AACA,gBAAIC,WAAW,GAAG,IAAlB;;AAEA,iBAAK,MAAMJ,QAAX,IAAuBH,YAAY,CAACI,eAApC,EAAqD;AACjD,mBAAK,MAAMI,OAAX,IAAsBL,QAAQ,CAACE,cAA/B,EAA+C;AAAA;;AAC3C,sBAAMI,SAAS,GAAGR,QAAQ,CAAC3E,QAAT,CAAkBgF,UAAlB,CAAlB,CAD2C,CAE3C;;AACA,sBAAMI,eAAe,yBAAGD,SAAS,CAACE,OAAb,mCAAG,mBAAmBC,KAAtB,qBAAG,mBAA0BC,KAAlD;AACA,sBAAMC,WAAW,GAAGN,OAAH,qCAAGA,OAAO,CAAEO,cAAZ,qBAAG,sBAAyB3G,IAA7C;;AAEA,oBAAIsG,eAAe,KAAKI,WAAxB,EAAqC;AACjCP,kBAAAA,WAAW,GAAG,KAAd;AACA;AACH;;AACDD,gBAAAA,UAAU;AACb;;AACD,kBAAI,CAACC,WAAL,EAAkB;AACrB;;AAED,gBAAI,CAACA,WAAL,EAAkB;AACdT,cAAAA,WAAW,GAAG,IAAd;AACAC,cAAAA,WAAW,CAACnD,IAAZ,CAAiBzB,CAAjB;AACH;AACJ;;AAED,cAAI2E,WAAJ,EAAiB;AACb;AAEA,iBAAK,MAAMhC,KAAX,IAAoBiC,WAApB,EAAiC;AAC7B,oBAAME,QAAQ,GAAG;AAAA;AAAA,wDAAiB/F,YAAjB,CAA8BuF,WAA9B,EAA4C,QAAO3B,KAAM,EAAzD,CAAjB;AACAmC,cAAAA,QAAQ,CAACxC,iBAAT;AACA,oBAAMuC,YAAY,GAAGzC,IAAI,CAACsC,YAAL,CAAkB/B,KAAlB,CAArB,CAH6B,CAI7B;;AACA,mBAAK,IAAIkD,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGhB,YAAY,CAACI,eAAb,CAA6B7E,MAAjD,EAAyDyF,CAAC,EAA1D,EAA8D;AAC1D,sBAAMb,QAAQ,GAAGH,YAAY,CAACI,eAAb,CAA6BY,CAA7B,CAAjB,CAD0D,CAG1D;;AACA,qBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGd,QAAQ,CAACE,cAAT,CAAwB9E,MAA5C,EAAoD0F,CAAC,EAArD,EAAyD;AAAA;;AACrD,wBAAMT,OAAO,GAAGL,QAAQ,CAACE,cAAT,CAAwBY,CAAxB,CAAhB;AACA/J,kBAAAA,YAAY,CAACiH,OAAb,CAAqB;AAAE/D,oBAAAA,IAAI,EAAEoG,OAAF,sCAAEA,OAAO,CAAEO,cAAX,qBAAE,uBAAyB3G;AAAjC,mBAArB,EAA8D,CAACgE,GAAD,EAAMzC,MAAN,KAAyB;AACnF,wBAAIyC,GAAJ,EAAS;AACL;AACA;AACH;;AAED,0BAAMjE,IAAI,GAAGhD,WAAW,CAACwE,MAAD,CAAxB;AACAxB,oBAAAA,IAAI,CAACoC,IAAL,GAAa,QAAOyE,CAAE,IAAGC,CAAE,EAA3B;AACAhB,oBAAAA,QAAQ,CAACpE,QAAT,CAAkB1B,IAAlB;AACA,0BAAM2E,aAAa,GAAGC,IAAI,CAACC,MAAL,MAAiBwB,OAAO,CAACU,OAAR,CAAiBhC,GAAjB,GAAuBsB,OAAO,CAACU,OAAR,CAAiB3H,GAAzD,IAAgEiH,OAAO,CAACU,OAAR,CAAiB3H,GAAvG;AACA,0BAAM4H,aAAa,GAAGpC,IAAI,CAACC,MAAL,MAAiBwB,OAAO,CAACY,OAAR,CAAiBlC,GAAjB,GAAuBsB,OAAO,CAACY,OAAR,CAAiB7H,GAAzD,IAAgEiH,OAAO,CAACY,OAAR,CAAiB7H,GAAvG;AACAkG,oBAAAA,WAAW,CAAC1E,WAAZ,CAAwB+D,aAAxB,EAAuCqC,aAAvC,EAAsD,CAAtD;AACH,mBAZD;AAaH;AACJ;AACJ;AACJ;AACJ;;AAEO9D,QAAAA,kBAAkB,CAACE,IAAD,EAAmBjB,UAAnB,EAA0C;AAChE,gBAAM+E,YAAY,GAAG;AAAA;AAAA,oDAAiBnH,YAAjB,CAA8BoC,UAA9B,EAA0C,WAA1C,CAArB;;AACA,cAAIiB,IAAI,CAAClE,IAAL,IAAa;AAAA;AAAA,sCAAU+D,QAA3B,EAAqC;AACjCiE,YAAAA,YAAY,CAAC5D,iBAAb;AACA;AACH,WAL+D,CAOhE;;;AACA,eAAK,IAAItC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGoC,IAAI,CAAC+D,cAAL,CAAoB/F,MAAxC,EAAgDJ,CAAC,EAAjD,EAAqD;AACjD,kBAAMoG,WAAW,GAAGhE,IAAI,CAAC+D,cAAL,CAAoBnG,CAApB,CAApB,CADiD,CAGjD;;AACA,kBAAMqG,YAAY,GAAGrG,CAAC,GAAGkG,YAAY,CAAC/F,QAAb,CAAsBC,MAA1B,GACD8F,YAAY,CAAC/F,QAAb,CAAsBH,CAAtB,CADC,GAED,IAFpB,CAJiD,CAQjD;;AACA,gBAAIsG,UAAU,GAAG,KAAjB;;AAEA,gBAAI,CAACD,YAAD,IAAiBD,WAAW,KAAK,IAArC,EAA2C;AACvC;AACAE,cAAAA,UAAU,GAAG,IAAb;AACH,aAHD,MAGO;AAAA;;AACH;AACA;AACA,kBAAI,0BAAAD,YAAY,CAACb,OAAb,4DAAsBC,KAAtB,2CAA6BxG,IAA7B,MAAsCmH,WAAW,CAACnH,IAAtD,EAA6D;AACzDqH,gBAAAA,UAAU,GAAG,IAAb;AACH;AACJ,aApBgD,CAsBjD;;;AACA,gBAAIA,UAAJ,EAAgB;AACZ;AACA,oBAAMC,WAAW,GAAGF,YAAY,GAAGA,YAAY,CAAC1F,QAAb,CAAsB6F,KAAtB,EAAH,GAAmC,IAAIjK,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,CAAnE;AACA,oBAAMkK,WAAW,GAAGJ,YAAY,GAAGA,YAAY,CAACK,QAAb,CAAsBF,KAAtB,EAAH,GAAmC,IAAIhK,IAAJ,EAAnE;AACA,oBAAMmK,QAAQ,GAAGN,YAAY,GAAGA,YAAY,CAACO,KAAb,CAAmBJ,KAAnB,EAAH,GAAgC,IAAIjK,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,CAA7D,CAJY,CAMZ;;AACA,kBAAI8J,YAAJ,EAAkB;AACdA,gBAAAA,YAAY,CAACxF,gBAAb;AACH;;AAED,kBAAIuF,WAAW,IAAIA,WAAW,CAACnH,IAA/B,EAAqC;AACjClD,gBAAAA,YAAY,CAACiH,OAAb,CAAqB;AAAE/D,kBAAAA,IAAI,EAAEmH,WAAW,CAACnH;AAApB,iBAArB,EAAiD,CAACgE,GAAD,EAAMzC,MAAN,KAAyB;AACtE,sBAAIyC,GAAJ,EAAS;AACL;AACH,mBAFD,MAEO;AAEH,0BAAM4D,WAAW,GAAG7K,WAAW,CAACwE,MAAD,CAA/B;AACAqG,oBAAAA,WAAW,CAACzF,IAAZ,GAAoB,YAAWpB,CAAE,EAAjC;AACA6G,oBAAAA,WAAW,CAAClG,QAAZ,GAAuB4F,WAAvB;AACAM,oBAAAA,WAAW,CAACH,QAAZ,GAAuBD,WAAvB;AACAI,oBAAAA,WAAW,CAACD,KAAZ,GAAoBD,QAApB;AAEAT,oBAAAA,YAAY,CAACxF,QAAb,CAAsBmG,WAAtB;AACAA,oBAAAA,WAAW,CAACC,eAAZ,CAA4B9G,CAA5B;AACH;AACJ,iBAdD;AAeH,eAhBD,MAgBO;AACH;AACH;AACJ;AACJ,WA9D+D,CAgEhE;;;AACA,iBAAOkG,YAAY,CAAC/F,QAAb,CAAsBC,MAAtB,GAA+BgC,IAAI,CAAC+D,cAAL,CAAoB/F,MAA1D,EAAkE;AAC9D,kBAAM2G,SAAS,GAAGb,YAAY,CAAC/F,QAAb,CAAsBC,MAAtB,GAA+B,CAAjD;AACA8F,YAAAA,YAAY,CAAC/F,QAAb,CAAsB4G,SAAtB,EAAiClG,gBAAjC;AACH;AACJ;;AAEOmG,QAAAA,iBAAiB,CAACC,KAAD,EAAiB9F,UAAjB,EAAwC;AAC7DA,UAAAA,UAAU,CAAChB,QAAX,CAAoBa,OAApB,CAA4BhC,IAAI,IAAI;AAChC,kBAAMkH,YAAY,GAAG;AAAA;AAAA,sDAAiBnH,YAAjB,CAA8BC,IAA9B,EAAoC,WAApC,CAArB;AACAH,YAAAA,OAAO,CAACC,GAAR,CAAY,8BAAZ;;AACA,gBAAIoH,YAAY,KAAK,IAAjB,IAAyBA,YAAY,CAAC/F,QAAb,CAAsBC,MAAtB,KAAiC,CAA9D,EAAiE;AAC7D;AACH;;AACDvB,YAAAA,OAAO,CAACC,GAAR,CAAY,kCAAZ;;AAEA,iBAAK,IAAIkB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGkG,YAAY,CAAC/F,QAAb,CAAsBC,MAA1C,EAAkDJ,CAAC,EAAnD,EAAuD;AACnD,oBAAM6G,WAAW,GAAGX,YAAY,CAAC/F,QAAb,CAAsBH,CAAtB,CAApB;AACA,oBAAMkH,QAAQ,GAAGL,WAAW,CAACnH,YAAZ;AAAA;AAAA,qDAAjB;AACAb,cAAAA,OAAO,CAACC,GAAR,CAAY,eAAZ,EAA6BmI,KAA7B;;AACA,kBAAIC,QAAJ,EAAc;AACVrI,gBAAAA,OAAO,CAACC,GAAR,CAAY,wBAAZ,EAAsCmI,KAAtC;AACAC,gBAAAA,QAAQ,CAACC,IAAT,CAAcF,KAAd;AACH;AACJ;AACJ,WAjBD;AAkBH;;AAEO9E,QAAAA,mBAAmB,CAAChD,EAAD,EAAagC,UAAb,EAAoC;AAC3D,gBAAM+E,YAAY,GAAG;AAAA;AAAA,oDAAiBnH,YAAjB,CAA8BoC,UAA9B,EAA0C,WAA1C,CAArB;;AACA,cAAI+E,YAAY,KAAK,IAAjB,IAAyBA,YAAY,CAAC/F,QAAb,CAAsBC,MAAtB,KAAiC,CAA9D,EAAiE;AAC7D;AACH;;AAED,eAAK,IAAIJ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGkG,YAAY,CAAC/F,QAAb,CAAsBC,MAA1C,EAAkDJ,CAAC,EAAnD,EAAuD;AACnD,kBAAM6G,WAAW,GAAGX,YAAY,CAAC/F,QAAb,CAAsBH,CAAtB,CAApB;AACA,kBAAMkH,QAAQ,GAAGL,WAAW,CAACnH,YAAZ;AAAA;AAAA,mDAAjB;;AACA,gBAAIwH,QAAJ,EAAc;AACVA,cAAAA,QAAQ,CAACpH,IAAT,CAAcX,EAAd;AACH;AACJ;AACJ;;AAEMiI,QAAAA,eAAe,CAAChF,IAAD,EAAuB;AAAA;;AACzC,eAAKiF,SAAL,GAAiBjF,IAAI,CAAChB,IAAtB;AACA,eAAKN,SAAL,GAAiBsB,IAAI,CAACtB,SAAtB;AAEA,eAAKxC,mBAAL,CAA0BgE,iBAA1B;AACA,eAAKrC,eAAL,GAAuB;AAAA;AAAA,6DAAvB;AACA,eAAKA,eAAL,CAAqBK,WAArB,GAAmC,EAAnC;AACA,mCAAA8B,IAAI,CAACnC,eAAL,4DAAsBK,WAAtB,mCAAmCU,OAAnC,CAA4CsG,UAAD,IAAgB;AACvDvL,YAAAA,YAAY,CAACiH,OAAb,CAAqB;AAAC/D,cAAAA,IAAI,EAACqI;AAAN,aAArB,EAAwC,CAACrE,GAAD,EAAazC,MAAb,KAA+B;AACnE,kBAAIyC,GAAJ,EAAS;AACLpE,gBAAAA,OAAO,CAACsE,KAAR,CAAc,8DAAd,EAA8EF,GAA9E;AACA;AACH;;AACD,mBAAKhD,eAAL,CAAqBK,WAArB,CAAiCmB,IAAjC,CAAsCjB,MAAtC;AACH,aAND;AAOH,WARD;AASA,eAAKP,eAAL,CAAqBc,KAArB,6BAA6BqB,IAAI,CAACnC,eAAlC,qBAA6B,uBAAsBc,KAAnD;AACA,eAAKd,eAAL,CAAqBsH,MAArB,6BAA8BnF,IAAI,CAACnC,eAAnC,qBAA8B,uBAAsBsH,MAApD;AACA,eAAKlJ,YAAL,GAAoB,KAAK4B,eAAL,CAAqBc,KAArB,GAA6B,KAAKD,SAAlC,GAA8C,IAAlE;AACA,eAAKb,eAAL,CAAqBjB,IAArB,GAA4Bf,iBAAiB,CAACiD,QAAlB,CAA2B,KAAK5C,mBAAhC,EAAsD,OAAtD,EAA+DU,IAA3F;AACA,eAAKiB,eAAL,CAAqBC,eAArB,GAAuC;AAAA;AAAA,oDAAiBnB,YAAjB,CAA8B,KAAKkB,eAAL,CAAqBjB,IAAnD,EAAyDhB,mBAAzD,CAAvC;AACA,eAAKiC,eAAL,CAAqBC,eAArB,CAAqC4G,eAArC,CAAqD,CAArD;AACA,eAAK7G,eAAL,CAAqBjB,IAArB,CAA0BU,YAA1B;AAAA;AAAA,wDAAgF0H,eAAhF,CAAgGhF,IAAI,CAACnC,eAArG;AAEA,eAAKZ,WAAL,GAAmB,EAAnB;AACA,eAAKC,SAAL,GAAiB,EAAjB;AACArB,UAAAA,iBAAiB,CAACuJ,UAAlB,CAA6B,KAAKjJ,eAAlC,EAAoD,KAAKc,WAAzD,EAAsE+C,IAAI,CAAC/C,WAA3E;AACApB,UAAAA,iBAAiB,CAACuJ,UAAlB,CAA6B,KAAKhJ,aAAlC,EAAkD,KAAKc,SAAvD,EAAkE8C,IAAI,CAAC9C,SAAvE;AAEA;AAAA;AAAA,0CAAYmI,QAAZ,uBAAsBC,KAAtB;;AAEA,eAAKC,iBAAL;AACH;;AAEwB,eAAVH,UAAU,CAACrG,UAAD,EAAmBI,MAAnB,EAAyCqG,UAAzC,EAA4E;AACjGzG,UAAAA,UAAU,CAACmB,iBAAX;AACAsF,UAAAA,UAAU,CAAC5G,OAAX,CAAmB,CAACC,KAAD,EAAQjB,CAAR,KAAc;AAC7B,gBAAI6H,UAAU,GAAG;AAAA;AAAA,2CAAjB;AACAA,YAAAA,UAAU,CAAC9G,KAAX,GAAmBE,KAAK,CAACF,KAAzB;AACA8G,YAAAA,UAAU,CAAC3J,IAAX,GAAkB+C,KAAK,CAAC/C,IAAxB;AACA2J,YAAAA,UAAU,CAACN,MAAX,GAAoBtG,KAAK,CAACsG,MAA1B;AACAM,YAAAA,UAAU,CAACC,MAAX,GAAoB7G,KAAK,CAAC6G,MAA1B;AACAD,YAAAA,UAAU,CAAC7I,IAAX,GAAkBf,iBAAiB,CAACiD,QAAlB,CAA2BC,UAA3B,EAAwC,SAAQnB,CAAE,EAAlD,EAAqDhB,IAAvE;AACA6I,YAAAA,UAAU,CAAC7I,IAAX,CAAgB8H,eAAhB,CAAgC7F,KAAK,CAAC6G,MAAtC;AACA,kBAAMC,kBAAkB,GAAGF,UAAU,CAAC7I,IAAX,CAAgBU,YAAhB;AAAA;AAAA,yDAA3B;;AACA,gBAAIuB,KAAK,CAAC/C,IAAN,KAAe;AAAA;AAAA,wCAAU2D,MAA7B,EAAqC;AACjChD,cAAAA,OAAO,CAACC,GAAR,CAAY,2CAAZ,EAAyD+I,UAAU,CAACrF,YAAX,CAAwBpC,MAAjF;AACA2H,cAAAA,kBAAkB,CAACC,sBAAnB,CAA0CH,UAA1C,EAAsD5G,KAAtD;AACH,aAHD,MAGO,IAAIA,KAAK,CAAC/C,IAAN,KAAe;AAAA;AAAA,wCAAU6D,MAA7B,EAAqC;AACxC8F,cAAAA,UAAU,CAACnD,YAAX,GAA0B,EAA1B;AACAzD,cAAAA,KAAK,CAACgH,QAAN,CAAejH,OAAf,CAAwBkH,OAAD,IAAa;AAChC,oBAAIxD,YAAY,GAAG;AAAA;AAAA,6EAAnB;AACAwD,gBAAAA,OAAO,CAACC,KAAR,CAAcnH,OAAd,CAAuB6D,YAAD,IAAkB;AACpChG,kBAAAA,OAAO,CAACC,GAAR,CAAY,sCAAZ,EAAoD+F,YAAY,CAAC+B,KAAb,CAAmBwB,CAAvE,EAA0EvD,YAAY,CAAC+B,KAAb,CAAmBhG,CAA7F;AACA,sBAAIyH,WAAW,GAAG;AAAA;AAAA,6EAAlB;AACAA,kBAAAA,WAAW,CAACnD,cAAZ,GAA6B,EAA7B;AACAmD,kBAAAA,WAAW,CAACC,MAAZ,GAAqBzD,YAAY,CAACyD,MAAlC;AACAzD,kBAAAA,YAAY,CAACG,QAAb,CAAsBhE,OAAtB,CAA+BqE,OAAD,IAAa;AACvC,wBAAIH,cAAc,GAAG;AAAA;AAAA,mEAArB;AACAA,oBAAAA,cAAc,CAACoD,MAAf,GAAwBjD,OAAO,CAACiD,MAAhC;AACAvM,oBAAAA,YAAY,CAACiH,OAAb,CAAqB;AAAC/D,sBAAAA,IAAI,EAAEoG,OAAO,CAACpG;AAAf,qBAArB,EAA2C,CAACgE,GAAD,EAAazC,MAAb,KAA+B;AACtE,0BAAIyC,GAAJ,EAAS;AACL;AACH;;AACDiC,sBAAAA,cAAc,CAACU,cAAf,GAAgCpF,MAAhC;AACA0E,sBAAAA,cAAc,CAACa,OAAf,GAAyB;AAAA;AAAA,6EAAzB;AACAb,sBAAAA,cAAc,CAACa,OAAf,CAAuB3H,GAAvB,GAA6BiH,OAAO,CAACU,OAAR,CAAiB3H,GAA9C;AACA8G,sBAAAA,cAAc,CAACa,OAAf,CAAuBhC,GAAvB,GAA6BsB,OAAO,CAACU,OAAR,CAAiBhC,GAA9C;AAEAmB,sBAAAA,cAAc,CAACe,OAAf,GAAyB;AAAA;AAAA,6EAAzB;AACAf,sBAAAA,cAAc,CAACe,OAAf,CAAuB7H,GAAvB,GAA6BiH,OAAO,CAACY,OAAR,CAAiB7H,GAA9C;AACA8G,sBAAAA,cAAc,CAACe,OAAf,CAAuBlC,GAAvB,GAA6BsB,OAAO,CAACY,OAAR,CAAiBlC,GAA9C;AACAsE,sBAAAA,WAAW,CAACnD,cAAZ,CAA2BzD,IAA3B,CAAgCyD,cAAhC;AACH,qBAbD;AAcH,mBAjBD;AAkBAR,kBAAAA,YAAY,CAACO,eAAb,CAA6BxD,IAA7B,CAAkC4G,WAAlC;AACH,iBAxBD;AAyBAR,gBAAAA,UAAU,CAACnD,YAAX,CAAwBjD,IAAxB,CAA6BiD,YAA7B;AACH,eA5BD;AA6BH,aA/BM,MA+BA,IAAIzD,KAAK,CAAC/C,IAAN,KAAe;AAAA;AAAA,wCAAU+D,QAA7B,EAAuC;AAC1C8F,cAAAA,kBAAkB,CAACQ,qBAAnB,CAAyCV,UAAzC,EAAqD5G,KAArD;AACH;;AACD8G,YAAAA,kBAAkB,CAACX,eAAnB,CAAmCnG,KAAnC;AACAM,YAAAA,MAAM,CAACE,IAAP,CAAYoG,UAAZ;AACH,WAhDD;AAiDH;;AAEOW,QAAAA,mBAAmB,CAACvH,KAAD,EAAoBwH,SAApB,EAAoD;AAC3EA,UAAAA,SAAS,CAAC1H,KAAV,GAAkBE,KAAK,CAACF,KAAxB;AACA0H,UAAAA,SAAS,CAACvK,IAAV,GAAiB+C,KAAK,CAAC/C,IAAvB;AACAuK,UAAAA,SAAS,CAAClB,MAAV,GAAmBtG,KAAK,CAACsG,MAAzB;AACAkB,UAAAA,SAAS,CAACX,MAAV,GAAmB7G,KAAK,CAAC6G,MAAzB;AACAW,UAAAA,SAAS,CAAC3H,SAAV,GAAsB,KAAKA,SAA3B;;AAEA,cAAIG,KAAK,CAAC/C,IAAN,KAAe;AAAA;AAAA,sCAAU2D,MAA7B,EAAqC;AACjC4G,YAAAA,SAAS,CAACC,OAAV,GAAoB,EAApB;AACAzH,YAAAA,KAAK,CAACuB,YAAN,CAAmBxB,OAAnB,CAA4B2H,WAAD,IAAiB;AACxC,kBAAIC,UAAU,GAAG;AAAA;AAAA,uDAAjB;AACAD,cAAAA,WAAW,CAAC/F,aAAZ,CAA0B5B,OAA1B,CAAmC6H,YAAD,IAAkB;AAChDD,gBAAAA,UAAU,CAACE,KAAX,CAAiBrH,IAAjB,CAAsBoH,YAAtB,oBAAsBA,YAAY,CAAE5J,IAApC;AACH,eAFD;AAGA2J,cAAAA,UAAU,CAACN,MAAX,GAAoBK,WAAW,CAACL,MAAhC;AAEAM,cAAAA,UAAU,CAAC3E,YAAX,GAA0B0E,WAAW,CAAC1E,YAAtC;AACA2E,cAAAA,UAAU,CAAC3C,OAAX,GAAqB;AAAA;AAAA,yDAArB;AACA2C,cAAAA,UAAU,CAAC3C,OAAX,CAAmB7H,GAAnB,GAAyBuK,WAAW,CAACtE,eAAZ,CAA6BjG,GAAtD;AACAwK,cAAAA,UAAU,CAAC3C,OAAX,CAAmBlC,GAAnB,GAAyB4E,WAAW,CAACtE,eAAZ,CAA6BN,GAAtD;AAEA6E,cAAAA,UAAU,CAAC7C,OAAX,GAAqB;AAAA;AAAA,yDAArB;AACA6C,cAAAA,UAAU,CAAC7C,OAAX,CAAmB3H,GAAnB,GAAyBuK,WAAW,CAAC7E,eAAZ,CAA6B1F,GAAtD;AACAwK,cAAAA,UAAU,CAAC7C,OAAX,CAAmBhC,GAAnB,GAAyB4E,WAAW,CAAC7E,eAAZ,CAA6BC,GAAtD;AACA0E,cAAAA,SAAS,CAACC,OAAV,CAAkBjH,IAAlB,CAAuBmH,UAAvB;AACA/J,cAAAA,OAAO,CAACC,GAAR,CAAY,yCAAZ,EAAuD2J,SAAvD;AACH,aAjBD;AAkBH,WApBD,MAoBO,IAAIxH,KAAK,CAAC/C,IAAN,KAAe;AAAA;AAAA,sCAAU6D,MAA7B,EAAqC;AACxC0G,YAAAA,SAAS,CAACR,QAAV,GAAqB,EAArB;AACAhH,YAAAA,KAAK,CAACyD,YAAN,CAAmB1D,OAAnB,CAA4BqH,WAAD,IAAiB;AACxC,kBAAIU,KAAK,GAAG;AAAA;AAAA,6EAAZ;AACAV,cAAAA,WAAW,CAACpD,eAAZ,CAA4BjE,OAA5B,CAAqCgE,QAAD,IAAc;AAC9C,oBAAI5C,IAAI,GAAG;AAAA;AAAA,qEAAX;AACAA,gBAAAA,IAAI,CAAC4C,QAAL,GAAgB,EAAhB;AACA5C,gBAAAA,IAAI,CAACkG,MAAL,GAActD,QAAQ,CAACsD,MAAvB;AACAtD,gBAAAA,QAAQ,CAACE,cAAT,CAAwBlE,OAAxB,CAAiC4E,cAAD,IAAoB;AAAA;;AAChD,sBAAIoD,WAAW,GAAG;AAAA;AAAA,qEAAlB;AACAA,kBAAAA,WAAW,CAACV,MAAZ,GAAqB1C,cAAc,CAAC0C,MAApC;AACAU,kBAAAA,WAAW,CAAC/J,IAAZ,4BAAmB2G,cAAc,CAACA,cAAlC,qBAAmB,sBAA+B3G,IAAlD;AACA+J,kBAAAA,WAAW,CAACjD,OAAZ,GAAsB;AAAA;AAAA,qFAAqBH,cAAc,CAACG,OAApC,qBAAqB,sBAAwB3H,GAA7C,4BAAkDwH,cAAc,CAACG,OAAjE,qBAAkD,uBAAwBhC,GAA1E,CAAtB;AACAiF,kBAAAA,WAAW,CAAC/C,OAAZ,GAAsB;AAAA;AAAA,sFAAqBL,cAAc,CAACK,OAApC,qBAAqB,uBAAwB7H,GAA7C,4BAAkDwH,cAAc,CAACK,OAAjE,qBAAkD,uBAAwBlC,GAA1E,CAAtB;AACA3B,kBAAAA,IAAI,CAAC4C,QAAL,CAAcvD,IAAd,CAAmBuH,WAAnB;AACH,iBAPD;AAQAD,gBAAAA,KAAK,CAACZ,KAAN,CAAY1G,IAAZ,CAAiBW,IAAjB;AACH,eAbD;AAcAqG,cAAAA,SAAS,CAACR,QAAV,CAAmBxG,IAAnB,CAAwBsH,KAAxB;AACH,aAjBD;AAkBH,WApBM,MAoBA,IAAI9H,KAAK,CAAC/C,IAAN,KAAe;AAAA;AAAA,sCAAU+D,QAA7B,EAAuC,CAE7C;;AACDhB,UAAAA,KAAK,CAACjC,IAAN,CAAYU,YAAZ;AAAA;AAAA,wDAAkEuJ,aAAlE,CAAgFR,SAAhF;AACH;;AAEOS,QAAAA,oBAAoB,CAAC3H,MAAD,EAAuBqG,UAAvB,EAA0D;AAClFrG,UAAAA,MAAM,CAAC4H,IAAP,CAAY,CAACC,CAAD,EAAIC,CAAJ,KAAUD,CAAC,CAACtB,MAAF,GAAWuB,CAAC,CAACvB,MAAnC;AACAvG,UAAAA,MAAM,CAACP,OAAP,CAAgBC,KAAD,IAAW;AACtB,gBAAI4G,UAAU,GAAG;AAAA;AAAA,mDAAjB;;AACA,iBAAKW,mBAAL,CAAyBvH,KAAzB,EAAgC4G,UAAhC;;AACAD,YAAAA,UAAU,CAACnG,IAAX,CAAgBoG,UAAhB;AACH,WAJD;AAKH;;AAEMoB,QAAAA,aAAa,CAAC7G,IAAD,EAAuB;AACvCA,UAAAA,IAAI,CAAChB,IAAL,GAAY,KAAKiG,SAAjB;AACAjF,UAAAA,IAAI,CAACtB,SAAL,GAAiB,KAAKA,SAAtB;AAEAsB,UAAAA,IAAI,CAACnC,eAAL,GAAuB;AAAA;AAAA,qEAAvB;;AACA,eAAK,IAAID,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,eAAL,CAAqBK,WAArB,CAAiCF,MAArD,EAA6DJ,CAAC,EAA9D,EAAkE;AAC9D,kBAAMQ,MAAM,GAAG,KAAKP,eAAL,CAAqBK,WAArB,CAAiCN,CAAjC,CAAf;;AACA,gBAAIQ,MAAM,IAAI,IAAd,EAAoB;AAChB;AACH;;AACD4B,YAAAA,IAAI,CAACnC,eAAL,CAAqBK,WAArB,CAAiCmB,IAAjC,CAAsCjB,MAAM,CAACvB,IAA7C;AACH;;AACD,eAAKuJ,mBAAL,CAAyB,KAAKvI,eAA9B,EAA+CmC,IAAI,CAACnC,eAApD;;AAEAmC,UAAAA,IAAI,CAAC/C,WAAL,GAAmB,EAAnB;AACA+C,UAAAA,IAAI,CAAC9C,SAAL,GAAiB,EAAjB;;AACA,eAAK4J,oBAAL,CAA0B,KAAK7J,WAA/B,EAA4C+C,IAAI,CAAC/C,WAAjD;;AACA,eAAK6J,oBAAL,CAA0B,KAAK5J,SAA/B,EAA0C8C,IAAI,CAAC9C,SAA/C;AACH;;AAEMgK,QAAAA,SAAS,CAACC,KAAD,EAAiBxJ,QAAjB,EAAmC;AAC/C,eAAKyJ,YAAL,CAAkBzJ,QAAlB;;AAEA,eAAK0J,UAAL,CAAgB,CAAC,KAAKxJ,eAAN,CAAhB,EAAwCsJ,KAAxC,EAA+CxJ,QAA/C;;AACA,eAAK0J,UAAL,CAAgB,KAAKpK,WAArB,EAAkCkK,KAAlC,EAAyCxJ,QAAzC;;AACA,eAAK0J,UAAL,CAAgB,KAAKnK,SAArB,EAAgCiK,KAAhC,EAAuCxJ,QAAvC;;AAEA,cAAI,KAAKrB,KAAL,KAAe6K,KAAnB,EAA0B;AACtB;AACH;;AAED,eAAK7K,KAAL,GAAa6K,KAAb;;AACA,cAAIA,KAAJ,EAAW;AACP,iBAAKG,SAAL;AACH,WAFD,MAEO;AACH,iBAAKC,cAAL;AACH;;AACD,eAAK3C,iBAAL,CAAuB,KAAKtI,KAA5B,EAAkC,KAAKH,eAAvC;;AACA,eAAKyI,iBAAL,CAAuB,KAAKtI,KAA5B,EAAkC,KAAKF,aAAvC;;AACA,eAAKoL,gBAAL,CAAsB,KAAKvK,WAA3B,EAAwC,KAAKd,eAA7C,EAA+D,KAAKG,KAApE;;AACA,eAAKkL,gBAAL,CAAsB,KAAKtK,SAA3B,EAAsC,KAAKd,aAA3C,EAA2D,KAAKE,KAAhE;AACH;;AAEO+K,QAAAA,UAAU,CAAClI,MAAD,EAAuBgI,KAAvB,EAAuCxJ,QAAvC,EAA+D;AAC7EwB,UAAAA,MAAM,CAACP,OAAP,CAAgBC,KAAD,IAAW;AACtBA,YAAAA,KAAK,CAACjC,IAAN,CAAYU,YAAZ;AAAA;AAAA,0DAAkEyH,IAAlE,CAAuEoC,KAAvE,EAA8ExJ,QAAQ,GAAG,KAAKe,SAAhB,GAA4BG,KAAK,CAACF,KAAhH;AACH,WAFD;AAGH;;AAEO6I,QAAAA,gBAAgB,CAACrI,MAAD,EAAuBJ,UAAvB,EAAyCoI,KAAzC,EAA8D;AAClFhI,UAAAA,MAAM,CAACP,OAAP,CAAe,CAACC,KAAD,EAAQ0B,KAAR,KAAkB;AAC7B,gBAAI1B,KAAK,CAAC/C,IAAN,KAAe;AAAA;AAAA,wCAAU6D,MAA7B,EAAqC;AACjC,oBAAMV,SAAS,GAAGF,UAAU,CAAC0I,cAAX,CAA2B,SAAQlH,KAAM,EAAzC,CAAlB;AACA,oBAAM2B,WAAW,GAAGjD,SAAS,CAAEwI,cAAX,CAA0B,SAA1B,CAApB;;AAEA,kBAAIN,KAAJ,EAAW;AACP,oBAAIjF,WAAW,CAACnE,QAAZ,CAAqBC,MAArB,IAA+B,CAAnC,EAAuC,OADhC,CAEP;;AACAkE,gBAAAA,WAAW,CAACnE,QAAZ,CAAqBa,OAArB,CAA6B8D,QAAQ,IAAIA,QAAQ,CAACgF,MAAT,GAAkB,IAA3D,EAHO,CAKP;;AACAxF,gBAAAA,WAAW,CAACnE,QAAZ,CAAqBa,OAArB,CAA6B,CAAC8D,QAAD,EAAWiF,UAAX,KAA0B;AACnD,sBAAI3J,MAAM,GAAG,CAAb;;AACA,uBAAK,IAAIJ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGiB,KAAK,CAACyD,YAAN,CAAmBqF,UAAnB,EAA+B9E,eAA/B,CAA+C7E,MAAnE,EAA2EJ,CAAC,EAA5E,EAAgF;AAC5EI,oBAAAA,MAAM,IAAIa,KAAK,CAACyD,YAAN,CAAmBqF,UAAnB,EAA+B9E,eAA/B,CAA+CjF,CAA/C,EAAkDkF,cAAlD,CAAiE9E,MAA3E;AACH;;AACD,sBAAI0E,QAAQ,CAAC3E,QAAT,CAAkBC,MAAlB,IAA4BA,MAAhC,EAAwC;AACpC;AACH;;AAED,sBAAI4J,cAAc,GAAG,CAAC,CAAtB;AACA,sBAAIC,cAAc,GAAG,CAAC,CAAtB,CAVmD,CAWnD;;AACA,sBAAIC,iBAAiB,GAAG,CAAxB;;AACA,uBAAK,MAAMC,aAAX,IAA4BlJ,KAAK,CAACyD,YAAN,CAAmBqF,UAAnB,EAA+B9E,eAA3D,EAA4E;AACxEiF,oBAAAA,iBAAiB,IAAIC,aAAa,CAAC7B,MAAnC;AACH;;AACD,sBAAI8B,gBAAgB,GAAGxG,IAAI,CAACC,MAAL,KAAgBqG,iBAAvC;AACA,sBAAIG,eAAe,GAAG,CAAtB;;AAEA,sBAAIpJ,KAAK,CAACyD,YAAN,CAAmBqF,UAAnB,EAA+B9E,eAA/B,CAA+C7E,MAA/C,KAA0D,CAA9D,EAAiE;AAC7D4J,oBAAAA,cAAc,GAAG,CAAjB;AACH,mBAFD,MAEO;AACH,yBAAK,IAAInE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG5E,KAAK,CAACyD,YAAN,CAAmBqF,UAAnB,EAA+B9E,eAA/B,CAA+C7E,MAAnE,EAA2EyF,CAAC,EAA5E,EAAgF;AAC5EwE,sBAAAA,eAAe,IAAIpJ,KAAK,CAACyD,YAAN,CAAmBqF,UAAnB,EAA+B9E,eAA/B,CAA+CY,CAA/C,EAAkDyC,MAArE;;AACA,0BAAI8B,gBAAgB,IAAIC,eAAxB,EAAyC;AACrCL,wBAAAA,cAAc,GAAGnE,CAAjB;AACAhH,wBAAAA,OAAO,CAACC,GAAR,CAAY,+CAAZ,EAA6DkL,cAA7D;AACA;AACH;AACJ;AACJ,mBA9BkD,CAgCnD;;;AACA,sBAAIM,gBAAgB,GAAG,CAAvB;;AACA,sBAAIrJ,KAAK,CAACyD,YAAN,CAAmBqF,UAAnB,MAAmC,IAAnC,IAA2C9I,KAAK,CAACyD,YAAN,CAAmBqF,UAAnB,EAA+B9E,eAA/B,CAA+C7E,MAA/C,KAA0D,CAArG,IAA0Ga,KAAK,CAACyD,YAAN,CAAmBqF,UAAnB,EAA+B9E,eAA/B,CAA+C+E,cAA/C,MAAmE,IAAjL,EAAuL;AACnLnL,oBAAAA,OAAO,CAACsE,KAAR,CAAc,6BAAd,EAA4C2B,QAAQ,CAACyF,MAAT,CAAiBA,MAAjB,CAAyBnJ,IAArE;AACA;AACH;;AACDH,kBAAAA,KAAK,CAACyD,YAAN,CAAmBqF,UAAnB,EAA+B9E,eAA/B,CAA+C+E,cAA/C,EAA+D9E,cAA/D,CAA8ElE,OAA9E,CAAsFgE,QAAQ,IAAI;AAC9FsF,oBAAAA,gBAAgB,IAAItF,QAAQ,CAACsD,MAA7B;AACH,mBAFD;AAGA,sBAAIkC,eAAe,GAAG5G,IAAI,CAACC,MAAL,KAAgByG,gBAAtC;AACA,sBAAIG,cAAc,GAAG,CAArB;;AAEA,sBAAIxJ,KAAK,CAACyD,YAAN,CAAmBqF,UAAnB,EAA+B9E,eAA/B,CAA+C+E,cAA/C,EAA+D9E,cAA/D,CAA8E9E,MAA9E,KAAyF,CAA7F,EAAgG;AAC5F6J,oBAAAA,cAAc,GAAG,CAAjB;AACH,mBAFD,MAEO;AACHpL,oBAAAA,OAAO,CAACC,GAAR,CAAY,uDAAZ,EAAqEmC,KAAK,CAACyD,YAAN,CAAmBqF,UAAnB,EAA+B9E,eAA/B,CAA+C+E,cAA/C,EAA+D9E,cAA/D,CAA8E9E,MAAnJ;;AACA,yBAAK,IAAIyF,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG5E,KAAK,CAACyD,YAAN,CAAmBqF,UAAnB,EAA+B9E,eAA/B,CAA+C+E,cAA/C,EAA+D9E,cAA/D,CAA8E9E,MAAlG,EAA0GyF,CAAC,EAA3G,EAA+G;AAC3G4E,sBAAAA,cAAc,IAAIxJ,KAAK,CAACyD,YAAN,CAAmBqF,UAAnB,EAA+B9E,eAA/B,CAA+C+E,cAA/C,EAA+D9E,cAA/D,CAA8EW,CAA9E,EAAiFyC,MAAnG;;AACA,0BAAIkC,eAAe,IAAIC,cAAvB,EAAuC;AACnCR,wBAAAA,cAAc,GAAGpE,CAAjB;AACAhH,wBAAAA,OAAO,CAACC,GAAR,CAAY,+CAAZ,EAA6DmL,cAA7D;AACA;AACH;AACJ;AACJ,mBAxDkD,CA0DnD;;;AACAnF,kBAAAA,QAAQ,CAAC3E,QAAT,CAAkBa,OAAlB,CAA2B0J,WAAD,IAAiB;AACvC,0BAAMlG,KAAK,GAAGkG,WAAW,CAACtJ,IAAZ,CAAiBoD,KAAjB,CAAuB,oBAAvB,CAAd;AACA,wBAAIsF,MAAM,GAAG,KAAb;;AACA,wBAAItF,KAAJ,EAAW;AACP,4BAAM7B,KAAK,GAAG8B,QAAQ,CAACD,KAAK,CAAC,CAAD,CAAN,CAAtB;AACA,4BAAMmG,SAAS,GAAGlG,QAAQ,CAACD,KAAK,CAAC,CAAD,CAAN,CAA1B;;AACA,0BAAI7B,KAAK,KAAKqH,cAAV,IAA4BW,SAAS,KAAKV,cAA9C,EAA8D;AAC1DH,wBAAAA,MAAM,GAAG,IAAT;AACH;;AACDY,sBAAAA,WAAW,CAACZ,MAAZ,GAAqBA,MAArB;AACA,4BAAMzE,OAAO,GAAGqF,WAAW,CAAChL,YAAZ;AAAA;AAAA,qDAAhB;;AACA,0BAAI2F,OAAJ,EAAa;AACTA,wBAAAA,OAAO,CAAC8B,IAAR,CAAa2C,MAAb;AACH;AACJ,qBAXD,MAWO;AACHY,sBAAAA,WAAW,CAACZ,MAAZ,GAAqBA,MAArB;AACH;AAEJ,mBAlBD;AAmBH,iBA9ED;AA+EH,eArFD,MAqFO;AACHxF,gBAAAA,WAAW,CAACnE,QAAZ,CAAqBa,OAArB,CAA6B8D,QAAQ,IAAI;AACrCA,kBAAAA,QAAQ,CAACgF,MAAT,GAAkB,IAAlB;AACAhF,kBAAAA,QAAQ,CAAC3E,QAAT,CAAkBa,OAAlB,CAA0B0J,WAAW,IAAI;AACrCA,oBAAAA,WAAW,CAACZ,MAAZ,GAAqB,IAArB;AACA,0BAAMzE,OAAO,GAAGqF,WAAW,CAAChL,YAAZ;AAAA;AAAA,mDAAhB;;AACA,wBAAI2F,OAAJ,EAAa;AACTA,sBAAAA,OAAO,CAAC8B,IAAR,CAAa,KAAb;AACH;AACJ,mBAND;AAOH,iBATD;AAUH;AACJ,aArGD,MAqGO,IAAIlG,KAAK,CAAC/C,IAAN,KAAe;AAAA;AAAA,wCAAU2D,MAA7B,EAAqC;AACxCV,cAAAA,UAAU,CAAChB,QAAX,CAAoBa,OAApB,CAA4BK,SAAS,IAAI;AACrC;AACA,sBAAMgB,WAAW,GAAGhB,SAAS,CAACwI,cAAV,CAAyB,SAAzB,CAApB;;AACA,oBAAIxH,WAAW,CAAClC,QAAZ,CAAqBC,MAArB,KAAgCa,KAAK,CAACuB,YAAN,CAAmBpC,MAAvD,EAA+D;AAC3D;AACH;;AACDiC,gBAAAA,WAAW,CAAClC,QAAZ,CAAqBa,OAArB,CAA6BK,SAAS,IAAIA,SAAS,CAACyI,MAAV,GAAmB,IAA7D;;AAEA,oBAAIP,KAAJ,EAAW;AACP;AACA,sBAAIqB,WAAW,GAAG,CAAlB;;AACA,uBAAK,MAAMjC,WAAX,IAA0B1H,KAAK,CAACuB,YAAhC,EAA8C;AAC1CoI,oBAAAA,WAAW,IAAIjC,WAAW,CAACL,MAA3B;AACH,mBALM,CAOP;;;AACA,sBAAIuC,YAAY,GAAGjH,IAAI,CAACC,MAAL,KAAgB+G,WAAnC;AACA,sBAAIE,aAAa,GAAG,CAAC,CAArB;;AACA,uBAAK,IAAI9K,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGiB,KAAK,CAACuB,YAAN,CAAmBpC,MAAvC,EAA+CJ,CAAC,EAAhD,EAAoD;AAChD6K,oBAAAA,YAAY,IAAI5J,KAAK,CAACuB,YAAN,CAAmBxC,CAAnB,EAAsBsI,MAAtC;;AACA,wBAAIuC,YAAY,IAAI,CAApB,EAAuB;AACnBC,sBAAAA,aAAa,GAAG9K,CAAhB;AACAnB,sBAAAA,OAAO,CAACC,GAAR,CAAY,+BAAZ,EAA6CgM,aAA7C;AACA;AACH;AACJ;;AAEDzI,kBAAAA,WAAW,CAAClC,QAAZ,CAAqBa,OAArB,CAA6B,CAAC0C,KAAD,EAAQf,KAAR,KAAkB;AAC3C,0BAAMmH,MAAM,GAAInH,KAAK,KAAKmI,aAA1B;AACApH,oBAAAA,KAAK,CAACoG,MAAN,GAAeA,MAAf;;AACA,wBAAIA,MAAJ,EAAY;AACRpG,sBAAAA,KAAK,CAACvD,QAAN,CAAea,OAAf,CAAuBb,QAAQ,IAAI;AAC/B,4BAAIA,QAAQ,CAACT,YAAT;AAAA;AAAA,uDAAJ,EAAwC;AACpCS,0BAAAA,QAAQ,CAACT,YAAT;AAAA;AAAA,0DAAoCyH,IAApC,CAAyC,IAAzC;AACH;AACJ,uBAJD;AAKH;;AACDtI,oBAAAA,OAAO,CAACC,GAAR,CAAY,kCAAZ,EAAgD4E,KAAK,CAACtC,IAAtD,EAA2DuB,KAA3D,EAAiE,eAAjE,EAAiFmI,aAAjF,EAA+F,QAA/F,EAAwGpH,KAAK,CAACoG,MAA9G;AACH,mBAXD;AAYH,iBA/BD,MA+BO;AACHzH,kBAAAA,WAAW,CAAClC,QAAZ,CAAqBa,OAArB,CAA6B0C,KAAK,IAAI;AAClCA,oBAAAA,KAAK,CAACoG,MAAN,GAAe,IAAf;AACApG,oBAAAA,KAAK,CAACvD,QAAN,CAAea,OAAf,CAAuBb,QAAQ,IAAI;AAC/B,0BAAIA,QAAQ,CAACT,YAAT;AAAA;AAAA,qDAAJ,EAAwC;AACpCS,wBAAAA,QAAQ,CAACT,YAAT;AAAA;AAAA,wDAAoCyH,IAApC,CAAyC,KAAzC;AACH;AACJ,qBAJD;AAKH,mBAPD;AAQH;AACJ,eAjDD;AAkDH;AACJ,WA1JD;AA2JH;;AAEOQ,QAAAA,iBAAiB,GAAS;AAC9B,gBAAMoD,QAAQ,GAAG,KAAKpM,SAAL,CAAgBe,YAAhB,CAA6BvD,QAA7B,CAAjB;;AACA,cAAI,CAAC4O,QAAL,EAAe;;AAEf,gBAAMC,aAAa,GAAG,KAAKrM,SAAL,CAAgBe,YAAhB,CAA6BzD,WAA7B,CAAtB;;AAEA,gBAAMgP,QAAQ,GAAG,IAAI5O,IAAJ,CACb,KAAKsC,SAAL,CAAgBuM,WAAhB,GAA8B9C,CAA9B,GAAkC4C,aAAa,CAACrL,WAAd,CAA0BwL,KAA1B,GAAkC,CADvD,EAEb,KAAKxM,SAAL,CAAgBuM,WAAhB,GAA8BtK,CAA9B,GAAkCoK,aAAa,CAACrL,WAAd,CAA0BF,MAA1B,GAAmC,CAFxD,EAGbuL,aAAa,CAACrL,WAAd,CAA0BwL,KAHb,EAIb,KAAK9M,YAJQ,CAAjB,CAN8B,CAa9B;;AACA0M,UAAAA,QAAQ,CAACK,WAAT,GAAuBhP,KAAK,CAACiP,IAA7B;AACAN,UAAAA,QAAQ,CAACO,SAAT,GAAqB,EAArB;AACAP,UAAAA,QAAQ,CAACQ,IAAT,CAAcN,QAAQ,CAAC7C,CAAvB,EAA0B6C,QAAQ,CAACrK,CAAnC,EAAsCqK,QAAQ,CAACE,KAA/C,EAAsDF,QAAQ,CAACxL,MAA/D;AACAsL,UAAAA,QAAQ,CAACS,MAAT;;AAEA,gBAAMC,YAAY,GAAG,KAAK9M,SAAL,CAAgBkL,cAAhB,CAA+B,UAA/B,EAA4CnK,YAA5C,CAAyDvD,QAAzD,CAArB;;AACA,cAAI,CAACsP,YAAL,EAAmB;AAEnB,gBAAMC,QAAQ,GAAG,IAAIrP,IAAJ,CACb,CAAC,GAAD,GAAO,CADM,EAEb,CAAC,IAAD,GAAQ,CAFK,EAGb,GAHa,EAIb,KAAKgC,YAJQ,CAAjB;AAOAoN,UAAAA,YAAY,CAACL,WAAb,GAA2BhP,KAAK,CAACuP,GAAjC;AACAF,UAAAA,YAAY,CAACH,SAAb,GAAyB,EAAzB;AACAG,UAAAA,YAAY,CAACF,IAAb,CAAkBG,QAAQ,CAACtD,CAA3B,EAA8BsD,QAAQ,CAAC9K,CAAvC,EAA0C8K,QAAQ,CAACP,KAAnD,EAA0DO,QAAQ,CAACjM,MAAnE;AACAgM,UAAAA,YAAY,CAACD,MAAb;AACH;;AAEO9B,QAAAA,SAAS,GAAS;AACtB,cAAI,CAAC,KAAKhL,KAAV,EAAiB;;AAEjB,gBAAMkN,YAAY,GAAG,KAAKjN,SAAL,CAAgBkL,cAAhB,CAA+B,UAA/B,EAA4CnK,YAA5C,CAAyDvD,QAAzD,CAArB;;AACA,cAAI,CAACyP,YAAL,EAAmB,OAJG,CAMtB;;AACA,gBAAMC,SAAS,GAAG,KAAlB;AACA,gBAAMC,UAAU,GAAG,IAAnB;AACAF,UAAAA,YAAY,CAACG,SAAb,GAAyB3P,KAAK,CAAC4P,KAA/B,CATsB,CAWtB;;AACAJ,UAAAA,YAAY,CAACK,QAAb,CACI,CAACJ,SAAD,GAAa,CADjB,EAEI,KAAKlN,SAAL,CAAgBuM,WAAhB,GAA8BtK,CAA9B,GAAkCkL,UAAlC,GAA+CA,UAAU,GAAG,CAFhE,EAGID,SAHJ,EAIIC,UAJJ,EAZsB,CAmBtB;;AACAF,UAAAA,YAAY,CAACK,QAAb,CACI,CAACJ,SAAD,GAAa,CADjB,EAEI,KAAKlN,SAAL,CAAgBuM,WAAhB,GAA8BtK,CAA9B,GAAkCkL,UAAlC,GAA+CA,UAAU,GAAG,CAFhE,EAGID,SAHJ,EAIIC,UAJJ,EApBsB,CA2BtB;;AACAF,UAAAA,YAAY,CAACK,QAAb,CACI,CAACJ,SAAD,GAAa,MAAM,CADvB,EAEI,KAAKlN,SAAL,CAAgBuM,WAAhB,GAA8BtK,CAA9B,GAAkCkL,UAAU,GAAG,CAFnD,EAGID,SAHJ,EAIIC,UAJJ,EA5BsB,CAmCtB;;AACAF,UAAAA,YAAY,CAACK,QAAb,CACI,MAAM,CADV,EAEI,KAAKtN,SAAL,CAAgBuM,WAAhB,GAA8BtK,CAA9B,GAAkCkL,UAAU,GAAG,CAFnD,EAGID,SAHJ,EAIIC,UAJJ;AAMH;;AAEOtC,QAAAA,YAAY,CAAEzJ,QAAF,EAAoB;AACpC,cAAI,CAAC,KAAKrB,KAAV,EAAiB;;AAEjB,gBAAMwN,QAAQ,GAAG,KAAKvN,SAAL,CAAgBkL,cAAhB,CAA+B,MAA/B,CAAjB;;AACA,gBAAMsC,YAAY,GAAG,KAAKxN,SAAL,CAAgBkL,cAAhB,CAA+B,UAA/B,CAArB;;AACA,cAAIqC,QAAQ,IAAIC,YAAhB,EAA8B;AAC1BD,YAAAA,QAAQ,CAACpC,MAAT,GAAkB,IAAlB;AACAqC,YAAAA,YAAY,CAACrC,MAAb,GAAsB,IAAtB;AACAoC,YAAAA,QAAQ,CAACxM,YAAT,CAAsBpD,KAAtB,EAA8B8P,MAA9B,GAAwC,MAAK,CAAC,KAAKtL,SAAL,GAAiBf,QAAjB,GAA4B,IAA7B,EAAmCsM,OAAnC,CAA2C,CAA3C,CAA8C,EAA3F;AACAF,YAAAA,YAAY,CAACzM,YAAb,CAA0BpD,KAA1B,EAAkC8P,MAAlC,GAA4C,MAAKrM,QAAQ,CAACsM,OAAT,CAAiB,CAAjB,CAAoB,EAArE;AACH;AACJ;;AAEO1C,QAAAA,cAAc,GAAS;AAC3B,gBAAMuC,QAAQ,GAAG,KAAKvN,SAAL,CAAgBkL,cAAhB,CAA+B,MAA/B,CAAjB;;AACA,gBAAMsC,YAAY,GAAG,KAAKxN,SAAL,CAAgBkL,cAAhB,CAA+B,UAA/B,CAArB;;AACA,cAAIqC,QAAQ,IAAIC,YAAhB,EAA8B;AAC1BD,YAAAA,QAAQ,CAACpC,MAAT,GAAkB,KAAlB;AACAqC,YAAAA,YAAY,CAACrC,MAAb,GAAsB,KAAtB;AACH;;AACD,gBAAM8B,YAAY,GAAG,KAAKjN,SAAL,CAAgBkL,cAAhB,CAA+B,UAA/B,EAA4CnK,YAA5C,CAAyDvD,QAAzD,CAArB;;AACA,cAAI,CAACyP,YAAL,EAAmB;AAEnBA,UAAAA,YAAY,CAACU,KAAb;AACH;;AAl2B4C,O;;;;;iBAElB,E;;;;;;;iBAEA,I;;;;;;;iBAIoB;AAAA;AAAA,6D;;;;;;;iBAEZ,E;;;;;;;iBAEF,E", "sourcesContent": ["import { _decorator, CCFloat, Component, Node, CCString, Prefab, assetManager, instantiate, UITransform, view, Graphics, Color, Rect, Label, Vec3, Quat } from 'cc';\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\nimport { LayerRandomRange, LayerSplicingMode, LayerType, LevelData, LevelDataBackgroundLayer, LevelDataLayer, LevelDataRandTerrain, LevelDataRandTerrains, LevelDataRandTerrainsGroup, LevelDataScroll } from 'db://assets/bundles/common/script/leveldata/leveldata';\r\nimport { LevelEditorLayerUI } from './LevelEditorLayerUI';\r\nimport { LayerEditorRandomRange, LevelBackgroundLayer, LevelEditorUtils, LevelLayer, LevelRandTerrainsLayersUI, LevelRandTerrainsLayerUI, LevelRandTerrainUI } from './utils';\r\nimport { RandTerrain } from 'db://assets/bundles/common/script/game/dyncTerrain/RandTerrain';\r\nimport { WavePreview } from './preview/WavePreview';\r\nimport { EmittierTerrain } from '../../bundles/common/script/game/dyncTerrain/EmittierTerrain';\r\n\r\nconst BackgroundsNodeName = \"backgrounds\";\r\n\r\n@ccclass('LevelEditorBaseUI')\r\n@executeInEditMode()\r\nexport class LevelEditorBaseUI extends Component {\r\n    @property(CCString)\r\n    public levelname: string = \"\";\r\n    @property({type:CCFloat, displayName:\"关卡时长(ms)\",min:2000})\r\n    public totalTime: number = 2000;\r\n    private _totalHeight: number = 0;\r\n\r\n    @property({type:LevelBackgroundLayer, displayName:\"背景\"})\r\n    public backgroundLayer: LevelBackgroundLayer = new LevelBackgroundLayer();\r\n    @property({type:[LevelLayer], displayName:\"地面\"})\r\n    public floorLayers: LevelLayer[] = [];\r\n    @property({type:[LevelLayer], displayName:\"天空\"})\r\n    public skyLayers: LevelLayer[] = [];\r\n\r\n    private backgroundLayerNode:Node|null = null;\r\n    private floorLayersNode:Node|null = null;\r\n    private skyLayersNode:Node|null = null;\r\n\r\n    private _isLoadingScrollNodes: boolean = false;\r\n    private _play: boolean = false;\r\n    private _drawNode: Node | null = null;\r\n\r\n    onLoad():void {\r\n        console.log(`LevelEditorBaseUI start.`);\r\n        this.backgroundLayerNode = LevelEditorUtils.getOrAddNode(this.node, \"BackgroundLayer\");\r\n        this.floorLayersNode = LevelEditorUtils.getOrAddNode(this.node, \"FloorLayers\");\r\n        this.skyLayersNode = LevelEditorUtils.getOrAddNode(this.node, \"SkyLayers\");\r\n\r\n        this._drawNode = LevelEditorUtils.getOrAddNode(this.node, \"DrawNode\");\r\n        \r\n        console.log(`LevelEditorBaseUI start ${this.floorLayersNode?.uuid}`);\r\n    }\r\n    update(dt:number):void {\r\n        this.checkLayerNode(this.floorLayersNode!, this.floorLayers,dt);\r\n        this.checkLayerNode(this.skyLayersNode!, this.skyLayers,dt);\r\n    }\r\n    private setBackgroundNodePosition(node:Node, yOff:number):number {\r\n        const height = node.getComponent(UITransform)!.contentSize.height;\r\n        node.setPosition(0, yOff-view.getVisibleSize().height / 2 + height / 2);\r\n        return height;\r\n\r\n    }\r\n    public tick(progress: number):void {\r\n        let yOff = 0\r\n        for (let i = 0; i < this.backgroundLayer.backgroundsNode!.children.length; i++) {\r\n            var bg = this.backgroundLayer.backgroundsNode!.children[i]\r\n            yOff += this.setBackgroundNodePosition(bg, yOff)\r\n        }\r\n        while(this.backgroundLayer.backgrounds.length > 0 && yOff < this._totalHeight) {\r\n            let bg:Node|null = null;\r\n            let bgIndex = this.backgroundLayer.backgroundsNode!.children.length % this.backgroundLayer.backgrounds.length;\r\n            const prefab = this.backgroundLayer.backgrounds[bgIndex]\r\n            if (prefab != null) {\r\n                bg = instantiate(prefab)\r\n            } \r\n            if (bg == null) {\r\n                bg = new Node(\"empty\");\r\n                bg.addComponent(UITransform).height = 1024;\r\n            }\r\n            this.backgroundLayer.backgroundsNode!.addChild(bg);\r\n            yOff += this.setBackgroundNodePosition(bg, yOff)\r\n        }\r\n        for (let i = this.backgroundLayer.backgroundsNode!.children.length - 1; i >= 0; i--) {\r\n            const bg = this.backgroundLayer.backgroundsNode!.children[i]\r\n            if (bg.position.y - bg.getComponent(UITransform)!.height/2 > this._totalHeight) {\r\n                bg.removeFromParent()\r\n            } else {\r\n                break;\r\n            }\r\n        }\r\n\r\n        this.backgroundLayer!.node!.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI)!.tick(\r\n            progress, this.totalTime, this.backgroundLayer.speed);\r\n        this.floorLayers.forEach((layer) => {\r\n            if (layer.node && layer.node.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI)) {\r\n                layer.node!.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI)!.tick(progress, this.totalTime, layer.speed);\r\n            }\r\n        });\r\n        this.skyLayers.forEach((layer) => {\r\n            if (layer.node && layer.node.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI)) {\r\n                layer.node!.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI)!.tick(progress, this.totalTime, layer.speed);\r\n            }\r\n        });\r\n    }\r\n\r\n    private static addLayer(parentNode: Node, name: string): LevelEditorLayerUI {\r\n        var layerNode = new Node(name);\r\n        var layerCom = layerNode.addComponent<LevelEditorLayerUI>(LevelEditorLayerUI);\r\n        parentNode.addChild(layerNode);\r\n        return layerCom;\r\n    }\r\n\r\n    private checkLayerNode(parentNode: Node, layers: LevelLayer[], dt: number):void {\r\n        var removeLayerNodes: Node[] = []\r\n        parentNode.children.forEach(node => {\r\n            var layerCom = node.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI);\r\n            if (layerCom == null) {\r\n                console.log(`Level checkLayerNode remove ${node.name} because layerCom == null\"`);\r\n                removeLayerNodes.push(node)\r\n                return;\r\n            }\r\n            if (layers.find((layer) => layer.node == node) == null) {\r\n                console.log(`Level checkLayerNode remove ${node.name} because not in layers\"`);\r\n                removeLayerNodes.push(node)\r\n                return;\r\n            }\r\n        });\r\n        removeLayerNodes.forEach(element => {\r\n            element.removeFromParent();    \r\n        });\r\n        layers.forEach((layer, i) => {\r\n            if (layer.node == null || layer.node.isValid == false) {\r\n                console.log(`Level checkLayerNode add because layer == null`);\r\n                layer.node = LevelEditorBaseUI.addLayer(parentNode, `layer_${i}`).node; \r\n            }\r\n\r\n            if (layer.type === LayerType.Scroll) {\r\n                this._checkScrollNode(layer, layer!.node);\r\n            } else if (layer.type === LayerType.Random) {\r\n                this._checkRandTerrainNode(layer, layer!.node);\r\n            } else if (layer.type === LayerType.Emittier) {\r\n                this._checkEmittierNode(layer, layer!.node);   \r\n                this._updateEmittierNode(dt, layer!.node);\r\n            }\r\n        });\r\n    }\r\n\r\n    private async _checkScrollNode(data: LevelLayer, parentNode: Node):Promise<void> {\r\n        const scrollsNode = LevelEditorUtils.getOrAddNode(parentNode, \"scrolls\");\r\n        if (data.type != LayerType.Scroll) {\r\n            scrollsNode.removeAllChildren();\r\n            return;\r\n        }\r\n\r\n        if (this._isLoadingScrollNodes) {\r\n            //console.log(`LevelEditorBaseUI _checkScrollNode _isLoadingScrollNodes ${this._isLoadingScrollNodes} scrollsNode.children.length ${scrollsNode.children.length}`);\r\n            return;\r\n        }\r\n\r\n        const isCountMatch = scrollsNode.children.length === data.scrollLayers.length;\r\n        //console.log(`LevelEditorBaseUI _checkScrollNode children.length ${scrollsNode.children.length} data.scrollLayers.length ${data.scrollLayers.length}`); \r\n        if (!isCountMatch) {\r\n            const loadPromises: Promise<void>[] = [];\r\n            scrollsNode.removeAllChildren();\r\n            this._isLoadingScrollNodes = true; // 标记为加载中\r\n\r\n            data.scrollLayers.forEach((scroll, index) => {\r\n                if (scroll.scrollPrefabs.length <= 0) return;\r\n\r\n                    scroll.scrollPrefabs.forEach(prefab => {\r\n                        const loadPromise = new Promise<void>((resolve) => {\r\n                            assetManager.loadAny({ uuid: prefab!.uuid }, (err: Error, loadedPrefab: Prefab) => {\r\n                                if (err) {\r\n                                    console.error(\"Failed to load prefab:\", err);\r\n                                    resolve();\r\n                                } else {\r\n                                    resolve();\r\n                                }\r\n                            });\r\n                    });\r\n                    \r\n                    loadPromises.push(loadPromise);\r\n                });\r\n            });\r\n\r\n            await Promise.all(loadPromises);\r\n            //console.log(`LevelEditorBaseUI _checkScrollNode data.scrollLayers ${data.scrollLayers.length}`);\r\n            data.scrollLayers.forEach((scroll, index) => {\r\n                const scrollNode = LevelEditorUtils.getOrAddNode(scrollsNode, `scroll_${index}`);\r\n                const totalHeight = data.speed * this.totalTime / 1000; \r\n                console.log(`LevelEditorBaseUI _checkScrollNode totalHeight ${totalHeight}`);\r\n                let posOffsetY = 0;\r\n                let height = 0;\r\n                let prefabIndex = 0;\r\n\r\n                while (height < totalHeight) {\r\n                    const curPrefab = scroll.scrollPrefabs[prefabIndex];\r\n                    const child = instantiate(curPrefab);\r\n                    const randomOffsetX = Math.random() * (scroll.splicingOffsetX!.max - scroll.splicingOffsetX!.min) + scroll.splicingOffsetX!.min;\r\n                    child.setPosition(randomOffsetX, posOffsetY, 0);\r\n\r\n                    let offY = 0;\r\n                    if (scroll.splicingMode === LayerSplicingMode.node_height) {\r\n                        offY = child.getComponent(UITransform)!.contentSize.height;\r\n                    } else if (scroll.splicingMode === LayerSplicingMode.fix_height) {\r\n                        offY = 1334;\r\n                    } else if (scroll.splicingMode === LayerSplicingMode.random_height) {\r\n                        offY = Math.max(scroll.splicingOffsetY!.min, scroll.splicingOffsetY!.max) + child.getComponent(UITransform)!.contentSize.height;\r\n                    }\r\n\r\n                    scrollNode.addChild(child);\r\n                    posOffsetY += offY;\r\n                    height += offY;\r\n                    prefabIndex = (prefabIndex + 1) % scroll.scrollPrefabs.length;\r\n                }\r\n            });\r\n            this._isLoadingScrollNodes = false; \r\n        }\r\n    }\r\n\r\n    private _checkRandTerrainNode(data: LevelLayer, parentNode: Node):void {\r\n        const dynamicNode = LevelEditorUtils.getOrAddNode(parentNode, \"dynamic\");\r\n\r\n        // 删除所有多余的dyna节点\r\n        const currentDynaNodes = dynamicNode.children;\r\n        for (let i = currentDynaNodes.length - 1; i >= 0; i--) {\r\n            const node = currentDynaNodes[i];\r\n            const match = node.name.match(/^dyna_(\\d+)$/);\r\n            if (match) {\r\n                const index = parseInt(match[1]);\r\n                if (index >= data.randomLayers.length) {\r\n                    node.removeFromParent();\r\n                }\r\n            }\r\n        }\r\n\r\n        if (data.type != LayerType.Random || data.randomLayers.length === 0) {\r\n            dynamicNode.removeAllChildren();\r\n            return;\r\n        }\r\n\r\n        let needRebuild = false;\r\n        const rebuildList: number[] = [];\r\n        \r\n        for (let i = 0; i < data.randomLayers.length; i++) {\r\n            const randTerrains = data.randomLayers[i];\r\n            const dynaNode = LevelEditorUtils.getOrAddNode(dynamicNode, `dyna_${i}`);\r\n            \r\n            // 计算该dyna节点应有的总地形元素数量\r\n            let expectedChildCount = 0;\r\n            for (const terrains of randTerrains.dynamicTerrains) {\r\n                expectedChildCount += terrains.dynamicTerrain.length;\r\n            }\r\n            \r\n            // 检查子节点数量是否匹配\r\n            if (dynaNode.children.length !== expectedChildCount) {\r\n                needRebuild = true;\r\n                rebuildList.push(i);\r\n                continue;\r\n            }\r\n            \r\n            // 检查每个子节点对应的预制体UUID是否匹配\r\n            let childIndex = 0;\r\n            let isUUIDMatch = true;\r\n            \r\n            for (const terrains of randTerrains.dynamicTerrains) {\r\n                for (const terrain of terrains.dynamicTerrain) {\r\n                    const childNode = dynaNode.children[childIndex];\r\n                    // @ts-ignore\r\n                    const childPrefabUUID = childNode._prefab?.asset?._uuid;\r\n                    const terrainUUID = terrain?.terrainElement?.uuid;\r\n                    \r\n                    if (childPrefabUUID !== terrainUUID) {\r\n                        isUUIDMatch = false;\r\n                        break;\r\n                    }\r\n                    childIndex++;\r\n                }\r\n                if (!isUUIDMatch) break;\r\n            }\r\n            \r\n            if (!isUUIDMatch) {\r\n                needRebuild = true;\r\n                rebuildList.push(i);\r\n            }\r\n        }\r\n\r\n        if (needRebuild) {\r\n            //console.log(\"LevelEditorBaseUI _checkRandTerrainNode need rebuild\");\r\n\r\n            for (const index of rebuildList) {\r\n                const dynaNode = LevelEditorUtils.getOrAddNode(dynamicNode, `dyna_${index}`);\r\n                dynaNode.removeAllChildren();\r\n                const randTerrains = data.randomLayers[index];\r\n                // 遍历所有地形组\r\n                for (let j = 0; j < randTerrains.dynamicTerrains.length; j++) {\r\n                    const terrains = randTerrains.dynamicTerrains[j];\r\n                    \r\n                    // 遍历地形组中的每个地形元素\r\n                    for (let k = 0; k < terrains.dynamicTerrain.length; k++) {\r\n                        const terrain = terrains.dynamicTerrain[k];\r\n                        assetManager.loadAny({ uuid: terrain?.terrainElement?.uuid }, (err, prefab: Prefab) => { \r\n                            if (err) {\r\n                                //console.error(`加载地形元素失败: ${terrain?.terrainElements?.uuid}`, err);\r\n                                return;\r\n                            }\r\n\r\n                            const node = instantiate(prefab);\r\n                            node.name = `rand_${j}_${k}`;\r\n                            dynaNode.addChild(node);\r\n                            const randomOffsetX = Math.random() * (terrain.offSetX!.max - terrain.offSetX!.min) + terrain.offSetX!.min;   \r\n                            const randomOffsetY = Math.random() * (terrain.offSetY!.max - terrain.offSetY!.min) + terrain.offSetY!.min;\r\n                            dynamicNode.setPosition(randomOffsetX, randomOffsetY, 0);\r\n                        });\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    private _checkEmittierNode(data: LevelLayer, parentNode: Node):void {\r\n        const emittierNode = LevelEditorUtils.getOrAddNode(parentNode, \"emittiers\");\r\n        if (data.type != LayerType.Emittier) {\r\n            emittierNode.removeAllChildren();\r\n            return;\r\n        }\r\n\r\n        // 1. 按索引遍历数据层\r\n        for (let i = 0; i < data.emittierLayers.length; i++) {\r\n            const emitterData = data.emittierLayers[i];\r\n            \r\n            // 安全获取节点 - 检查索引是否有效\r\n            const existingNode = i < emittierNode.children.length ? \r\n                                emittierNode.children[i] : \r\n                                null;\r\n            \r\n            // 检查是否需要重新加载节点\r\n            let needReload = false;\r\n            \r\n            if (!existingNode || emitterData === null) {\r\n                // 节点不存在，需要创建新节点\r\n                needReload = true;\r\n            } else {\r\n                // 检查UUID是否匹配\r\n                // @ts-ignore\r\n                if (existingNode._prefab?.asset?.uuid !== emitterData.uuid ) {\r\n                    needReload = true; \r\n                }\r\n            }\r\n            \r\n            // 重新加载节点\r\n            if (needReload) {\r\n                // 保存原有位置和缩放信息（如果节点存在）\r\n                const oldPosition = existingNode ? existingNode.position.clone() : new Vec3(0, 0, 0);\r\n                const oldRotation = existingNode ? existingNode.rotation.clone() : new Quat();\r\n                const oldScale = existingNode ? existingNode.scale.clone() : new Vec3(1, 1, 1);\r\n                \r\n                // 移除旧节点（如果存在）\r\n                if (existingNode) {\r\n                    existingNode.removeFromParent();\r\n                }\r\n                \r\n                if (emitterData && emitterData.uuid) {\r\n                    assetManager.loadAny({ uuid: emitterData.uuid }, (err, prefab: Prefab) => {\r\n                        if (err) {\r\n                            return;\r\n                        } else {\r\n                            \r\n                            const emitterNode = instantiate(prefab);\r\n                            emitterNode.name = `emittier_${i}`;\r\n                            emitterNode.position = oldPosition;\r\n                            emitterNode.rotation = oldRotation;\r\n                            emitterNode.scale = oldScale;\r\n                            \r\n                            emittierNode.addChild(emitterNode);\r\n                            emitterNode.setSiblingIndex(i);\r\n                        }\r\n                    });\r\n                } else {\r\n                    return;\r\n                }\r\n            }\r\n        }\r\n        \r\n        // 2. 删除多余的节点\r\n        while (emittierNode.children.length > data.emittierLayers.length) {\r\n            const lastIndex = emittierNode.children.length - 1;\r\n            emittierNode.children[lastIndex].removeFromParent();\r\n        }\r\n    }\r\n\r\n    private _playEmittierNode(value: boolean, parentNode: Node):void {\r\n        parentNode.children.forEach(node => {\r\n            const emittierNode = LevelEditorUtils.getOrAddNode(node, \"emittiers\");\r\n            console.log(\"play emittier   ---------- 0\");\r\n            if (emittierNode === null || emittierNode.children.length === 0) {\r\n                return;\r\n            }\r\n            console.log(\"play emittier   ---------- 0 <USER> <GROUP>\");\r\n\r\n            for (let i = 0; i < emittierNode.children.length; i++) {\r\n                const emitterNode = emittierNode.children[i];\r\n                const emittier = emitterNode.getComponent(EmittierTerrain);\r\n                console.log(\"play emittier\", value);\r\n                if (emittier) {\r\n                    console.log(\"play emittier ------ 1\", value);\r\n                    emittier.play(value);\r\n                }\r\n            }\r\n        });\r\n    }\r\n\r\n    private _updateEmittierNode(dt: number, parentNode: Node):void {\r\n        const emittierNode = LevelEditorUtils.getOrAddNode(parentNode, \"emittiers\");\r\n        if (emittierNode === null || emittierNode.children.length === 0) {\r\n            return;\r\n        }\r\n\r\n        for (let i = 0; i < emittierNode.children.length; i++) {\r\n            const emitterNode = emittierNode.children[i];\r\n            const emittier = emitterNode.getComponent(EmittierTerrain);\r\n            if (emittier) {\r\n                emittier.tick(dt);\r\n            }\r\n        }\r\n    }\r\n\r\n    public initByLevelData(data: LevelData):void {\r\n        this.levelname = data.name;\r\n        this.totalTime = data.totalTime;\r\n\r\n        this.backgroundLayerNode!.removeAllChildren()\r\n        this.backgroundLayer = new LevelBackgroundLayer();\r\n        this.backgroundLayer.backgrounds = [];\r\n        data.backgroundLayer?.backgrounds?.forEach((background) => {\r\n            assetManager.loadAny({uuid:background}, (err: Error, prefab:Prefab) => {\r\n                if (err) {\r\n                    console.error(\"LevelEditorBaseUI initByLevelData load background prefab err\", err);\r\n                    return\r\n                } \r\n                this.backgroundLayer.backgrounds.push(prefab);\r\n            });\r\n        });\r\n        this.backgroundLayer.speed = data.backgroundLayer?.speed;\r\n        this.backgroundLayer.remark = data.backgroundLayer?.remark;\r\n        this._totalHeight = this.backgroundLayer.speed * this.totalTime / 1000;\r\n        this.backgroundLayer.node = LevelEditorBaseUI.addLayer(this.backgroundLayerNode!, \"layer\").node;\r\n        this.backgroundLayer.backgroundsNode = LevelEditorUtils.getOrAddNode(this.backgroundLayer.node, BackgroundsNodeName);\r\n        this.backgroundLayer.backgroundsNode.setSiblingIndex(0); \r\n        this.backgroundLayer.node.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI)!.initByLevelData(data.backgroundLayer);\r\n    \r\n        this.floorLayers = []\r\n        this.skyLayers = []\r\n        LevelEditorBaseUI.initLayers(this.floorLayersNode!, this.floorLayers, data.floorLayers);\r\n        LevelEditorBaseUI.initLayers(this.skyLayersNode!, this.skyLayers, data.skyLayers);\r\n\r\n        WavePreview.instance?.reset();\r\n\r\n        this._drawNodeGraphics();\r\n    }\r\n\r\n    private static initLayers(parentNode: Node, layers: LevelLayer[], dataLayers: LevelDataLayer[]):void {\r\n        parentNode.removeAllChildren()\r\n        dataLayers.forEach((layer, i) => {\r\n            var levelLayer = new LevelLayer();\r\n            levelLayer.speed = layer.speed;\r\n            levelLayer.type = layer.type;\r\n            levelLayer.remark = layer.remark;\r\n            levelLayer.zIndex = layer.zIndex;\r\n            levelLayer.node = LevelEditorBaseUI.addLayer(parentNode, `layer_${i}`).node;\r\n            levelLayer.node.setSiblingIndex(layer.zIndex);\r\n            const levelEditorLayerUI = levelLayer.node.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI)!;\r\n            if (layer.type === LayerType.Scroll) {\r\n                console.log(\"initScorllsByLevelData levelLayer.length \", levelLayer.scrollLayers.length);\r\n                levelEditorLayerUI.initScorllsByLevelData(levelLayer, layer); \r\n            } else if (layer.type === LayerType.Random) {\r\n                levelLayer.randomLayers = [];\r\n                layer.dynamics.forEach((dynamic) => {   \r\n                    var randomLayers = new LevelRandTerrainsLayersUI();\r\n                    dynamic.group.forEach((randTerrains) => {    \r\n                        console.log(\"initRandom ByLevelData randTerrains \", randTerrains.scale.x, randTerrains.scale.y);  \r\n                        var randomLayer = new LevelRandTerrainsLayerUI();\r\n                        randomLayer.dynamicTerrain = [];\r\n                        randomLayer.weight = randTerrains.weight;\r\n                        randTerrains.terrains.forEach((terrain) => {\r\n                            var dynamicTerrain = new LevelRandTerrainUI();\r\n                            dynamicTerrain.weight = terrain.weight;\r\n                            assetManager.loadAny({uuid: terrain.uuid}, (err: Error, prefab:Prefab) => {\r\n                                if (err) {\r\n                                    return;\r\n                                } \r\n                                dynamicTerrain.terrainElement = prefab;\r\n                                dynamicTerrain.offSetX = new LayerEditorRandomRange();\r\n                                dynamicTerrain.offSetX.min = terrain.offSetX!.min;\r\n                                dynamicTerrain.offSetX.max = terrain.offSetX!.max;\r\n                                \r\n                                dynamicTerrain.offSetY = new LayerEditorRandomRange();\r\n                                dynamicTerrain.offSetY.min = terrain.offSetY!.min;\r\n                                dynamicTerrain.offSetY.max = terrain.offSetY!.max;\r\n                                randomLayer.dynamicTerrain.push(dynamicTerrain);\r\n                            });\r\n                        });\r\n                        randomLayers.dynamicTerrains.push(randomLayer);  \r\n                    });   \r\n                    levelLayer.randomLayers.push(randomLayers);\r\n                }); \r\n            } else if (layer.type === LayerType.Emittier) {\r\n                levelEditorLayerUI.initEmittierLevelData(levelLayer, layer); \r\n            }\r\n            levelEditorLayerUI.initByLevelData(layer);\r\n            layers.push(levelLayer);\r\n        });\r\n    }\r\n\r\n    private _fillLevelLayerData(layer: LevelLayer, dataLayer: LevelDataLayer):void {\r\n        dataLayer.speed = layer.speed;\r\n        dataLayer.type = layer.type;\r\n        dataLayer.remark = layer.remark;\r\n        dataLayer.zIndex = layer.zIndex;\r\n        dataLayer.totalTime = this.totalTime;\r\n\r\n        if (layer.type === LayerType.Scroll) {   \r\n            dataLayer.scrolls = [];\r\n            layer.scrollLayers.forEach((scrollLayer) => {  \r\n                var dataScroll = new LevelDataScroll();     \r\n                scrollLayer.scrollPrefabs.forEach((scrollPrefab) => {\r\n                    dataScroll.uuids.push(scrollPrefab?.uuid!);\r\n                });\r\n                dataScroll.weight = scrollLayer.weight;\r\n                \r\n                dataScroll.splicingMode = scrollLayer.splicingMode;\r\n                dataScroll.offSetY = new LayerRandomRange();\r\n                dataScroll.offSetY.min = scrollLayer.splicingOffsetY!.min;\r\n                dataScroll.offSetY.max = scrollLayer.splicingOffsetY!.max;\r\n\r\n                dataScroll.offSetX = new LayerRandomRange();\r\n                dataScroll.offSetX.min = scrollLayer.splicingOffsetX!.min;\r\n                dataScroll.offSetX.max = scrollLayer.splicingOffsetX!.max;\r\n                dataLayer.scrolls.push(dataScroll);\r\n                console.log(\"LevelEditorBaseUI fill scrollLayersData\", dataLayer);\r\n            });\r\n        } else if (layer.type === LayerType.Random) {\r\n            dataLayer.dynamics = [];\r\n            layer.randomLayers.forEach((randomLayer) => {   \r\n                var datas = new LevelDataRandTerrainsGroup();\r\n                randomLayer.dynamicTerrains.forEach((terrains) => {\r\n                    var data = new LevelDataRandTerrains();\r\n                    data.terrains = [];\r\n                    data.weight = terrains.weight;\r\n                    terrains.dynamicTerrain.forEach((terrainElement) => {  \r\n                        var terrainData = new LevelDataRandTerrain();\r\n                        terrainData.weight = terrainElement.weight;\r\n                        terrainData.uuid = terrainElement.terrainElement?.uuid!;\r\n                        terrainData.offSetX = new LayerRandomRange(terrainElement.offSetX?.min, terrainElement.offSetX?.max);\r\n                        terrainData.offSetY = new LayerRandomRange(terrainElement.offSetY?.min, terrainElement.offSetY?.max);\r\n                        data.terrains.push(terrainData);\r\n                    });\r\n                    datas.group.push(data);\r\n                }); \r\n                dataLayer.dynamics.push(datas);\r\n            });\r\n        } else if (layer.type === LayerType.Emittier) {\r\n            \r\n        }\r\n        layer.node!.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI)!.fillLevelData(dataLayer);\r\n    }\r\n\r\n    private _fillLevelLayersData(layers: LevelLayer[], dataLayers: LevelDataLayer[]):void {\r\n        layers.sort((a, b) => a.zIndex - b.zIndex);\r\n        layers.forEach((layer) => {\r\n            var levelLayer = new LevelDataLayer();\r\n            this._fillLevelLayerData(layer, levelLayer);\r\n            dataLayers.push(levelLayer);\r\n        });\r\n    }\r\n    \r\n    public fillLevelData(data: LevelData):void {\r\n        data.name = this.levelname;\r\n        data.totalTime = this.totalTime;\r\n\r\n        data.backgroundLayer = new LevelDataBackgroundLayer();\r\n        for (let i = 0; i < this.backgroundLayer.backgrounds.length; i++) {\r\n            const prefab = this.backgroundLayer.backgrounds[i];\r\n            if (prefab == null) {\r\n                continue;\r\n            }\r\n            data.backgroundLayer.backgrounds.push(prefab.uuid);\r\n        }\r\n        this._fillLevelLayerData(this.backgroundLayer, data.backgroundLayer);\r\n\r\n        data.floorLayers = []\r\n        data.skyLayers = []\r\n        this._fillLevelLayersData(this.floorLayers, data.floorLayers);\r\n        this._fillLevelLayersData(this.skyLayers, data.skyLayers);\r\n    }\r\n\r\n    public playLevel(bPlay: boolean, progress: number) {\r\n        this._setTimeNode(progress);\r\n\r\n        this._playLayer([this.backgroundLayer], bPlay, progress);\r\n        this._playLayer(this.floorLayers, bPlay, progress);\r\n        this._playLayer(this.skyLayers, bPlay, progress);\r\n\r\n        if (this._play === bPlay) {\r\n            return;\r\n        }\r\n\r\n        this._play = bPlay; \r\n        if (bPlay) {\r\n            this._drawMask();\r\n        } else {\r\n            this._drawMaskClear();\r\n        }\r\n        this._playEmittierNode(this._play,this.floorLayersNode!);\r\n        this._playEmittierNode(this._play,this.skyLayersNode!);\r\n        this._randLayerActive(this.floorLayers, this.floorLayersNode!, this._play);\r\n        this._randLayerActive(this.skyLayers, this.skyLayersNode!, this._play);\r\n    }\r\n\r\n    private _playLayer(layers: LevelLayer[], bPlay: boolean, progress: number): void {\r\n        layers.forEach((layer) => {\r\n            layer.node!.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI)!.play(bPlay, progress * this.totalTime * layer.speed);\r\n        });\r\n    }\r\n\r\n    private _randLayerActive(layers: LevelLayer[], parentNode: Node, bPlay: boolean):void {\r\n        layers.forEach((layer, index) => {\r\n            if (layer.type === LayerType.Random) {\r\n                const layerNode = parentNode.getChildByName(`layer_${index}`);\r\n                const dynamicNode = layerNode!.getChildByName(\"dynamic\")!; \r\n            \r\n                if (bPlay) {\r\n                    if (dynamicNode.children.length <= 0 ) return; \r\n                    // 先激活所有dyna_x节点\r\n                    dynamicNode.children.forEach(dynaNode => dynaNode.active = true);\r\n                    \r\n                    // 遍历所有dyna_x节点（每个对应一个地形策略组）\r\n                    dynamicNode.children.forEach((dynaNode, groupIndex) => {\r\n                        let length = 0; \r\n                        for (let i = 0; i < layer.randomLayers[groupIndex].dynamicTerrains.length; i++) {\r\n                            length += layer.randomLayers[groupIndex].dynamicTerrains[i].dynamicTerrain.length;\r\n                        }\r\n                        if (dynaNode.children.length != length) {\r\n                            return;\r\n                        }\r\n                        \r\n                        let layerRandIndex = -1;\r\n                        let groupRandIndex = -1; \r\n                        // 策略的总权重\r\n                        let layersTotalWeight = 0;\r\n                        for (const randomTerrain of layer.randomLayers[groupIndex].dynamicTerrains) {  \r\n                            layersTotalWeight += randomTerrain.weight;\r\n                        }\r\n                        let layersRandWeight = Math.random() * layersTotalWeight;\r\n                        let layersAccWeight = 0;\r\n                        \r\n                        if (layer.randomLayers[groupIndex].dynamicTerrains.length === 1) {\r\n                            layerRandIndex = 0;\r\n                        } else {\r\n                            for (let j = 0; j < layer.randomLayers[groupIndex].dynamicTerrains.length; j++) {\r\n                                layersAccWeight += layer.randomLayers[groupIndex].dynamicTerrains[j].weight;\r\n                                if (layersRandWeight <= layersAccWeight) {\r\n                                    layerRandIndex = j;\r\n                                    console.log(\"LevelEditorBaseUI _rand random layerRandIndex\", layerRandIndex);\r\n                                    break;\r\n                                }\r\n                            }\r\n                        }\r\n\r\n                        // 组的总权重\r\n                        let groupTotalWeight = 0;\r\n                        if (layer.randomLayers[groupIndex] === null || layer.randomLayers[groupIndex].dynamicTerrains.length === 0 || layer.randomLayers[groupIndex].dynamicTerrains[layerRandIndex] === null) {\r\n                            console.error(\"策划佬地形策略组没有配东西!!!!!! 在layer:\",dynaNode.parent!.parent!.name);\r\n                            return;\r\n                        }\r\n                        layer.randomLayers[groupIndex].dynamicTerrains[layerRandIndex].dynamicTerrain.forEach(terrains => { \r\n                            groupTotalWeight += terrains.weight;\r\n                        });\r\n                        let groupRandWeight = Math.random() * groupTotalWeight;\r\n                        let groupAccWeight = 0;\r\n                        \r\n                        if (layer.randomLayers[groupIndex].dynamicTerrains[layerRandIndex].dynamicTerrain.length === 1) {\r\n                            groupRandIndex = 0;\r\n                        } else {\r\n                            console.log(\"LevelEditorBaseUI _rand random dynamicTerrains length\", layer.randomLayers[groupIndex].dynamicTerrains[layerRandIndex].dynamicTerrain.length);\r\n                            for (let j = 0; j < layer.randomLayers[groupIndex].dynamicTerrains[layerRandIndex].dynamicTerrain.length; j++) {\r\n                                groupAccWeight += layer.randomLayers[groupIndex].dynamicTerrains[layerRandIndex].dynamicTerrain[j].weight;\r\n                                if (groupRandWeight <= groupAccWeight) {\r\n                                    groupRandIndex = j;\r\n                                    console.log(\"LevelEditorBaseUI _rand random groupRandIndex\", groupRandIndex);\r\n                                    break;\r\n                                }\r\n                            }\r\n                        }\r\n\r\n                        // 显示选中的策略\r\n                        dynaNode.children.forEach((terrainNode) => {\r\n                            const match = terrainNode.name.match(/^rand_(\\d+)_(\\d+)$/);\r\n                            let active = false;\r\n                            if (match) {\r\n                                const index = parseInt(match[1]);\r\n                                const itemIndex = parseInt(match[2]);\r\n                                if (index === layerRandIndex && itemIndex === groupRandIndex) {\r\n                                    active = true;\r\n                                }\r\n                                terrainNode.active = active;\r\n                                const terrain = terrainNode.getComponent(RandTerrain)!;\r\n                                if (terrain) {\r\n                                    terrain.play(active);\r\n                                }\r\n                            } else {\r\n                                terrainNode.active = active;\r\n                            }\r\n                            \r\n                        });\r\n                    });\r\n                } else {\r\n                    dynamicNode.children.forEach(dynaNode => {\r\n                        dynaNode.active = true;\r\n                        dynaNode.children.forEach(terrainNode => {\r\n                            terrainNode.active = true;\r\n                            const terrain = terrainNode.getComponent(RandTerrain)!;\r\n                            if (terrain) {\r\n                                terrain.play(false);\r\n                            }\r\n                        });\r\n                    });\r\n                } \r\n            } else if (layer.type === LayerType.Scroll) {\r\n                parentNode.children.forEach(layerNode => {\r\n                    // 滚动层逻辑\r\n                    const scrollsNode = layerNode.getChildByName(\"scrolls\")!;\r\n                    if (scrollsNode.children.length !== layer.scrollLayers.length) {\r\n                        return;\r\n                    }\r\n                    scrollsNode.children.forEach(layerNode => layerNode.active = true);\r\n                    \r\n                    if (bPlay) {\r\n                        // 计算总权重\r\n                        let totalWeight = 0;\r\n                        for (const scrollLayer of layer.scrollLayers) {\r\n                            totalWeight += scrollLayer.weight;\r\n                        }\r\n                        \r\n                        // 随机选择要显示的滚动体\r\n                        let randomWeight = Math.random() * totalWeight;\r\n                        let selectedIndex = -1;\r\n                        for (let i = 0; i < layer.scrollLayers.length; i++) {\r\n                            randomWeight -= layer.scrollLayers[i].weight;\r\n                            if (randomWeight <= 0) {\r\n                                selectedIndex = i;\r\n                                console.log(\"LevelEditorBase selectedIndex\", selectedIndex);\r\n                                break;\r\n                            }\r\n                        }\r\n                        \r\n                        scrollsNode.children.forEach((child, index) => {\r\n                            const active = (index === selectedIndex);\r\n                            child.active = active;\r\n                            if (active) {\r\n                                child.children.forEach(children => {\r\n                                    if (children.getComponent(RandTerrain)) {\r\n                                        children.getComponent(RandTerrain)!.play(true);\r\n                                    }\r\n                                });\r\n                            }\r\n                            console.log(\"LevelEditorBase scrollsNode name\", child.name,index,\"selectedIndex\",selectedIndex,\"active\",child.active);\r\n                        });\r\n                    } else {\r\n                        scrollsNode.children.forEach(child => {\r\n                            child.active = true;\r\n                            child.children.forEach(children => {\r\n                                if (children.getComponent(RandTerrain)) {\r\n                                    children.getComponent(RandTerrain)!.play(false);\r\n                                }\r\n                            });\r\n                        });\r\n                    }\r\n                })\r\n            }\r\n        });\r\n    }\r\n\r\n    private _drawNodeGraphics(): void {\r\n        const graphics = this._drawNode!.getComponent(Graphics);\r\n        if (!graphics) return; \r\n\r\n        const drawTransform = this._drawNode!.getComponent(UITransform)!;\r\n\r\n        const drawport = new Rect(\r\n            this._drawNode!.getPosition().x - drawTransform.contentSize.width / 2,\r\n            this._drawNode!.getPosition().y - drawTransform.contentSize.height / 2,\r\n            drawTransform.contentSize.width,\r\n            this._totalHeight\r\n        );\r\n        \r\n        // Draw drawport rectangle\r\n        graphics.strokeColor = Color.BLUE;\r\n        graphics.lineWidth = 10;\r\n        graphics.rect(drawport.x, drawport.y, drawport.width, drawport.height);\r\n        graphics.stroke()\r\n\r\n        const graphicsView = this._drawNode!.getChildByName(\"drawView\")!.getComponent(Graphics);\r\n        if (!graphicsView) return;\r\n\r\n        const drawview = new Rect(\r\n            -750 / 2,\r\n            -1334 / 2,\r\n            750,\r\n            this._totalHeight\r\n        );\r\n\r\n        graphicsView.strokeColor = Color.RED;\r\n        graphicsView.lineWidth = 10;\r\n        graphicsView.rect(drawview.x, drawview.y, drawview.width, drawview.height);\r\n        graphicsView.stroke()\r\n    }\r\n\r\n    private _drawMask(): void {\r\n        if (!this._play) return;\r\n\r\n        const maskGraphics = this._drawNode!.getChildByName(\"drawMask\")!.getComponent(Graphics);\r\n        if (!maskGraphics) return;\r\n\r\n        // 绘制4个填充矩形表示视口边界\r\n        const maskWidth = 10000;\r\n        const maskHeight = 1334;\r\n        maskGraphics.fillColor = Color.BLACK;\r\n        \r\n        // 顶部矩形\r\n        maskGraphics.fillRect(\r\n            -maskWidth / 2,\r\n            this._drawNode!.getPosition().y + maskHeight - maskHeight / 2, \r\n            maskWidth, \r\n            maskHeight\r\n        );\r\n        \r\n        // 底部矩形\r\n        maskGraphics.fillRect(\r\n            -maskWidth / 2,\r\n            this._drawNode!.getPosition().y - maskHeight - maskHeight / 2,  \r\n            maskWidth, \r\n            maskHeight\r\n        );\r\n        \r\n        // 左侧矩形\r\n        maskGraphics.fillRect(\r\n            -maskWidth - 750 / 2, \r\n            this._drawNode!.getPosition().y - maskHeight / 2, \r\n            maskWidth, \r\n            maskHeight\r\n        );\r\n        \r\n        // 右侧矩形\r\n        maskGraphics.fillRect(\r\n            750 / 2, \r\n            this._drawNode!.getPosition().y - maskHeight / 2, \r\n            maskWidth, \r\n            maskHeight\r\n        );\r\n    }\r\n\r\n    private _setTimeNode (progress: number) {\r\n        if (!this._play) return;\r\n\r\n        const timeNode = this._drawNode!.getChildByName(\"time\")!;\r\n        const progressNode = this._drawNode!.getChildByName(\"progress\")!;\r\n        if (timeNode && progressNode) {\r\n            timeNode.active = true;\r\n            progressNode.active = true;\r\n            timeNode.getComponent(Label)!.string = `时间：${(this.totalTime * progress / 1000).toFixed(2)}`;\r\n            progressNode.getComponent(Label)!.string = `进度：${progress.toFixed(2)}`;\r\n        }\r\n    }\r\n\r\n    private _drawMaskClear(): void {\r\n        const timeNode = this._drawNode!.getChildByName(\"time\")!;\r\n        const progressNode = this._drawNode!.getChildByName(\"progress\")!;\r\n        if (timeNode && progressNode) {\r\n            timeNode.active = false;\r\n            progressNode.active = false;\r\n        }\r\n        const maskGraphics = this._drawNode!.getChildByName(\"drawMask\")!.getComponent(Graphics);\r\n        if (!maskGraphics) return;\r\n\r\n        maskGraphics.clear();\r\n    }\r\n}"]}