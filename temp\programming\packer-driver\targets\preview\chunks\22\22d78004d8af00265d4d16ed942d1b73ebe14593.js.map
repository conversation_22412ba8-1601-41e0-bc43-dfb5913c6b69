{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/gm/script/ui/GmButtonUI.ts"], "names": ["_decorator", "Label", "v3", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "logDebug", "BundleName", "Drag<PERSON><PERSON><PERSON>", "GmUI", "ccclass", "property", "GmButtonUI", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Top", "getBundleName", "Gm", "start", "node", "position", "getComponent", "addClick", "onClick", "openUI", "hideUI", "onShow", "extraText", "getComponentInChildren", "string", "onHide", "onClose"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,E,OAAAA,E;;AAEnBC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,I,iBAAAA,I;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;;4BAGjBY,U,WADZF,OAAO,CAAC,YAAD,C,gBAAR,MACaE,UADb;AAAA;AAAA,4BACuC;AACf,eAANC,MAAM,GAAW;AAAE,iBAAO,sBAAP;AAAgC;;AAC3C,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,GAAf;AAAoB;;AAC7B,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,EAAlB;AAAsB;;AAEpDC,QAAAA,KAAK,GAAS;AACpB,eAAKC,IAAL,CAAUC,QAAV,GAAqBlB,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,CAAV,CAAvB;AACA,eAAKmB,YAAL;AAAA;AAAA,wCAA+BC,QAA/B,CAAwC,KAAKC,OAA7C,EAAsD,IAAtD;AACH;;AAEKA,QAAAA,OAAO,GAAG;AAAA;AACZ;AAAA;AAAA,sCAAS,oBAAT,EAA+B,QAA/B;AACA,kBAAM;AAAA;AAAA,gCAAMC,MAAN;AAAA;AAAA,6BAAN;AACA,kBAAM;AAAA;AAAA,gCAAMC,MAAN,CAAab,UAAb,CAAN;AAHY;AAIf;;AAEKc,QAAAA,MAAM,CAACC,SAAD,EAAoC;AAAA;;AAAA;AAC5C,gBAAIA,SAAJ,EAAe;AACX,cAAA,KAAI,CAACC,sBAAL,CAA4B3B,KAA5B,EAAoC4B,MAApC,GAA6C,OAAO,KAAP,GAAeF,SAAf,GAA2B,GAAxE;AACH,aAFD,MAEO;AACH,cAAA,KAAI,CAACC,sBAAL,CAA4B3B,KAA5B,EAAoC4B,MAApC,GAA6C,IAA7C;AACH;AAL2C;AAM/C;;AAEKC,QAAAA,MAAM,GAAgC;AAAA;AAC3C;;AAEKC,QAAAA,OAAO,GAAgC;AAAA;AAC5C;;AA5BkC,O", "sourcesContent": ["import { _decorator, Label, v3 } from 'cc';\n\nimport { BaseUI, UILayer, UIMgr } from \"db://assets/scripts/core/base/UIMgr\";\nimport { logDebug } from '../../../../scripts/utils/Logger';\nimport { BundleName } from '../../../common/script/const/BundleConst';\nimport { DragButton } from '../../../common/script/ui/common/components/button/DragButton';\nimport { GmUI } from './GmUI';\nconst { ccclass, property } = _decorator;\n\n@ccclass('GmButtonUI')\nexport class GmButtonUI extends BaseUI {\n    public static getUrl(): string { return \"prefab/ui/GmButtonUI\"; }\n    public static getLayer(): UILayer { return UILayer.Top }\n    public static getBundleName(): string { return BundleName.Gm }\n\n    protected start(): void {\n        this.node.position = v3(315, 12, 0)\n        this.getComponent(DragButton)!.addClick(this.onClick, this)\n    }\n\n    async onClick() {\n        logDebug(\"GmButtonUI onClick\", \"aaaaaa\")\n        await UIMgr.openUI(GmUI)\n        await UIMgr.hideUI(GmButtonUI)\n    }\n\n    async onShow(extraText?: string): Promise<void> {\n        if (extraText) {\n            this.getComponentInChildren(Label)!.string = \"GM\" + \"\\n(\" + extraText + \")\"\n        } else {\n            this.getComponentInChildren(Label)!.string = \"GM\"\n        }\n    }\n\n    async onHide(...args: any[]): Promise<void> {\n    }\n\n    async onClose(...args: any[]): Promise<void> {\n    }\n}"]}