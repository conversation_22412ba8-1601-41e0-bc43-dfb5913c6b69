{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/core/base/GameConst.ts"], "names": ["_GameConst", "view", "ColliderDraw", "ActionFrameTime", "designWidth", "designHeight", "offsetWidth", "ViewHeight", "getVisibleSize", "height", "ViewWidth", "width", "ViewBattleWidth", "GameConst"], "mappings": ";;;wEAGMA,U;;;;;;;AAFGC,MAAAA,I,OAAAA,I;;;;;;;;;AAEHD,MAAAA,U,GAAN,MAAMA,UAAN,CAAiB;AAAA;AAAA,eAEJE,YAFI,GAEoB,KAFpB;AAAA,eAGJC,eAHI,GAGsB,MAHtB;AAAA,eAKbC,WALa,GAKS,GALT;AAKa;AALb,eAMbC,YANa,GAMU,IANV;AAMe;AANf,eAObC,WAPa,GAOS,GAPT;AAAA;;AAOa;AAGZ,YAAVC,UAAU,GAAG;AACb,iBAAON,IAAI,CAACO,cAAL,GAAsBC,MAA7B;AACH;;AAEY,YAATC,SAAS,GAAG;AACZ,iBAAOT,IAAI,CAACO,cAAL,GAAsBG,KAA7B;AACH;;AAEkB,YAAfC,eAAe,GAAG;AAClB,cAAID,KAAK,GAAGV,IAAI,CAACO,cAAL,GAAsBG,KAAlC;AACA,iBAAOA,KAAK,GAAG,KAAKL,WAApB;AACH;;AArBY,O;;2BAyBJO,S,GAAY,IAAIb,UAAJ,E", "sourcesContent": ["\r\nimport { view } from \"cc\";\r\n\r\nclass _GameConst {\r\n\r\n    readonly ColliderDraw: boolean = false;\r\n    readonly ActionFrameTime: number = 0.0333;\r\n\r\n    designWidth: number = 750 // 设计分辨率\r\n    designHeight: number = 1334 // 设计分辨率\r\n    offsetWidth: number = 200 // 宽屏的时候，宽度最高多显示200像素\r\n\r\n\r\n    get ViewHeight() {\r\n        return view.getVisibleSize().height\r\n    }\r\n\r\n    get ViewWidth() {\r\n        return view.getVisibleSize().width;\r\n    }\r\n\r\n    get ViewBattleWidth() {\r\n        let width = view.getVisibleSize().width;\r\n        return width + this.offsetWidth;\r\n    }\r\n\r\n}\r\n\r\nexport const GameConst = new _GameConst();"]}