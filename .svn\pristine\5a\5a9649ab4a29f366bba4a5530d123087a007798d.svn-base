// @ts-ignore
import packageJSON from '../package.json';
import * as fs from 'fs';
import * as path from 'path';

function genEnum(jsonFileName: string, enumName: string) {
    try {
        // @ts-ignore
        const projectPath = Editor.Project.path;
        const jsonPath = path.join(projectPath, 'assets', 'bundles', 'luban', jsonFileName + '.json');
        const outputPath = path.join(projectPath, 'assets', 'editor', 'enum-gen', enumName + '.ts');

        // 检查输入文件是否存在
        if (!fs.existsSync(jsonPath)) {
            console.warn('json file not found:', jsonPath);
            return;
        }

        // 读取JSON文件
        const jsonContent = fs.readFileSync(jsonPath, 'utf8');
        const bulletData = JSON.parse(jsonContent);

        if (!Array.isArray(bulletData)) {
            console.warn('json is not an array');
            return;
        }

        // 生成枚举内容
        let enumContent = `export enum ${enumName} {\n`;

        bulletData.forEach((item: any) => {
            if (item.id) {
                // remove '-'
                item.name = item.name.replaceAll('-', '');
                if (item.name && item.name.trim() !== '') {
                    enumContent += `    ${item.name} = ${item.id},\n`;
                } else {
                    enumContent += `    ${item.id} = ${item.id},\n`;
                }
            }
        });

        enumContent += '}\n';

        // 确保输出目录存在
        const outputDir = path.dirname(outputPath);
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }

        // 写入文件
        fs.writeFileSync(outputPath, enumContent, 'utf8');
        console.log(`${enumName}.ts generated successfully at:`, outputPath);

        // 刷新资源数据库
        // @ts-ignore
        Editor.Message.request('asset-db', 'refresh-asset', `db://assets/editor/enum-gen/${enumName}.ts`);
    } catch (error) {
        console.error(`Error creating ${enumName}:`, error);
    }
}

/**
 * @en Registration method for the main process of Extension
 * @zh 为扩展的主进程的注册方法
 */
export const methods: { [key: string]: (...any: any) => any } = {
    /**
     * @en A method that can be triggered by message
     * @zh 通过 message 触发的方法
     */
    movePlayerUp() {
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
                name: 'emitter-editor',
                method: 'movePlayerUp',
                args: []
            });
    },
    movePlayerDown() {
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
                name: 'emitter-editor',
                method: 'movePlayerDown',
                args: []
            });
    },
    movePlayerLeft() {
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
                name: 'emitter-editor',
                method: 'movePlayerLeft',
                args: []
            });
    },
    movePlayerRight() {
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
                name: 'emitter-editor',
                method: 'movePlayerRight',
                args: []
            });
    },

    /**
     * 监听asset-db:asset-changed消息
     */
    onAssetChanged(_uuid: string, info: any) {
        if (info && info.path) {
            if (info.path.includes('tbresemitter.json')) {
                console.log('tbresemitter.json file changed, regenerating EmitterEnum...');
                methods.createEmitterEnum();
            } else if (info.path.includes('tbresenemy.json')) {
                console.log('tbresenemy.json file changed, regenerating EnemyEnum...');
                methods.createEnemyEnum();
            }
        }
    },

    /**
     * 创建EmitterEnum枚举文件
     */
    createEmitterEnum() {
        genEnum('tbresemitter', 'EmitterEnum');
    },

    createEnemyEnum() {
        genEnum('tbresenemy', 'EnemyEnum');
    }
};

/**
 * @en Method Triggered on Extension Startup
 * @zh 扩展启动时触发的方法
 */
export function load() {
    console.log('emitter-editor extension loaded');
    console.log('Available methods:', Object.keys(methods));

    // 初始化时生成一次EmitterEnum
    methods.createEmitterEnum();
    methods.createEnemyEnum();
}

/**
 * @en Method triggered when uninstalling the extension
 * @zh 卸载扩展时触发的方法
 */
export function unload() { }
