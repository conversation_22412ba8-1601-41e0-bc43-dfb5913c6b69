/**
 * 战斗数据管理
 */

import { SingletonBase } from "db://assets/scripts/core/base/SingletonBase";
import csproto from 'db://assets/bundles/common/script/autogen/pb/cs_proto.js';
import { GameIns } from "../GameIns";

export class GameDataManager extends SingletonBase<GameDataManager> {

    gameTime:number = 0;
    isWin:boolean = false;
    totalScore:number = 0;
    starLevel:number = 0;

    reset(){
        this.gameTime = 0;
        this.totalScore = 0;
        this.starLevel = 0;
        this.isWin = false;
    }

    setGameEnd(isWin:boolean){
        this.isWin = isWin;
    }
    
    getGameResultData():csproto.comm.IGameResult|null {
        let data = new csproto.comm.GameResult();
        data.total_time_cost = this.gameTime;
        data.max_levels = GameIns.battleManager.curLevel;
        data.is_pass = this.isWin ? 1 : 0;
        data.total_score = this.totalScore;
        data.star_level = this.starLevel;
        data.gain_items = this.getAllItemData();
        data.rogues = this.getRogueItemData();
        return data;

        // fixed64  game_id = 1;           // 本局的唯一ID
        // int32  mode_id = 2;           // 入口模式ID，见模式表
        // int32  total_time_cost = 3;   // 总耗时
        // int32  max_levels = 4;        // 最大关卡数
        // int32  is_pass = 5;           // 是否过关（对于剧情或者闯关模式。如果是无尽模式 - 最后一关总是失败才结算的，则忽略）
        // int32  total_score = 6;       // 总得分
        // int32  star_level = 7;        // 星级
        // repeated  comm.GainItem gain_items = 8;    // 局内获得的道具
        // repeated  RogueItem rogues = 9;  // 本局选择的Rogue
        // repeated GameStatItem  game_stats = 10;  // 局内的一些零散的统计值，比如击杀的怪物数，击杀的怪物类型数，等等
        // int32  is_week_bset = 11;      // 是否本周最佳得分，服务器下发用
        // int32  history_bese_score = 12; // 历史最高分，服务器下发用
        // repeated  comm.GainItem  reward_items = 13; // 服务器计算的奖励列表
    }

    getAllItemData(){
        let list:csproto.comm.GainItem[] = [];
        return list;
    }
    getRogueItemData(){
        let list:csproto.comm.RogueItem[] = [];
        return list;
    }

    getGameLevelResultData():csproto.comm.IGameLevelResult[]|null{

        // int32  level_index = 1;
        // int32  level_id = 2;
        // int32  score = 3;
        // int32  star = 4;
        // int32  is_pass = 5;
        // int32  time_cost = 6;
        // repeated GameStatItem level_stats = 7;    // 关卡内的各项信息统计（取决于需要）

        // let data = new csproto.comm.GameLevelResult();
        return null;
    }
}