{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>"], "names": ["_decorator", "Layout", "UITransform", "BundleName", "EventMgr", "HomeUIEvent", "ButtonPlus", "BottomTab", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "logDebug", "BottomUIEvent", "ccclass", "property", "BottomUI", "_moduleBtns", "_curClickTab", "Home", "_originSize", "_tabUIs", "Map", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "getBundleName", "onLoad", "moduleBtns", "bottomLayer", "node", "getComponentsInChildren", "i", "length", "element", "addClick", "onClick", "push", "getComponent", "contentSize", "on", "BottomTabRegister", "onTabUIRegister", "updateLayout", "immediate", "updateClickState", "curTab", "get", "Object", "values", "for<PERSON>ach", "v", "baseUI", "Number", "hideUI", "uiClass", "event", "index", "findIndex", "target", "oldTab", "openUI", "emit", "SwitchPanel", "tab", "set", "onShow", "args", "onHide", "onClose", "closeUI", "update", "dt"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAwBC,MAAAA,M,OAAAA,M;AAAcC,MAAAA,W,OAAAA,W;;AACtCC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,a,iBAAAA,a;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBd,U;;0BAGjBe,Q,WADZF,OAAO,CAAC,UAAD,C,UAKHC,QAAQ,CAACb,MAAD,C,2BALb,MACac,QADb;AAAA;AAAA,4BACqC;AAAA;AAAA;;AAAA;;AAAA,eAOzBC,WAPyB,GAOG,EAPH;AAAA,eAQzBC,YARyB,GAQC;AAAA;AAAA,sCAAUC,IARX;AAAA,eASzBC,WATyB,GASO,IATP;AAAA,eAUzBC,OAVyB,GAUM,IAAIC,GAAJ,EAVN;AAAA;;AACb,eAANC,MAAM,GAAW;AAAE,iBAAO,oBAAP;AAA8B;;AACzC,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,OAAf;AAAwB;;AACjC,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWP,IAAlB;AAAwB;;AAStDQ,QAAAA,MAAM,GAAS;AACrB,gBAAMC,UAAU,GAAG,KAAKC,WAAL,CAAkBC,IAAlB,CAAuBC,uBAAvB;AAAA;AAAA,uCAAnB;;AACA,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGJ,UAAU,CAACK,MAA/B,EAAuCD,CAAC,EAAxC,EAA4C;AACxC,kBAAME,OAAO,GAAGN,UAAU,CAACI,CAAD,CAA1B;AACAE,YAAAA,OAAO,CAACC,QAAR,CAAiB,KAAKC,OAAtB,EAA+B,IAA/B;;AACA,iBAAKnB,WAAL,CAAiBoB,IAAjB,CAAsBH,OAAtB;AACH;;AACD,eAAKd,WAAL,GAAmB,KAAKH,WAAL,CAAiB,CAAjB,EAAqBa,IAArB,CAA0BQ,YAA1B,CAAuCnC,WAAvC,EAAqDoC,WAAxE;AACA;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,0CAAYC,iBAAxB,EAA2C,KAAKC,eAAhD,EAAiE,IAAjE;AACA,eAAKC,YAAL,CAAkB,IAAlB;AACA;AAAA;AAAA,oCAAS,iBAAT,EAA4B,OAA5B;AACH;;AAEDA,QAAAA,YAAY,CAACC,SAAS,GAAG,KAAb,EAAoB;AAC5B;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEK;;AAEDC,QAAAA,gBAAgB,GAAG;AACf,gBAAMC,MAAM,GAAG,KAAKzB,OAAL,CAAa0B,GAAb,CAAiB,KAAK7B,YAAtB,CAAf;;AACA,cAAI,CAAC4B,MAAL,EAAa;AACT;AACH;;AACDE,UAAAA,MAAM,CAACC,MAAP;AAAA;AAAA,sCAAyBC,OAAzB,CAAiCC,CAAC,IAAI;AAClC,gBAAIA,CAAC,IAAI,KAAKjC,YAAd,EAA4B;;AAC5B,kBAAMkC,MAAM,GAAG,KAAK/B,OAAL,CAAa0B,GAAb,CAAiBM,MAAM,CAACF,CAAD,CAAvB,CAAf;;AACA,gBAAI,CAACC,MAAL,EAAa;AAAE;AAAQ;;AACvB;AAAA;AAAA,gCAAME,MAAN,CAAaF,MAAM,CAACG,OAApB;AACH,WALD;AAMA,eAAKZ,YAAL,CAAkB,IAAlB;AACH;;AAEY,cAAPP,OAAO,CAACoB,KAAD,EAAoB;AAC7B,gBAAMC,KAAK,GAAG,KAAKxC,WAAL,CAAiByC,SAAjB,CAA2BP,CAAC,IAAI;AAC1C,mBAAOA,CAAC,CAACrB,IAAF,KAAW0B,KAAK,CAACG,MAAxB;AACH,WAFa,CAAd;;AAGA,gBAAMC,MAAM,GAAG,KAAK1C,YAApB;AACA,cAAIuC,KAAK,IAAI,KAAKvC,YAAlB,EAAgC;AAChC,eAAKA,YAAL,GAAoBuC,KAApB;;AACA,gBAAMX,MAAM,GAAG,KAAKzB,OAAL,CAAa0B,GAAb,CAAiB,KAAK7B,YAAtB,CAAf;;AACA,cAAI,CAAC4B,MAAL,EAAa;AAAE;AAAQ;;AACvB,gBAAM;AAAA;AAAA,8BAAMe,MAAN,CAAaf,MAAM,CAACS,OAApB,CAAN;AACA;AAAA;AAAA,oCAASO,IAAT,CAAc;AAAA;AAAA,8CAAcC,WAA5B,EAAyCH,MAAzC,EAAiD,KAAK1C,YAAtD;AACA,eAAK2B,gBAAL;AACH;;AAEDH,QAAAA,eAAe,CAACsB,GAAD,EAAiBZ,MAAjB,EAAiC;AAC5C,eAAK/B,OAAL,CAAa4C,GAAb,CAAiBD,GAAjB,EAAsBZ,MAAtB;AACH;;AAEW,cAANc,MAAM,CAAC,GAAGC,IAAJ,EAAgC,CAC3C;;AACW,cAANC,MAAM,CAAC,GAAGD,IAAJ,EAAgC;AACxC,eAAK9C,OAAL,CAAa6B,OAAb,CAAqBC,CAAC,IAAI;AACtB;AAAA;AAAA,gCAAMG,MAAN,CAAaH,CAAC,CAACI,OAAf;AACH,WAFD;AAGH;;AAEY,cAAPc,OAAO,CAAC,GAAGF,IAAJ,EAAgC;AACzC,eAAK9C,OAAL,CAAa6B,OAAb,CAAqBC,CAAC,IAAI;AACtB;AAAA;AAAA,gCAAMmB,OAAN,CAAcnB,CAAC,CAACI,OAAhB;AACH,WAFD;AAGH;;AACSgB,QAAAA,MAAM,CAACC,EAAD,EAAmB,CAClC;;AA/FgC,O;;;;;iBAKJ,I", "sourcesContent": ["import { _decorator, EventTouch, Layout, math, UITransform } from 'cc';\nimport { BundleName } from 'db://assets/bundles/common/script/const/BundleConst';\nimport { EventMgr } from 'db://assets/bundles/common/script/event/EventManager';\nimport { HomeUIEvent } from 'db://assets/bundles/common/script/event/HomeUIEvent';\nimport { ButtonPlus } from 'db://assets/bundles/common/script/ui/common/components/button/ButtonPlus';\nimport { BottomTab } from 'db://assets/bundles/common/script/ui/home/<USER>';\nimport { BaseUI, UILayer, UIMgr } from 'db://assets/scripts/core/base/UIMgr';\nimport { logDebug } from 'db://assets/scripts/utils/Logger';\nimport { BottomUIEvent } from '../../event/BottomUIEvent';\n\nconst { ccclass, property } = _decorator;\n\n@ccclass('BottomUI')\nexport class BottomUI extends BaseUI {\n    public static getUrl(): string { return \"prefab/ui/BottomUI\"; }\n    public static getLayer(): UILayer { return UILayer.Default }\n    public static getBundleName(): string { return BundleName.Home }\n    @property(Layout)\n    bottomLayer: Layout | null = null;\n\n    private _moduleBtns: ButtonPlus[] = [];\n    private _curClickTab: BottomTab = BottomTab.Home;\n    private _originSize: math.Size | null = null;\n    private _tabUIs: Map<number, BaseUI> = new Map();\n\n    protected onLoad(): void {\n        const moduleBtns = this.bottomLayer!.node.getComponentsInChildren(ButtonPlus);\n        for (let i = 0; i < moduleBtns.length; i++) {\n            const element = moduleBtns[i];\n            element.addClick(this.onClick, this)\n            this._moduleBtns.push(element);\n        }\n        this._originSize = this._moduleBtns[0]!.node.getComponent(UITransform)!.contentSize\n        EventMgr.on(HomeUIEvent.BottomTabRegister, this.onTabUIRegister, this)\n        this.updateLayout(true)\n        logDebug(\"BottomUI onLoad\", \"aaaaa\")\n    }\n\n    updateLayout(immediate = false) {\n        /*暂时不做表现，等策划定\n        this._moduleBtns.forEach((btn, i) => {\n            const isSelected = i === this._curClickTab;\n            const targetScale = isSelected ? 1.2 : 0.8;\n            if (immediate) {\n                btn.node.setScale(v3(targetScale, targetScale, 1));\n            } else {\n                tween(btn.node)\n                    .to(.3,\n                        { scale: v3(targetScale, targetScale, 1) },\n                        { easing: 'sineOut' }\n                    )\n                    .start();\n            }\n        });\n\n        // 延迟更新Layout以确保动画完成后重新计算位置\n        this.scheduleOnce(() => {\n            this.bottomLayer?.updateLayout();\n        }, immediate ? 0 : .3);\n        */\n    }\n\n    updateClickState() {\n        const curTab = this._tabUIs.get(this._curClickTab);\n        if (!curTab) {\n            return\n        }\n        Object.values(BottomTab).forEach(v => {\n            if (v == this._curClickTab) return;\n            const baseUI = this._tabUIs.get(Number(v))\n            if (!baseUI) { return }\n            UIMgr.hideUI(baseUI.uiClass!)\n        })\n        this.updateLayout(true)\n    }\n\n    async onClick(event: EventTouch) {\n        const index = this._moduleBtns.findIndex(v => {\n            return v.node === event.target;\n        })\n        const oldTab = this._curClickTab;\n        if (index == this._curClickTab) return;\n        this._curClickTab = index\n        const curTab = this._tabUIs.get(this._curClickTab);\n        if (!curTab) { return }\n        await UIMgr.openUI(curTab.uiClass!)\n        EventMgr.emit(BottomUIEvent.SwitchPanel, oldTab, this._curClickTab)\n        this.updateClickState();\n    }\n\n    onTabUIRegister(tab: BottomTab, baseUI: BaseUI) {\n        this._tabUIs.set(tab, baseUI)\n    }\n\n    async onShow(...args: any[]): Promise<void> {\n    }\n    async onHide(...args: any[]): Promise<void> {\n        this._tabUIs.forEach(v => {\n            UIMgr.hideUI(v.uiClass!)\n        })\n    }\n\n    async onClose(...args: any[]): Promise<void> {\n        this._tabUIs.forEach(v => {\n            UIMgr.closeUI(v.uiClass!)\n        })\n    }\n    protected update(dt: number): void {\n    }\n\n}\n\n"]}