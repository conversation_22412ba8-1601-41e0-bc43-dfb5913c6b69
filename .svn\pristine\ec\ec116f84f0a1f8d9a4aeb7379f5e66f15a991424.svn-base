import { _decorator, Label, math, Node, Sprite, tween, Tween } from 'cc';
const { ccclass, property } = _decorator;

import { DamageType, EffectParam, EffectType } from 'db://assets/bundles/common/script/autogen/luban/schema';
import { AttributeConst } from 'db://assets/bundles/common/script/const/AttributeConst';
import { AttributeData } from 'db://assets/bundles/common/script/data/base/AttributeData';
import { GameEnum } from 'db://assets/bundles/common/script/game/const/GameEnum';

import Entity from 'db://assets/bundles/common/script/game/ui/base/Entity';

import FBoxCollider from 'db://assets/bundles/common/script/game/collider-system/FBoxCollider';
import FCircleCollider from 'db://assets/bundles/common/script/game/collider-system/FCircleCollider';
import FPolygonCollider from 'db://assets/bundles/common/script/game/collider-system/FPolygonCollider';

import {BuffComp} from './skill/BuffComp';
import SkillComp from './skill/SkillComp';
import forEachEntityByTargetType from './skill/SearchTarget';
import { GameIns } from 'db://assets/bundles/common/script/game/GameIns';
import { PlaneEventType } from './event/PlaneEventType';
import PlaneEventComp from './event/PlaneEventComp';
import { Buff } from './skill/Buff';

@ccclass('PlaneBase')
export default class PlaneBase extends Entity {
    constructor() {
        super();
    }
    @property(Node)
    hpNode: Node | null = null;
    @property(Sprite)
    hpBar: Sprite | null = null; // 血条
    @property(Sprite)
    hpAniSprite: Sprite | null = null; // 血条动画条
    @property(Label)
    hpfont: Label | null = null; // 血条文本

    enemy = true; // 是否为敌机
    type = 0; // 敌人类型
    bDamageable: boolean = true; // 是否可以被造成伤害

    get maxHp(): number{
        return  this.attribute.getMaxHP();
    };
    curHp: number = 0;
    hurtTime:number = 0;

    collideComp: FCircleCollider | FBoxCollider | FPolygonCollider | null = null; // 碰撞组件

    private _skillComp: SkillComp | null = null;
    private _buffComp: BuffComp | null = null;
    private _eventComp: PlaneEventComp | null = null;

    // TODO 临时做法，后续应该挪到 PlaneBase
    private _attributeData: AttributeData = new AttributeData();

    init() {
        this._skillComp = new SkillComp();
        this.addComp("skill", this._skillComp);
        this._buffComp = new BuffComp();
        this.addComp("buff", this._buffComp)
        this._eventComp = new PlaneEventComp();
        this.addComp("event", this._eventComp)
        super.init();
    }

    get skillComp() {
        return this._skillComp!;
    }

    get buffComp() {
        return this._buffComp!;
    }

    get attribute(): AttributeData {
        return this._attributeData;
    }

    set colliderEnabled(value: boolean) {
        if (this.collideComp) {
            this.collideComp.isEnable = value;
        }
    }
    get colliderEnabled(): boolean {
        return this.collideComp ? this.collideComp.isEnable : false;
    }

    CastSkill(skillID: number) {
        this.skillComp.Cast(this, skillID);
    }

    addHp(heal: number) {
        this.curHp = Math.min(
            this.maxHp,
            this.curHp + heal
        );
        this.updateHpUI();
    }

    hurt(damage: number) {
        if (this.isDead) {
            return;
        }
        this.hurtTime = GameIns.battleManager._gameTime;
        this.cutHp(damage);
        if (this.curHp <= 0) {
            this.PlaneEventTrigger(PlaneEventType.FatalInjuryHurt)
        }
        this.playHurtAnim();
        if (this.curHp <= 0) {
            this.toDie();
        }
    }

    get collisionLevel() {
        return 0;
    }
    get collisionHurt() {
        return 0;
    }

    // 撞机
    collisionPlane(plane: PlaneBase) {
        if (this.isDead || plane.isDead) {
            return;
        }
        if (this.collisionLevel > plane.collisionLevel) {
            return
        }
        let hurt:number
        if (this.collisionLevel < plane.collisionLevel) {
            hurt = Math.max(this.maxHp, plane.collisionHurt)
        } else {
            hurt = plane.collisionHurt == -1 ? this.maxHp : plane.collisionHurt
        }

        hurt = (hurt - this.attribute.getFinialAttributeByOutInKey(
                AttributeConst.CollisionHurtResistanceOutAdd, AttributeConst.CollisionHurtResistanceOutPer, 
                AttributeConst.CollisionHurtResistanceInAdd, AttributeConst.CollisionHurtResistanceInPer))
            * (1 - this.attribute.getFinalAttributeByKey(AttributeConst.CollisionHurtDerateOut))
            * (1 - this.attribute.getFinalAttributeByKey(AttributeConst.CollisionHurtDerateIn));
        this.hurt(hurt);
    }

    /**
     * 减少血量
     * @param {number} damage 受到的伤害值
     */
    cutHp(damage: number) {
        const newHp = this.curHp - damage;
        this.curHp = Math.max(0, newHp);

        this.updateHpUI();
    }

    toDie(destroyType: GameEnum.EnemyDestroyType = GameEnum.EnemyDestroyType.Die): boolean {
        if (this.isDead) {
            return false
        }

        this.PlaneEventTrigger(PlaneEventType.Die, this);
        this.isDead = true;
        this.colliderEnabled = false;
        return true
    }
    /**
     * 更新血量显示
     */
    updateHpUI() {
        if (this.hpBar) {
            // 更新血条前景的填充范围
            this.hpBar.fillRange = this.curHp / this.maxHp;

            if (this.hpAniSprite) {
                // 计算血条动画时间
                const duration = Math.abs(this.hpAniSprite.fillRange - this.hpBar.fillRange);

                Tween.stopAllByTarget(this.hpAniSprite);
                // 血条中间部分的动画
                tween(this.hpAniSprite)
                    .to(duration, { fillRange: this.hpBar.fillRange })
                    .call(() => {

                    })
                    .start();
            }
        }

        // 更新血量文字
        this.hpfont && (this.hpfont!.string = this.curHp.toFixed(0));
    }

    playHurtAnim() {
        // 子类实现
    }

    ApplyBuffEffect(buff: Buff | null, effectData: EffectParam) {
        switch (effectData.type) {
            case EffectType.AttrMaxHPPer:
                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.MaxHPOutPer, AttributeConst.MaxHPInPer, effectData);
                break;
            case EffectType.AttrMaxHPAdd:
                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.MaxHPOutAdd, AttributeConst.MaxHPInAdd, effectData);
                break;
            case EffectType.AttrHPRecoveryPer:
                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.HPRecoveryOutPer, AttributeConst.HPRecoveryInPer, effectData);
                break;
            case EffectType.AttrHPRecoveryAdd:
                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.HPRecoveryOutAdd, AttributeConst.HPRecoveryInAdd, effectData);
                break;
            case EffectType.AttrHPRecoveryMaxHPPerAdd:
                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.MaxHPRecoveryRateOut, AttributeConst.MaxHPRecoveryRateIn, effectData);
                break;
            case EffectType.HealMaxHPPer:
                if (effectData.param.length >= 1) {
                    this.addHp(Math.floor(this.maxHp * effectData.param[0]/10000));
                }
                break;
            case EffectType.HealLoseHPPer:
                if (effectData.param.length >= 1) {
                    this.addHp(Math.floor((this.maxHp - this.curHp) * effectData.param[0]/10000));
                }
                break;
            case EffectType.HealHP:
                if (effectData.param.length >= 1) {
                    this.addHp(effectData.param[0]);
                }
                break;
            case EffectType.AttrAttackPer:
                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.AttackOutPer, AttributeConst.AttackInPer, effectData);
                break;
            case EffectType.AttrAttackAdd:
                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.AttackOutAdd, AttributeConst.AttackInAdd, effectData);
                break;
            case EffectType.AttrAttackBossPer:
                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.BossHurtBonusOut, AttributeConst.BossHurtBonusIn, effectData);
                break;
            case EffectType.AttrAttackNormalPer:
                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.NormalHurtBonusOut, AttributeConst.NormalHurtBonusIn, effectData);
                break;
            case EffectType.AttrFortunatePer:
                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.FortunateOutPer, AttributeConst.FortunateInPer, effectData);
                break;
            case EffectType.AttrFortunateAdd:
                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.FortunateOutAdd, AttributeConst.FortunateInAdd, effectData);
                break;
            case EffectType.AttrMissAdd:
                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.MissRateOut, AttributeConst.MissRateIn, effectData);
                break;
            case EffectType.AttrBulletHurtResistancePer:
                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.BulletHurtResistanceOutPer, AttributeConst.BulletHurtResistanceInPer, effectData);
                break;
            case EffectType.AttrBulletHurtResistanceAdd:
                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.BulletHurtResistanceOutAdd, AttributeConst.BulletHurtResistanceInAdd, effectData);
                break;
            case EffectType.AttrBulletHurtDerateAdd:
                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.BulletHurtDerateOut, AttributeConst.BulletHurtDerateIn, effectData);
                break;
            case EffectType.AttrCollisionHurtResistancePer:
                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.CollisionHurtResistanceOutPer, AttributeConst.CollisionHurtResistanceInPer, effectData);
                break;
            case EffectType.AttrCollisionHurtResistanceAdd:
                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.CollisionHurtResistanceOutAdd, AttributeConst.CollisionHurtResistanceInAdd, effectData);
                break;
            case EffectType.AttrCollisionHurtDerateAdd:
                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.CollisionHurtDerateOut, AttributeConst.CollisionHurtDerateIn, effectData);
                break;
            case EffectType.AttrFinalScoreAdd:
                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.FinalScoreRateOut, AttributeConst.FinalScoreRateIn, effectData);
                break;
            case EffectType.AttrKillScoreAdd:
                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.KillScoreRateOut, AttributeConst.KillScoreRateIn, effectData);
                break;
            case EffectType.AttrEnergyRecoveryPerAdd:
                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.EnergyRecoveryOutPer, AttributeConst.EnergyRecoveryInPer, effectData);
                break;
            case EffectType.AttrEnergyRecoveryAdd:
                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.EnergyRecoveryOutAdd, AttributeConst.EnergyRecoveryInAdd, effectData);
                break;
            case EffectType.AttrPickRadiusPer:
                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.PickRadiusOutPer, AttributeConst.PickRadiusInPer, effectData);
                break;
            case EffectType.AttrPickRadiusAdd:
                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.PickRadiusOutAdd, AttributeConst.PickRadiusInAdd, effectData);
                break;
            case EffectType.ApplyBuff:
                if (effectData.param.length < 2) {
                    return;
                }
                const buffID = effectData.param[0];
                const target = effectData.param[1];
                forEachEntityByTargetType(this, target, (entity) => {
                    entity.buffComp.ApplyBuff(buff?.isOutside||false, buffID);
                })
                break;
            case EffectType.ImmuneBulletHurt:
                if (buff) {
                    this.attribute.addModify(buff.id, AttributeConst.StatusImmuneBulletHurt, 1);
                }
                break;
            case EffectType.ImmuneCollisionHurt:
                if (buff) {
                    this.attribute.addModify(buff.id, AttributeConst.StatusImmuneCollisionHurt, 1);
                }
                break;
            case EffectType.IgnoreBullet:
                if (buff) {
                    this.attribute.addModify(buff.id, AttributeConst.StatusIgnoreBullet, 1);
                }
                break;
            case EffectType.IgnoreCollision:
                if (buff) {
                    this.attribute.addModify(buff.id, AttributeConst.StatusIgnoreCollision, 1);
                }
                break;
            case EffectType.ImmuneNuclearHurt:
                if (buff) {
                    this.attribute.addModify(buff.id, AttributeConst.StatusImmuneNuclearHurt, 1);
                }
                break;
            case EffectType.ImmuneActiveSkillHurt:
                if (buff) {
                    this.attribute.addModify(buff.id, AttributeConst.StatusImmuneActiveSkillHurt, 1);
                }
                break;
            case EffectType.Invincible:
                if (buff) {
                    this.attribute.addModify(buff.id, AttributeConst.StatusInvincible, 1);
                }
                break;
            case EffectType.AttrNuclearMax:
                this.ApplyBuffAttributeEffect(buff, AttributeConst.NuclearMax, effectData);
                break;
            case EffectType.AttrBulletAttackAdd:
                let damageType = DamageType.ALL;
                if (effectData.param.length > 1) {
                    damageType = effectData.param[1];
                }
                switch(damageType) {
                    case DamageType.ALL:
                        this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.BulletAttackOutAdd, AttributeConst.BulletAttackInAdd, effectData);
                        break;
                    case DamageType.EXPLOSIVE:
                        this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.ExplosiveBulletAttackOutAdd, AttributeConst.ExplosiveBulletAttackInAdd, effectData);
                        break;
                    case DamageType.NORMAL:
                        this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.NormalBulletAttackOutAdd, AttributeConst.NormalBulletAttackInAdd, effectData);
                        break;
                    case DamageType.ENERGETIC:
                        this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.EnergeticBulletAttackOutAdd, AttributeConst.EnergeticBulletAttackInAdd, effectData);
                        break;
                    case DamageType.PHYSICAL:
                        this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.PhysicsBulletAttackOutAdd, AttributeConst.PhysicsBulletAttackInAdd, effectData);
                        break;
                }
            case EffectType.AttrBulletAttackPer:
                damageType = DamageType.ALL;
                if (effectData.param.length > 1) {
                    damageType = effectData.param[1];
                }
                switch(damageType) {
                    case DamageType.ALL:
                        this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.BulletAttackOutPer, AttributeConst.BulletAttackInPer, effectData);
                        break;
                    case DamageType.EXPLOSIVE:
                        this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.ExplosiveBulletAttackOutPer, AttributeConst.ExplosiveBulletAttackInPer, effectData);
                        break;
                    case DamageType.NORMAL:
                        this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.NormalBulletAttackOutPer, AttributeConst.NormalBulletAttackInPer, effectData);
                        break;
                    case DamageType.ENERGETIC:
                        this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.EnergeticBulletAttackOutPer, AttributeConst.EnergeticBulletAttackInPer, effectData);
                        break;
                    case DamageType.PHYSICAL:
                        this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.PhysicsBulletAttackOutPer, AttributeConst.PhysicsBulletAttackInPer, effectData);
                        break;
                }
                break;
            case EffectType.AttrBulletHurtFix:
                damageType = DamageType.ALL;
                if (effectData.param.length > 1) {
                    damageType = effectData.param[1];
                }
                switch(damageType) {
                    case DamageType.ALL:
                        this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.BulletHurtFixOut, AttributeConst.BulletHurtFixIn, effectData);
                        break;
                    case DamageType.EXPLOSIVE:
                        this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.ExplosiveBulletHurtFixOut, AttributeConst.ExplosiveBulletHurtFixIn, effectData);
                        break;
                    case DamageType.NORMAL:
                        this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.NormalBulletHurtFixOut, AttributeConst.NormalBulletHurtFixIn, effectData);
                        break;
                    case DamageType.ENERGETIC:
                        this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.EnergeticBulletHurtFixOut, AttributeConst.EnergeticBulletHurtFixIn, effectData);
                        break;
                    case DamageType.PHYSICAL:
                        this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.PhysicsBulletHurtFixOut, AttributeConst.PhysicsBulletHurtFixIn, effectData);
                        break;
                }
                break;
            case EffectType.HurtMaxHPPer:
                if (effectData.param.length < 1) {
                    return;
                }
                this.hurt(this.maxHp * effectData.param[0]);
                break;
            case EffectType.HurtCurHPPer:
                if (effectData.param.length < 1) {
                    return;
                }
                this.hurt(this.curHp * effectData.param[0]);
                break;
            case EffectType.AttrNuclearAttackPer:
                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.NuclearAttackOutPer, AttributeConst.NuclearAttackInPer, effectData);
                break;
            case EffectType.AttrNuclearAttackAdd:
                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.NuclearAttackOutAdd, AttributeConst.NuclearAttackInAdd, effectData);
                break;
            case EffectType.FireBullet:
                // TODO not implement
                break;
            case EffectType.AttrNuclearHurtFix:
                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.NuclearHurtFixOut, AttributeConst.NuclearHurtFixIn, effectData);
                break;
            case EffectType.AddNuclear:
                if (effectData.param.length > 0) {
                    this.addNuclear(effectData.param[0])
                }
                break;
            default:
                break;
        }
    }

    private ApplyBuffAttributeOutInEffect(buff: Buff | null, inKey: AttributeConst, outKey:AttributeConst, effectData: EffectParam) {
        if (!buff) {
            return;
        }
        if (buff.isOutside) {
            this.ApplyBuffAttributeEffect(buff, outKey, effectData);
        } else {
            this.ApplyBuffAttributeEffect(buff, inKey, effectData);
        }
    }
    private ApplyBuffAttributeEffect(buff: Buff | null, key: number, effectData: EffectParam) {
        if (!buff) {
            return;
        }
        if (effectData.param.length < 1) {
            return;
        }
        this.attribute.addModify(buff.id, key, effectData.param[0]);
    }
    RemoveBuffEffect(buff: Buff, effectData: EffectParam) {
        this.attribute.removeModify(buff.id);
    }

    PlaneEventRegister(et:PlaneEventType, cb: Function) {
        this._eventComp?.Register(et, cb);
    }
    PlaneEventUnRegister(et:PlaneEventType, cb: Function) {
        this._eventComp?.UnRegister(et, cb);
    }
    PlaneEventTrigger(et:PlaneEventType, ...params: any[]) {
        this._eventComp?.Trigger(et, ...params);
    }

    setAnimSpeed(speed: number) {
        // 子类实现
    }

    // 获取当前的攻击目标
    // 对于敌机，返回玩家飞机；
    // 对于玩家飞机，可以按策划规则（距离或者其他规则）
    public getTarget(): PlaneBase|null {
        // 子类实现
        return null;
    }

    // 增加核弹
    addNuclear(num: number) {
        // 子类实现
    }

    // 获取已拾取宝石数量
    get pickDiamondNum():number {return 0}
    // 获取已击杀敌机数量
    get killEnemyNum():number {return 0}
    // 获取已使用核弹数量
    get usedNuclearNum():number {return 0}
    // 获取已使用大招数量
    get usedSuperNum():number {return 0}
    // 获取当前核弹数量
    get nuclearNum():number {return 0}
}