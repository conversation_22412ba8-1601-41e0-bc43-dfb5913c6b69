import { _decorator, Component, Enum, misc, Node, UITransform, Vec2, Vec3 } from 'cc';
import { IMovable, MoveBase } from './IMovable';
import Entity from '../ui/base/Entity';
const { degreesToRadians, radiansToDegrees } = misc;
const { ccclass, property, executeInEditMode } = _decorator;

/**
 * 追踪选项配置类
 */
export class TrackingOption {
    public duration: number = 0;                  // 追踪持续时间（毫秒，0表示无限制）
    public maxDistance: number = 0;               // 最大追踪距离（0表示无限制）
    public maxTurnAngle: number = 180;            // 最大转向角度（度，180表示可以完全掉头）
    public minEfficiency: number = 0.1;           // 最小追踪效率（当效率低于此值时停止追踪）
    public efficiencyCheckInterval: number = 500; // 效率检查间隔（毫秒）

    // 内部状态
    public elapsedTime: number = 0;               // 已追踪时间
    public lastDistanceToTarget: number = 0;      // 上一帧到目标的距离
    public lastEfficiencyCheckTime: number = 0;   // 上次效率检查时间
    public isActive: boolean = true;              // 追踪是否激活

    constructor(options?: Partial<TrackingOption>) {
        if (options) {
            Object.assign(this, options);
        }
    }

    /**
     * 重置追踪状态
     */
    public reset(): void {
        this.elapsedTime = 0;
        this.lastDistanceToTarget = 0;
        this.lastEfficiencyCheckTime = 0;
        this.isActive = true;
    }

    /**
     * 检查是否应该停止追踪
     */
    public shouldStopTracking(currentDistance: number, dt: number): boolean {
        this.elapsedTime += dt * 1000; // dt是秒，转换为毫秒

        // 时间限制检查
        if (this.duration > 0 && this.elapsedTime >= this.duration) {
            return true;
        }

        // 距离限制检查
        if (this.maxDistance > 0 && currentDistance > this.maxDistance) {
            return true;
        }

        // 效率检查
        if (this.elapsedTime - this.lastEfficiencyCheckTime >= this.efficiencyCheckInterval) {
            if (this.lastDistanceToTarget > 0) {
                const efficiency = (this.lastDistanceToTarget - currentDistance) / this.lastDistanceToTarget;
                if (efficiency < this.minEfficiency) {
                    return true;
                }
            }
            this.lastDistanceToTarget = currentDistance;
            this.lastEfficiencyCheckTime = this.elapsedTime;
        }

        return false;
    }
}

@ccclass('DefaultMove')
@executeInEditMode
export class DefaultMove extends MoveBase {

    public isFacingMoveDir: boolean = false;      // 是否朝向行进方向
    public isTrackingTarget: boolean = false;     // 是否正在追踪目标
    public speed: number = 1;                     // 速度
    public speedAngle: number = 0;                // 速度方向 (用角度表示)
    public turnSpeed: number = 60;                // 转向速度（仅用在追踪目标时）
    public acceleration: number = 0;              // 加速度
    public accelerationAngle: number = 0;         // 加速度方向 (用角度表示)

    // 追踪配置
    public trackingOption: TrackingOption = new TrackingOption();

    // @property({displayName: '振荡偏移速度', tooltip: '控制倾斜振荡的频率'})
    public tiltSpeed: number = 0;                 // 偏移速度
    // @property({displayName: '振荡偏移幅度', tooltip: '控制倾斜振荡的幅度'})
    public tiltOffset: number = 100;               // 偏移距离

    public target: Node | null = null;            // 追踪的目标节点
    public arrivalDistance: number = 10;          // 到达目标的距离

    private _tiltTime: number = 0;                // 用于计算倾斜偏移的累积时间
    private _basePosition: Vec3 = new Vec3();     // 基础位置（不包含倾斜偏移）

    /**
     * 更新追踪逻辑
     * @param dt 时间增量（秒）
     * @returns 追踪结果，包含新的速度向量，如果停止追踪则返回null
     */
    private updateTracking(dt: number): { velocityX: number; velocityY: number } | null {
        if (!this.target) return null;

        const targetPos = this.target.getPosition();
        const currentPos = this._basePosition;

        // Calculate direction to target
        const directionX = targetPos.x - currentPos.x;
        const directionY = targetPos.y - currentPos.y;
        const distance = Math.sqrt(directionX * directionX + directionY * directionY);

        // 检查是否应该停止追踪
        if (this.trackingOption.shouldStopTracking(distance, dt)) {
            this.trackingOption.isActive = false;
            return null;
        }

        if (distance > 0) {
            // Calculate desired angle to target
            const desiredAngle = radiansToDegrees(Math.atan2(directionY, directionX));

            // Smoothly adjust speedAngle toward target
            const angleDiff = desiredAngle - this.speedAngle;
            // Normalize angle difference to [-180, 180] range
            const normalizedAngleDiff = ((angleDiff + 180) % 360) - 180;

            // 检查转向角度限制
            if (Math.abs(normalizedAngleDiff) > this.trackingOption.maxTurnAngle) {
                this.trackingOption.isActive = false;
                return null;
            }

            // Apply tracking adjustment
            const trackingStrength = 1.0; // Can be made configurable
            const maxTurnRate = this.turnSpeed; // degrees per second
            const turnAmount = Math.min(Math.abs(normalizedAngleDiff), maxTurnRate * dt) * Math.sign(normalizedAngleDiff);

            this.speedAngle += turnAmount * trackingStrength;
            const angleRadiansAfterTracking = degreesToRadians(this.speedAngle);

            // Calculate new velocity with updated angle
            return {
                velocityX: this.speed * Math.cos(angleRadiansAfterTracking),
                velocityY: this.speed * Math.sin(angleRadiansAfterTracking)
            };
        }

        return null;
    }

    public tick(dt: number): void {
        if (!this._isMovable) return;

        const angleRadians = degreesToRadians(this.speedAngle);
        // Convert speed and angle to velocity vector
        let velocityX = this.speed * Math.cos(angleRadians);
        let velocityY = this.speed * Math.sin(angleRadians);

        // 处理追踪逻辑
        if (this.isTrackingTarget && this.target && this.trackingOption.isActive) {
            const trackingResult = this.updateTracking(dt);
            if (trackingResult) {
                velocityX = trackingResult.velocityX;
                velocityY = trackingResult.velocityY;
            }
        }

        // Convert acceleration and angle to acceleration vector
        if (this.acceleration !== 0) {
            const accelerationRadians = degreesToRadians(this.accelerationAngle);
            const accelerationX = this.acceleration * Math.cos(accelerationRadians);
            const accelerationY = this.acceleration * Math.sin(accelerationRadians);
            // Update velocity vector: v = v + a * dt
            velocityX += accelerationX * dt;
            velocityY += accelerationY * dt;
        }

        // Convert back to speed and angle
        this.speed = Math.sqrt(velocityX * velocityX + velocityY * velocityY);
        this.speedAngle = radiansToDegrees(Math.atan2(velocityY, velocityX));

        // Update position: p = p + v * dt
        if (velocityX !== 0 || velocityY !== 0) {
            // Update base position (main movement path)
            this._basePosition.x += velocityX * dt;
            this._basePosition.y += velocityY * dt;

            // Start with base position
            this._position.set(this._basePosition);

            // Apply tilting behavior if enabled
            if (this.tiltSpeed > 0 && this.tiltOffset > 0) {
                // Update tilt time
                this._tiltTime += dt;

                // Calculate perpendicular direction to movement
                // If moving in direction (cos(angle), sin(angle)), perpendicular is (-sin(angle), cos(angle))
                const moveAngleRad = degreesToRadians(this.speedAngle);
                const perpX = -Math.sin(moveAngleRad);
                const perpY = Math.cos(moveAngleRad);

                // Calculate tilt offset using sine wave
                const tiltAmount = Math.sin(this._tiltTime * this.tiltSpeed) * this.tiltOffset;

                // Apply tilt offset in perpendicular direction (as position offset, not velocity)
                this._position.x += perpX * tiltAmount;
                this._position.y += perpY * tiltAmount;
            }

            this.node.setPosition(this._position);
            this.checkVisibility();
        }

        if (this.isFacingMoveDir && this.speed > 0) {
            const finalAngle = this.speedAngle + this.defaultFacing;
            this.node.setRotationFromEuler(0, 0, finalAngle);
        }
    }

    /**
     * Set the target
     */
    public setTarget(target: Entity | null): DefaultMove {
        this.target = target ? target.node : null;
        return this;
    }

    /**
     * Set whether to track the target
     */
    public setTracking(tracking: boolean): DefaultMove {
        this.isTrackingTarget = tracking;
        if (tracking) {
            this.trackingOption.reset(); // 重置追踪状态
        }
        return this;
    }

    /**
     * 配置追踪选项
     * @param options 追踪选项
     */
    public setTrackingOption(options: Partial<TrackingOption>): DefaultMove {
        Object.assign(this.trackingOption, options);
        return this;
    }

    /**
     * 设置追踪持续时间
     * @param duration 持续时间（毫秒）
     */
    public setTrackingDuration(duration: number): DefaultMove {
        this.trackingOption.duration = duration;
        return this;
    }

    /**
     * 设置最大追踪距离
     * @param distance 最大距离
     */
    public setTrackingMaxDistance(distance: number): DefaultMove {
        this.trackingOption.maxDistance = distance;
        return this;
    }

    /**
     * 设置最大转向角度
     * @param angle 最大角度（度）
     */
    public setTrackingMaxTurnAngle(angle: number): DefaultMove {
        this.trackingOption.maxTurnAngle = angle;
        return this;
    }

    public setMovable(movable: boolean): DefaultMove {
        this._isMovable = movable;

        if (this._isMovable) {
            // Initialize base position to current node position
            this.node.getPosition(this._basePosition);
        }

        return this;
    }
}