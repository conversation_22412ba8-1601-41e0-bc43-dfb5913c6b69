import { _decorator, Component, misc, Enum, Node, UITransform, Vec2, Vec3, JsonAsset } from 'cc';
import { BulletSystem } from '../bullet/BulletSystem';
import { MoveBase, eMoveEvent } from './IMovable';
import Entity from '../ui/base/Entity';
import { PathData, PathPoint } from '../data/PathData';

const { degreesToRadians, radiansToDegrees } = misc;
const { ccclass, property, executeInEditMode } = _decorator;

@ccclass('PathMove')
@executeInEditMode()
export class PathMove extends MoveBase {
    public _pathAsset: JsonAsset | null = null;
    @property({ type: JsonAsset, displayName: "路径数据(预览用)" })
    public get pathAsset(): JsonAsset | null {
        return this._pathAsset;
    }
    public set pathAsset(value: JsonAsset) {
        this._pathAsset = value;
        if (value) {
            this.setPath(PathData.fromJSON(value.json));
        }
    }

    @property({ displayName: "循环移动" })
    public loop: boolean = false;

    @property({ displayName: "反向移动" })
    public reverse: boolean = false;

    @property({ displayName: "振荡偏移速度", tooltip: "控制倾斜振荡的频率" })
    public tiltSpeed: number = 0;

    @property({ displayName: "振荡偏移幅度", tooltip: "控制倾斜振荡的幅度" })
    public tiltOffset: number = 0;

    // 路径相关数据
    private _pathData: PathData | null = null;
    private _subdivided: PathPoint[] = []; // 细分后的路径点（包含完整信息）

    // 移动状态
    private _currentPosition: Vec3 = new Vec3();
    private _currentPointIndex: number = 0; // 当前所在的细分点索引
    private _nextPointIndex: number = 0;
    private _remainDistance: number = 0;    // 距离下一个点的剩余距离

    private _tiltTime: number = 0;

    // 停留状态
    private _stayTimer: number = 0; // 停留计时器（秒）
    private _stayPointIndex: number = -1; // 停留的点索引

    private _updateInEditor: boolean = false;
    public onFocusInEditor() {
        this._updateInEditor = true;
        this._isMovable = true;
    }

    public onLostFocusInEditor() {
        this._updateInEditor = false;
        this._isMovable = false;
        this.resetToStart();
    }

    public update(dt: number) {
        if (this._updateInEditor) {
            this.tick(dt);
        }
    }

    /**
     * 加载路径数据（使用新的细分点方法）
     */
    public setPath(pathData: PathData) {
        this._pathData = pathData;
        // 使用新的细分点方法，直接获取包含完整信息的PathPoint数组
        this._subdivided = this._pathData.getSubdividedPoints();
        this.resetToStart();
    }

    /**
     * 主要的移动更新逻辑
     */
    public tick(dt: number): void {
        if (!this._isMovable) return;
        
        if (this._nextPointIndex !== this._currentPointIndex) {
            this.tickMovement(dt);
        }

        this.tickTilting(dt);
    }

    private tickMovement(dt: number) {
        // 处理停留逻辑
        if (this._stayTimer > 0) {
            this._stayTimer -= dt;
            if (this._stayTimer <= 0) {
                this._stayPointIndex = -1;
                // 停留结束，继续移动到下一个点
                this.moveToNextPoint();
            }
            return;
        }

        // 使用匀加速直线运动更新位置
        const v0 = this.speed;
        // s = v0*t + 0.5*a*t^2
        const s = v0 * dt + 0.5 * this.acceleration * dt * dt;
        this.speed += this.acceleration * dt;

        // console.log(`v0: ${v0.toFixed(2)}, a: ${this.acceleration.toFixed(2)}, s: ${s.toFixed(2)}`);

        // 计算移动向量
        const angleRad = this.speedAngle;
        const deltaX = Math.cos(angleRad) * s;
        const deltaY = Math.sin(angleRad) * s;

        // 更新位置
        this._currentPosition.x += deltaX;
        this._currentPosition.y += deltaY;

        // 设置节点位置
        this.node.setPosition(this._currentPosition);

        // 检查是否到达目标点
        if (this._remainDistance > 0) {
            this._remainDistance -= s;
            if (this._remainDistance <= 0) {
                this.onReachPoint(this._nextPointIndex);
            }
        }
    }

    private tickTilting(dt: number) {
        // 应用倾斜偏移
        if (this.tiltSpeed > 0 && this.tiltOffset > 0) {
            this._tiltTime += dt;

            // 计算垂直于移动方向的向量
            const angleRad = this.speedAngle * Math.PI / 180;
            const perpX = -Math.sin(angleRad);
            const perpY = Math.cos(angleRad);

            // 计算倾斜偏移
            const tiltAmount = Math.sin(this._tiltTime * this.tiltSpeed) * this.tiltOffset;

            // 应用倾斜偏移到节点位置
            const finalX = this._currentPosition.x + perpX * tiltAmount;
            const finalY = this._currentPosition.y + perpY * tiltAmount;
            this.node.setPosition(finalX, finalY, 0);
        }
    }

    private onReachPoint(pointIndex: number) {
        // 更新当前点索引
        this._currentPointIndex = pointIndex;
        // 检查是否需要停留
        const currentPoint = this.getPathPoint(pointIndex);
        if (currentPoint) { 
            this._currentPosition.x = currentPoint.x;
            this._currentPosition.y = currentPoint.y;
            this.node.setPosition(this._currentPosition);

            console.log(`到达点 ${pointIndex}, 停留时间: ${currentPoint.stayDuration}ms`);
            if (currentPoint.stayDuration > 0) {
                this._stayTimer = currentPoint.stayDuration / 1000.0;
                this._stayPointIndex = pointIndex;
                return;
            }
        }

        // 继续移动到下一个点
        this.moveToNextPoint();
    }

    private moveToNextPoint() {
        const nextIndex = this._currentPointIndex + 1;

        if (nextIndex >= this._subdivided.length) {
            // 到达路径终点
            if (this.loop) {
                // 循环模式，回到起点
                this.setNext(0);
            } else {
                // 停止移动
                this._nextPointIndex = this._currentPointIndex;
            }
        } else {
            // 移动到下一个点
            this.setNext(nextIndex);
        }
    }

    private setNext(pathPointIndex: number) {
        this._nextPointIndex = pathPointIndex;

        const currentPoint = this.getPathPoint(this._currentPointIndex);
        const nextPoint = this.getPathPoint(this._nextPointIndex);
        if (currentPoint && nextPoint) {
            const dirX = nextPoint.x - currentPoint.x;
            const dirY = nextPoint.y - currentPoint.y;
            this._remainDistance = Math.sqrt(dirX * dirX + dirY * dirY);

            if (this._remainDistance > 1) {
                // 计算移动角度
                this.speedAngle = Math.atan2(dirY, dirX);

                // 计算加速度：使用匀加速直线运动公式 v1^2 = v0^2 + 2*a*x
                // 解出 a = (v1^2 - v0^2) / (2*x)
                const v0 = currentPoint.speed;
                const v1 = nextPoint.speed;
                this.acceleration = (v1 * v1 - v0 * v0) / (2 * this._remainDistance);

                // 设置初始速度
                this.speed = v0;

                // console.log(`设置下一段: ${this._currentPointIndex} -> ${this._nextPointIndex}, 距离: ${this._remainDistance.toFixed(2)}, 初速度: ${v0}, 末速度: ${v1}, 加速度: ${this.acceleration.toFixed(2)}, 角度: ${radiansToDegrees(this.speedAngle).toFixed(2)}`);
            }
        }
    }

    private getPathPoint(pathPointIndex: number): PathPoint | null {
        if (pathPointIndex < 0 || pathPointIndex >= this._subdivided.length) {
            return null;
        }
        return this._subdivided[pathPointIndex];
    }

    // 公共API方法
    public setMovable(movable: boolean): PathMove {
        this._isMovable = movable;
        return this;
    }

    private resetToStart() {
        this._currentPointIndex = 0;
        this._nextPointIndex = 0;
        this._stayTimer = 0;
        this._stayPointIndex = -1;
        this._tiltTime = 0;

        this.onReachPoint(0);
    }

    public isStaying(): boolean {
        return this._stayTimer > 0;
    }

    public getRemainingStayTime(): number {
        return this._stayTimer;
    }
}
