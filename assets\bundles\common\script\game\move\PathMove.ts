import { _decorator, Component, Enum, misc, Node, UITransform, Vec2, Vec3, JsonAsset } from 'cc';
import { BulletSystem } from '../bullet/BulletSystem';
import { MoveBase, eMoveEvent } from './IMovable';
import Entity from '../ui/base/Entity';
import { PathData, PathPoint } from '../data/PathData';

const { degreesToRadians, radiansToDegrees } = misc;
const { ccclass, property } = _decorator;

@ccclass('PathMove')
export class PathMove extends MoveBase {

    @property({ displayName: "循环移动" })
    public loop: boolean = false;

    @property({ displayName: "反向移动" })
    public reverse: boolean = false;

    @property({ displayName: "振荡偏移速度", tooltip: "控制倾斜振荡的频率" })
    public tiltSpeed: number = 0;

    @property({ displayName: "振荡偏移幅度", tooltip: "控制倾斜振荡的幅度" })
    public tiltOffset: number = 0;

    // 路径相关数据
    private _pathData: PathData | null = null;
    private _subdivided: PathPoint[] = []; // 细分后的路径点（包含完整信息）
    private _totalDistance: number = 0;
    private _distances: number[] = [];

    // 移动状态
    private _currentDistance: number = 0;
    private _currentPointIndex: number = 0; // 当前所在的细分点索引
    private _segmentT: number = 0; // 在当前段内的插值参数 [0,1]
    private _tiltTime: number = 0;

    // 停留状态
    private _isStaying: boolean = false; // 是否正在停留
    private _stayTimer: number = 0; // 停留计时器（秒）
    private _stayPointIndex: number = -1; // 停留的点索引

    /**
     * 加载路径数据（使用新的细分点方法）
     */
    public setPath(pathData: PathData) {
        this._pathData = pathData;
        // 使用新的细分点方法，直接获取包含完整信息的PathPoint数组
        this._subdivided = this._pathData.getSubdividedPoints();
        // 计算距离信息
        this.calculateDistances();
    }

    /**
     * 计算距离信息
     */
    private calculateDistances() {
        this._distances = [0];
        this._totalDistance = 0;

        for (let i = 1; i < this._subdivided.length; i++) {
            const distance = Vec2.distance(this._subdivided[i - 1].position, this._subdivided[i].position);
            this._totalDistance += distance;
            this._distances.push(this._totalDistance);
        }
    }

    /**
     * 主要的移动更新逻辑
     */
    public tick(dt: number): void {
        if (!this._isMovable || this._subdivided.length < 2) return;

        // 处理停留逻辑
        if (this._isStaying) {
            this._stayTimer -= dt;
            if (this._stayTimer <= 0) {
                this._isStaying = false;
                this._stayPointIndex = -1;
            } else {
                // 停留期间只更新位置（倾斜效果等），不移动
                this.updatePosition(dt);
                return;
            }
        }

        // 获取当前位置的速度（从细分点直接获取）
        const currentSpeed = this.getCurrentSpeed();

        // 更新沿路径的距离
        const deltaDistance = currentSpeed * dt;

        if (this.reverse) {
            this._currentDistance -= deltaDistance;
            if (this._currentDistance < 0) {
                if (this.loop) {
                    this._currentDistance = this._totalDistance + this._currentDistance;
                } else {
                    this._currentDistance = 0;
                }
            }
        } else {
            this._currentDistance += deltaDistance;
            if (this._currentDistance > this._totalDistance) {
                if (this.loop) {
                    this._currentDistance = this._currentDistance - this._totalDistance;
                } else {
                    this._currentDistance = this._totalDistance;
                }
            }
        }

        this.updateCurrentPointIndex();
        this.checkForStayPoint();
        this.updatePosition(dt);
    }

    /**
     * 检查是否到达需要停留的点
     */
    private checkForStayPoint(): void {
        if (this._isStaying || this._subdivided.length === 0 || !this._pathData) return;

        // 获取原始路径点
        const originalPoints = this._pathData.points;
        if (originalPoints.length === 0) return;

        // 检查是否接近任何原始路径点
        const tolerance = 10.0; // 容差值，用于判断是否足够接近路径点
        const currentPos = this.getCurrentPosition();

        for (let i = 0; i < originalPoints.length; i++) {
            const originalPoint = originalPoints[i];
            if (originalPoint.stayDuration <= 0) continue;

            // 检查距离是否足够接近这个原始路径点
            const distance = Vec2.distance(currentPos, originalPoint.position);
            if (distance <= tolerance && this._stayPointIndex !== i) {
                // 开始停留
                this._isStaying = true;
                this._stayTimer = originalPoint.stayDuration / 1000.0; // 转换为秒
                this._stayPointIndex = i;
                break;
            }
        }
    }

    /**
     * 根据当前距离更新点索引和段内插值参数
     */
    private updateCurrentPointIndex() {
        if (this._subdivided.length === 0) return;

        // 边界情况处理
        if (this._currentDistance <= 0) {
            this._currentPointIndex = 0;
            this._segmentT = 0;
            return;
        }

        if (this._currentDistance >= this._totalDistance) {
            this._currentPointIndex = Math.max(0, this._subdivided.length - 2);
            this._segmentT = 1;
            return;
        }

        // 从当前索引开始搜索，利用时间连续性
        let searchStart = Math.max(0, this._currentPointIndex - 1);
        let found = false;

        // 向前搜索
        for (let i = searchStart; i < this._distances.length - 1; i++) {
            if (this._currentDistance >= this._distances[i] && this._currentDistance <= this._distances[i + 1]) {
                this._currentPointIndex = i;
                const segmentLength = this._distances[i + 1] - this._distances[i];
                this._segmentT = segmentLength > 0 ? (this._currentDistance - this._distances[i]) / segmentLength : 0;
                found = true;
                break;
            }
        }

        // 如果向前搜索没找到，向后搜索（处理反向移动）
        if (!found) {
            for (let i = Math.min(searchStart, this._distances.length - 2); i >= 0; i--) {
                if (this._currentDistance >= this._distances[i] && this._currentDistance <= this._distances[i + 1]) {
                    this._currentPointIndex = i;
                    const segmentLength = this._distances[i + 1] - this._distances[i];
                    this._segmentT = segmentLength > 0 ? (this._currentDistance - this._distances[i]) / segmentLength : 0;
                    break;
                }
            }
        }
    }

    /**
     * 获取当前速度（使用索引优化）
     */
    private getCurrentSpeed(): number {
        if (this._subdivided.length === 0) {
            return this.speed; // 回退到基础速度
        }

        // 边界情况
        if (this._currentPointIndex >= this._subdivided.length - 1) {
            return this._subdivided[this._subdivided.length - 1].speed * this._speedMultiplier;
        }

        // 使用预计算的索引和插值参数
        const startSpeed = this._subdivided[this._currentPointIndex].speed;
        const endSpeed = this._subdivided[this._currentPointIndex + 1].speed;

        // 在两个细分点之间插值速度
        const interpolatedSpeed = startSpeed + (endSpeed - startSpeed) * this._segmentT;
        return interpolatedSpeed * this._speedMultiplier;
    }

    /**
     * 更新节点位置和朝向
     */
    private updatePosition(dt: number) {
        const position = this.getPositionAtDistance(this._currentDistance);
        
        // 应用倾斜偏移
        if (this.tiltSpeed > 0 && this.tiltOffset > 0) {
            this._tiltTime += dt;
            
            const direction = this.getDirectionAtDistance(this._currentDistance);
            if (direction.lengthSqr() > 0.001) {
                // 计算垂直于移动方向的向量
                const perpX = -direction.y;
                const perpY = direction.x;
                
                // 计算倾斜偏移
                const tiltAmount = Math.sin(this._tiltTime * this.tiltSpeed) * this.tiltOffset;
                
                position.x += perpX * tiltAmount;
                position.y += perpY * tiltAmount;
            }
        }

        this.node.setPosition(position.x, position.y, 0);

        // // 更新朝向
        // if (this.isFacingMoveDir) {
        //     const direction = this.getDirectionAtDistance(this._currentDistance);
        //     if (direction.lengthSqr() > 0.001) {
        //         const angle = Math.atan2(direction.y, direction.x) * 180 / Math.PI;
        //         const finalAngle = angle + this.defaultFacing;
        //         this.node.setRotationFromEuler(0, 0, finalAngle);
        //     }
        // }

        // 可见性检查
        if (++this._visibilityCheckCounter >= PathMove.VISIBILITY_CHECK_INTERVAL) {
            this._visibilityCheckCounter = 0;
            this.checkVisibility();
        }
    }

    /**
     * 根据距离获取位置（优化版本，主要用于当前位置）
     */
    private getPositionAtDistance(distance: number): Vec2 {
        // 如果查询的是当前距离，使用优化的索引方法
        if (Math.abs(distance - this._currentDistance) < 0.001) {
            return this.getCurrentPosition();
        }

        // 其他距离仍使用原来的方法（用于方向计算等）
        if (this._subdivided.length === 0) return new Vec2();
        if (distance <= 0) return this._subdivided[0].position.clone();
        if (distance >= this._totalDistance) return this._subdivided[this._subdivided.length - 1].position.clone();

        for (let i = 1; i < this._distances.length; i++) {
            if (distance <= this._distances[i]) {
                const segmentStart = this._distances[i - 1];
                const segmentEnd = this._distances[i];
                const segmentLength = segmentEnd - segmentStart;

                if (segmentLength === 0) return this._subdivided[i - 1].position.clone();

                const t = (distance - segmentStart) / segmentLength;
                return Vec2.lerp(new Vec2(), this._subdivided[i - 1].position, this._subdivided[i].position, t);
            }
        }

        return this._subdivided[this._subdivided.length - 1].position.clone();
    }

    /**
     * 获取当前位置（使用索引优化）
     */
    private getCurrentPosition(): Vec2 {
        if (this._subdivided.length === 0) return new Vec2();

        // 边界情况
        if (this._currentPointIndex >= this._subdivided.length - 1) {
            return this._subdivided[this._subdivided.length - 1].position.clone();
        }

        // 使用预计算的索引和插值参数
        const startPos = this._subdivided[this._currentPointIndex].position;
        const endPos = this._subdivided[this._currentPointIndex + 1].position;

        return Vec2.lerp(new Vec2(), startPos, endPos, this._segmentT);
    }

    /**
     * 根据距离获取移动方向
     */
    private getDirectionAtDistance(distance: number): Vec2 {
        const epsilon = 1;
        const pos1 = this.getPositionAtDistance(distance);
        const pos2 = this.getPositionAtDistance(distance + epsilon);

        return Vec2.subtract(new Vec2(), pos2, pos1).normalize();
    }

    // 公共API方法
    public setMovable(movable: boolean): PathMove {
        this._isMovable = movable;
        return this;
    }

    /**
     * 设置路径进度 [0-1]
     */
    public setProgress(progress: number): PathMove {
        this._currentDistance = Math.max(0, Math.min(1, progress)) * this._totalDistance;
        this.updateCurrentPointIndex(); // 更新索引
        this._isStaying = false;
        this._stayTimer = 0;
        this._stayPointIndex = -1;
        return this;
    }

    /**
     * 获取当前进度 [0-1]
     */
    public getProgress(): number {
        return this._totalDistance > 0 ? this._currentDistance / this._totalDistance : 0;
    }

    /**
     * 重置到路径起点
     */
    public resetToStart(): PathMove {
        this._currentDistance = 0;
        this._currentPointIndex = 0;
        this._segmentT = 0;
        this._isStaying = false;
        this._stayTimer = 0;
        this._stayPointIndex = -1;
        return this;
    }

    /**
     * 移动到路径终点
     */
    public moveToEnd(): PathMove {
        this._currentDistance = this._totalDistance;
        this.updateCurrentPointIndex(); // 更新索引
        this._isStaying = false;
        this._stayTimer = 0;
        this._stayPointIndex = -1;
        return this;
    }

    /**
     * 获取当前位置对应的路径点数据（使用索引优化）
     */
    public getCurrentPathPointData(): PathPoint | null {
        if (this._subdivided.length === 0) return null;

        // 边界情况
        if (this._currentPointIndex >= this._subdivided.length - 1) {
            return this._subdivided[this._subdivided.length - 1];
        }

        // 使用预计算的索引，返回更接近的那个点
        return this._segmentT < 0.5 ?
            this._subdivided[this._currentPointIndex] :
            this._subdivided[this._currentPointIndex + 1];
    }

    /**
     * 获取当前位置的详细路径信息（包括速度、朝向等）
     */
    public getCurrentPathInfo() {
        if (this._subdivided.length === 0) return null;

        const currentPoint = this.getCurrentPathPointData();
        const currentSpeed = this.getCurrentSpeed();

        return {
            speed: currentSpeed,
            position: this.getPositionAtDistance(this._currentDistance),
            direction: this.getDirectionAtDistance(this._currentDistance),
            pathPoint: currentPoint,
            progress: this.getProgress(),
            distance: this._currentDistance,
            totalDistance: this._totalDistance
        };
    }

    /**
     * 设置速度倍率（用于临时调整速度）
     */
    private _speedMultiplier: number = 1.0;

    public setSpeedMultiplier(multiplier: number): PathMove {
        this._speedMultiplier = Math.max(0, multiplier);
        return this;
    }

    public getSpeedMultiplier(): number {
        return this._speedMultiplier;
    }

    /**
     * 获取是否正在停留
     */
    public isStaying(): boolean {
        return this._isStaying;
    }

    /**
     * 获取剩余停留时间（秒）
     */
    public getRemainingStayTime(): number {
        return this._isStaying ? this._stayTimer : 0;
    }

    /**
     * 强制结束停留状态
     */
    public endStay(): PathMove {
        this._isStaying = false;
        this._stayTimer = 0;
        this._stayPointIndex = -1;
        return this;
    }
}
