import { _decorator, Component, Enum, misc, Node, UITransform, Vec2, Vec3, JsonAsset } from 'cc';
import { BulletSystem } from '../bullet/BulletSystem';
import { MoveBase, eMoveEvent } from './IMovable';
import Entity from '../ui/base/Entity';
import { PathData, PathPoint } from '../data/PathData';

const { degreesToRadians, radiansToDegrees } = misc;
const { ccclass, property, executeInEditMode } = _decorator;

@ccclass('PathMove')
@executeInEditMode()
export class PathMove extends MoveBase {
    public _pathAsset: JsonAsset | null = null;
    @property({ type: JsonAsset, displayName: "路径数据(预览用)" })
    public get pathAsset(): JsonAsset | null {
        return this._pathAsset;
    }
    public set pathAsset(value: JsonAsset) {
        this._pathAsset = value;
        if (value) {
            this.setPath(PathData.fromJSON(value.json));
        }
    }

    @property({ displayName: "循环移动" })
    public loop: boolean = false;

    @property({ displayName: "反向移动" })
    public reverse: boolean = false;

    @property({ displayName: "振荡偏移速度", tooltip: "控制倾斜振荡的频率" })
    public tiltSpeed: number = 0;

    @property({ displayName: "振荡偏移幅度", tooltip: "控制倾斜振荡的幅度" })
    public tiltOffset: number = 0;

    // 路径相关数据
    private _pathData: PathData | null = null;
    private _subdivided: PathPoint[] = []; // 细分后的路径点（包含完整信息）

    // 移动状态
    private _currentPosition: Vec3 = new Vec3();
    private _currentPointIndex: number = 0; // 当前所在的细分点索引
    private _nextPointIndex: number = 0;

    private _segmentT: number = 0; // 在当前段内的插值参数 [0,1]
    private _tiltTime: number = 0;

    // 停留状态
    private _isStaying: boolean = false; // 是否正在停留
    private _stayTimer: number = 0; // 停留计时器（秒）
    private _stayPointIndex: number = -1; // 停留的点索引

    private _updateInEditor: boolean = false;
    public onFocusInEditor() {
        this._updateInEditor = true;
        this._isMovable = true;
    }

    public onLostFocusInEditor() {
        this._updateInEditor = false;
        this._isMovable = false;
        this.resetToStart();
    }

    public update(dt: number) {
        if (this._updateInEditor) {
            this.tick(dt);
        }
    }

    /**
     * 加载路径数据（使用新的细分点方法）
     */
    public setPath(pathData: PathData) {
        this._pathData = pathData;
        // 使用新的细分点方法，直接获取包含完整信息的PathPoint数组
        this._subdivided = this._pathData.getSubdividedPoints();
        this.resetToStart();
    }

    /**
     * 主要的移动更新逻辑
     */
    public tick(dt: number): void {
        if (!this._isMovable) return;
        
        if (this._nextPointIndex !== this._currentPointIndex) {
            this.tickMovement(dt);
        }

        this.tickTilting(dt);
    }

    private tickMovement(dt: number) {
        
    }

    private tickTilting(dt: number) {
        
    }

    private resetToStart() {
        this._currentPointIndex = 0;
        this._nextPointIndex = 0;
        this._segmentT = 0;
        
    }

    private setNext(pathPointIndex: number) {
        this._nextPointIndex = pathPointIndex;

        const currentPoint = this.getPathPoint(this._currentPointIndex);
        const nextPoint = this.getPathPoint(this._nextPointIndex);
        if (currentPoint && nextPoint) {
            const distance = Vec2.distance(currentPoint.position, nextPoint.position);
            this._segmentT = 0;
            const speedDiff = nextPoint.speed - currentPoint.speed;
            this.acceleration = speedDiff / distance;
        }
    }

    private getPathPoint(pathPointIndex: number): PathPoint | null {
        if (pathPointIndex < 0 || pathPointIndex >= this._subdivided.length) {
            return null;
        }
        return this._subdivided[pathPointIndex];
    }
}
