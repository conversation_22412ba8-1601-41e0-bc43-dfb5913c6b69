import { _decorator, Label, ProgressBar } from 'cc';
import csproto from 'db://assets/bundles/common/script/autogen/pb/cs_proto.js';
import { BaseUI, UILayer, UIMgr } from "db://assets/scripts/core/base/UIMgr";
import { logError } from 'db://assets/scripts/utils/Logger';
import { MyApp } from '../../app/MyApp';
import { ResTaskClass } from '../../autogen/luban/schema';
import { BundleName } from '../../const/BundleConst';
import { DataMgr } from '../../data/DataManager';
import { BottomUIEvent } from '../../event/BottomUIEvent';
import { DataEvent } from '../../event/DataEvent';
import { EventMgr } from '../../event/EventManager';
import { ButtonPlus } from '../common/components/button/ButtonPlus';
import { BottomTab } from '../home/<USER>';
import { TaskUI } from './TaskUI';

const { ccclass, property } = _decorator;

@ccclass('TaskTipUI')
export class TaskTipUI extends BaseUI {
    public static getUrl(): string { return "prefab/ui/TaskTipUI"; }
    public static getLayer(): UILayer { return UILayer.Background }
    public static getBundleName(): string { return BundleName.HomeTask }
    @property(Label)
    private taskDesc: Label | null = null;
    @property(ProgressBar)
    private taskProgress: ProgressBar | null = null;
    @property(ButtonPlus)
    private taskJumpBtn: ButtonPlus | null = null;
    @property(Label)
    private progressDesc: Label | null = null;
    private _isHomeUIHide: boolean = false;

    protected onLoad(): void {
        EventMgr.on(DataEvent.TaskRefresh, this.onTaskRefresh, this);
        EventMgr.on(BottomUIEvent.SwitchPanel, this.onSwitchPanel, this);
        this.taskJumpBtn!.addClick(this.onTaskJump, this);
    }

    async onShow() {
        this.onTaskRefresh(ResTaskClass.DAILY_TASK);
    }

    async onHide(_isHomeUIHide: boolean = false): Promise<void> {
        this._isHomeUIHide = _isHomeUIHide
    }

    async onClose(): Promise<void> {
        EventMgr.targetOff(this);
    }

    private onSwitchPanel(from: BottomTab, to: BottomTab) {
        if (to !== BottomTab.Home) {
            this.node.active = false;
            return
        }
        if (this.node.active) return
        this.node.active = true;
    }

    private onTaskRefresh(taskClass: ResTaskClass) {
        if (taskClass !== ResTaskClass.DAILY_TASK || this._isHomeUIHide) {
            return;
        }
        const taskList = DataMgr.task.getTaskListByClass(taskClass).filter(t => t.status === csproto.comm.TASK_STATUS.TASK_STATUS_NORMAL).sort((a, b) => b.task_id! - a.task_id!);
        if (taskList.length == 0) {
            this.node.active = false
            return;
        }
        const taskInfo = taskList[0];
        const resTask = MyApp.lubanTables.TbResTask.get(taskInfo.task_id!);
        if (!resTask) {
            logError("TaskTipUI", `task id ${taskInfo.task_id} not found`);
            return;
        }
        this.node.active = true;
        const info = DataMgr.task.getTaskDescAndProgress(resTask);
        this.taskDesc!.string = info.desc;
        this.taskProgress!.progress = taskInfo.progress! / info.progressMax;
        this.progressDesc!.string = `${taskInfo.progress!}/${info.progressMax}`;
    }
    private onTaskJump() {
        UIMgr.openUI(TaskUI)
    }
}
