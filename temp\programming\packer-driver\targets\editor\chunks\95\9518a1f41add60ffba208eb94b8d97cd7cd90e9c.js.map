{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/EnemyManager.ts"], "names": ["EnemyManager", "Prefab", "instantiate", "MyApp", "SingletonBase", "GameEnum", "GameResourceList", "EnemyData", "GameIns", "BattleLayer", "Tools", "_normalCount", "_pfPlane", "_planePool", "_planeArr", "_willDeadPlane", "enemies", "preLoad", "battleManager", "addLoadCount", "resMgr", "load", "EnemyPlane", "error", "prefab", "checkLoadFinish", "addPlane", "id", "planeData", "planeId", "node", "pop", "createNewPlane", "instance", "addEnemy", "plane", "getComponent", "initPlane", "pushPlane", "Error", "planes", "removeAllAlivePlane", "isDead", "will<PERSON><PERSON><PERSON>", "arrC<PERSON>ain", "push", "updateGameLogic", "deltaTime", "i", "length", "removeAble", "removePlaneForIndex", "type", "EnemyType", "<PERSON><PERSON><PERSON>", "Ship", "splice", "mainReset", "subReset", "destroy", "Normal", "removeFromParent", "clear", "isEnemyOver", "getNormalPlaneCount", "index", "_willRemovePlane", "setAnimSpeed", "speed"], "mappings": ";;;8LAYaA,Y;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAZEC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;;AACdC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,Q,iBAAAA,Q;;AACFC,MAAAA,gB;;AACEC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,W;;AAEEC,MAAAA,K,iBAAAA,K;;;;;;;;;8BAGIV,Y,GAAN,MAAMA,YAAN;AAAA;AAAA,0CAAuD;AAAA;AAAA;AAAA,eAC1DW,YAD0D,GAC3C,CAD2C;AAAA,eAE1DC,QAF0D,GAEhC,IAFgC;AAAA,eAG1DC,UAH0D,GAGrC,EAHqC;AAAA,eAI1DC,SAJ0D,GAIhC,EAJgC;AAAA,eAK1DC,cAL0D,GAK3B,EAL2B;AAAA;;AAO/C,YAAPC,OAAO,GAAG;AACV,iBAAO,KAAKF,SAAZ;AACH;;AAEMG,QAAAA,OAAO,GAAG;AACb;AAAA;AAAA,kCAAQC,aAAR,CAAsBC,YAAtB,CAAmC,CAAnC;AACA;AAAA;AAAA,8BAAMC,MAAN,CAAaC,IAAb,CAAkB;AAAA;AAAA,oDAAiBC,UAAnC,EAA+CrB,MAA/C,EAAuD,CAACsB,KAAD,EAAaC,MAAb,KAAgC;AACnF,iBAAKZ,QAAL,GAAgBY,MAAhB;AACA;AAAA;AAAA,oCAAQN,aAAR,CAAsBO,eAAtB;AACH,WAHD;AAIH;;AAEMC,QAAAA,QAAQ,CAACC,EAAD,EAAa;AACxB,cAAI;AACA,kBAAMC,SAAS,GAAG;AAAA;AAAA,yCAAlB;AACAA,YAAAA,SAAS,CAACC,OAAV,GAAoBF,EAApB;AAEA,gBAAIG,IAAI,GAAG,KAAKjB,UAAL,CAAgBkB,GAAhB,MAA0B,KAAKC,cAAL,EAArC;AACA;AAAA;AAAA,4CAAYC,QAAZ,CAAqBC,QAArB,CAA8BJ,IAA9B;AAEA,kBAAMK,KAAK,GAAGL,IAAI,CAACM,YAAL,CAAkB,YAAlB,CAAd;AACAD,YAAAA,KAAK,CAACE,SAAN,CAAgBT,SAAhB;AACA,iBAAKU,SAAL,CAAeH,KAAf;AAEA,iBAAKxB,YAAL;AACA,mBAAOwB,KAAP;AACH,WAbD,CAaE,OAAOZ,KAAP,EAAc;AACZ,mBAAO,IAAP;AACH;AACJ;;AAEDS,QAAAA,cAAc,GAAS;AACnB,cAAI,CAAC,KAAKpB,QAAV,EAAoB;AAChB,kBAAM,IAAI2B,KAAJ,CAAU,wDAAV,CAAN;AACH;;AACD,gBAAMT,IAAU,GAAG5B,WAAW,CAAC,KAAKU,QAAN,CAA9B;AACA,iBAAOkB,IAAP;AACH;;AAES,YAANU,MAAM,GAAiB;AACvB,iBAAO,KAAK1B,SAAZ;AACH;AAED;AACJ;AACA;;;AACI2B,QAAAA,mBAAmB,GAAG;AAClB,eAAK,MAAMN,KAAX,IAAoB,KAAKrB,SAAzB,EAAoC;AAChC,gBAAI,CAACqB,KAAK,CAACO,MAAX,EAAmB;AACfP,cAAAA,KAAK,CAACQ,UAAN;AACH;AACJ;AACJ;AAED;AACJ;AACA;AACA;;;AACIL,QAAAA,SAAS,CAACH,KAAD,EAAoB;AACzB,cAAI,CAAC;AAAA;AAAA,8BAAMS,UAAN,CAAiB,KAAK9B,SAAtB,EAAiCqB,KAAjC,CAAL,EAA8C;AAC1C,iBAAKrB,SAAL,CAAe+B,IAAf,CAAoBV,KAApB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIW,QAAAA,eAAe,CAACC,SAAD,EAAoB;AAC/B,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKlC,SAAL,CAAemC,MAAnC,EAA2CD,CAAC,EAA5C,EAAgD;AAC5C,kBAAMb,KAAK,GAAG,KAAKrB,SAAL,CAAekC,CAAf,CAAd;;AACA,gBAAIb,KAAK,CAACe,UAAV,EAAsB;AAClB,mBAAKC,mBAAL,CAAyBH,CAAzB;AACAA,cAAAA,CAAC;AACJ,aAHD,MAGO;AACH,kBAAIb,KAAK,CAACO,MAAV,EAAkB;AACd,oBAAIP,KAAK,CAACiB,IAAN,KAAe;AAAA;AAAA,0CAASC,SAAT,CAAmBC,MAAlC,IAA4CnB,KAAK,CAACiB,IAAN,KAAe;AAAA;AAAA,0CAASC,SAAT,CAAmBE,IAAlF,EAAwF;AACpF,uBAAKxC,cAAL,CAAoB8B,IAApB,CAAyBV,KAAzB;;AACA,uBAAKrB,SAAL,CAAe0C,MAAf,CAAsBR,CAAtB,EAAyB,CAAzB;;AACAA,kBAAAA,CAAC;AACD;AACH;AACJ;;AACDb,cAAAA,KAAK,CAACW,eAAN,CAAsBC,SAAtB;AACH;AACJ;;AAED,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKjC,cAAL,CAAoBkC,MAAxC,EAAgDD,CAAC,EAAjD,EAAqD;AACjD,kBAAMb,KAAK,GAAG,KAAKpB,cAAL,CAAoBiC,CAApB,CAAd;;AACA,gBAAIb,KAAK,CAACe,UAAV,EAAsB;AAClB,mBAAKC,mBAAL,CAAyBH,CAAzB,EAA4B,IAA5B;AACAA,cAAAA,CAAC;AACJ,aAHD,MAGO;AACHb,cAAAA,KAAK,CAACW,eAAN,CAAsBC,SAAtB;AACH;AACJ;AACJ;;AAEDU,QAAAA,SAAS,GAAG;AACR,eAAKC,QAAL,GADQ,CAER;;AACA,eAAK,MAAMvB,KAAX,IAAoB,KAAKtB,UAAzB,EAAqC;AACjCsB,YAAAA,KAAK,CAACwB,OAAN;AACH;;AACD,eAAK9C,UAAL,CAAgB2C,MAAhB,CAAuB,CAAvB,EANQ,CAQR;;;AACA,eAAK,MAAMrB,KAAX,IAAoB,KAAKpB,cAAzB,EAAyC;AACrC,gBAAIoB,KAAK,IAAIA,KAAK,CAACL,IAAnB,EAAyB;AACrBK,cAAAA,KAAK,CAACL,IAAN,CAAW6B,OAAX;AACH;AACJ;;AACD,eAAK5C,cAAL,GAAsB,EAAtB;AACH;AAED;AACJ;AACA;;;AACI2C,QAAAA,QAAQ,GAAG;AACP,cAAIL,SAAS,GAAG;AAAA;AAAA,oCAASA,SAAzB;;AACA,eAAK,MAAMlB,KAAX,IAAoB,KAAKrB,SAAzB,EAAoC;AAChC,oBAAQqB,KAAK,CAACiB,IAAd;AACI,mBAAKC,SAAS,CAACO,MAAf;AACIzB,gBAAAA,KAAK,CAACQ,UAAN;;AACA,qBAAK9B,UAAL,CAAgBgC,IAAhB,CAAqBV,KAAK,CAACL,IAA3B;;AACA;AAJR;;AAMAK,YAAAA,KAAK,CAACL,IAAN,CAAW+B,gBAAX;AACH;;AACD,eAAK/C,SAAL,CAAe0C,MAAf,CAAsB,CAAtB;AACH;AAGD;AACJ;AACA;;;AACIM,QAAAA,KAAK,GAAG,CAEP;AAED;AACJ;AACA;;;AACIC,QAAAA,WAAW,GAAY;AACnB,iBAAO,KAAKjD,SAAL,CAAemC,MAAf,KAA0B,CAAjC;AACH;AAED;AACJ;AACA;;;AACIe,QAAAA,mBAAmB,GAAW;AAC1B,iBAAO,KAAKrD,YAAZ;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIwC,QAAAA,mBAAmB,CAACc,KAAD,EAAgBvB,MAAe,GAAG,KAAlC,EAAyC;AACxD,cAAIA,MAAJ,EAAY;AACR,iBAAKwB,gBAAL,CAAsB,KAAKnD,cAAL,CAAoBkD,KAApB,CAAtB;;AACA,iBAAKlD,cAAL,CAAoByC,MAApB,CAA2BS,KAA3B,EAAkC,CAAlC;AACH,WAHD,MAGO;AACH,iBAAKC,gBAAL,CAAsB,KAAKpD,SAAL,CAAemD,KAAf,CAAtB;;AACA,iBAAKnD,SAAL,CAAe0C,MAAf,CAAsBS,KAAtB,EAA6B,CAA7B;AACH;AACJ;AAGD;AACJ;AACA;AACA;;;AACIC,QAAAA,gBAAgB,CAAC/B,KAAD,EAAoB;AAChC,cAAIkB,SAAS,GAAG;AAAA;AAAA,oCAASA,SAAzB;;AACA,kBAAQlB,KAAK,CAACiB,IAAd;AACI,iBAAKC,SAAS,CAACO,MAAf;AACI,mBAAKjD,YAAL;;AACA,mBAAKE,UAAL,CAAgBgC,IAAhB,CAAqBV,KAAK,CAACL,IAA3B;;AACA;AAJR;;AAMAK,UAAAA,KAAK,CAACL,IAAN,CAAW+B,gBAAX;AACH;;AAEDM,QAAAA,YAAY,CAACC,KAAD,EAAgB;AACxB,eAAK,MAAMjC,KAAX,IAAoB,KAAKrB,SAAzB,EAAoC;AAChCqB,YAAAA,KAAK,CAACgC,YAAN,CAAmBC,KAAnB;AACH;AACJ;;AApMyD,O", "sourcesContent": ["import { Node, Prefab, instantiate } from \"cc\";\r\nimport { MyApp } from 'db://assets/bundles/common/script/app/MyApp';\r\nimport { SingletonBase } from \"../../../../../scripts/core/base/SingletonBase\";\r\nimport { GameEnum } from \"../const/GameEnum\";\r\nimport GameResourceList from \"../const/GameResourceList\";\r\nimport { EnemyData } from \"../data/EnemyData\"\r\nimport { GameIns } from \"../GameIns\";\r\nimport BattleLayer from \"../ui/layer/BattleLayer\";\r\nimport type EnemyPlane from \"../ui/plane/enemy/EnemyPlane\";\r\nimport { Tools } from \"../utils/Tools\";\r\n\r\n\r\nexport class EnemyManager extends SingletonBase<EnemyManager> {\r\n    _normalCount = 0;\r\n    _pfPlane: Prefab | null = null;\r\n    _planePool: Node[] = [];\r\n    _planeArr: EnemyPlane[] = [];\r\n    _willDeadPlane: EnemyPlane[] = [];\r\n\r\n    get enemies() {\r\n        return this._planeArr;\r\n    }\r\n\r\n    public preLoad() {\r\n        GameIns.battleManager.addLoadCount(1);\r\n        MyApp.resMgr.load(GameResourceList.EnemyPlane, Prefab, (error: any, prefab: Prefab) => {\r\n            this._pfPlane = prefab;\r\n            GameIns.battleManager.checkLoadFinish();\r\n        });\r\n    }\r\n\r\n    public addPlane(id: number) {\r\n        try {\r\n            const planeData = new EnemyData();\r\n            planeData.planeId = id;\r\n\r\n            let node = this._planePool.pop()! || this.createNewPlane()!;\r\n            BattleLayer.instance.addEnemy(node);\r\n\r\n            const plane = node.getComponent(\"EnemyPlane\") as EnemyPlane;\r\n            plane.initPlane(planeData);\r\n            this.pushPlane(plane);\r\n\r\n            this._normalCount++;\r\n            return plane;\r\n        } catch (error) {\r\n            return null;\r\n        }\r\n    }\r\n\r\n    createNewPlane(): Node {\r\n        if (!this._pfPlane) {\r\n            throw new Error(\"Plane prefab is not initialized. Call preLoad() first.\");\r\n        }\r\n        const node: Node = instantiate(this._pfPlane);\r\n        return node;\r\n    }\r\n\r\n    get planes(): EnemyPlane[] {\r\n        return this._planeArr;\r\n    }\r\n\r\n    /**\r\n     * 移除所有存活的敌机\r\n     */\r\n    removeAllAlivePlane() {\r\n        for (const plane of this._planeArr) {\r\n            if (!plane.isDead) {\r\n                plane.willRemove();\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 添加敌机到管理器\r\n     * @param plane 敌机对象\r\n     */\r\n    pushPlane(plane: EnemyPlane) {\r\n        if (!Tools.arrContain(this._planeArr, plane)) {\r\n            this._planeArr.push(plane);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 更新游戏逻辑\r\n     * @param deltaTime 每帧时间\r\n     */\r\n    updateGameLogic(deltaTime: number) {\r\n        for (let i = 0; i < this._planeArr.length; i++) {\r\n            const plane = this._planeArr[i];\r\n            if (plane.removeAble) {\r\n                this.removePlaneForIndex(i);\r\n                i--;\r\n            } else {\r\n                if (plane.isDead) {\r\n                    if (plane.type === GameEnum.EnemyType.Turret || plane.type === GameEnum.EnemyType.Ship) {\r\n                        this._willDeadPlane.push(plane);\r\n                        this._planeArr.splice(i, 1);\r\n                        i--;\r\n                        continue;\r\n                    }\r\n                }\r\n                plane.updateGameLogic(deltaTime);\r\n            }\r\n        }\r\n\r\n        for (let i = 0; i < this._willDeadPlane.length; i++) {\r\n            const plane = this._willDeadPlane[i];\r\n            if (plane.removeAble) {\r\n                this.removePlaneForIndex(i, true);\r\n                i--;\r\n            } else {\r\n                plane.updateGameLogic(deltaTime);\r\n            }\r\n        }\r\n    }\r\n\r\n    mainReset() {\r\n        this.subReset();\r\n        // 清理飞机池\r\n        for (const plane of this._planePool) {\r\n            plane.destroy();\r\n        }\r\n        this._planePool.splice(0);\r\n\r\n        // 清理即将死亡的飞机\r\n        for (const plane of this._willDeadPlane) {\r\n            if (plane && plane.node) {\r\n                plane.node.destroy();\r\n            }\r\n        }\r\n        this._willDeadPlane = [];\r\n    }\r\n\r\n    /**\r\n     * 重置子关卡\r\n     */\r\n    subReset() {\r\n        let EnemyType = GameEnum.EnemyType;\r\n        for (const plane of this._planeArr) {\r\n            switch (plane.type) {\r\n                case EnemyType.Normal:\r\n                    plane.willRemove();\r\n                    this._planePool.push(plane.node);\r\n                    break;\r\n            }\r\n            plane.node.removeFromParent();\r\n        }\r\n        this._planeArr.splice(0);\r\n    }\r\n\r\n\r\n    /**\r\n     * 清理敌人管理器\r\n     */\r\n    clear() {\r\n\r\n    }\r\n\r\n    /**\r\n     * 检查敌人是否全部消灭\r\n     */\r\n    isEnemyOver(): boolean {\r\n        return this._planeArr.length === 0;\r\n    }\r\n\r\n    /**\r\n     * 获取普通敌机数量\r\n     */\r\n    getNormalPlaneCount(): number {\r\n        return this._normalCount;\r\n    }\r\n\r\n    /**\r\n     * 根据索引移除敌机\r\n     * @param index 索引\r\n     * @param isDead 是否为死亡敌机\r\n     */\r\n    removePlaneForIndex(index: number, isDead: boolean = false) {\r\n        if (isDead) {\r\n            this._willRemovePlane(this._willDeadPlane[index]);\r\n            this._willDeadPlane.splice(index, 1);\r\n        } else {\r\n            this._willRemovePlane(this._planeArr[index]);\r\n            this._planeArr.splice(index, 1);\r\n        }\r\n    }\r\n\r\n\r\n    /**\r\n     * 处理即将移除的敌机\r\n     * @param plane 敌机对象\r\n     */\r\n    _willRemovePlane(plane: EnemyPlane) {\r\n        let EnemyType = GameEnum.EnemyType;\r\n        switch (plane.type) {\r\n            case EnemyType.Normal:\r\n                this._normalCount--;\r\n                this._planePool.push(plane.node);\r\n                break;\r\n        }\r\n        plane.node.removeFromParent();\r\n    }\r\n\r\n    setAnimSpeed(speed: number) {\r\n        for (const plane of this._planeArr) {\r\n            plane.setAnimSpeed(speed);\r\n        }\r\n    }\r\n}"]}