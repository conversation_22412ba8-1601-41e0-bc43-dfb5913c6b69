{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/utils.ts"], "names": ["LevelEditorUtils", "_decorator", "assetManager", "CCFloat", "CCInteger", "Enum", "Node", "Prefab", "LayerSplicingMode", "LayerType", "ccclass", "property", "LayerTypeZh", "Background", "Random", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "LayerSplicingModeZh", "node_height", "fix_height", "random_height", "LayerEditorRandomRange", "displayName", "constructor", "min", "max", "LevelScrollLayerUI", "type", "visible", "splicingMode", "splicingModeZh", "value", "splicingOffsetX", "splicingOffsetY", "LevelRandTerrainUI", "offSetX", "offSetY", "LevelRandTerrainsLayerUI", "dynamicTerrain", "LevelRandTerrainsLayersUI", "dynamicTerrains", "<PERSON><PERSON><PERSON><PERSON>", "displayOrder", "typeZh", "scrollLayers", "randomLayers", "emittierLayers", "LevelBackgroundLayer", "backgroundsNode", "getOrAddNode", "node_parent", "name", "node", "getChildByName", "<PERSON><PERSON><PERSON><PERSON>", "getOrAddComp", "classConstructor", "comp", "getComponent", "addComponent", "loadByPath", "path", "Promise", "resolve", "uuid", "Editor", "Message", "request", "loadAny", "err", "asset", "console", "warn"], "mappings": ";;;+KAgKaA,gB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAhKOC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,Y,OAAAA,Y;AAAqBC,MAAAA,O,OAAAA,O;AAASC,MAAAA,S,OAAAA,S;AAAsBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;;AACpEC,MAAAA,iB,iBAAAA,iB;AAAmBC,MAAAA,S,iBAAAA,S;;;;;;;;;OAEzC;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAuBV,U;;AAExBW,MAAAA,W,aAAAA,W;AAAAA,QAAAA,W,CAAAA,W,mBACI;AAAA;AAAA,oCAAUC,U;AADdD,QAAAA,W,CAAAA,W,mBAEI;AAAA;AAAA,oCAAUE,M;AAFdF,QAAAA,W,CAAAA,W,mBAGI;AAAA;AAAA,oCAAUG,M;AAHdH,QAAAA,W,CAAAA,W,mBAII;AAAA;AAAA,oCAAUI,Q;eAJdJ,W;QAAAA,W;;AAOAK,MAAAA,mB,aAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB,2CACQ;AAAA;AAAA,oDAAkBC,W;AAD1BD,QAAAA,mB,CAAAA,mB,2CAEQ;AAAA;AAAA,oDAAkBE,U;AAF1BF,QAAAA,mB,CAAAA,mB,2CAGQ;AAAA;AAAA,oDAAkBG,a;eAH1BH,mB;QAAAA,mB;;wCAOQI,sB,WADZX,OAAO,CAAC,wBAAD,C,UAEHC,QAAQ,CAAC;AAAEW,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAGRX,QAAQ,CAAC;AAAEW,QAAAA,WAAW,EAAE;AAAf,OAAD,C,2BALb,MACaD,sBADb,CACoC;AAOhCE,QAAAA,WAAW,CAACC,GAAD,EAAkBC,GAAlB,EAAmC;AAAA,cAAlCD,GAAkC;AAAlCA,YAAAA,GAAkC,GAApB,CAAoB;AAAA;;AAAA,cAAjBC,GAAiB;AAAjBA,YAAAA,GAAiB,GAAH,CAAG;AAAA;;AAAA;;AAAA;;AAC1C,eAAKD,GAAL,GAAWA,GAAX;AACA,eAAKC,GAAL,GAAWA,GAAX;AACH;;AAV+B,O;;;;;iBAEX,C;;;;;;;iBAGP,C;;;;oCASLC,kB,YADZhB,OAAO,CAAC,0BAAD,C,UAEHC,QAAQ,CAAC;AAACgB,QAAAA,IAAI,EAAE,CAACpB,MAAD,CAAP;AAAiBe,QAAAA,WAAW,EAAE;AAA9B,OAAD,C,UAERX,QAAQ,CAAC;AAACgB,QAAAA,IAAI,EAAEvB,SAAP;AAAkBkB,QAAAA,WAAW,EAAE;AAA/B,OAAD,C,UAGRX,QAAQ,CAAC;AAACiB,QAAAA,OAAO,EAAE;AAAV,OAAD,C,UAERjB,QAAQ,CAAC;AAACgB,QAAAA,IAAI,EAAEtB,IAAI,CAACY,mBAAD,CAAX;AAAkCK,QAAAA,WAAW,EAAC;AAA9C,OAAD,C,UAGRX,QAAQ,CAAC;AAACgB,QAAAA,IAAI,EAAEN,sBAAP;AAA+BC,QAAAA,WAAW,EAAC,OAA3C;AACNM,QAAAA,OAAO,EAAE,mBAAmC;AACxC,iBAAO,KAAKC,YAAL,KAAsB;AAAA;AAAA,sDAAkBT,aAA/C;AACH;AAHK,OAAD,C,WAORT,QAAQ,CAAC;AAACgB,QAAAA,IAAI,EAAEN,sBAAP;AAA+BC,QAAAA,WAAW,EAAC,OAA3C;AACNM,QAAAA,OAAO,EAAE,mBAAmC;AACxC,iBAAO,KAAKC,YAAL,KAAsB;AAAA;AAAA,sDAAkBT,aAA/C;AACH;AAHK,OAAD,C,6BAnBb,MACaM,kBADb,CACgC;AASH,YAAdI,cAAc,GAAwB;AAAE,iBAAO,KAAKD,YAAZ;AAA4D;;AACtF,YAAdC,cAAc,CAACC,KAAD,EAA6B;AAAE,eAAKF,YAAL,GAAoBE,KAApB;AAA4D;;AAepHR,QAAAA,WAAW,GAAG;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AACV,eAAKS,eAAL,GAAuB,IAAIX,sBAAJ,CAA2B,CAA3B,EAA8B,CAA9B,CAAvB;AACA,eAAKY,eAAL,GAAuB,IAAIZ,sBAAJ,CAA2B,CAA3B,EAA8B,CAA9B,CAAvB;AACH;;AA5B2B,O;;;;;iBAEK,E;;;;;;;iBAET,G;;;;;;;iBAGiB;AAAA;AAAA,sDAAkBH,W;;;;;;;iBAUH,I;;;;;;;iBAMA,I;;;;oCAS/CgB,kB,aADZxB,OAAO,CAAC,0BAAD,C,WAEHC,QAAQ,CAAC;AAACgB,QAAAA,IAAI,EAAEvB,SAAP;AAAkBkB,QAAAA,WAAW,EAAE;AAA/B,OAAD,C,WAGRX,QAAQ,CAAC;AAACgB,QAAAA,IAAI,EAAEpB,MAAP;AAAee,QAAAA,WAAW,EAAE;AAA5B,OAAD,C,WAGRX,QAAQ,CAAC;AAACgB,QAAAA,IAAI,EAAEN,sBAAP;AAA+BC,QAAAA,WAAW,EAAE;AAA5C,OAAD,C,WAGRX,QAAQ,CAAC;AAACgB,QAAAA,IAAI,EAAEN,sBAAP;AAA+BC,QAAAA,WAAW,EAAE;AAA5C,OAAD,C,8BAXb,MACaY,kBADb,CACgC;AAa5BX,QAAAA,WAAW,GAAG;AAAA;;AAAA;;AAR+B;AAQ/B;;AAAA;;AACV,eAAKY,OAAL,GAAe,IAAId,sBAAJ,CAA2B,CAA3B,EAA8B,CAA9B,CAAf;AACA,eAAKe,OAAL,GAAe,IAAIf,sBAAJ,CAA2B,CAA3B,EAA8B,CAA9B,CAAf;AACH;;AAhB2B,O;;;;;iBAEJ,G;;;;;;;iBAGe,I;;;;;;;iBAGS,I;;;;;;;iBAGA,I;;;;0CASvCgB,wB,aADZ3B,OAAO,CAAC,gCAAD,C,WAEHC,QAAQ,CAAC;AAACgB,QAAAA,IAAI,EAAEvB,SAAP;AAAkBkB,QAAAA,WAAW,EAAE;AAA/B,OAAD,C,WAGRX,QAAQ,CAAC;AAACgB,QAAAA,IAAI,EAAE,CAACO,kBAAD,CAAP;AAA6BZ,QAAAA,WAAW,EAAE;AAA1C,OAAD,C,gCALb,MACae,wBADb,CACsC;AAOlCd,QAAAA,WAAW,GAAG;AAAA;;AAAA;;AACV,eAAKe,cAAL,GAAsB,EAAtB;AACH;;AATiC,O;;;;;iBAEV,G;;;;;;;iBAGsB,E;;;;2CAQrCC,yB,aADZ7B,OAAO,CAAC,iCAAD,C,WAEHC,QAAQ,CAAC;AAACgB,QAAAA,IAAI,EAAE,CAACU,wBAAD,CAAP;AAAmCf,QAAAA,WAAW,EAAE;AAAhD,OAAD,C,gCAFb,MACaiB,yBADb,CACuC;AAInChB,QAAAA,WAAW,GAAG;AAAA;;AACV,eAAKiB,eAAL,GAAuB,EAAvB;AACH;;AANkC,O;;;;;iBAEkB,E;;;;4BAQ5CC,U,aADZ/B,OAAO,CAAC,kBAAD,C,WAEHC,QAAQ,CAAC;AAACW,QAAAA,WAAW,EAAE,IAAd;AAAoBoB,QAAAA,YAAY,EAAE;AAAlC,OAAD,C,WAER/B,QAAQ,CAAC;AAACgB,QAAAA,IAAI,EAAEvB,SAAP;AAAkBkB,QAAAA,WAAW,EAAE,MAA/B;AAAuCoB,QAAAA,YAAY,EAAE;AAArD,OAAD,C,WAER/B,QAAQ,CAAC;AAACgB,QAAAA,IAAI,EAAErB,IAAP;AAAaoC,QAAAA,YAAY,EAAE;AAA3B,OAAD,C,WAER/B,QAAQ,CAAC;AAACgB,QAAAA,IAAI,EAACxB,OAAN;AAAemB,QAAAA,WAAW,EAAC,IAA3B;AAAiCoB,QAAAA,YAAY,EAAE;AAA/C,OAAD,C,WAGR/B,QAAQ,CAAC;AAACgB,QAAAA,IAAI,EAAEtB,IAAI,CAACO,WAAD,CAAX;AAA0BU,QAAAA,WAAW,EAAC,MAAtC;AAA8CoB,QAAAA,YAAY,EAAE;AAA5D,OAAD,C,WAIR/B,QAAQ,CAAC;AAAEgB,QAAAA,IAAI,EAAE,CAACD,kBAAD,CAAR;AAA8BJ,QAAAA,WAAW,EAAE,KAA3C;AACNM,QAAAA,OAAO,EAAE,mBAA2B;AAChC,iBAAO,KAAKD,IAAL,KAAc;AAAA;AAAA,sCAAUZ,MAA/B;AACH,SAHK;AAIN2B,QAAAA,YAAY,EAAE;AAJR,OAAD,C,WAOR/B,QAAQ,CAAC;AAACgB,QAAAA,IAAI,EAAE,CAACY,yBAAD,CAAP;AAAoCjB,QAAAA,WAAW,EAAC,KAAhD;AACNM,QAAAA,OAAO,EAAE,mBAA2B;AAChC,iBAAO,KAAKD,IAAL,KAAc;AAAA;AAAA,sCAAUb,MAA/B;AACH,SAHK;AAIN4B,QAAAA,YAAY,EAAE;AAJR,OAAD,C,WAOR/B,QAAQ,CAAC;AAACgB,QAAAA,IAAI,EAAE,CAACpB,MAAD,CAAP;AAAiBe,QAAAA,WAAW,EAAC,KAA7B;AACNM,QAAAA,OAAO,EAAE,mBAA2B;AAChC,iBAAO,KAAKD,IAAL,KAAc;AAAA;AAAA,sCAAUX,QAA/B;AACH,SAHK;AAIN0B,QAAAA,YAAY,EAAE;AAJR,OAAD,C,gCA7Bb,MACaD,UADb,CACwB;AAWH,YAANE,MAAM,GAAgB;AAAE,iBAAO,KAAKhB,IAAZ;AAA4C;;AAC9D,YAANgB,MAAM,CAACZ,KAAD,EAAqB;AAAE,eAAKJ,IAAL,GAAYI,KAAZ;AAA2C;;AAwBnFR,QAAAA,WAAW,GAAG;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAvBPI,IAuBO,GAvBW;AAAA;AAAA,sCAAUd,UAuBrB;;AAAA;;AAAA;;AAAA;;AACV,eAAKc,IAAL,GAAY;AAAA;AAAA,sCAAUd,UAAtB;AACA,eAAK+B,YAAL,GAAoB,EAApB;AACA,eAAKC,YAAL,GAAoB,EAApB;AACA,eAAKC,cAAL,GAAsB,EAAtB;AACH;;AAzCmB,O;;;;;iBAEI,E;;;;;;;iBAEA,C;;;;;;;iBAEG,I;;;;;;;iBAEJ,E;;;;;;;iBAYqB,E;;;;;;;iBAOO,E;;;;;;;iBAOjB,E;;;;sCAWzBC,oB,aADZrC,OAAO,CAAC,4BAAD,C,WAEHC,QAAQ,CAAC;AAACgB,QAAAA,IAAI,EAAE,CAACpB,MAAD,CAAP;AAAiBe,QAAAA,WAAW,EAAE;AAA9B,OAAD,C,gCAFb,MACayB,oBADb,SAC0CN,UAD1C,CACqD;AAAA;AAAA;;AAAA;;AAAA,eAI1CO,eAJ0C,GAIb,IAJa;AAAA;;AAAA,O;;;;;iBAElB,E;;;;kCAKtBhD,gB,GAAN,MAAMA,gBAAN,CAAuB;AACA,eAAZiD,YAAY,CAACC,WAAD,EAAoBC,IAApB,EAAwC;AAC9D,cAAIC,IAAI,GAAGF,WAAW,CAAEG,cAAb,CAA4BF,IAA5B,CAAX;;AACA,cAAIC,IAAI,IAAI,IAAZ,EAAkB;AACdA,YAAAA,IAAI,GAAG,IAAI9C,IAAJ,CAAS6C,IAAT,CAAP;AACAD,YAAAA,WAAW,CAACI,QAAZ,CAAqBF,IAArB;AACH;;AACD,iBAAOA,IAAP;AACH;;AAEyB,eAAZG,YAAY,CAAsBH,IAAtB,EAAkCI,gBAAlC,EAAkG;AACxH,cAAIC,IAAI,GAAGL,IAAI,CAACM,YAAL,CAAkBF,gBAAlB,CAAX;;AACA,cAAIC,IAAI,IAAI,IAAZ,EAAkB;AACdA,YAAAA,IAAI,GAAGL,IAAI,CAACO,YAAL,CAAkBH,gBAAlB,CAAP;AACH;;AACD,iBAAOC,IAAP;AACH,SAhByB,CAkB1B;;;AAC8B,eAAVG,UAAU,CAAkBC,IAAlB,EAAiD;AAAA;AAC3E,gBAAIA,IAAI,IAAI,IAAR,IAAgBA,IAAI,IAAI,EAA5B,EAAgC;AAC5B,qBAAOC,OAAO,CAACC,OAAR,CAAgB,IAAhB,CAAP;AACH,aAH0E,CAK3E;;;AACA,gBAAMC,IAAI,SAASC,MAAM,CAACC,OAAP,CAAeC,OAAf,CAAuB,UAAvB,EAAmC,YAAnC,EAAiDN,IAAjD,CAAnB;;AACA,gBAAI,CAACG,IAAL,EAAW;AACP,qBAAOF,OAAO,CAACC,OAAR,CAAgB,IAAhB,CAAP;AACH;;AACD,mBAAO,IAAID,OAAJ,CAAuBC,OAAD,IAAa;AACtC7D,cAAAA,YAAY,CAACkE,OAAb,CAAwBJ,IAAxB,EAA8B,CAACK,GAAD,EAAiBC,KAAjB,KAA8B;AACxD,oBAAID,GAAJ,EAAS;AACLE,kBAAAA,OAAO,CAACC,IAAR,8BAAwCX,IAAxC,EAAgDQ,GAAhD;AACAN,kBAAAA,OAAO,CAAC,IAAD,CAAP;AACA;AACH;;AACDA,gBAAAA,OAAO,CAACO,KAAD,CAAP;AACH,eAPD;AAQH,aATM,CAAP;AAV2E;AAoB9E;;AAvCyB,O", "sourcesContent": ["import { __private, _decorator, assetManager, Asset, CCFloat, CCInteger, Component, Enum, Node, Prefab, TerrainLayer, CCBoolean } from \"cc\";\r\nimport { LayerEmittierType, LayerSplicingMode, LayerType } from \"db://assets/bundles/common/script/leveldata/leveldata\";\r\n\r\nconst { ccclass, property} = _decorator;\r\n\r\nenum LayerTypeZh {\r\n    背景 = LayerType.Background,\r\n    随机 = LayerType.Random,\r\n    滚动 = LayerType.Scroll,\r\n    发射 = LayerType.Emittier\r\n}\r\n\r\nenum LayerSplicingModeZh {\r\n    节点高度拼接 = LayerSplicingMode.node_height,\r\n    固定高度拼接 = LayerSplicingMode.fix_height,\r\n    随机高度拼接 = LayerSplicingMode.random_height\r\n}\r\n\r\n@ccclass('LayerEditorRandomRange')\r\nexport class LayerEditorRandomRange {\r\n    @property({ displayName: \"最小值\" })\r\n    public min: number = 0;\r\n    \r\n    @property({ displayName: \"最大值\" })\r\n    max: number = 0;\r\n\r\n    constructor(min: number = 0, max: number = 0) {\r\n        this.min = min;\r\n        this.max = max;\r\n    }\r\n}\r\n\r\n@ccclass('LevelEditorScrollLayerUI')\r\nexport class LevelScrollLayerUI { \r\n    @property({type: [Prefab], displayName: '滚动体'})\r\n    public scrollPrefabs: Prefab[] = [];\r\n    @property({type: CCInteger, displayName: \"权重\"})\r\n    public weight: number = 100;\r\n\r\n    @property({visible: false})\r\n    public splicingMode: LayerSplicingMode = LayerSplicingMode.node_height;\r\n    @property({type: Enum(LayerSplicingModeZh), displayName:\"拼接模式\",})\r\n    public get splicingModeZh(): LayerSplicingModeZh { return this.splicingMode as unknown as LayerSplicingModeZh;}\r\n    public set splicingModeZh(value: LayerSplicingModeZh) { this.splicingMode = value as unknown as LayerSplicingMode; }\r\n    @property({type: LayerEditorRandomRange, displayName:\"X偏移范围\",\r\n        visible: function(this: LevelScrollLayerUI) {\r\n            return this.splicingMode === LayerSplicingMode.random_height;\r\n        }\r\n    })\r\n    \r\n    public splicingOffsetX: LayerEditorRandomRange | null = null;; \r\n    @property({type: LayerEditorRandomRange, displayName:\"Y偏移范围\",\r\n        visible: function(this: LevelScrollLayerUI) {\r\n            return this.splicingMode === LayerSplicingMode.random_height;\r\n        }\r\n    })\r\n    public splicingOffsetY: LayerEditorRandomRange | null = null; \r\n\r\n    constructor() {\r\n        this.splicingOffsetX = new LayerEditorRandomRange(0, 0);\r\n        this.splicingOffsetY = new LayerEditorRandomRange(0, 0);\r\n    }\r\n}\r\n\r\n@ccclass('LevelEditorRandTerrainUI')\r\nexport class LevelRandTerrainUI {\r\n    @property({type: CCInteger, displayName: \"权重\"})\r\n    public weight: number = 100;\r\n\r\n    @property({type: Prefab, displayName: \"地形组预制体\"})\r\n    public terrainElement: Prefab | null = null; // 可以是TerrainElem、DynamicTerrains的预制体\r\n\r\n    @property({type: LayerEditorRandomRange, displayName: \"X偏移范围\"})\r\n    public offSetX: LayerEditorRandomRange | null = null;\r\n    \r\n    @property({type: LayerEditorRandomRange, displayName: \"Y偏移范围\"})\r\n    public offSetY: LayerEditorRandomRange | null = null;\r\n\r\n    constructor() {\r\n        this.offSetX = new LayerEditorRandomRange(0, 0);\r\n        this.offSetY = new LayerEditorRandomRange(0, 0);\r\n    }\r\n}\r\n\r\n@ccclass('LevelEditorRandTerrainsLayerUI')\r\nexport class LevelRandTerrainsLayerUI {\r\n    @property({type: CCInteger, displayName: \"权重\"})\r\n    public weight: number = 100;\r\n\r\n    @property({type: [LevelRandTerrainUI], displayName: \"地形策略\"})\r\n    public dynamicTerrain: LevelRandTerrainUI[] = []; \r\n\r\n    constructor() {\r\n        this.dynamicTerrain = [];\r\n    }\r\n}\r\n\r\n@ccclass('LevelEditorRandTerrainsLayersUI')\r\nexport class LevelRandTerrainsLayersUI {\r\n    @property({type: [LevelRandTerrainsLayerUI], displayName: \"地形策略组\"})\r\n    public dynamicTerrains: LevelRandTerrainsLayerUI[] = []; \r\n\r\n    constructor() {\r\n        this.dynamicTerrains = [];\r\n    }\r\n}\r\n\r\n@ccclass('LevelEditorLayer')\r\nexport class LevelLayer {\r\n    @property({displayName: \"备注\", displayOrder: 0})\r\n    public remark: string = \"\";\r\n    @property({type: CCInteger, displayName: \"层级顺序\", displayOrder: 1})\r\n    public zIndex: number = 0;\r\n    @property({type: Node, displayOrder: 2})\r\n    public node: Node | null = null;\r\n    @property({type:CCFloat, displayName:\"速度\", displayOrder: 3})\r\n    public speed: number = 10;\r\n\r\n    @property({type: Enum(LayerTypeZh), displayName:\"地形类型\", displayOrder: 4})\r\n    public get typeZh(): LayerTypeZh { return this.type as unknown as LayerTypeZh;}\r\n    public set typeZh(value: LayerTypeZh) { this.type = value as unknown as LayerType;}\r\n    public type: LayerType = LayerType.Background;\r\n    @property({ type: [LevelScrollLayerUI], displayName: \"滚动组\",\r\n        visible: function(this: LevelLayer) {\r\n            return this.type === LayerType.Scroll;\r\n        },\r\n        displayOrder: 5\r\n    })\r\n    public scrollLayers: LevelScrollLayerUI[] = [];\r\n    @property({type: [LevelRandTerrainsLayersUI], displayName:\"随机组\",\r\n        visible: function(this: LevelLayer) {\r\n            return this.type === LayerType.Random;\r\n        },\r\n        displayOrder: 6\r\n    })\r\n    public randomLayers: LevelRandTerrainsLayersUI[] = [];\r\n    @property({type: [Prefab], displayName:\"发射器\",\r\n        visible: function(this: LevelLayer) {\r\n            return this.type === LayerType.Emittier;\r\n        },\r\n        displayOrder: 7\r\n    })\r\n    public emittierLayers: Prefab[] = [];\r\n\r\n    constructor() {\r\n        this.type = LayerType.Background;\r\n        this.scrollLayers = [];\r\n        this.randomLayers = [];\r\n        this.emittierLayers = [];\r\n    }\r\n}\r\n\r\n@ccclass('LevelEditorBackgroundLayer')\r\nexport class LevelBackgroundLayer extends LevelLayer {\r\n    @property({type: [Prefab], displayName: '背景组'})\r\n    public backgrounds: Prefab[] = [];\r\n\r\n    public backgroundsNode: Node|null = null;\r\n}\r\n\r\nexport class LevelEditorUtils {\r\n    public static getOrAddNode(node_parent: Node, name: string): Node {\r\n        var node = node_parent!.getChildByName(name);\r\n        if (node == null) {\r\n            node = new Node(name);\r\n            node_parent.addChild(node);\r\n        }\r\n        return node;\r\n    }\r\n\r\n    public static getOrAddComp<T extends Component>(node: Node, classConstructor: __private.__types_globals__Constructor<T>): T {\r\n        var comp = node.getComponent(classConstructor);\r\n        if (comp == null) {\r\n            comp = node.addComponent(classConstructor);\r\n        }\r\n        return comp;\r\n    }\r\n    \r\n    // 仅在编辑器中使用\r\n    public static async loadByPath<T extends Asset>(path: string): Promise<T|null> {\r\n        if (path == null || path == \"\") {\r\n            return Promise.resolve(null);\r\n        }\r\n        \r\n        // @ts-ignore\r\n        const uuid = await Editor.Message.request('asset-db', 'query-uuid', path)!;\r\n        if (!uuid) {\r\n            return Promise.resolve(null);\r\n        }\r\n        return new Promise<T | null>((resolve) => {\r\n            assetManager.loadAny<T>(uuid, (err:Error|null, asset: T) => {\r\n                if (err) {\r\n                    console.warn(`Failed to load by path: ${path}`, err);\r\n                    resolve(null);\r\n                    return;\r\n                }\r\n                resolve(asset);\r\n            });\r\n        });\r\n    }\r\n}\r\n\r\n"]}