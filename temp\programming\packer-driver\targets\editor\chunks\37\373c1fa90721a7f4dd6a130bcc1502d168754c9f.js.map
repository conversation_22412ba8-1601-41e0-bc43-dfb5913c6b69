{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/SettlementUI.ts"], "names": ["_decorator", "Label", "Node", "ProgressBar", "RichText", "Sprite", "tween", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "MyApp", "BundleName", "DataMgr", "UITools", "StateSprite", "ButtonPlus", "StatisticsUI", "ccclass", "property", "SettlementUI", "game_stats", "_scoreTween", "_expTween", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Top", "getBundleName", "Home", "getUIOption", "isClickBgCloseUI", "onLoad", "btnOK", "addClick", "onOKClick", "btnNext", "onNextClick", "scoreAdd", "string", "lblScore", "scoreHigh", "gap", "score", "value", "to", "onUpdate", "target", "undefined", "Math", "round", "toString", "start", "curLevel", "role", "curExp", "maxExp", "exp", "expProBar", "progress", "lblExp", "lblLevel", "setNodeItem", "nodeItem1", "nodeItem2", "nodeItem3", "nodePass", "active", "stop", "closeUI", "openUI", "node", "itemID", "limit", "res", "lubanTables", "TbResItem", "get", "nodeQuaIcon", "getChildByName", "icon", "quaSprite", "getComponent", "setState", "quality", "labelNum", "modifyNumber", "labelLimit", "labelNumY", "y", "labelLimitY", "middleY", "trim", "onShow", "result", "modifyColorTag", "passText", "max_levels", "is_pass", "total_score", "nodeBestWeek", "is_week_bset", "history_bese_score", "lblTimeCost", "formatTime", "total_time_cost", "reward_items", "for<PERSON>ach", "item", "item_id", "expiration", "onHide", "onClose"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;;AAExDC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,Y,iBAAAA,Y;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBlB,U;;8BAIjBmB,Y,WADZF,OAAO,CAAC,cAAD,C,UAGHC,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,mC,UAGRA,QAAQ,CAACjB,KAAD,C,UAERiB,QAAQ,CAACjB,KAAD,C,UAERiB,QAAQ,CAACjB,KAAD,C,UAGRiB,QAAQ,CAACjB,KAAD,C,UAERiB,QAAQ,CAACf,WAAD,C,UAERe,QAAQ,CAACjB,KAAD,C,WAGRiB,QAAQ,CAAChB,IAAD,C,WAERgB,QAAQ,CAAChB,IAAD,C,WAERgB,QAAQ,CAAChB,IAAD,C,WAGRgB,QAAQ,CAAChB,IAAD,C,WAGRgB,QAAQ,CAACd,QAAD,C,WAGRc,QAAQ,CAAChB,IAAD,C,WAGRgB,QAAQ,CAACjB,KAAD,C,2BAtCb,MACakB,YADb;AAAA;AAAA,4BACyC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAwCrCC,UAxCqC,GAwCM,EAxCN;AAAA,eA0C7BC,WA1C6B;AA0CX;AA1CW,eA2C7BC,SA3C6B;AAAA;;AA2CX;AAEN,eAANC,MAAM,GAAW;AAAE,iBAAO,wBAAP;AAAkC;;AAC7C,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,GAAf;AAAoB;;AAC7B,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,IAAlB;AAAwB;;AACvC,eAAXC,WAAW,GAAU;AAC/B,iBAAO;AAAEC,YAAAA,gBAAgB,EAAE;AAApB,WAAP;AACH;;AAESC,QAAAA,MAAM,GAAS;AACrB,eAAKC,KAAL,CAAYC,QAAZ,CAAqB,KAAKC,SAA1B,EAAqC,IAArC;AACA,eAAKC,OAAL,CAAcF,QAAd,CAAuB,KAAKG,WAA5B,EAAyC,IAAzC;AAEA,eAAKC,QAAL,CAAeC,MAAf,GAAwB,UAAU,GAAV,GAAgB,GAAxC;AACA,eAAKC,QAAL,CAAeD,MAAf,GAAwB,GAAxB;AACA,eAAKE,SAAL,CAAgBF,MAAhB,GAAyB,WAAW,KAApC;AAEA,gBAAMG,GAAG,GAAG,GAAZ,CARqB,CASrB;;AACA,gBAAMC,KAAK,GAAG,IAAd;AACA,eAAKpB,WAAL,GAAmBf,KAAK,CAAC;AAAEoC,YAAAA,KAAK,EAAE;AAAT,WAAD,CAAL,CACdC,EADc,CACXH,GADW,EACN;AAAEE,YAAAA,KAAK,EAAED;AAAT,WADM,EACY;AACvBG,YAAAA,QAAQ,EAAGC,MAAD,IAAY;AAClB,kBAAI,CAAC,KAAKP,QAAN,IAAkBO,MAAM,KAAKC,SAAjC,EAA4C;AAC5C,mBAAKR,QAAL,CAAcD,MAAd,GAAuBU,IAAI,CAACC,KAAL,CAAWH,MAAM,CAACH,KAAlB,EAAyBO,QAAzB,EAAvB;AACH;AAJsB,WADZ,EAOdC,KAPc,EAAnB,CAXqB,CAoBrB;;AACA,cAAIC,QAAQ,GAAG;AAAA;AAAA,kCAAQC,IAAR,CAAaD,QAA5B;AACA,cAAIE,MAAM,GAAG;AAAA;AAAA,kCAAQD,IAAR,CAAaC,MAA1B;AACA,cAAIC,MAAM,GAAG;AAAA;AAAA,kCAAQF,IAAR,CAAaE,MAA1B;AACA,cAAIC,GAAG,GAAGF,MAAM,GAAGC,MAAnB;;AACA,cAAIC,GAAG,GAAG,CAAV,EAAa;AACTA,YAAAA,GAAG,GAAG,CAAN;AACH;;AAED,eAAKC,SAAL,CAAgBC,QAAhB,GAA2B,CAA3B;;AACA,cAAIH,MAAM,GAAG,CAAb,EAAgB;AACZ;AACA,iBAAKhC,SAAL,GAAiBhB,KAAK,CAAC,KAAKkD,SAAN,CAAL,CACZb,EADY,CACTH,GADS,EACJ;AAAEiB,cAAAA,QAAQ,EAAEF;AAAZ,aADI,EACe;AACxBX,cAAAA,QAAQ,EAAE,MAAM;AACZ,oBAAI,CAAC,KAAKY,SAAV,EAAqB;AACrB,qBAAKE,MAAL,CAAarB,MAAb,GAAuB,GAAEU,IAAI,CAACC,KAAL,CAAWM,MAAM,GAAG,KAAKE,SAAL,CAAeC,QAAnC,CAA6C,IAAGH,MAAO,EAAhF;AACH;AAJuB,aADf,EAOZJ,KAPY,EAAjB;AAQH;;AACD,eAAKS,QAAL,CAAetB,MAAf,GAAwB,OAAOc,QAA/B;AAEA,eAAKS,WAAL,CAAiB,KAAKC,SAAtB,EAAkC,QAAlC,EAA4C,GAA5C;AACA,eAAKD,WAAL,CAAiB,KAAKE,SAAtB,EAAkC,QAAlC,EAA4C,CAA5C;AACA,eAAKF,WAAL,CAAiB,KAAKG,SAAtB,EAAkC,QAAlC,EAA4C,GAA5C;AAEA,eAAKC,QAAL,CAAeC,MAAf,GAAwB,KAAxB;AACH;;AACc,cAAThC,SAAS,GAAG;AACd;AACA,cAAI,KAAKZ,WAAT,EAAsB,KAAKA,WAAL,CAAiB6C,IAAjB;AACtB,cAAI,KAAK5C,SAAT,EAAoB,KAAKA,SAAL,CAAe4C,IAAf;AACpB;AAAA;AAAA,8BAAMC,OAAN,CAAchD,YAAd;AACH;;AACgB,cAAXgB,WAAW,GAAG;AAChB;AACA,cAAI,KAAKd,WAAT,EAAsB,KAAKA,WAAL,CAAiB6C,IAAjB;AACtB,cAAI,KAAK5C,SAAT,EAAoB,KAAKA,SAAL,CAAe4C,IAAf;AACpB,gBAAM;AAAA;AAAA,8BAAME,MAAN;AAAA;AAAA,2CAAN;AACA;AAAA;AAAA,8BAAMD,OAAN,CAAchD,YAAd;AACH;;AAEDyC,QAAAA,WAAW,CAACS,IAAD,EAAaC,MAAb,EAA6BC,KAA7B,EAA4C;AAAA;;AACnD,cAAIC,GAAG,GAAG;AAAA;AAAA,8BAAMC,WAAN,CAAkBC,SAAlB,CAA4BC,GAA5B,CAAgCL,MAAhC,CAAV;;AACA,cAAIE,GAAG,KAAK,IAAZ,EAAkB;AACd;AACH;;AAED,cAAII,WAAW,GAAGP,IAAI,CAAEQ,cAAN,CAAqB,aAArB,CAAlB;AACA,cAAIC,IAAI,GAAGF,WAAW,CAAEC,cAAb,CAA4B,MAA5B,CAAX;AACA,cAAIE,SAAS,GAAGH,WAAW,CAAEC,cAAb,CAA4B,mBAA5B,CAAhB;AACAE,UAAAA,SAAS,QAAT,6BAAAA,SAAS,CAAEC,YAAX;AAAA;AAAA,4EAAsCC,QAAtC,CAA+CF,SAAS,CAAEC,YAAX,CAAwB3E,MAAxB,CAA/C,EAAiFmE,GAAG,CAAEU,OAAtF;AACA,cAAIC,QAAQ,GAAGd,IAAI,CAAEQ,cAAN,CAAqB,UAArB,CAAf;AACA;AAAA;AAAA,kCAAQO,YAAR,CAAqBD,QAAQ,CAAEH,YAAV,CAAuB/E,KAAvB,CAArB,EAAqD,GAArD;AACA,cAAIoF,UAAU,GAAGhB,IAAI,CAAEQ,cAAN,CAAqB,YAArB,CAAjB;;AACA,cAAIN,KAAK,KAAK,CAAd,EAAiB;AACbc,YAAAA,UAAU,CAAEL,YAAZ,CAAyB/E,KAAzB,EAAiCoC,MAAjC,GAA0C,EAA1C;AACH,WAFD,MAEO;AACH;AAAA;AAAA,oCAAQ+C,YAAR,CAAqBC,UAAU,CAAEL,YAAZ,CAAyB/E,KAAzB,CAArB,EAAuDsE,KAAvD;AACH;;AACD,gBAAMe,SAAS,GAAGH,QAAQ,CAAEI,CAA5B;AACA,gBAAMC,WAAW,GAAGH,UAAU,CAAEE,CAAhC;AACA,gBAAME,OAAO,GAAG,CAACH,SAAS,GAAGE,WAAb,IAA4B,CAA5C;;AACA,cAAIH,UAAU,CAAEL,YAAZ,CAAyB/E,KAAzB,EAAiCoC,MAAjC,IAA2CgD,UAAU,CAAEL,YAAZ,CAAyB/E,KAAzB,EAAiCoC,MAAjC,CAAwCqD,IAAxC,OAAmD,EAAlG,EAAsG;AAClGP,YAAAA,QAAQ,CAAEI,CAAV,GAAcE,OAAd;AACH;AACJ;;AACW,cAANE,MAAM,CAACC,MAAD,EAAkD;AAAA;;AAC1D,cAAIA,MAAM,IAAI,IAAd,EAAoB;AACpB;AAAA;AAAA,kCAAQC,cAAR,CAAuB,KAAKC,QAA5B,EAAuC,CAAvC,EAA0C;AAAEpD,YAAAA,KAAK,iDAAEkD,MAAM,CAACG,UAAT,qBAAE,mBAAmB9C,QAAnB,EAAF,oCAAmC;AAA1C,WAA1C;AACA,eAAKe,QAAL,CAAeC,MAAf,GAAyB2B,MAAM,CAACI,OAAP,IAAkB,CAA3C;AACA,eAAK1D,QAAL,CAAeD,MAAf,GAAwBuD,MAAM,CAACK,WAAP,CAAoBhD,QAApB,EAAxB;AACA,eAAKiD,YAAL,CAAmBjC,MAAnB,GAA6B2B,MAAM,CAACO,YAAP,IAAuB,CAApD;AACA;AAAA;AAAA,kCAAQf,YAAR,CAAqB,KAAK7C,SAA1B,EAAsCqD,MAAM,CAACQ,kBAA7C;AACA,eAAKC,WAAL,CAAkBhE,MAAlB,GAA2B,QAAQ;AAAA;AAAA,kCAAQiE,UAAR,CAAmBV,MAAM,CAACW,eAA1B,CAAnC;AACA,kCAAAX,MAAM,CAACY,YAAP,kCAAqBC,OAArB,CAA6BC,IAAI,IAAI;AACjC,iBAAK9C,WAAL,CAAiB,KAAKC,SAAtB,EAAkC6C,IAAI,CAACC,OAAvC,EAAiDD,IAAI,CAACE,UAAtD;AACH,WAFD;AAGA,eAAKxF,UAAL,GAAkBwE,MAAM,CAACxE,UAAzB;AACH;;AAEW,cAANyF,MAAM,GAAkB,CAC7B;;AACY,cAAPC,OAAO,GAAkB;AAC3B;AACA,cAAI,KAAKzF,WAAT,EAAsB,KAAKA,WAAL,CAAiB6C,IAAjB;AACtB,cAAI,KAAK5C,SAAT,EAAoB,KAAKA,SAAL,CAAe4C,IAAf;AACvB;;AAhKoC,O;;;;;iBAGV,I;;;;;;;iBAEE,I;;;;;;;iBAGJ,I;;;;;;;iBAEA,I;;;;;;;iBAEC,I;;;;;;;iBAGD,I;;;;;;;iBAEO,I;;;;;;;iBAET,I;;;;;;;iBAGE,I;;;;;;;iBAEA,I;;;;;;;iBAEA,I;;;;;;;iBAGG,I;;;;;;;iBAGA,I;;;;;;;iBAGJ,I;;;;;;;iBAGI,I", "sourcesContent": ["import { _decorator, Label, Node, ProgressBar, RichText, Sprite, tween } from 'cc';\r\nimport csproto from 'db://assets/bundles/common/script/autogen/pb/cs_proto.js';\r\nimport { BaseUI, UILayer, UIMgr, UIOpt } from \"db://assets/scripts/core/base/UIMgr\";\r\nimport { MyApp } from '../../app/MyApp';\r\nimport { BundleName } from '../../const/BundleConst';\r\nimport { DataMgr } from '../../data/DataManager';\r\nimport { UITools } from '../../game/utils/UITools';\r\nimport { StateSprite } from './components/base/StateSprite';\r\nimport { ButtonPlus } from './components/button/ButtonPlus';\r\nimport { StatisticsUI } from './StatisticsUI';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n\r\n@ccclass('SettlementUI')\r\nexport class SettlementUI extends BaseUI {\r\n\r\n    @property(ButtonPlus)\r\n    btnOK: ButtonPlus | null = null;\r\n    @property(ButtonPlus)\r\n    btnNext: ButtonPlus | null = null;\r\n\r\n    @property(Label)\r\n    lblScore: Label | null = null;\r\n    @property(Label)\r\n    scoreAdd: Label | null = null;\r\n    @property(Label)\r\n    scoreHigh: Label | null = null;\r\n\r\n    @property(Label)\r\n    lblLevel: Label | null = null;\r\n    @property(ProgressBar)\r\n    expProBar: ProgressBar | null = null;\r\n    @property(Label)\r\n    lblExp: Label | null = null;\r\n\r\n    @property(Node)\r\n    nodeItem1: Node | null = null;\r\n    @property(Node)\r\n    nodeItem2: Node | null = null;\r\n    @property(Node)\r\n    nodeItem3: Node | null = null;\r\n\r\n    @property(Node)\r\n    nodeBestWeek: Node | null = null;\r\n\r\n    @property(RichText)\r\n    passText: RichText | null = null;\r\n\r\n    @property(Node)\r\n    nodePass: Node | null = null;\r\n\r\n    @property(Label)\r\n    lblTimeCost: Label | null = null;\r\n\r\n    game_stats: csproto.comm.IGameStatItem[] = [];\r\n\r\n    private _scoreTween: any; // 分数动画的 tween 引用\r\n    private _expTween: any;   // 经验条动画的 tween 引用\r\n\r\n    public static getUrl(): string { return \"prefab/ui/SettlementUI\"; }\r\n    public static getLayer(): UILayer { return UILayer.Top }\r\n    public static getBundleName(): string { return BundleName.Home }\r\n    public static getUIOption(): UIOpt {\r\n        return { isClickBgCloseUI: false }\r\n    }\r\n\r\n    protected onLoad(): void {\r\n        this.btnOK!.addClick(this.onOKClick, this);\r\n        this.btnNext!.addClick(this.onNextClick, this);\r\n\r\n        this.scoreAdd!.string = \"分数加成 \" + 123 + \"%\";\r\n        this.lblScore!.string = \"0\";\r\n        this.scoreHigh!.string = \"历史最高分 \" + 10000;\r\n\r\n        const gap = 0.5;\r\n        // 分数动画\r\n        const score = 1000;\r\n        this._scoreTween = tween({ value: 0 })\r\n            .to(gap, { value: score }, {\r\n                onUpdate: (target) => {\r\n                    if (!this.lblScore || target === undefined) return;\r\n                    this.lblScore.string = Math.round(target.value).toString();\r\n                }\r\n            })\r\n            .start();\r\n\r\n        //  GM命令 //setattr xp 1000    \r\n        let curLevel = DataMgr.role.curLevel;\r\n        let curExp = DataMgr.role.curExp;\r\n        let maxExp = DataMgr.role.maxExp;\r\n        let exp = curExp / maxExp;\r\n        if (exp > 1) {\r\n            exp = 1;\r\n        }\r\n\r\n        this.expProBar!.progress = 0;\r\n        if (maxExp > 0) {\r\n            // 经验条动画\r\n            this._expTween = tween(this.expProBar!)\r\n                .to(gap, { progress: exp }, {\r\n                    onUpdate: () => {\r\n                        if (!this.expProBar) return;\r\n                        this.lblExp!.string = `${Math.round(maxExp * this.expProBar.progress)}/${maxExp}`;\r\n                    }\r\n                })\r\n                .start();\r\n        }\r\n        this.lblLevel!.string = \"lv\" + curLevel;\r\n\r\n        this.setNodeItem(this.nodeItem1!, 80000101, 100);\r\n        this.setNodeItem(this.nodeItem2!, 80100101, 0);\r\n        this.setNodeItem(this.nodeItem3!, 89999998, 200);\r\n\r\n        this.nodePass!.active = false;\r\n    }\r\n    async onOKClick() {\r\n        // 停止所有动画\r\n        if (this._scoreTween) this._scoreTween.stop();\r\n        if (this._expTween) this._expTween.stop();\r\n        UIMgr.closeUI(SettlementUI);\r\n    }\r\n    async onNextClick() {\r\n        // 停止所有动画\r\n        if (this._scoreTween) this._scoreTween.stop();\r\n        if (this._expTween) this._expTween.stop();\r\n        await UIMgr.openUI(StatisticsUI);\r\n        UIMgr.closeUI(SettlementUI);\r\n    }\r\n\r\n    setNodeItem(node: Node, itemID: number, limit: number) {\r\n        let res = MyApp.lubanTables.TbResItem.get(itemID);\r\n        if (res === null) {\r\n            return;\r\n        }\r\n\r\n        let nodeQuaIcon = node!.getChildByName(\"ItemQuaIcon\");\r\n        let icon = nodeQuaIcon!.getChildByName(\"icon\");\r\n        let quaSprite = nodeQuaIcon!.getChildByName(\"QualityTypeSprite\");\r\n        quaSprite?.getComponent(StateSprite)?.setState(quaSprite!.getComponent(Sprite)!, res!.quality);\r\n        let labelNum = node!.getChildByName(\"LabelNum\");\r\n        UITools.modifyNumber(labelNum!.getComponent(Label)!, 111);\r\n        let labelLimit = node!.getChildByName(\"LabelLimit\");\r\n        if (limit === 0) {\r\n            labelLimit!.getComponent(Label)!.string = \"\";\r\n        } else {\r\n            UITools.modifyNumber(labelLimit!.getComponent(Label)!, limit);\r\n        }\r\n        const labelNumY = labelNum!.y;\r\n        const labelLimitY = labelLimit!.y;\r\n        const middleY = (labelNumY + labelLimitY) / 2;\r\n        if (labelLimit!.getComponent(Label)!.string || labelLimit!.getComponent(Label)!.string.trim() === \"\") {\r\n            labelNum!.y = middleY;\r\n        }\r\n    }\r\n    async onShow(result: csproto.comm.IGameResult): Promise<void> {\r\n        if (result == null) return;\r\n        UITools.modifyColorTag(this.passText!, 1, { value: result.max_levels?.toString() ?? \"0\" });\r\n        this.nodePass!.active = (result.is_pass == 1);\r\n        this.lblScore!.string = result.total_score!.toString();\r\n        this.nodeBestWeek!.active = (result.is_week_bset == 1);\r\n        UITools.modifyNumber(this.scoreHigh!, result.history_bese_score!);\r\n        this.lblTimeCost!.string = \"用时 \" + UITools.formatTime(result.total_time_cost!);\r\n        result.reward_items?.forEach(item => {\r\n            this.setNodeItem(this.nodeItem1!, item.item_id!, item.expiration!);\r\n        });\r\n        this.game_stats = result.game_stats!;\r\n    }\r\n\r\n    async onHide(): Promise<void> {\r\n    }\r\n    async onClose(): Promise<void> {\r\n        // 停止所有动画\r\n        if (this._scoreTween) this._scoreTween.stop();\r\n        if (this._expTween) this._expTween.stop();\r\n    }\r\n}\r\n"]}