import csproto from 'db://assets/bundles/common/script/autogen/pb/cs_proto.js';
import { logWarn } from 'db://assets/scripts/utils/Logger';
import { MyApp } from "../../app/MyApp";
import { DataEvent } from '../../event/DataEvent';
import { EventMgr } from '../../event/EventManager';
import { IData } from "../DataManager";
export class Mail implements IData {

    mail_list: csproto.comm.IMail[] = [];

    public init(): void {
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_MAIL_GET_LIST, this.onMailGetList, this);
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_MAIL_NEW_MAIL_NOTIFY, this.onMailNewMailNotify, this);
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_MAIL_UPDATE_READED, this.onMailUpdateReaded, this);
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_MAIL_GET_ATTACHMENT, this.onMailGetAttachment, this);
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_MAIL_DELETE, this.onMailDelete, this);
        this.cmdMailGetListAll();
    }
    public update(): void {
    }
    public getMailByIndex(index: number): csproto.comm.IMail | undefined {
        if (index >= 0 && index < this.mail_list.length) {
            return this.mail_list[index];
        }
        return undefined;
    }
    //#region 收协议
    onMailGetList(msg: csproto.cs.IS2CMsg) {
        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {
            logWarn("NetMgr", `onMailGetList failed ${msg.ret_code}`);
            return;
        }
        var data = msg.body?.mail_get_list;
        if (!data) {
            logWarn("NetMgr", "onMailGetList data is null");
            return;
        }
        this.mail_list = data.mail_list!;
        EventMgr.emit(DataEvent.MailRefresh);
        //MessageBox.show(`onMailGetList ${JSON.stringify(data)}`);
    }
    onMailNewMailNotify(msg: csproto.cs.IS2CMsg) {
        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {
            logWarn("NetMgr", `onMailNewMailNotify failed ${msg.ret_code}`);
            return;
        }
        var data = msg.body?.mail_new_mail_notify;
        if (!data) {
            logWarn("NetMgr", "onMailNewMailNotify data is null");
            return;
        }
        let guids = data.guids!;
        this.cmdMailGetList(guids, 0, guids.length);
    }
    onMailUpdateReaded(msg: csproto.cs.IS2CMsg) {
        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {
            logWarn("NetMgr", `onMailUpdateReaded failed ${msg.ret_code}`);
            return;
        }
        var data = msg.body?.mail_update_readed;
        if (!data) {
            logWarn("NetMgr", "onMailUpdateReaded data is null");
            return;
        }
        EventMgr.emit(DataEvent.MailRefresh, data.guids);
    }
    onMailGetAttachment(msg: csproto.cs.IS2CMsg) {
        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {
            logWarn("NetMgr", `onMailGetAttachment failed ${msg.ret_code}`);
            return;
        }
        var data = msg.body?.mail_get_attachment;
        if (!data) {
            logWarn("NetMgr", "onMailGetAttachment data is null");
            return;
        }
    }
    onMailDelete(msg: csproto.cs.IS2CMsg) {
        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {
            logWarn("NetMgr", `onMailDelete failed ${msg.ret_code}`);
            return;
        }
        var data = msg.body?.mail_delete;
        if (!data) {
            logWarn("NetMgr", "onMailDelete data is null");
            return;
        }
    }
    //#endregion
    //#region 发协议
    cmdMailGetListAll() {
        this.cmdMailGetList([], 0, 0);
    }

    cmdMailGetList(ids: Long[], form: number, to: number) {
        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_MAIL_GET_LIST, {
            mail_get_list: {
                guids: ids,
                index_from: form,
                index_to: to,
            }
        });
    }
    cmdMailUpdateReaded(ids: Long[]) {
        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_MAIL_UPDATE_READED, {
            mail_update_readed: {
                guids: ids
            }
        });
    }
    cmdMailGetAttachment(ids: Long[]) {
        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_MAIL_GET_ATTACHMENT, {
            mail_get_attachment: {
                guids: ids
            }
        });
    }
    cmdMailDelete(ids: Long[]) {
        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_MAIL_DELETE, {
            mail_delete: {
                guids: ids
            }
        });
    }
    //#endregion
}
