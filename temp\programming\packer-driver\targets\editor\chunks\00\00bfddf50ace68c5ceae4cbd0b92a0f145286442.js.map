{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/HurtEffectManager.ts"], "names": ["HurtEffectManager", "Font", "instantiate", "Label", "Prefab", "Tween", "v3", "warn", "MyApp", "SingletonBase", "GameIns", "GameResourceList", "UIAnimMethods", "EnemyEffectLayer", "Tools", "ratio", "hurt<PERSON>um", "m_hurtNums", "Map", "m_hurtFont", "preLoad", "battleManager", "addLoadCount", "resMgr", "load", "HurtNum", "error", "prefab", "checkLoadFinish", "loadDir", "font_hurtNum", "fonts", "for<PERSON>ach", "font", "set", "name", "clear", "instances", "i", "labelNode", "label", "getComponent", "string", "push", "clearMapForCompArr", "createHurtNumByType", "position", "damage", "isCirt", "fontType", "lab", "GetHurtNumsByCount", "instance", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "node", "parent", "setPosition", "x", "y", "startHurtAni", "pool", "get", "length", "pop", "opacity", "active", "Math", "ceil", "toString", "stopAllByTarget", "tween", "setScale", "to", "fromTo", "scale", "call", "pushHurtNums", "start", "destroy"], "mappings": ";;;2NAUaA,iB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAVJC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;AAAaC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,E,OAAAA,E;AAAUC,MAAAA,I,OAAAA,I;;AACzDC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,gB;;AACAC,MAAAA,a;;AACAC,MAAAA,gB;;AACEC,MAAAA,K,iBAAAA,K;;;;;;;;;mCAGId,iB,GAAN,MAAMA,iBAAN;AAAA;AAAA,0CAAiE;AAAA;AAAA;AAAA,eACpEe,KADoE,GAC5D,GAD4D;AAAA,eAEpEC,OAFoE,GAE3C,IAF2C;AAAA,eAGpEC,UAHoE,GAGvD,IAAIC,GAAJ,EAHuD;AAAA,eAIpEC,UAJoE,GAIvD,IAAID,GAAJ,EAJuD;AAAA;;AAMpE;AACJ;AACA;AACIE,QAAAA,OAAO,GAAG;AACN,cAAI,CAAC,KAAKJ,OAAV,EAAmB;AACf;AAAA;AAAA,oCAAQK,aAAR,CAAsBC,YAAtB,CAAmC,CAAnC;AACA;AAAA;AAAA,gCAAMC,MAAN,CAAaC,IAAb,CAAkB;AAAA;AAAA,sDAAiBC,OAAnC,EAA4CrB,MAA5C,EAAoD,CAACsB,KAAD,EAAaC,MAAb,KAAgC;AAChF,mBAAKX,OAAL,GAAeW,MAAf;AACA;AAAA;AAAA,sCAAQN,aAAR,CAAsBO,eAAtB;AACH,aAHD;AAIH;;AAED,cAAI,CAAC,KAAKT,UAAV,EAAsB;AAClB,iBAAKA,UAAL,GAAkB,IAAID,GAAJ,EAAlB;AACH;;AAED;AAAA;AAAA,kCAAQG,aAAR,CAAsBC,YAAtB,CAAmC,CAAnC;AACA;AAAA;AAAA,8BAAMC,MAAN,CAAaM,OAAb,CAAqB;AAAA;AAAA,oDAAiBC,YAAtC,EAAoD7B,IAApD,EAA0D,CAACyB,KAAD,EAAaK,KAAb,KAA+B;AACrFA,YAAAA,KAAK,CAACC,OAAN,CAAeC,IAAD,IAAU;AACpB,kBAAIA,IAAJ,EAAU;AACN,qBAAKd,UAAL,CAAgBe,GAAhB,CAAoBD,IAAI,CAACE,IAAzB,EAA+BF,IAA/B;AACH;AACJ,aAJD;AAMA,iBAAKhB,UAAL,CAAgBmB,KAAhB;AACA,iBAAKjB,UAAL,CAAgBa,OAAhB,CAAwB,CAACC,IAAD,EAAOE,IAAP,KAAgB;AACpC,oBAAME,SAAS,GAAG,EAAlB;;AACA,mBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,CAApB,EAAuBA,CAAC,EAAxB,EAA4B;AACxB,oBAAI,CAAC,KAAKtB,OAAV,EAAmB;AACnB,sBAAMuB,SAAS,GAAGrC,WAAW,CAAC,KAAKc,OAAN,CAA7B;AACA,sBAAMwB,KAAK,GAAGD,SAAS,CAAEE,YAAX,CAAwBtC,KAAxB,CAAd;AACAqC,gBAAAA,KAAK,CAAEE,MAAP,GAAgB,EAAhB;AACAF,gBAAAA,KAAK,CAAEP,IAAP,GAAcA,IAAd;AACAI,gBAAAA,SAAS,CAACM,IAAV,CAAeH,KAAf;AACH;;AACD,mBAAKvB,UAAL,CAAgBiB,GAAhB,CAAoBC,IAApB,EAA0BE,SAA1B;AACH,aAXD;AAaA;AAAA;AAAA,oCAAQhB,aAAR,CAAsBO,eAAtB;AACH,WAtBD;AAuBH;AAGD;AACJ;AACA;;;AACIQ,QAAAA,KAAK,GAAG;AACJ;AAAA;AAAA,8BAAMQ,kBAAN,CAAyB,KAAK3B,UAA9B;AACH;;AAED4B,QAAAA,mBAAmB,CAACC,QAAD,EAAiBC,MAAjB,EAAiCC,MAAe,GAAG,KAAnD,EAA0D;AACzE,cAAID,MAAM,IAAI,CAAd,EAAiB;AAEjB,gBAAME,QAAQ,GAAGD,MAAM,GAAG,eAAH,GAAqB,cAA5C;AACA,gBAAME,GAAU,GAAG,KAAKC,kBAAL,CAAwBF,QAAxB,EAAkCF,MAAlC,CAAnB;;AAEA,cAAIG,GAAG,IAAI;AAAA;AAAA,oDAAiBE,QAAjB,CAA0BC,YAArC,EAAmD;AAC/CH,YAAAA,GAAG,CAACI,IAAJ,CAASC,MAAT,GAAkB;AAAA;AAAA,sDAAiBH,QAAjB,CAA0BC,YAA5C;AACAH,YAAAA,GAAG,CAACI,IAAJ,CAASE,WAAT,CAAqBV,QAAQ,CAACW,CAA9B,EAAiCX,QAAQ,CAACY,CAAT,GAAa,EAA9C;AAEA,iBAAKC,YAAL,CAAkBT,GAAlB,EAAuBD,QAAvB;AACH;AACJ;;AAEDE,QAAAA,kBAAkB,CAACF,QAAD,EAAmBF,MAAnB,EAAmC;AACjD,cAAI/B,OAAO,GAAG,IAAd;AACA,gBAAM4C,IAAI,GAAG,KAAK3C,UAAL,CAAgB4C,GAAhB,CAAoBZ,QAApB,CAAb;;AAEA,cAAIW,IAAJ,EAAU;AACN,gBAAIA,IAAI,CAACE,MAAL,GAAc,CAAlB,EAAqB;AACjB9C,cAAAA,OAAO,GAAG4C,IAAI,CAACG,GAAL,EAAV;AACH,aAFD,MAEO;AACH/C,cAAAA,OAAO,GAAGd,WAAW,CAAC,KAAKc,OAAN,CAAX,CAA2ByB,YAA3B,CAAwCtC,KAAxC,CAAV;AACAa,cAAAA,OAAO,CAACiB,IAAR,GAAe,KAAKd,UAAL,CAAgB0C,GAAhB,CAAoBZ,QAApB,CAAf;AACH;AACJ;;AAEDjC,UAAAA,OAAO,CAACsC,IAAR,CAAaU,OAAb,GAAuB,GAAvB;AACAhD,UAAAA,OAAO,CAACsC,IAAR,CAAaW,MAAb,GAAsB,IAAtB;AACAjD,UAAAA,OAAO,CAAC0B,MAAR,GAAiBwB,IAAI,CAACC,IAAL,CAAUpB,MAAV,EAAkBqB,QAAlB,EAAjB;AAEA,iBAAOpD,OAAP;AACH;;AAED2C,QAAAA,YAAY,CAAC3C,OAAD,EAAiBiC,QAAjB,EAAmC;AAC3C,gBAAMlC,KAAK,GAAG,KAAKA,KAAnB;AACAV,UAAAA,KAAK,CAACgE,eAAN,CAAsBrD,OAAO,CAACsC,IAA9B;AAEA,cAAIgB,KAAJ;;AACA,kBAAQrB,QAAR;AACI,iBAAK,cAAL;AACIjC,cAAAA,OAAO,CAACsC,IAAR,CAAaiB,QAAb,CAAsB,IAAtB,EAA4B,IAA5B;AACAD,cAAAA,KAAK,GAAG,IAAIjE,KAAJ,CAAUW,OAAO,CAACsC,IAAlB,EACHkB,EADG,CACA;AAAA;AAAA,kDAAcC,MAAd,CAAqB,CAArB,EAAwB,CAAxB,CADA,EAC4B;AAAEC,gBAAAA,KAAK,EAAEpE,EAAE,CAAC,OAAOS,KAAR,EAAe,OAAOA,KAAtB;AAAX,eAD5B,EAEHyD,EAFG,CAEA;AAAA;AAAA,kDAAcC,MAAd,CAAqB,CAArB,EAAwB,EAAxB,CAFA,EAE6B;AAAEC,gBAAAA,KAAK,EAAEpE,EAAE,CAAC,OAAOS,KAAR,EAAe,OAAOA,KAAtB;AAAX,eAF7B,EAGHyD,EAHG,CAGA;AAAA;AAAA,kDAAcC,MAAd,CAAqB,EAArB,EAAyB,EAAzB,CAHA,EAG8B;AAAE3B,gBAAAA,QAAQ,EAAExC,EAAE,CAACU,OAAO,CAACsC,IAAR,CAAaG,CAAd,EAAiBzC,OAAO,CAACsC,IAAR,CAAaI,CAAb,GAAiB,EAAlC,CAAd;AAAqDgB,gBAAAA,KAAK,EAAEpE,EAAE,CAAC,OAAOS,KAAR,EAAe,OAAOA,KAAtB;AAA9D,eAH9B,CAAR;AAIA;;AAEJ,iBAAK,eAAL;AACIC,cAAAA,OAAO,CAACsC,IAAR,CAAaiB,QAAb,CAAsB,IAAtB,EAA4B,IAA5B;AACAD,cAAAA,KAAK,GAAG,IAAIjE,KAAJ,CAAUW,OAAO,CAACsC,IAAlB,EACHkB,EADG,CACA;AAAA;AAAA,kDAAcC,MAAd,CAAqB,CAArB,EAAwB,CAAxB,CADA,EAC4B;AAAEC,gBAAAA,KAAK,EAAEpE,EAAE,CAAC,OAAOS,KAAR,EAAe,OAAOA,KAAtB;AAAX,eAD5B,EAEHyD,EAFG,CAEA;AAAA;AAAA,kDAAcC,MAAd,CAAqB,CAArB,EAAwB,CAAxB,CAFA,EAE4B;AAAEC,gBAAAA,KAAK,EAAEpE,EAAE,CAAC,OAAOS,KAAR,EAAe,OAAOA,KAAtB;AAAX,eAF5B,EAGHyD,EAHG,CAGA;AAAA;AAAA,kDAAcC,MAAd,CAAqB,CAArB,EAAwB,EAAxB,CAHA,EAG6B;AAAEC,gBAAAA,KAAK,EAAEpE,EAAE,CAAC,OAAOS,KAAR,EAAe,OAAOA,KAAtB;AAAX,eAH7B,EAIHyD,EAJG,CAIA;AAAA;AAAA,kDAAcC,MAAd,CAAqB,EAArB,EAAyB,EAAzB,CAJA,EAI8B;AAAE3B,gBAAAA,QAAQ,EAAExC,EAAE,CAACU,OAAO,CAACsC,IAAR,CAAaG,CAAd,EAAiBzC,OAAO,CAACsC,IAAR,CAAaI,CAAb,GAAiB,EAAlC,CAAd;AAAqDgB,gBAAAA,KAAK,EAAEpE,EAAE,CAAC,OAAOS,KAAR,EAAe,OAAOA,KAAtB;AAA9D,eAJ9B,CAAR;AAKA;;AAEJ;AACIR,cAAAA,IAAI,CAAC,4CAAD,CAAJ;AAnBR;;AAsBA+D,UAAAA,KAAK,CAAEK,IAAP,CAAY,MAAM;AACd,iBAAKC,YAAL,CAAkB3B,QAAlB,EAA4BjC,OAA5B;AACH,WAFD,EAEG6D,KAFH;AAGH;;AAEDD,QAAAA,YAAY,CAAC3B,QAAD,EAAmBjC,OAAnB,EAAmC;AAC3C,cAAIA,OAAO,IAAIA,OAAO,CAACsC,IAAvB,EAA6B;AACzBtC,YAAAA,OAAO,CAAC0B,MAAR,GAAiB,EAAjB;AACA1B,YAAAA,OAAO,CAACsC,IAAR,CAAaW,MAAb,GAAsB,KAAtB;AAEA,kBAAML,IAAI,GAAG,KAAK3C,UAAL,CAAgB4C,GAAhB,CAAoBZ,QAApB,CAAb;;AACA,gBAAIW,IAAJ,EAAU;AACNA,cAAAA,IAAI,CAACjB,IAAL,CAAU3B,OAAV;AACH,aAFD,MAEO;AACHA,cAAAA,OAAO,CAACsC,IAAR,CAAawB,OAAb;AACH;AACJ;AACJ;;AAtImE,O", "sourcesContent": ["import { Font, instantiate, Label, Node, Prefab, Tween, v3, Vec3, warn } from \"cc\";\r\nimport { MyApp } from 'db://assets/bundles/common/script/app/MyApp';\r\nimport { SingletonBase } from \"../../../../../scripts/core/base/SingletonBase\";\r\nimport { GameIns } from \"../GameIns\";\r\nimport GameResourceList from \"../const/GameResourceList\";\r\nimport UIAnimMethods from \"../ui/base/UIAnimMethods\";\r\nimport EnemyEffectLayer from \"../ui/layer/EnemyEffectLayer\";\r\nimport { Tools } from \"../utils/Tools\";\r\n\r\n\r\nexport class HurtEffectManager extends SingletonBase<HurtEffectManager> {\r\n    ratio = 0.8;\r\n    hurtNum: Prefab | null = null;\r\n    m_hurtNums = new Map();\r\n    m_hurtFont = new Map();\r\n\r\n    /**\r\n     * 预加载资源\r\n     */\r\n    preLoad() {\r\n        if (!this.hurtNum) {\r\n            GameIns.battleManager.addLoadCount(1);\r\n            MyApp.resMgr.load(GameResourceList.HurtNum, Prefab, (error: any, prefab: Prefab) => {\r\n                this.hurtNum = prefab;\r\n                GameIns.battleManager.checkLoadFinish();\r\n            });\r\n        }\r\n\r\n        if (!this.m_hurtFont) {\r\n            this.m_hurtFont = new Map();\r\n        }\r\n\r\n        GameIns.battleManager.addLoadCount(1);\r\n        MyApp.resMgr.loadDir(GameResourceList.font_hurtNum, Font, (error: any, fonts: Font[]) => {\r\n            fonts.forEach((font) => {\r\n                if (font) {\r\n                    this.m_hurtFont.set(font.name, font);\r\n                }\r\n            });\r\n\r\n            this.m_hurtNums.clear();\r\n            this.m_hurtFont.forEach((font, name) => {\r\n                const instances = [];\r\n                for (let i = 0; i < 3; i++) {\r\n                    if (!this.hurtNum) continue;\r\n                    const labelNode = instantiate(this.hurtNum) as Node;\r\n                    const label = labelNode!.getComponent(Label);\r\n                    label!.string = \"\";\r\n                    label!.font = font;\r\n                    instances.push(label);\r\n                }\r\n                this.m_hurtNums.set(name, instances);\r\n            });\r\n\r\n            GameIns.battleManager.checkLoadFinish();\r\n        });\r\n    }\r\n\r\n\r\n    /**\r\n     * 清理资源\r\n     */\r\n    clear() {\r\n        Tools.clearMapForCompArr(this.m_hurtNums);\r\n    }\r\n\r\n    createHurtNumByType(position: Vec3, damage: number, isCirt: boolean = false) {\r\n        if (damage <= 0) return;\r\n\r\n        const fontType = isCirt ? \"yellowHurtNum\" : \"whiteHurtNum\";\r\n        const lab: Label = this.GetHurtNumsByCount(fontType, damage);\r\n\r\n        if (lab && EnemyEffectLayer.instance.hurtNumLayer) {\r\n            lab.node.parent = EnemyEffectLayer.instance.hurtNumLayer;\r\n            lab.node.setPosition(position.x, position.y - 30);\r\n\r\n            this.startHurtAni(lab, fontType);\r\n        }\r\n    }\r\n\r\n    GetHurtNumsByCount(fontType: string, damage: number) {\r\n        let hurtNum = null;\r\n        const pool = this.m_hurtNums.get(fontType);\r\n\r\n        if (pool) {\r\n            if (pool.length > 0) {\r\n                hurtNum = pool.pop();\r\n            } else {\r\n                hurtNum = instantiate(this.hurtNum!).getComponent(Label)!;\r\n                hurtNum.font = this.m_hurtFont.get(fontType);\r\n            }\r\n        }\r\n\r\n        hurtNum.node.opacity = 255;\r\n        hurtNum.node.active = true;\r\n        hurtNum.string = Math.ceil(damage).toString();\r\n\r\n        return hurtNum;\r\n    }\r\n\r\n    startHurtAni(hurtNum: Label, fontType: string) {\r\n        const ratio = this.ratio;\r\n        Tween.stopAllByTarget(hurtNum.node);\r\n\r\n        let tween: Tween;\r\n        switch (fontType) {\r\n            case \"whiteHurtNum\":\r\n                hurtNum.node.setScale(0.15, 0.15);\r\n                tween = new Tween(hurtNum.node)\r\n                    .to(UIAnimMethods.fromTo(1, 6), { scale: v3(1.53 * ratio, 1.53 * ratio) })\r\n                    .to(UIAnimMethods.fromTo(6, 11), { scale: v3(0.47 * ratio, 0.47 * ratio) })\r\n                    .to(UIAnimMethods.fromTo(11, 32), { position: v3(hurtNum.node.x, hurtNum.node.y + 13), scale: v3(0.47 * ratio, 0.47 * ratio) });\r\n                break;\r\n\r\n            case \"yellowHurtNum\":\r\n                hurtNum.node.setScale(0.16, 0.16);\r\n                tween = new Tween(hurtNum.node)\r\n                    .to(UIAnimMethods.fromTo(1, 6), { scale: v3(1.75 * ratio, 1.75 * ratio) })\r\n                    .to(UIAnimMethods.fromTo(6, 9), { scale: v3(0.44 * ratio, 0.44 * ratio) })\r\n                    .to(UIAnimMethods.fromTo(9, 12), { scale: v3(0.52 * ratio, 0.52 * ratio) })\r\n                    .to(UIAnimMethods.fromTo(12, 31), { position: v3(hurtNum.node.x, hurtNum.node.y + 21), scale: v3(0.52 * ratio, 0.52 * ratio) });\r\n                break;\r\n\r\n            default:\r\n                warn(\"Unknown font type in createHurtNumInTarget\");\r\n        }\r\n\r\n        tween!.call(() => {\r\n            this.pushHurtNums(fontType, hurtNum);\r\n        }).start();\r\n    }\r\n\r\n    pushHurtNums(fontType: string, hurtNum: Label) {\r\n        if (hurtNum && hurtNum.node) {\r\n            hurtNum.string = \"\";\r\n            hurtNum.node.active = false;\r\n\r\n            const pool = this.m_hurtNums.get(fontType);\r\n            if (pool) {\r\n                pool.push(hurtNum);\r\n            } else {\r\n                hurtNum.node.destroy();\r\n            }\r\n        }\r\n    }\r\n}"]}