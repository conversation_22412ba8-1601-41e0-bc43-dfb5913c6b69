System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, i18n, TTFUtils, _decorator, Component, Label, RichText, warn, _dec, _dec2, _dec3, _dec4, _class, _class2, _descriptor, _descriptor2, _crd, ccclass, property, executeInEditMode, LocalizedLabel;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfTTFUtils(extras) {
    _reporterNs.report("TTFUtils", "./TTFUtils", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Label = _cc.Label;
      RichText = _cc.RichText;
      warn = _cc.warn;
    }, function (_unresolved_2) {
      i18n = _unresolved_2;
    }, function (_unresolved_3) {
      TTFUtils = _unresolved_3.TTFUtils;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "edf98QH2D1AUoCrtQlbxFrU", "LocalizedLabel", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Label', 'RichText', 'warn']);

      ({
        ccclass,
        property,
        executeInEditMode
      } = _decorator);

      _export("LocalizedLabel", LocalizedLabel = (_dec = ccclass('LocalizedLabel'), _dec2 = property({
        visible: false
      }), _dec3 = property({
        displayName: 'Key',
        visible: true
      }), _dec4 = property({
        type: (_crd && TTFUtils === void 0 ? (_reportPossibleCrUseOfTTFUtils({
          error: Error()
        }), TTFUtils) : TTFUtils).CUSTOM_TYPE,
        tooltip: "加载字体类型"
      }), _dec(_class = executeInEditMode(_class = (_class2 = class LocalizedLabel extends Component {
        constructor(...args) {
          super(...args);
          this.label = null;

          _initializerDefineProperty(this, "key", _descriptor, this);

          _initializerDefineProperty(this, "customType", _descriptor2, this);
        }

        get _key() {
          return this.key;
        }

        set _key(str) {
          this.updateLabel();
          this.key = str;
        }

        onLoad() {
          if (!i18n.ready) {
            i18n.init('zh');
          }

          this.fetchRender();
        }

        start() {
          let lab;
          lab = this.getComponent(Label);

          if (!lab) {
            lab = this.getComponent(RichText);
          }

          if (lab) {
            if (this.customType === (_crd && TTFUtils === void 0 ? (_reportPossibleCrUseOfTTFUtils({
              error: Error()
            }), TTFUtils) : TTFUtils).CUSTOM_TYPE.NUMBER || this.customType === (_crd && TTFUtils === void 0 ? (_reportPossibleCrUseOfTTFUtils({
              error: Error()
            }), TTFUtils) : TTFUtils).CUSTOM_TYPE.TEXT) {
              lab.useSystemFont = false;
              (_crd && TTFUtils === void 0 ? (_reportPossibleCrUseOfTTFUtils({
                error: Error()
              }), TTFUtils) : TTFUtils).getInstance().setCustomTTF(lab, this.customType);
            } else {
              lab.useSystemFont = true;
              lab.fontFamily = "Arial";
            }
          } else {
            warn("TTFComponent: ", this.node.name, " 节点没有Label 或 RichText组件！！！");
          }
        }

        fetchRender() {
          let lab;
          lab = this.getComponent(Label);

          if (!lab) {
            lab = this.getComponent(RichText);
          }

          if (lab) {
            this.label = lab;
            this.updateLabel();
            return;
          }
        }

        updateLabel() {
          if (this.key === '') {
            return;
          }

          this.label && (this.label.string = i18n.getI18StrByKey(this.key));
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "key", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return '';
        }
      }), _applyDecoratedDescriptor(_class2.prototype, "_key", [_dec3], Object.getOwnPropertyDescriptor(_class2.prototype, "_key"), _class2.prototype), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "customType", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return (_crd && TTFUtils === void 0 ? (_reportPossibleCrUseOfTTFUtils({
            error: Error()
          }), TTFUtils) : TTFUtils).CUSTOM_TYPE.TEXT;
        }
      })), _class2)) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=43db00c3c4fd1f269121d5fdd57302f4949c5953.js.map