{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "f9edb3df-98a8-47a8-bee6-da581acdabf4", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "f9edb3df-98a8-47a8-bee6-da581acdabf4@6c48a", "displayName": "warehouse_btn", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "isUuid": true, "imageUuidOrDatabaseUri": "f9edb3df-98a8-47a8-bee6-da581acdabf4", "visible": false}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "f9edb3df-98a8-47a8-bee6-da581acdabf4@f9941", "displayName": "warehouse_btn", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 1, "trimX": 0, "trimY": 0, "width": 94, "height": 127, "rawWidth": 94, "rawHeight": 129, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-47, -63.5, 0, 47, -63.5, 0, -47, 63.5, 0, 47, 63.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 129, 94, 129, 0, 2, 94, 2], "nuv": [0, 0.015503875968992248, 1, 0.015503875968992248, 0, 1, 1, 1], "minPos": [-47, -63.5, 0], "maxPos": [47, 63.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "f9edb3df-98a8-47a8-bee6-da581acdabf4@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "f9edb3df-98a8-47a8-bee6-da581acdabf4@6c48a"}}