{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/messagebox/MessageBox.ts"], "names": ["MessageBox", "DEBUG", "csproto", "MarqueeUI", "PopupUI", "ToastUI", "UIMgr", "ShowNextPopup", "showingPopup", "contentPopup", "length", "content", "shift", "onConfirm", "confirmCallbacks", "onCancel", "cancelCallbacks", "openUI", "ui", "get", "toClose", "closePopup", "bind", "closeUI", "confirm", "cancelHandler", "show", "push", "marquee", "contentMarquee", "ShowNextMarquee", "showingMarquee", "closeMarquee", "toast", "testShow", "testToast", "errorCode", "ret_code", "comm", "RET_CODE"], "mappings": ";;;kFAUaA,U;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAVJC,MAAAA,K,UAAAA,K;;AACFC,MAAAA,O;;AACEC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,K,iBAAAA,K;;;;;;;AAET;4BAGaN,U,GAAN,MAAMA,UAAN,CAAiB;AAK0C;AAE9B,eAAbO,aAAa,GAAG;AAAA;;AAAA;AACnC,gBAAI,KAAI,CAACC,YAAL,IAAqB,KAAI,CAACC,YAAL,CAAkBC,MAAlB,KAA6B,CAAtD,EAAyD;AACvD;AACD;;AACD,YAAA,KAAI,CAACF,YAAL,GAAoB,IAApB;;AACA,gBAAMG,OAAO,GAAG,KAAI,CAACF,YAAL,CAAkBG,KAAlB,EAAhB;;AACA,gBAAMC,SAAS,GAAG,KAAI,CAACC,gBAAL,CAAsBF,KAAtB,EAAlB;;AACA,gBAAMG,QAAQ,GAAG,KAAI,CAACC,eAAL,CAAqBJ,KAArB,EAAjB,CAPmC,CAQnC;;;AACA,kBAAM;AAAA;AAAA,gCAAMK,MAAN;AAAA;AAAA,oCAAsBN,OAAtB,EAA+BE,SAA/B,EAA0CE,QAA1C,CAAN;AACA,gBAAIG,EAAE,GAAG;AAAA;AAAA,gCAAMC,GAAN;AAAA;AAAA,mCAAT;;AACA,gBAAID,EAAJ,EAAQ;AACNA,cAAAA,EAAE,CAACE,OAAH,GAAa,KAAI,CAACC,UAAL,CAAgBC,IAAhB,CAAqB,KAArB,CAAb;AACD;AAbkC;AAcpC;;AAE8B,eAAVD,UAAU,GAAG;AAAA;;AAAA;AAChC,kBAAM;AAAA;AAAA,gCAAME,OAAN;AAAA;AAAA,mCAAN;AACA,YAAA,MAAI,CAACf,YAAL,GAAoB,KAApB;;AACA,YAAA,MAAI,CAACD,aAAL;AAHgC;AAIjC,SA3BqB,CA6BtB;;;AACqB,eAAPiB,OAAO,CAACb,OAAD,EAAkBE,SAAlB,EAAuCE,QAAvC,EAA4D;AAC/E,cAAMU,aAAa,GAAGV,QAAQ,KAAK,MAAM,CAAG,CAAd,CAA9B;;AACA,eAAKW,IAAL,CAAUf,OAAV,EAAmBE,SAAnB,EAA8BY,aAA9B;AACD,SAjCqB,CAmCtB;;;AACkB,eAAJC,IAAI,CAACf,OAAD,EAAkBE,SAAlB,EAAwCE,QAAxC,EAAmE;AACnF,eAAKN,YAAL,CAAkBkB,IAAlB,CAAuBhB,OAAvB;AACA,eAAKG,gBAAL,CAAsBa,IAAtB,CAA2Bd,SAA3B;AACA,eAAKG,eAAL,CAAqBW,IAArB,CAA0BZ,QAA1B;AACA,eAAKR,aAAL;AACD;;AAG6C;AACzB,eAAPqB,OAAO,CAACjB,OAAD,EAAkB;AACrC,eAAKkB,cAAL,CAAoBF,IAApB,CAAyBhB,OAAzB;AACA,eAAKmB,eAAL;AACD;;AACmC,eAAfA,eAAe,GAAG;AAAA;;AAAA;AACrC,gBAAI,MAAI,CAACC,cAAL,IAAuB,MAAI,CAACF,cAAL,CAAoBnB,MAApB,KAA+B,CAA1D,EAA6D;AAC3D;AACD;;AACD,YAAA,MAAI,CAACqB,cAAL,GAAsB,IAAtB;;AACA,gBAAMpB,OAAO,GAAG,MAAI,CAACkB,cAAL,CAAoBjB,KAApB,EAAhB,CALqC,CAMrC;;;AACA,kBAAM;AAAA;AAAA,gCAAMK,MAAN;AAAA;AAAA,wCAAwBN,OAAxB,CAAN;AACA,gBAAIO,EAAE,GAAG;AAAA;AAAA,gCAAMC,GAAN;AAAA;AAAA,uCAAT;;AACA,gBAAID,EAAJ,EAAQ;AACNA,cAAAA,EAAE,CAACE,OAAH,GAAa,MAAI,CAACY,YAAL,CAAkBV,IAAlB,CAAuB,MAAvB,CAAb;AACD;AAXoC;AAYtC;;AAEgC,eAAZU,YAAY,GAAG;AAAA;;AAAA;AAClC,kBAAM;AAAA;AAAA,gCAAMT,OAAN;AAAA;AAAA,uCAAN;AACA,YAAA,MAAI,CAACQ,cAAL,GAAsB,KAAtB;;AACA,YAAA,MAAI,CAACD,eAAL;AAHkC;AAInC;;AAEkB,eAALG,KAAK,CAACtB,OAAD,EAAkB;AACnC;AAAA;AAAA,8BAAMM,MAAN;AAAA;AAAA,kCAAsBN,OAAtB;AACD,SAvEqB,CAwEtB;;;AACsB,eAARuB,QAAQ,CAACvB,OAAD,EAAkB;AACtC,cAAIV,KAAJ,EAAW;AACT,iBAAKyB,IAAL,CAAUf,OAAV;AACD;AACF;;AACsB,eAATwB,SAAS,CAACxB,OAAD,EAAkB;AACvC,cAAIV,KAAJ,EAAW;AACT,iBAAKgC,KAAL,CAAWtB,OAAX;AACD;AACF;;AACsB,eAATyB,SAAS,CAACC,QAAD,EAAkC;AACvD,cAAIpC,KAAJ,EAAW;AACT,iBAAKyB,IAAL,CAAU,WAAWW,QAAX,GAAuB,IAAvB,GAA8B;AAAA;AAAA,oCAAQC,IAAR,CAAaC,QAAb,CAAsBF,QAAtB,CAAxC;AACD;AACF;;AAvFqB,O;;AAAXrC,MAAAA,U,CAEIQ,Y,GAAwB,K;AAF5BR,MAAAA,U,CAGIS,Y,GAAyB,E;AAAI;AAHjCT,MAAAA,U,CAIIc,gB,GAA6C,E;AAAI;AAJrDd,MAAAA,U,CAKIgB,e,GAA4C,E;AALhDhB,MAAAA,U,CA2CI+B,c,GAA0B,K;AA3C9B/B,MAAAA,U,CA4CI6B,c,GAA2B,E", "sourcesContent": ["import { DEBUG } from \"cc/env\";\nimport csproto from 'db://assets/bundles/common/script/autogen/pb/cs_proto.js';\nimport { MarqueeUI } from \"db://assets/bundles/common/script/ui/common/MarqueeUI\";\nimport { PopupUI } from \"db://assets/bundles/common/script/ui/common/PopupUI\";\nimport { ToastUI } from \"db://assets/bundles/common/script/ui/common/ToastUI\";\nimport { UIMgr } from \"db://assets/scripts/core/base/UIMgr\";\n\n// 定义回调函数类型\ntype Callback = () => void;\n\nexport class MessageBox {\n\n  private static showingPopup: boolean = false;\n  private static contentPopup: string[] = []; // 确认回调数组\n  private static confirmCallbacks: (Callback | undefined)[] = []; // 确认回调数组\n  private static cancelCallbacks: (Callback | undefined)[] = [];  // 取消回调数组\n\n  private static async ShowNextPopup() {\n    if (this.showingPopup || this.contentPopup.length === 0) {\n      return;\n    }\n    this.showingPopup = true;\n    const content = this.contentPopup.shift();\n    const onConfirm = this.confirmCallbacks.shift();\n    const onCancel = this.cancelCallbacks.shift();\n    // 统一调用 UIMgr\n    await UIMgr.openUI(PopupUI, content, onConfirm, onCancel);\n    let ui = UIMgr.get(PopupUI);\n    if (ui) {\n      ui.toClose = this.closePopup.bind(this);\n    }\n  }\n\n  private static async closePopup() {\n    await UIMgr.closeUI(PopupUI);\n    this.showingPopup = false;\n    this.ShowNextPopup();\n  }\n\n  // 强制显示确认+取消按钮，不需要取消函数回调，不传onCancel\n  public static confirm(content: string, onConfirm: Callback, onCancel?: Callback) {\n    const cancelHandler = onCancel || (() => { });\n    this.show(content, onConfirm, cancelHandler);\n  }\n\n  // 只显示确认按钮，调用这个\n  public static show(content: string, onConfirm?: Callback, onCancel?: Callback): void {\n    this.contentPopup.push(content);\n    this.confirmCallbacks.push(onConfirm);\n    this.cancelCallbacks.push(onCancel);\n    this.ShowNextPopup();\n  }\n\n  private static showingMarquee: boolean = false;\n  private static contentMarquee: string[] = []; // 确认回调数组\n  public static marquee(content: string) {\n    this.contentMarquee.push(content);\n    this.ShowNextMarquee();\n  }\n  private static async ShowNextMarquee() {\n    if (this.showingMarquee || this.contentMarquee.length === 0) {\n      return;\n    }\n    this.showingMarquee = true;\n    const content = this.contentMarquee.shift();\n    // 统一调用 UIMgr\n    await UIMgr.openUI(MarqueeUI, content);\n    let ui = UIMgr.get(MarqueeUI);\n    if (ui) {\n      ui.toClose = this.closeMarquee.bind(this);\n    }\n  }\n\n  private static async closeMarquee() {\n    await UIMgr.closeUI(MarqueeUI);\n    this.showingMarquee = false;\n    this.ShowNextMarquee();\n  }\n\n  public static toast(content: string) {\n    UIMgr.openUI(ToastUI, content);\n  }\n  //DEBUG\n  public static testShow(content: string) {\n    if (DEBUG) {\n      this.show(content);\n    }\n  }\n  public static testToast(content: string) {\n    if (DEBUG) {\n      this.toast(content);\n    }\n  }\n  public static errorCode(ret_code: csproto.comm.RET_CODE) {\n    if (DEBUG) {\n      this.show(\"错误码：\\n\" + ret_code! + \"\\n\" + csproto.comm.RET_CODE[ret_code!]);\n    }\n  }\n}\n"]}