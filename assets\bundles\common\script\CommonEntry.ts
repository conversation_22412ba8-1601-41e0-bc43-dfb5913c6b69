import { _decorator } from "cc";
import { MyApp } from "db://assets/bundles/common/script/app/MyApp";
import { BundleName } from "db://assets/bundles/common/script/const/BundleConst";
import { IBundleEntry } from "db://assets/scripts/core/base/Bundle";
import { ResManager } from "db://assets/scripts/core/base/ResManager";
import { UIMgr } from "db://assets/scripts/core/base/UIMgr";
import { logDebug } from "db://assets/scripts/utils/Logger";
import { EventMgr } from "./event/EventManager";
import { HomeUIEvent } from "./event/HomeUIEvent";
import { FriendUI } from "./ui/friend/FriendUI";
import { DevLoginUI } from "./ui/gameui/DevLoginUI";
import { BottomTab } from "./ui/home/<USER>";
import { BottomUI } from "./ui/home/<USER>";
import { HomeUI } from "./ui/home/<USER>";
import { TopUI } from "./ui/home/<USER>";
import { MailUI } from "./ui/mail/MailUI";
import { PKUI } from "./ui/pk/PKUI";
import { PlaneUI } from "./ui/plane/PlaneUI";
import { ShopUI } from "./ui/shop/ShopUI";
import { SkyIslandUI } from "./ui/skyisland/SkyIslandUI";
import { StoryUI } from "./ui/story/StoryUI";
import { TalentUI } from "./ui/talent/TalentUI";
import { TTFUtils } from "./utils/TTFUtils";
const { ccclass } = _decorator;
@ccclass('CommonEntry')
export class CommonEntry extends IBundleEntry {
    public async initEntry(...args: any[]): Promise<void> {
        logDebug("CommonEntry", "initEntry")
        MyApp.GetInstance().init();
        TTFUtils.getInstance().init(TTFUtils.CUSTOM_TYPE.TEXT,"font/gamefont")
        await MyApp.planeMgr.load();
        await ResManager.instance.loadBundle(BundleName.Luban) //优先加载完配置
        await MyApp.lubanMgr.load();
        await UIMgr.loadUI(DevLoginUI)
        await MyApp.resMgr.loadBundle(BundleName.Home)
        await UIMgr.openUI(HomeUI)
        await UIMgr.openUI(BottomUI)
        await UIMgr.openUI(TopUI)
        //暂时这样 后面再优化
        EventMgr.on(HomeUIEvent.Leave, () => {
            UIMgr.closeUI(HomeUI)
            UIMgr.closeUI(BottomUI) //这里会把下面的主界面都关闭  
            UIMgr.closeUI(TopUI)
        })
        EventMgr.emit(HomeUIEvent.BottomTabRegister, BottomTab.Home, UIMgr.get(HomeUI))
        this.preloadHomeSubBundles();
    }

    private preloadHomeSubBundles() {
        //异步加载其他bundle
        MyApp.resMgr.loadBundle(BundleName.HomePlane).then(async () => {
            await UIMgr.loadUI(PlaneUI)
            EventMgr.emit(HomeUIEvent.BottomTabRegister, BottomTab.Plane, UIMgr.get(PlaneUI))
        })
        MyApp.resMgr.loadBundle(BundleName.HomeTalent).then(async () => {
            await UIMgr.loadUI(TalentUI)
            EventMgr.emit(HomeUIEvent.BottomTabRegister, BottomTab.Talent, UIMgr.get(TalentUI))
        })
        MyApp.resMgr.loadBundle(BundleName.HomeShop).then(async () => {
            await UIMgr.loadUI(ShopUI)
            EventMgr.emit(HomeUIEvent.BottomTabRegister, BottomTab.Shop, UIMgr.get(ShopUI))
        })
        MyApp.resMgr.loadBundle(BundleName.HomeSkyIsland).then(async () => {
            await UIMgr.loadUI(SkyIslandUI)
            EventMgr.emit(HomeUIEvent.BottomTabRegister, BottomTab.SkyIsLand, UIMgr.get(SkyIslandUI))
        })
        MyApp.resMgr.loadBundle(BundleName.HomeFriend).then(async () => {
            await UIMgr.loadUI(FriendUI)
        })
        MyApp.resMgr.loadBundle(BundleName.HomeMail).then(async () => {
            await UIMgr.loadUI(MailUI)
        })
        MyApp.resMgr.loadBundle(BundleName.HomePK).then(async () => {
            await UIMgr.loadUI(PKUI)
        })
        MyApp.resMgr.loadBundle(BundleName.HomeStory).then(async () => {
            await UIMgr.loadUI(StoryUI)
        })
    }
}