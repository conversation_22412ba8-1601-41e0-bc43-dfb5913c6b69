System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, CCFloat, Component, Vec3, view, logInfo, GameConst, GameIns, GameEnum, _dec, _dec2, _dec3, _class, _class2, _descriptor, _descriptor2, _crd, ccclass, property, CameraMove;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOflogInfo(extras) {
    _reporterNs.report("logInfo", "db://assets/scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../../../../../scripts/core/base/GameConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEnum(extras) {
    _reporterNs.report("GameEnum", "../const/GameEnum", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      CCFloat = _cc.CCFloat;
      Component = _cc.Component;
      Vec3 = _cc.Vec3;
      view = _cc.view;
    }, function (_unresolved_2) {
      logInfo = _unresolved_2.logInfo;
    }, function (_unresolved_3) {
      GameConst = _unresolved_3.GameConst;
    }, function (_unresolved_4) {
      GameIns = _unresolved_4.GameIns;
    }, function (_unresolved_5) {
      GameEnum = _unresolved_5.GameEnum;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "bf258c+BlNO24ox3moFQ/T+", "CameraMove", undefined);

      __checkObsolete__(['_decorator', 'CCFloat', 'Component', 'Vec3', 'view']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("CameraMove", CameraMove = (_dec = ccclass('CameraMove'), _dec2 = property(CCFloat), _dec3 = property(CCFloat), _dec(_class = (_class2 = class CameraMove extends Component {
        constructor(...args) {
          super(...args);

          /* 参数组合效果示例
           * 场景	    followFactor  maxMoveSpeed  摄像机行为
           * 平滑跟随	0.3	          100	        平滑移动，不会突然加速
           * 快速响应	0.7	          200	        快速接近目标，但不会跳跃
           * 精确控制	0.5	          80	        平衡响应速度和平滑度
           * 电影效果	0.2	          50	        非常平滑的移动，适合过场动画
          */
          _initializerDefineProperty(this, "followFactor", _descriptor, this);

          // 跟随比例因子 (0.0-1.0)
          _initializerDefineProperty(this, "maxMoveSpeed", _descriptor2, this);

          // 摄像机最大移动速度（单位：世界单位/秒）
          this._screenRatio = 1;
          this._battleWidth = 0;
          this._currentPosition = new Vec3();
          // 当前位置
          this._targetPosition = new Vec3();
        }

        // 目标位置
        start() {
          // 初始化位置
          this.node.getPosition(this._currentPosition);

          this._targetPosition.set(this._currentPosition);

          this._calculateScreenRatio();
        }

        update(deltaTime) {
          if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameStateManager.gameState != (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameState.Battle) {
            return; // 游戏未进入战斗状态时不处理
          }

          let mainPlane = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainPlane;
          if (!mainPlane) return; // 获取飞机位置

          const planePos = mainPlane.node.position; // 计算摄像机应该跟随的目标位置

          const targetX = this.calculateTargetPosition(planePos.x);

          this._targetPosition.set(this._currentPosition);

          this._targetPosition.x = targetX; // 平滑移动摄像机（考虑最大移动速度）

          this.smoothMoveCamera(deltaTime);
        }
        /**
         * 平滑移动摄像机（考虑最大移动速度）
         * @param deltaTime 帧时间（秒）
         */


        smoothMoveCamera(deltaTime) {
          // 获取当前位置
          this.node.getPosition(this._currentPosition); // 计算当前位置到目标位置的距离

          const distanceToTarget = this._targetPosition.x - this._currentPosition.x; // 计算期望移动距离（基于平滑因子）

          const desiredMove = distanceToTarget * this.followFactor; // 计算最大允许移动距离（基于最大速度）

          const maxMove = this.maxMoveSpeed * deltaTime; // 应用移动速度限制

          let actualMove = desiredMove;

          if (Math.abs(desiredMove) > maxMove) {
            actualMove = Math.sign(desiredMove) * maxMove;
          } // 计算新位置


          const newX = this._currentPosition.x + actualMove; // 设置新位置（只修改X轴）

          this._currentPosition.x = newX;
          this.node.setPosition(this._currentPosition);
        }
        /**
         * 计算摄像机目标位置
         * @param planeX 飞机X坐标
         * @returns 摄像机目标X坐标
         */


        calculateTargetPosition(planeX) {
          // 计算飞机在战斗区域中的位置比例
          const relativeX = planeX - -this._battleWidth / 2; // 左边界为 -battleWidth/2
          // 计算摄像机偏移比例 (0.0 到 1.0)

          let cameraRatio = relativeX / this._battleWidth; // 限制在 0.0 到 1.0 范围内

          cameraRatio = Math.max(0.0, Math.min(1.0, cameraRatio)); // 返回摄像机目标位置（-100% 到 100%）

          return cameraRatio * 200 - 100;
        }
        /**
         * 设置摄像机位置
         * @param targetX 目标X坐标
         * @param planeX 飞机X坐标（用于日志记录）
         */


        setCameraPosition(targetX, planeX) {
          // 获取当前位置
          this.node.getPosition(this._currentPosition); // 计算新位置（按比例平滑移动）

          const newX = this._currentPosition.x * (1 - this.followFactor) + targetX * this.followFactor; // 设置新位置（只修改X轴）

          this._currentPosition.x = newX;
          this.node.setPosition(this._currentPosition); // logInfo('CameraMove', `摄像机当前位置: ${this._currentPosition.x} name: ${this.node.name}`);
          // // 调试日志（现在可以正确使用 planeX）
          // logInfo('CameraMove', ` 飞机位置:${planeX}, 目标位置:${targetX}, 新位置:${newX}`);
        }
        /**
         * 强制移动摄像机到目标位置
         */


        forceMoveToTarget() {
          this.node.setPosition(this._targetPosition);

          this._currentPosition.set(this._targetPosition);
        }
        /**
         * 重置摄像机状态
         */


        resetCamera() {
          // 重置位置到初始状态
          this.node.setPosition(0, 0, 0);

          this._currentPosition.set(0, 0, 0);

          this._targetPosition.set(0, 0, 0);
        }
        /**
         * 计算屏幕适配比例
         */


        _calculateScreenRatio() {
          const visibleSize = view.getVisibleSize();
          this._screenRatio = visibleSize.width / (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).designWidth; // 计算实际战斗宽度（考虑宽屏适配）

          this._battleWidth = (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).ViewBattleWidth * this._screenRatio;
          (_crd && logInfo === void 0 ? (_reportPossibleCrUseOflogInfo({
            error: Error()
          }), logInfo) : logInfo)('CameraMove', `屏幕比例: ${this._screenRatio}, 战斗宽度: ${this._battleWidth}`);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "followFactor", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0.7;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "maxMoveSpeed", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 200;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=593c4ca226a05759dc333b45e98a646f861cf82d.js.map