System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9", "__unresolved_10", "__unresolved_11", "__unresolved_12", "__unresolved_13"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Label, ProgressBar, csproto, BaseUI, UILayer, UIMgr, logError, MyApp, ResTaskClass, BundleName, DataMgr, BottomUIEvent, DataEvent, EventMgr, ButtonPlus, BottomTab, TaskUI, _dec, _dec2, _dec3, _dec4, _dec5, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _crd, ccclass, property, TaskTipUI;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfcsproto(extras) {
    _reporterNs.report("csproto", "db://assets/bundles/common/script/autogen/pb/cs_proto.js", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBaseUI(extras) {
    _reporterNs.report("BaseUI", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUILayer(extras) {
    _reporterNs.report("UILayer", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogError(extras) {
    _reporterNs.report("logError", "db://assets/scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "../../app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfResTaskClass(extras) {
    _reporterNs.report("ResTaskClass", "../../autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBundleName(extras) {
    _reporterNs.report("BundleName", "../../const/BundleConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDataMgr(extras) {
    _reporterNs.report("DataMgr", "../../data/DataManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBottomUIEvent(extras) {
    _reporterNs.report("BottomUIEvent", "../../event/BottomUIEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDataEvent(extras) {
    _reporterNs.report("DataEvent", "../../event/DataEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventMgr(extras) {
    _reporterNs.report("EventMgr", "../../event/EventManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfButtonPlus(extras) {
    _reporterNs.report("ButtonPlus", "../common/components/button/ButtonPlus", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBottomTab(extras) {
    _reporterNs.report("BottomTab", "../home/<USER>", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTaskUI(extras) {
    _reporterNs.report("TaskUI", "./TaskUI", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Label = _cc.Label;
      ProgressBar = _cc.ProgressBar;
    }, function (_unresolved_2) {
      csproto = _unresolved_2.default;
    }, function (_unresolved_3) {
      BaseUI = _unresolved_3.BaseUI;
      UILayer = _unresolved_3.UILayer;
      UIMgr = _unresolved_3.UIMgr;
    }, function (_unresolved_4) {
      logError = _unresolved_4.logError;
    }, function (_unresolved_5) {
      MyApp = _unresolved_5.MyApp;
    }, function (_unresolved_6) {
      ResTaskClass = _unresolved_6.ResTaskClass;
    }, function (_unresolved_7) {
      BundleName = _unresolved_7.BundleName;
    }, function (_unresolved_8) {
      DataMgr = _unresolved_8.DataMgr;
    }, function (_unresolved_9) {
      BottomUIEvent = _unresolved_9.BottomUIEvent;
    }, function (_unresolved_10) {
      DataEvent = _unresolved_10.DataEvent;
    }, function (_unresolved_11) {
      EventMgr = _unresolved_11.EventMgr;
    }, function (_unresolved_12) {
      ButtonPlus = _unresolved_12.ButtonPlus;
    }, function (_unresolved_13) {
      BottomTab = _unresolved_13.BottomTab;
    }, function (_unresolved_14) {
      TaskUI = _unresolved_14.TaskUI;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "70f5fbGegVEy4MRLJsED8LR", "TaskTipUI", undefined);

      __checkObsolete__(['_decorator', 'Label', 'ProgressBar']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("TaskTipUI", TaskTipUI = (_dec = ccclass('TaskTipUI'), _dec2 = property(Label), _dec3 = property(ProgressBar), _dec4 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec5 = property(Label), _dec(_class = (_class2 = class TaskTipUI extends (_crd && BaseUI === void 0 ? (_reportPossibleCrUseOfBaseUI({
        error: Error()
      }), BaseUI) : BaseUI) {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "taskDesc", _descriptor, this);

          _initializerDefineProperty(this, "taskProgress", _descriptor2, this);

          _initializerDefineProperty(this, "taskJumpBtn", _descriptor3, this);

          _initializerDefineProperty(this, "progressDesc", _descriptor4, this);

          this._isHomeUIHide = false;
        }

        static getUrl() {
          return "prefab/ui/TaskTipUI";
        }

        static getLayer() {
          return (_crd && UILayer === void 0 ? (_reportPossibleCrUseOfUILayer({
            error: Error()
          }), UILayer) : UILayer).Background;
        }

        static getBundleName() {
          return (_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).HomeTask;
        }

        onLoad() {
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).on((_crd && DataEvent === void 0 ? (_reportPossibleCrUseOfDataEvent({
            error: Error()
          }), DataEvent) : DataEvent).TaskRefresh, this.onTaskRefresh, this);
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).on((_crd && BottomUIEvent === void 0 ? (_reportPossibleCrUseOfBottomUIEvent({
            error: Error()
          }), BottomUIEvent) : BottomUIEvent).SwitchPanel, this.onSwitchPanel, this);
          this.taskJumpBtn.addClick(this.onTaskJump, this);
        }

        async onShow() {
          this.onTaskRefresh((_crd && ResTaskClass === void 0 ? (_reportPossibleCrUseOfResTaskClass({
            error: Error()
          }), ResTaskClass) : ResTaskClass).DAILY_TASK);
        }

        async onHide(_isHomeUIHide = false) {
          this._isHomeUIHide = _isHomeUIHide;
        }

        async onClose() {
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).targetOff(this);
        }

        onSwitchPanel(from, to) {
          if (to !== (_crd && BottomTab === void 0 ? (_reportPossibleCrUseOfBottomTab({
            error: Error()
          }), BottomTab) : BottomTab).Home) {
            this.node.active = false;
            return;
          }

          if (this.node.active) return;
          this.node.active = true;
        }

        onTaskRefresh(taskClass) {
          if (taskClass !== (_crd && ResTaskClass === void 0 ? (_reportPossibleCrUseOfResTaskClass({
            error: Error()
          }), ResTaskClass) : ResTaskClass).DAILY_TASK || this._isHomeUIHide) {
            return;
          }

          const taskList = (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).task.getTaskListByClass(taskClass).filter(t => t.status === (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).comm.TASK_STATUS.TASK_STATUS_NORMAL).sort((a, b) => b.task_id - a.task_id);

          if (taskList.length == 0) {
            this.node.active = false;
            return;
          }

          const taskInfo = taskList[0];
          const resTask = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanTables.TbResTask.get(taskInfo.task_id);

          if (!resTask) {
            (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
              error: Error()
            }), logError) : logError)("TaskTipUI", `task id ${taskInfo.task_id} not found`);
            return;
          }

          this.node.active = true;
          const info = (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).task.getTaskDescAndProgress(resTask);
          this.taskDesc.string = info.desc;
          this.taskProgress.progress = taskInfo.progress / info.progressMax;
          this.progressDesc.string = `${taskInfo.progress}/${info.progressMax}`;
        }

        onTaskJump() {
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && TaskUI === void 0 ? (_reportPossibleCrUseOfTaskUI({
            error: Error()
          }), TaskUI) : TaskUI);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "taskDesc", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "taskProgress", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "taskJumpBtn", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "progressDesc", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=21be59894c688ba332bf4357b62cd46bc0a6aaa4.js.map