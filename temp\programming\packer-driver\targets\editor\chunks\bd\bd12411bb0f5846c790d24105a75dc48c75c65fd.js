System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, _crd, BottomUIEvent;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "3556bwTcZlOTZRozQhg9/GF", "BottomUIEvent", undefined);

      _export("BottomUIEvent", BottomUIEvent = {
        SwitchPanel: "BottomUIEvent_SwitchPanel"
      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=bd12411bb0f5846c790d24105a75dc48c75c65fd.js.map