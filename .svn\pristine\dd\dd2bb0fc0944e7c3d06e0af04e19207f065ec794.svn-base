<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>HP.png</key>
            <dict>
                <key>frame</key>
                <string>{{378,53},{54,31}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{54,31}}</string>
                <key>sourceSize</key>
                <string>{54,31}</string>
            </dict>
            <key>bg_num.png</key>
            <dict>
                <key>frame</key>
                <string>{{580,79},{32,32}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{32,32}}</string>
                <key>sourceSize</key>
                <string>{32,32}</string>
            </dict>
            <key>boss.png</key>
            <dict>
                <key>frame</key>
                <string>{{329,53},{46,47}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{46,47}}</string>
                <key>sourceSize</key>
                <string>{46,47}</string>
            </dict>
            <key>boss_hp.png</key>
            <dict>
                <key>frame</key>
                <string>{{1,33},{413,18}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{413,18}}</string>
                <key>sourceSize</key>
                <string>{413,18}</string>
            </dict>
            <key>boss_hp2.png</key>
            <dict>
                <key>frame</key>
                <string>{{266,53},{44,61}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{44,61}}</string>
                <key>sourceSize</key>
                <string>{44,61}</string>
            </dict>
            <key>boss_hp_di.png</key>
            <dict>
                <key>frame</key>
                <string>{{1,1},{427,30}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{427,30}}</string>
                <key>sourceSize</key>
                <string>{427,30}</string>
            </dict>
            <key>boss_hp_di2.png</key>
            <dict>
                <key>frame</key>
                <string>{{591,28},{44,61}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{44,61}}</string>
                <key>sourceSize</key>
                <string>{44,61}</string>
            </dict>
            <key>hp_bar.png</key>
            <dict>
                <key>frame</key>
                <string>{{1,91},{247,20}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{247,20}}</string>
                <key>sourceSize</key>
                <string>{247,20}</string>
            </dict>
            <key>hp_bar_bg.png</key>
            <dict>
                <key>frame</key>
                <string>{{1,53},{263,36}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{263,36}}</string>
                <key>sourceSize</key>
                <string>{263,36}</string>
            </dict>
            <key>plane_state.png</key>
            <dict>
                <key>frame</key>
                <string>{{532,79},{45,46}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{45,46}}</string>
                <key>sourceSize</key>
                <string>{45,46}</string>
            </dict>
            <key>prgress.png</key>
            <dict>
                <key>frame</key>
                <string>{{1,113},{234,11}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{13,5},{234,11}}</string>
                <key>sourceSize</key>
                <string>{260,21}</string>
            </dict>
            <key>prgress_bg.png</key>
            <dict>
                <key>frame</key>
                <string>{{430,1},{246,25}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{246,25}}</string>
                <key>sourceSize</key>
                <string>{246,25}</string>
            </dict>
            <key>score.png</key>
            <dict>
                <key>frame</key>
                <string>{{532,28},{57,49}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{57,49}}</string>
                <key>sourceSize</key>
                <string>{57,49}</string>
            </dict>
            <key>skill_bg.png</key>
            <dict>
                <key>frame</key>
                <string>{{678,1},{114,118}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{114,118}}</string>
                <key>sourceSize</key>
                <string>{114,118}</string>
            </dict>
            <key>skill_mask.png</key>
            <dict>
                <key>frame</key>
                <string>{{430,28},{100,95}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{100,95}}</string>
                <key>sourceSize</key>
                <string>{100,95}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>fight.png</string>
            <key>size</key>
            <string>{793,125}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:bf3d9ab26d14b55edc956d3b0bba14fd$</string>
            <key>textureFileName</key>
            <string>fight.png</string>
        </dict>
    </dict>
</plist>
