System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Label, Node, ProgressBar, RichText, Sprite, tween, BaseUI, UILayer, UIMgr, MyApp, BundleName, DataMgr, UITools, StateSprite, ButtonPlus, StatisticsUI, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _dec9, _dec10, _dec11, _dec12, _dec13, _dec14, _dec15, _dec16, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _descriptor8, _descriptor9, _descriptor10, _descriptor11, _descriptor12, _descriptor13, _descriptor14, _descriptor15, _crd, ccclass, property, SettlementUI;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfcsproto(extras) {
    _reporterNs.report("csproto", "db://assets/bundles/common/script/autogen/pb/cs_proto.js", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBaseUI(extras) {
    _reporterNs.report("BaseUI", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUILayer(extras) {
    _reporterNs.report("UILayer", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIOpt(extras) {
    _reporterNs.report("UIOpt", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "../../app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBundleName(extras) {
    _reporterNs.report("BundleName", "../../const/BundleConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDataMgr(extras) {
    _reporterNs.report("DataMgr", "../../data/DataManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUITools(extras) {
    _reporterNs.report("UITools", "../../game/utils/UITools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfStateSprite(extras) {
    _reporterNs.report("StateSprite", "./components/base/StateSprite", _context.meta, extras);
  }

  function _reportPossibleCrUseOfButtonPlus(extras) {
    _reporterNs.report("ButtonPlus", "./components/button/ButtonPlus", _context.meta, extras);
  }

  function _reportPossibleCrUseOfStatisticsUI(extras) {
    _reporterNs.report("StatisticsUI", "./StatisticsUI", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Label = _cc.Label;
      Node = _cc.Node;
      ProgressBar = _cc.ProgressBar;
      RichText = _cc.RichText;
      Sprite = _cc.Sprite;
      tween = _cc.tween;
    }, function (_unresolved_2) {
      BaseUI = _unresolved_2.BaseUI;
      UILayer = _unresolved_2.UILayer;
      UIMgr = _unresolved_2.UIMgr;
    }, function (_unresolved_3) {
      MyApp = _unresolved_3.MyApp;
    }, function (_unresolved_4) {
      BundleName = _unresolved_4.BundleName;
    }, function (_unresolved_5) {
      DataMgr = _unresolved_5.DataMgr;
    }, function (_unresolved_6) {
      UITools = _unresolved_6.UITools;
    }, function (_unresolved_7) {
      StateSprite = _unresolved_7.StateSprite;
    }, function (_unresolved_8) {
      ButtonPlus = _unresolved_8.ButtonPlus;
    }, function (_unresolved_9) {
      StatisticsUI = _unresolved_9.StatisticsUI;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "228a95JNwlDwqlaj+cCZoYb", "SettlementUI", undefined);

      __checkObsolete__(['_decorator', 'Label', 'Node', 'ProgressBar', 'RichText', 'Sprite', 'tween']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("SettlementUI", SettlementUI = (_dec = ccclass('SettlementUI'), _dec2 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec3 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec4 = property(Label), _dec5 = property(Label), _dec6 = property(Label), _dec7 = property(Label), _dec8 = property(ProgressBar), _dec9 = property(Label), _dec10 = property(Node), _dec11 = property(Node), _dec12 = property(Node), _dec13 = property(Node), _dec14 = property(RichText), _dec15 = property(Node), _dec16 = property(Label), _dec(_class = (_class2 = class SettlementUI extends (_crd && BaseUI === void 0 ? (_reportPossibleCrUseOfBaseUI({
        error: Error()
      }), BaseUI) : BaseUI) {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "btnOK", _descriptor, this);

          _initializerDefineProperty(this, "btnNext", _descriptor2, this);

          _initializerDefineProperty(this, "lblScore", _descriptor3, this);

          _initializerDefineProperty(this, "scoreAdd", _descriptor4, this);

          _initializerDefineProperty(this, "scoreHigh", _descriptor5, this);

          _initializerDefineProperty(this, "lblLevel", _descriptor6, this);

          _initializerDefineProperty(this, "expProBar", _descriptor7, this);

          _initializerDefineProperty(this, "lblExp", _descriptor8, this);

          _initializerDefineProperty(this, "nodeItem1", _descriptor9, this);

          _initializerDefineProperty(this, "nodeItem2", _descriptor10, this);

          _initializerDefineProperty(this, "nodeItem3", _descriptor11, this);

          _initializerDefineProperty(this, "nodeBestWeek", _descriptor12, this);

          _initializerDefineProperty(this, "passText", _descriptor13, this);

          _initializerDefineProperty(this, "nodePass", _descriptor14, this);

          _initializerDefineProperty(this, "lblTimeCost", _descriptor15, this);

          this.game_stats = [];
          this._scoreTween = void 0;
          // 分数动画的 tween 引用
          this._expTween = void 0;
        }

        // 经验条动画的 tween 引用
        static getUrl() {
          return "prefab/ui/SettlementUI";
        }

        static getLayer() {
          return (_crd && UILayer === void 0 ? (_reportPossibleCrUseOfUILayer({
            error: Error()
          }), UILayer) : UILayer).Top;
        }

        static getBundleName() {
          return (_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).Home;
        }

        static getUIOption() {
          return {
            isClickBgCloseUI: false
          };
        }

        onLoad() {
          this.btnOK.addClick(this.onOKClick, this);
          this.btnNext.addClick(this.onNextClick, this);
          this.scoreAdd.string = "分数加成 " + 123 + "%";
          this.lblScore.string = "0";
          this.scoreHigh.string = "历史最高分 " + 10000;
          const gap = 0.5; // 分数动画

          const score = 1000;
          this._scoreTween = tween({
            value: 0
          }).to(gap, {
            value: score
          }, {
            onUpdate: target => {
              if (!this.lblScore || target === undefined) return;
              this.lblScore.string = Math.round(target.value).toString();
            }
          }).start(); //  GM命令 //setattr xp 1000    

          let curLevel = (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).role.curLevel;
          let curExp = (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).role.curExp;
          let maxExp = (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).role.maxExp;
          let exp = curExp / maxExp;

          if (exp > 1) {
            exp = 1;
          }

          this.expProBar.progress = 0;

          if (maxExp > 0) {
            // 经验条动画
            this._expTween = tween(this.expProBar).to(gap, {
              progress: exp
            }, {
              onUpdate: () => {
                if (!this.expProBar) return;
                this.lblExp.string = `${Math.round(maxExp * this.expProBar.progress)}/${maxExp}`;
              }
            }).start();
          }

          this.lblLevel.string = "lv" + curLevel;
          this.setNodeItem(this.nodeItem1, 80000101, 100);
          this.setNodeItem(this.nodeItem2, 80100101, 0);
          this.setNodeItem(this.nodeItem3, 89999998, 200);
          this.nodePass.active = false;
        }

        async onOKClick() {
          // 停止所有动画
          if (this._scoreTween) this._scoreTween.stop();
          if (this._expTween) this._expTween.stop();
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).closeUI(SettlementUI);
        }

        async onNextClick() {
          // 停止所有动画
          if (this._scoreTween) this._scoreTween.stop();
          if (this._expTween) this._expTween.stop();
          await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && StatisticsUI === void 0 ? (_reportPossibleCrUseOfStatisticsUI({
            error: Error()
          }), StatisticsUI) : StatisticsUI);
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).closeUI(SettlementUI);
        }

        setNodeItem(node, itemID, limit) {
          var _quaSprite$getCompone;

          let res = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanTables.TbResItem.get(itemID);

          if (res === null) {
            return;
          }

          let nodeQuaIcon = node.getChildByName("ItemQuaIcon");
          let icon = nodeQuaIcon.getChildByName("icon");
          let quaSprite = nodeQuaIcon.getChildByName("QualityTypeSprite");
          quaSprite == null || (_quaSprite$getCompone = quaSprite.getComponent(_crd && StateSprite === void 0 ? (_reportPossibleCrUseOfStateSprite({
            error: Error()
          }), StateSprite) : StateSprite)) == null || _quaSprite$getCompone.setState(quaSprite.getComponent(Sprite), res.quality);
          let labelNum = node.getChildByName("LabelNum");
          (_crd && UITools === void 0 ? (_reportPossibleCrUseOfUITools({
            error: Error()
          }), UITools) : UITools).modifyNumber(labelNum.getComponent(Label), 111);
          let labelLimit = node.getChildByName("LabelLimit");

          if (limit === 0) {
            labelLimit.getComponent(Label).string = "";
          } else {
            (_crd && UITools === void 0 ? (_reportPossibleCrUseOfUITools({
              error: Error()
            }), UITools) : UITools).modifyNumber(labelLimit.getComponent(Label), limit);
          }

          const labelNumY = labelNum.y;
          const labelLimitY = labelLimit.y;
          const middleY = (labelNumY + labelLimitY) / 2;

          if (labelLimit.getComponent(Label).string || labelLimit.getComponent(Label).string.trim() === "") {
            labelNum.y = middleY;
          }
        }

        async onShow(result) {
          var _result$max_levels$to, _result$max_levels, _result$reward_items;

          if (result == null) return;
          (_crd && UITools === void 0 ? (_reportPossibleCrUseOfUITools({
            error: Error()
          }), UITools) : UITools).modifyColorTag(this.passText, 1, {
            value: (_result$max_levels$to = (_result$max_levels = result.max_levels) == null ? void 0 : _result$max_levels.toString()) != null ? _result$max_levels$to : "0"
          });
          this.nodePass.active = result.is_pass == 1;
          this.lblScore.string = result.total_score.toString();
          this.nodeBestWeek.active = result.is_week_bset == 1;
          (_crd && UITools === void 0 ? (_reportPossibleCrUseOfUITools({
            error: Error()
          }), UITools) : UITools).modifyNumber(this.scoreHigh, result.history_bese_score);
          this.lblTimeCost.string = "用时 " + (_crd && UITools === void 0 ? (_reportPossibleCrUseOfUITools({
            error: Error()
          }), UITools) : UITools).formatTime(result.total_time_cost);
          (_result$reward_items = result.reward_items) == null || _result$reward_items.forEach(item => {
            this.setNodeItem(this.nodeItem1, item.item_id, item.expiration);
          });
          this.game_stats = result.game_stats;
        }

        async onHide() {}

        async onClose() {
          // 停止所有动画
          if (this._scoreTween) this._scoreTween.stop();
          if (this._expTween) this._expTween.stop();
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "btnOK", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "btnNext", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "lblScore", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "scoreAdd", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "scoreHigh", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "lblLevel", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor7 = _applyDecoratedDescriptor(_class2.prototype, "expProBar", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor8 = _applyDecoratedDescriptor(_class2.prototype, "lblExp", [_dec9], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor9 = _applyDecoratedDescriptor(_class2.prototype, "nodeItem1", [_dec10], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor10 = _applyDecoratedDescriptor(_class2.prototype, "nodeItem2", [_dec11], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor11 = _applyDecoratedDescriptor(_class2.prototype, "nodeItem3", [_dec12], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor12 = _applyDecoratedDescriptor(_class2.prototype, "nodeBestWeek", [_dec13], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor13 = _applyDecoratedDescriptor(_class2.prototype, "passText", [_dec14], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor14 = _applyDecoratedDescriptor(_class2.prototype, "nodePass", [_dec15], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor15 = _applyDecoratedDescriptor(_class2.prototype, "lblTimeCost", [_dec16], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=373c1fa90721a7f4dd6a130bcc1502d168754c9f.js.map