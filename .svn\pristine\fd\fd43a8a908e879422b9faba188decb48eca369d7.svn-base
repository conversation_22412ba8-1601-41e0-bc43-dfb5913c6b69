
import { view } from "cc";

class _GameConst {

    readonly ColliderDraw: boolean = false;
    readonly ActionFrameTime: number = 0.0333;

    designWidth: number = 750 // 设计分辨率
    designHeight: number = 1334 // 设计分辨率
    offsetWidth: number = 200 // 宽屏的时候，宽度最高多显示200像素


    get ViewHeight() {
        return view.getVisibleSize().height
    }

    get ViewWidth() {
        return view.getVisibleSize().width;
    }

    get ViewBattleWidth() {
        let width = view.getVisibleSize().width;
        return width + this.offsetWidth;
    }

}

export const GameConst = new _GameConst();