import csproto from 'db://assets/bundles/common/script/autogen/pb/cs_proto.js';
import { MessageBox } from "db://assets/bundles/common/script/ui/common/components/messagebox/MessageBox";
import { logWarn } from "db://assets/scripts/utils/Logger";
import Long from "long";
import { MyApp } from "../../app/MyApp";
import { ModeType } from "../../autogen/luban/schema";
import EventManager from '../../event/EventManager';
import { GameEvent } from '../../game/event/GameEvent';
import { DataMgr, IData } from "../DataManager";

export class GameLogic implements IData {
    gameID: Long = Long.ZERO;
    chapterID: number = 0;
    result: csproto.comm.IGameResult | null = null;
    init() {
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GAME_START, this.onGameStart, this);
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GAME_END, this.onGameEnd, this);
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GAME_GET_INFO, this.onGameGetInfo, this);
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GAME_GET_REWARD, this.onGameGetReward, this);
    }
    update(): void {
    }
    //#region 收协议相关
    onGameStart(msg: csproto.cs.IS2CMsg): void {
        // MessageBox.testToast("收到协议返回，开始战斗");
        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {
            logWarn("NetMgr", `onGameStart failed ${msg.ret_code}`);
            MessageBox.errorCode(msg.ret_code!);
            return;
        }
        var data = msg.body?.game_start;
        if (!data) {
            logWarn("NetMgr", "onGameStart data is null");
            return;
        }
        this.gameID = data.start_info?.base?.game_id!;
        var mode_type: ModeType = data.start_info?.base?.mode_type!;
        this.chapterID = data.start_info?.base?.chapter_id!;

        EventManager.Instance.emit(GameEvent.onNetGameStart);
    }

    onGameEnd(msg: csproto.cs.IS2CMsg): void {
        // MessageBox.testToast("收到协议返回，战斗结束");
        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {
            logWarn("NetMgr", `onGameEnd failed ${msg.ret_code}`);
            MessageBox.errorCode(msg.ret_code!);
            return;
        }
        var data = msg.body?.game_end;
        if (!data) {
            logWarn("NetMgr", "onGameEnd data is null");
            return;
        }
        data.result = data.result!;
        EventManager.Instance.emit(GameEvent.onNetGameOver);
    }

    onGameGetInfo(msg: csproto.cs.IS2CMsg): void {
        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {
            logWarn("NetMgr", `onGameGetInfo failed ${msg.ret_code}`);
            return;
        }
        var data = msg.body?.game_get_info;
        if (!data) {
            logWarn("NetMgr", "onGameGetInfo data is null");
            return;
        }
        let status = data.status;
        if (status == csproto.comm.ROLE_GAME_STATUS.ROLE_GAME_STATUS_FIGHTING) {
            //todo  战斗未结束，获取 之前存 的keys
            let start_info = data.start_info;
            DataMgr.role.cmdGetClientSettingBattle();
        }
        else if (status == csproto.comm.ROLE_GAME_STATUS.ROLE_GAME_STATUS_END) {
            let result = data.result;
            let game_id = result?.game_id;
            //this.cmdGameGetReward(game_id!);
        }
    }

    onGameGetReward(msg: csproto.cs.IS2CMsg): void {
        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {
            logWarn("NetMgr", `onGameGetReward failed ${msg.ret_code}`);
            return;
        }
        var data = msg.body?.game_get_reward;
        if (!data) {
            logWarn("NetMgr", "onGameGetReward data is null");
            return;
        }
        let game_id = data.game_id;
        let gain_items = data.gain_items;
    }
    //#endregion
    //#region 发送协议相关
    /** 开始游戏
     * 
     * @param modeID 模式id--无尽=101，来自模式表
     * @param partnerUin 合体好友的uin，默认为0，无合体
     */
    cmdGameStart(modeID: number, partnerUin: Long = Long.ZERO) {
        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_GAME_START, {
            game_start: {
                mode_id: modeID,
                partner_uin: partnerUin,
            }
        })
    }
    cmdGameEnd(result_info: csproto.comm.IGameResult | null, level_result_info: csproto.comm.IGameLevelResult[] | null) {
        if (!this.gameID) {
            logWarn("GameLogic", "gameID is null");
            return;
        }
        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_GAME_END, {
            game_end: {
                game_id: this.gameID,
                result: result_info,
                level_result: level_result_info,
            }
        })
    }
    cmdGameGetInfo() {
        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_GAME_GET_INFO, {
            game_get_info: {}
        });
    }
    cmdGameGetReward(gameID: Long, is_double: boolean = false) {
        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_GAME_GET_REWARD, {
            game_get_reward: {
                game_id: gameID,
                is_double: (is_double ? 2 : 1),
            }
        })
    }
    //#endregion
    //#region 外部调用相关
    checkGameID(gameID: Long) {
        // if (gameID.isZero()) {
        //     return;
        // }
        //this.cmdGameGetInfo();
    }
    //#endregion
}