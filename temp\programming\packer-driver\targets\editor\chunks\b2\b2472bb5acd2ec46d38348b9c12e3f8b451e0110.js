System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "cc/env"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, UIMgr, logInfo, MyApp, csproto, DevLoginUI, WECHAT, _dec, _class, _crd, ccclass, property, MainUI;

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogInfo(extras) {
    _reporterNs.report("logInfo", "db://assets/scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "../../app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfcsproto(extras) {
    _reporterNs.report("csproto", "../../autogen/pb/cs_proto.js", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDevLoginUI(extras) {
    _reporterNs.report("DevLoginUI", "../gameui/DevLoginUI", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
    }, function (_unresolved_2) {
      UIMgr = _unresolved_2.UIMgr;
    }, function (_unresolved_3) {
      logInfo = _unresolved_3.logInfo;
    }, function (_unresolved_4) {
      MyApp = _unresolved_4.MyApp;
    }, function (_unresolved_5) {
      csproto = _unresolved_5.default;
    }, function (_unresolved_6) {
      DevLoginUI = _unresolved_6.DevLoginUI;
    }, function (_ccEnv) {
      WECHAT = _ccEnv.WECHAT;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "ae5246o93ZERJ5bpRQ+Btv5", "MainUI", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Prefab']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("MainUI", MainUI = (_dec = ccclass('MainUI'), _dec(_class = class MainUI extends Component {
        onLoad() {}

        async start() {
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_KICK_OFF, this.onKickOff, this);

          if ((_crd && DevLoginUI === void 0 ? (_reportPossibleCrUseOfDevLoginUI({
            error: Error()
          }), DevLoginUI) : DevLoginUI).needLogin) {
            if (WECHAT) {
              (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                error: Error()
              }), MyApp) : MyApp).netMgr.connect();
            } else {
              await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
                error: Error()
              }), UIMgr) : UIMgr).openUI(_crd && DevLoginUI === void 0 ? (_reportPossibleCrUseOfDevLoginUI({
                error: Error()
              }), DevLoginUI) : DevLoginUI);
            }
          }
        }

        onDestroy() {
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.unregisterHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_KICK_OFF, this.onKickOff, this);
        }

        onKickOff(msg) {
          (_crd && logInfo === void 0 ? (_reportPossibleCrUseOflogInfo({
            error: Error()
          }), logInfo) : logInfo)("MainUI", "onKickOff");
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.disableReconnect();

          if (WECHAT) {} else {
            (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).openUI(_crd && DevLoginUI === void 0 ? (_reportPossibleCrUseOfDevLoginUI({
              error: Error()
            }), DevLoginUI) : DevLoginUI);
          }
        }

        update(deltaTime) {}

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=b2472bb5acd2ec46d38348b9c12e3f8b451e0110.js.map