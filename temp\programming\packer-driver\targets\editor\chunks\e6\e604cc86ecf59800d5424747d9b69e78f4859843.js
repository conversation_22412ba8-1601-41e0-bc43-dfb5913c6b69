System.register(["__unresolved_0", "cc", "cc/env", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, DEBUG, csproto, MarqueeUI, PopupUI, ToastUI, UIMgr, MessageBox, _crd;

  function _reportPossibleCrUseOfcsproto(extras) {
    _reporterNs.report("csproto", "db://assets/bundles/common/script/autogen/pb/cs_proto.js", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMarqueeUI(extras) {
    _reporterNs.report("MarqueeUI", "db://assets/bundles/common/script/ui/common/MarqueeUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPopupUI(extras) {
    _reporterNs.report("<PERSON>upUI", "db://assets/bundles/common/script/ui/common/PopupUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfToastUI(extras) {
    _reporterNs.report("ToastUI", "db://assets/bundles/common/script/ui/common/ToastUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  _export("MessageBox", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_ccEnv) {
      DEBUG = _ccEnv.DEBUG;
    }, function (_unresolved_2) {
      csproto = _unresolved_2.default;
    }, function (_unresolved_3) {
      MarqueeUI = _unresolved_3.MarqueeUI;
    }, function (_unresolved_4) {
      PopupUI = _unresolved_4.PopupUI;
    }, function (_unresolved_5) {
      ToastUI = _unresolved_5.ToastUI;
    }, function (_unresolved_6) {
      UIMgr = _unresolved_6.UIMgr;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "0cbcaRVKQpLSp3iiPY+VaU7", "MessageBox", undefined);

      // 定义回调函数类型
      _export("MessageBox", MessageBox = class MessageBox {
        // 取消回调数组
        static async ShowNextPopup() {
          if (this.showingPopup || this.contentPopup.length === 0) {
            return;
          }

          this.showingPopup = true;
          const content = this.contentPopup.shift();
          const onConfirm = this.confirmCallbacks.shift();
          const onCancel = this.cancelCallbacks.shift(); // 统一调用 UIMgr

          await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && PopupUI === void 0 ? (_reportPossibleCrUseOfPopupUI({
            error: Error()
          }), PopupUI) : PopupUI, content, onConfirm, onCancel);
          let ui = (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).get(_crd && PopupUI === void 0 ? (_reportPossibleCrUseOfPopupUI({
            error: Error()
          }), PopupUI) : PopupUI);

          if (ui) {
            ui.toClose = this.closePopup.bind(this);
          }
        }

        static async closePopup() {
          await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).closeUI(_crd && PopupUI === void 0 ? (_reportPossibleCrUseOfPopupUI({
            error: Error()
          }), PopupUI) : PopupUI);
          this.showingPopup = false;
          this.ShowNextPopup();
        } // 强制显示确认+取消按钮，不需要取消函数回调，不传onCancel


        static confirm(content, onConfirm, onCancel) {
          const cancelHandler = onCancel || (() => {});

          this.show(content, onConfirm, cancelHandler);
        } // 只显示确认按钮，调用这个


        static show(content, onConfirm, onCancel) {
          this.contentPopup.push(content);
          this.confirmCallbacks.push(onConfirm);
          this.cancelCallbacks.push(onCancel);
          this.ShowNextPopup();
        }

        // 确认回调数组
        static marquee(content) {
          this.contentMarquee.push(content);
          this.ShowNextMarquee();
        }

        static async ShowNextMarquee() {
          if (this.showingMarquee || this.contentMarquee.length === 0) {
            return;
          }

          this.showingMarquee = true;
          const content = this.contentMarquee.shift(); // 统一调用 UIMgr

          await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && MarqueeUI === void 0 ? (_reportPossibleCrUseOfMarqueeUI({
            error: Error()
          }), MarqueeUI) : MarqueeUI, content);
          let ui = (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).get(_crd && MarqueeUI === void 0 ? (_reportPossibleCrUseOfMarqueeUI({
            error: Error()
          }), MarqueeUI) : MarqueeUI);

          if (ui) {
            ui.toClose = this.closeMarquee.bind(this);
          }
        }

        static async closeMarquee() {
          await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).closeUI(_crd && MarqueeUI === void 0 ? (_reportPossibleCrUseOfMarqueeUI({
            error: Error()
          }), MarqueeUI) : MarqueeUI);
          this.showingMarquee = false;
          this.ShowNextMarquee();
        }

        static toast(content) {
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && ToastUI === void 0 ? (_reportPossibleCrUseOfToastUI({
            error: Error()
          }), ToastUI) : ToastUI, content);
        } //DEBUG


        static testShow(content) {
          if (DEBUG) {
            this.show(content);
          }
        }

        static testToast(content) {
          if (DEBUG) {
            this.toast(content);
          }
        }

        static errorCode(ret_code) {
          if (DEBUG) {
            this.show("错误码：\n" + ret_code + "\n" + (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
              error: Error()
            }), csproto) : csproto).comm.RET_CODE[ret_code]);
          }
        }

      });

      MessageBox.showingPopup = false;
      MessageBox.contentPopup = [];
      // 确认回调数组
      MessageBox.confirmCallbacks = [];
      // 确认回调数组
      MessageBox.cancelCallbacks = [];
      MessageBox.showingMarquee = false;
      MessageBox.contentMarquee = [];

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=e604cc86ecf59800d5424747d9b69e78f4859843.js.map