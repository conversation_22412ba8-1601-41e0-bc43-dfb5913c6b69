System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, misc, Vec3, JsonAsset, MoveBase, PathData, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _crd, degreesToRadians, radiansToDegrees, ccclass, property, executeInEditMode, PathMove;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _reportPossibleCrUseOfMoveBase(extras) {
    _reporterNs.report("MoveBase", "./IMovable", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathData(extras) {
    _reporterNs.report("PathData", "../data/PathData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathPoint(extras) {
    _reporterNs.report("PathPoint", "../data/PathData", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      misc = _cc.misc;
      Vec3 = _cc.Vec3;
      JsonAsset = _cc.JsonAsset;
    }, function (_unresolved_2) {
      MoveBase = _unresolved_2.MoveBase;
    }, function (_unresolved_3) {
      PathData = _unresolved_3.PathData;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "0793c9EHOFKg6LLrnZPLKct", "PathMove", undefined);

      __checkObsolete__(['_decorator', 'Component', 'misc', 'Enum', 'Node', 'UITransform', 'Vec2', 'Vec3', 'JsonAsset']);

      ({
        degreesToRadians,
        radiansToDegrees
      } = misc);
      ({
        ccclass,
        property,
        executeInEditMode
      } = _decorator);

      _export("PathMove", PathMove = (_dec = ccclass('PathMove'), _dec2 = executeInEditMode(), _dec3 = property({
        type: JsonAsset,
        displayName: "路径数据(预览用)"
      }), _dec4 = property({
        displayName: "循环移动"
      }), _dec5 = property({
        displayName: "反向移动"
      }), _dec6 = property({
        displayName: "振荡偏移速度",
        tooltip: "控制倾斜振荡的频率"
      }), _dec7 = property({
        displayName: "振荡偏移幅度",
        tooltip: "控制倾斜振荡的幅度"
      }), _dec(_class = _dec2(_class = (_class2 = class PathMove extends (_crd && MoveBase === void 0 ? (_reportPossibleCrUseOfMoveBase({
        error: Error()
      }), MoveBase) : MoveBase) {
        constructor() {
          super(...arguments);
          this._pathAsset = null;

          _initializerDefineProperty(this, "loop", _descriptor, this);

          _initializerDefineProperty(this, "reverse", _descriptor2, this);

          _initializerDefineProperty(this, "tiltSpeed", _descriptor3, this);

          _initializerDefineProperty(this, "tiltOffset", _descriptor4, this);

          // 路径相关数据
          this._pathData = null;
          this._subdivided = [];
          // 细分后的路径点（包含完整信息）
          // 移动状态
          this._currentPosition = new Vec3();
          this._currentPointIndex = 0;
          // 当前所在的细分点索引
          this._nextPointIndex = 0;
          this._remainDistance = 0;
          // 距离下一个点的剩余距离
          this._tiltTime = 0;
          // 停留状态
          this._stayTimer = 0;
          // 停留计时器（秒）
          this._stayPointIndex = -1;
          // 停留的点索引
          this._updateInEditor = false;
        }

        get pathAsset() {
          return this._pathAsset;
        }

        set pathAsset(value) {
          this._pathAsset = value;

          if (value) {
            this.setPath((_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
              error: Error()
            }), PathData) : PathData).fromJSON(value.json));
          }
        }

        onFocusInEditor() {
          this._updateInEditor = true;
          this._isMovable = true;
        }

        onLostFocusInEditor() {
          this._updateInEditor = false;
          this._isMovable = false;
          this.resetToStart();
        }

        update(dt) {
          if (this._updateInEditor) {
            this.tick(dt);
          }
        }
        /**
         * 加载路径数据（使用新的细分点方法）
         */


        setPath(pathData) {
          this._pathData = pathData; // 使用新的细分点方法，直接获取包含完整信息的PathPoint数组

          this._subdivided = this._pathData.getSubdividedPoints();
          this.resetToStart();
        }
        /**
         * 主要的移动更新逻辑
         */


        tick(dt) {
          if (!this._isMovable) return;

          if (this._nextPointIndex !== this._currentPointIndex) {
            this.tickMovement(dt);
          }

          this.tickTilting(dt);
        }

        tickMovement(dt) {
          // 处理停留逻辑
          if (this._stayTimer > 0) {
            this._stayTimer -= dt;

            if (this._stayTimer <= 0) {
              this._stayPointIndex = -1; // 停留结束，继续移动到下一个点

              this.moveToNextPoint();
            }

            return;
          } // 使用匀加速直线运动更新位置


          var v0 = this.speed; // s = v0*t + 0.5*a*t^2

          var s = v0 * dt + 0.5 * this.acceleration * dt * dt;
          this.speed += this.acceleration * dt; // console.log(`v0: ${v0.toFixed(2)}, a: ${this.acceleration.toFixed(2)}, s: ${s.toFixed(2)}`);
          // 计算移动向量

          var angleRad = this.speedAngle;
          var deltaX = Math.cos(angleRad) * s;
          var deltaY = Math.sin(angleRad) * s; // 更新位置

          this._currentPosition.x += deltaX;
          this._currentPosition.y += deltaY; // 设置节点位置

          this.node.setPosition(this._currentPosition); // 检查是否到达目标点

          if (this._remainDistance > 0) {
            this._remainDistance -= s;

            if (this._remainDistance <= 0) {
              this.onReachPoint(this._nextPointIndex);
            }
          }
        }

        tickTilting(dt) {
          // 应用倾斜偏移
          if (this.tiltSpeed > 0 && this.tiltOffset > 0) {
            this._tiltTime += dt; // 计算垂直于移动方向的向量

            var angleRad = this.speedAngle * Math.PI / 180;
            var perpX = -Math.sin(angleRad);
            var perpY = Math.cos(angleRad); // 计算倾斜偏移

            var tiltAmount = Math.sin(this._tiltTime * this.tiltSpeed) * this.tiltOffset; // 应用倾斜偏移到节点位置

            var finalX = this._currentPosition.x + perpX * tiltAmount;
            var finalY = this._currentPosition.y + perpY * tiltAmount;
            this.node.setPosition(finalX, finalY, 0);
          }
        }

        onReachPoint(pointIndex) {
          // 更新当前点索引
          this._currentPointIndex = pointIndex; // 检查是否需要停留

          var currentPoint = this.getPathPoint(pointIndex);

          if (currentPoint) {
            this._currentPosition.x = currentPoint.x;
            this._currentPosition.y = currentPoint.y;
            this.node.setPosition(this._currentPosition);
            console.log("\u5230\u8FBE\u70B9 " + pointIndex + ", \u505C\u7559\u65F6\u95F4: " + currentPoint.stayDuration + "ms");

            if (currentPoint.stayDuration > 0) {
              this._stayTimer = currentPoint.stayDuration / 1000.0;
              this._stayPointIndex = pointIndex;
              return;
            }
          } // 继续移动到下一个点


          this.moveToNextPoint();
        }

        moveToNextPoint() {
          var nextIndex = this._currentPointIndex + 1;

          if (nextIndex >= this._subdivided.length) {
            // 到达路径终点
            if (this.loop) {
              // 循环模式，回到起点
              this.setNext(0);
            } else {
              // 停止移动
              this._nextPointIndex = this._currentPointIndex;
            }
          } else {
            // 移动到下一个点
            this.setNext(nextIndex);
          }
        }

        setNext(pathPointIndex) {
          this._nextPointIndex = pathPointIndex;
          var currentPoint = this.getPathPoint(this._currentPointIndex);
          var nextPoint = this.getPathPoint(this._nextPointIndex);

          if (currentPoint && nextPoint) {
            var dirX = nextPoint.x - currentPoint.x;
            var dirY = nextPoint.y - currentPoint.y;
            this._remainDistance = Math.sqrt(dirX * dirX + dirY * dirY);

            if (this._remainDistance > 1) {
              // 计算移动角度
              this.speedAngle = Math.atan2(dirY, dirX); // 计算加速度：使用匀加速直线运动公式 v1^2 = v0^2 + 2*a*x
              // 解出 a = (v1^2 - v0^2) / (2*x)

              var v0 = currentPoint.speed;
              var v1 = nextPoint.speed;
              this.acceleration = (v1 * v1 - v0 * v0) / (2 * this._remainDistance); // 设置初始速度

              this.speed = v0; // console.log(`设置下一段: ${this._currentPointIndex} -> ${this._nextPointIndex}, 距离: ${this._remainDistance.toFixed(2)}, 初速度: ${v0}, 末速度: ${v1}, 加速度: ${this.acceleration.toFixed(2)}, 角度: ${radiansToDegrees(this.speedAngle).toFixed(2)}`);
            }
          }
        }

        getPathPoint(pathPointIndex) {
          if (pathPointIndex < 0 || pathPointIndex >= this._subdivided.length) {
            return null;
          }

          return this._subdivided[pathPointIndex];
        } // 公共API方法


        setMovable(movable) {
          this._isMovable = movable;
          return this;
        }

        resetToStart() {
          this._currentPointIndex = 0;
          this._nextPointIndex = 0;
          this._stayTimer = 0;
          this._stayPointIndex = -1;
          this._tiltTime = 0;
          this.onReachPoint(0);
        }

        isStaying() {
          return this._stayTimer > 0;
        }

        getRemainingStayTime() {
          return this._stayTimer;
        }

      }, (_applyDecoratedDescriptor(_class2.prototype, "pathAsset", [_dec3], Object.getOwnPropertyDescriptor(_class2.prototype, "pathAsset"), _class2.prototype), _descriptor = _applyDecoratedDescriptor(_class2.prototype, "loop", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return false;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "reverse", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return false;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "tiltSpeed", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "tiltOffset", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      })), _class2)) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=794ce2bb3f0615a636a10a8c3f025e15162051e7.js.map