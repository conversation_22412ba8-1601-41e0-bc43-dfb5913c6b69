System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, misc, Vec2, JsonAsset, MoveBase, PathData, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _crd, degreesToRadians, radiansToDegrees, ccclass, property, PathMovable;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfMoveBase(extras) {
    _reporterNs.report("MoveBase", "./IMovable", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathData(extras) {
    _reporterNs.report("PathData", "../data/PathData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathPoint(extras) {
    _reporterNs.report("PathPoint", "../data/PathData", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      misc = _cc.misc;
      Vec2 = _cc.Vec2;
      JsonAsset = _cc.JsonAsset;
    }, function (_unresolved_2) {
      MoveBase = _unresolved_2.MoveBase;
    }, function (_unresolved_3) {
      PathData = _unresolved_3.PathData;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "0793c9EHOFKg6LLrnZPLKct", "PathMove", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Enum', 'misc', 'Node', 'UITransform', 'Vec2', 'Vec3', 'JsonAsset']);

      ({
        degreesToRadians,
        radiansToDegrees
      } = misc);
      ({
        ccclass,
        property
      } = _decorator);

      _export("PathMovable", PathMovable = (_dec = ccclass('PathMovable'), _dec2 = property({
        type: JsonAsset,
        displayName: "路径数据"
      }), _dec3 = property({
        displayName: "循环移动"
      }), _dec4 = property({
        displayName: "反向移动"
      }), _dec5 = property({
        displayName: "振荡偏移速度",
        tooltip: "控制倾斜振荡的频率"
      }), _dec6 = property({
        displayName: "振荡偏移幅度",
        tooltip: "控制倾斜振荡的幅度"
      }), _dec(_class = (_class2 = class PathMovable extends (_crd && MoveBase === void 0 ? (_reportPossibleCrUseOfMoveBase({
        error: Error()
      }), MoveBase) : MoveBase) {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "pathAsset", _descriptor, this);

          _initializerDefineProperty(this, "loop", _descriptor2, this);

          _initializerDefineProperty(this, "reverse", _descriptor3, this);

          _initializerDefineProperty(this, "tiltSpeed", _descriptor4, this);

          _initializerDefineProperty(this, "tiltOffset", _descriptor5, this);

          // 路径相关数据
          this._pathData = null;
          this._subdivided = [];
          // 细分后的路径点（包含完整信息）
          this._totalDistance = 0;
          this._distances = [];
          // 移动状态
          this._currentDistance = 0;
          this._currentPointIndex = 0;
          // 当前所在的细分点索引
          this._segmentT = 0;
          // 在当前段内的插值参数 [0,1]
          this._tiltTime = 0;

          /**
           * 设置速度倍率（用于临时调整速度）
           */
          this._speedMultiplier = 1.0;
        }

        onLoad() {
          super.onLoad();
          this.loadPathData();
        }
        /**
         * 加载路径数据（使用新的细分点方法）
         */


        loadPathData() {
          if (!this.pathAsset) return; // 创建PathData实例并加载数据

          this._pathData = (_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
            error: Error()
          }), PathData) : PathData).fromJSON(this.pathAsset.json); // 使用新的细分点方法，直接获取包含完整信息的PathPoint数组

          this._subdivided = this._pathData.getSubdividedPoints(); // 计算距离信息

          this.calculateDistances();
        }
        /**
         * 计算距离信息
         */


        calculateDistances() {
          this._distances = [0];
          this._totalDistance = 0;

          for (var i = 1; i < this._subdivided.length; i++) {
            var distance = Vec2.distance(this._subdivided[i - 1].position, this._subdivided[i].position);
            this._totalDistance += distance;

            this._distances.push(this._totalDistance);
          }
        }
        /**
         * 主要的移动更新逻辑
         */


        tick(dt) {
          if (!this._isMovable || this._subdivided.length < 2) return; // 获取当前位置的速度（从细分点直接获取）

          var currentSpeed = this.getCurrentSpeed(); // 更新沿路径的距离

          var deltaDistance = currentSpeed * dt;

          if (this.reverse) {
            this._currentDistance -= deltaDistance;

            if (this._currentDistance < 0) {
              if (this.loop) {
                this._currentDistance = this._totalDistance + this._currentDistance;
              } else {
                this._currentDistance = 0;
              }
            }
          } else {
            this._currentDistance += deltaDistance;

            if (this._currentDistance > this._totalDistance) {
              if (this.loop) {
                this._currentDistance = this._currentDistance - this._totalDistance;
              } else {
                this._currentDistance = this._totalDistance;
              }
            }
          }

          this.updateCurrentPointIndex();
          this.updatePosition(dt);
        }
        /**
         * 根据当前距离更新点索引和段内插值参数
         */


        updateCurrentPointIndex() {
          if (this._subdivided.length === 0) return; // 边界情况处理

          if (this._currentDistance <= 0) {
            this._currentPointIndex = 0;
            this._segmentT = 0;
            return;
          }

          if (this._currentDistance >= this._totalDistance) {
            this._currentPointIndex = Math.max(0, this._subdivided.length - 2);
            this._segmentT = 1;
            return;
          } // 从当前索引开始搜索，利用时间连续性


          var searchStart = Math.max(0, this._currentPointIndex - 1);
          var found = false; // 向前搜索

          for (var i = searchStart; i < this._distances.length - 1; i++) {
            if (this._currentDistance >= this._distances[i] && this._currentDistance <= this._distances[i + 1]) {
              this._currentPointIndex = i;
              var segmentLength = this._distances[i + 1] - this._distances[i];
              this._segmentT = segmentLength > 0 ? (this._currentDistance - this._distances[i]) / segmentLength : 0;
              found = true;
              break;
            }
          } // 如果向前搜索没找到，向后搜索（处理反向移动）


          if (!found) {
            for (var _i = Math.min(searchStart, this._distances.length - 2); _i >= 0; _i--) {
              if (this._currentDistance >= this._distances[_i] && this._currentDistance <= this._distances[_i + 1]) {
                this._currentPointIndex = _i;

                var _segmentLength = this._distances[_i + 1] - this._distances[_i];

                this._segmentT = _segmentLength > 0 ? (this._currentDistance - this._distances[_i]) / _segmentLength : 0;
                break;
              }
            }
          }
        }
        /**
         * 获取当前速度（使用索引优化）
         */


        getCurrentSpeed() {
          if (this._subdivided.length === 0) {
            return this.speed; // 回退到基础速度
          } // 边界情况


          if (this._currentPointIndex >= this._subdivided.length - 1) {
            return this._subdivided[this._subdivided.length - 1].speed * this._speedMultiplier;
          } // 使用预计算的索引和插值参数


          var startSpeed = this._subdivided[this._currentPointIndex].speed;
          var endSpeed = this._subdivided[this._currentPointIndex + 1].speed; // 在两个细分点之间插值速度

          var interpolatedSpeed = startSpeed + (endSpeed - startSpeed) * this._segmentT;
          return interpolatedSpeed * this._speedMultiplier;
        }
        /**
         * 更新节点位置和朝向
         */


        updatePosition(dt) {
          var position = this.getPositionAtDistance(this._currentDistance); // 应用倾斜偏移

          if (this.tiltSpeed > 0 && this.tiltOffset > 0) {
            this._tiltTime += dt;
            var direction = this.getDirectionAtDistance(this._currentDistance);

            if (direction.lengthSqr() > 0.001) {
              // 计算垂直于移动方向的向量
              var perpX = -direction.y;
              var perpY = direction.x; // 计算倾斜偏移

              var tiltAmount = Math.sin(this._tiltTime * this.tiltSpeed) * this.tiltOffset;
              position.x += perpX * tiltAmount;
              position.y += perpY * tiltAmount;
            }
          }

          this.node.setPosition(position.x, position.y, 0); // // 更新朝向
          // if (this.isFacingMoveDir) {
          //     const direction = this.getDirectionAtDistance(this._currentDistance);
          //     if (direction.lengthSqr() > 0.001) {
          //         const angle = Math.atan2(direction.y, direction.x) * 180 / Math.PI;
          //         const finalAngle = angle + this.defaultFacing;
          //         this.node.setRotationFromEuler(0, 0, finalAngle);
          //     }
          // }
          // 可见性检查

          if (++this._visibilityCheckCounter >= PathMovable.VISIBILITY_CHECK_INTERVAL) {
            this._visibilityCheckCounter = 0;
            this.checkVisibility();
          }
        }
        /**
         * 根据距离获取位置（优化版本，主要用于当前位置）
         */


        getPositionAtDistance(distance) {
          // 如果查询的是当前距离，使用优化的索引方法
          if (Math.abs(distance - this._currentDistance) < 0.001) {
            return this.getCurrentPosition();
          } // 其他距离仍使用原来的方法（用于方向计算等）


          if (this._subdivided.length === 0) return new Vec2();
          if (distance <= 0) return this._subdivided[0].position.clone();
          if (distance >= this._totalDistance) return this._subdivided[this._subdivided.length - 1].position.clone();

          for (var i = 1; i < this._distances.length; i++) {
            if (distance <= this._distances[i]) {
              var segmentStart = this._distances[i - 1];
              var segmentEnd = this._distances[i];
              var segmentLength = segmentEnd - segmentStart;
              if (segmentLength === 0) return this._subdivided[i - 1].position.clone();
              var t = (distance - segmentStart) / segmentLength;
              return Vec2.lerp(new Vec2(), this._subdivided[i - 1].position, this._subdivided[i].position, t);
            }
          }

          return this._subdivided[this._subdivided.length - 1].position.clone();
        }
        /**
         * 获取当前位置（使用索引优化）
         */


        getCurrentPosition() {
          if (this._subdivided.length === 0) return new Vec2(); // 边界情况

          if (this._currentPointIndex >= this._subdivided.length - 1) {
            return this._subdivided[this._subdivided.length - 1].position.clone();
          } // 使用预计算的索引和插值参数


          var startPos = this._subdivided[this._currentPointIndex].position;
          var endPos = this._subdivided[this._currentPointIndex + 1].position;
          return Vec2.lerp(new Vec2(), startPos, endPos, this._segmentT);
        }
        /**
         * 根据距离获取移动方向
         */


        getDirectionAtDistance(distance) {
          var epsilon = 1;
          var pos1 = this.getPositionAtDistance(distance);
          var pos2 = this.getPositionAtDistance(distance + epsilon);
          return Vec2.subtract(new Vec2(), pos2, pos1).normalize();
        } // 公共API方法


        setMovable(movable) {
          this._isMovable = movable;
          return this;
        }
        /**
         * 设置路径进度 [0-1]
         */


        setProgress(progress) {
          this._currentDistance = Math.max(0, Math.min(1, progress)) * this._totalDistance;
          this.updateCurrentPointIndex(); // 更新索引

          return this;
        }
        /**
         * 获取当前进度 [0-1]
         */


        getProgress() {
          return this._totalDistance > 0 ? this._currentDistance / this._totalDistance : 0;
        }
        /**
         * 重置到路径起点
         */


        resetToStart() {
          this._currentDistance = 0;
          this._currentPointIndex = 0;
          this._segmentT = 0;
          return this;
        }
        /**
         * 移动到路径终点
         */


        moveToEnd() {
          this._currentDistance = this._totalDistance;
          this.updateCurrentPointIndex(); // 更新索引

          return this;
        }
        /**
         * 获取当前位置对应的路径点数据（使用索引优化）
         */


        getCurrentPathPointData() {
          if (this._subdivided.length === 0) return null; // 边界情况

          if (this._currentPointIndex >= this._subdivided.length - 1) {
            return this._subdivided[this._subdivided.length - 1];
          } // 使用预计算的索引，返回更接近的那个点


          return this._segmentT < 0.5 ? this._subdivided[this._currentPointIndex] : this._subdivided[this._currentPointIndex + 1];
        }
        /**
         * 获取当前位置的详细路径信息（包括速度、朝向等）
         */


        getCurrentPathInfo() {
          if (this._subdivided.length === 0) return null;
          var currentPoint = this.getCurrentPathPointData();
          var currentSpeed = this.getCurrentSpeed();
          return {
            speed: currentSpeed,
            position: this.getPositionAtDistance(this._currentDistance),
            direction: this.getDirectionAtDistance(this._currentDistance),
            pathPoint: currentPoint,
            progress: this.getProgress(),
            distance: this._currentDistance,
            totalDistance: this._totalDistance
          };
        }

        setSpeedMultiplier(multiplier) {
          this._speedMultiplier = Math.max(0, multiplier);
          return this;
        }

        getSpeedMultiplier() {
          return this._speedMultiplier;
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "pathAsset", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "loop", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return false;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "reverse", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return false;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "tiltSpeed", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "tiltOffset", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=794ce2bb3f0615a636a10a8c3f025e15162051e7.js.map