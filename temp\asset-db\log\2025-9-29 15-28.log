2025-9-29 15:28:48-debug: start **** info
2025-9-29 15:28:49-log: Cannot access game frame or container.
2025-9-29 15:28:49-debug: asset-db:require-engine-code (383ms)
2025-9-29 15:28:49-log: meshopt wasm decoder initialized
2025-9-29 15:28:49-log: [bullet]:bullet wasm lib loaded.
2025-9-29 15:28:49-log: [box2d]:box2d wasm lib loaded.
2025-9-29 15:28:49-log: Cocos Creator v3.8.6
2025-9-29 15:28:49-log: Using legacy pipeline
2025-9-29 15:28:49-debug: [Assets Memory track]: asset-db-plugin-register: programming start:81.26MB, end 84.06MB, increase: 2.80MB
2025-9-29 15:28:49-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:30.97MB, end 80.13MB, increase: 49.17MB
2025-9-29 15:28:50-debug: [Assets Memory track]: asset-db-plugin-register: project start:81.11MB, end 280.83MB, increase: 199.72MB
2025-9-29 15:28:50-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:80.16MB, end 280.86MB, increase: 200.70MB
2025-9-29 15:28:50-debug: [Assets Memory track]: asset-db-plugin-register: builder start:84.10MB, end 282.55MB, increase: 198.45MB
2025-9-29 15:28:49-log: Forward render pipeline initialized.
2025-9-29 15:28:50-debug: run package(fb-instant-games) handler(enable) success!
2025-9-29 15:28:50-debug: run package(fb-instant-games) handler(enable) start
2025-9-29 15:28:50-debug: run package(google-play) handler(enable) start
2025-9-29 15:28:50-debug: run package(google-play) handler(enable) success!
2025-9-29 15:28:50-debug: run package(harmonyos-next) handler(enable) success!
2025-9-29 15:28:50-debug: run package(honor-mini-game) handler(enable) start
2025-9-29 15:28:50-debug: run package(huawei-agc) handler(enable) start
2025-9-29 15:28:50-debug: run package(harmonyos-next) handler(enable) start
2025-9-29 15:28:50-debug: run package(honor-mini-game) handler(enable) success!
2025-9-29 15:28:50-debug: run package(huawei-quick-game) handler(enable) start
2025-9-29 15:28:50-debug: run package(huawei-agc) handler(enable) success!
2025-9-29 15:28:50-debug: run package(ios) handler(enable) start
2025-9-29 15:28:50-debug: run package(ios) handler(enable) success!
2025-9-29 15:28:50-debug: run package(huawei-quick-game) handler(enable) success!
2025-9-29 15:28:50-debug: run package(linux) handler(enable) success!
2025-9-29 15:28:50-debug: run package(linux) handler(enable) start
2025-9-29 15:28:50-debug: run package(mac) handler(enable) success!
2025-9-29 15:28:50-debug: run package(migu-mini-game) handler(enable) start
2025-9-29 15:28:50-debug: run package(mac) handler(enable) start
2025-9-29 15:28:50-debug: run package(native) handler(enable) start
2025-9-29 15:28:50-debug: run package(native) handler(enable) success!
2025-9-29 15:28:50-debug: run package(ohos) handler(enable) success!
2025-9-29 15:28:50-debug: run package(migu-mini-game) handler(enable) success!
2025-9-29 15:28:50-debug: run package(ohos) handler(enable) start
2025-9-29 15:28:50-debug: run package(runtime-dev-tools) handler(enable) start
2025-9-29 15:28:50-debug: run package(oppo-mini-game) handler(enable) start
2025-9-29 15:28:50-debug: run package(oppo-mini-game) handler(enable) success!
2025-9-29 15:28:50-debug: run package(runtime-dev-tools) handler(enable) success!
2025-9-29 15:28:50-debug: run package(taobao-mini-game) handler(enable) start
2025-9-29 15:28:50-debug: run package(taobao-mini-game) handler(enable) success!
2025-9-29 15:28:50-debug: run package(vivo-mini-game) handler(enable) success!
2025-9-29 15:28:50-debug: run package(web-desktop) handler(enable) start
2025-9-29 15:28:50-debug: run package(vivo-mini-game) handler(enable) start
2025-9-29 15:28:50-debug: run package(web-desktop) handler(enable) success!
2025-9-29 15:28:50-debug: run package(web-mobile) handler(enable) start
2025-9-29 15:28:50-debug: run package(web-mobile) handler(enable) success!
2025-9-29 15:28:50-debug: run package(wechatprogram) handler(enable) start
2025-9-29 15:28:50-debug: run package(wechatgame) handler(enable) start
2025-9-29 15:28:50-debug: run package(wechatprogram) handler(enable) success!
2025-9-29 15:28:50-debug: run package(wechatgame) handler(enable) success!
2025-9-29 15:28:50-debug: run package(windows) handler(enable) success!
2025-9-29 15:28:50-debug: run package(windows) handler(enable) start
2025-9-29 15:28:50-debug: run package(xiaomi-quick-game) handler(enable) start
2025-9-29 15:28:50-debug: run package(xiaomi-quick-game) handler(enable) success!
2025-9-29 15:28:50-debug: run package(cocos-service) handler(enable) success!
2025-9-29 15:28:50-debug: run package(im-plugin) handler(enable) start
2025-9-29 15:28:50-debug: run package(cocos-service) handler(enable) start
2025-9-29 15:28:50-debug: run package(fix-asset-default-userdata) handler(enable) start
2025-9-29 15:28:50-debug: run package(im-plugin) handler(enable) success!
2025-9-29 15:28:50-debug: run package(fix-asset-default-userdata) handler(enable) success!
2025-9-29 15:28:50-debug: asset-db:worker-init: initPlugin (996ms)
2025-9-29 15:28:50-debug: [Assets Memory track]: asset-db:worker-init start:30.96MB, end 283.12MB, increase: 252.16MB
2025-9-29 15:28:50-debug: Run asset db hook programming:beforePreStart ...
2025-9-29 15:28:50-debug: Run asset db hook engine-extends:beforePreStart success!
2025-9-29 15:28:50-debug: Run asset db hook programming:beforePreStart success!
2025-9-29 15:28:50-debug: run package(emitter-editor) handler(enable) start
2025-9-29 15:28:50-debug: start refresh asset from db://assets/editor/enum-gen/EmitterEnum.ts...
2025-9-29 15:28:50-debug: Run asset db hook engine-extends:beforePreStart ...
2025-9-29 15:28:50-debug: run package(emitter-editor) handler(enable) success!
2025-9-29 15:28:50-debug: refresh asset db://assets/editor/enum-gen success
2025-9-29 15:28:50-debug: start refresh asset from db://assets/editor/enum-gen/EnemyEnum.ts...
2025-9-29 15:28:50-debug: run package(i18n) handler(enable) start
2025-9-29 15:28:50-debug: refresh asset db://assets/editor/enum-gen success
2025-9-29 15:28:50-debug: start custom db i18n...
2025-9-29 15:28:50-debug: run package(i18n) handler(enable) success!
2025-9-29 15:28:50-debug: start asset-db(i18n)...
2025-9-29 15:28:50-debug: run package(level-editor) handler(enable) start
2025-9-29 15:28:50-debug: run package(level-editor) handler(enable) success!
2025-9-29 15:28:50-debug: asset-db:worker-init (1495ms)
2025-9-29 15:28:50-debug: asset-db-hook-programming-beforePreStart (55ms)
2025-9-29 15:28:50-debug: asset-db-hook-engine-extends-beforePreStart (55ms)
2025-9-29 15:28:50-debug: asset-db:worker-startup-database[i18n] (18ms)
2025-9-29 15:28:50-debug: Preimport db internal success
2025-9-29 15:28:50-debug: run package(placeholder) handler(enable) start
2025-9-29 15:28:50-debug: run package(placeholder) handler(enable) success!
2025-9-29 15:28:50-debug: %cImport%c: E:\M2Game\Client\assets\editor\enum-gen\EnemyEnum.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 15:28:50-debug: %cImport%c: E:\M2Game\Client\assets\editor\enum-gen\EmitterEnum.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 15:28:50-debug: Preimport db assets success
2025-9-29 15:28:50-debug: Run asset db hook programming:afterPreStart ...
2025-9-29 15:28:50-debug: starting packer-driver...
2025-9-29 15:28:56-debug: initialize scripting environment...
2025-9-29 15:28:56-debug: [[Executor]] prepare before lock
2025-9-29 15:28:56-debug: Set detail map pack:///resolution-detail-map.json: {}
2025-9-29 15:28:56-debug: Run asset db hook programming:afterPreStart success!
2025-9-29 15:28:56-debug: [[Executor]] prepare after unlock
2025-9-29 15:28:56-debug: Run asset db hook engine-extends:afterPreStart ...
2025-9-29 15:28:56-debug: Run asset db hook engine-extends:afterPreStart success!
2025-9-29 15:28:56-debug: [Assets Memory track]: asset-db:worker-init: preStart start:283.13MB, end 288.06MB, increase: 4.92MB
2025-9-29 15:28:56-debug: Start up the 'internal' database...
2025-9-29 15:28:57-debug: asset-db-hook-programming-afterPreStart (6811ms)
2025-9-29 15:28:57-debug: asset-db:worker-effect-data-processing (220ms)
2025-9-29 15:28:57-debug: asset-db-hook-engine-extends-afterPreStart (220ms)
2025-9-29 15:28:57-debug: Start up the 'assets' database...
2025-9-29 15:28:57-debug: asset-db:worker-startup-database[internal] (7071ms)
2025-9-29 15:28:57-debug: lazy register asset handler *
2025-9-29 15:28:57-debug: lazy register asset handler text
2025-9-29 15:28:57-debug: lazy register asset handler spine-data
2025-9-29 15:28:57-debug: lazy register asset handler json
2025-9-29 15:28:57-debug: lazy register asset handler directory
2025-9-29 15:28:57-debug: lazy register asset handler dragonbones
2025-9-29 15:28:57-debug: lazy register asset handler dragonbones-atlas
2025-9-29 15:28:57-debug: lazy register asset handler terrain
2025-9-29 15:28:57-debug: lazy register asset handler javascript
2025-9-29 15:28:57-debug: lazy register asset handler typescript
2025-9-29 15:28:57-debug: lazy register asset handler scene
2025-9-29 15:28:57-debug: lazy register asset handler sprite-frame
2025-9-29 15:28:57-debug: lazy register asset handler tiled-map
2025-9-29 15:28:57-debug: lazy register asset handler prefab
2025-9-29 15:28:57-debug: lazy register asset handler sign-image
2025-9-29 15:28:57-debug: lazy register asset handler buffer
2025-9-29 15:28:57-debug: lazy register asset handler alpha-image
2025-9-29 15:28:57-debug: lazy register asset handler image
2025-9-29 15:28:57-debug: lazy register asset handler texture-cube
2025-9-29 15:28:57-debug: lazy register asset handler erp-texture-cube
2025-9-29 15:28:57-debug: lazy register asset handler texture
2025-9-29 15:28:57-debug: lazy register asset handler render-texture
2025-9-29 15:28:57-debug: lazy register asset handler rt-sprite-frame
2025-9-29 15:28:57-debug: lazy register asset handler texture-cube-face
2025-9-29 15:28:57-debug: lazy register asset handler gltf
2025-9-29 15:28:57-debug: lazy register asset handler gltf-mesh
2025-9-29 15:28:57-debug: lazy register asset handler gltf-animation
2025-9-29 15:28:57-debug: lazy register asset handler gltf-material
2025-9-29 15:28:57-debug: lazy register asset handler gltf-scene
2025-9-29 15:28:57-debug: lazy register asset handler gltf-embeded-image
2025-9-29 15:28:57-debug: lazy register asset handler fbx
2025-9-29 15:28:57-debug: lazy register asset handler gltf-skeleton
2025-9-29 15:28:57-debug: lazy register asset handler physics-material
2025-9-29 15:28:57-debug: lazy register asset handler material
2025-9-29 15:28:57-debug: lazy register asset handler audio-clip
2025-9-29 15:28:57-debug: lazy register asset handler effect-header
2025-9-29 15:28:57-debug: lazy register asset handler animation-graph
2025-9-29 15:28:57-debug: lazy register asset handler effect
2025-9-29 15:28:57-debug: lazy register asset handler animation-clip
2025-9-29 15:28:57-debug: lazy register asset handler animation-graph-variant
2025-9-29 15:28:57-debug: lazy register asset handler animation-mask
2025-9-29 15:28:57-debug: lazy register asset handler ttf-font
2025-9-29 15:28:57-debug: lazy register asset handler bitmap-font
2025-9-29 15:28:57-debug: lazy register asset handler auto-atlas
2025-9-29 15:28:57-debug: lazy register asset handler label-atlas
2025-9-29 15:28:57-debug: lazy register asset handler sprite-atlas
2025-9-29 15:28:57-debug: lazy register asset handler render-pipeline
2025-9-29 15:28:57-debug: lazy register asset handler render-stage
2025-9-29 15:28:57-debug: lazy register asset handler particle
2025-9-29 15:28:57-debug: lazy register asset handler instantiation-material
2025-9-29 15:28:57-debug: lazy register asset handler render-flow
2025-9-29 15:28:57-debug: lazy register asset handler instantiation-skeleton
2025-9-29 15:28:57-debug: lazy register asset handler instantiation-mesh
2025-9-29 15:28:57-debug: lazy register asset handler video-clip
2025-9-29 15:28:57-debug: lazy register asset handler instantiation-animation
2025-9-29 15:28:57-debug: asset-db:worker-startup-database[assets] (7075ms)
2025-9-29 15:28:57-debug: asset-db:start-database (7150ms)
2025-9-29 15:28:57-debug: asset-db:ready (10023ms)
2025-9-29 15:28:57-debug: fix the bug of updateDefaultUserData
2025-9-29 15:28:57-debug: init worker message success
2025-9-29 15:28:57-debug: programming:execute-script (3ms)
2025-9-29 15:28:57-debug: [Build Memory track]: builder:worker-init start:196.00MB, end 203.59MB, increase: 7.60MB
2025-9-29 15:28:57-debug: builder:worker-init (269ms)
2025-9-29 15:29:05-debug: refresh db internal success
2025-9-29 15:29:05-debug: refresh db i18n success
2025-9-29 15:29:05-debug: refresh db assets success
2025-9-29 15:29:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 15:29:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 15:29:05-debug: asset-db:refresh-all-database (197ms)
2025-9-29 15:29:05-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-29 15:29:05-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 15:29:08-debug: Query all assets info in project
2025-9-29 15:29:08-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-29 15:29:08-debug: Skip compress image, progress: 0%
2025-9-29 15:29:08-debug: Init all bundles start..., progress: 0%
2025-9-29 15:29:08-debug: 查询 Asset Bundle start, progress: 0%
2025-9-29 15:29:08-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 15:29:08-debug: Num of bundles: 17..., progress: 0%
2025-9-29 15:29:08-debug: Init bundle root assets start..., progress: 0%
2025-9-29 15:29:08-debug:   Number of all scenes: 11
2025-9-29 15:29:08-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-29 15:29:08-debug:   Number of all scripts: 275
2025-9-29 15:29:08-debug: Init bundle root assets success..., progress: 0%
2025-9-29 15:29:08-debug:   Number of other assets: 2158
2025-9-29 15:29:08-debug: // ---- build task 查询 Asset Bundle ---- (33ms)
2025-9-29 15:29:08-log: run build task 查询 Asset Bundle success in 33 ms√, progress: 5%
2025-9-29 15:29:08-debug: [Build Memory track]: 查询 Asset Bundle start:202.76MB, end 203.61MB, increase: 872.70KB
2025-9-29 15:29:08-debug: 查询 Asset Bundle start, progress: 5%
2025-9-29 15:29:08-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 15:29:08-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-9-29 15:29:08-debug: [Build Memory track]: 查询 Asset Bundle start:203.65MB, end 203.97MB, increase: 325.71KB
2025-9-29 15:29:08-log: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-9-29 15:29:08-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-29 15:29:08-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 15:29:08-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-29 15:29:08-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-29 15:29:08-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-29 15:29:08-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:204.01MB, end 204.05MB, increase: 41.29KB
2025-9-29 15:29:08-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-29 15:29:08-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-29 15:29:08-debug: [Build Memory track]: 填充脚本数据到 settings.json start:204.09MB, end 204.11MB, increase: 20.31KB
2025-9-29 15:29:08-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 15:29:08-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-29 15:29:08-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-29 15:29:08-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-9-29 15:29:08-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:204.14MB, end 204.44MB, increase: 303.57KB
2025-9-29 15:29:08-debug: Query all assets info in project
2025-9-29 15:29:08-debug: Query all assets info in project
2025-9-29 15:29:08-debug: Query all assets info in project
2025-9-29 15:29:08-debug: Query all assets info in project
2025-9-29 15:29:08-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-29 15:29:08-debug: Skip compress image, progress: 0%
2025-9-29 15:29:08-debug: Skip compress image, progress: 0%
2025-9-29 15:29:08-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-29 15:29:08-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-29 15:29:08-debug: Skip compress image, progress: 0%
2025-9-29 15:29:08-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-29 15:29:08-debug: Skip compress image, progress: 0%
2025-9-29 15:29:08-debug: Init all bundles start..., progress: 0%
2025-9-29 15:29:08-debug: Num of bundles: 17..., progress: 0%
2025-9-29 15:29:08-debug: 查询 Asset Bundle start, progress: 0%
2025-9-29 15:29:08-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 15:29:08-debug: Init bundle root assets start..., progress: 0%
2025-9-29 15:29:08-debug: Init all bundles start..., progress: 0%
2025-9-29 15:29:08-debug: Num of bundles: 17..., progress: 0%
2025-9-29 15:29:08-debug: 查询 Asset Bundle start, progress: 0%
2025-9-29 15:29:08-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 15:29:08-debug: Init bundle root assets start..., progress: 0%
2025-9-29 15:29:08-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-29 15:29:08-debug:   Number of all scripts: 275
2025-9-29 15:29:08-debug:   Number of other assets: 2158
2025-9-29 15:29:08-debug: Init all bundles start..., progress: 0%
2025-9-29 15:29:08-debug: Init bundle root assets success..., progress: 0%
2025-9-29 15:29:08-debug: Num of bundles: 17..., progress: 0%
2025-9-29 15:29:08-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 15:29:08-debug: Init bundle root assets start..., progress: 0%
2025-9-29 15:29:08-debug:   Number of all scenes: 11
2025-9-29 15:29:08-debug: 查询 Asset Bundle start, progress: 0%
2025-9-29 15:29:08-debug: Init all bundles start..., progress: 0%
2025-9-29 15:29:08-debug: Num of bundles: 17..., progress: 0%
2025-9-29 15:29:08-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 15:29:08-debug: Init bundle root assets start..., progress: 0%
2025-9-29 15:29:08-debug: 查询 Asset Bundle start, progress: 0%
2025-9-29 15:29:08-debug: // ---- build task 查询 Asset Bundle ---- (26ms)
2025-9-29 15:29:08-log: run build task 查询 Asset Bundle success in 26 ms√, progress: 5%
2025-9-29 15:29:08-debug: 查询 Asset Bundle start, progress: 5%
2025-9-29 15:29:08-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 15:29:08-debug: [Build Memory track]: 查询 Asset Bundle start:208.85MB, end 208.89MB, increase: 42.95KB
2025-9-29 15:29:08-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-29 15:29:08-debug:   Number of all scripts: 275
2025-9-29 15:29:08-debug:   Number of other assets: 2158
2025-9-29 15:29:08-debug: Init bundle root assets success..., progress: 0%
2025-9-29 15:29:08-debug:   Number of all scenes: 11
2025-9-29 15:29:08-debug: // ---- build task 查询 Asset Bundle ---- (26ms)
2025-9-29 15:29:08-debug: [Build Memory track]: 查询 Asset Bundle start:208.92MB, end 209.47MB, increase: 557.35KB
2025-9-29 15:29:08-debug: 查询 Asset Bundle start, progress: 5%
2025-9-29 15:29:08-log: run build task 查询 Asset Bundle success in 26 ms√, progress: 5%
2025-9-29 15:29:08-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-9-29 15:29:08-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 15:29:08-debug: [Build Memory track]: 查询 Asset Bundle start:209.50MB, end 209.53MB, increase: 23.24KB
2025-9-29 15:29:08-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-29 15:29:08-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 15:29:08-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-29 15:29:08-debug:   Number of all scenes: 11
2025-9-29 15:29:08-debug:   Number of all scripts: 275
2025-9-29 15:29:08-debug:   Number of other assets: 2158
2025-9-29 15:29:08-debug:   Number of all scenes: 11
2025-9-29 15:29:08-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-29 15:29:08-debug: Init bundle root assets success..., progress: 0%
2025-9-29 15:29:08-debug:   Number of other assets: 2158
2025-9-29 15:29:08-debug: Init bundle root assets success..., progress: 0%
2025-9-29 15:29:08-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-29 15:29:08-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-29 15:29:08-debug:   Number of all scripts: 275
2025-9-29 15:29:08-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:209.57MB, end 209.57MB, increase: 3.48KB
2025-9-29 15:29:08-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-29 15:29:08-debug: // ---- build task 查询 Asset Bundle ---- (50ms)
2025-9-29 15:29:08-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-29 15:29:08-log: run build task 查询 Asset Bundle success in 50 ms√, progress: 5%
2025-9-29 15:29:08-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 15:29:08-debug: [Build Memory track]: 查询 Asset Bundle start:209.93MB, end 209.95MB, increase: 14.66KB
2025-9-29 15:29:08-debug: 查询 Asset Bundle start, progress: 5%
2025-9-29 15:29:08-log: run build task 查询 Asset Bundle success in √, progress: 5%
2025-9-29 15:29:08-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 15:29:08-debug: 查询 Asset Bundle start, progress: 5%
2025-9-29 15:29:08-debug: // ---- build task 填充脚本数据到 settings.json ---- (3ms)
2025-9-29 15:29:08-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-29 15:29:08-log: run build task 填充脚本数据到 settings.json success in 3 ms√, progress: 13%
2025-9-29 15:29:08-debug: [Build Memory track]: 填充脚本数据到 settings.json start:209.61MB, end 210.03MB, increase: 426.24KB
2025-9-29 15:29:08-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 15:29:08-debug: [Build Memory track]: 查询 Asset Bundle start:209.98MB, end 210.09MB, increase: 106.99KB
2025-9-29 15:29:08-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 10%
2025-9-29 15:29:08-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-9-29 15:29:08-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 15:29:08-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-29 15:29:08-log: run build task 整理部分构建选项内数据到 settings.json success in 3 ms√, progress: 12%
2025-9-29 15:29:08-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-9-29 15:29:08-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-29 15:29:08-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:210.12MB, end 210.71MB, increase: 604.55KB
2025-9-29 15:29:08-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-29 15:29:08-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-29 15:29:08-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-9-29 15:29:08-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-29 15:29:08-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-9-29 15:29:08-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 15:29:08-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 15:29:08-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-29 15:29:08-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-29 15:29:08-debug: [Build Memory track]: 填充脚本数据到 settings.json start:210.75MB, end 210.86MB, increase: 111.23KB
2025-9-29 15:29:08-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 15:29:08-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-29 15:29:08-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-29 15:29:08-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-29 15:29:08-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-29 15:29:08-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-29 15:29:08-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-29 15:29:08-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:210.89MB, end 210.92MB, increase: 26.89KB
2025-9-29 15:29:08-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-29 15:29:08-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-29 15:29:08-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-29 15:29:08-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-29 15:29:08-debug: [Build Memory track]: 填充脚本数据到 settings.json start:211.00MB, end 211.05MB, increase: 46.15KB
2025-9-29 15:29:08-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 15:29:08-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-29 15:29:08-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-29 15:29:08-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-29 15:29:08-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-29 15:29:08-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 15:29:08-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-29 15:29:08-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:211.13MB, end 211.66MB, increase: 536.91KB
2025-9-29 15:29:08-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-9-29 15:29:08-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-9-29 15:29:08-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-9-29 15:29:08-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-9-29 15:29:58-debug: refresh db internal success
2025-9-29 15:29:58-debug: refresh db i18n success
2025-9-29 15:29:58-debug: refresh db assets success
2025-9-29 15:29:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 15:29:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 15:29:58-debug: asset-db:refresh-all-database (170ms)
2025-9-29 15:29:58-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 15:30:09-debug: Query all assets info in project
2025-9-29 15:30:09-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-29 15:30:09-debug: Skip compress image, progress: 0%
2025-9-29 15:30:09-debug: Init all bundles start..., progress: 0%
2025-9-29 15:30:09-debug: 查询 Asset Bundle start, progress: 0%
2025-9-29 15:30:09-debug: Num of bundles: 17..., progress: 0%
2025-9-29 15:30:09-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 15:30:09-debug: Init bundle root assets start..., progress: 0%
2025-9-29 15:30:09-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-29 15:30:09-debug:   Number of other assets: 2158
2025-9-29 15:30:09-debug: Init bundle root assets success..., progress: 0%
2025-9-29 15:30:09-debug:   Number of all scripts: 275
2025-9-29 15:30:09-debug:   Number of all scenes: 11
2025-9-29 15:30:09-debug: // ---- build task 查询 Asset Bundle ---- (22ms)
2025-9-29 15:30:09-log: run build task 查询 Asset Bundle success in 22 ms√, progress: 5%
2025-9-29 15:30:09-debug: [Build Memory track]: 查询 Asset Bundle start:208.12MB, end 208.96MB, increase: 857.54KB
2025-9-29 15:30:09-debug: 查询 Asset Bundle start, progress: 5%
2025-9-29 15:30:09-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 15:30:09-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-9-29 15:30:09-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 10%
2025-9-29 15:30:09-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 15:30:09-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-29 15:30:09-debug: [Build Memory track]: 查询 Asset Bundle start:208.99MB, end 209.30MB, increase: 316.24KB
2025-9-29 15:30:09-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (-1ms)
2025-9-29 15:30:09-log: run build task 整理部分构建选项内数据到 settings.json success in -1 h -1 min -1 s√, progress: 12%
2025-9-29 15:30:09-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-29 15:30:09-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:209.34MB, end 209.37MB, increase: 31.73KB
2025-9-29 15:30:09-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-29 15:30:09-debug: // ---- build task 填充脚本数据到 settings.json ---- (-1ms)
2025-9-29 15:30:09-log: run build task 填充脚本数据到 settings.json success in -1 h -1 min -1 s√, progress: 13%
2025-9-29 15:30:09-debug: [Build Memory track]: 填充脚本数据到 settings.json start:209.40MB, end 209.43MB, increase: 31.61KB
2025-9-29 15:30:09-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-29 15:30:09-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 15:30:09-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-29 15:30:09-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-9-29 15:30:09-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:209.47MB, end 209.77MB, increase: 302.55KB
2025-9-29 15:30:14-debug: Query all assets info in project
2025-9-29 15:30:14-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-29 15:30:14-debug: Skip compress image, progress: 0%
2025-9-29 15:30:14-debug: Init all bundles start..., progress: 0%
2025-9-29 15:30:14-debug: 查询 Asset Bundle start, progress: 0%
2025-9-29 15:30:14-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 15:30:14-debug: Num of bundles: 17..., progress: 0%
2025-9-29 15:30:14-debug: Init bundle root assets start..., progress: 0%
2025-9-29 15:30:14-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-29 15:30:14-debug:   Number of all scenes: 11
2025-9-29 15:30:14-debug:   Number of all scripts: 275
2025-9-29 15:30:14-debug: Init bundle root assets success..., progress: 0%
2025-9-29 15:30:14-debug:   Number of other assets: 2158
2025-9-29 15:30:14-debug: // ---- build task 查询 Asset Bundle ---- (19ms)
2025-9-29 15:30:14-log: run build task 查询 Asset Bundle success in 19 ms√, progress: 5%
2025-9-29 15:30:14-debug: [Build Memory track]: 查询 Asset Bundle start:212.52MB, end 211.48MB, increase: -1066.03KB
2025-9-29 15:30:14-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 15:30:14-debug: 查询 Asset Bundle start, progress: 5%
2025-9-29 15:30:14-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-9-29 15:30:14-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 10%
2025-9-29 15:30:14-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-29 15:30:14-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 15:30:14-debug: [Build Memory track]: 查询 Asset Bundle start:211.51MB, end 211.83MB, increase: 322.13KB
2025-9-29 15:30:14-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (-1ms)
2025-9-29 15:30:14-log: run build task 整理部分构建选项内数据到 settings.json success in -1 h -1 min -1 s√, progress: 12%
2025-9-29 15:30:14-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:211.87MB, end 211.90MB, increase: 32.25KB
2025-9-29 15:30:14-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-29 15:30:14-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-29 15:30:14-debug: // ---- build task 填充脚本数据到 settings.json ---- (-1ms)
2025-9-29 15:30:14-debug: [Build Memory track]: 填充脚本数据到 settings.json start:211.94MB, end 211.97MB, increase: 32.68KB
2025-9-29 15:30:14-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-29 15:30:14-log: run build task 填充脚本数据到 settings.json success in -1 h -1 min -1 s√, progress: 13%
2025-9-29 15:30:14-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 15:30:14-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-9-29 15:30:14-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:212.00MB, end 212.29MB, increase: 287.18KB
2025-9-29 15:30:14-debug: Query all assets info in project
2025-9-29 15:30:14-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-29 15:30:14-debug: Skip compress image, progress: 0%
2025-9-29 15:30:14-debug: Init all bundles start..., progress: 0%
2025-9-29 15:30:14-debug: Num of bundles: 17..., progress: 0%
2025-9-29 15:30:14-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 15:30:14-debug: 查询 Asset Bundle start, progress: 0%
2025-9-29 15:30:14-debug: Init bundle root assets start..., progress: 0%
2025-9-29 15:30:14-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-29 15:30:14-debug:   Number of all scripts: 275
2025-9-29 15:30:14-debug:   Number of other assets: 2158
2025-9-29 15:30:14-debug: Init bundle root assets success..., progress: 0%
2025-9-29 15:30:14-debug:   Number of all scenes: 11
2025-9-29 15:30:14-log: run build task 查询 Asset Bundle success in 19 ms√, progress: 5%
2025-9-29 15:30:14-debug: [Build Memory track]: 查询 Asset Bundle start:214.59MB, end 213.68MB, increase: -933.23KB
2025-9-29 15:30:14-debug: 查询 Asset Bundle start, progress: 5%
2025-9-29 15:30:14-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 15:30:14-debug: // ---- build task 查询 Asset Bundle ---- (19ms)
2025-9-29 15:30:14-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-9-29 15:30:14-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 10%
2025-9-29 15:30:14-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-29 15:30:14-debug: [Build Memory track]: 查询 Asset Bundle start:213.71MB, end 214.01MB, increase: 312.66KB
2025-9-29 15:30:14-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 15:30:14-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (-1ms)
2025-9-29 15:30:14-log: run build task 整理部分构建选项内数据到 settings.json success in -1 h -1 min -1 s√, progress: 12%
2025-9-29 15:30:14-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:214.04MB, end 214.07MB, increase: 26.63KB
2025-9-29 15:30:14-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-29 15:30:14-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-29 15:30:14-debug: // ---- build task 填充脚本数据到 settings.json ---- (-1ms)
2025-9-29 15:30:14-log: run build task 填充脚本数据到 settings.json success in -1 h -1 min -1 s√, progress: 13%
2025-9-29 15:30:14-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-29 15:30:14-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 15:30:14-debug: [Build Memory track]: 填充脚本数据到 settings.json start:214.10MB, end 214.12MB, increase: 26.56KB
2025-9-29 15:30:14-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-9-29 15:30:14-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:214.15MB, end 214.43MB, increase: 286.42KB
2025-9-29 15:30:43-debug: refresh db internal success
2025-9-29 15:30:43-debug: refresh db i18n success
2025-9-29 15:30:44-debug: refresh db assets success
2025-9-29 15:30:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 15:30:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 15:30:44-debug: asset-db:refresh-all-database (163ms)
2025-9-29 15:30:44-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-29 15:30:44-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 15:30:59-debug: refresh db internal success
2025-9-29 15:30:59-debug: refresh db i18n success
2025-9-29 15:30:59-debug: refresh db assets success
2025-9-29 15:30:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 15:30:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 15:30:59-debug: asset-db:refresh-all-database (131ms)
2025-9-29 15:30:59-debug: asset-db:worker-effect-data-processing (3ms)
2025-9-29 15:30:59-debug: asset-db-hook-engine-extends-afterRefresh (4ms)
2025-9-29 15:31:06-debug: Query all assets info in project
2025-9-29 15:31:06-debug: Skip compress image, progress: 0%
2025-9-29 15:31:06-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-29 15:31:06-debug: Init all bundles start..., progress: 0%
2025-9-29 15:31:06-debug: Num of bundles: 17..., progress: 0%
2025-9-29 15:31:06-debug: 查询 Asset Bundle start, progress: 0%
2025-9-29 15:31:06-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 15:31:06-debug: Init bundle root assets start..., progress: 0%
2025-9-29 15:31:06-debug:   Number of all scripts: 275
2025-9-29 15:31:06-debug:   Number of other assets: 2158
2025-9-29 15:31:06-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-29 15:31:06-debug: Init bundle root assets success..., progress: 0%
2025-9-29 15:31:06-debug:   Number of all scenes: 11
2025-9-29 15:31:06-debug: // ---- build task 查询 Asset Bundle ---- (26ms)
2025-9-29 15:31:06-log: run build task 查询 Asset Bundle success in 26 ms√, progress: 5%
2025-9-29 15:31:06-debug: 查询 Asset Bundle start, progress: 5%
2025-9-29 15:31:06-debug: [Build Memory track]: 查询 Asset Bundle start:202.39MB, end 204.23MB, increase: 1.84MB
2025-9-29 15:31:06-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 15:31:06-debug: [Build Memory track]: 查询 Asset Bundle start:204.26MB, end 204.58MB, increase: 320.74KB
2025-9-29 15:31:06-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-9-29 15:31:06-log: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-9-29 15:31:06-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-29 15:31:06-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 15:31:06-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-29 15:31:06-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 12%
2025-9-29 15:31:06-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-29 15:31:06-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:204.61MB, end 204.64MB, increase: 27.35KB
2025-9-29 15:31:06-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-29 15:31:06-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-29 15:31:06-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-29 15:31:06-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 15:31:06-debug: [Build Memory track]: 填充脚本数据到 settings.json start:204.66MB, end 204.69MB, increase: 27.75KB
2025-9-29 15:31:06-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-29 15:31:06-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-9-29 15:31:06-log: run build task 整理部分构建选项内数据到 settings.json success in 3 ms√, progress: 15%
2025-9-29 15:31:06-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:204.72MB, end 205.05MB, increase: 333.11KB
2025-9-29 15:32:05-debug: refresh db internal success
2025-9-29 15:32:05-debug: refresh db i18n success
2025-9-29 15:32:05-debug: refresh db assets success
2025-9-29 15:32:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 15:32:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 15:32:05-debug: asset-db:refresh-all-database (159ms)
2025-9-29 15:32:05-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 15:32:05-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 15:32:38-debug: refresh db internal success
2025-9-29 15:32:38-debug: refresh db i18n success
2025-9-29 15:32:38-debug: refresh db assets success
2025-9-29 15:32:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 15:32:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 15:32:38-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 15:32:38-debug: asset-db:refresh-all-database (129ms)
2025-9-29 15:32:38-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 15:32:43-debug: refresh db internal success
2025-9-29 15:32:43-debug: refresh db i18n success
2025-9-29 15:32:43-debug: refresh db assets success
2025-9-29 15:32:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 15:32:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 15:32:43-debug: asset-db:refresh-all-database (122ms)
2025-9-29 15:32:43-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 15:32:43-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 15:33:42-debug: refresh db internal success
2025-9-29 15:33:42-debug: refresh db i18n success
2025-9-29 15:33:42-debug: refresh db assets success
2025-9-29 15:33:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 15:33:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 15:33:42-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 15:33:42-debug: asset-db:refresh-all-database (167ms)
2025-9-29 15:33:42-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 15:49:29-debug: refresh db internal success
2025-9-29 15:49:29-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\spine
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 15:49:29-debug: refresh db i18n success
2025-9-29 15:49:29-debug: %cImport%c: E:\M2Game\Client\assets\resources
background: #aaff85; color: #000;
color: #000;
2025-9-29 15:49:29-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\top_ui\coin_btn.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 15:49:29-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\top_ui\energy_btn.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 15:49:29-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\top_ui\diamond_btn.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 15:49:29-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\top_ui\diamond_btn.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 15:49:29-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\top_ui\energy_btn.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 15:49:29-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\top_ui\coin_btn.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 15:49:29-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\top_ui\diamond_btn.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 15:49:29-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\top_ui\energy_btn.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 15:49:29-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\top_ui\coin_btn.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 15:49:29-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui
background: #aaff85; color: #000;
color: #000;
2025-9-29 15:49:29-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\top_ui
background: #aaff85; color: #000;
color: #000;
2025-9-29 15:49:29-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\plane
background: #aaff85; color: #000;
color: #000;
2025-9-29 15:49:29-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home
background: #aaff85; color: #000;
color: #000;
2025-9-29 15:49:29-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\HomeUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-29 15:49:29-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\TopUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-29 15:49:29-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\plane\PlaneData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 15:49:29-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home\HomeUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 15:49:29-debug: refresh db assets success
2025-9-29 15:49:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 15:49:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 15:49:29-debug: asset-db:refresh-all-database (197ms)
2025-9-29 15:49:29-debug: asset-db:worker-effect-data-processing (-1ms)
2025-9-29 15:49:29-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000002.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 15:49:29-debug: asset-db:reimport-assetf3b50cd4-91d4-42ff-a57b-6bec3e0e750e (7ms)
2025-9-29 15:49:30-debug: Query all assets info in project
2025-9-29 15:49:30-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-29 15:49:30-debug: Skip compress image, progress: 0%
2025-9-29 15:49:30-debug: Num of bundles: 17..., progress: 0%
2025-9-29 15:49:30-debug: 查询 Asset Bundle start, progress: 0%
2025-9-29 15:49:30-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 15:49:30-debug: Init all bundles start..., progress: 0%
2025-9-29 15:49:30-debug: Init bundle root assets start..., progress: 0%
2025-9-29 15:49:30-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-29 15:49:30-debug:   Number of all scenes: 11
2025-9-29 15:49:30-debug:   Number of other assets: 2158
2025-9-29 15:49:30-debug: Init bundle root assets success..., progress: 0%
2025-9-29 15:49:30-debug:   Number of all scripts: 275
2025-9-29 15:49:30-log: run build task 查询 Asset Bundle success in 31 ms√, progress: 5%
2025-9-29 15:49:30-debug: [Build Memory track]: 查询 Asset Bundle start:211.77MB, end 207.42MB, increase: -4453.12KB
2025-9-29 15:49:30-debug: // ---- build task 查询 Asset Bundle ---- (31ms)
2025-9-29 15:49:30-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 15:49:30-debug: 查询 Asset Bundle start, progress: 5%
2025-9-29 15:49:30-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-9-29 15:49:30-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-9-29 15:49:30-debug: [Build Memory track]: 查询 Asset Bundle start:207.45MB, end 207.75MB, increase: 311.11KB
2025-9-29 15:49:30-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-29 15:49:30-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 15:49:30-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-9-29 15:49:30-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-29 15:49:30-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:207.79MB, end 207.81MB, increase: 17.89KB
2025-9-29 15:49:30-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-29 15:49:30-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-29 15:49:30-debug: [Build Memory track]: 填充脚本数据到 settings.json start:207.83MB, end 207.86MB, increase: 21.29KB
2025-9-29 15:49:30-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-29 15:49:30-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 15:49:30-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-29 15:49:30-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:207.88MB, end 208.18MB, increase: 298.86KB
2025-9-29 15:49:30-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-9-29 15:50:23-debug: refresh db internal success
2025-9-29 15:50:23-debug: refresh db i18n success
2025-9-29 15:50:23-debug: refresh db assets success
2025-9-29 15:50:23-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 15:50:23-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 15:50:23-debug: asset-db:refresh-all-database (125ms)
2025-9-29 15:50:32-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000002.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 15:50:32-debug: asset-db:reimport-assetf3b50cd4-91d4-42ff-a57b-6bec3e0e750e (4ms)
2025-9-29 15:50:52-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000002.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 15:50:52-debug: asset-db:reimport-assetf3b50cd4-91d4-42ff-a57b-6bec3e0e750e (5ms)
2025-9-29 15:51:09-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000002.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 15:51:09-debug: asset-db:reimport-assetf3b50cd4-91d4-42ff-a57b-6bec3e0e750e (6ms)
2025-9-29 15:51:09-debug: refresh db internal success
2025-9-29 15:51:09-debug: refresh db i18n success
2025-9-29 15:51:09-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 15:51:09-debug: refresh db assets success
2025-9-29 15:51:09-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 15:51:09-debug: asset-db:refresh-all-database (148ms)
2025-9-29 15:51:09-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-29 15:51:09-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 15:52:08-debug: refresh db internal success
2025-9-29 15:52:08-debug: refresh db i18n success
2025-9-29 15:52:08-debug: refresh db assets success
2025-9-29 15:52:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 15:52:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 15:52:08-debug: asset-db:refresh-all-database (175ms)
2025-9-29 15:52:08-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-29 15:52:08-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 15:52:14-debug: Query all assets info in project
2025-9-29 15:52:14-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-29 15:52:14-debug: Skip compress image, progress: 0%
2025-9-29 15:52:14-debug: Init all bundles start..., progress: 0%
2025-9-29 15:52:14-debug: Num of bundles: 17..., progress: 0%
2025-9-29 15:52:14-debug: Init bundle root assets start..., progress: 0%
2025-9-29 15:52:14-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 15:52:14-debug: 查询 Asset Bundle start, progress: 0%
2025-9-29 15:52:14-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-29 15:52:14-debug:   Number of all scenes: 11
2025-9-29 15:52:14-debug:   Number of all scripts: 275
2025-9-29 15:52:14-debug:   Number of other assets: 2158
2025-9-29 15:52:14-debug: Init bundle root assets success..., progress: 0%
2025-9-29 15:52:14-debug: // ---- build task 查询 Asset Bundle ---- (26ms)
2025-9-29 15:52:14-log: run build task 查询 Asset Bundle success in 26 ms√, progress: 5%
2025-9-29 15:52:14-debug: 查询 Asset Bundle start, progress: 5%
2025-9-29 15:52:14-debug: [Build Memory track]: 查询 Asset Bundle start:205.81MB, end 204.21MB, increase: -1641.33KB
2025-9-29 15:52:14-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 15:52:14-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-9-29 15:52:14-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-9-29 15:52:14-debug: [Build Memory track]: 查询 Asset Bundle start:204.24MB, end 204.59MB, increase: 364.61KB
2025-9-29 15:52:14-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-29 15:52:14-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 15:52:14-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-29 15:52:14-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-29 15:52:14-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:204.62MB, end 204.65MB, increase: 25.78KB
2025-9-29 15:52:14-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-29 15:52:14-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-29 15:52:14-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-29 15:52:14-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-29 15:52:14-debug: [Build Memory track]: 填充脚本数据到 settings.json start:204.67MB, end 204.70MB, increase: 26.34KB
2025-9-29 15:52:14-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-29 15:52:14-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 15:52:14-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-9-29 15:52:14-log: run build task 整理部分构建选项内数据到 settings.json success in 3 ms√, progress: 15%
2025-9-29 15:52:14-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:204.73MB, end 205.03MB, increase: 305.75KB
2025-9-29 15:52:15-debug: Query all assets info in project
2025-9-29 15:52:15-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-29 15:52:15-debug: Skip compress image, progress: 0%
2025-9-29 15:52:15-debug: Init all bundles start..., progress: 0%
2025-9-29 15:52:15-debug: Num of bundles: 17..., progress: 0%
2025-9-29 15:52:15-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 15:52:15-debug: 查询 Asset Bundle start, progress: 0%
2025-9-29 15:52:15-debug: Init bundle root assets start..., progress: 0%
2025-9-29 15:52:15-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-29 15:52:15-debug:   Number of all scenes: 11
2025-9-29 15:52:15-debug:   Number of all scripts: 275
2025-9-29 15:52:15-debug:   Number of other assets: 2158
2025-9-29 15:52:15-debug: Init bundle root assets success..., progress: 0%
2025-9-29 15:52:15-log: run build task 查询 Asset Bundle success in 23 ms√, progress: 5%
2025-9-29 15:52:15-debug: // ---- build task 查询 Asset Bundle ---- (23ms)
2025-9-29 15:52:15-debug: [Build Memory track]: 查询 Asset Bundle start:204.67MB, end 206.96MB, increase: 2.29MB
2025-9-29 15:52:15-debug: 查询 Asset Bundle start, progress: 5%
2025-9-29 15:52:15-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 15:52:15-log: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-9-29 15:52:15-debug: [Build Memory track]: 查询 Asset Bundle start:206.99MB, end 207.29MB, increase: 310.98KB
2025-9-29 15:52:15-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-9-29 15:52:15-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 15:52:15-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-29 15:52:15-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-29 15:52:15-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-29 15:52:15-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-29 15:52:15-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:207.32MB, end 207.35MB, increase: 25.77KB
2025-9-29 15:52:15-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-29 15:52:15-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-29 15:52:15-debug: [Build Memory track]: 填充脚本数据到 settings.json start:207.37MB, end 207.40MB, increase: 25.89KB
2025-9-29 15:52:15-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-29 15:52:15-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-29 15:52:15-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 15:52:15-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-29 15:52:15-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-9-29 15:52:15-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:207.43MB, end 207.71MB, increase: 292.14KB
2025-9-29 15:52:20-debug: Query all assets info in project
2025-9-29 15:52:20-debug: refresh db internal success
2025-9-29 15:52:20-debug: refresh db i18n success
2025-9-29 15:52:20-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-29 15:52:20-debug: Skip compress image, progress: 0%
2025-9-29 15:52:20-debug: Init all bundles start..., progress: 0%
2025-9-29 15:52:20-debug: 查询 Asset Bundle start, progress: 0%
2025-9-29 15:52:20-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 15:52:20-debug: Num of bundles: 17..., progress: 0%
2025-9-29 15:52:20-debug: Init bundle root assets start..., progress: 0%
2025-9-29 15:52:20-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-29 15:52:20-debug:   Number of all scripts: 275
2025-9-29 15:52:20-debug:   Number of other assets: 2158
2025-9-29 15:52:20-debug:   Number of all scenes: 11
2025-9-29 15:52:20-debug: Init bundle root assets success..., progress: 0%
2025-9-29 15:52:20-debug: // ---- build task 查询 Asset Bundle ---- (24ms)
2025-9-29 15:52:20-debug: 查询 Asset Bundle start, progress: 5%
2025-9-29 15:52:20-log: run build task 查询 Asset Bundle success in 24 ms√, progress: 5%
2025-9-29 15:52:20-debug: [Build Memory track]: 查询 Asset Bundle start:209.06MB, end 210.13MB, increase: 1.08MB
2025-9-29 15:52:20-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 15:52:20-log: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-9-29 15:52:20-debug: [Build Memory track]: 查询 Asset Bundle start:210.16MB, end 210.60MB, increase: 450.97KB
2025-9-29 15:52:20-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 15:52:20-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-29 15:52:20-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-9-29 15:52:20-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-29 15:52:20-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-29 15:52:20-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:210.63MB, end 210.70MB, increase: 78.89KB
2025-9-29 15:52:20-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-29 15:52:20-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-29 15:52:20-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-29 15:52:20-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-29 15:52:20-debug: [Build Memory track]: 填充脚本数据到 settings.json start:210.73MB, end 210.79MB, increase: 60.86KB
2025-9-29 15:52:20-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-29 15:52:20-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 15:52:20-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (7ms)
2025-9-29 15:52:20-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:210.82MB, end 211.63MB, increase: 832.56KB
2025-9-29 15:52:20-log: run build task 整理部分构建选项内数据到 settings.json success in 7 ms√, progress: 15%
2025-9-29 15:52:20-debug: refresh db assets success
2025-9-29 15:52:20-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 15:52:20-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 15:52:20-debug: asset-db:refresh-all-database (173ms)
2025-9-29 15:52:20-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 15:52:20-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 15:53:44-debug: refresh db internal success
2025-9-29 15:53:44-debug: refresh db i18n success
2025-9-29 15:53:44-debug: refresh db assets success
2025-9-29 15:53:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 15:53:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 15:53:44-debug: asset-db:refresh-all-database (124ms)
2025-9-29 15:53:44-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 15:53:44-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 15:53:44-debug: Query all assets info in project
2025-9-29 15:53:44-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-29 15:53:44-debug: Skip compress image, progress: 0%
2025-9-29 15:53:44-debug: Init all bundles start..., progress: 0%
2025-9-29 15:53:44-debug: Num of bundles: 17..., progress: 0%
2025-9-29 15:53:44-debug: 查询 Asset Bundle start, progress: 0%
2025-9-29 15:53:44-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 15:53:44-debug: Init bundle root assets start..., progress: 0%
2025-9-29 15:53:44-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-29 15:53:44-debug:   Number of all scenes: 11
2025-9-29 15:53:44-debug:   Number of other assets: 2158
2025-9-29 15:53:44-debug: Init bundle root assets success..., progress: 0%
2025-9-29 15:53:44-debug:   Number of all scripts: 275
2025-9-29 15:53:44-debug: // ---- build task 查询 Asset Bundle ---- (21ms)
2025-9-29 15:53:44-debug: [Build Memory track]: 查询 Asset Bundle start:211.31MB, end 212.06MB, increase: 773.65KB
2025-9-29 15:53:44-debug: 查询 Asset Bundle start, progress: 5%
2025-9-29 15:53:44-log: run build task 查询 Asset Bundle success in 21 ms√, progress: 5%
2025-9-29 15:53:44-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 15:53:44-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-9-29 15:53:44-log: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-9-29 15:53:44-debug: [Build Memory track]: 查询 Asset Bundle start:212.09MB, end 212.40MB, increase: 321.23KB
2025-9-29 15:53:44-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-29 15:53:44-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 15:53:44-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-29 15:53:44-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:212.43MB, end 212.46MB, increase: 26.16KB
2025-9-29 15:53:44-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-29 15:53:44-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-29 15:53:44-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-29 15:53:44-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-29 15:53:44-debug: [Build Memory track]: 填充脚本数据到 settings.json start:212.49MB, end 212.52MB, increase: 32.60KB
2025-9-29 15:53:44-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-29 15:53:44-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-29 15:53:44-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 15:53:44-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-9-29 15:53:44-log: run build task 整理部分构建选项内数据到 settings.json success in 3 ms√, progress: 15%
2025-9-29 15:53:44-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:212.55MB, end 212.83MB, increase: 291.16KB
2025-9-29 15:54:18-debug: refresh db internal success
2025-9-29 15:54:18-debug: refresh db i18n success
2025-9-29 15:54:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 15:54:19-debug: refresh db assets success
2025-9-29 15:54:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 15:54:19-debug: asset-db:refresh-all-database (127ms)
2025-9-29 15:54:19-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 15:54:19-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 15:54:36-debug: refresh db internal success
2025-9-29 15:54:36-debug: refresh db i18n success
2025-9-29 15:54:36-debug: refresh db assets success
2025-9-29 15:54:36-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 15:54:36-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 15:54:36-debug: asset-db:refresh-all-database (121ms)
2025-9-29 15:54:36-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 15:54:36-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 15:54:42-debug: refresh db internal success
2025-9-29 15:54:42-debug: refresh db i18n success
2025-9-29 15:54:42-debug: refresh db assets success
2025-9-29 15:54:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 15:54:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 15:54:42-debug: asset-db:refresh-all-database (132ms)
2025-9-29 15:54:42-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 15:54:42-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 15:55:22-debug: refresh db internal success
2025-9-29 15:55:22-debug: refresh db i18n success
2025-9-29 15:55:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 15:55:22-debug: refresh db assets success
2025-9-29 15:55:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 15:55:22-debug: asset-db:refresh-all-database (152ms)
2025-9-29 15:55:30-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\PlaneBase.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 15:55:30-debug: asset-db:reimport-asset8c01b9ec-ee15-4803-a6ba-aaad36e4381d (1ms)
2025-9-29 15:55:50-debug: refresh db internal success
2025-9-29 15:55:50-debug: refresh db i18n success
2025-9-29 15:55:50-debug: refresh db assets success
2025-9-29 15:55:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 15:55:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 15:55:50-debug: asset-db:refresh-all-database (117ms)
2025-9-29 15:55:50-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 15:55:50-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 15:55:52-debug: refresh db internal success
2025-9-29 15:55:52-debug: refresh db i18n success
2025-9-29 15:55:52-debug: refresh db assets success
2025-9-29 15:55:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 15:55:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 15:55:52-debug: asset-db:refresh-all-database (119ms)
2025-9-29 15:55:52-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 15:55:52-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 15:56:27-debug: refresh db internal success
2025-9-29 15:56:27-debug: refresh db i18n success
2025-9-29 15:56:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 15:56:27-debug: refresh db assets success
2025-9-29 15:56:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 15:56:27-debug: asset-db:refresh-all-database (159ms)
2025-9-29 15:56:27-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 15:56:27-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 15:56:34-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-29 15:56:34-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-29 15:56:41-debug: refresh db internal success
2025-9-29 15:56:41-debug: refresh db i18n success
2025-9-29 15:56:41-debug: refresh db assets success
2025-9-29 15:56:41-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 15:56:41-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 15:56:41-debug: asset-db:refresh-all-database (117ms)
2025-9-29 15:56:41-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 15:56:41-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 15:56:43-debug: refresh db internal success
2025-9-29 15:56:43-debug: refresh db i18n success
2025-9-29 15:56:43-debug: refresh db assets success
2025-9-29 15:56:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 15:56:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 15:56:43-debug: asset-db:refresh-all-database (118ms)
2025-9-29 15:56:43-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 15:57:46-debug: refresh db internal success
2025-9-29 15:57:46-debug: refresh db i18n success
2025-9-29 15:57:46-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 15:57:46-debug: refresh db assets success
2025-9-29 15:57:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 15:57:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 15:57:46-debug: asset-db:refresh-all-database (160ms)
2025-9-29 15:57:46-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 15:57:46-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 15:58:09-debug: refresh db internal success
2025-9-29 15:58:09-debug: refresh db i18n success
2025-9-29 15:58:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 15:58:10-debug: refresh db assets success
2025-9-29 15:58:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 15:58:10-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 15:58:10-debug: asset-db:refresh-all-database (159ms)
2025-9-29 15:58:10-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 15:59:51-debug: refresh db internal success
2025-9-29 15:59:51-debug: refresh db i18n success
2025-9-29 15:59:51-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 15:59:51-debug: refresh db assets success
2025-9-29 15:59:51-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 15:59:51-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 15:59:51-debug: asset-db:refresh-all-database (128ms)
2025-9-29 15:59:51-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 15:59:51-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 16:02:07-debug: refresh db internal success
2025-9-29 16:02:07-debug: refresh db i18n success
2025-9-29 16:02:07-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:02:07-debug: refresh db assets success
2025-9-29 16:02:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:02:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:02:07-debug: asset-db:refresh-all-database (157ms)
2025-9-29 16:02:07-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:02:07-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:02:57-debug: refresh db internal success
2025-9-29 16:02:57-debug: refresh db i18n success
2025-9-29 16:02:57-debug: refresh db assets success
2025-9-29 16:02:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:02:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:02:57-debug: asset-db:refresh-all-database (127ms)
2025-9-29 16:02:57-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-29 16:02:57-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 16:03:01-debug: Query all assets info in project
2025-9-29 16:03:01-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-29 16:03:01-debug: Skip compress image, progress: 0%
2025-9-29 16:03:01-debug: Init all bundles start..., progress: 0%
2025-9-29 16:03:01-debug: 查询 Asset Bundle start, progress: 0%
2025-9-29 16:03:01-debug: Num of bundles: 17..., progress: 0%
2025-9-29 16:03:01-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 16:03:01-debug: Init bundle root assets start..., progress: 0%
2025-9-29 16:03:01-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-29 16:03:01-debug:   Number of all scenes: 11
2025-9-29 16:03:01-debug:   Number of all scripts: 275
2025-9-29 16:03:01-debug: Init bundle root assets success..., progress: 0%
2025-9-29 16:03:01-log: run build task 查询 Asset Bundle success in 22 ms√, progress: 5%
2025-9-29 16:03:01-debug: [Build Memory track]: 查询 Asset Bundle start:219.71MB, end 220.62MB, increase: 931.43KB
2025-9-29 16:03:01-debug:   Number of other assets: 2158
2025-9-29 16:03:01-debug: // ---- build task 查询 Asset Bundle ---- (22ms)
2025-9-29 16:03:01-debug: 查询 Asset Bundle start, progress: 5%
2025-9-29 16:03:01-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 16:03:01-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-9-29 16:03:01-log: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-9-29 16:03:01-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-29 16:03:01-debug: [Build Memory track]: 查询 Asset Bundle start:220.65MB, end 220.96MB, increase: 311.09KB
2025-9-29 16:03:01-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 16:03:01-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:220.98MB, end 221.01MB, increase: 26.01KB
2025-9-29 16:03:01-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-29 16:03:01-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-29 16:03:01-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 12%
2025-9-29 16:03:01-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-29 16:03:01-log: run build task 填充脚本数据到 settings.json success in 2 ms√, progress: 13%
2025-9-29 16:03:01-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-29 16:03:01-debug: [Build Memory track]: 填充脚本数据到 settings.json start:221.04MB, end 221.06MB, increase: 26.97KB
2025-9-29 16:03:01-debug: // ---- build task 填充脚本数据到 settings.json ---- (2ms)
2025-9-29 16:03:01-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 16:03:01-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-9-29 16:03:01-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:221.09MB, end 221.38MB, increase: 295.88KB
2025-9-29 16:03:01-log: run build task 整理部分构建选项内数据到 settings.json success in 3 ms√, progress: 15%
2025-9-29 16:03:20-debug: refresh db internal success
2025-9-29 16:03:20-debug: refresh db i18n success
2025-9-29 16:03:20-debug: refresh db assets success
2025-9-29 16:03:20-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:03:20-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:03:20-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:03:20-debug: asset-db:refresh-all-database (180ms)
2025-9-29 16:03:20-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:03:22-debug: refresh db internal success
2025-9-29 16:03:22-debug: refresh db i18n success
2025-9-29 16:03:22-debug: refresh db assets success
2025-9-29 16:03:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:03:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:03:22-debug: asset-db:refresh-all-database (155ms)
2025-9-29 16:03:22-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:03:22-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:03:24-debug: refresh db internal success
2025-9-29 16:03:24-debug: refresh db i18n success
2025-9-29 16:03:24-debug: refresh db assets success
2025-9-29 16:03:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:03:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:03:24-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-29 16:03:24-debug: asset-db:refresh-all-database (165ms)
2025-9-29 16:03:24-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 16:03:29-debug: refresh db internal success
2025-9-29 16:03:29-debug: refresh db i18n success
2025-9-29 16:03:29-debug: refresh db assets success
2025-9-29 16:03:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:03:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:03:29-debug: asset-db:refresh-all-database (116ms)
2025-9-29 16:03:29-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-29 16:03:29-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 16:04:22-debug: refresh db internal success
2025-9-29 16:04:22-debug: refresh db i18n success
2025-9-29 16:04:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:04:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:04:22-debug: refresh db assets success
2025-9-29 16:04:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:04:22-debug: asset-db:refresh-all-database (159ms)
2025-9-29 16:04:22-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:04:22-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 16:04:22-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000002.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:04:22-debug: asset-db:reimport-assetf3b50cd4-91d4-42ff-a57b-6bec3e0e750e (6ms)
2025-9-29 16:05:37-debug: refresh db internal success
2025-9-29 16:05:37-debug: refresh db i18n success
2025-9-29 16:05:38-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:05:38-debug: refresh db assets success
2025-9-29 16:05:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:05:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:05:38-debug: asset-db:refresh-all-database (162ms)
2025-9-29 16:05:38-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:05:38-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 16:05:38-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000002.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:05:38-debug: asset-db:reimport-assetf3b50cd4-91d4-42ff-a57b-6bec3e0e750e (7ms)
2025-9-29 16:06:14-debug: refresh db internal success
2025-9-29 16:06:14-debug: refresh db i18n success
2025-9-29 16:06:14-debug: refresh db assets success
2025-9-29 16:06:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:06:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:06:14-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:06:14-debug: asset-db:refresh-all-database (160ms)
2025-9-29 16:06:14-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:06:45-debug: refresh db internal success
2025-9-29 16:06:45-debug: refresh db i18n success
2025-9-29 16:06:45-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:06:45-debug: refresh db assets success
2025-9-29 16:06:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:06:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:06:45-debug: asset-db:refresh-all-database (130ms)
2025-9-29 16:06:45-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:06:45-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:06:46-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000002.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:06:46-debug: asset-db:reimport-assetf3b50cd4-91d4-42ff-a57b-6bec3e0e750e (5ms)
2025-9-29 16:08:02-debug: refresh db internal success
2025-9-29 16:08:02-debug: refresh db i18n success
2025-9-29 16:08:02-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:08:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:08:03-debug: refresh db assets success
2025-9-29 16:08:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:08:03-debug: asset-db:refresh-all-database (165ms)
2025-9-29 16:08:03-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:08:03-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:08:03-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000002.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:08:03-debug: asset-db:reimport-assetf3b50cd4-91d4-42ff-a57b-6bec3e0e750e (6ms)
2025-9-29 16:08:33-debug: refresh db internal success
2025-9-29 16:08:33-debug: refresh db i18n success
2025-9-29 16:08:33-debug: refresh db assets success
2025-9-29 16:08:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:08:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:08:33-debug: asset-db:refresh-all-database (122ms)
2025-9-29 16:08:33-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:08:37-debug: refresh db internal success
2025-9-29 16:08:37-debug: refresh db i18n success
2025-9-29 16:08:37-debug: refresh db assets success
2025-9-29 16:08:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:08:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:08:37-debug: asset-db:refresh-all-database (119ms)
2025-9-29 16:08:37-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:08:40-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:08:40-debug: asset-db:reimport-asset401efd7e-bd20-4537-a13a-f25e6238c2a9 (5ms)
2025-9-29 16:08:44-debug: refresh db internal success
2025-9-29 16:08:44-debug: refresh db i18n success
2025-9-29 16:08:44-debug: refresh db assets success
2025-9-29 16:08:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:08:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:08:44-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:08:44-debug: asset-db:refresh-all-database (123ms)
2025-9-29 16:08:44-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:08:45-debug: refresh db internal success
2025-9-29 16:08:45-debug: refresh db i18n success
2025-9-29 16:08:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:08:45-debug: refresh db assets success
2025-9-29 16:08:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:08:45-debug: asset-db:refresh-all-database (128ms)
2025-9-29 16:09:05-debug: refresh db internal success
2025-9-29 16:09:05-debug: refresh db i18n success
2025-9-29 16:09:05-debug: refresh db assets success
2025-9-29 16:09:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:09:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:09:05-debug: asset-db:refresh-all-database (154ms)
2025-9-29 16:09:05-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:09:05-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:10:56-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:10:56-debug: asset-db:reimport-asset401efd7e-bd20-4537-a13a-f25e6238c2a9 (5ms)
2025-9-29 16:10:59-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000002.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:10:59-debug: asset-db:reimport-assetf3b50cd4-91d4-42ff-a57b-6bec3e0e750e (4ms)
2025-9-29 16:11:27-debug: refresh db internal success
2025-9-29 16:11:27-debug: refresh db i18n success
2025-9-29 16:11:27-debug: refresh db assets success
2025-9-29 16:11:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:11:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:11:27-debug: asset-db:refresh-all-database (154ms)
2025-9-29 16:11:27-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:11:27-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:16:40-debug: refresh db internal success
2025-9-29 16:16:40-debug: refresh db i18n success
2025-9-29 16:16:40-debug: refresh db assets success
2025-9-29 16:16:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:16:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:16:40-debug: asset-db:refresh-all-database (171ms)
2025-9-29 16:16:40-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:16:40-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:17:42-debug: refresh db internal success
2025-9-29 16:17:42-debug: refresh db i18n success
2025-9-29 16:17:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:17:42-debug: refresh db assets success
2025-9-29 16:17:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:17:42-debug: asset-db:refresh-all-database (122ms)
2025-9-29 16:17:42-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:17:42-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:18:20-debug: refresh db internal success
2025-9-29 16:18:20-debug: refresh db i18n success
2025-9-29 16:18:20-debug: refresh db assets success
2025-9-29 16:18:20-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:18:20-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:18:20-debug: asset-db:refresh-all-database (131ms)
2025-9-29 16:18:20-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:18:20-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:18:29-debug: refresh db internal success
2025-9-29 16:18:29-debug: refresh db i18n success
2025-9-29 16:18:29-debug: refresh db assets success
2025-9-29 16:18:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:18:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:18:29-debug: asset-db:refresh-all-database (130ms)
2025-9-29 16:18:29-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:18:29-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:19:36-debug: refresh db internal success
2025-9-29 16:19:36-debug: refresh db i18n success
2025-9-29 16:19:36-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorBaseUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:19:36-debug: refresh db assets success
2025-9-29 16:19:36-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:19:36-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:19:36-debug: asset-db:refresh-all-database (129ms)
2025-9-29 16:19:36-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:19:36-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:19:37-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000002.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:19:37-debug: asset-db:reimport-assetf3b50cd4-91d4-42ff-a57b-6bec3e0e750e (6ms)
2025-9-29 16:20:10-debug: refresh db internal success
2025-9-29 16:20:10-debug: refresh db i18n success
2025-9-29 16:20:10-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorBaseUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:20:10-debug: refresh db assets success
2025-9-29 16:20:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:20:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:20:10-debug: asset-db:refresh-all-database (174ms)
2025-9-29 16:20:10-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-29 16:20:10-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 16:20:11-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000002.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:20:11-debug: asset-db:reimport-assetf3b50cd4-91d4-42ff-a57b-6bec3e0e750e (7ms)
2025-9-29 16:21:12-debug: refresh db internal success
2025-9-29 16:21:12-debug: refresh db i18n success
2025-9-29 16:21:12-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:21:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:21:12-debug: refresh db assets success
2025-9-29 16:21:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:21:12-debug: asset-db:refresh-all-database (163ms)
2025-9-29 16:21:12-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:21:12-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:21:13-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000002.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:21:13-debug: asset-db:reimport-assetf3b50cd4-91d4-42ff-a57b-6bec3e0e750e (6ms)
2025-9-29 16:21:37-debug: refresh db internal success
2025-9-29 16:21:37-debug: refresh db i18n success
2025-9-29 16:21:37-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:21:37-debug: refresh db assets success
2025-9-29 16:21:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:21:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:21:37-debug: asset-db:refresh-all-database (135ms)
2025-9-29 16:21:37-debug: asset-db:worker-effect-data-processing (7ms)
2025-9-29 16:21:37-debug: asset-db-hook-engine-extends-afterRefresh (7ms)
2025-9-29 16:21:38-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000002.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:21:38-debug: asset-db:reimport-assetf3b50cd4-91d4-42ff-a57b-6bec3e0e750e (4ms)
2025-9-29 16:23:20-debug: refresh db internal success
2025-9-29 16:23:20-debug: refresh db i18n success
2025-9-29 16:23:20-debug: refresh db assets success
2025-9-29 16:23:20-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:23:20-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:23:20-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:23:20-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:23:20-debug: asset-db:refresh-all-database (155ms)
2025-9-29 16:23:55-debug: refresh db internal success
2025-9-29 16:23:55-debug: refresh db i18n success
2025-9-29 16:23:56-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:23:56-debug: refresh db assets success
2025-9-29 16:23:56-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:23:56-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:23:56-debug: asset-db:refresh-all-database (163ms)
2025-9-29 16:23:56-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:23:56-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:23:56-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000002.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:23:56-debug: asset-db:reimport-assetf3b50cd4-91d4-42ff-a57b-6bec3e0e750e (6ms)
2025-9-29 16:24:07-debug: refresh db internal success
2025-9-29 16:24:07-debug: refresh db i18n success
2025-9-29 16:24:07-debug: refresh db assets success
2025-9-29 16:24:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:24:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:24:07-debug: asset-db:refresh-all-database (134ms)
2025-9-29 16:24:07-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:24:07-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:24:08-debug: refresh db internal success
2025-9-29 16:24:08-debug: refresh db i18n success
2025-9-29 16:24:08-debug: refresh db assets success
2025-9-29 16:24:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:24:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:24:08-debug: asset-db:refresh-all-database (124ms)
2025-9-29 16:24:08-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:24:08-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:27:30-debug: refresh db internal success
2025-9-29 16:27:30-debug: refresh db i18n success
2025-9-29 16:27:30-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:27:30-debug: refresh db assets success
2025-9-29 16:27:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:27:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:27:30-debug: asset-db:refresh-all-database (164ms)
2025-9-29 16:27:30-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:27:30-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:27:31-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000002.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:27:31-debug: asset-db:reimport-assetf3b50cd4-91d4-42ff-a57b-6bec3e0e750e (5ms)
2025-9-29 16:28:05-debug: refresh db internal success
2025-9-29 16:28:05-debug: refresh db i18n success
2025-9-29 16:28:05-debug: refresh db assets success
2025-9-29 16:28:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:28:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:28:05-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:28:05-debug: asset-db:refresh-all-database (160ms)
2025-9-29 16:28:05-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:29:40-debug: refresh db internal success
2025-9-29 16:29:40-debug: refresh db i18n success
2025-9-29 16:29:40-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:29:40-debug: refresh db assets success
2025-9-29 16:29:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:29:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:29:40-debug: asset-db:refresh-all-database (163ms)
2025-9-29 16:29:40-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:29:40-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 16:29:41-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000002.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:29:41-debug: asset-db:reimport-assetf3b50cd4-91d4-42ff-a57b-6bec3e0e750e (138ms)
2025-9-29 16:31:10-debug: refresh db internal success
2025-9-29 16:31:10-debug: refresh db i18n success
2025-9-29 16:31:10-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\map\GameMapRun.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:31:10-debug: refresh db assets success
2025-9-29 16:31:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:31:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:31:10-debug: asset-db:refresh-all-database (160ms)
2025-9-29 16:31:11-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000002.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:31:11-debug: asset-db:reimport-assetf3b50cd4-91d4-42ff-a57b-6bec3e0e750e (4ms)
2025-9-29 16:31:40-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000002.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:31:40-debug: asset-db:reimport-assetf3b50cd4-91d4-42ff-a57b-6bec3e0e750e (13ms)
2025-9-29 16:31:41-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:31:41-debug: asset-db:reimport-asset401efd7e-bd20-4537-a13a-f25e6238c2a9 (3ms)
2025-9-29 16:31:46-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:31:46-debug: asset-db:reimport-asset401efd7e-bd20-4537-a13a-f25e6238c2a9 (3ms)
2025-9-29 16:32:03-debug: refresh db internal success
2025-9-29 16:32:03-debug: refresh db i18n success
2025-9-29 16:32:03-debug: refresh db assets success
2025-9-29 16:32:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:32:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:32:03-debug: asset-db:refresh-all-database (161ms)
2025-9-29 16:32:03-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:32:03-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:32:05-debug: refresh db internal success
2025-9-29 16:32:05-debug: refresh db i18n success
2025-9-29 16:32:05-debug: refresh db assets success
2025-9-29 16:32:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:32:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:32:05-debug: asset-db:refresh-all-database (151ms)
2025-9-29 16:32:05-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:32:05-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:32:06-debug: refresh db internal success
2025-9-29 16:32:06-debug: refresh db i18n success
2025-9-29 16:32:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:32:06-debug: refresh db assets success
2025-9-29 16:32:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:32:06-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-29 16:32:06-debug: asset-db:refresh-all-database (182ms)
2025-9-29 16:32:06-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 16:35:08-debug: refresh db internal success
2025-9-29 16:35:08-debug: refresh db i18n success
2025-9-29 16:35:08-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:35:08-debug: refresh db assets success
2025-9-29 16:35:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:35:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:35:08-debug: asset-db:refresh-all-database (161ms)
2025-9-29 16:35:08-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-29 16:35:08-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 16:35:09-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000002.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:35:09-debug: asset-db:reimport-assetf3b50cd4-91d4-42ff-a57b-6bec3e0e750e (5ms)
2025-9-29 16:36:02-debug: refresh db internal success
2025-9-29 16:36:02-debug: refresh db i18n success
2025-9-29 16:36:02-debug: refresh db assets success
2025-9-29 16:36:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:36:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:36:02-debug: asset-db:refresh-all-database (159ms)
2025-9-29 16:36:02-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:36:02-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:39:01-debug: refresh db internal success
2025-9-29 16:39:01-debug: refresh db i18n success
2025-9-29 16:39:01-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:39:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:39:01-debug: refresh db assets success
2025-9-29 16:39:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:39:01-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:39:01-debug: asset-db:refresh-all-database (176ms)
2025-9-29 16:39:01-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:39:01-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000002.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:39:01-debug: asset-db:reimport-assetf3b50cd4-91d4-42ff-a57b-6bec3e0e750e (6ms)
2025-9-29 16:39:24-debug: refresh db internal success
2025-9-29 16:39:24-debug: refresh db i18n success
2025-9-29 16:39:24-debug: refresh db assets success
2025-9-29 16:39:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:39:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:39:24-debug: asset-db:refresh-all-database (129ms)
2025-9-29 16:40:20-debug: refresh db internal success
2025-9-29 16:40:20-debug: refresh db i18n success
2025-9-29 16:40:20-debug: refresh db assets success
2025-9-29 16:40:20-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:40:20-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:40:20-debug: asset-db:refresh-all-database (128ms)
2025-9-29 16:40:20-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:40:20-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:42:00-debug: refresh db internal success
2025-9-29 16:42:00-debug: refresh db i18n success
2025-9-29 16:42:00-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\preview\WavePreview.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:42:00-debug: refresh db assets success
2025-9-29 16:42:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:42:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:42:00-debug: asset-db:refresh-all-database (166ms)
2025-9-29 16:42:00-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:42:01-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000002.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:42:01-debug: asset-db:reimport-assetf3b50cd4-91d4-42ff-a57b-6bec3e0e750e (4ms)
2025-9-29 16:42:54-debug: refresh db internal success
2025-9-29 16:42:54-debug: refresh db i18n success
2025-9-29 16:42:54-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:42:54-debug: refresh db assets success
2025-9-29 16:42:54-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:42:54-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:42:54-debug: asset-db:refresh-all-database (154ms)
2025-9-29 16:42:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000002.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:42:55-debug: asset-db:reimport-assetf3b50cd4-91d4-42ff-a57b-6bec3e0e750e (5ms)
2025-9-29 16:43:00-debug: refresh db internal success
2025-9-29 16:43:00-debug: refresh db i18n success
2025-9-29 16:43:00-debug: refresh db assets success
2025-9-29 16:43:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:43:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:43:00-debug: asset-db:refresh-all-database (149ms)
2025-9-29 16:43:00-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:43:00-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:43:18-debug: refresh db internal success
2025-9-29 16:43:18-debug: refresh db i18n success
2025-9-29 16:43:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:43:18-debug: refresh db assets success
2025-9-29 16:43:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:43:18-debug: asset-db:refresh-all-database (147ms)
2025-9-29 16:43:18-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:44:25-debug: refresh db internal success
2025-9-29 16:44:25-debug: refresh db i18n success
2025-9-29 16:44:25-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:44:25-debug: refresh db assets success
2025-9-29 16:44:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:44:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:44:25-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:44:25-debug: asset-db:refresh-all-database (156ms)
2025-9-29 16:44:25-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:44:25-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000002.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:44:25-debug: asset-db:reimport-assetf3b50cd4-91d4-42ff-a57b-6bec3e0e750e (5ms)
2025-9-29 16:44:55-debug: refresh db internal success
2025-9-29 16:44:55-debug: refresh db i18n success
2025-9-29 16:44:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:44:55-debug: refresh db assets success
2025-9-29 16:44:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:44:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:44:55-debug: asset-db:refresh-all-database (127ms)
2025-9-29 16:44:55-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:44:55-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 16:44:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000002.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:44:55-debug: asset-db:reimport-assetf3b50cd4-91d4-42ff-a57b-6bec3e0e750e (5ms)
2025-9-29 16:45:04-debug: refresh db internal success
2025-9-29 16:45:04-debug: refresh db i18n success
2025-9-29 16:45:05-debug: refresh db assets success
2025-9-29 16:45:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:45:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:45:05-debug: asset-db:refresh-all-database (135ms)
2025-9-29 16:45:05-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:45:05-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 16:45:10-debug: refresh db internal success
2025-9-29 16:45:10-debug: refresh db i18n success
2025-9-29 16:45:10-debug: refresh db assets success
2025-9-29 16:45:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:45:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:45:10-debug: asset-db:refresh-all-database (122ms)
2025-9-29 16:45:10-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:45:10-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:45:24-debug: refresh db internal success
2025-9-29 16:45:24-debug: refresh db i18n success
2025-9-29 16:45:24-debug: refresh db assets success
2025-9-29 16:45:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:45:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:45:24-debug: asset-db:refresh-all-database (161ms)
2025-9-29 16:45:24-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:45:24-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:45:49-debug: refresh db internal success
2025-9-29 16:45:49-debug: refresh db i18n success
2025-9-29 16:45:49-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:45:49-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:45:49-debug: refresh db assets success
2025-9-29 16:45:49-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:45:49-debug: asset-db:refresh-all-database (123ms)
2025-9-29 16:45:50-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000002.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:45:50-debug: asset-db:reimport-assetf3b50cd4-91d4-42ff-a57b-6bec3e0e750e (4ms)
2025-9-29 16:46:10-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000002.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:46:10-debug: asset-db:reimport-assetf3b50cd4-91d4-42ff-a57b-6bec3e0e750e (4ms)
2025-9-29 16:47:48-debug: refresh db internal success
2025-9-29 16:47:48-debug: refresh db i18n success
2025-9-29 16:47:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:47:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:47:48-debug: refresh db assets success
2025-9-29 16:47:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:47:48-debug: asset-db:refresh-all-database (151ms)
2025-9-29 16:48:32-debug: refresh db internal success
2025-9-29 16:48:32-debug: refresh db i18n success
2025-9-29 16:48:32-debug: refresh db assets success
2025-9-29 16:48:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:48:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:48:32-debug: asset-db:refresh-all-database (161ms)
2025-9-29 16:48:32-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:48:32-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:49:13-debug: refresh db internal success
2025-9-29 16:49:13-debug: refresh db i18n success
2025-9-29 16:49:13-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:49:13-debug: refresh db assets success
2025-9-29 16:49:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:49:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:49:13-debug: asset-db:refresh-all-database (163ms)
2025-9-29 16:49:13-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:49:13-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:49:14-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000002.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:49:14-debug: asset-db:reimport-assetf3b50cd4-91d4-42ff-a57b-6bec3e0e750e (6ms)
2025-9-29 16:49:33-debug: refresh db internal success
2025-9-29 16:49:33-debug: refresh db i18n success
2025-9-29 16:49:33-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:49:33-debug: refresh db assets success
2025-9-29 16:49:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:49:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:49:33-debug: asset-db:refresh-all-database (158ms)
2025-9-29 16:49:33-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:49:33-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 16:49:33-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000002.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:49:33-debug: asset-db:reimport-assetf3b50cd4-91d4-42ff-a57b-6bec3e0e750e (4ms)
2025-9-29 16:50:18-debug: refresh db internal success
2025-9-29 16:50:18-debug: refresh db i18n success
2025-9-29 16:50:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:50:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:50:19-debug: refresh db assets success
2025-9-29 16:50:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:50:19-debug: asset-db:refresh-all-database (157ms)
2025-9-29 16:50:19-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:50:19-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:50:19-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000002.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:50:19-debug: asset-db:reimport-assetf3b50cd4-91d4-42ff-a57b-6bec3e0e750e (6ms)
2025-9-29 16:51:14-debug: refresh db internal success
2025-9-29 16:51:14-debug: refresh db i18n success
2025-9-29 16:51:14-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:51:14-debug: refresh db assets success
2025-9-29 16:51:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:51:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:51:14-debug: asset-db:refresh-all-database (174ms)
2025-9-29 16:51:14-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:51:14-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:51:15-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000002.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:51:15-debug: asset-db:reimport-assetf3b50cd4-91d4-42ff-a57b-6bec3e0e750e (6ms)
2025-9-29 16:51:33-debug: refresh db internal success
2025-9-29 16:51:33-debug: refresh db i18n success
2025-9-29 16:51:33-debug: refresh db assets success
2025-9-29 16:51:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:51:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:51:33-debug: asset-db:refresh-all-database (159ms)
2025-9-29 16:54:10-debug: refresh db internal success
2025-9-29 16:54:10-debug: refresh db i18n success
2025-9-29 16:54:10-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:54:10-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:54:10-debug: refresh db assets success
2025-9-29 16:54:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:54:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:54:10-debug: asset-db:refresh-all-database (166ms)
2025-9-29 16:54:10-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:54:11-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000002.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:54:11-debug: asset-db:reimport-assetf3b50cd4-91d4-42ff-a57b-6bec3e0e750e (3ms)
2025-9-29 16:54:12-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000002.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:54:12-debug: asset-db:reimport-assetf3b50cd4-91d4-42ff-a57b-6bec3e0e750e (4ms)
2025-9-29 16:55:27-debug: refresh db internal success
2025-9-29 16:55:27-debug: refresh db i18n success
2025-9-29 16:55:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:55:27-debug: refresh db assets success
2025-9-29 16:55:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:55:27-debug: asset-db:refresh-all-database (153ms)
2025-9-29 16:55:27-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-29 16:55:27-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 16:56:19-debug: refresh db internal success
2025-9-29 16:56:19-debug: refresh db i18n success
2025-9-29 16:56:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:56:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:56:19-debug: refresh db assets success
2025-9-29 16:56:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:56:19-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:56:19-debug: asset-db:refresh-all-database (162ms)
2025-9-29 16:56:19-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:56:20-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000002.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:56:20-debug: asset-db:reimport-assetf3b50cd4-91d4-42ff-a57b-6bec3e0e750e (7ms)
2025-9-29 16:58:02-debug: refresh db internal success
2025-9-29 16:58:02-debug: refresh db i18n success
2025-9-29 16:58:03-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:58:03-debug: refresh db assets success
2025-9-29 16:58:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:58:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:58:03-debug: asset-db:refresh-all-database (169ms)
2025-9-29 16:58:03-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-29 16:58:03-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 16:58:03-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000002.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:58:03-debug: asset-db:reimport-assetf3b50cd4-91d4-42ff-a57b-6bec3e0e750e (143ms)
2025-9-29 16:58:51-debug: refresh db internal success
2025-9-29 16:58:51-debug: refresh db i18n success
2025-9-29 16:58:51-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:58:51-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:58:51-debug: refresh db assets success
2025-9-29 16:58:51-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:58:51-debug: asset-db:refresh-all-database (126ms)
2025-9-29 16:58:51-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:58:51-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:58:52-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000002.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:58:52-debug: asset-db:reimport-assetf3b50cd4-91d4-42ff-a57b-6bec3e0e750e (5ms)
2025-9-29 16:59:02-debug: refresh db internal success
2025-9-29 16:59:02-debug: refresh db i18n success
2025-9-29 16:59:02-debug: refresh db assets success
2025-9-29 16:59:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:59:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:59:02-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:59:02-debug: asset-db:refresh-all-database (131ms)
2025-9-29 16:59:02-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:59:03-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000001.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:59:03-debug: asset-db:reimport-asset51185af3-09e6-4807-83f9-40a8b6102e11 (3ms)
2025-9-29 16:59:03-debug: refresh db internal success
2025-9-29 16:59:03-debug: refresh db i18n success
2025-9-29 16:59:03-debug: refresh db assets success
2025-9-29 16:59:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:59:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:59:03-debug: asset-db:refresh-all-database (127ms)
2025-9-29 16:59:03-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:59:07-debug: refresh db internal success
2025-9-29 16:59:07-debug: refresh db i18n success
2025-9-29 16:59:07-debug: refresh db assets success
2025-9-29 16:59:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:59:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:59:07-debug: asset-db:refresh-all-database (120ms)
2025-9-29 16:59:07-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000002.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:59:07-debug: asset-db:reimport-assetf3b50cd4-91d4-42ff-a57b-6bec3e0e750e (4ms)
2025-9-29 16:59:07-debug: refresh db internal success
2025-9-29 16:59:07-debug: refresh db i18n success
2025-9-29 16:59:07-debug: refresh db assets success
2025-9-29 16:59:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:59:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:59:07-debug: asset-db:refresh-all-database (120ms)
2025-9-29 16:59:07-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:59:07-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:59:10-debug: refresh db internal success
2025-9-29 16:59:10-debug: refresh db i18n success
2025-9-29 16:59:10-debug: refresh db assets success
2025-9-29 16:59:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:59:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:59:10-debug: asset-db:refresh-all-database (118ms)
2025-9-29 16:59:10-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:59:11-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000001.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:59:11-debug: asset-db:reimport-asset51185af3-09e6-4807-83f9-40a8b6102e11 (10ms)
2025-9-29 16:59:12-debug: refresh db internal success
2025-9-29 16:59:12-debug: refresh db i18n success
2025-9-29 16:59:12-debug: refresh db assets success
2025-9-29 16:59:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:59:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:59:12-debug: asset-db:refresh-all-database (127ms)
2025-9-29 16:59:50-debug: refresh db internal success
2025-9-29 16:59:50-debug: refresh db i18n success
2025-9-29 16:59:50-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:59:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 16:59:50-debug: refresh db assets success
2025-9-29 16:59:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 16:59:50-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 16:59:50-debug: asset-db:refresh-all-database (164ms)
2025-9-29 16:59:50-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 16:59:51-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000002.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 16:59:51-debug: asset-db:reimport-assetf3b50cd4-91d4-42ff-a57b-6bec3e0e750e (5ms)
2025-9-29 17:03:25-debug: refresh db internal success
2025-9-29 17:03:25-debug: refresh db i18n success
2025-9-29 17:03:25-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:03:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 17:03:25-debug: refresh db assets success
2025-9-29 17:03:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 17:03:25-debug: asset-db:refresh-all-database (159ms)
2025-9-29 17:03:25-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 17:03:25-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 17:03:25-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000002.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:03:25-debug: asset-db:reimport-assetf3b50cd4-91d4-42ff-a57b-6bec3e0e750e (4ms)
2025-9-29 17:05:24-debug: refresh db internal success
2025-9-29 17:05:24-debug: refresh db i18n success
2025-9-29 17:05:24-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:05:24-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\map\LevelEventRun.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:05:24-debug: refresh db assets success
2025-9-29 17:05:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 17:05:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 17:05:24-debug: asset-db:refresh-all-database (171ms)
2025-9-29 17:05:24-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 17:05:24-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 17:05:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000002.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:05:24-debug: asset-db:reimport-assetf3b50cd4-91d4-42ff-a57b-6bec3e0e750e (4ms)
2025-9-29 17:05:36-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000002.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:05:36-debug: asset-db:reimport-assetf3b50cd4-91d4-42ff-a57b-6bec3e0e750e (4ms)
2025-9-29 17:05:36-debug: refresh db internal success
2025-9-29 17:05:36-debug: refresh db i18n success
2025-9-29 17:05:36-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 17:05:36-debug: refresh db assets success
2025-9-29 17:05:36-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 17:05:36-debug: asset-db:refresh-all-database (139ms)
2025-9-29 17:05:36-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 17:05:40-debug: Query all assets info in project
2025-9-29 17:05:40-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-29 17:05:40-debug: Skip compress image, progress: 0%
2025-9-29 17:05:40-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 17:05:40-debug: Num of bundles: 17..., progress: 0%
2025-9-29 17:05:40-debug: 查询 Asset Bundle start, progress: 0%
2025-9-29 17:05:40-debug: Init all bundles start..., progress: 0%
2025-9-29 17:05:40-debug: Init bundle root assets start..., progress: 0%
2025-9-29 17:05:40-debug:   Number of all scenes: 11
2025-9-29 17:05:40-debug:   Number of all scripts: 275
2025-9-29 17:05:40-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-29 17:05:40-debug:   Number of other assets: 2158
2025-9-29 17:05:40-debug: Init bundle root assets success..., progress: 0%
2025-9-29 17:05:40-debug: // ---- build task 查询 Asset Bundle ---- (26ms)
2025-9-29 17:05:40-log: run build task 查询 Asset Bundle success in 26 ms√, progress: 5%
2025-9-29 17:05:40-debug: [Build Memory track]: 查询 Asset Bundle start:215.46MB, end 215.46MB, increase: 0.05KB
2025-9-29 17:05:40-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 17:05:40-debug: 查询 Asset Bundle start, progress: 5%
2025-9-29 17:05:40-log: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-9-29 17:05:40-debug: [Build Memory track]: 查询 Asset Bundle start:215.49MB, end 215.79MB, increase: 309.63KB
2025-9-29 17:05:40-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-29 17:05:40-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 17:05:40-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-9-29 17:05:40-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-9-29 17:05:40-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:215.82MB, end 215.84MB, increase: 17.55KB
2025-9-29 17:05:40-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-29 17:05:40-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-29 17:05:40-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-29 17:05:40-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-29 17:05:40-debug: [Build Memory track]: 填充脚本数据到 settings.json start:215.87MB, end 215.89MB, increase: 25.49KB
2025-9-29 17:05:40-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-29 17:05:40-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 17:05:40-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-29 17:05:40-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-9-29 17:05:40-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:215.92MB, end 216.21MB, increase: 295.81KB
2025-9-29 17:06:46-debug: refresh db internal success
2025-9-29 17:06:46-debug: refresh db i18n success
2025-9-29 17:06:47-debug: refresh db assets success
2025-9-29 17:06:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 17:06:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 17:06:47-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 17:06:47-debug: asset-db:refresh-all-database (172ms)
2025-9-29 17:06:47-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 17:07:17-debug: refresh db internal success
2025-9-29 17:07:17-debug: refresh db i18n success
2025-9-29 17:07:17-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\map\LevelEventRun.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:07:17-debug: refresh db assets success
2025-9-29 17:07:17-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 17:07:17-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 17:07:17-debug: asset-db:refresh-all-database (119ms)
2025-9-29 17:07:17-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 17:07:17-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 17:07:18-debug: refresh db internal success
2025-9-29 17:07:18-debug: refresh db i18n success
2025-9-29 17:07:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 17:07:18-debug: refresh db assets success
2025-9-29 17:07:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 17:07:18-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 17:07:18-debug: asset-db:refresh-all-database (123ms)
2025-9-29 17:07:18-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 17:07:24-debug: refresh db internal success
2025-9-29 17:07:24-debug: refresh db i18n success
2025-9-29 17:07:24-debug: refresh db assets success
2025-9-29 17:07:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 17:07:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 17:07:24-debug: asset-db:refresh-all-database (121ms)
2025-9-29 17:07:24-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 17:07:24-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 17:07:31-debug: refresh db internal success
2025-9-29 17:07:31-debug: refresh db i18n success
2025-9-29 17:07:31-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 17:07:31-debug: refresh db assets success
2025-9-29 17:07:31-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 17:07:31-debug: asset-db:refresh-all-database (117ms)
2025-9-29 17:07:31-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 17:07:31-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 17:07:48-debug: refresh db internal success
2025-9-29 17:07:48-debug: refresh db i18n success
2025-9-29 17:07:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\weapon\Weapon.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:07:48-debug: refresh db assets success
2025-9-29 17:07:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 17:07:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 17:07:48-debug: asset-db:refresh-all-database (128ms)
2025-9-29 17:07:48-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 17:07:48-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 17:08:16-debug: refresh db internal success
2025-9-29 17:08:16-debug: refresh db i18n success
2025-9-29 17:08:16-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:08:16-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 17:08:16-debug: refresh db assets success
2025-9-29 17:08:16-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 17:08:16-debug: asset-db:refresh-all-database (125ms)
2025-9-29 17:08:16-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 17:08:16-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 17:08:18-debug: Query all assets info in project
2025-9-29 17:08:18-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-29 17:08:18-debug: Skip compress image, progress: 0%
2025-9-29 17:08:18-debug: Init all bundles start..., progress: 0%
2025-9-29 17:08:18-debug: 查询 Asset Bundle start, progress: 0%
2025-9-29 17:08:18-debug: Num of bundles: 17..., progress: 0%
2025-9-29 17:08:18-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 17:08:18-debug: Init bundle root assets start..., progress: 0%
2025-9-29 17:08:18-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-29 17:08:18-debug:   Number of all scripts: 275
2025-9-29 17:08:18-debug:   Number of other assets: 2158
2025-9-29 17:08:18-debug: Init bundle root assets success..., progress: 0%
2025-9-29 17:08:18-debug:   Number of all scenes: 11
2025-9-29 17:08:18-debug: // ---- build task 查询 Asset Bundle ---- (25ms)
2025-9-29 17:08:18-log: run build task 查询 Asset Bundle success in 25 ms√, progress: 5%
2025-9-29 17:08:18-debug: [Build Memory track]: 查询 Asset Bundle start:217.21MB, end 220.15MB, increase: 2.94MB
2025-9-29 17:08:18-debug: 查询 Asset Bundle start, progress: 5%
2025-9-29 17:08:18-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 17:08:18-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-9-29 17:08:18-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-29 17:08:18-debug: [Build Memory track]: 查询 Asset Bundle start:220.18MB, end 220.48MB, increase: 308.69KB
2025-9-29 17:08:18-log: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-9-29 17:08:18-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 17:08:18-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-9-29 17:08:18-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-29 17:08:18-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:220.51MB, end 220.53MB, increase: 16.89KB
2025-9-29 17:08:18-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-29 17:08:18-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-29 17:08:18-debug: [Build Memory track]: 填充脚本数据到 settings.json start:220.56MB, end 214.66MB, increase: -6034.58KB
2025-9-29 17:08:18-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-29 17:08:18-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 17:08:18-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-29 17:08:18-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-9-29 17:08:18-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:214.69MB, end 214.98MB, increase: 297.03KB
2025-9-29 17:09:25-debug: refresh db internal success
2025-9-29 17:09:25-debug: refresh db i18n success
2025-9-29 17:09:25-debug: refresh db assets success
2025-9-29 17:09:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 17:09:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 17:09:25-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 17:09:25-debug: asset-db:refresh-all-database (129ms)
2025-9-29 17:09:25-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 17:09:54-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\enemy\EnemyPlane.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:09:54-debug: asset-db:reimport-assetf22ea656-6b22-4569-95bf-8be5766bba40 (2ms)
2025-9-29 17:09:57-debug: Query all assets info in project
2025-9-29 17:09:58-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-29 17:09:58-debug: Skip compress image, progress: 0%
2025-9-29 17:09:58-debug: Init all bundles start..., progress: 0%
2025-9-29 17:09:58-debug: 查询 Asset Bundle start, progress: 0%
2025-9-29 17:09:58-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 17:09:58-debug: Num of bundles: 17..., progress: 0%
2025-9-29 17:09:58-debug: Init bundle root assets start..., progress: 0%
2025-9-29 17:09:58-debug:   Number of all scenes: 11
2025-9-29 17:09:58-debug:   Number of all scripts: 275
2025-9-29 17:09:58-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-29 17:09:58-debug:   Number of other assets: 2158
2025-9-29 17:09:58-debug: Init bundle root assets success..., progress: 0%
2025-9-29 17:09:58-debug: // ---- build task 查询 Asset Bundle ---- (21ms)
2025-9-29 17:09:58-log: run build task 查询 Asset Bundle success in 21 ms√, progress: 5%
2025-9-29 17:09:58-debug: [Build Memory track]: 查询 Asset Bundle start:220.03MB, end 221.98MB, increase: 1.95MB
2025-9-29 17:09:58-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 17:09:58-debug: 查询 Asset Bundle start, progress: 5%
2025-9-29 17:09:58-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-9-29 17:09:58-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-9-29 17:09:58-debug: [Build Memory track]: 查询 Asset Bundle start:222.01MB, end 220.82MB, increase: -1209.91KB
2025-9-29 17:09:58-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-29 17:09:58-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 17:09:58-log: run build task 整理部分构建选项内数据到 settings.json success in 3 ms√, progress: 12%
2025-9-29 17:09:58-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-9-29 17:09:58-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-29 17:09:58-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:220.86MB, end 220.88MB, increase: 26.90KB
2025-9-29 17:09:58-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-29 17:09:58-debug: // ---- build task 填充脚本数据到 settings.json ---- (3ms)
2025-9-29 17:09:58-log: run build task 填充脚本数据到 settings.json success in 3 ms√, progress: 13%
2025-9-29 17:09:58-debug: [Build Memory track]: 填充脚本数据到 settings.json start:220.91MB, end 220.94MB, increase: 25.98KB
2025-9-29 17:09:58-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-29 17:09:58-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 17:09:58-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (7ms)
2025-9-29 17:09:58-log: run build task 整理部分构建选项内数据到 settings.json success in 7 ms√, progress: 15%
2025-9-29 17:09:58-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:220.96MB, end 221.25MB, increase: 291.11KB
2025-9-29 17:12:36-debug: refresh db internal success
2025-9-29 17:12:36-debug: refresh db i18n success
2025-9-29 17:12:36-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 17:12:36-debug: refresh db assets success
2025-9-29 17:12:36-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 17:12:36-debug: asset-db:refresh-all-database (162ms)
2025-9-29 17:12:36-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 17:12:36-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 17:13:00-debug: refresh db internal success
2025-9-29 17:13:00-debug: refresh db i18n success
2025-9-29 17:13:01-debug: refresh db assets success
2025-9-29 17:13:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 17:13:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 17:13:01-debug: asset-db:refresh-all-database (148ms)
2025-9-29 17:13:01-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 17:13:01-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 17:15:43-debug: refresh db internal success
2025-9-29 17:15:43-debug: refresh db i18n success
2025-9-29 17:15:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:15:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbequipupgrade.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:15:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbglobalattr.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:15:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresachievement.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:15:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbplane.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:15:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresactivity.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:15:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresbuffer.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:15:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbreschapter.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:15:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresemitter.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:15:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresenemy.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:15:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresequip.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:15:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresgamemode.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:15:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresitem.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:15:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbreslevel.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:15:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbreslevelgroup.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:15:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresmodereward.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:15:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresskill.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:15:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresloot.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:15:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresskillcondition.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:15:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbrestask.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:15:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresstore.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:15:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbrestaskorbit.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:15:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresupgrade.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:15:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\autogen\luban
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:15:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\autogen\luban\schema.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:15:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 17:15:43-debug: refresh db assets success
2025-9-29 17:15:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 17:15:43-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 17:15:43-debug: asset-db:refresh-all-database (258ms)
2025-9-29 17:15:43-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 17:15:44-debug: Query all assets info in project
2025-9-29 17:15:44-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-29 17:15:44-debug: Skip compress image, progress: 0%
2025-9-29 17:15:44-debug: Init all bundles start..., progress: 0%
2025-9-29 17:15:44-debug: Num of bundles: 17..., progress: 0%
2025-9-29 17:15:44-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 17:15:44-debug: 查询 Asset Bundle start, progress: 0%
2025-9-29 17:15:44-debug: Init bundle root assets start..., progress: 0%
2025-9-29 17:15:44-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-29 17:15:44-debug:   Number of all scenes: 11
2025-9-29 17:15:44-debug:   Number of other assets: 2158
2025-9-29 17:15:44-debug: Init bundle root assets success..., progress: 0%
2025-9-29 17:15:44-debug:   Number of all scripts: 275
2025-9-29 17:15:44-debug: // ---- build task 查询 Asset Bundle ---- (31ms)
2025-9-29 17:15:44-log: run build task 查询 Asset Bundle success in 31 ms√, progress: 5%
2025-9-29 17:15:44-debug: 查询 Asset Bundle start, progress: 5%
2025-9-29 17:15:44-debug: [Build Memory track]: 查询 Asset Bundle start:214.88MB, end 215.34MB, increase: 471.95KB
2025-9-29 17:15:44-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 17:15:44-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-9-29 17:15:44-log: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-9-29 17:15:44-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-29 17:15:44-debug: [Build Memory track]: 查询 Asset Bundle start:215.37MB, end 215.67MB, increase: 307.86KB
2025-9-29 17:15:44-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 17:15:44-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-29 17:15:44-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-29 17:15:44-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:215.70MB, end 215.73MB, increase: 26.86KB
2025-9-29 17:15:44-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-29 17:15:44-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-29 17:15:44-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-29 17:15:44-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-29 17:15:44-debug: [Build Memory track]: 填充脚本数据到 settings.json start:215.75MB, end 215.78MB, increase: 26.11KB
2025-9-29 17:15:44-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-29 17:15:44-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 17:15:44-log: run build task 整理部分构建选项内数据到 settings.json success in 5 ms√, progress: 15%
2025-9-29 17:15:44-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:215.81MB, end 216.10MB, increase: 295.79KB
2025-9-29 17:15:44-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (5ms)
2025-9-29 17:15:45-debug: Query all assets info in project
2025-9-29 17:15:45-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-29 17:15:45-debug: Skip compress image, progress: 0%
2025-9-29 17:15:45-debug: Init all bundles start..., progress: 0%
2025-9-29 17:15:45-debug: Num of bundles: 17..., progress: 0%
2025-9-29 17:15:45-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 17:15:45-debug: 查询 Asset Bundle start, progress: 0%
2025-9-29 17:15:45-debug: Init bundle root assets start..., progress: 0%
2025-9-29 17:15:45-debug:   Number of all scenes: 11
2025-9-29 17:15:45-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-29 17:15:45-debug:   Number of other assets: 2158
2025-9-29 17:15:45-debug: Init bundle root assets success..., progress: 0%
2025-9-29 17:15:45-debug:   Number of all scripts: 275
2025-9-29 17:15:45-debug: [Build Memory track]: 查询 Asset Bundle start:218.55MB, end 217.55MB, increase: -1017.32KB
2025-9-29 17:15:45-debug: 查询 Asset Bundle start, progress: 5%
2025-9-29 17:15:45-log: run build task 查询 Asset Bundle success in 25 ms√, progress: 5%
2025-9-29 17:15:45-debug: // ---- build task 查询 Asset Bundle ---- (25ms)
2025-9-29 17:15:45-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 17:15:45-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-9-29 17:15:45-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-9-29 17:15:45-debug: [Build Memory track]: 查询 Asset Bundle start:217.58MB, end 217.88MB, increase: 308.75KB
2025-9-29 17:15:45-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-29 17:15:45-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 17:15:45-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-29 17:15:45-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:217.91MB, end 217.94MB, increase: 26.00KB
2025-9-29 17:15:45-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-29 17:15:45-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-29 17:15:45-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-29 17:15:45-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-29 17:15:45-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-29 17:15:45-debug: [Build Memory track]: 填充脚本数据到 settings.json start:217.97MB, end 217.99MB, increase: 25.92KB
2025-9-29 17:15:45-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-29 17:15:45-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 17:15:45-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-9-29 17:15:45-log: run build task 整理部分构建选项内数据到 settings.json success in 3 ms√, progress: 15%
2025-9-29 17:15:45-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:218.02MB, end 218.31MB, increase: 291.92KB
2025-9-29 17:15:45-debug: Query all assets info in project
2025-9-29 17:15:45-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-29 17:15:45-debug: Skip compress image, progress: 0%
2025-9-29 17:15:45-debug: Init all bundles start..., progress: 0%
2025-9-29 17:15:45-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 17:15:45-debug: 查询 Asset Bundle start, progress: 0%
2025-9-29 17:15:45-debug: Init bundle root assets start..., progress: 0%
2025-9-29 17:15:45-debug: Num of bundles: 17..., progress: 0%
2025-9-29 17:15:45-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-29 17:15:45-debug:   Number of other assets: 2158
2025-9-29 17:15:45-debug: Init bundle root assets success..., progress: 0%
2025-9-29 17:15:45-debug:   Number of all scripts: 275
2025-9-29 17:15:45-debug: // ---- build task 查询 Asset Bundle ---- (30ms)
2025-9-29 17:15:45-debug:   Number of all scenes: 11
2025-9-29 17:15:45-log: run build task 查询 Asset Bundle success in 30 ms√, progress: 5%
2025-9-29 17:15:45-debug: [Build Memory track]: 查询 Asset Bundle start:220.66MB, end 219.60MB, increase: -1080.03KB
2025-9-29 17:15:45-debug: 查询 Asset Bundle start, progress: 5%
2025-9-29 17:15:45-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 17:15:45-debug: // ---- build task 查询 Asset Bundle ---- (29ms)
2025-9-29 17:15:45-log: run build task 查询 Asset Bundle success in 29 ms√, progress: 10%
2025-9-29 17:15:45-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-29 17:15:45-debug: [Build Memory track]: 查询 Asset Bundle start:219.63MB, end 202.45MB, increase: -17593.65KB
2025-9-29 17:15:45-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 17:15:45-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-9-29 17:15:45-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-29 17:15:45-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:202.48MB, end 202.50MB, increase: 18.08KB
2025-9-29 17:15:45-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-29 17:15:45-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-29 17:15:45-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-29 17:15:45-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-29 17:15:45-debug: [Build Memory track]: 填充脚本数据到 settings.json start:202.53MB, end 202.56MB, increase: 36.63KB
2025-9-29 17:15:45-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 17:15:45-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-9-29 17:15:45-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:202.59MB, end 202.88MB, increase: 297.12KB
2025-9-29 17:15:45-log: run build task 整理部分构建选项内数据到 settings.json success in 3 ms√, progress: 15%
2025-9-29 17:16:25-debug: refresh db internal success
2025-9-29 17:16:25-debug: refresh db i18n success
2025-9-29 17:16:26-debug: refresh db assets success
2025-9-29 17:16:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 17:16:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 17:16:26-debug: asset-db:refresh-all-database (129ms)
2025-9-29 17:16:26-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-29 17:16:26-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 17:18:09-debug: refresh db internal success
2025-9-29 17:18:09-debug: refresh db i18n success
2025-9-29 17:18:09-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:18:09-debug: refresh db assets success
2025-9-29 17:18:09-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 17:18:09-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 17:18:09-debug: asset-db:refresh-all-database (154ms)
2025-9-29 17:18:09-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 17:18:09-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 17:18:10-debug: Query all assets info in project
2025-9-29 17:18:10-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-29 17:18:10-debug: Skip compress image, progress: 0%
2025-9-29 17:18:10-debug: Init all bundles start..., progress: 0%
2025-9-29 17:18:10-debug: Num of bundles: 17..., progress: 0%
2025-9-29 17:18:10-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 17:18:10-debug: 查询 Asset Bundle start, progress: 0%
2025-9-29 17:18:10-debug: Init bundle root assets start..., progress: 0%
2025-9-29 17:18:10-debug:   Number of all scenes: 11
2025-9-29 17:18:10-debug:   Number of all scripts: 275
2025-9-29 17:18:10-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-29 17:18:10-debug:   Number of other assets: 2158
2025-9-29 17:18:10-debug: Init bundle root assets success..., progress: 0%
2025-9-29 17:18:10-log: run build task 查询 Asset Bundle success in 33 ms√, progress: 5%
2025-9-29 17:18:10-debug: // ---- build task 查询 Asset Bundle ---- (33ms)
2025-9-29 17:18:10-debug: [Build Memory track]: 查询 Asset Bundle start:214.95MB, end 212.58MB, increase: -2421.86KB
2025-9-29 17:18:10-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 17:18:10-debug: 查询 Asset Bundle start, progress: 5%
2025-9-29 17:18:10-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-9-29 17:18:10-debug: [Build Memory track]: 查询 Asset Bundle start:212.61MB, end 212.91MB, increase: 309.37KB
2025-9-29 17:18:10-log: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-9-29 17:18:10-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-29 17:18:10-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 17:18:10-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-9-29 17:18:10-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-29 17:18:10-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-29 17:18:10-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:212.94MB, end 212.96MB, increase: 16.66KB
2025-9-29 17:18:10-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-29 17:18:10-debug: [Build Memory track]: 填充脚本数据到 settings.json start:212.99MB, end 213.01MB, increase: 23.18KB
2025-9-29 17:18:10-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-29 17:18:10-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 17:18:10-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-29 17:18:10-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:213.04MB, end 213.33MB, increase: 290.42KB
2025-9-29 17:18:10-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-9-29 17:18:10-debug: Query all assets info in project
2025-9-29 17:18:10-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-29 17:18:10-debug: Skip compress image, progress: 0%
2025-9-29 17:18:10-debug: Init all bundles start..., progress: 0%
2025-9-29 17:18:10-debug: Num of bundles: 17..., progress: 0%
2025-9-29 17:18:10-debug: 查询 Asset Bundle start, progress: 0%
2025-9-29 17:18:10-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 17:18:10-debug: Init bundle root assets start..., progress: 0%
2025-9-29 17:18:10-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-29 17:18:10-debug:   Number of other assets: 2158
2025-9-29 17:18:10-debug:   Number of all scenes: 11
2025-9-29 17:18:10-debug:   Number of all scripts: 275
2025-9-29 17:18:10-debug: Init bundle root assets success..., progress: 0%
2025-9-29 17:18:10-log: run build task 查询 Asset Bundle success in 26 ms√, progress: 5%
2025-9-29 17:18:10-debug: // ---- build task 查询 Asset Bundle ---- (26ms)
2025-9-29 17:18:10-debug: [Build Memory track]: 查询 Asset Bundle start:211.36MB, end 215.12MB, increase: 3.76MB
2025-9-29 17:18:10-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 17:18:10-debug: 查询 Asset Bundle start, progress: 5%
2025-9-29 17:18:10-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-9-29 17:18:10-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 17:18:10-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-9-29 17:18:10-debug: [Build Memory track]: 查询 Asset Bundle start:215.15MB, end 215.45MB, increase: 309.03KB
2025-9-29 17:18:10-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-29 17:18:10-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-29 17:18:10-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:215.48MB, end 215.50MB, increase: 24.23KB
2025-9-29 17:18:10-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-9-29 17:18:10-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-29 17:18:10-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-29 17:18:10-debug: [Build Memory track]: 填充脚本数据到 settings.json start:215.53MB, end 215.55MB, increase: 16.21KB
2025-9-29 17:18:10-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-29 17:18:10-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 17:18:10-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-29 17:18:10-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:215.58MB, end 215.86MB, increase: 291.23KB
2025-9-29 17:18:10-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-9-29 17:18:16-debug: Query all assets info in project
2025-9-29 17:18:16-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-29 17:18:16-debug: Skip compress image, progress: 0%
2025-9-29 17:18:16-debug: Num of bundles: 17..., progress: 0%
2025-9-29 17:18:16-debug: Init all bundles start..., progress: 0%
2025-9-29 17:18:16-debug: 查询 Asset Bundle start, progress: 0%
2025-9-29 17:18:16-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 17:18:16-debug: Init bundle root assets start..., progress: 0%
2025-9-29 17:18:16-debug:   Number of all scripts: 275
2025-9-29 17:18:16-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-29 17:18:16-debug:   Number of all scenes: 11
2025-9-29 17:18:16-debug: Init bundle root assets success..., progress: 0%
2025-9-29 17:18:16-debug:   Number of other assets: 2158
2025-9-29 17:18:16-debug: // ---- build task 查询 Asset Bundle ---- (20ms)
2025-9-29 17:18:16-log: run build task 查询 Asset Bundle success in 20 ms√, progress: 5%
2025-9-29 17:18:16-debug: [Build Memory track]: 查询 Asset Bundle start:218.09MB, end 217.11MB, increase: -1001.52KB
2025-9-29 17:18:16-debug: 查询 Asset Bundle start, progress: 5%
2025-9-29 17:18:16-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 17:18:16-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-9-29 17:18:16-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 10%
2025-9-29 17:18:16-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-29 17:18:16-debug: [Build Memory track]: 查询 Asset Bundle start:217.14MB, end 217.44MB, increase: 308.40KB
2025-9-29 17:18:16-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 17:18:16-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:217.47MB, end 217.48MB, increase: 16.44KB
2025-9-29 17:18:16-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-29 17:18:16-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-9-29 17:18:16-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-29 17:18:16-debug: [Build Memory track]: 填充脚本数据到 settings.json start:217.51MB, end 217.53MB, increase: 16.25KB
2025-9-29 17:18:16-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-29 17:18:16-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-29 17:18:16-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 17:18:16-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-29 17:18:16-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-9-29 17:18:16-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:217.56MB, end 217.84MB, increase: 293.09KB
2025-9-29 17:18:48-debug: refresh db internal success
2025-9-29 17:18:48-debug: refresh db i18n success
2025-9-29 17:18:48-debug: refresh db assets success
2025-9-29 17:18:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 17:18:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 17:18:48-debug: asset-db:refresh-all-database (169ms)
2025-9-29 17:18:48-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 17:19:09-debug: refresh db internal success
2025-9-29 17:19:09-debug: refresh db i18n success
2025-9-29 17:19:09-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 17:19:09-debug: refresh db assets success
2025-9-29 17:19:09-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 17:19:09-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 17:19:09-debug: asset-db:refresh-all-database (136ms)
2025-9-29 17:19:09-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 17:19:10-debug: Query all assets info in project
2025-9-29 17:19:10-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-29 17:19:10-debug: Skip compress image, progress: 0%
2025-9-29 17:19:10-debug: Init all bundles start..., progress: 0%
2025-9-29 17:19:10-debug: 查询 Asset Bundle start, progress: 0%
2025-9-29 17:19:10-debug: Num of bundles: 17..., progress: 0%
2025-9-29 17:19:10-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 17:19:10-debug: Init bundle root assets start..., progress: 0%
2025-9-29 17:19:10-debug:   Number of all scenes: 11
2025-9-29 17:19:10-debug:   Number of all scripts: 275
2025-9-29 17:19:10-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-29 17:19:10-debug:   Number of other assets: 2158
2025-9-29 17:19:10-debug: Init bundle root assets success..., progress: 0%
2025-9-29 17:19:10-debug: // ---- build task 查询 Asset Bundle ---- (22ms)
2025-9-29 17:19:10-log: run build task 查询 Asset Bundle success in 22 ms√, progress: 5%
2025-9-29 17:19:10-debug: 查询 Asset Bundle start, progress: 5%
2025-9-29 17:19:10-debug: [Build Memory track]: 查询 Asset Bundle start:204.44MB, end 205.31MB, increase: 889.56KB
2025-9-29 17:19:10-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 17:19:10-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-9-29 17:19:10-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-9-29 17:19:10-debug: [Build Memory track]: 查询 Asset Bundle start:205.33MB, end 205.64MB, increase: 308.71KB
2025-9-29 17:19:10-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 17:19:10-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-29 17:19:10-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-29 17:19:10-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-29 17:19:10-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-29 17:19:10-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:205.67MB, end 205.69MB, increase: 26.83KB
2025-9-29 17:19:10-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-29 17:19:10-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-29 17:19:10-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-29 17:19:10-debug: [Build Memory track]: 填充脚本数据到 settings.json start:205.72MB, end 205.75MB, increase: 26.11KB
2025-9-29 17:19:10-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-29 17:19:10-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 17:19:10-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-29 17:19:10-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-9-29 17:19:10-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:205.77MB, end 206.06MB, increase: 295.86KB
2025-9-29 17:19:41-debug: refresh db internal success
2025-9-29 17:19:41-debug: refresh db i18n success
2025-9-29 17:19:41-debug: refresh db assets success
2025-9-29 17:19:41-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 17:19:41-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 17:19:41-debug: asset-db:refresh-all-database (126ms)
2025-9-29 17:19:41-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 17:19:41-debug: Query all assets info in project
2025-9-29 17:19:41-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-29 17:19:41-debug: Skip compress image, progress: 0%
2025-9-29 17:19:41-debug: Init all bundles start..., progress: 0%
2025-9-29 17:19:41-debug: 查询 Asset Bundle start, progress: 0%
2025-9-29 17:19:41-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 17:19:41-debug: Num of bundles: 17..., progress: 0%
2025-9-29 17:19:41-debug: Init bundle root assets start..., progress: 0%
2025-9-29 17:19:41-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-29 17:19:41-debug:   Number of all scripts: 275
2025-9-29 17:19:41-debug: Init bundle root assets success..., progress: 0%
2025-9-29 17:19:41-debug:   Number of other assets: 2158
2025-9-29 17:19:41-debug:   Number of all scenes: 11
2025-9-29 17:19:41-log: run build task 查询 Asset Bundle success in 20 ms√, progress: 5%
2025-9-29 17:19:41-debug: [Build Memory track]: 查询 Asset Bundle start:210.62MB, end 208.23MB, increase: -2445.99KB
2025-9-29 17:19:41-debug: 查询 Asset Bundle start, progress: 5%
2025-9-29 17:19:41-debug: // ---- build task 查询 Asset Bundle ---- (20ms)
2025-9-29 17:19:41-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 17:19:41-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-9-29 17:19:41-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-9-29 17:19:41-debug: [Build Memory track]: 查询 Asset Bundle start:208.26MB, end 208.56MB, increase: 309.50KB
2025-9-29 17:19:41-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-29 17:19:41-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 17:19:41-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-29 17:19:41-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-29 17:19:41-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:208.59MB, end 208.62MB, increase: 26.86KB
2025-9-29 17:19:41-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-29 17:19:41-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-29 17:19:41-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-29 17:19:41-debug: [Build Memory track]: 填充脚本数据到 settings.json start:208.65MB, end 208.66MB, increase: 15.68KB
2025-9-29 17:19:41-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-29 17:19:41-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 17:19:41-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-29 17:19:41-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-9-29 17:19:41-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:208.69MB, end 208.99MB, increase: 309.33KB
2025-9-29 17:20:14-debug: refresh db internal success
2025-9-29 17:20:14-debug: refresh db i18n success
2025-9-29 17:20:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 17:20:14-debug: refresh db assets success
2025-9-29 17:20:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 17:20:14-debug: asset-db:refresh-all-database (160ms)
2025-9-29 17:20:14-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 17:20:14-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 17:20:16-debug: refresh db internal success
2025-9-29 17:20:16-debug: refresh db i18n success
2025-9-29 17:20:16-debug: refresh db assets success
2025-9-29 17:20:16-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 17:20:16-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 17:20:16-debug: asset-db:refresh-all-database (158ms)
2025-9-29 17:20:16-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 17:20:16-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 17:20:18-debug: refresh db internal success
2025-9-29 17:20:18-debug: refresh db i18n success
2025-9-29 17:20:18-debug: refresh db assets success
2025-9-29 17:20:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 17:20:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 17:20:18-debug: asset-db:refresh-all-database (142ms)
2025-9-29 17:20:18-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 17:20:29-debug: refresh db internal success
2025-9-29 17:20:29-debug: refresh db i18n success
2025-9-29 17:20:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 17:20:29-debug: refresh db assets success
2025-9-29 17:20:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 17:20:29-debug: asset-db:refresh-all-database (132ms)
2025-9-29 17:20:29-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 17:20:29-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 17:20:39-debug: refresh db internal success
2025-9-29 17:20:39-debug: refresh db i18n success
2025-9-29 17:20:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 17:20:39-debug: refresh db assets success
2025-9-29 17:20:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 17:20:39-debug: asset-db:refresh-all-database (118ms)
2025-9-29 17:20:39-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 17:20:39-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 17:20:41-debug: refresh db internal success
2025-9-29 17:20:41-debug: refresh db i18n success
2025-9-29 17:20:41-debug: refresh db assets success
2025-9-29 17:20:41-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 17:20:41-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 17:20:41-debug: asset-db:refresh-all-database (119ms)
2025-9-29 17:22:10-debug: refresh db internal success
2025-9-29 17:22:10-debug: refresh db i18n success
2025-9-29 17:22:10-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\map\LevelLayerUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:22:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 17:22:10-debug: refresh db assets success
2025-9-29 17:22:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 17:22:10-debug: asset-db:refresh-all-database (149ms)
2025-9-29 17:22:10-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 17:22:10-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 17:22:31-debug: refresh db internal success
2025-9-29 17:22:31-debug: refresh db i18n success
2025-9-29 17:22:31-debug: refresh db assets success
2025-9-29 17:22:31-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 17:22:31-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 17:22:31-debug: asset-db:refresh-all-database (134ms)
2025-9-29 17:22:41-debug: Query all assets info in project
2025-9-29 17:22:41-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-29 17:22:41-debug: Skip compress image, progress: 0%
2025-9-29 17:22:41-debug: Init all bundles start..., progress: 0%
2025-9-29 17:22:41-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 17:22:41-debug: 查询 Asset Bundle start, progress: 0%
2025-9-29 17:22:41-debug: Num of bundles: 17..., progress: 0%
2025-9-29 17:22:41-debug: Init bundle root assets start..., progress: 0%
2025-9-29 17:22:41-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-29 17:22:41-debug:   Number of other assets: 2158
2025-9-29 17:22:41-debug:   Number of all scripts: 275
2025-9-29 17:22:41-debug:   Number of all scenes: 11
2025-9-29 17:22:41-debug: Init bundle root assets success..., progress: 0%
2025-9-29 17:22:41-debug: // ---- build task 查询 Asset Bundle ---- (25ms)
2025-9-29 17:22:41-log: run build task 查询 Asset Bundle success in 25 ms√, progress: 5%
2025-9-29 17:22:41-debug: [Build Memory track]: 查询 Asset Bundle start:206.52MB, end 205.88MB, increase: -651.53KB
2025-9-29 17:22:41-debug: 查询 Asset Bundle start, progress: 5%
2025-9-29 17:22:41-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 17:22:41-log: run build task 查询 Asset Bundle success in 5 ms√, progress: 10%
2025-9-29 17:22:41-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-29 17:22:41-debug: // ---- build task 查询 Asset Bundle ---- (5ms)
2025-9-29 17:22:41-debug: [Build Memory track]: 查询 Asset Bundle start:205.91MB, end 206.21MB, increase: 309.65KB
2025-9-29 17:22:41-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 17:22:41-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-29 17:22:41-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-29 17:22:41-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:206.24MB, end 206.27MB, increase: 30.29KB
2025-9-29 17:22:41-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-29 17:22:41-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-29 17:22:41-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-29 17:22:41-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-29 17:22:41-debug: [Build Memory track]: 填充脚本数据到 settings.json start:206.30MB, end 206.33MB, increase: 26.96KB
2025-9-29 17:22:41-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-29 17:22:41-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 17:22:41-log: run build task 整理部分构建选项内数据到 settings.json success in 3 ms√, progress: 15%
2025-9-29 17:22:41-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:206.36MB, end 206.64MB, increase: 295.84KB
2025-9-29 17:22:41-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-9-29 17:26:20-debug: refresh db internal success
2025-9-29 17:26:20-debug: refresh db i18n success
2025-9-29 17:26:20-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\map\LevelLayerUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:26:20-debug: refresh db assets success
2025-9-29 17:26:20-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 17:26:20-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 17:26:20-debug: asset-db:refresh-all-database (153ms)
2025-9-29 17:26:20-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 17:26:20-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 17:26:21-debug: Query all assets info in project
2025-9-29 17:26:21-debug: Skip compress image, progress: 0%
2025-9-29 17:26:21-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-29 17:26:21-debug: Init all bundles start..., progress: 0%
2025-9-29 17:26:21-debug: Num of bundles: 17..., progress: 0%
2025-9-29 17:26:21-debug: 查询 Asset Bundle start, progress: 0%
2025-9-29 17:26:21-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 17:26:21-debug: Init bundle root assets start..., progress: 0%
2025-9-29 17:26:21-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-29 17:26:21-debug:   Number of all scenes: 11
2025-9-29 17:26:21-debug:   Number of all scripts: 275
2025-9-29 17:26:21-debug:   Number of other assets: 2158
2025-9-29 17:26:21-debug: Init bundle root assets success..., progress: 0%
2025-9-29 17:26:21-debug: // ---- build task 查询 Asset Bundle ---- (27ms)
2025-9-29 17:26:21-log: run build task 查询 Asset Bundle success in 27 ms√, progress: 5%
2025-9-29 17:26:21-debug: 查询 Asset Bundle start, progress: 5%
2025-9-29 17:26:21-debug: [Build Memory track]: 查询 Asset Bundle start:215.60MB, end 212.74MB, increase: -2927.23KB
2025-9-29 17:26:21-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 17:26:21-debug: [Build Memory track]: 查询 Asset Bundle start:212.77MB, end 213.07MB, increase: 308.49KB
2025-9-29 17:26:21-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-9-29 17:26:21-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-29 17:26:21-log: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-9-29 17:26:21-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 17:26:21-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-29 17:26:21-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:213.10MB, end 213.13MB, increase: 25.74KB
2025-9-29 17:26:21-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-29 17:26:21-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-29 17:26:21-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-29 17:26:21-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-29 17:26:21-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-29 17:26:21-debug: [Build Memory track]: 填充脚本数据到 settings.json start:213.15MB, end 213.18MB, increase: 26.19KB
2025-9-29 17:26:21-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-29 17:26:21-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 17:26:21-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-29 17:26:21-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-9-29 17:26:21-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:213.21MB, end 213.49MB, increase: 293.22KB
2025-9-29 17:26:23-debug: refresh db internal success
2025-9-29 17:26:23-debug: refresh db i18n success
2025-9-29 17:26:23-debug: refresh db assets success
2025-9-29 17:26:23-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 17:26:23-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 17:26:23-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 17:26:23-debug: asset-db:refresh-all-database (145ms)
2025-9-29 17:26:23-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 17:26:24-debug: Query all assets info in project
2025-9-29 17:26:24-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-29 17:26:24-debug: Skip compress image, progress: 0%
2025-9-29 17:26:24-debug: Init all bundles start..., progress: 0%
2025-9-29 17:26:24-debug: Num of bundles: 17..., progress: 0%
2025-9-29 17:26:24-debug: 查询 Asset Bundle start, progress: 0%
2025-9-29 17:26:24-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 17:26:24-debug: Init bundle root assets start..., progress: 0%
2025-9-29 17:26:24-debug:   Number of all scenes: 11
2025-9-29 17:26:24-debug:   Number of other assets: 2158
2025-9-29 17:26:24-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-29 17:26:24-debug:   Number of all scripts: 275
2025-9-29 17:26:24-debug: Init bundle root assets success..., progress: 0%
2025-9-29 17:26:24-debug: // ---- build task 查询 Asset Bundle ---- (28ms)
2025-9-29 17:26:24-log: run build task 查询 Asset Bundle success in 28 ms√, progress: 5%
2025-9-29 17:26:24-debug: 查询 Asset Bundle start, progress: 5%
2025-9-29 17:26:24-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 17:26:24-debug: [Build Memory track]: 查询 Asset Bundle start:215.14MB, end 214.90MB, increase: -248.25KB
2025-9-29 17:26:24-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-9-29 17:26:24-log: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-9-29 17:26:24-debug: [Build Memory track]: 查询 Asset Bundle start:214.93MB, end 215.23MB, increase: 307.45KB
2025-9-29 17:26:24-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-29 17:26:24-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 17:26:24-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-29 17:26:24-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-29 17:26:24-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:215.25MB, end 215.28MB, increase: 25.96KB
2025-9-29 17:26:24-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-29 17:26:24-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-29 17:26:24-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-29 17:26:24-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-29 17:26:24-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-29 17:26:24-debug: [Build Memory track]: 填充脚本数据到 settings.json start:215.31MB, end 215.33MB, increase: 25.76KB
2025-9-29 17:26:24-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 17:26:24-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-9-29 17:26:24-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:215.36MB, end 215.65MB, increase: 292.03KB
2025-9-29 17:26:24-log: run build task 整理部分构建选项内数据到 settings.json success in 3 ms√, progress: 15%
2025-9-29 17:26:26-debug: refresh db internal success
2025-9-29 17:26:26-debug: refresh db i18n success
2025-9-29 17:26:26-debug: refresh db assets success
2025-9-29 17:26:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 17:26:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 17:26:26-debug: asset-db:refresh-all-database (132ms)
2025-9-29 17:26:26-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-29 17:26:26-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 17:33:25-debug: refresh db internal success
2025-9-29 17:33:25-debug: refresh db i18n success
2025-9-29 17:33:25-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\map\LevelLayerUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:33:25-debug: refresh db assets success
2025-9-29 17:33:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 17:33:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 17:33:25-debug: asset-db:refresh-all-database (156ms)
2025-9-29 17:33:27-debug: Query all assets info in project
2025-9-29 17:33:27-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-29 17:33:27-debug: Skip compress image, progress: 0%
2025-9-29 17:33:27-debug: Init all bundles start..., progress: 0%
2025-9-29 17:33:27-debug: 查询 Asset Bundle start, progress: 0%
2025-9-29 17:33:27-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 17:33:27-debug: Num of bundles: 17..., progress: 0%
2025-9-29 17:33:27-debug: Init bundle root assets start..., progress: 0%
2025-9-29 17:33:27-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-29 17:33:27-debug:   Number of all scenes: 11
2025-9-29 17:33:27-debug:   Number of other assets: 2158
2025-9-29 17:33:27-debug: Init bundle root assets success..., progress: 0%
2025-9-29 17:33:27-debug: // ---- build task 查询 Asset Bundle ---- (25ms)
2025-9-29 17:33:27-debug:   Number of all scripts: 275
2025-9-29 17:33:27-log: run build task 查询 Asset Bundle success in 25 ms√, progress: 5%
2025-9-29 17:33:27-debug: [Build Memory track]: 查询 Asset Bundle start:222.18MB, end 220.90MB, increase: -1311.29KB
2025-9-29 17:33:27-debug: 查询 Asset Bundle start, progress: 5%
2025-9-29 17:33:27-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 17:33:27-debug: [Build Memory track]: 查询 Asset Bundle start:220.93MB, end 221.23MB, increase: 308.06KB
2025-9-29 17:33:27-debug: // ---- build task 查询 Asset Bundle ---- (7ms)
2025-9-29 17:33:27-log: run build task 查询 Asset Bundle success in 7 ms√, progress: 10%
2025-9-29 17:33:27-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-29 17:33:27-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 17:33:27-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (9ms)
2025-9-29 17:33:27-log: run build task 整理部分构建选项内数据到 settings.json success in 9 ms√, progress: 12%
2025-9-29 17:33:27-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-29 17:33:27-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-29 17:33:27-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:221.26MB, end 221.29MB, increase: 26.82KB
2025-9-29 17:33:27-debug: // ---- build task 填充脚本数据到 settings.json ---- (8ms)
2025-9-29 17:33:27-log: run build task 填充脚本数据到 settings.json success in 8 ms√, progress: 13%
2025-9-29 17:33:27-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 17:33:27-debug: [Build Memory track]: 填充脚本数据到 settings.json start:221.31MB, end 202.58MB, increase: -19186.00KB
2025-9-29 17:33:27-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-29 17:33:27-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-29 17:33:27-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-9-29 17:33:27-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:202.61MB, end 202.91MB, increase: 307.75KB
2025-9-29 17:33:48-debug: refresh db internal success
2025-9-29 17:33:48-debug: refresh db i18n success
2025-9-29 17:33:48-debug: refresh db assets success
2025-9-29 17:33:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 17:33:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 17:33:48-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 17:33:48-debug: asset-db:refresh-all-database (124ms)
2025-9-29 17:33:48-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 17:37:04-debug: refresh db internal success
2025-9-29 17:37:04-debug: refresh db i18n success
2025-9-29 17:37:04-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:37:04-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\map\LevelLayerUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:37:04-debug: refresh db assets success
2025-9-29 17:37:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 17:37:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 17:37:04-debug: asset-db:refresh-all-database (157ms)
2025-9-29 17:37:04-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 17:37:04-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 17:52:49-debug: refresh db internal success
2025-9-29 17:52:49-debug: refresh db i18n success
2025-9-29 17:52:49-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:52:49-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:52:49-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\DefaultMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:52:49-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\IMovable.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:52:49-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 17:52:49-debug: refresh db assets success
2025-9-29 17:52:49-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 17:52:49-debug: asset-db:refresh-all-database (151ms)
2025-9-29 17:52:49-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 17:52:49-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 17:52:52-debug: refresh db internal success
2025-9-29 17:52:52-debug: refresh db i18n success
2025-9-29 17:52:52-debug: refresh db assets success
2025-9-29 17:52:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 17:52:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 17:52:52-debug: asset-db:refresh-all-database (152ms)
2025-9-29 17:52:52-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 17:52:52-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 17:52:55-debug: refresh db internal success
2025-9-29 17:52:55-debug: refresh db i18n success
2025-9-29 17:52:55-debug: refresh db assets success
2025-9-29 17:52:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 17:52:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 17:52:55-debug: asset-db:refresh-all-database (120ms)
2025-9-29 17:52:55-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 17:59:24-debug: refresh db internal success
2025-9-29 17:59:24-debug: refresh db i18n success
2025-9-29 17:59:24-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\DefaultMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:59:24-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\IMovable.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 17:59:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 17:59:24-debug: refresh db assets success
2025-9-29 17:59:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 17:59:24-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-29 17:59:24-debug: asset-db:refresh-all-database (157ms)
2025-9-29 17:59:24-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 18:00:03-debug: refresh db internal success
2025-9-29 18:00:03-debug: refresh db i18n success
2025-9-29 18:00:03-debug: refresh db assets success
2025-9-29 18:00:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 18:00:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 18:00:03-debug: asset-db:refresh-all-database (125ms)
2025-9-29 18:00:03-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 18:00:03-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 18:03:13-debug: refresh db internal success
2025-9-29 18:03:13-debug: refresh db i18n success
2025-9-29 18:03:13-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\Bullet.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 18:03:13-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\IMovable.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 18:03:13-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\conditions\EmitterEventConditions.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 18:03:13-debug: refresh db assets success
2025-9-29 18:03:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 18:03:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 18:03:13-debug: asset-db:refresh-all-database (165ms)
2025-9-29 18:03:13-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 18:03:13-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 18:03:43-debug: refresh db internal success
2025-9-29 18:03:43-debug: refresh db i18n success
2025-9-29 18:03:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\BulletSystem.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 18:03:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\IMovable.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 18:03:43-debug: refresh db assets success
2025-9-29 18:03:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 18:03:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 18:03:43-debug: asset-db:refresh-all-database (163ms)
2025-9-29 18:04:10-debug: refresh db internal success
2025-9-29 18:04:10-debug: refresh db i18n success
2025-9-29 18:04:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 18:04:11-debug: refresh db assets success
2025-9-29 18:04:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 18:04:11-debug: asset-db:refresh-all-database (152ms)
2025-9-29 18:04:11-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 18:04:11-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 18:04:45-debug: refresh db internal success
2025-9-29 18:04:45-debug: refresh db i18n success
2025-9-29 18:04:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 18:04:45-debug: refresh db assets success
2025-9-29 18:04:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 18:04:45-debug: asset-db:refresh-all-database (121ms)
2025-9-29 18:04:45-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 18:04:45-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 18:04:52-debug: refresh db internal success
2025-9-29 18:04:52-debug: refresh db i18n success
2025-9-29 18:04:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 18:04:52-debug: refresh db assets success
2025-9-29 18:04:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 18:04:52-debug: asset-db:refresh-all-database (118ms)
2025-9-29 18:04:52-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 18:04:52-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 18:05:04-debug: refresh db internal success
2025-9-29 18:05:04-debug: refresh db i18n success
2025-9-29 18:05:04-debug: refresh db assets success
2025-9-29 18:05:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 18:05:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 18:05:04-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 18:05:04-debug: asset-db:refresh-all-database (122ms)
2025-9-29 18:05:04-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 18:06:02-debug: refresh db internal success
2025-9-29 18:06:02-debug: refresh db i18n success
2025-9-29 18:06:02-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 18:06:02-debug: refresh db assets success
2025-9-29 18:06:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 18:06:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 18:06:02-debug: asset-db:refresh-all-database (164ms)
2025-9-29 18:13:56-debug: refresh db internal success
2025-9-29 18:13:56-debug: refresh db i18n success
2025-9-29 18:13:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\Bullet.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 18:13:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 18:13:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\DefaultMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 18:13:56-debug: refresh db assets success
2025-9-29 18:13:56-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 18:13:56-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 18:13:56-debug: asset-db:refresh-all-database (163ms)
2025-9-29 18:14:18-debug: refresh db internal success
2025-9-29 18:14:18-debug: refresh db i18n success
2025-9-29 18:14:19-debug: refresh db assets success
2025-9-29 18:14:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 18:14:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 18:14:19-debug: asset-db:refresh-all-database (119ms)
2025-9-29 18:14:25-debug: refresh db internal success
2025-9-29 18:14:25-debug: refresh db i18n success
2025-9-29 18:14:25-debug: refresh db assets success
2025-9-29 18:14:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 18:14:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 18:14:25-debug: asset-db:refresh-all-database (116ms)
2025-9-29 18:14:25-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 18:14:25-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 18:14:27-debug: refresh db internal success
2025-9-29 18:14:27-debug: refresh db i18n success
2025-9-29 18:14:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 18:14:27-debug: refresh db assets success
2025-9-29 18:14:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 18:14:27-debug: asset-db:refresh-all-database (113ms)
2025-9-29 18:14:29-debug: refresh db internal success
2025-9-29 18:14:29-debug: refresh db i18n success
2025-9-29 18:14:30-debug: refresh db assets success
2025-9-29 18:14:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 18:14:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 18:14:30-debug: asset-db:refresh-all-database (116ms)
2025-9-29 18:14:30-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 18:14:30-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 18:14:31-debug: refresh db internal success
2025-9-29 18:14:31-debug: refresh db i18n success
2025-9-29 18:14:32-debug: refresh db assets success
2025-9-29 18:14:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 18:14:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 18:14:32-debug: asset-db:refresh-all-database (130ms)
2025-9-29 18:14:32-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 18:14:32-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 18:14:36-debug: refresh db internal success
2025-9-29 18:14:36-debug: refresh db i18n success
2025-9-29 18:14:36-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 18:14:36-debug: refresh db assets success
2025-9-29 18:14:36-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 18:14:36-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 18:14:36-debug: asset-db:refresh-all-database (117ms)
2025-9-29 18:14:36-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 18:14:44-debug: refresh db internal success
2025-9-29 18:14:44-debug: refresh db i18n success
2025-9-29 18:14:44-debug: refresh db assets success
2025-9-29 18:14:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 18:14:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 18:14:44-debug: asset-db:refresh-all-database (116ms)
2025-9-29 18:14:44-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 18:14:44-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 18:14:51-debug: %cImport%c: E:\M2Game\Client\assets\scenes\PathEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-29 18:14:51-debug: asset-db:reimport-assetf87e7412-cf27-4eb5-a664-0071cad67cf8 (2ms)
2025-9-29 18:15:03-debug: refresh db internal success
2025-9-29 18:15:03-debug: refresh db i18n success
2025-9-29 18:15:03-debug: refresh db assets success
2025-9-29 18:15:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 18:15:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 18:15:03-debug: asset-db:refresh-all-database (127ms)
2025-9-29 18:15:03-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 18:15:03-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 18:15:05-debug: refresh db internal success
2025-9-29 18:15:05-debug: refresh db i18n success
2025-9-29 18:15:05-debug: refresh db assets success
2025-9-29 18:15:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 18:15:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 18:15:05-debug: asset-db:refresh-all-database (115ms)
2025-9-29 18:17:08-debug: refresh db internal success
2025-9-29 18:17:08-debug: refresh db i18n success
2025-9-29 18:17:09-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 18:17:09-debug: refresh db assets success
2025-9-29 18:17:09-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 18:17:09-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 18:17:09-debug: asset-db:refresh-all-database (153ms)
2025-9-29 18:17:22-debug: refresh db internal success
2025-9-29 18:17:22-debug: refresh db i18n success
2025-9-29 18:17:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 18:17:22-debug: refresh db assets success
2025-9-29 18:17:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 18:17:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 18:17:22-debug: asset-db:refresh-all-database (154ms)
2025-9-29 18:17:22-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 18:17:22-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 18:17:30-debug: refresh db internal success
2025-9-29 18:17:30-debug: refresh db i18n success
2025-9-29 18:17:31-debug: refresh db assets success
2025-9-29 18:17:31-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 18:17:31-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 18:17:31-debug: asset-db:refresh-all-database (118ms)
2025-9-29 18:17:31-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 18:17:31-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 18:17:33-debug: refresh db internal success
2025-9-29 18:17:33-debug: refresh db i18n success
2025-9-29 18:17:33-debug: refresh db assets success
2025-9-29 18:17:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 18:17:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 18:17:33-debug: asset-db:refresh-all-database (121ms)
2025-9-29 18:19:54-debug: refresh db internal success
2025-9-29 18:19:54-debug: refresh db i18n success
2025-9-29 18:19:54-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\IMovable.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 18:19:54-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 18:19:55-debug: refresh db assets success
2025-9-29 18:19:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 18:19:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 18:19:55-debug: asset-db:refresh-all-database (161ms)
2025-9-29 18:19:55-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 18:20:05-debug: refresh db internal success
2025-9-29 18:20:05-debug: refresh db i18n success
2025-9-29 18:20:05-debug: refresh db assets success
2025-9-29 18:20:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 18:20:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 18:20:05-debug: asset-db:refresh-all-database (120ms)
2025-9-29 18:20:06-debug: refresh db internal success
2025-9-29 18:20:06-debug: refresh db i18n success
2025-9-29 18:20:06-debug: refresh db assets success
2025-9-29 18:20:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 18:20:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 18:20:06-debug: asset-db:refresh-all-database (118ms)
2025-9-29 18:20:06-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 18:20:52-debug: refresh db internal success
2025-9-29 18:20:52-debug: refresh db i18n success
2025-9-29 18:20:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 18:20:52-debug: refresh db assets success
2025-9-29 18:20:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 18:20:52-debug: asset-db:refresh-all-database (150ms)
2025-9-29 18:20:52-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 18:41:42-debug: refresh db internal success
2025-9-29 18:41:42-debug: refresh db i18n success
2025-9-29 18:41:42-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 18:41:42-debug: refresh db assets success
2025-9-29 18:41:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 18:41:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 18:41:42-debug: asset-db:refresh-all-database (149ms)
2025-9-29 18:41:45-debug: refresh db internal success
2025-9-29 18:41:45-debug: refresh db i18n success
2025-9-29 18:41:45-debug: refresh db assets success
2025-9-29 18:41:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 18:41:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 18:41:45-debug: asset-db:refresh-all-database (157ms)
2025-9-29 18:41:48-debug: refresh db internal success
2025-9-29 18:41:48-debug: refresh db i18n success
2025-9-29 18:41:48-debug: refresh db assets success
2025-9-29 18:41:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 18:41:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 18:41:48-debug: asset-db:refresh-all-database (117ms)
2025-9-29 18:42:07-debug: refresh db internal success
2025-9-29 18:42:07-debug: refresh db i18n success
2025-9-29 18:42:08-debug: refresh db assets success
2025-9-29 18:42:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 18:42:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 18:42:08-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 18:42:08-debug: asset-db:refresh-all-database (121ms)
2025-9-29 18:42:08-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 18:42:10-debug: refresh db internal success
2025-9-29 18:42:10-debug: refresh db i18n success
2025-9-29 18:42:10-debug: refresh db assets success
2025-9-29 18:42:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 18:42:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 18:42:10-debug: asset-db:refresh-all-database (116ms)
2025-9-29 18:42:10-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 18:42:10-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 18:42:36-debug: refresh db internal success
2025-9-29 18:42:36-debug: refresh db i18n success
2025-9-29 18:42:36-debug: refresh db assets success
2025-9-29 18:42:36-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 18:42:36-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 18:42:36-debug: asset-db:refresh-all-database (122ms)
2025-9-29 18:51:22-debug: refresh db internal success
2025-9-29 18:51:22-debug: refresh db i18n success
2025-9-29 18:51:22-debug: refresh db assets success
2025-9-29 18:51:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 18:51:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 18:51:22-debug: asset-db:refresh-all-database (118ms)
2025-9-29 19:05:30-debug: refresh db internal success
2025-9-29 19:05:30-debug: refresh db i18n success
2025-9-29 19:05:30-debug: refresh db assets success
2025-9-29 19:05:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 19:05:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 19:05:30-debug: asset-db:refresh-all-database (147ms)
2025-9-29 19:05:30-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 19:05:30-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 19:05:32-debug: refresh db internal success
2025-9-29 19:05:32-debug: refresh db i18n success
2025-9-29 19:05:32-debug: refresh db assets success
2025-9-29 19:05:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 19:05:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 19:05:32-debug: asset-db:refresh-all-database (145ms)
2025-9-29 19:05:32-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-29 19:05:32-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 19:06:05-debug: refresh db internal success
2025-9-29 19:06:05-debug: refresh db i18n success
2025-9-29 19:06:05-debug: refresh db assets success
2025-9-29 19:06:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 19:06:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 19:06:05-debug: asset-db:refresh-all-database (126ms)
2025-9-29 19:06:05-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 19:14:36-debug: refresh db internal success
2025-9-29 19:14:36-debug: refresh db i18n success
2025-9-29 19:14:36-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 19:14:36-debug: refresh db assets success
2025-9-29 19:14:36-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 19:14:36-debug: asset-db:refresh-all-database (153ms)
2025-9-29 19:14:36-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 19:14:36-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 09:25:34-debug: refresh db internal success
2025-9-30 09:25:34-debug: refresh db i18n success
2025-9-30 09:25:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 09:25:34-debug: refresh db assets success
2025-9-30 09:25:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 09:25:34-debug: asset-db:refresh-all-database (262ms)
2025-9-30 09:25:34-debug: asset-db:worker-effect-data-processing (8ms)
2025-9-30 09:25:34-debug: asset-db-hook-engine-extends-afterRefresh (9ms)
2025-9-30 09:55:21-debug: refresh db internal success
2025-9-30 09:55:21-debug: refresh db i18n success
2025-9-30 09:55:21-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\plane
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:21-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\plane\mainplane
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresword.json
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000003.json
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\plane\mainplane\10001001.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbreswordgroup.json
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\middle\girl_image.png
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\plane\mainplane\10100001.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\plane\mainplane\10100005.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Doodad\EmittierIconArrow.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\LevelEmittier\TraainEmittier_HighSky_Fog with Angle.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\RandNod\RandNod_mount_01.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\middle\girl_image.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\RandNod\RandNod_mount_02.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\RandNod\RandNod_Mount_03.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\RandNod\RandNod_mount_04.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\middle\girl_image.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\RandNod\RandNod_Mount_05.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\RandNod\RandNod_Mount_06.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\RandNod\RandNod_Mount_07.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\RandNod\RandNod_mount_08.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\RandNod\RandNod_mount_09.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\RandNod\RandNod_mount_10.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\RandNod\RandNod_Mount_11.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\RandNod\RandNod_mount_12.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\RandNod\RandNod_mount_13.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\RandNod\RandNod_mount_14.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\RandNod\RandNod_mount_15.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\RandNod\RandNod_Null.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Doodad\IconArrow.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\scripts\resupdate
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\prefab
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbequipupgrade.json
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbplane.json
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresequip.json
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresgamemode.json
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresitem.json
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresloot.json
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\scripts\resupdate\ResUpdate.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\CommonEntry.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\prefab\Plane.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\plane
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_friend\prefab\ui
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_pk\prefab\ui
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_mail\prefab\ui
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000002.json
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000001.json
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\autogen\luban
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\plane
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\scenes
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\plane\PlaneManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\main
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\Plane.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\HomeUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\PlaneShowUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\middle
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_friend\prefab\ui\FriendUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_mail\prefab\ui\MailUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_pk\prefab\ui\PKUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\autogen\luban\schema.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\plane\PlaneData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager\GlobalDataManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\scenes\GameMain.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home\HomeUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home\PlaneShowUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home\TopUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\main\MainUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Doodad
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\LevelEmittier
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Clouds
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\RandNod
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\LevelEmittier\TraainEmittier_HighSky_Clouds_Lot.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Land
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Clouds\BG_Clouds01.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Land\Element_Lake2.png
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cReImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Land\Element_Lake2.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: %cReImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Land\Element_Lake2.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:55:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 09:55:22-debug: refresh db assets success
2025-9-30 09:55:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 09:55:22-debug: asset-db:refresh-all-database (383ms)
2025-9-30 09:55:27-debug: refresh db internal success
2025-9-30 09:55:27-debug: refresh db i18n success
2025-9-30 09:55:27-debug: refresh db assets success
2025-9-30 09:55:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 09:55:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 09:55:27-debug: asset-db:refresh-all-database (127ms)
2025-9-30 09:55:29-debug: refresh db internal success
2025-9-30 09:55:29-debug: refresh db i18n success
2025-9-30 09:55:30-debug: refresh db assets success
2025-9-30 09:55:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 09:55:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 09:55:30-debug: asset-db:refresh-all-database (125ms)
2025-9-30 09:57:02-debug: refresh db internal success
2025-9-30 09:57:02-debug: refresh db i18n success
2025-9-30 09:57:03-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 09:57:03-debug: refresh db assets success
2025-9-30 09:57:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 09:57:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 09:57:03-debug: asset-db:refresh-all-database (160ms)
2025-9-30 09:57:03-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 09:57:07-debug: refresh db internal success
2025-9-30 09:57:07-debug: refresh db i18n success
2025-9-30 09:57:07-debug: refresh db assets success
2025-9-30 09:57:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 09:57:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 09:57:07-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 09:57:07-debug: asset-db:refresh-all-database (117ms)
2025-9-30 09:57:07-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 09:57:08-debug: refresh db internal success
2025-9-30 09:57:08-debug: refresh db i18n success
2025-9-30 09:57:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 09:57:08-debug: refresh db assets success
2025-9-30 09:57:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 09:57:08-debug: asset-db:refresh-all-database (120ms)
2025-9-30 09:59:18-debug: refresh db internal success
2025-9-30 09:59:18-debug: refresh db i18n success
2025-9-30 09:59:18-debug: refresh db assets success
2025-9-30 09:59:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 09:59:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 09:59:18-debug: asset-db:refresh-all-database (160ms)
2025-9-30 09:59:18-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 09:59:18-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 10:10:52-debug: refresh db internal success
2025-9-30 10:10:52-debug: refresh db i18n success
2025-9-30 10:10:52-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 10:10:52-debug: refresh db assets success
2025-9-30 10:10:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:10:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:10:52-debug: asset-db:refresh-all-database (156ms)
2025-9-30 10:10:52-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 10:10:52-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 10:10:56-debug: refresh db internal success
2025-9-30 10:10:56-debug: refresh db i18n success
2025-9-30 10:10:56-debug: refresh db assets success
2025-9-30 10:10:56-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:10:56-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:10:56-debug: asset-db:refresh-all-database (143ms)
2025-9-30 10:10:58-debug: refresh db internal success
2025-9-30 10:10:58-debug: refresh db i18n success
2025-9-30 10:10:58-debug: refresh db assets success
2025-9-30 10:10:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:10:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:10:58-debug: asset-db:refresh-all-database (129ms)
2025-9-30 10:10:58-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-30 10:11:18-debug: refresh db internal success
2025-9-30 10:11:18-debug: refresh db i18n success
2025-9-30 10:11:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 10:11:18-debug: refresh db assets success
2025-9-30 10:11:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:11:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:11:18-debug: asset-db:refresh-all-database (123ms)
2025-9-30 10:11:28-debug: refresh db internal success
2025-9-30 10:11:28-debug: refresh db i18n success
2025-9-30 10:11:28-debug: refresh db assets success
2025-9-30 10:11:28-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:11:28-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:11:28-debug: asset-db:refresh-all-database (128ms)
2025-9-30 10:11:28-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 10:11:28-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 10:11:30-debug: refresh db internal success
2025-9-30 10:11:30-debug: refresh db i18n success
2025-9-30 10:11:30-debug: refresh db assets success
2025-9-30 10:11:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:11:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:11:30-debug: asset-db:refresh-all-database (132ms)
2025-9-30 10:11:30-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 10:12:07-debug: refresh db internal success
2025-9-30 10:12:07-debug: refresh db i18n success
2025-9-30 10:12:07-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 10:12:07-debug: refresh db assets success
2025-9-30 10:12:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:12:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:12:07-debug: asset-db:refresh-all-database (160ms)
2025-9-30 10:12:07-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 10:12:07-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 10:12:11-debug: refresh db internal success
2025-9-30 10:12:11-debug: refresh db i18n success
2025-9-30 10:12:11-debug: refresh db assets success
2025-9-30 10:12:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:12:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:12:11-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 10:12:11-debug: asset-db:refresh-all-database (148ms)
2025-9-30 10:12:11-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 10:12:12-debug: refresh db internal success
2025-9-30 10:12:12-debug: refresh db i18n success
2025-9-30 10:12:12-debug: refresh db assets success
2025-9-30 10:12:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:12:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:12:12-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 10:12:12-debug: asset-db:refresh-all-database (135ms)
2025-9-30 10:12:12-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 10:12:32-debug: refresh db internal success
2025-9-30 10:12:32-debug: refresh db i18n success
2025-9-30 10:12:32-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 10:12:32-debug: refresh db assets success
2025-9-30 10:12:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:12:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:12:32-debug: asset-db:refresh-all-database (162ms)
2025-9-30 10:12:32-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 10:12:32-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 10:12:35-debug: refresh db internal success
2025-9-30 10:12:35-debug: refresh db i18n success
2025-9-30 10:12:35-debug: refresh db assets success
2025-9-30 10:12:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:12:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:12:35-debug: asset-db:refresh-all-database (156ms)
2025-9-30 10:12:35-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 10:12:35-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 10:12:36-debug: refresh db internal success
2025-9-30 10:12:36-debug: refresh db i18n success
2025-9-30 10:12:36-debug: refresh db assets success
2025-9-30 10:12:36-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:12:36-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:12:36-debug: asset-db:refresh-all-database (150ms)
2025-9-30 10:12:36-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 10:12:36-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 10:13:09-debug: refresh db internal success
2025-9-30 10:13:09-debug: refresh db i18n success
2025-9-30 10:13:09-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 10:13:09-debug: refresh db assets success
2025-9-30 10:13:09-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:13:09-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:13:09-debug: asset-db:refresh-all-database (167ms)
2025-9-30 10:13:09-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 10:13:09-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-30 10:13:13-debug: refresh db internal success
2025-9-30 10:13:13-debug: refresh db i18n success
2025-9-30 10:13:13-debug: refresh db assets success
2025-9-30 10:13:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:13:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:13:13-debug: asset-db:refresh-all-database (142ms)
2025-9-30 10:13:13-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 10:13:13-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 10:13:15-debug: refresh db internal success
2025-9-30 10:13:15-debug: refresh db i18n success
2025-9-30 10:13:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:13:15-debug: refresh db assets success
2025-9-30 10:13:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:13:15-debug: asset-db:refresh-all-database (120ms)
2025-9-30 10:13:15-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 10:13:15-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 10:13:52-debug: refresh db internal success
2025-9-30 10:13:52-debug: refresh db i18n success
2025-9-30 10:13:53-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 10:13:53-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:13:53-debug: refresh db assets success
2025-9-30 10:13:53-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:13:53-debug: asset-db:refresh-all-database (156ms)
2025-9-30 10:13:53-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 10:13:53-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 10:13:57-debug: refresh db internal success
2025-9-30 10:13:57-debug: refresh db i18n success
2025-9-30 10:13:57-debug: refresh db assets success
2025-9-30 10:13:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:13:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:13:57-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 10:13:57-debug: asset-db:refresh-all-database (120ms)
2025-9-30 10:13:57-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 10:13:59-debug: refresh db internal success
2025-9-30 10:13:59-debug: refresh db i18n success
2025-9-30 10:13:59-debug: refresh db assets success
2025-9-30 10:13:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:13:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:13:59-debug: asset-db:refresh-all-database (123ms)
2025-9-30 10:13:59-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 10:13:59-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 10:14:21-debug: refresh db internal success
2025-9-30 10:14:21-debug: refresh db i18n success
2025-9-30 10:14:21-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 10:14:21-debug: refresh db assets success
2025-9-30 10:14:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:14:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:14:21-debug: asset-db:refresh-all-database (149ms)
2025-9-30 10:14:34-debug: refresh db internal success
2025-9-30 10:14:34-debug: refresh db i18n success
2025-9-30 10:14:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 10:14:34-debug: refresh db assets success
2025-9-30 10:14:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:14:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:14:34-debug: asset-db:refresh-all-database (171ms)
2025-9-30 10:14:34-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 10:14:34-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 10:14:38-debug: refresh db internal success
2025-9-30 10:14:38-debug: refresh db i18n success
2025-9-30 10:14:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:14:38-debug: refresh db assets success
2025-9-30 10:14:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:14:38-debug: asset-db:refresh-all-database (145ms)
2025-9-30 10:14:38-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 10:14:39-debug: refresh db internal success
2025-9-30 10:14:39-debug: refresh db i18n success
2025-9-30 10:14:40-debug: refresh db assets success
2025-9-30 10:14:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:14:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:14:40-debug: asset-db:refresh-all-database (119ms)
2025-9-30 10:14:40-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 10:14:40-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 10:14:46-debug: refresh db internal success
2025-9-30 10:14:46-debug: refresh db i18n success
2025-9-30 10:14:47-debug: refresh db assets success
2025-9-30 10:14:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:14:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:14:47-debug: asset-db:refresh-all-database (116ms)
2025-9-30 10:14:47-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 10:15:29-debug: refresh db internal success
2025-9-30 10:15:29-debug: refresh db i18n success
2025-9-30 10:15:29-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 10:15:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:15:29-debug: refresh db assets success
2025-9-30 10:15:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:15:29-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 10:15:29-debug: asset-db:refresh-all-database (157ms)
2025-9-30 10:15:29-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 10:16:14-debug: refresh db internal success
2025-9-30 10:16:14-debug: refresh db i18n success
2025-9-30 10:16:14-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 10:16:14-debug: refresh db assets success
2025-9-30 10:16:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:16:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:16:14-debug: asset-db:refresh-all-database (150ms)
2025-9-30 10:16:14-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-30 10:16:14-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-30 10:16:16-debug: refresh db internal success
2025-9-30 10:16:16-debug: refresh db i18n success
2025-9-30 10:16:16-debug: refresh db assets success
2025-9-30 10:16:16-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:16:16-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:16:16-debug: asset-db:refresh-all-database (161ms)
2025-9-30 10:16:16-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 10:16:16-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 10:16:18-debug: refresh db internal success
2025-9-30 10:16:18-debug: refresh db i18n success
2025-9-30 10:16:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:16:19-debug: refresh db assets success
2025-9-30 10:16:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:16:19-debug: asset-db:refresh-all-database (150ms)
2025-9-30 10:16:19-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 10:16:19-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 10:16:55-debug: refresh db internal success
2025-9-30 10:16:55-debug: refresh db i18n success
2025-9-30 10:16:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 10:16:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:16:55-debug: refresh db assets success
2025-9-30 10:16:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:16:55-debug: asset-db:refresh-all-database (156ms)
2025-9-30 10:16:55-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 10:16:55-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 10:16:59-debug: refresh db internal success
2025-9-30 10:16:59-debug: refresh db i18n success
2025-9-30 10:16:59-debug: refresh db assets success
2025-9-30 10:16:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:16:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:16:59-debug: asset-db:refresh-all-database (150ms)
2025-9-30 10:16:59-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 10:17:01-debug: refresh db internal success
2025-9-30 10:17:01-debug: refresh db i18n success
2025-9-30 10:17:01-debug: refresh db assets success
2025-9-30 10:17:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:17:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:17:01-debug: asset-db:refresh-all-database (126ms)
2025-9-30 10:43:27-debug: refresh db internal success
2025-9-30 10:43:27-debug: refresh db i18n success
2025-9-30 10:43:27-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 10:43:27-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 10:43:27-debug: refresh db assets success
2025-9-30 10:43:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:43:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:43:27-debug: asset-db:refresh-all-database (163ms)
2025-9-30 10:43:33-debug: refresh db internal success
2025-9-30 10:43:33-debug: refresh db i18n success
2025-9-30 10:43:33-debug: refresh db assets success
2025-9-30 10:43:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:43:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:43:33-debug: asset-db:refresh-all-database (126ms)
2025-9-30 10:43:35-debug: refresh db internal success
2025-9-30 10:43:35-debug: refresh db i18n success
2025-9-30 10:43:36-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:43:36-debug: refresh db assets success
2025-9-30 10:43:36-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:43:36-debug: asset-db:refresh-all-database (121ms)
2025-9-30 10:43:48-debug: refresh db internal success
2025-9-30 10:43:48-debug: refresh db i18n success
2025-9-30 10:43:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 10:43:48-debug: refresh db assets success
2025-9-30 10:43:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:43:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:43:48-debug: asset-db:refresh-all-database (155ms)
2025-9-30 10:43:48-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-30 10:43:48-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-30 10:43:52-debug: refresh db internal success
2025-9-30 10:43:52-debug: refresh db i18n success
2025-9-30 10:43:52-debug: refresh db assets success
2025-9-30 10:43:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:43:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:43:52-debug: asset-db:refresh-all-database (143ms)
2025-9-30 10:43:52-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 10:43:52-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 10:43:53-debug: refresh db internal success
2025-9-30 10:43:53-debug: refresh db i18n success
2025-9-30 10:43:53-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:43:53-debug: refresh db assets success
2025-9-30 10:43:53-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:43:53-debug: asset-db:refresh-all-database (120ms)
2025-9-30 10:43:53-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 10:43:53-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 10:44:20-debug: refresh db internal success
2025-9-30 10:44:20-debug: refresh db i18n success
2025-9-30 10:44:20-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 10:44:20-debug: refresh db assets success
2025-9-30 10:44:20-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:44:20-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:44:20-debug: asset-db:refresh-all-database (165ms)
2025-9-30 10:44:20-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 10:44:20-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 10:44:25-debug: refresh db internal success
2025-9-30 10:44:25-debug: refresh db i18n success
2025-9-30 10:44:25-debug: refresh db assets success
2025-9-30 10:44:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:44:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:44:25-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 10:44:25-debug: asset-db:refresh-all-database (122ms)
2025-9-30 10:44:25-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 10:44:27-debug: refresh db internal success
2025-9-30 10:44:27-debug: refresh db i18n success
2025-9-30 10:44:28-debug: refresh db assets success
2025-9-30 10:44:28-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:44:28-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:44:28-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 10:44:28-debug: asset-db:refresh-all-database (115ms)
2025-9-30 10:44:28-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-30 10:45:05-debug: refresh db internal success
2025-9-30 10:45:05-debug: refresh db i18n success
2025-9-30 10:45:05-debug: refresh db assets success
2025-9-30 10:45:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:45:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:45:05-debug: asset-db:refresh-all-database (151ms)
2025-9-30 10:45:05-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-30 10:45:05-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-30 10:45:18-debug: refresh db internal success
2025-9-30 10:45:18-debug: refresh db i18n success
2025-9-30 10:45:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 10:45:18-debug: refresh db assets success
2025-9-30 10:45:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:45:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:45:18-debug: asset-db:refresh-all-database (132ms)
2025-9-30 10:45:18-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 10:45:18-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 10:45:20-debug: refresh db internal success
2025-9-30 10:45:20-debug: refresh db i18n success
2025-9-30 10:45:20-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:45:20-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:45:20-debug: refresh db assets success
2025-9-30 10:45:20-debug: asset-db:refresh-all-database (123ms)
2025-9-30 10:45:20-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 10:45:20-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 10:45:22-debug: refresh db internal success
2025-9-30 10:45:22-debug: refresh db i18n success
2025-9-30 10:45:22-debug: refresh db assets success
2025-9-30 10:45:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:45:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:45:22-debug: asset-db:refresh-all-database (122ms)
2025-9-30 10:45:22-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 10:45:22-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 10:46:06-debug: refresh db internal success
2025-9-30 10:46:06-debug: refresh db i18n success
2025-9-30 10:46:06-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 10:46:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:46:06-debug: refresh db assets success
2025-9-30 10:46:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:46:06-debug: asset-db:refresh-all-database (158ms)
2025-9-30 10:46:06-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 10:46:06-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 10:46:09-debug: refresh db internal success
2025-9-30 10:46:09-debug: refresh db i18n success
2025-9-30 10:46:09-debug: refresh db assets success
2025-9-30 10:46:09-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:46:09-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:46:09-debug: asset-db:refresh-all-database (147ms)
2025-9-30 10:46:09-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 10:46:09-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 10:46:11-debug: refresh db internal success
2025-9-30 10:46:11-debug: refresh db i18n success
2025-9-30 10:46:11-debug: refresh db assets success
2025-9-30 10:46:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:46:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:46:11-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 10:46:11-debug: asset-db:refresh-all-database (118ms)
2025-9-30 10:46:11-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 10:52:37-debug: refresh db internal success
2025-9-30 10:52:37-debug: refresh db i18n success
2025-9-30 10:52:37-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 10:52:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:52:37-debug: refresh db assets success
2025-9-30 10:52:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:52:37-debug: asset-db:refresh-all-database (161ms)
2025-9-30 10:52:40-debug: refresh db internal success
2025-9-30 10:52:40-debug: refresh db i18n success
2025-9-30 10:52:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:52:40-debug: refresh db assets success
2025-9-30 10:52:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:52:40-debug: asset-db:refresh-all-database (149ms)
2025-9-30 10:52:41-debug: refresh db internal success
2025-9-30 10:52:41-debug: refresh db i18n success
2025-9-30 10:52:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:52:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:52:42-debug: refresh db assets success
2025-9-30 10:52:42-debug: asset-db:refresh-all-database (150ms)
2025-9-30 10:52:42-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 10:52:42-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 10:52:55-debug: refresh db internal success
2025-9-30 10:52:55-debug: refresh db i18n success
2025-9-30 10:52:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 10:52:55-debug: refresh db assets success
2025-9-30 10:52:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:52:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:52:55-debug: asset-db:refresh-all-database (156ms)
2025-9-30 10:52:55-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 10:52:58-debug: refresh db internal success
2025-9-30 10:52:58-debug: refresh db i18n success
2025-9-30 10:52:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:52:58-debug: refresh db assets success
2025-9-30 10:52:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:52:58-debug: asset-db:refresh-all-database (152ms)
2025-9-30 10:53:01-debug: refresh db internal success
2025-9-30 10:53:01-debug: refresh db i18n success
2025-9-30 10:53:01-debug: refresh db assets success
2025-9-30 10:53:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 10:53:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 10:53:01-debug: asset-db:refresh-all-database (121ms)
2025-9-30 11:02:42-debug: refresh db internal success
2025-9-30 11:02:42-debug: refresh db i18n success
2025-9-30 11:02:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 11:02:43-debug: refresh db assets success
2025-9-30 11:02:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 11:02:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 11:02:43-debug: asset-db:refresh-all-database (166ms)
2025-9-30 11:02:43-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 11:02:46-debug: refresh db internal success
2025-9-30 11:02:46-debug: refresh db i18n success
2025-9-30 11:02:46-debug: refresh db assets success
2025-9-30 11:02:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 11:02:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 11:02:46-debug: asset-db:refresh-all-database (175ms)
2025-9-30 11:02:46-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 11:02:48-debug: refresh db internal success
2025-9-30 11:02:48-debug: refresh db i18n success
2025-9-30 11:02:48-debug: refresh db assets success
2025-9-30 11:02:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 11:02:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 11:02:48-debug: asset-db:refresh-all-database (120ms)
2025-9-30 11:02:59-debug: refresh db internal success
2025-9-30 11:02:59-debug: refresh db i18n success
2025-9-30 11:03:00-debug: refresh db assets success
2025-9-30 11:03:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 11:03:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 11:03:00-debug: asset-db:refresh-all-database (128ms)
2025-9-30 11:03:00-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 11:03:28-debug: refresh db internal success
2025-9-30 11:03:28-debug: refresh db i18n success
2025-9-30 11:03:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 11:03:28-debug: refresh db assets success
2025-9-30 11:03:28-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 11:03:28-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 11:03:28-debug: asset-db:refresh-all-database (127ms)
2025-9-30 11:03:28-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 11:03:28-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 11:03:34-debug: refresh db internal success
2025-9-30 11:03:34-debug: refresh db i18n success
2025-9-30 11:03:34-debug: refresh db assets success
2025-9-30 11:03:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 11:03:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 11:03:34-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 11:03:34-debug: asset-db:refresh-all-database (123ms)
2025-9-30 11:03:34-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 11:03:37-debug: refresh db internal success
2025-9-30 11:03:37-debug: refresh db i18n success
2025-9-30 11:03:37-debug: refresh db assets success
2025-9-30 11:03:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 11:03:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 11:03:37-debug: asset-db:refresh-all-database (117ms)
2025-9-30 11:03:39-debug: refresh db internal success
2025-9-30 11:03:39-debug: refresh db i18n success
2025-9-30 11:03:39-debug: refresh db assets success
2025-9-30 11:03:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 11:03:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 11:03:39-debug: asset-db:refresh-all-database (120ms)
2025-9-30 11:03:39-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 11:04:57-debug: refresh db internal success
2025-9-30 11:04:57-debug: refresh db i18n success
2025-9-30 11:04:57-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 11:04:57-debug: refresh db assets success
2025-9-30 11:04:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 11:04:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 11:04:57-debug: asset-db:refresh-all-database (158ms)
2025-9-30 11:04:57-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 11:04:57-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 11:05:01-debug: refresh db internal success
2025-9-30 11:05:01-debug: refresh db i18n success
2025-9-30 11:05:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 11:05:01-debug: refresh db assets success
2025-9-30 11:05:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 11:05:01-debug: asset-db:refresh-all-database (165ms)
2025-9-30 11:05:01-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 11:05:01-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 11:05:02-debug: refresh db internal success
2025-9-30 11:05:02-debug: refresh db i18n success
2025-9-30 11:05:02-debug: refresh db assets success
2025-9-30 11:05:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 11:05:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 11:05:02-debug: asset-db:refresh-all-database (124ms)
2025-9-30 11:05:26-debug: refresh db internal success
2025-9-30 11:05:26-debug: refresh db i18n success
2025-9-30 11:05:26-debug: refresh db assets success
2025-9-30 11:05:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 11:05:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 11:05:26-debug: asset-db:refresh-all-database (152ms)
2025-9-30 11:05:48-debug: refresh db internal success
2025-9-30 11:05:48-debug: refresh db i18n success
2025-9-30 11:05:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 11:05:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 11:05:48-debug: refresh db assets success
2025-9-30 11:05:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 11:05:48-debug: asset-db:refresh-all-database (129ms)
2025-9-30 11:05:52-debug: refresh db internal success
2025-9-30 11:05:52-debug: refresh db i18n success
2025-9-30 11:05:52-debug: refresh db assets success
2025-9-30 11:05:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 11:05:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 11:05:52-debug: asset-db:refresh-all-database (121ms)
2025-9-30 11:05:55-debug: refresh db internal success
2025-9-30 11:05:55-debug: refresh db i18n success
2025-9-30 11:05:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 11:05:55-debug: refresh db assets success
2025-9-30 11:05:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 11:05:55-debug: asset-db:refresh-all-database (118ms)
2025-9-30 11:07:56-debug: refresh db internal success
2025-9-30 11:07:56-debug: refresh db i18n success
2025-9-30 11:07:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 11:07:56-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 11:07:56-debug: refresh db assets success
2025-9-30 11:07:56-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 11:07:56-debug: asset-db:refresh-all-database (160ms)
2025-9-30 11:07:56-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 11:07:59-debug: refresh db internal success
2025-9-30 11:07:59-debug: refresh db i18n success
2025-9-30 11:07:59-debug: refresh db assets success
2025-9-30 11:07:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 11:07:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 11:07:59-debug: asset-db:refresh-all-database (151ms)
2025-9-30 11:08:01-debug: refresh db internal success
2025-9-30 11:08:01-debug: refresh db i18n success
2025-9-30 11:08:01-debug: refresh db assets success
2025-9-30 11:08:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 11:08:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 11:08:01-debug: asset-db:refresh-all-database (152ms)
2025-9-30 11:08:11-debug: refresh db internal success
2025-9-30 11:08:11-debug: refresh db i18n success
2025-9-30 11:08:11-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 11:08:11-debug: refresh db assets success
2025-9-30 11:08:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 11:08:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 11:08:11-debug: asset-db:refresh-all-database (126ms)
2025-9-30 11:08:11-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 11:08:13-debug: refresh db internal success
2025-9-30 11:08:13-debug: refresh db i18n success
2025-9-30 11:08:14-debug: refresh db assets success
2025-9-30 11:08:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 11:08:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 11:08:14-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 11:08:14-debug: asset-db:refresh-all-database (133ms)
2025-9-30 11:08:14-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 11:08:16-debug: refresh db internal success
2025-9-30 11:08:16-debug: refresh db i18n success
2025-9-30 11:08:16-debug: refresh db assets success
2025-9-30 11:08:16-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 11:08:16-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 11:08:16-debug: asset-db:refresh-all-database (122ms)
2025-9-30 11:08:16-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 11:09:11-debug: refresh db internal success
2025-9-30 11:09:11-debug: refresh db i18n success
2025-9-30 11:09:11-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 11:09:11-debug: refresh db assets success
2025-9-30 11:09:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 11:09:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 11:09:11-debug: asset-db:refresh-all-database (160ms)
2025-9-30 11:09:11-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 11:09:11-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 11:09:15-debug: refresh db internal success
2025-9-30 11:09:15-debug: refresh db i18n success
2025-9-30 11:09:15-debug: refresh db assets success
2025-9-30 11:09:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 11:09:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 11:09:15-debug: asset-db:refresh-all-database (159ms)
2025-9-30 11:09:15-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-30 11:09:15-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-30 11:09:17-debug: refresh db internal success
2025-9-30 11:09:17-debug: refresh db i18n success
2025-9-30 11:09:17-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 11:09:17-debug: refresh db assets success
2025-9-30 11:09:17-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 11:09:17-debug: asset-db:refresh-all-database (120ms)
2025-9-30 11:09:17-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 11:09:17-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 11:09:35-debug: refresh db internal success
2025-9-30 11:09:35-debug: refresh db i18n success
2025-9-30 11:09:35-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 11:09:35-debug: refresh db assets success
2025-9-30 11:09:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 11:09:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 11:09:35-debug: asset-db:refresh-all-database (164ms)
2025-9-30 11:09:35-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-30 11:09:35-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-30 11:09:39-debug: refresh db internal success
2025-9-30 11:09:39-debug: refresh db i18n success
2025-9-30 11:09:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 11:09:39-debug: refresh db assets success
2025-9-30 11:09:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 11:09:39-debug: asset-db:refresh-all-database (157ms)
2025-9-30 11:09:39-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 11:09:39-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 11:09:41-debug: refresh db internal success
2025-9-30 11:09:41-debug: refresh db i18n success
2025-9-30 11:09:41-debug: refresh db assets success
2025-9-30 11:09:41-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 11:09:41-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 11:09:41-debug: asset-db:refresh-all-database (125ms)
2025-9-30 11:09:41-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 11:09:41-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 11:11:28-debug: refresh db internal success
2025-9-30 11:11:28-debug: refresh db i18n success
2025-9-30 11:11:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 11:11:28-debug: refresh db assets success
2025-9-30 11:11:28-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 11:11:28-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 11:11:28-debug: asset-db:refresh-all-database (156ms)
2025-9-30 11:11:28-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 11:11:31-debug: refresh db internal success
2025-9-30 11:11:31-debug: refresh db i18n success
2025-9-30 11:11:31-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 11:11:31-debug: refresh db assets success
2025-9-30 11:11:31-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 11:11:31-debug: asset-db:refresh-all-database (154ms)
2025-9-30 11:11:31-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 11:11:31-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 11:11:32-debug: refresh db internal success
2025-9-30 11:11:32-debug: refresh db i18n success
2025-9-30 11:11:32-debug: refresh db assets success
2025-9-30 11:11:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 11:11:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 11:11:32-debug: asset-db:refresh-all-database (150ms)
2025-9-30 11:11:38-debug: refresh db internal success
2025-9-30 11:11:38-debug: refresh db i18n success
2025-9-30 11:11:38-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-30 11:11:38-debug: refresh db assets success
2025-9-30 11:11:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 11:11:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 11:11:38-debug: asset-db:refresh-all-database (122ms)
2025-9-30 11:11:38-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 11:13:01-debug: refresh db internal success
2025-9-30 11:13:01-debug: refresh db i18n success
2025-9-30 11:13:01-debug: refresh db assets success
2025-9-30 11:13:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 11:13:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 11:13:01-debug: asset-db:worker-effect-data-processing (33ms)
2025-9-30 11:13:01-debug: asset-db:refresh-all-database (227ms)
2025-9-30 11:13:01-debug: asset-db-hook-engine-extends-afterRefresh (33ms)
2025-9-30 11:13:01-debug: refresh db internal success
2025-9-30 11:13:01-debug: refresh db i18n success
2025-9-30 11:13:02-debug: refresh db assets success
2025-9-30 11:13:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 11:13:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 11:13:02-debug: asset-db:refresh-all-database (152ms)
2025-9-30 11:13:02-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-30 11:13:03-debug: refresh db internal success
2025-9-30 11:13:03-debug: refresh db i18n success
2025-9-30 11:13:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-30 11:13:04-debug: refresh db assets success
2025-9-30 11:13:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-30 11:13:04-debug: asset-db:refresh-all-database (152ms)
2025-9-30 11:13:04-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-30 11:13:04-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
