System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, _crd, DataEvent;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "4ff57oMUJ1Gv7GKi2FOnT04", "DataEvent", undefined);

      _export("DataEvent", DataEvent = {
        ItemsRefresh: 'DataEvent_ItemsRefresh',
        EquipSlotRefresh: 'DataEvent_EquipSlotRefresh',
        GamePvpGetAward: 'DataEvent_GamePvpGetAward',
        GamePvpGetList: 'DataEvent_GamePvpGetList',
        GamePvpMatchSuc: 'DataEvent_GamePvpMatchSuc',
        BattleItemClick: 'DataEvent_BattleItemClick',
        RogueSelectClick: 'DataEvent_RogueSelectClick',
        TaskRefresh: 'DataEvent_TaskRefresh',
        RoleBaseAttrChange: 'DataEvent_RoleBaseAttrChange',
        RoleExtAttrChange: 'DataEvent_RoleExtAttrChange',
        MailRefresh: 'DataEvent_MailRefresh',
        FriendRefresh: 'DataEvent_FriendRefresh'
      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=ddfebf349616332ee9ca8952a5ff5688555a4ded.js.map