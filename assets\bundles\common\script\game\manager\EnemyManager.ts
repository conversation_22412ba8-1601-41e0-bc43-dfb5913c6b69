import { Node, Prefab, instantiate } from "cc";
import { MyApp } from 'db://assets/bundles/common/script/app/MyApp';
import { SingletonBase } from "../../../../../scripts/core/base/SingletonBase";
import { GameEnum } from "../const/GameEnum";
import GameResourceList from "../const/GameResourceList";
import { EnemyData } from "../data/EnemyData"
import { GameIns } from "../GameIns";
import BattleLayer from "../ui/layer/BattleLayer";
import type EnemyPlane from "../ui/plane/enemy/EnemyPlane";
import { Tools } from "../utils/Tools";


export class EnemyManager extends SingletonBase<EnemyManager> {
    _normalCount = 0;
    _pfPlane: Prefab | null = null;
    _planePool: Node[] = [];
    _planeArr: EnemyPlane[] = [];
    _willDeadPlane: EnemyPlane[] = [];

    get enemies() {
        return this._planeArr;
    }

    public preLoad() {
        GameIns.battleManager.addLoadCount(1);
        MyApp.resMgr.load(GameResourceList.EnemyPlane, Prefab, (error: any, prefab: Prefab) => {
            this._pfPlane = prefab;
            GameIns.battleManager.checkLoadFinish();
        });
    }

    public addPlane(id: number) {
        try {
            const planeData = new EnemyData();
            planeData.planeId = id;

            let node = this._planePool.pop()! || this.createNewPlane()!;
            BattleLayer.instance.addEnemy(node);

            const plane = node.getComponent("EnemyPlane") as EnemyPlane;
            plane.initPlane(planeData);
            this.pushPlane(plane);

            this._normalCount++;
            return plane;
        } catch (error) {
            return null;
        }
    }

    createNewPlane(): Node {
        if (!this._pfPlane) {
            throw new Error("Plane prefab is not initialized. Call preLoad() first.");
        }
        const node: Node = instantiate(this._pfPlane);
        return node;
    }

    get planes(): EnemyPlane[] {
        return this._planeArr;
    }

    /**
     * 移除所有存活的敌机
     */
    removeAllAlivePlane() {
        for (const plane of this._planeArr) {
            if (!plane.isDead) {
                plane.willRemove();
            }
        }
    }

    /**
     * 添加敌机到管理器
     * @param plane 敌机对象
     */
    pushPlane(plane: EnemyPlane) {
        if (!Tools.arrContain(this._planeArr, plane)) {
            this._planeArr.push(plane);
        }
    }

    /**
     * 更新游戏逻辑
     * @param deltaTime 每帧时间
     */
    updateGameLogic(deltaTime: number) {
        for (let i = 0; i < this._planeArr.length; i++) {
            const plane = this._planeArr[i];
            if (plane.removeAble) {
                this.removePlaneForIndex(i);
                i--;
            } else {
                if (plane.isDead) {
                    if (plane.type === GameEnum.EnemyType.Turret || plane.type === GameEnum.EnemyType.Ship) {
                        this._willDeadPlane.push(plane);
                        this._planeArr.splice(i, 1);
                        i--;
                        continue;
                    }
                }
                plane.updateGameLogic(deltaTime);
            }
        }

        for (let i = 0; i < this._willDeadPlane.length; i++) {
            const plane = this._willDeadPlane[i];
            if (plane.removeAble) {
                this.removePlaneForIndex(i, true);
                i--;
            } else {
                plane.updateGameLogic(deltaTime);
            }
        }
    }

    mainReset() {
        this.subReset();
        // 清理飞机池
        for (const plane of this._planePool) {
            plane.destroy();
        }
        this._planePool.splice(0);

        // 清理即将死亡的飞机
        for (const plane of this._willDeadPlane) {
            if (plane && plane.node) {
                plane.node.destroy();
            }
        }
        this._willDeadPlane = [];
    }

    /**
     * 重置子关卡
     */
    subReset() {
        let EnemyType = GameEnum.EnemyType;
        for (const plane of this._planeArr) {
            switch (plane.type) {
                case EnemyType.Normal:
                    plane.willRemove();
                    this._planePool.push(plane.node);
                    break;
            }
            plane.node.removeFromParent();
        }
        this._planeArr.splice(0);
    }


    /**
     * 清理敌人管理器
     */
    clear() {

    }

    /**
     * 检查敌人是否全部消灭
     */
    isEnemyOver(): boolean {
        return this._planeArr.length === 0;
    }

    /**
     * 获取普通敌机数量
     */
    getNormalPlaneCount(): number {
        return this._normalCount;
    }

    /**
     * 根据索引移除敌机
     * @param index 索引
     * @param isDead 是否为死亡敌机
     */
    removePlaneForIndex(index: number, isDead: boolean = false) {
        if (isDead) {
            this._willRemovePlane(this._willDeadPlane[index]);
            this._willDeadPlane.splice(index, 1);
        } else {
            this._willRemovePlane(this._planeArr[index]);
            this._planeArr.splice(index, 1);
        }
    }


    /**
     * 处理即将移除的敌机
     * @param plane 敌机对象
     */
    _willRemovePlane(plane: EnemyPlane) {
        let EnemyType = GameEnum.EnemyType;
        switch (plane.type) {
            case EnemyType.Normal:
                this._normalCount--;
                this._planePool.push(plane.node);
                break;
        }
        plane.node.removeFromParent();
    }

    setAnimSpeed(speed: number) {
        for (const plane of this._planeArr) {
            plane.setAnimSpeed(speed);
        }
    }
}