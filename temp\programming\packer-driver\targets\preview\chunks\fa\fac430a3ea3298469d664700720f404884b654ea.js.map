{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/story/StoryUI.ts"], "names": ["_decorator", "Label", "Node", "MyApp", "BundleName", "ButtonPlus", "MessageBox", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "DataEvent", "EventMgr", "startGameByMode", "GameEnum", "BuidingUI", "BuildingInfoUI", "ccclass", "property", "StoryUI", "index", "imageUrls", "gameMode", "undefined", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "getBundleName", "HomeStory", "onLoad", "on", "BattleItemClick", "onBattleItemClick", "lubanTables", "TbResGameMode", "get", "list", "getDataList", "row", "bagItems", "buidings", "children", "for<PERSON>ach", "element", "getComponent", "setNewFrame", "setTitle", "modeID", "btnClose", "addClick", "closeUI", "onDestroy", "targetOff", "item", "lblBattle", "string", "openUI", "onShow", "btnMap", "onMapClick", "onHide", "onClose", "onList<PERSON>ender", "listItem", "name", "getComponentInChildren", "show", "GameModeId", "ENDLESS"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;;AACnBC,MAAAA,K,iBAAAA,K;;AAEAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,e,iBAAAA,e;;AACAC,MAAAA,Q,kBAAAA,Q;;AACAC,MAAAA,S,kBAAAA,S;;AACAC,MAAAA,c,kBAAAA,c;;;;;;;;;OAGH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBjB,U;;yBAGjBkB,O,WADZF,OAAO,CAAC,SAAD,C,UAEHC,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ,CAAChB,KAAD,C,UAERgB,QAAQ,CAACf,IAAD,C,UAKRe,QAAQ;AAAA;AAAA,mC,2BAXb,MACaC,OADb;AAAA;AAAA,4BACoC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAOxBC,KAPwB,GAOX,CAPW;AAAA,eAQxBC,SARwB,GAQF,CAAC,UAAD,EAAa,UAAb,EAAyB,UAAzB,EAAqC,UAArC,EAAiD,UAAjD,EAA6D,UAA7D,EAAyE,UAAzE,CARE;AAAA,eAShCC,QATgC,GASIC,SATJ;;AAAA;AAAA;;AAaZ,eAANC,MAAM,GAAW;AAAE,iBAAO,mBAAP;AAA4B;;AACvC,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,OAAf;AAAwB;;AACjC,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,SAAlB;AAA6B;;AAC3DC,QAAAA,MAAM,GAAS;AACrB;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,sCAAUC,eAAtB,EAAuC,KAAKC,iBAA5C,EAA+D,IAA/D;AAEA,eAAKV,QAAL,GAAgB;AAAA;AAAA,8BAAMW,WAAN,CAAkBC,aAAlB,CAAgCC,GAAhC,CAAoC,IAApC,CAAhB,CAHqB,CAIrB;;AACA,cAAIC,IAAmB,GAAG;AAAA;AAAA,8BAAMH,WAAN,CAAkBC,aAAlB,CAAgCG,WAAhC,EAA1B;AACA,cAAIC,GAAG,GAAG,CAAV;AACA,cAAMC,QAAQ,GAAG,KAAKC,QAAL,CAAeC,QAAf,CAAwBC,OAAxB,CAAgCC,OAAO,IAAI;AACxDA,YAAAA,OAAO,CAAEC,YAAT;AAAA;AAAA,wCAAkCC,WAAlC,CAA8C,KAAKxB,SAAL,CAAeiB,GAAf,CAA9C;AACAK,YAAAA,OAAO,CAAEC,YAAT;AAAA;AAAA,wCAAkCE,QAAlC,CAA2CV,IAAI,CAACE,GAAD,CAAJ,CAAUS,MAArD,cAAkET,GAAG,GAAG,CAAxE;AACAA,YAAAA,GAAG;AACN,WAJgB,CAAjB;AAKA,eAAKU,QAAL,CAAeC,QAAf,CAAwB,KAAKC,OAA7B,EAAsC,IAAtC;AACH;;AACKA,QAAAA,OAAO,GAAG;AAAA;AACZ;AACA;AAAA;AAAA,gCAAMA,OAAN,CAAc/B,OAAd;AAFY;AAGf;;AACSgC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AACOpB,QAAAA,iBAAiB,CAACZ,KAAD,EAAgBiC,IAAhB,EAA8B;AACnD,eAAKjC,KAAL,GAAaA,KAAb;AACA,eAAKkC,SAAL,CAAgBC,MAAhB,GAA4BF,IAA5B,SAAoCjC,KAApC;AACA;AAAA;AAAA,8BAAMoC,MAAN;AAAA;AAAA;AACH;;AACKC,QAAAA,MAAM,GAAgC;AAAA;;AAAA;AACxC,YAAA,KAAI,CAACC,MAAL,CAAaT,QAAb,CAAsB,KAAI,CAACU,UAA3B,EAAuC,KAAvC;AADwC;AAE3C;;AAEKC,QAAAA,MAAM,GAAgC;AAAA;AAAG;;AAEzCC,QAAAA,OAAO,GAAgC;AAAA;AAAG;;AAEhDC,QAAAA,YAAY,CAACC,QAAD,EAAiBzB,GAAjB,EAA8B;AACtCyB,UAAAA,QAAQ,CAACC,IAAT,gBAA2B1B,GAA3B;AACAyB,UAAAA,QAAQ,CAACE,sBAAT,CAAgC/D,KAAhC,EAAwCqD,MAAxC,eAAsDjB,GAAG,GAAG,CAA5D;AACH;;AAEKqB,QAAAA,UAAU,GAAG;AAAA;;AAAA;AACf,gBAAI,MAAI,CAACvC,KAAL,IAAc,CAAlB,EAAqB;AACjB;AAAA;AAAA,4CAAW8C,IAAX,CAAgB,OAAhB;AACA;AACH;;AACD;AAAA;AAAA,oDAAgB;AAAA;AAAA,sCAASC,UAAT,CAAoBC,OAApC;AALe;AAMlB;;AA7D+B,O;;;;;iBAEJ,I;;;;;;;iBAEF,I;;;;;;;iBAEF,I;;;;;;;iBAKM,I", "sourcesContent": ["import { _decorator, Label, Node } from 'cc';\nimport { MyApp } from 'db://assets/bundles/common/script/app/MyApp';\nimport { ResGameMode } from 'db://assets/bundles/common/script/autogen/luban/schema';\nimport { BundleName } from 'db://assets/bundles/common/script/const/BundleConst';\nimport { ButtonPlus } from 'db://assets/bundles/common/script/ui/common/components/button/ButtonPlus';\nimport { MessageBox } from 'db://assets/bundles/common/script/ui/common/components/messagebox/MessageBox';\nimport { BaseUI, UILayer, UIMgr } from 'db://assets/scripts/core/base/UIMgr';\nimport { DataEvent } from '../../event/DataEvent';\nimport { EventMgr } from '../../event/EventManager';\nimport { startGameByMode } from '../../game/GameInsStart';\nimport { GameEnum } from '../../game/const/GameEnum';\nimport { BuidingUI } from './BuidingUI';\nimport { BuildingInfoUI } from './BuildingInfoUI';\n\n\nconst { ccclass, property } = _decorator;\n\n@ccclass(\"StoryUI\")\nexport class StoryUI extends BaseUI {\n    @property(ButtonPlus)\n    btnMap: ButtonPlus | null = null;\n    @property(Label)\n    lblBattle: Label | null = null;\n    @property(Node)\n    buidings: Node | null = null;\n    private index: any = 0;\n    private imageUrls: string[] = [\"item_7_1\", \"item_7_2\", \"item_7_3\", \"item_7_4\", \"item_7_5\", \"item_7_6\", \"item_7_8\"];\n    gameMode: ResGameMode | undefined = undefined;\n    @property(ButtonPlus)\n    btnClose: ButtonPlus | null = null;\n\n    public static getUrl(): string { return \"prefab/ui/StoryUI\" };\n    public static getLayer(): UILayer { return UILayer.Default }\n    public static getBundleName(): string { return BundleName.HomeStory }\n    protected onLoad(): void {\n        EventMgr.on(DataEvent.BattleItemClick, this.onBattleItemClick, this);\n\n        this.gameMode = MyApp.lubanTables.TbResGameMode.get(2001);\n        //let list: GameMode[] = MyApp.lubanTables.TbResGameMode.getDataList().filter(element => element.modeType == ModeType.STORY);\n        let list: ResGameMode[] = MyApp.lubanTables.TbResGameMode.getDataList();\n        let row = 0;\n        const bagItems = this.buidings!.children.forEach(element => {\n            element!.getComponent(BuidingUI)!.setNewFrame(this.imageUrls[row]);\n            element!.getComponent(BuidingUI)!.setTitle(list[row].modeID, `第${(row + 1)}关`);\n            row++;\n        });\n        this.btnClose!.addClick(this.closeUI, this);\n    }\n    async closeUI() {\n        //await UIMgr.openUI(HomeUI)\n        UIMgr.closeUI(StoryUI)\n    }\n    protected onDestroy(): void {\n        EventMgr.targetOff(this)\n    }\n    private onBattleItemClick(index: number, item: string) {\n        this.index = index;\n        this.lblBattle!.string = `${item}(${index})`;\n        UIMgr.openUI(BuildingInfoUI);\n    }\n    async onShow(...args: any[]): Promise<void> {\n        this.btnMap!.addClick(this.onMapClick, this);\n    }\n\n    async onHide(...args: any[]): Promise<void> { }\n\n    async onClose(...args: any[]): Promise<void> { }\n\n    onListRender(listItem: Node, row: number) {\n        listItem.name = `listItem${row}`\n        listItem.getComponentInChildren(Label)!.string = `第${(row + 1)}关`;\n    }\n\n    async onMapClick() {\n        if (this.index == 0) {\n            MessageBox.show(\"未选择地图\");\n            return;\n        }\n        startGameByMode(GameEnum.GameModeId.ENDLESS);\n    }\n}\n"]}