System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, Prefab, instantiate, MyApp, SingletonBase, GameEnum, GameResourceList, EnemyData, GameIns, BattleLayer, Tools, EnemyManager, _crd;

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/bundles/common/script/app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfSingletonBase(extras) {
    _reporterNs.report("SingletonBase", "../../../../../scripts/core/base/SingletonBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEnum(extras) {
    _reporterNs.report("GameEnum", "../const/GameEnum", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameResourceList(extras) {
    _reporterNs.report("GameResourceList", "../const/GameResourceList", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyData(extras) {
    _reporterNs.report("EnemyData", "../data/EnemyData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBattleLayer(extras) {
    _reporterNs.report("BattleLayer", "../ui/layer/BattleLayer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyPlane(extras) {
    _reporterNs.report("EnemyPlane", "../ui/plane/enemy/EnemyPlane", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "../utils/Tools", _context.meta, extras);
  }

  _export("EnemyManager", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      Prefab = _cc.Prefab;
      instantiate = _cc.instantiate;
    }, function (_unresolved_2) {
      MyApp = _unresolved_2.MyApp;
    }, function (_unresolved_3) {
      SingletonBase = _unresolved_3.SingletonBase;
    }, function (_unresolved_4) {
      GameEnum = _unresolved_4.GameEnum;
    }, function (_unresolved_5) {
      GameResourceList = _unresolved_5.default;
    }, function (_unresolved_6) {
      EnemyData = _unresolved_6.EnemyData;
    }, function (_unresolved_7) {
      GameIns = _unresolved_7.GameIns;
    }, function (_unresolved_8) {
      BattleLayer = _unresolved_8.default;
    }, function (_unresolved_9) {
      Tools = _unresolved_9.Tools;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "7a8acsCga9NJbCkCaR42MV3", "EnemyManager", undefined);

      __checkObsolete__(['Node', 'Prefab', 'instantiate']);

      _export("EnemyManager", EnemyManager = class EnemyManager extends (_crd && SingletonBase === void 0 ? (_reportPossibleCrUseOfSingletonBase({
        error: Error()
      }), SingletonBase) : SingletonBase) {
        constructor(...args) {
          super(...args);
          this._normalCount = 0;
          this._pfPlane = null;
          this._planePool = [];
          this._planeArr = [];
          this._willDeadPlane = [];
        }

        get enemies() {
          return this._planeArr;
        }

        preLoad() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.addLoadCount(1);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.load((_crd && GameResourceList === void 0 ? (_reportPossibleCrUseOfGameResourceList({
            error: Error()
          }), GameResourceList) : GameResourceList).EnemyPlane, Prefab, (error, prefab) => {
            this._pfPlane = prefab;
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.checkLoadFinish();
          });
        }

        addPlane(id) {
          try {
            const planeData = new (_crd && EnemyData === void 0 ? (_reportPossibleCrUseOfEnemyData({
              error: Error()
            }), EnemyData) : EnemyData)();
            planeData.planeId = id;
            let node = this._planePool.pop() || this.createNewPlane();
            (_crd && BattleLayer === void 0 ? (_reportPossibleCrUseOfBattleLayer({
              error: Error()
            }), BattleLayer) : BattleLayer).instance.addEnemy(node);
            const plane = node.getComponent("EnemyPlane");
            plane.initPlane(planeData);
            this.pushPlane(plane);
            this._normalCount++;
            return plane;
          } catch (error) {
            return null;
          }
        }

        createNewPlane() {
          if (!this._pfPlane) {
            throw new Error("Plane prefab is not initialized. Call preLoad() first.");
          }

          const node = instantiate(this._pfPlane);
          return node;
        }

        get planes() {
          return this._planeArr;
        }
        /**
         * 移除所有存活的敌机
         */


        removeAllAlivePlane() {
          for (const plane of this._planeArr) {
            if (!plane.isDead) {
              plane.willRemove();
            }
          }
        }
        /**
         * 添加敌机到管理器
         * @param plane 敌机对象
         */


        pushPlane(plane) {
          if (!(_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).arrContain(this._planeArr, plane)) {
            this._planeArr.push(plane);
          }
        }
        /**
         * 更新游戏逻辑
         * @param deltaTime 每帧时间
         */


        updateGameLogic(deltaTime) {
          for (let i = 0; i < this._planeArr.length; i++) {
            const plane = this._planeArr[i];

            if (plane.removeAble) {
              this.removePlaneForIndex(i);
              i--;
            } else {
              if (plane.isDead) {
                if (plane.type === (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                  error: Error()
                }), GameEnum) : GameEnum).EnemyType.Turret || plane.type === (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                  error: Error()
                }), GameEnum) : GameEnum).EnemyType.Ship) {
                  this._willDeadPlane.push(plane);

                  this._planeArr.splice(i, 1);

                  i--;
                  continue;
                }
              }

              plane.updateGameLogic(deltaTime);
            }
          }

          for (let i = 0; i < this._willDeadPlane.length; i++) {
            const plane = this._willDeadPlane[i];

            if (plane.removeAble) {
              this.removePlaneForIndex(i, true);
              i--;
            } else {
              plane.updateGameLogic(deltaTime);
            }
          }
        }

        mainReset() {
          this.subReset(); // 清理飞机池

          for (const plane of this._planePool) {
            plane.destroy();
          }

          this._planePool.splice(0); // 清理即将死亡的飞机


          for (const plane of this._willDeadPlane) {
            if (plane && plane.node) {
              plane.node.destroy();
            }
          }

          this._willDeadPlane = [];
        }
        /**
         * 重置子关卡
         */


        subReset() {
          let EnemyType = (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).EnemyType;

          for (const plane of this._planeArr) {
            switch (plane.type) {
              case EnemyType.Normal:
                plane.willRemove();

                this._planePool.push(plane.node);

                break;
            }

            plane.node.removeFromParent();
          }

          this._planeArr.splice(0);
        }
        /**
         * 清理敌人管理器
         */


        clear() {}
        /**
         * 检查敌人是否全部消灭
         */


        isEnemyOver() {
          return this._planeArr.length === 0;
        }
        /**
         * 获取普通敌机数量
         */


        getNormalPlaneCount() {
          return this._normalCount;
        }
        /**
         * 根据索引移除敌机
         * @param index 索引
         * @param isDead 是否为死亡敌机
         */


        removePlaneForIndex(index, isDead = false) {
          if (isDead) {
            this._willRemovePlane(this._willDeadPlane[index]);

            this._willDeadPlane.splice(index, 1);
          } else {
            this._willRemovePlane(this._planeArr[index]);

            this._planeArr.splice(index, 1);
          }
        }
        /**
         * 处理即将移除的敌机
         * @param plane 敌机对象
         */


        _willRemovePlane(plane) {
          let EnemyType = (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).EnemyType;

          switch (plane.type) {
            case EnemyType.Normal:
              this._normalCount--;

              this._planePool.push(plane.node);

              break;
          }

          plane.node.removeFromParent();
        }

        setAnimSpeed(speed) {
          for (const plane of this._planeArr) {
            plane.setAnimSpeed(speed);
          }
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=9518a1f41add60ffba208eb94b8d97cd7cd90e9c.js.map