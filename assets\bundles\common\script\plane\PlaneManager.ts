import { instantiate, Node, NodePool, Prefab } from "cc";
import { PlaneData } from "db://assets/bundles/common/script/data/plane/PlaneData";
import { Plane } from "db://assets/bundles/common/script/ui/Plane";
import { IMgr } from "db://assets/scripts/core/base/IMgr";
import { MyApp } from "../app/MyApp";
import { BundleName } from "../const/BundleConst";

export class PlaneManager extends IMgr {
    _planePreFab: Prefab | null = null;
    _planePoor: NodePool = new NodePool();

    async load(){
        let plane = await MyApp.resMgr.loadAsync(BundleName.Common, "prefab/Plane", Prefab)
        this.initPlanePreFab(plane)
    }

    initPlanePreFab(prefab: Prefab) {
        if (!prefab) {
            throw new Error("Invalid prefab: prefab is null or undefined.");
        }
        this._planePreFab = prefab;
    }

    getPlane(planeData: PlaneData) {
        let planeNode: Node;
        if (this._planePoor.size() > 0) {
            const node = this._planePoor.get();
            if (!node) {
                throw new Error("NodePool returned null despite having available nodes.");
            }
            planeNode = node;
        } else {
            if (!this._planePreFab) {
                throw new Error("Plane prefab is not initialized. Call initPlanePreFab first.");
            }
            planeNode = instantiate(this._planePreFab);
        }

        const planeComponent = planeNode.getComponent(Plane);
        planeComponent!.initPlane(planeData);

        return planeNode;
    }

    //回收英雄，不直接调用，调用英雄的remove方法
    recyclePlane(planeNode: Node) {
        planeNode.active = true;
        planeNode.setPosition(0, 0);
        planeNode.setSiblingIndex(0);
        planeNode.setScale(1, 1);
        this._planePoor.put(planeNode);
    }
}