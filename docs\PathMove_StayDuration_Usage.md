# PathMove停留功能使用说明

## 概述

PathMove组件现在支持在路径点停留功能。当战机到达设置了`stayDuration`的路径点时，会在该点停留指定的时间，然后继续沿路径移动。

## 功能特性

- **自动停留**: 当战机到达有停留时间的路径点时，自动开始停留
- **精确计时**: 停留时间以毫秒为单位设置，运行时精确计时
- **状态查询**: 可以查询当前是否在停留状态以及剩余停留时间
- **手动控制**: 可以强制结束停留状态

## 使用方法

### 1. 设置路径点停留时间

在PathPoint中设置`stayDuration`属性（单位：毫秒）：

```typescript
// 创建路径点
const pathPoint = new PathPoint();
pathPoint.x = 100;
pathPoint.y = 200;
pathPoint.speed = 300;
pathPoint.stayDuration = 2000; // 停留2秒
```

### 2. 查询停留状态

```typescript
const pathMove = this.getComponent(PathMove);

// 检查是否正在停留
if (pathMove.isStaying()) {
    console.log("战机正在停留");
    
    // 获取剩余停留时间（秒）
    const remainingTime = pathMove.getRemainingStayTime();
    console.log(`剩余停留时间: ${remainingTime}秒`);
}
```

### 3. 手动控制停留

```typescript
// 强制结束停留状态
pathMove.endStay();
```

## 实现细节

### 停留检测机制

- 系统会检测当前路径点的`stayDuration`属性
- 插值生成的细分点的`stayDuration`永远为0，只有原始路径点才可能有停留时间
- 当战机接近有停留时间的路径点时（容差5像素），自动触发停留
- 每个路径点只会触发一次停留（除非重置路径）

### 停留期间的行为

- **位置**: 战机保持在当前位置
- **倾斜效果**: 如果启用了倾斜效果，停留期间仍会继续
- **朝向**: 保持停留开始时的朝向
- **移动**: 暂停沿路径的移动

### 状态重置

以下操作会重置停留状态：
- `resetToStart()`: 重置到路径起点
- `moveToEnd()`: 移动到路径终点  
- `setProgress()`: 设置路径进度
- `endStay()`: 手动结束停留

## 注意事项

1. **时间单位**: PathPoint中的`stayDuration`以毫秒为单位，但API返回的剩余时间以秒为单位
2. **检测精度**: 停留检测使用5像素的容差，确保在高速移动时也能准确触发
3. **性能**: 停留检测只在移动时进行，不会影响性能
4. **循环路径**: 在循环路径中，每次经过停留点都会重新触发停留

## 示例场景

### Boss战机停留攻击

```typescript
// Boss在特定位置停留3秒进行攻击
const bossPath = new PathData();
const attackPoint = new PathPoint(400, 300);
attackPoint.stayDuration = 3000; // 停留3秒
attackPoint.speed = 200;
bossPath.points.push(attackPoint);
```

### 巡逻路径

```typescript
// 巡逻兵在关键点停留观察
const patrolPoint = new PathPoint(200, 150);
patrolPoint.stayDuration = 1500; // 停留1.5秒观察
patrolPoint.speed = 100;
```
