{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/data/friend/Friend.ts"], "names": ["Friend", "csproto", "MyApp", "init", "netMgr", "registerHandler", "cs", "CS_CMD", "CS_CMD_GET_FRIEND_LIST", "onGetFriendInfoMsg", "CS_CMD_GET_VIP_INFO", "onGetVIPInfoMsg", "msg", "update"], "mappings": ";;;8CAGaA,M;;;;;;;;;;;;;;;;;;;;;;AAHNC,MAAAA,O;;AACEC,MAAAA,K,iBAAAA,K;;;;;;;wBAEIF,M,GAAN,MAAMA,MAAN,CAA8B;AAE1BG,QAAAA,IAAI,GAAS;AAChB;AAAA;AAAA,8BAAMC,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBC,sBAA/C,EAAuE,KAAKC,kBAA5E,EAAgG,IAAhG;AACA;AAAA;AAAA,8BAAML,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBG,mBAA/C,EAAoE,KAAKC,eAAzE,EAA0F,IAA1F,EAFgB,CAIhB;AACA;AACH;;AACOF,QAAAA,kBAAkB,CAACG,GAAD,EAA0B,CACnD;;AACOD,QAAAA,eAAe,CAACC,GAAD,EAA0B,CAChD;;AACMC,QAAAA,MAAM,GAAS,CACrB;;AAdgC,O", "sourcesContent": ["import csproto from 'db://assets/bundles/common/script/autogen/pb/cs_proto.js';\nimport { MyApp } from \"../../app/MyApp\";\nimport { IData } from \"../DataManager\";\nexport class Friend implements IData {\n\n    public init(): void {\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GET_FRIEND_LIST, this.onGetFriendInfoMsg, this);\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GET_VIP_INFO, this.onGetVIPInfoMsg, this);\n\n        //MessageBox.show(\"错误码：\\n\" + csproto.comm.RET_CODE[csproto.comm.RET_CODE.RET_CODE_CARD_GROUP_FORCE_ID_INVALID]);\n        //MessageBox.show(\"错误码：\\n\" + csproto.comm.RET_CODE[10111]);\n    }\n    private onGetFriendInfoMsg(msg: csproto.cs.IS2CMsg) {\n    }\n    private onGetVIPInfoMsg(msg: csproto.cs.IS2CMsg) {\n    }\n    public update(): void {\n    }\n}\n"]}