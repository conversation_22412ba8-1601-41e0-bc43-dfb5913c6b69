import { IData } from "db://assets/bundles/common/script/data/DataManager";
import { PlaneData } from "./PlaneData";



export class PlaneCacheInfo implements IData {
    _isInit = false;
    _planeDataDic:{[key:string]:PlaneData} = {}; //飞机数据


    public init(): void {
        this._isInit = true;
        this.onNetAllPlaneInfo()
    }

    update(): void {
    }

    onNetAllPlaneInfo(){
        let serverPlaneList = [
            {
                planeId:10003101,
            }
        ]

        for(let data of serverPlaneList){
            let planeData = new PlaneData();
            planeData.planeId = data.planeId;
            this._planeDataDic[planeData.planeId] = planeData;
        }
    }

    getPlaneInfoById(id:number = 10003101){
        if (!this._isInit){
            this.init();
        }
        return this._planeDataDic[id]
    }
}
