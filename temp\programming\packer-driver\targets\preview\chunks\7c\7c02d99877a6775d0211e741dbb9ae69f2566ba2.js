System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "long", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, csproto, MessageBox, logWarn, Long, MyApp, EventManager, GameEvent, DataMgr, GameLogic, _crd;

  function _reportPossibleCrUseOfcsproto(extras) {
    _reporterNs.report("csproto", "db://assets/bundles/common/script/autogen/pb/cs_proto.js", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMessageBox(extras) {
    _reporterNs.report("MessageBox", "db://assets/bundles/common/script/ui/common/components/messagebox/MessageBox", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogWarn(extras) {
    _reporterNs.report("logWarn", "db://assets/scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLong(extras) {
    _reporterNs.report("Long", "long", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "../../app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfModeType(extras) {
    _reporterNs.report("ModeType", "../../autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventManager(extras) {
    _reporterNs.report("EventManager", "../../event/EventManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEvent(extras) {
    _reporterNs.report("GameEvent", "../../game/event/GameEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDataMgr(extras) {
    _reporterNs.report("DataMgr", "../DataManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIData(extras) {
    _reporterNs.report("IData", "../DataManager", _context.meta, extras);
  }

  _export("GameLogic", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      csproto = _unresolved_2.default;
    }, function (_unresolved_3) {
      MessageBox = _unresolved_3.MessageBox;
    }, function (_unresolved_4) {
      logWarn = _unresolved_4.logWarn;
    }, function (_long) {
      Long = _long.default;
    }, function (_unresolved_5) {
      MyApp = _unresolved_5.MyApp;
    }, function (_unresolved_6) {
      EventManager = _unresolved_6.default;
    }, function (_unresolved_7) {
      GameEvent = _unresolved_7.GameEvent;
    }, function (_unresolved_8) {
      DataMgr = _unresolved_8.DataMgr;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "63a20h61NxO4Zw2kpoEiHhW", "GameLogic", undefined);

      _export("GameLogic", GameLogic = class GameLogic {
        constructor() {
          this.gameID = (_crd && Long === void 0 ? (_reportPossibleCrUseOfLong({
            error: Error()
          }), Long) : Long).ZERO;
          this.chapterID = 0;
          this.result = null;
        }

        init() {
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GAME_START, this.onGameStart, this);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GAME_END, this.onGameEnd, this);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GAME_GET_INFO, this.onGameGetInfo, this);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GAME_GET_REWARD, this.onGameGetReward, this);
        }

        update() {} //#region 收协议相关


        onGameStart(msg) {
          var _msg$body, _data$start_info, _data$start_info2, _data$start_info3;

          // MessageBox.testToast("收到协议返回，开始战斗");
          if (msg.ret_code != (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).comm.RET_CODE.RET_CODE_NO_ERROR) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", "onGameStart failed " + msg.ret_code);
            (_crd && MessageBox === void 0 ? (_reportPossibleCrUseOfMessageBox({
              error: Error()
            }), MessageBox) : MessageBox).errorCode(msg.ret_code);
            return;
          }

          var data = (_msg$body = msg.body) == null ? void 0 : _msg$body.game_start;

          if (!data) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", "onGameStart data is null");
            return;
          }

          this.gameID = (_data$start_info = data.start_info) == null || (_data$start_info = _data$start_info.base) == null ? void 0 : _data$start_info.game_id;
          var mode_type = (_data$start_info2 = data.start_info) == null || (_data$start_info2 = _data$start_info2.base) == null ? void 0 : _data$start_info2.mode_type;
          this.chapterID = (_data$start_info3 = data.start_info) == null || (_data$start_info3 = _data$start_info3.base) == null ? void 0 : _data$start_info3.chapter_id;
          (_crd && EventManager === void 0 ? (_reportPossibleCrUseOfEventManager({
            error: Error()
          }), EventManager) : EventManager).Instance.emit((_crd && GameEvent === void 0 ? (_reportPossibleCrUseOfGameEvent({
            error: Error()
          }), GameEvent) : GameEvent).onNetGameStart);
        }

        onGameEnd(msg) {
          var _msg$body2;

          // MessageBox.testToast("收到协议返回，战斗结束");
          if (msg.ret_code != (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).comm.RET_CODE.RET_CODE_NO_ERROR) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", "onGameEnd failed " + msg.ret_code);
            (_crd && MessageBox === void 0 ? (_reportPossibleCrUseOfMessageBox({
              error: Error()
            }), MessageBox) : MessageBox).errorCode(msg.ret_code);
            return;
          }

          var data = (_msg$body2 = msg.body) == null ? void 0 : _msg$body2.game_end;

          if (!data) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", "onGameEnd data is null");
            return;
          }

          data.result = data.result;
          (_crd && EventManager === void 0 ? (_reportPossibleCrUseOfEventManager({
            error: Error()
          }), EventManager) : EventManager).Instance.emit((_crd && GameEvent === void 0 ? (_reportPossibleCrUseOfGameEvent({
            error: Error()
          }), GameEvent) : GameEvent).onNetGameOver);
        }

        onGameGetInfo(msg) {
          var _msg$body3;

          if (msg.ret_code != (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).comm.RET_CODE.RET_CODE_NO_ERROR) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", "onGameGetInfo failed " + msg.ret_code);
            return;
          }

          var data = (_msg$body3 = msg.body) == null ? void 0 : _msg$body3.game_get_info;

          if (!data) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", "onGameGetInfo data is null");
            return;
          }

          var status = data.status;

          if (status == (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).comm.ROLE_GAME_STATUS.ROLE_GAME_STATUS_FIGHTING) {
            //todo  战斗未结束，获取 之前存 的keys
            var start_info = data.start_info;
            (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
              error: Error()
            }), DataMgr) : DataMgr).role.cmdGetClientSettingBattle();
          } else if (status == (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).comm.ROLE_GAME_STATUS.ROLE_GAME_STATUS_END) {
            var result = data.result;
            var game_id = result == null ? void 0 : result.game_id; //this.cmdGameGetReward(game_id!);
          }
        }

        onGameGetReward(msg) {
          var _msg$body4;

          if (msg.ret_code != (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).comm.RET_CODE.RET_CODE_NO_ERROR) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", "onGameGetReward failed " + msg.ret_code);
            return;
          }

          var data = (_msg$body4 = msg.body) == null ? void 0 : _msg$body4.game_get_reward;

          if (!data) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", "onGameGetReward data is null");
            return;
          }

          var game_id = data.game_id;
          var gain_items = data.gain_items;
        } //#endregion
        //#region 发送协议相关

        /** 开始游戏
         * 
         * @param modeID 模式id--无尽=101，来自模式表
         * @param partnerUin 合体好友的uin，默认为0，无合体
         */


        cmdGameStart(modeID, partnerUin) {
          if (partnerUin === void 0) {
            partnerUin = (_crd && Long === void 0 ? (_reportPossibleCrUseOfLong({
              error: Error()
            }), Long) : Long).ZERO;
          }

          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.sendMessage((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GAME_START, {
            game_start: {
              mode_id: modeID,
              partner_uin: partnerUin
            }
          });
        }

        cmdGameEnd(result_info, level_result_info) {
          if (!this.gameID) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("GameLogic", "gameID is null");
            return;
          }

          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.sendMessage((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GAME_END, {
            game_end: {
              game_id: this.gameID,
              result: result_info,
              level_result: level_result_info
            }
          });
        }

        cmdGameGetInfo() {
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.sendMessage((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GAME_GET_INFO, {
            game_get_info: {}
          });
        }

        cmdGameGetReward(gameID, is_double) {
          if (is_double === void 0) {
            is_double = false;
          }

          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.sendMessage((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GAME_GET_REWARD, {
            game_get_reward: {
              game_id: gameID,
              is_double: is_double ? 2 : 1
            }
          });
        } //#endregion
        //#region 外部调用相关


        checkGameID(gameID) {// if (gameID.isZero()) {
          //     return;
          // }
          //this.cmdGameGetInfo();
        } //#endregion


      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=7c02d99877a6775d0211e741dbb9ae69f2566ba2.js.map