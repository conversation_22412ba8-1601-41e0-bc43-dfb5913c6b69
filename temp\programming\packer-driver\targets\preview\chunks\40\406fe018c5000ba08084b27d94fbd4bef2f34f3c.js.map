{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/resupdate/ResUpdate.ts"], "names": ["_decorator", "ccenum", "Component", "director", "LabelComponent", "ProgressBar", "WECHAT", "initBundle", "UIMgr", "logDebug", "ccclass", "property", "warnCustom", "console", "warn", "res", "indexOf", "groupCustom", "group", "GameLogLevel", "ResUpdate", "start", "initializeLayers", "preloadScene", "OnLoadProgress", "bind", "loadScene", "completedCount", "totalCount", "progress", "toFixed", "node", "per<PERSON><PERSON><PERSON>", "string", "<PERSON><PERSON><PERSON><PERSON>", "loadingBar", "onLoad", "onDestroy", "update", "deltaTime"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,c,OAAAA,c;AAAgBC,MAAAA,W,OAAAA,W;;AACzDC,MAAAA,M,UAAAA,M;;AAGAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,Q,iBAAAA,Q;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;;AAE9B,UAAIM,MAAJ,EAAY;AACJM,QAAAA,UADI,GACSC,OAAO,CAACC,IADjB;;AAERD,QAAAA,OAAO,CAACC,IAAR,GAAe,UAAUC,GAAV,EAAe;AAC1B,cAAI,OAAOA,GAAP,IAAc,QAAd,IAA0BA,GAAG,CAACC,OAAJ,CAAY,gBAAZ,IAAgC,CAAC,CAA/D,EAAkE;AAC9D;AACH,WAFD,MAEO;AACHJ,YAAAA,UAAU,CAACG,GAAD,CAAV;AACH;AAGJ,SARD;;AASIE,QAAAA,WAXI,GAWUJ,OAAO,CAACK,KAXlB;;AAYRL,QAAAA,OAAO,CAACK,KAAR,GAAgB,UAAUH,GAAV,EAAe;AAC3B,cAAI,OAAOA,GAAP,IAAc,QAAd,IAA0BA,GAAG,CAACC,OAAJ,CAAY,YAAZ,IAA4B,CAAC,CAA3D,EAA8D;AAC1D;AACH,WAFD,MAEO;AACHC,YAAAA,WAAW,CAACF,GAAD,CAAX;AACH;AACJ,SAND;AAOH;;AAEII,MAAAA,Y,0BAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;eAAAA,Y;QAAAA,Y;;AAQLlB,MAAAA,MAAM,CAACkB,YAAD,CAAN;;2BAGaC,S,WADZV,OAAO,CAAC,WAAD,C,UAKHC,QAAQ,CAACP,cAAD,C,UAERO,QAAQ,CAACP,cAAD,C,UAERO,QAAQ,CAACP,cAAD,C,UAERO,QAAQ,CAACN,WAAD,C,2BAXb,MACae,SADb,SAC+BlB,SAD/B,CACyC;AAAA;AAAA;;AACrC;AACA;AAFqC;;AAAA;;AAAA;;AAAA;AAAA;;AAa/BmB,QAAAA,KAAK,GAAG;AAAA;;AAAA;AACV;AAAA;AAAA,gCAAMC,gBAAN,GADU,CAGV;;AACAnB,YAAAA,QAAQ,CAACoB,YAAT,CAAsB,MAAtB,EAA8B,KAAI,CAACC,cAAL,CAAoBC,IAApB,CAAyB,KAAzB,CAA9B,iCAA8D,aAAY;AACtE;AACA,oBAAM;AAAA;AAAA,4CAAW,QAAX,CAAN;AACAtB,cAAAA,QAAQ,CAACuB,SAAT,CAAmB,MAAnB;AACH,aAJD;AAJU;AASb;;AAEDF,QAAAA,cAAc,CAACG,cAAD,EAAyBC,UAAzB,EAA6C;AACvD,cAAIC,QAAQ,GAAIF,cAAc,GAAGC,UAAjC;AACA;AAAA;AAAA,oCAAS,WAAT,wBAA0CD,cAA1C,UAA6DC,UAA7D,UAA4E,CAACC,QAAQ,GAAG,GAAZ,EAAiBC,OAAjB,CAAyB,CAAzB,CAA5E;;AACA,cAAI,KAAKC,IAAL,IAAa,IAAjB,EAAuB;AACnB;AACH;;AACD,eAAKC,QAAL,CAAeC,MAAf,GAAwB,CAACJ,QAAQ,GAAG,GAAZ,EAAiBC,OAAjB,CAAyB,CAAzB,IAA8B,GAAtD;AACA,eAAKI,UAAL,CAAiBD,MAAjB,GAA0B,YAAYN,cAAZ,GAA6B,GAA7B,GAAmCC,UAAnC,GAAgD,GAA1E;AACA,eAAKO,UAAL,CAAiBN,QAAjB,GAA4BA,QAA5B;AACH;;AAEDO,QAAAA,MAAM,GAAG,CACR;;AACDC,QAAAA,SAAS,GAAG,CACX;;AAEDC,QAAAA,MAAM,CAACC,SAAD,EAAoB,CACtB;AACA;AACA;AACA;AACA;AACH;;AA9CoC,O;;;;;iBAKO,I;;;;;;;iBAEF,I;;;;;;;iBAEI,I;;;;;;;iBAEL,I", "sourcesContent": ["import { _decorator, ccenum, Component, director, LabelComponent, ProgressBar } from 'cc';\r\nimport { WECHAT } from \"cc/env\";\r\n\r\nimport \"../AAA/init_cs_proto.js\";\r\nimport { initBundle } from '../core/base/Bundle';\r\nimport { UIMgr } from \"../core/base/UIMgr\";\r\nimport { logDebug } from \"../utils/Logger\";\r\nconst { ccclass, property } = _decorator;\r\n\r\nif (WECHAT) {\r\n    var warnCustom = console.warn;\r\n    console.warn = function (res) {\r\n        if (typeof res == \"string\" && res.indexOf(\"文件路径在真机上可能无法读取\") > -1) {\r\n            return;\r\n        } else {\r\n            warnCustom(res)\r\n        }\r\n\r\n\r\n    }\r\n    var groupCustom = console.group;\r\n    console.group = function (res) {\r\n        if (typeof res == \"string\" && res.indexOf(\"读取文件/文件夹警告\") > -1) {\r\n            return;\r\n        } else {\r\n            groupCustom(res)\r\n        }\r\n    }\r\n}\r\n\r\nenum GameLogLevel {\r\n    TRACE = 0,\r\n    DEBUG = 1,\r\n    LOG = 2,\r\n    INFO = 3,\r\n    WARN = 4,\r\n    ERROR = 5,\r\n}\r\nccenum(GameLogLevel)\r\n\r\n@ccclass(\"ResUpdate\")\r\nexport class ResUpdate extends Component {\r\n    /* class member could be defined like this */\r\n    // dummy = '';\r\n\r\n    @property(LabelComponent)\r\n    private countLabel: LabelComponent | null = null;\r\n    @property(LabelComponent)\r\n    private perLabel: LabelComponent | null = null;\r\n    @property(LabelComponent)\r\n    private versionLabel: LabelComponent | null = null;\r\n    @property(ProgressBar)\r\n    private loadingBar: ProgressBar | null = null;\r\n\r\n    async start() {\r\n        UIMgr.initializeLayers();\r\n\r\n        //!todo 获取资源版本号\r\n        director.preloadScene(\"Main\", this.OnLoadProgress.bind(this), async () => {\r\n            // dev 先load login UI\r\n            await initBundle(\"common\")\r\n            director.loadScene(\"Main\")\r\n        });\r\n    }\r\n\r\n    OnLoadProgress(completedCount: number, totalCount: number) {\r\n        let progress = (completedCount / totalCount)\r\n        logDebug(\"ResUpdate\", `load main scene, ${completedCount}, ${totalCount}, ${(progress * 100).toFixed(2)}`)\r\n        if (this.node == null) {\r\n            return;\r\n        }\r\n        this.perLabel!.string = (progress * 100).toFixed(2) + \"%\"\r\n        this.countLabel!.string = '加载中...(' + completedCount + '/' + totalCount + ')'\r\n        this.loadingBar!.progress = progress\r\n    }\r\n\r\n    onLoad() {\r\n    }\r\n    onDestroy() {\r\n    }\r\n\r\n    update(deltaTime: number) {\r\n        //if (this.isMainSceneLoaded) //&& window.MyApp && window.MyApp.getLogin().isLogined)\r\n        //{\r\n        //    director.loadScene(\"MainScene\")\r\n        //}\r\n        // Your update function goes here.\r\n    }\r\n}\r\n"]}