import { _decorator, instantiate, Prefab } from "cc";
import { GameIns } from "../../game/GameIns";
import EventManager from "db://assets/bundles/common/script/event/EventManager";
import { GameEvent } from "db://assets/bundles/common/script/game/event/GameEvent";
import { LevelDataEventTrigger, LevelDataEventTriggerType } from "./LevelDataEventTrigger";

export enum eLevelSpecialEvent {
    SectionFinish,  // 关卡小节结束
}

export class LevelDataEventTriggerSpecialEvent extends LevelDataEventTrigger {
    // 是否是当前的最后一波
    public eventType: eLevelSpecialEvent = eLevelSpecialEvent.SectionFinish;
    
    constructor() {
        super(LevelDataEventTriggerType.Wave);
    }
    
    public onInit() {
    }

    public onTrigger(x: number, y: number) {        
        EventManager.Instance.emit(GameEvent.onLevelSpecialEvent, this.eventType);
    }

    public fromJSON(obj: any): void {
        super.fromJSON(obj);
    }

    public toJSON(): any {
        // avoid private properties
        return {
            _type: this._type,
            eventType: this.eventType,
        };
    }
}

