{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/DevLogin.ts"], "names": ["DevLogin", "logInfo", "csproto", "DevLoginData", "CryptoJS", "showUserInfoButton", "hideUserInfoButton", "login", "cb", "accountType", "cs", "ACCOUNT_TYPE", "ACCOUNT_TYPE_NONE", "code", "instance", "user", "MD5", "password", "serverAddr", "getServerAddr", "getUserInfo", "param", "name", "icon", "getStatusBarHeight"], "mappings": ";;;wEASaA,Q;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AARJC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,O;;AAEEC,MAAAA,Y,iBAAAA,Y;;AAGFC,MAAAA,Q;;;;;gFADP;;;0BAGaJ,Q,GAAN,MAAMA,QAAN,CAAuC;AAC1CK,QAAAA,kBAAkB,GAAG,CACpB;;AACDC,QAAAA,kBAAkB,GAAG,CACpB;;AACDC,QAAAA,KAAK,CAACC,EAAD,EAAuD;AACxD;AAAA;AAAA,kCAAQ,OAAR,EAAiB,WAAjB;AACAA,UAAAA,EAAE,CAAC,IAAD,EAAO;AACLC,YAAAA,WAAW,EAAE;AAAA;AAAA,oCAAQC,EAAR,CAAWC,YAAX,CAAwBC,iBADhC;AAELC,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAaC,QAAb,CAAsBC,IAAtB,GAA6B,GAA7B,GAAmC;AAAA;AAAA,sCAASC,GAAT,CAAa;AAAA;AAAA,8CAAaF,QAAb,CAAsBG,QAAnC,CAFpC;AAGLC,YAAAA,UAAU,EAAE;AAAA;AAAA,8CAAaJ,QAAb,CAAsBK,aAAtB;AAHP,WAAP,CAAF;AAKH;;AAEDC,QAAAA,WAAW,CAACZ,EAAD,EAA8Ea,KAA9E,EAA0F;AACjGb,UAAAA,EAAE,CAAC,EAAD,EAAK;AACHc,YAAAA,IAAI,EAAE,KADH;AAEHC,YAAAA,IAAI,EAAE;AAFH,WAAL,EAGC,KAHD,CAAF;AAIH;;AAEDC,QAAAA,kBAAkB,GAAI;AAClB,iBAAO,CAAP;AACH;;AAvByC,O", "sourcesContent": ["\r\nimport { logInfo } from '../../../../scripts/utils/Logger';\r\nimport csproto from 'db://assets/bundles/common/script/autogen/pb/cs_proto.js';\r\nimport { LoginInfo } from '../network/NetMgr';\r\nimport { DevLoginData } from './DevLoginData';\r\nimport { IPlatformSDK, PlatformSDKUserInfo } from './IPlatformSDK.js';\r\n// @ts-ignore\r\nimport CryptoJ<PERSON> from 'crypto-js';\r\n\r\nexport class DevLogin implements IPlatformSDK {\r\n    showUserInfoButton() {\r\n    }\r\n    hideUserInfoButton() {\r\n    }\r\n    login(cb: (err: string|null, req: LoginInfo) => void): void {\r\n        logInfo(\"Login\", \"dev login\")\r\n        cb(null, {\r\n            accountType: csproto.cs.ACCOUNT_TYPE.ACCOUNT_TYPE_NONE,\r\n            code: DevLoginData.instance.user + \"#\" + CryptoJS.MD5(DevLoginData.instance.password),\r\n            serverAddr: DevLoginData.instance.getServerAddr(),\r\n        })\r\n    }\r\n\r\n    getUserInfo(cb: (err: string, req: PlatformSDKUserInfo | null, hasTap: boolean) => void, param: any) {\r\n        cb(\"\", {\r\n            name: 'dev',\r\n            icon: \"\",\r\n        }, false)\r\n    }\r\n\r\n    getStatusBarHeight () {\r\n        return 0;\r\n    }\r\n}"]}