System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, _crd, GameEvent;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "47b69f6XQ1HiZfP5wtb2WkD", "GameEvent", undefined);

      _export("GameEvent", GameEvent = {
        GameLoadEnd: "GameLoadEnd",
        GameMainPlaneIn: "GameMainPlaneIn",
        GameStart: "GameStart",
        // 游戏开始事件
        onNetGameStart: "onNetGameStart",
        // 联网游戏开始事件
        onNetGameOver: "onNetGameOver",
        // 联网游戏结束事件
        onLevelSpecialEvent: "onLevelSpecialEvent" // 关卡配置的特殊事件

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=4b9346e67cad1d5b42ef751bd466d463e39f33fb.js.map