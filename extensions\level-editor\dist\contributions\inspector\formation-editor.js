/**
 * 阵型编辑器
 */
'use strict';
Object.defineProperty(exports, "__esModule", { value: true });
exports.$ = exports.template = void 0;
exports.update = update;
exports.ready = ready;
const { updatePropByDump, disconnectGroup } = require('./../../prop');
exports.template = `
<div class="component-container"></div>
<ui-prop>
    <ui-button class="btn-add"">添加阵型点</ui-button>
    <ui-button class="btn-save">保存</ui-button>
</ui-prop>
`;
exports.$ = {
    componentContainer: '.component-container',
    btnAdd: '.btn-add',
    btnSave: '.btn-save',
};
function update(dump) {
    updatePropByDump(this, dump);
    this.dump = dump;
}
async function ready() {
    disconnectGroup(this);
    this.$.btnAdd.addEventListener('confirm', async () => {
        var _a;
        // console.log('add formation point', this.dump);
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'level-editor',
            method: 'addFormationPoint',
            args: [(_a = this.dump) === null || _a === void 0 ? void 0 : _a.value.uuid.value]
        });
    });
    this.$.btnSave.addEventListener('confirm', async () => {
        var _a;
        // console.log('save formation', this.dump);
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'level-editor',
            method: 'saveFormationGroup',
            args: [(_a = this.dump) === null || _a === void 0 ? void 0 : _a.value.uuid.value]
        });
    });
}
//# sourceMappingURL=data:application/json;base64,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