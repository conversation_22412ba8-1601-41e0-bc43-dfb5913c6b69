System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, instantiate, Node, UITransform, view, MyApp, LayerSplicingMode, GameMapRun, LevelEventRun, Tools, GameIns, _dec, _class, _crd, ccclass, TerrainsNodeName, DynamicNodeName, ScrollsNodeName, EmittiersNodeName, LevelLayerUI;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/bundles/common/script/app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLayerSplicingMode(extras) {
    _reporterNs.report("LayerSplicingMode", "../../../leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEvent(extras) {
    _reporterNs.report("LevelDataEvent", "../../../leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataLayer(extras) {
    _reporterNs.report("LevelDataLayer", "../../../leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameMapRun(extras) {
    _reporterNs.report("GameMapRun", "./GameMapRun", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelEventRun(extras) {
    _reporterNs.report("LevelEventRun", "./LevelEventRun", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "../../utils/Tools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../GameIns", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      instantiate = _cc.instantiate;
      Node = _cc.Node;
      UITransform = _cc.UITransform;
      view = _cc.view;
    }, function (_unresolved_2) {
      MyApp = _unresolved_2.MyApp;
    }, function (_unresolved_3) {
      LayerSplicingMode = _unresolved_3.LayerSplicingMode;
    }, function (_unresolved_4) {
      GameMapRun = _unresolved_4.default;
    }, function (_unresolved_5) {
      LevelEventRun = _unresolved_5.LevelEventRun;
    }, function (_unresolved_6) {
      Tools = _unresolved_6.Tools;
    }, function (_unresolved_7) {
      GameIns = _unresolved_7.GameIns;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "d139a2/Z9NEzIGUXE+qqxb8", "LevelLayerUI", undefined);

      __checkObsolete__(['_decorator', 'Component', 'instantiate', 'Node', 'Prefab', 'UITransform', 'view']);

      ({
        ccclass
      } = _decorator);
      TerrainsNodeName = "terrains";
      DynamicNodeName = "dynamic";
      ScrollsNodeName = "scrolls";
      EmittiersNodeName = "emittiers";

      _export("LevelLayerUI", LevelLayerUI = (_dec = ccclass('LevelLayerUI'), _dec(_class = class LevelLayerUI extends Component {
        constructor() {
          super(...arguments);
          this.backgrounds = [];
          this._offSetY = 0;
          // 当前关卡的偏移量
          this._bTrackBackground = true;
          // 是否跟随背景移动（预加载关卡未在显示区域的时候跟随）
          this.terrainsNode = null;
          this.dynamicNode = null;
          this.scrollsNode = null;
          this.emittiersNode = null;
          this.events = [];
          this.eventRunners = [];
        }

        get TrackBackground() {
          return this._bTrackBackground;
        }

        set TrackBackground(value) {
          this._bTrackBackground = value;
        }

        onLoad() {}

        initByLevelData(data, offSetY) {
          var _this = this;

          return _asyncToGenerator(function* () {
            var _data$terrains;

            _this._offSetY = offSetY;

            _this.node.setPosition(0, offSetY, 0);

            _this.terrainsNode = _this._getOrAddNode(_this.node, TerrainsNodeName);
            _this.dynamicNode = _this._getOrAddNode(_this.node, DynamicNodeName);
            _this.scrollsNode = _this._getOrAddNode(_this.node, ScrollsNodeName);
            _this.emittiersNode = _this._getOrAddNode(_this.node, EmittiersNodeName);
            _this.backgrounds = [];
            (_data$terrains = data.terrains) == null || _data$terrains.forEach(terrain => {
              var path = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                error: Error()
              }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                error: Error()
              }), MyApp) : MyApp).resMgr.defaultBundleName, terrain.uuid);
              (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                error: Error()
              }), MyApp) : MyApp).resMgr.load(path, (err, prefab) => {
                if (err) {
                  console.error('LevelLayerUI', " initByLevelData load terrain prefab err", err);
                  return;
                }

                var terrainNode = instantiate(prefab);
                terrainNode.setPosition(terrain.position.x, terrain.position.y, 0);
                terrainNode.setScale(terrain.scale.x, terrain.scale.y, 1);
                terrainNode.setRotationFromEuler(0, 0, terrain.rotation);

                _this.terrainsNode.addChild(terrainNode);
              });
            });
            yield _this._initDynamicsByLevelData(data);
            yield _this._initEmittierLevelData(data);
            yield _this._initScorllsByLevelData(data);
            _this.events = [...data.events];

            _this.events.sort((a, b) => a.position.y - b.position.y);

            _this.events.forEach(event => {
              _this.eventRunners.push(new (_crd && LevelEventRun === void 0 ? (_reportPossibleCrUseOfLevelEventRun({
                error: Error()
              }), LevelEventRun) : LevelEventRun)(event, _this));
            });
          })();
        }

        _initDynamicsByLevelData(data) {
          var _this2 = this;

          return _asyncToGenerator(function* () {
            var _data$dynamics;

            if (!data || _this2.dynamicNode === null || data.dynamics.length === 0) {
              return;
            }

            var loadPromises = [];
            (_data$dynamics = data.dynamics) == null || _data$dynamics.forEach((dynamics, index) => {
              var dynaNode = _this2._getOrAddNode(_this2.dynamicNode, "dyna_" + index);

              var weights = [];
              dynamics.group.forEach(dynamic => {
                weights.push(dynamic.weight);
              });
              var dynaIndex = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                error: Error()
              }), Tools) : Tools).getRandomIndexByWeights(weights, (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).battleManager.random());
              var dynamic = dynamics.group[dynaIndex];
              dynaNode.setPosition(dynamic.position.x, dynamic.position.y, 0);
              dynaNode.setScale(dynamic.scale.x, dynamic.scale.y, 1);
              dynaNode.setRotationFromEuler(0, 0, dynamic.rotation);
              weights = [];
              dynamic.terrains.forEach(terrain => {
                weights.push(terrain.weight);
              });
              var terrainIndex = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                error: Error()
              }), Tools) : Tools).getRandomIndexByWeights(weights, (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).battleManager.random());
              var terrain = dynamic.terrains[terrainIndex];
              var loadPromise = new Promise(resolve => {
                var path = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                  error: Error()
                }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                  error: Error()
                }), MyApp) : MyApp).resMgr.defaultBundleName, terrain.uuid);
                (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                  error: Error()
                }), MyApp) : MyApp).resMgr.load(path, (err, prefab) => {
                  if (err) {
                    console.error("LevelEditorLayerUI initByLevelData load dynamic prefab err", err);
                    resolve();
                    return;
                  }

                  var dynamicNode = instantiate(prefab);
                  dynamicNode.name = "rand_" + dynaIndex + "_" + terrainIndex;
                  dynaNode.addChild(dynamicNode);
                  var randomOffsetX = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                    error: Error()
                  }), GameIns) : GameIns).battleManager.random() * (terrain.offSetX.max - terrain.offSetX.min) + terrain.offSetX.min;
                  var randomOffsetY = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                    error: Error()
                  }), GameIns) : GameIns).battleManager.random() * (terrain.offSetY.max - terrain.offSetY.min) + terrain.offSetY.min;
                  dynamicNode.setPosition(randomOffsetX, randomOffsetY, 0);
                  resolve();
                });
              });
              loadPromises.push(loadPromise);
            });
            yield Promise.all(loadPromises);
          })();
        }

        _initEmittierLevelData(data) {
          var _this3 = this;

          return _asyncToGenerator(function* () {
            var _data$emittiers;

            if (!data || _this3.emittiersNode === null || data.emittiers.length === 0) {
              return;
            }

            var loadPromises = [];
            (_data$emittiers = data.emittiers) == null || _data$emittiers.forEach((emittier, index) => {
              var loadPromise = new Promise(resolve => {
                var path = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                  error: Error()
                }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                  error: Error()
                }), MyApp) : MyApp).resMgr.defaultBundleName, emittier.uuid);
                (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                  error: Error()
                }), MyApp) : MyApp).resMgr.load(path, (err, prefab) => {
                  if (err) {
                    resolve();
                    return;
                  }

                  var emittierNode = instantiate(prefab);
                  emittierNode.name = "emittier_" + index;
                  emittierNode.setPosition(emittier.position.x, emittier.position.y, 0);
                  emittierNode.setScale(emittier.scale.x, emittier.scale.y, 1);
                  emittierNode.setRotationFromEuler(0, 0, emittier.rotation);

                  _this3.emittiersNode.addChild(emittierNode);

                  resolve();
                });
              });
              loadPromises.push(loadPromise);
            });
            yield Promise.all(loadPromises);
          })();
        }

        _initScorllsByLevelData(data) {
          var _this4 = this;

          return _asyncToGenerator(function* () {
            if (!data || _this4.scrollsNode === null || data.scrolls.length === 0) {
              return;
            } // 根据权重随机出一个滚动组


            var weights = [];
            data.scrolls.forEach(element => {
              weights.push(element.weight);
            });
            var srocllIndex = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).getRandomIndexByWeights(weights, (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.random());
            var scroll = data.scrolls[srocllIndex];
            var loadPromises = [];
            var prefabList = []; // 先加载成功所有预制体

            scroll.uuids.forEach(uuid => {
              var loadPromise = new Promise(resolve => {
                var path = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                  error: Error()
                }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                  error: Error()
                }), MyApp) : MyApp).resMgr.defaultBundleName, uuid);
                (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                  error: Error()
                }), MyApp) : MyApp).resMgr.load(path, (err, prefab) => {
                  if (err) {
                    resolve();
                    return;
                  }

                  prefabList.push(prefab);
                  resolve();
                });
              });
              loadPromises.push(loadPromise);
            });
            yield Promise.all(loadPromises);

            var scrollsNode = _this4._getOrAddNode(_this4.scrollsNode, "scroll_" + srocllIndex);

            var totalHeight = data.speed * data.totalTime / 1000;
            var posOffsetY = 0;
            var height = 0;
            var prefabIndex = 0; // 当前使用的 prefab 索引

            while (height < totalHeight) {
              // 循环使用 prefab
              var curPrefab = prefabList[prefabIndex];
              var child = instantiate(curPrefab);
              var randomOffsetX = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).battleManager.random() * (scroll.offSetX.max - scroll.offSetX.min) + scroll.offSetX.min;
              child.setPosition(randomOffsetX, posOffsetY, 0);
              var offY = 0;

              if (scroll.splicingMode === (_crd && LayerSplicingMode === void 0 ? (_reportPossibleCrUseOfLayerSplicingMode({
                error: Error()
              }), LayerSplicingMode) : LayerSplicingMode).node_height) {
                offY = child.getComponent(UITransform).contentSize.height;
              } else if (scroll.splicingMode === (_crd && LayerSplicingMode === void 0 ? (_reportPossibleCrUseOfLayerSplicingMode({
                error: Error()
              }), LayerSplicingMode) : LayerSplicingMode).fix_height) {
                offY = 1334;
              } else if (scroll.splicingMode === (_crd && LayerSplicingMode === void 0 ? (_reportPossibleCrUseOfLayerSplicingMode({
                error: Error()
              }), LayerSplicingMode) : LayerSplicingMode).random_height) {
                offY = Math.max(scroll.offSetY.min, scroll.offSetY.max) + child.getComponent(UITransform).contentSize.height;
              }

              scrollsNode.addChild(child);
              posOffsetY += offY;
              height += offY;
              prefabIndex = (prefabIndex + 1) % scroll.uuids.length;
            }
          })();
        }

        tick(deltaTime, speed) {
          var posY = this.node.position.y;

          if (this.TrackBackground === true) {
            var topPosY = view.getVisibleSize().height / 2;

            if (posY < topPosY) {
              this._bTrackBackground = false;
            }
          }

          posY -= deltaTime * speed;
          this.node.setPosition(0, posY, 0); // 说明: event的激活，是从进入世界范围开始。
          // 又：因为游戏是从上往下滚动，因为我们用一个固定大小变量定义上边界，一旦进入这个范围，就激活。

          var scrollY = -posY + this._offSetY + (_crd && GameMapRun === void 0 ? (_reportPossibleCrUseOfGameMapRun({
            error: Error()
          }), GameMapRun) : GameMapRun).VIEWPORT_TOP; // console.log('LevelLayerUI tick scrollY', scrollY, -posY, this._offSetY, GameMapRun.VIEWPORT_TOP);

          for (var i = 0; i < this.eventRunners.length; i++) {
            var eventRunner = this.eventRunners[i];
            eventRunner.tick(scrollY);

            if (eventRunner.isTriggered) {
              // 条件已触发
              this.eventRunners.splice(i, 1);
              i--;
            }
          }
        } // 动态实例化场景元素，当元素的位置在在一个屏幕以上位置是，就实例化


        _instantiateTerrain() {}

        _getOrAddNode(node_parent, name) {
          var node = node_parent.getChildByName(name);

          if (node == null) {
            node = new Node(name);
            node_parent.addChild(node);
          }

          return node;
        }

        getEventByElemID(elemID) {
          for (var event of this.events) {
            if (event.elemID == elemID) {
              return event;
            }
          }

          return null;
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=928ab71a9883ce6ff25bdd946042179d19325cc0.js.map