System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, ResManager, audioManager, GlobalDataManager, LubanMgr, NetMgr, PlaneManager, CreateLoginSDK, _dec, _class, _class2, _crd, ccclass, MyApp;

  function _reportPossibleCrUseOfFnOnLateUpdate(extras) {
    _reporterNs.report("FnOnLateUpdate", "../../../../scripts/core/base/IMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFnOnUpdate(extras) {
    _reporterNs.report("FnOnUpdate", "../../../../scripts/core/base/IMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIMgr(extras) {
    _reporterNs.report("IMgr", "../../../../scripts/core/base/IMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfResManager(extras) {
    _reporterNs.report("ResManager", "../../../../scripts/core/base/ResManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfaudioManager(extras) {
    _reporterNs.report("audioManager", "../audio/audioManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGlobalDataManager(extras) {
    _reporterNs.report("GlobalDataManager", "../game/manager/GlobalDataManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLubanMgr(extras) {
    _reporterNs.report("LubanMgr", "../luban/LubanMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfNetMgr(extras) {
    _reporterNs.report("NetMgr", "../network/NetMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneManager(extras) {
    _reporterNs.report("PlaneManager", "../plane/PlaneManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfCreateLoginSDK(extras) {
    _reporterNs.report("CreateLoginSDK", "../platformsdk/IPlatformSDK", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIPlatformSDK(extras) {
    _reporterNs.report("IPlatformSDK", "../platformsdk/IPlatformSDK", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_unresolved_2) {
      ResManager = _unresolved_2.ResManager;
    }, function (_unresolved_3) {
      audioManager = _unresolved_3.audioManager;
    }, function (_unresolved_4) {
      GlobalDataManager = _unresolved_4.GlobalDataManager;
    }, function (_unresolved_5) {
      LubanMgr = _unresolved_5.LubanMgr;
    }, function (_unresolved_6) {
      NetMgr = _unresolved_6.NetMgr;
    }, function (_unresolved_7) {
      PlaneManager = _unresolved_7.PlaneManager;
    }, function (_unresolved_8) {
      CreateLoginSDK = _unresolved_8.CreateLoginSDK;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "815bcg6mBVI2oWeCmHI13ZP", "MyApp", undefined);

      __checkObsolete__(['_decorator']);

      ({
        ccclass
      } = _decorator);

      _export("MyApp", MyApp = (_dec = ccclass("MyApp"), _dec(_class = (_class2 = class MyApp {
        constructor() {
          this.ManagerPool = [];
          this._updateContainer = [];
          this._lateUpdateContainer = [];
          this._lubanMgr = null;
          this._netMgr = null;
          this._resMgr = null;
          this._platformSDK = null;
          this._globalDataManager = null;
          this._planeManager = null;
          this._audioManager = null;
        }

        static GetInstance() {
          if (!MyApp._instance) {
            MyApp._instance = new MyApp();
          }

          return MyApp._instance;
        }

        init() {
          this.ManagerPool.push((_crd && audioManager === void 0 ? (_reportPossibleCrUseOfaudioManager({
            error: Error()
          }), audioManager) : audioManager).instance);
          this._lubanMgr = new (_crd && LubanMgr === void 0 ? (_reportPossibleCrUseOfLubanMgr({
            error: Error()
          }), LubanMgr) : LubanMgr)();
          this.ManagerPool.push(this._lubanMgr);
          this._resMgr = (_crd && ResManager === void 0 ? (_reportPossibleCrUseOfResManager({
            error: Error()
          }), ResManager) : ResManager).instance;
          this.ManagerPool.push(this._resMgr);
          this._platformSDK = (_crd && CreateLoginSDK === void 0 ? (_reportPossibleCrUseOfCreateLoginSDK({
            error: Error()
          }), CreateLoginSDK) : CreateLoginSDK)();
          this._netMgr = new (_crd && NetMgr === void 0 ? (_reportPossibleCrUseOfNetMgr({
            error: Error()
          }), NetMgr) : NetMgr)(this._platformSDK);
          this.ManagerPool.push(this._netMgr);
          this._globalDataManager = new (_crd && GlobalDataManager === void 0 ? (_reportPossibleCrUseOfGlobalDataManager({
            error: Error()
          }), GlobalDataManager) : GlobalDataManager)();
          this._planeManager = new (_crd && PlaneManager === void 0 ? (_reportPossibleCrUseOfPlaneManager({
            error: Error()
          }), PlaneManager) : PlaneManager)();
          this._audioManager = (_crd && audioManager === void 0 ? (_reportPossibleCrUseOfaudioManager({
            error: Error()
          }), audioManager) : audioManager).instance;
          this.ManagerPool.forEach(manager => {
            manager.init();

            this._updateContainer.push(manager.onUpdate.bind(manager));

            this._lateUpdateContainer.push(manager.onLateUpdate.bind(manager));
          });
        }

        update(deltaTime) {
          for (let i = 0; i < this._updateContainer.length; i++) {
            this._updateContainer[i](deltaTime);
          }
        }

        lateUpdate() {
          for (let i = 0; i < this._lateUpdateContainer.length; i++) {
            this._lateUpdateContainer[i]();
          }
        }

        static get netMgr() {
          return MyApp.GetInstance()._netMgr;
        }

        static get lubanMgr() {
          return MyApp.GetInstance()._lubanMgr;
        }

        static get lubanTables() {
          return MyApp.GetInstance()._lubanMgr.table;
        }

        static get platformSDK() {
          return MyApp.GetInstance()._platformSDK;
        }

        static get resMgr() {
          return MyApp.GetInstance()._resMgr;
        }

        static get globalMgr() {
          return MyApp.GetInstance()._globalDataManager;
        }

        static get planeMgr() {
          return MyApp.GetInstance()._planeManager;
        }

        static get audioMgr() {
          return MyApp.GetInstance()._audioManager;
        }

      }, _class2._instance = null, _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=e2f211f332981cffeb2db47a354ba9fea8e12df4.js.map