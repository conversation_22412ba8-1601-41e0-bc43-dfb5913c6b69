import { _decorator, Node, Prefab } from 'cc';
import { BaseUI, UILayer, UIOpt } from "db://assets/scripts/core/base/UIMgr";
import { MyApp } from '../../app/MyApp';
import { BundleName } from '../../const/BundleConst';
import { DataMgr } from '../../data/DataManager';

const { ccclass, property } = _decorator;

@ccclass('PlaneShowUI')
export class PlaneShowUI extends BaseUI {

    @property(Node)
    planeCon: Node | null = null;

    public static getUrl(): string { return "prefab/ui/PlaneShowUI"; }
    public static getLayer(): UILayer { return UILayer.Default }
    public static getBundleName(): string { return BundleName.Home }
    public static getUIOption(): UIOpt {
        return { isClickBgCloseUI: false }
    }

    protected async onLoad(): Promise<void> {
        let planeData = DataMgr.planeInfo.getPlaneInfoById()
        let plane = MyApp.planeMgr.getPlane(planeData);
        this.planeCon!.addChild(plane);
    }

    async onShow(): Promise<void> {

    }
    async onHide(): Promise<void> {
    }
    async onClose(): Promise<void> {
    }

}
