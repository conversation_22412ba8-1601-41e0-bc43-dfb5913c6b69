System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, find, Label, Node, tween, Vec3, BaseUI, UILayer, UIMgr, BundleName, EventMgr, ButtonPlus, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _crd, ccclass, property, RogueUI;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfBaseUI(extras) {
    _reporterNs.report("BaseUI", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUILayer(extras) {
    _reporterNs.report("UILayer", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBundleName(extras) {
    _reporterNs.report("BundleName", "../../../const/BundleConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventMgr(extras) {
    _reporterNs.report("EventMgr", "../../../event/EventManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfButtonPlus(extras) {
    _reporterNs.report("ButtonPlus", "../../common/components/button/ButtonPlus", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      find = _cc.find;
      Label = _cc.Label;
      Node = _cc.Node;
      tween = _cc.tween;
      Vec3 = _cc.Vec3;
    }, function (_unresolved_2) {
      BaseUI = _unresolved_2.BaseUI;
      UILayer = _unresolved_2.UILayer;
      UIMgr = _unresolved_2.UIMgr;
    }, function (_unresolved_3) {
      BundleName = _unresolved_3.BundleName;
    }, function (_unresolved_4) {
      EventMgr = _unresolved_4.EventMgr;
    }, function (_unresolved_5) {
      ButtonPlus = _unresolved_5.ButtonPlus;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "e094c3NOSVC84pbIiaj27xx", "RogueUI", undefined);

      __checkObsolete__(['_decorator', 'find', 'Label', 'Node', 'Tween', 'tween', 'Vec3']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("RogueUI", RogueUI = (_dec = ccclass("RogueUI"), _dec2 = property(Node), _dec3 = property(Node), _dec4 = property(Node), _dec5 = property([Node]), _dec6 = property(Label), _dec7 = property(Label), _dec(_class = (_class2 = class RogueUI extends (_crd && BaseUI === void 0 ? (_reportPossibleCrUseOfBaseUI({
        error: Error()
      }), BaseUI) : BaseUI) {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "nodeFresh", _descriptor, this);

          _initializerDefineProperty(this, "nodeExclude", _descriptor2, this);

          _initializerDefineProperty(this, "nodeAbility", _descriptor3, this);

          _initializerDefineProperty(this, "rogueSelectNodes", _descriptor4, this);

          _initializerDefineProperty(this, "freshTimes", _descriptor5, this);

          _initializerDefineProperty(this, "excludeTimes", _descriptor6, this);

          this.activeTweens = [];
          this.activeTimeouts = [];
          this._callFunc = null;
        }

        static getUrl() {
          return "prefab/ui/fight/RogueUI";
        }

        static getLayer() {
          return (_crd && UILayer === void 0 ? (_reportPossibleCrUseOfUILayer({
            error: Error()
          }), UILayer) : UILayer).Default;
        }

        static getBundleName() {
          return (_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).Common;
        }

        onLoad() {
          this.nodeFresh.getComponentInChildren(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
            error: Error()
          }), ButtonPlus) : ButtonPlus).addClick(this.onFresh, this);
          this.nodeExclude.getComponentInChildren(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
            error: Error()
          }), ButtonPlus) : ButtonPlus).addClick(this.onCancel, this);
          this.showNodesSequentiallyWithScale();
        }

        showNodesSequentiallyWithScale() {
          var children = this.nodeAbility.children;
          if (!children || children.length === 0) return; // 初始隐藏所有子节点

          children.forEach(child => {
            child.active = false;
          }); // 显示第一个节点并启动动画

          this.showNodeWithHalfScale(children, 0);
        }

        showNodeWithHalfScale(children, index) {
          if (index >= children.length) return;
          var child = children[index];
          child.active = true;
          child.setScale(new Vec3(0, 0, 1)); // 前半部分动画：缩放到一半

          var halfScaleTween = tween(child).to(0.05, {
            scale: new Vec3(0.5, 0.5, 1)
          }, {
            easing: 'quadOut'
          }).call(() => {
            // 缩放到一半时，触发下一个节点
            this.showNodeWithHalfScale(children, index + 1);
          }).start(); // 后半部分动画：从一半缩放到完整

          var fullScaleTween = tween(child).to(0.05, {
            scale: new Vec3(1, 1, 1)
          }, {
            easing: 'quadOut'
          }).start();
          this.activeTweens.push(halfScaleTween, fullScaleTween);
        }

        showNodesSequentially() {
          var children = this.nodeAbility.children;
          if (!children || children.length === 0) return; // 初始隐藏所有子节点

          children.forEach(child => {
            child.active = false;
          }); // 逐个显示子节点

          var delay = 0;
          var interval = 0.1; // 每个节点显示的间隔时间（秒）

          children.forEach((child, index) => {
            var timeoutId = setTimeout(() => {
              child.active = true; // 初始缩放为 0（使用 Vec3 类型）

              child.setScale(new Vec3(0, 0, 1)); // 缩放动画（使用 Vec3 类型）

              var t = tween(child).to(0.1, {
                scale: new Vec3(1, 1, 1)
              }, {
                easing: 'quadOut'
              }).start();
              this.activeTweens.push(t);
            }, delay * 1000);
            this.activeTimeouts.push(timeoutId);
            delay += interval;
          });
        }

        onFresh() {// this.rogueSelectIcons.forEach(element => {
          //     element.updateActive(0);
          // });
        }

        onCancel() {// this.rogueSelectIcons.forEach(element => {
          //     element.updateActive(0);
          // });
          // let btn = this.nodeExclude!.getComponentInChildren(ButtonPlus);
          // if (btn!.getComponentInChildren(Label)!.string == "排除") {
          //     btn!.getComponentInChildren(Label)!.string = "取消";
          //     this.rogueSelectIcons.forEach(element => {
          //         element.updateStatus(2);
          //     });
          // } else {
          //     btn!.getComponentInChildren(Label)!.string = "排除";
          //     this.rogueSelectIcons.forEach(element => {
          //         element.updateStatus(1);
          //     });
          // }
        }

        closeUI() {
          var _this = this;

          return _asyncToGenerator(function* () {
            (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).closeUI(RogueUI);
            _this._callFunc == null || _this._callFunc();
          })();
        }

        onDestroy() {
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).targetOff(this);
        }

        onShow(callFunc) {
          var _this2 = this;

          return _asyncToGenerator(function* () {
            if (callFunc === void 0) {
              callFunc = null;
            }

            _this2._callFunc = callFunc;

            _this2.refreshUI();
          })();
        }

        refreshUI() {
          var len = 3;

          for (var i = 0; i < len; i++) {
            var item = this.rogueSelectNodes[i];
            var iconBg = find("iconBg", item);
            var rogueIcon = find("rogueIcon", item);
            var rogueName = find("rogueName", item);
            var rogueDesc = find("rogueDesc", item);
            var rogueType = find("rogueType", item);
            item.getComponent(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
              error: Error()
            }), ButtonPlus) : ButtonPlus).addClick(() => {
              this.closeUI();
            }, this);
          }
        }

        onHide() {
          return _asyncToGenerator(function* () {})();
        }

        onClose() {
          var _this3 = this;

          return _asyncToGenerator(function* () {
            _this3.stopAllEffects(); // 停止所有动画
            // 可选：重置节点状态


            var children = _this3.nodeAbility.children;

            if (children) {
              children.forEach(child => {
                child.active = false;
                child.setScale(new Vec3(1, 1, 1)); // 恢复默认缩放
              });
            }
          })();
        }

        stopAllEffects() {
          // 停止所有 tween 动画
          this.activeTweens.forEach(tween => {
            tween.stop();
          });
          this.activeTweens = []; // 清除所有 setTimeout

          this.activeTimeouts.forEach(timeoutId => {
            clearTimeout(timeoutId);
          });
          this.activeTimeouts = [];
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "nodeFresh", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "nodeExclude", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "nodeAbility", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "rogueSelectNodes", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return [];
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "freshTimes", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "excludeTimes", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=b285a60718939ec8a36dd5d6b1150dd07e9e01bf.js.map