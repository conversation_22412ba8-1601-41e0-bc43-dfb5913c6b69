import { _decorator, Vec2, CCFloat, CCInteger, Enum } from 'cc';
import { eOrientationType } from './WaveData';
const { ccclass, property } = _decorator;

/**
 * 路径点数据
 */
@ccclass("PathPoint")
export class PathPoint {
    @property({ type: CCFloat, displayName: "X坐标" })
    public x: number = 0;

    @property({ type: CCFloat, displayName: "Y坐标" })
    public y: number = 0;

    @property({ type: CCFloat, displayName: "平滑程度", range: [0, 1], slide: true, tooltip: "0=直线连接, 1=最大平滑曲线" })
    public smoothness: number = 1;

    @property({ type: CCInteger, displayName: "速度", tooltip: "飞机在此点的速度" })
    public speed: number = 500;

    @property({ type: Enum(eOrientationType), displayName: "朝向类型", tooltip: "飞机在此点的朝向" })
    public orientationType: eOrientationType = 0;

    @property({ type: CCInteger, displayName: "朝向参数", tooltip: "根据朝向类型不同而不同" })
    public orientationParam: number = 0;

    constructor(x: number = 0, y: number = 0) {
        this.x = x;
        this.y = y;
    }

    public get position(): Vec2 {
        return new Vec2(this.x, this.y);
    }

    public set position(value: Vec2) {
        this.x = value.x;
        this.y = value.y;
    }
}

/**
 * 路径数据
 */
@ccclass("PathData")
export class PathData {
    @property({ displayName: '路径名称', editorOnly: true })
    public name: string = "";

    @property({ type: CCInteger, displayName: '起始点(默认0)'})
    public startIdx: number = 0;

    @property({ type: CCInteger, displayName: '结束点(-1代表使用路径终点)'})
    public endIdx: number = -1;

    @property({ type: [PathPoint], displayName: '路径点' })
    public points: PathPoint[] = [];

    @property({ displayName: "是否闭合路径", tooltip: "路径是否形成闭环" })
    public closed: boolean = false;

    // 缓存的路径数据（不参与序列化）
    private _cachedCurvePoints: Vec2[] | null = null;

    /**
     * 获取Catmull-Rom曲线上的点
     * @param t 参数值 [0, 1]
     * @param p0 前一个控制点（用于计算切线）
     * @param p1 起始点（曲线经过此点）
     * @param p2 结束点（曲线经过此点）
     * @param p3 后一个控制点（用于计算切线）
     * @param smoothness 平滑程度 [0, 1]，0=直线，1=最平滑曲线
     */
    public static catmullRomPoint(t: number, p0: Vec2, p1: Vec2, p2: Vec2, p3: Vec2, smoothness: number = 0.5): Vec2 {
        // 当smoothness为0时，直接返回线性插值（直线）
        if (smoothness === 0) {
            return Vec2.lerp(new Vec2(), p1, p2, t);
        }

        const t2 = t * t;
        const t3 = t2 * t;

        // 标准Catmull-Rom插值公式
        const catmullRom = new Vec2();
        catmullRom.x = 0.5 * (
            (2 * p1.x) +
            (-p0.x + p2.x) * t +
            (2 * p0.x - 5 * p1.x + 4 * p2.x - p3.x) * t2 +
            (-p0.x + 3 * p1.x - 3 * p2.x + p3.x) * t3
        );

        catmullRom.y = 0.5 * (
            (2 * p1.y) +
            (-p0.y + p2.y) * t +
            (2 * p0.y - 5 * p1.y + 4 * p2.y - p3.y) * t2 +
            (-p0.y + 3 * p1.y - 3 * p2.y + p3.y) * t3
        );

        // 当smoothness不为1时，在线性插值和Catmull-Rom之间混合
        if (smoothness < 1) {
            const linear = Vec2.lerp(new Vec2(), p1, p2, t);
            return Vec2.lerp(new Vec2(), linear, catmullRom, smoothness);
        }

        return catmullRom;
    }

    /**
     * 生成完整的曲线路径点(使用细分)
     */
    public generateCurvePoints(regen: boolean = false): Vec2[] {
        // 生成并缓存曲线点
        if (!this._cachedCurvePoints || regen) {
            this._cachedCurvePoints = this.generateCurvePointsInternal();
        }
        return this._cachedCurvePoints;
    }

    /**
     * 内部方法：实际生成曲线点
     */
    private generateCurvePointsInternal(): Vec2[] {
        if (this.points.length < 2) {
            return this.points.map(p => p.position);
        }

        const curvePoints: Vec2[] = [];
        const pointCount = this.points.length;

        // 添加第一个点（确保曲线经过起点）
        curvePoints.push(this.points[0].position);

        // 计算需要处理的段数
        const segmentCount = this.closed ? pointCount : pointCount - 1;

        // 为每一段生成曲线点
        for (let i = 0; i < segmentCount; i++) {
            const p0 = this.getControlPoint(i - 1);
            const p1 = this.points[i].position;
            const p2 = this.getControlPoint(i + 1);
            const p3 = this.getControlPoint(i + 2);

            const point = this.points[i];
            const pointNext = this.points[(i + 1) % pointCount];
            // 检查是否有任何一个端点要求直线连接
            const startSmoothness = point.smoothness;
            const endSmoothness = pointNext.smoothness;

            const startSpeed = point.speed;
            const endSpeed = pointNext.speed;
            const avgSpeed = (startSpeed + endSpeed) / 2;
            const distanceX = pointNext.x - point.x;
            const distanceY = pointNext.y - point.y; 

            // 如果任一端点的smoothness为0，则整段使用直线
            if (startSmoothness === 0 || endSmoothness === 0) {
                // 直线连接：只需要添加终点即可（起点已经添加过了）
                curvePoints.push(p2);
            } else {
                // 使用平滑程度的最小值（更保守的方法）
                const smoothness = Math.min(startSmoothness, endSmoothness);
                const maxSegments: number = 20;
                // 生成这一段的曲线点（不包括起点，因为已经添加过了）
                for (let j = 1; j <= maxSegments; j++) {
                    const t = j / maxSegments;
                    const point = PathData.catmullRomPoint(t, p0, p1, p2, p3, smoothness);
                    curvePoints.push(point);
                }
            }
        }

        // 对于闭合路径，移除可能重复的最后一个点（如果它与第一个点太接近）
        if (this.closed && curvePoints.length > 1) {
            const firstPoint = curvePoints[0];
            const lastPoint = curvePoints[curvePoints.length - 1];
            const distance = Vec2.distance(firstPoint, lastPoint);

            // 如果最后一个点与第一个点距离很近，移除最后一个点
            if (distance < 0.1) {
                curvePoints.pop();
            }
        }

        return curvePoints;
    }

    /**
     * 获取控制点（处理边界情况）
     */
    private getControlPoint(index: number): Vec2 {
        const pointCount = this.points.length;

        if (this.closed) {
            // 闭合路径，使用循环索引
            const wrappedIndex = ((index % pointCount) + pointCount) % pointCount;
            return this.points[wrappedIndex].position;
        } else {
            // 开放路径，边界处理
            if (index < 0) {
                // 延伸第一个点
                const p0 = this.points[0].position;
                const p1 = this.points[1].position;
                return Vec2.subtract(new Vec2(), p0, Vec2.subtract(new Vec2(), p1, p0));
            } else if (index >= pointCount) {
                // 延伸最后一个点
                const p0 = this.points[pointCount - 2].position;
                const p1 = this.points[pointCount - 1].position;
                return Vec2.add(new Vec2(), p1, Vec2.subtract(new Vec2(), p1, p0));
            } else {
                return this.points[index].position;
            }
        }
    }

    /**
     * 自定义序列化 - 排除缓存数据
     */
    public toJSON(): any {
        return {
            name: this.name,
            startIdx: this.startIdx,
            endIdx: this.endIdx,
            points: this.points,
            closed: this.closed
        };
    }

    /**
     * 自定义反序列化 - 清除缓存确保重新计算
     */
    public fromJSON(data: any): void {
        this.name = data.name || "";
        this.startIdx = data.startIdx || 0;
        this.endIdx = data.endIdx || -1;
        this.points = data.points || [];
        this.closed = data.closed || false;

        // 清除缓存，确保使用新数据重新计算
        this._cachedCurvePoints = null;
    }

    /**
     * 静态工厂方法 - 从JSON创建PathData实例
     */
    public static fromJSON(data: any): PathData {
        const pathData = new PathData();
        pathData.fromJSON(data);
        return pathData;
    }
}