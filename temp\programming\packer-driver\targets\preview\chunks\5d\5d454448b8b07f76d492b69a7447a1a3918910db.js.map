{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/mainPlane/MainPlaneDebug.ts"], "names": ["_decorator", "CCInteger", "MainPlane", "PlaneBaseDebug", "AttributeConst", "ccclass", "property", "MainPlaneDebug", "mainPlane", "start", "node", "getComponent", "maxNuclear", "Math", "floor", "attribute", "getFinalAttributeByKey", "NuclearMax", "curNuclear", "nuclearNum"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAgCC,MAAAA,S,OAAAA,S;;AAChCC,MAAAA,S,iBAAAA,S;;AACFC,MAAAA,c;;AACEC,MAAAA,c,iBAAAA,c;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBN,U;;yBAGTO,c,WADpBF,OAAO,CAAC,gBAAD,C,UAOHC,QAAQ,CAACL,SAAD,C,UAIRK,QAAQ,CAACL,SAAD,C,2BAXb,MACqBM,cADrB;AAAA;AAAA,4CAC2D;AAAA;AAAA;AAAA,eAC7CC,SAD6C,GACf,IADe;AAAA;;AAEvDC,QAAAA,KAAK,GAAS;AACV,gBAAMA,KAAN;AACA,eAAKD,SAAL,GAAiB,KAAKE,IAAL,CAAUC,YAAV;AAAA;AAAA,qCAAjB;AACH;;AAEa,YAAVC,UAAU,GAAU;AAAA;;AACpB,iBAAOC,IAAI,CAACC,KAAL,CAAW,yBAAKN,SAAL,qCAAgBO,SAAhB,CAA0BC,sBAA1B,CAAiD;AAAA;AAAA,gDAAeC,UAAhE,MAA+E,CAA1F,CAAP;AACH;;AAEa,YAAVC,UAAU,GAAU;AAAA;;AACpB,iBAAO,0BAAKV,SAAL,sCAAgBW,UAAhB,KAA8B,CAArC;AACH;;AAbsD,O", "sourcesContent": ["import { _decorator, CCBoolean, CCFloat, CCInteger, Component } from \"cc\";\r\nimport { MainPlane } from \"./MainPlane\";\r\nimport PlaneBaseDebug from \"../PlaneBaseDebug\";\r\nimport { AttributeConst } from \"../../../../const/AttributeConst\";\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass(\"MainPlaneDebug\")\r\nexport default class MainPlaneDebug extends PlaneBaseDebug {\r\n    protected mainPlane: MainPlane | null = null;\r\n    start(): void {\r\n        super.start();\r\n        this.mainPlane = this.node.getComponent(MainPlane);\r\n    }\r\n    @property(CCInteger)\r\n    get maxNuclear():number {\r\n        return Math.floor(this.mainPlane?.attribute.getFinalAttributeByKey(AttributeConst.NuclearMax) || 0);\r\n    }\r\n    @property(CCInteger)\r\n    get curNuclear():number {\r\n        return this.mainPlane?.nuclearNum || 0;\r\n    }\r\n}"]}