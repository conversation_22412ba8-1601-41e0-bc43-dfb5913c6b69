
import * as i18n from './LanguageData';
import { TTFUtils } from './TTFUtils';

import { _decorator, Component, Label, RichText, warn } from 'cc';
const { ccclass, property, executeInEditMode } = _decorator;

@ccclass('LocalizedLabel')
@executeInEditMode
export class LocalizedLabel extends Component {
    label: Label | RichText | null = null;

    @property({ visible: false })
    key: string = '';

    @property({ displayName: 'Key', visible: true })
    get _key() {
        return this.key;
    }
    set _key(str: string) {
        this.updateLabel();
        this.key = str;
    }

    @property({
        type: TTFUtils.CUSTOM_TYPE,
        tooltip: "加载字体类型"
    })
    customType: number = TTFUtils.CUSTOM_TYPE.TEXT;

    onLoad() {
        if (!i18n.ready) {
            i18n.init('zh');
        }
        this.fetchRender();
    }

    start() {
        let lab: Label | RichText | null;
        lab = this.getComponent(Label);
        if (!lab) {
            lab = this.getComponent(RichText);
        }
        if (lab) {
            if (this.customType === TTFUtils.CUSTOM_TYPE.NUMBER
                || this.customType === TTFUtils.CUSTOM_TYPE.TEXT) {
                lab.useSystemFont = false;
                TTFUtils.getInstance().setCustomTTF(lab, this.customType);
            } else {
                lab.useSystemFont = true;
                lab.fontFamily = "Arial";
            }
        } else {
            warn("TTFComponent: ", this.node.name, " 节点没有Label 或 RichText组件！！！");
        }
    }

    fetchRender() {
        let lab: Label | RichText | null;
        lab = this.getComponent(Label);
        if (!lab) {
            lab = this.getComponent(RichText);
        }
        if (lab) {
            this.label = lab;
            this.updateLabel();
            return;
        }
    }

    updateLabel() {
        if (this.key === '') { return }
        this.label && (this.label.string = i18n.getI18StrByKey(this.key));
    }
}
