{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/wave/Wave.ts"], "names": ["_decorator", "Component", "WaveData", "eSpawnOrder", "eWaveCompletion", "GameIns", "WaveEventGroup", "WaveEventGroupContext", "PlaneEventType", "ccclass", "property", "executeInEditMode", "menu", "Wave", "displayName", "editor<PERSON><PERSON><PERSON>", "type", "_isSpawnCompleted", "_isAllEnemyDead", "_waveElapsedTime", "_nextSpawnTime", "_totalWeight", "_waveCompleteParam", "_nextSpawnIndex", "_spawnQueue", "_offsetX", "_offsetY", "_eventGroups", "_eventGroupContext", "_enemyCreated", "_createPlaneDelegate", "isSpawnCompleted", "isAllEnemyDead", "onLoad", "waveData", "spawnOrder", "Random", "spawnGroups", "for<PERSON>ach", "group", "weight", "selfWeight", "eventGroupData", "groupData", "push", "reset", "length", "trigger", "x", "y", "waveCompletionParam", "eval", "waveCompletion", "SpawnCount", "i", "randomWeight", "battleManager", "random", "planeID", "wave", "tryStart", "tick", "dtInMiliseconds", "tickSpawn", "spawnFromQueue", "spawnFromGroup", "spawnSingleFromQueue", "spawnInterval", "index", "spawnPos", "spawnAngle", "createPlane", "planeId", "pos", "angle", "enemy", "enemyManager", "addPlane", "setPos", "initMove", "PlaneEventRegister", "Die", "onEnemyDie", "bind", "plane", "PlaneEventUnRegister", "indexOf", "splice", "setCreatePlaneDelegate", "func"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAqDC,MAAAA,S,OAAAA,S;;AACrDC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,W,iBAAAA,W;AAAaC,MAAAA,e,iBAAAA,e;;AACvBC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,c,iBAAAA,c;AAAgBC,MAAAA,qB,iBAAAA,qB;;AAGhBC,MAAAA,c,iBAAAA,c;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA,iBAArB;AAAwCC,QAAAA;AAAxC,O,GAAiDZ,U;;sBAK1Ca,I,WAHZJ,OAAO,CAAC,MAAD,C,UACPG,IAAI,CAAC,OAAD,C,UACJD,iBAAiB,E,UAEbD,QAAQ,CAAC;AAACI,QAAAA,WAAW,EAAE,IAAd;AAAoBC,QAAAA,UAAU,EAAE;AAAhC,OAAD,C,UAGRL,QAAQ,CAAC;AAACM,QAAAA,IAAI;AAAA;AAAA;AAAL,OAAD,C,yDAPb,MAGaH,IAHb,SAG0BZ,SAH1B,CAGoC;AAAA;AAAA;;AAAA;;AAEM;AAFN;;AAOhC;AACJ;AACA;AAToC,eAUxBgB,iBAVwB,GAUK,KAVL;AAAA,eAYxBC,eAZwB,GAYG,KAZH;AAAA,eAexBC,gBAfwB,GAeG,CAfH;AAAA,eAgBxBC,cAhBwB,GAgBC,CAhBD;AAAA,eAiBxBC,YAjBwB,GAiBD,CAjBC;AAkBhC;AAlBgC,eAmBxBC,kBAnBwB,GAmBK,CAnBL;AAoBhC;AApBgC,eAqBxBC,eArBwB,GAqBE,CArBF;AAAA,eAsBxBC,WAtBwB,GAsBA,EAtBA;AAuBhC;AAvBgC,eAwBxBC,QAxBwB,GAwBL,CAxBK;AAAA,eAyBxBC,QAzBwB,GAyBL,CAzBK;AA0BhC;AA1BgC,eA2BxBC,YA3BwB,GA2BS,EA3BT;AAAA,eA4BxBC,kBA5BwB,GA4ByB,IA5BzB;AA6BhC;AA7BgC,eA8BxBC,aA9BwB,GA8BM,EA9BN;AAuNhC;AAvNgC,eAwNxBC,oBAxNwB,GAwN2D,IAxN3D;AAAA;;AAWL,YAAhBC,gBAAgB,GAAG;AAAE,iBAAO,KAAKd,iBAAZ;AAAgC;;AAEvC,YAAde,cAAc,GAAG;AAAE,iBAAO,KAAKd,eAAZ;AAA8B;;AAmB5De,QAAAA,MAAM,GAAG;AACL,cAAI,KAAKC,QAAT,EAAmB;AACf,gBAAI,KAAKA,QAAL,CAAcC,UAAd,KAA6B;AAAA;AAAA,4CAAYC,MAA7C,EAAqD;AACjD,mBAAKf,YAAL,GAAoB,CAApB,CADiD,CAEjD;;AACA,mBAAKa,QAAL,CAAcG,WAAd,CAA0BC,OAA1B,CAAmCC,KAAD,IAAW;AACzC,qBAAKlB,YAAL,IAAqBkB,KAAK,CAACC,MAA3B;AACAD,gBAAAA,KAAK,CAACE,UAAN,GAAmB,KAAKpB,YAAxB;AACH,eAHD;AAIH;;AAED,gBAAI,KAAKa,QAAL,CAAcQ,cAAlB,EAAkC;AAC9B,kBAAI,CAAC,KAAKd,kBAAV,EAA8B;AAC1B,qBAAKA,kBAAL,GAA0B;AAAA;AAAA,qEAA1B;AACH;;AACD,mBAAKM,QAAL,CAAcQ,cAAd,CAA6BJ,OAA7B,CAAsCK,SAAD,IAAe;AAChD,sBAAMJ,KAAK,GAAG;AAAA;AAAA,sDAAmB,KAAKX,kBAAxB,EAA6Ce,SAA7C,CAAd;;AACA,qBAAKhB,YAAL,CAAkBiB,IAAlB,CAAuBL,KAAvB;AACH,eAHD;AAIH;AACJ;AACJ;;AAEOM,QAAAA,KAAK,GAAG;AACZ,eAAK5B,iBAAL,GAAyB,KAAzB;AACA,eAAKC,eAAL,GAAuB,KAAvB;AACA,eAAKC,gBAAL,GAAwB,CAAxB;AACA,eAAKC,cAAL,GAAsB,CAAtB;AACA,eAAKG,eAAL,GAAuB,CAAvB;AACA,eAAKC,WAAL,CAAiBsB,MAAjB,GAA0B,CAA1B;AACA,eAAKlB,kBAAL,IAA4B,KAAKA,kBAAL,CAAyBiB,KAAzB,EAA5B;;AAEA,eAAKlB,YAAL,CAAkBW,OAAlB,CAA2BC,KAAD,IAAW;AACjCA,YAAAA,KAAK,CAACM,KAAN;AACH,WAFD;;AAIA,cAAI,KAAKhB,aAAL,CAAmBiB,MAAnB,GAA4B,CAAhC,EAAmC;AAC/B,iBAAKjB,aAAL,CAAmBiB,MAAnB,GAA4B,CAA5B;AACH;AACJ;;AAEDC,QAAAA,OAAO,CAACC,CAAD,EAAYC,CAAZ,EAAuB;AAC1B,eAAKJ,KAAL;AACA,eAAKpB,QAAL,GAAgBuB,CAAhB;AACA,eAAKtB,QAAL,GAAgBuB,CAAhB,CAH0B,CAK1B;;AACA,cAAI,KAAKf,QAAT,EAAmB;AACf,iBAAKZ,kBAAL,GAA0B,KAAKY,QAAL,CAAcgB,mBAAd,CAAkCC,IAAlC,EAA1B;;AACA,gBAAI,KAAKjB,QAAL,CAAckB,cAAd,KAAiC;AAAA;AAAA,oDAAgBC,UAArD,EAAiE;AAC7D,kBAAI,KAAKnB,QAAL,CAAcC,UAAd,KAA6B;AAAA;AAAA,8CAAYC,MAA7C,EAAqD;AACjD,qBAAK,IAAIkB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKhC,kBAAzB,EAA6CgC,CAAC,EAA9C,EAAkD;AAC9C,wBAAMC,YAAY,GAAG;AAAA;AAAA,0CAAQC,aAAR,CAAsBC,MAAtB,KAAiC,KAAKpC,YAA3D;;AACA,uBAAK,MAAMkB,KAAX,IAAoB,KAAKL,QAAL,CAAcG,WAAlC,EAA+C;AAC3C,wBAAIkB,YAAY,IAAIhB,KAAK,CAACE,UAA1B,EAAsC;AAClC,2BAAKjB,WAAL,CAAiBoB,IAAjB,CAAsBL,KAAK,CAACmB,OAA5B;;AACA;AACH;AACJ;AACJ;AACJ,eAVD,MAUO;AACH,qBAAK,IAAIJ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKhC,kBAAzB,EAA6CgC,CAAC,EAA9C,EAAkD;AAC9C;AACA,uBAAK9B,WAAL,CAAiBoB,IAAjB,CAAsB,KAAKV,QAAL,CAAcG,WAAd,CAA0BiB,CAAC,GAAG,KAAKpB,QAAL,CAAcG,WAAd,CAA0BS,MAAxD,EAAgEY,OAAtF;AACH;AACJ;AACJ;AACJ;;AAED,eAAK9B,kBAAL,KAA4B,KAAKA,kBAAL,CAAyB+B,IAAzB,GAAgC,IAA5D;;AACA,eAAKhC,YAAL,CAAkBW,OAAlB,CAA2BC,KAAD,IAAW;AACjCA,YAAAA,KAAK,CAACqB,QAAN;AACH,WAFD;AAGH,SAzG+B,CA2GhC;;;AACAC,QAAAA,IAAI,CAACC,eAAD,EAA0B;AAC1B,cAAI,CAAC,KAAK7C,iBAAV,EAA6B;AACzB,iBAAK8C,SAAL,CAAeD,eAAf;AACH;AACJ;;AAEOC,QAAAA,SAAS,CAACD,eAAD,EAA0B;AACvC,eAAK3C,gBAAL,IAAyB2C,eAAzB;;AACA,cAAI,KAAK5B,QAAL,CAAckB,cAAd,KAAiC;AAAA;AAAA,kDAAgBC,UAArD,EAAiE;AAC7D;AACA,gBAAI,KAAKlC,gBAAL,IAAyB,KAAKC,cAAlC,EAAkD;AAC9C,kBAAI,CAAC,KAAK4C,cAAL,EAAL,EAA4B;AACxB,qBAAK/C,iBAAL,GAAyB,IAAzB;AACH;AACJ;AACJ,WAPD,MAOO;AACH;AACA,gBAAI,KAAKE,gBAAL,IAAyB,KAAKG,kBAAlC,EAAsD;AAClD,mBAAKL,iBAAL,GAAyB,IAAzB;AACH,aAFD,MAEO;AACH,kBAAI,KAAKE,gBAAL,IAAyB,KAAKC,cAAlC,EAAkD;AAC9C,qBAAK6C,cAAL;AACH;AACJ;AACJ;;AAED,cAAI,KAAKtC,YAAL,CAAkBmB,MAAlB,GAA2B,CAA/B,EAAkC;AAC9B,iBAAK,IAAIQ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK3B,YAAL,CAAkBmB,MAAtC,EAA8CQ,CAAC,EAA/C,EAAmD;AAC/C,mBAAK3B,YAAL,CAAkB2B,CAAlB,EAAqBO,IAArB,CAA0BC,eAA1B;AACH;AACJ;AACJ;;AAEOE,QAAAA,cAAc,GAAY;AAC9B,cAAI,KAAKzC,eAAL,IAAwB,KAAKC,WAAL,CAAiBsB,MAA7C,EAAqD;AACjD,mBAAO,KAAP;AACH;;AAED,eAAKoB,oBAAL,CAA0B,KAAK3C,eAAL,EAA1B;AACA,eAAKH,cAAL,GAAsB,KAAKD,gBAAL,GAAwB,KAAKe,QAAL,CAAciC,aAAd,CAA4BhB,IAA5B,EAA9C;AACA,iBAAO,IAAP;AACH;;AAEOe,QAAAA,oBAAoB,CAACE,KAAD,EAAsB;AAC9C,cAAIA,KAAK,IAAI,KAAK5C,WAAL,CAAiBsB,MAA9B,EAAsC;AAClC;AACH;;AAED,cAAIuB,QAAQ,GAAG,KAAKnC,QAAL,CAAcmC,QAA7B;AACA,cAAIC,UAAU,GAAG,KAAKpC,QAAL,CAAcoC,UAAd,CAAyBnB,IAAzB,EAAjB,CAN8C,CAO9C;;AAEA,eAAKoB,WAAL,CAAiB,KAAK/C,WAAL,CAAiB4C,KAAjB,CAAjB,EAA0CC,QAA1C,EAAoDC,UAApD;AACH;;AAEOL,QAAAA,cAAc,GAAS;AAC3B,eAAK7C,cAAL,GAAsB,KAAKD,gBAAL,GAAwB,KAAKe,QAAL,CAAciC,aAAd,CAA4BhB,IAA5B,EAA9C;AAEA,cAAIkB,QAAQ,GAAG,KAAKnC,QAAL,CAAcmC,QAA7B;AACA,cAAIC,UAAU,GAAG,KAAKpC,QAAL,CAAcoC,UAAd,CAAyBnB,IAAzB,EAAjB;;AAEA,cAAI,KAAKjB,QAAL,CAAcC,UAAd,KAA6B;AAAA;AAAA,0CAAYC,MAA7C,EAAqD;AACjD,kBAAMmB,YAAY,GAAG;AAAA;AAAA,oCAAQC,aAAR,CAAsBC,MAAtB,KAAiC,KAAKpC,YAA3D;;AACA,iBAAK,MAAMkB,KAAX,IAAoB,KAAKL,QAAL,CAAcG,WAAlC,EAA+C;AAC3C,kBAAIkB,YAAY,IAAIhB,KAAK,CAACE,UAA1B,EAAsC;AAClC,qBAAK8B,WAAL,CAAiBhC,KAAK,CAACmB,OAAvB,EAAgCW,QAAhC,EAA0CC,UAA1C;AACA;AACH;AACJ;AACJ,WARD,MAQO;AACH,iBAAKC,WAAL,CAAiB,KAAKrC,QAAL,CAAcG,WAAd,CAA0B,KAAKd,eAAL,KAAyB,KAAKW,QAAL,CAAcG,WAAd,CAA0BS,MAA7E,EAAqFY,OAAtG,EAA+GW,QAA/G,EAAyHC,UAAzH;AACH;AACJ;;AAEOC,QAAAA,WAAW,CAACC,OAAD,EAAkBC,GAAlB,EAA6BC,KAA7B,EAA4C;AAC3DD,UAAAA,GAAG,CAACzB,CAAJ,IAAS,KAAKvB,QAAd;AACAgD,UAAAA,GAAG,CAACxB,CAAJ,IAAS,KAAKvB,QAAd;;AAEA,cAAI,KAAKI,oBAAT,EAA+B;AAC3B,iBAAKA,oBAAL,CAA0B0C,OAA1B,EAAmCC,GAAnC,EAAwCC,KAAxC;;AACA;AACH;;AAED,cAAIC,KAAK,GAAG;AAAA;AAAA,kCAAQC,YAAR,CAAqBC,QAArB,CAA8BL,OAA9B,CAAZ;;AACA,cAAIG,KAAJ,EAAW;AACP;AACA;AACA;AACAA,YAAAA,KAAK,CAACG,MAAN,CAAaL,GAAG,CAACzB,CAAjB,EAAoByB,GAAG,CAACxB,CAAxB;AACA0B,YAAAA,KAAK,CAACI,QAAN,CAAeL,KAAf;AACAC,YAAAA,KAAK,CAACK,kBAAN,CAAyB;AAAA;AAAA,kDAAeC,GAAxC,EAA6C,KAAKC,UAAL,CAAgBC,IAAhB,CAAqB,IAArB,CAA7C;;AACA,iBAAKtD,aAAL,CAAmBe,IAAnB,CAAwB+B,KAAxB,EAPO,CAOyB;;AACnC;AACJ;;AAEOO,QAAAA,UAAU,CAACE,KAAD,EAAoB;AAClCA,UAAAA,KAAK,CAACC,oBAAN,CAA2B;AAAA;AAAA,gDAAeJ,GAA1C,EAA+C,KAAKC,UAAL,CAAgBC,IAAhB,CAAqB,IAArB,CAA/C;;AACA,gBAAMf,KAAK,GAAG,KAAKvC,aAAL,CAAmByD,OAAnB,CAA2BF,KAA3B,CAAd;;AACA,cAAIhB,KAAK,KAAK,CAAC,CAAf,EAAkB;AACd,iBAAKvC,aAAL,CAAmB0D,MAAnB,CAA0BnB,KAA1B,EAAiC,CAAjC;;AAEA,gBAAI,KAAKvC,aAAL,CAAmBiB,MAAnB,KAA8B,CAA9B,IAAmC,KAAK7B,iBAA5C,EAA+D;AAC3D,mBAAKC,eAAL,GAAuB,IAAvB;AACH;AACJ;AACJ;;AAIMsE,QAAAA,sBAAsB,CAACC,IAAD,EAA4D;AACrF,eAAK3D,oBAAL,GAA4B2D,IAA5B;AACH;;AA3N+B,O;;;;;iBAEb,E;;;;;;;iBAGW;AAAA;AAAA,qC", "sourcesContent": ["import { _decorator, CCBoolean, CCFloat, CCInteger, CCString, Component, Vec2 } from 'cc';\r\nimport { WaveData, eSpawnOrder, eWaveCompletion } from '../data/WaveData';\r\nimport { GameIns } from 'db://assets/bundles/common/script/game/GameIns';\r\nimport { WaveEventGroup, WaveEventGroupContext } from './WaveEventGroup';\r\nimport EnemyPlane from 'db://assets/bundles/common/script/game/ui/plane/enemy/EnemyPlane';\r\nimport PlaneBase from 'db://assets/bundles/common/script/game/ui/plane/PlaneBase';\r\nimport { PlaneEventType } from 'db://assets/bundles/common/script/game/ui/plane/event/PlaneEventType';\r\nconst { ccclass, property, executeInEditMode, menu } = _decorator;\r\n\r\n@ccclass('Wave')\r\n@menu(\"怪物/波次\")\r\n@executeInEditMode()\r\nexport class Wave extends Component {\r\n    @property({displayName: '名称', editorOnly: true})\r\n    waveName: string = '';                // 备注(策划用)\r\n\r\n    @property({type:WaveData})\r\n    readonly waveData: WaveData = new WaveData();\r\n\r\n    /*\r\n     * 以下是新的wave逻辑, 旧的逻辑主要在WaveManager与EnemyWave\r\n     */\r\n    private _isSpawnCompleted: boolean = false;\r\n    public get isSpawnCompleted() { return this._isSpawnCompleted; }\r\n    private _isAllEnemyDead: boolean = false;\r\n    public get isAllEnemyDead() { return this._isAllEnemyDead; }\r\n    \r\n    private _waveElapsedTime: number = 0;\r\n    private _nextSpawnTime: number = 0;\r\n    private _totalWeight: number = 0;\r\n    // 这个参数可能是时间，也可能是数量，取决于waveData.waveCompletion\r\n    private _waveCompleteParam: number = 0;\r\n    // 以下两个是用在waveCompletion == SpawnCount时的队列\r\n    private _nextSpawnIndex: number = 0;\r\n    private _spawnQueue: number[] = [];\r\n    // 当前wave的偏移位置\r\n    private _offsetX: number = 0;\r\n    private _offsetY: number = 0;\r\n    // 事件组\r\n    private _eventGroups: WaveEventGroup[] = [];\r\n    private _eventGroupContext: WaveEventGroupContext|null = null;\r\n    // 怪物\r\n    private _enemyCreated: EnemyPlane[] = [];\r\n\r\n    onLoad() {\r\n        if (this.waveData) {\r\n            if (this.waveData.spawnOrder === eSpawnOrder.Random) {\r\n                this._totalWeight = 0;\r\n                // add up _totalWeight if is random\r\n                this.waveData.spawnGroups.forEach((group) => {\r\n                    this._totalWeight += group.weight;\r\n                    group.selfWeight = this._totalWeight;\r\n                });\r\n            }\r\n            \r\n            if (this.waveData.eventGroupData) {\r\n                if (!this._eventGroupContext) {\r\n                    this._eventGroupContext = new WaveEventGroupContext();\r\n                }\r\n                this.waveData.eventGroupData.forEach((groupData) => {\r\n                    const group = new WaveEventGroup(this._eventGroupContext!, groupData);\r\n                    this._eventGroups.push(group);\r\n                });\r\n            }\r\n        }\r\n    }\r\n\r\n    private reset() {\r\n        this._isSpawnCompleted = false;\r\n        this._isAllEnemyDead = false;\r\n        this._waveElapsedTime = 0;\r\n        this._nextSpawnTime = 0;\r\n        this._nextSpawnIndex = 0;\r\n        this._spawnQueue.length = 0;\r\n        this._eventGroupContext && (this._eventGroupContext!.reset());\r\n        \r\n        this._eventGroups.forEach((group) => {\r\n            group.reset();\r\n        });\r\n\r\n        if (this._enemyCreated.length > 0) {\r\n            this._enemyCreated.length = 0;\r\n        }\r\n    }\r\n\r\n    trigger(x: number, y: number) {\r\n        this.reset();\r\n        this._offsetX = x;\r\n        this._offsetY = y;\r\n\r\n        // 对于固定数量的波次，可以预先生成队列，每次从里面取即可\r\n        if (this.waveData) {\r\n            this._waveCompleteParam = this.waveData.waveCompletionParam.eval();\r\n            if (this.waveData.waveCompletion === eWaveCompletion.SpawnCount) {\r\n                if (this.waveData.spawnOrder === eSpawnOrder.Random) {\r\n                    for (let i = 0; i < this._waveCompleteParam; i++) {\r\n                        const randomWeight = GameIns.battleManager.random() * this._totalWeight;\r\n                        for (const group of this.waveData.spawnGroups) {\r\n                            if (randomWeight <= group.selfWeight) {\r\n                                this._spawnQueue.push(group.planeID);\r\n                                break;\r\n                            }\r\n                        }\r\n                    }\r\n                } else {\r\n                    for (let i = 0; i < this._waveCompleteParam; i++) {\r\n                        // 通过取余实现循环\r\n                        this._spawnQueue.push(this.waveData.spawnGroups[i % this.waveData.spawnGroups.length].planeID);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        this._eventGroupContext && (this._eventGroupContext!.wave = this);\r\n        this._eventGroups.forEach((group) => {\r\n            group.tryStart();\r\n        });\r\n    }\r\n\r\n    // tick wave\r\n    tick(dtInMiliseconds: number) {\r\n        if (!this._isSpawnCompleted) {\r\n            this.tickSpawn(dtInMiliseconds);\r\n        }\r\n    }\r\n    \r\n    private tickSpawn(dtInMiliseconds: number) {\r\n        this._waveElapsedTime += dtInMiliseconds;\r\n        if (this.waveData.waveCompletion === eWaveCompletion.SpawnCount) {\r\n            // 产出固定数量的波次\r\n            if (this._waveElapsedTime >= this._nextSpawnTime) {\r\n                if (!this.spawnFromQueue()) {\r\n                    this._isSpawnCompleted = true;\r\n                }\r\n            }\r\n        } else {\r\n            // 完全根据时间的波次\r\n            if (this._waveElapsedTime >= this._waveCompleteParam) {\r\n                this._isSpawnCompleted = true;\r\n            } else {\r\n                if (this._waveElapsedTime >= this._nextSpawnTime) {\r\n                    this.spawnFromGroup();\r\n                }\r\n            }\r\n        }\r\n\r\n        if (this._eventGroups.length > 0) {\r\n            for (let i = 0; i < this._eventGroups.length; i++) {\r\n                this._eventGroups[i].tick(dtInMiliseconds);\r\n            }\r\n        }\r\n    }\r\n\r\n    private spawnFromQueue(): boolean {        \r\n        if (this._nextSpawnIndex >= this._spawnQueue.length) {\r\n            return false;\r\n        }\r\n\r\n        this.spawnSingleFromQueue(this._nextSpawnIndex++);\r\n        this._nextSpawnTime = this._waveElapsedTime + this.waveData.spawnInterval.eval();\r\n        return true;\r\n    }\r\n\r\n    private spawnSingleFromQueue(index: number): void {\r\n        if (index >= this._spawnQueue.length) {\r\n            return;\r\n        }\r\n\r\n        let spawnPos = this.waveData.spawnPos;\r\n        let spawnAngle = this.waveData.spawnAngle.eval();\r\n        // let spawnSpeed = this.waveData.spawnSpeed.eval();\r\n\r\n        this.createPlane(this._spawnQueue[index], spawnPos, spawnAngle);\r\n    }\r\n\r\n    private spawnFromGroup(): void {\r\n        this._nextSpawnTime = this._waveElapsedTime + this.waveData.spawnInterval.eval();\r\n\r\n        let spawnPos = this.waveData.spawnPos;\r\n        let spawnAngle = this.waveData.spawnAngle.eval();\r\n\r\n        if (this.waveData.spawnOrder === eSpawnOrder.Random) {\r\n            const randomWeight = GameIns.battleManager.random() * this._totalWeight;\r\n            for (const group of this.waveData.spawnGroups) {\r\n                if (randomWeight <= group.selfWeight) {\r\n                    this.createPlane(group.planeID, spawnPos, spawnAngle);\r\n                    break;\r\n                }\r\n            }\r\n        } else {\r\n            this.createPlane(this.waveData.spawnGroups[this._nextSpawnIndex++ % this.waveData.spawnGroups.length].planeID, spawnPos, spawnAngle);\r\n        }\r\n    }\r\n\r\n    private createPlane(planeId: number, pos: Vec2, angle: number) {\r\n        pos.x += this._offsetX;\r\n        pos.y += this._offsetY;\r\n        \r\n        if (this._createPlaneDelegate) {\r\n            this._createPlaneDelegate(planeId, pos, angle);\r\n            return;\r\n        }\r\n\r\n        let enemy = GameIns.enemyManager.addPlane(planeId);\r\n        if (enemy) {\r\n            // enemy.initTrack(this.waveData.trackGroups, this.waveData.liveParam, spawnPos.x, spawnPos.y);\r\n            // enemy.setStandByTime(0);\r\n            // console.log(\"createPlane\", planeId, pos, angle, speed);\r\n            enemy.setPos(pos.x, pos.y);\r\n            enemy.initMove(angle);\r\n            enemy.PlaneEventRegister(PlaneEventType.Die, this.onEnemyDie.bind(this));\r\n            this._enemyCreated.push(enemy); // 记录下来，方便后面判断是否都死了\r\n        }\r\n    }\r\n\r\n    private onEnemyDie(plane: EnemyPlane) {\r\n        plane.PlaneEventUnRegister(PlaneEventType.Die, this.onEnemyDie.bind(this));\r\n        const index = this._enemyCreated.indexOf(plane);\r\n        if (index !== -1) {\r\n            this._enemyCreated.splice(index, 1);\r\n\r\n            if (this._enemyCreated.length === 0 && this._isSpawnCompleted) {\r\n                this._isAllEnemyDead = true;\r\n            }\r\n        }\r\n    }\r\n\r\n    // 以下几个函数是为了给编辑器预览用\r\n    private _createPlaneDelegate: ((planeId: number, pos: Vec2, angle: number) => void)|null = null;\r\n    public setCreatePlaneDelegate(func: (planeId: number, pos: Vec2, angle: number) => void) {\r\n        this._createPlaneDelegate = func;\r\n    }\r\n}"]}