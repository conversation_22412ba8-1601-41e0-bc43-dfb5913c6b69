{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/wave/Wave.ts"], "names": ["_decorator", "Component", "Vec2", "WaveData", "FormationGroup", "eSpawnOrder", "eWaveCompletion", "PathData", "GameIns", "WaveEventGroup", "WaveEventGroupContext", "PlaneEventType", "ccclass", "property", "executeInEditMode", "menu", "Wave", "displayName", "editor<PERSON><PERSON><PERSON>", "type", "_isSpawnCompleted", "_isAllEnemyDead", "_waveElapsedTime", "_nextSpawnTime", "_totalWeight", "_waveCompleteParam", "_spawnIndex", "_spawnQueue", "_originX", "_originY", "_formationGroup", "_path", "_eventGroups", "_eventGroupContext", "_enemyCreated", "_createPlaneDelegate", "isSpawnCompleted", "isAllEnemyDead", "onLoad", "waveData", "spawnOrder", "Random", "spawnGroups", "for<PERSON>ach", "group", "weight", "selfWeight", "eventGroupData", "groupData", "push", "formationAsset", "Object", "assign", "json", "pathAsset", "fromJSON", "reset", "length", "random", "battleManager", "Math", "trigger", "x", "y", "console", "log", "waveCompletionParam", "eval", "waveCompletion", "SpawnCount", "i", "randomWeight", "planeID", "wave", "tryStart", "tick", "dtInMiliseconds", "tickSpawn", "spawnFromQueue", "spawnFromGroup", "spawnSingleFromQueue", "spawnInterval", "index", "spawnOffset", "spawnPosOffset", "spawnAngle", "createPlane", "planeId", "offset", "angle", "origin", "points", "startIdx", "position", "point", "enemy", "enemyManager", "addPlane", "initPath", "initMove", "PlaneEventRegister", "Die", "onEnemyDie", "bind", "plane", "PlaneEventUnRegister", "indexOf", "splice", "setCreatePlaneDelegate", "func"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAqDC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;;AAChEC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,c,iBAAAA,c;AAAgCC,MAAAA,W,iBAAAA,W;AAAaC,MAAAA,e,iBAAAA,e;;AACvDC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,c,iBAAAA,c;AAAgBC,MAAAA,qB,iBAAAA,qB;;AAGhBC,MAAAA,c,iBAAAA,c;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA,iBAArB;AAAwCC,QAAAA;AAAxC,O,GAAiDf,U;;sBAK1CgB,I,WAHZJ,OAAO,CAAC,MAAD,C,UACPG,IAAI,CAAC,OAAD,C,UACJD,iBAAiB,E,UAEbD,QAAQ,CAAC;AAACI,QAAAA,WAAW,EAAE,IAAd;AAAoBC,QAAAA,UAAU,EAAE;AAAhC,OAAD,C,UAGRL,QAAQ,CAAC;AAACM,QAAAA,IAAI;AAAA;AAAA;AAAL,OAAD,C,yDAPb,MAGaH,IAHb,SAG0Bf,SAH1B,CAGoC;AAAA;AAAA;;AAAA;;AAEM;AAFN;;AAOhC;AACJ;AACA;AAToC,eAUxBmB,iBAVwB,GAUK,KAVL;AAAA,eAYxBC,eAZwB,GAYG,KAZH;AAAA,eAexBC,gBAfwB,GAeG,CAfH;AAAA,eAgBxBC,cAhBwB,GAgBC,CAhBD;AAAA,eAiBxBC,YAjBwB,GAiBD,CAjBC;AAkBhC;AAlBgC,eAmBxBC,kBAnBwB,GAmBK,CAnBL;AAAA,eAoBxBC,WApBwB,GAoBF,CApBE;AAsBhC;AAtBgC,eAuBxBC,WAvBwB,GAuBA,EAvBA;AAwBhC;AAxBgC,eAyBxBC,QAzBwB,GAyBL,CAzBK;AAAA,eA0BxBC,QA1BwB,GA0BL,CA1BK;AAAA,eA2BxBC,eA3BwB,GA2Be,IA3Bf;AAAA,eA4BxBC,KA5BwB,GA4BD,IA5BC;AA6BhC;AA7BgC,eA8BxBC,YA9BwB,GA8BS,EA9BT;AAAA,eA+BxBC,kBA/BwB,GA+ByB,IA/BzB;AAgChC;AAhCgC,eAiCxBC,aAjCwB,GAiCM,EAjCN;AAoQhC;AApQgC,eAqQxBC,oBArQwB,GAqQ2D,IArQ3D;AAAA;;AAWL,YAAhBC,gBAAgB,GAAG;AAAE,iBAAO,KAAKhB,iBAAZ;AAAgC;;AAEvC,YAAdiB,cAAc,GAAG;AAAE,iBAAO,KAAKhB,eAAZ;AAA8B;;AAsB5DiB,QAAAA,MAAM,GAAG;AACL,cAAI,KAAKC,QAAT,EAAmB;AACf,gBAAI,KAAKA,QAAL,CAAcC,UAAd,KAA6B;AAAA;AAAA,4CAAYC,MAA7C,EAAqD;AACjD,mBAAKjB,YAAL,GAAoB,CAApB,CADiD,CAEjD;;AACA,mBAAKe,QAAL,CAAcG,WAAd,CAA0BC,OAA1B,CAAmCC,KAAD,IAAW;AACzC,qBAAKpB,YAAL,IAAqBoB,KAAK,CAACC,MAA3B;AACAD,gBAAAA,KAAK,CAACE,UAAN,GAAmB,KAAKtB,YAAxB;AACH,eAHD;AAIH;;AAED,gBAAI,KAAKe,QAAL,CAAcQ,cAAlB,EAAkC;AAC9B,kBAAI,CAAC,KAAKd,kBAAV,EAA8B;AAC1B,qBAAKA,kBAAL,GAA0B;AAAA;AAAA,qEAA1B;AACH;;AACD,mBAAKM,QAAL,CAAcQ,cAAd,CAA6BJ,OAA7B,CAAsCK,SAAD,IAAe;AAChD,sBAAMJ,KAAK,GAAG;AAAA;AAAA,sDAAmB,KAAKX,kBAAxB,EAA6Ce,SAA7C,CAAd;;AACA,qBAAKhB,YAAL,CAAkBiB,IAAlB,CAAuBL,KAAvB;AACH,eAHD;AAIH;;AAED,gBAAI,KAAKL,QAAL,CAAcW,cAAlB,EAAkC;AAC9B,mBAAKpB,eAAL,GAAuB;AAAA;AAAA,qDAAvB;AACAqB,cAAAA,MAAM,CAACC,MAAP,CAAc,KAAKtB,eAAnB,EAAoC,KAAKS,QAAL,CAAcW,cAAd,CAA6BG,IAAjE;AACH;;AAED,gBAAI,KAAKd,QAAL,CAAce,SAAlB,EAA6B;AACzB,mBAAKvB,KAAL,GAAa;AAAA;AAAA,wCAASwB,QAAT,CAAkB,KAAKhB,QAAL,CAAce,SAAd,CAAwBD,IAA1C,CAAb;AACH;AACJ;AACJ;;AAEOG,QAAAA,KAAK,GAAG;AACZ,eAAKpC,iBAAL,GAAyB,KAAzB;AACA,eAAKC,eAAL,GAAuB,KAAvB;AACA,eAAKC,gBAAL,GAAwB,CAAxB;AACA,eAAKC,cAAL,GAAsB,CAAtB;AACA,eAAKG,WAAL,GAAmB,CAAnB;AACA,eAAKC,WAAL,CAAiB8B,MAAjB,GAA0B,CAA1B;AACA,eAAKxB,kBAAL,IAA4B,KAAKA,kBAAL,CAAyBuB,KAAzB,EAA5B;;AAEA,eAAKxB,YAAL,CAAkBW,OAAlB,CAA2BC,KAAD,IAAW;AACjCA,YAAAA,KAAK,CAACY,KAAN;AACH,WAFD;;AAIA,cAAI,KAAKtB,aAAL,CAAmBuB,MAAnB,GAA4B,CAAhC,EAAmC;AAC/B,iBAAKvB,aAAL,CAAmBuB,MAAnB,GAA4B,CAA5B;AACH;AACJ;;AAEY,eAANC,MAAM,GAAW;AACpB,cAAI;AAAA;AAAA,kCAAQC,aAAZ,EAA2B;AACvB,mBAAO;AAAA;AAAA,oCAAQA,aAAR,CAAsBD,MAAtB,EAAP;AACH;;AAED,iBAAOE,IAAI,CAACF,MAAL,EAAP;AACH;;AAEDG,QAAAA,OAAO,CAACC,CAAD,EAAYC,CAAZ,EAAuB;AAC1B,eAAKP,KAAL;AACA,eAAK5B,QAAL,GAAgBkC,CAAhB;AACA,eAAKjC,QAAL,GAAgBkC,CAAhB;AAEAC,UAAAA,OAAO,CAACC,GAAR,CAAY,0BAA0BH,CAA1B,GAA8B,OAA9B,GAAwCC,CAApD,EAL0B,CAO1B;;AACA,cAAI,KAAKxB,QAAT,EAAmB;AACf,iBAAKd,kBAAL,GAA0B,KAAKc,QAAL,CAAc2B,mBAAd,CAAkCC,IAAlC,EAA1B;;AACA,gBAAI,KAAK5B,QAAL,CAAc6B,cAAd,KAAiC;AAAA;AAAA,oDAAgBC,UAArD,EAAiE;AAC7D,kBAAI,KAAK9B,QAAL,CAAcC,UAAd,KAA6B;AAAA;AAAA,8CAAYC,MAA7C,EAAqD;AACjD,qBAAK,IAAI6B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK7C,kBAAzB,EAA6C6C,CAAC,EAA9C,EAAkD;AAC9C,wBAAMC,YAAY,GAAGvD,IAAI,CAAC0C,MAAL,KAAgB,KAAKlC,YAA1C;;AACA,uBAAK,MAAMoB,KAAX,IAAoB,KAAKL,QAAL,CAAcG,WAAlC,EAA+C;AAC3C,wBAAI6B,YAAY,IAAI3B,KAAK,CAACE,UAA1B,EAAsC;AAClC,2BAAKnB,WAAL,CAAiBsB,IAAjB,CAAsBL,KAAK,CAAC4B,OAA5B;;AACA;AACH;AACJ;AACJ;AACJ,eAVD,MAUO;AACH,qBAAK,IAAIF,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK7C,kBAAzB,EAA6C6C,CAAC,EAA9C,EAAkD;AAC9C;AACA,uBAAK3C,WAAL,CAAiBsB,IAAjB,CAAsB,KAAKV,QAAL,CAAcG,WAAd,CAA0B4B,CAAC,GAAG,KAAK/B,QAAL,CAAcG,WAAd,CAA0Be,MAAxD,EAAgEe,OAAtF;AACH;AACJ;AACJ,aAnBc,CAqBf;;;AACA,gBAAI,CAAC,KAAK1C,eAAN,IAAyB,KAAKS,QAAL,CAAcW,cAA3C,EAA2D;AACvD,mBAAKpB,eAAL,GAAuB;AAAA;AAAA,qDAAvB;AACAqB,cAAAA,MAAM,CAACC,MAAP,CAAc,KAAKtB,eAAnB,EAAoC,KAAKS,QAAL,CAAcW,cAAd,CAA6BG,IAAjE;AACH;;AAED,gBAAI,CAAC,KAAKtB,KAAN,IAAe,KAAKQ,QAAL,CAAce,SAAjC,EAA4C;AACxC,mBAAKvB,KAAL,GAAa;AAAA;AAAA,wCAASwB,QAAT,CAAkB,KAAKhB,QAAL,CAAce,SAAd,CAAwBD,IAA1C,CAAb;AACH;AACJ;;AAED,eAAKpB,kBAAL,KAA4B,KAAKA,kBAAL,CAAyBwC,IAAzB,GAAgC,IAA5D;;AACA,eAAKzC,YAAL,CAAkBW,OAAlB,CAA2BC,KAAD,IAAW;AACjCA,YAAAA,KAAK,CAAC8B,QAAN;AACH,WAFD;AAGH,SAzI+B,CA2IhC;;;AACAC,QAAAA,IAAI,CAACC,eAAD,EAA0B;AAC1B,cAAI,CAAC,KAAKxD,iBAAV,EAA6B;AACzB,iBAAKyD,SAAL,CAAeD,eAAf;AACH;AACJ;;AAEOC,QAAAA,SAAS,CAACD,eAAD,EAA0B;AACvC,eAAKtD,gBAAL,IAAyBsD,eAAzB;;AACA,cAAI,KAAKrC,QAAL,CAAc6B,cAAd,KAAiC;AAAA;AAAA,kDAAgBC,UAArD,EAAiE;AAC7D;AACA,gBAAI,KAAK3C,WAAL,IAAoB,KAAKD,kBAA7B,EAAiD;AAC7C,mBAAKL,iBAAL,GAAyB,IAAzB;AACH,aAFD,MAEO;AACH,kBAAI,KAAKE,gBAAL,IAAyB,KAAKC,cAAlC,EAAkD;AAC9C,qBAAKuD,cAAL;AACH;AACJ;AACJ,WATD,MASO;AACH;AACA,gBAAI,KAAKxD,gBAAL,IAAyB,KAAKG,kBAAlC,EAAsD;AAClD,mBAAKL,iBAAL,GAAyB,IAAzB;AACH,aAFD,MAEO;AACH,kBAAI,KAAKE,gBAAL,IAAyB,KAAKC,cAAlC,EAAkD;AAC9C,qBAAKwD,cAAL;AACH;AACJ;AACJ;;AAED,cAAI,KAAK/C,YAAL,CAAkByB,MAAlB,GAA2B,CAA/B,EAAkC;AAC9B,iBAAK,IAAIa,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKtC,YAAL,CAAkByB,MAAtC,EAA8Ca,CAAC,EAA/C,EAAmD;AAC/C,mBAAKtC,YAAL,CAAkBsC,CAAlB,EAAqBK,IAArB,CAA0BC,eAA1B;AACH;AACJ;AACJ;;AAEOE,QAAAA,cAAc,GAAS;AAC3B,eAAKE,oBAAL,CAA0B,KAAKtD,WAAL,GAAmB,KAAKC,WAAL,CAAiB8B,MAA9D;AACA,eAAKlC,cAAL,GAAsB,KAAKD,gBAAL,GAAwB,KAAKiB,QAAL,CAAc0C,aAAd,CAA4Bd,IAA5B,EAA9C;AACH;;AAEOa,QAAAA,oBAAoB,CAACE,KAAD,EAAsB;AAC9C,cAAIA,KAAK,IAAI,KAAKvD,WAAL,CAAiB8B,MAA9B,EAAsC;AAClC;AACH;;AAED,cAAI0B,WAAW,GAAG,KAAK5C,QAAL,CAAc6C,cAAhC;AACA,cAAIC,UAAU,GAAG,KAAK9C,QAAL,CAAc8C,UAAd,CAAyBlB,IAAzB,EAAjB,CAN8C,CAO9C;;AAEA,eAAKmB,WAAL,CAAiB,KAAK3D,WAAL,CAAiBuD,KAAjB,CAAjB,EAA0CC,WAA1C,EAAuDE,UAAvD;AACH;;AAEON,QAAAA,cAAc,GAAS;AAC3B,eAAKxD,cAAL,GAAsB,KAAKD,gBAAL,GAAwB,KAAKiB,QAAL,CAAc0C,aAAd,CAA4Bd,IAA5B,EAA9C;AAEA,cAAIgB,WAAW,GAAG,KAAK5C,QAAL,CAAc6C,cAAhC;AACA,cAAIC,UAAU,GAAG,KAAK9C,QAAL,CAAc8C,UAAd,CAAyBlB,IAAzB,EAAjB;;AAEA,cAAI,KAAK5B,QAAL,CAAcC,UAAd,KAA6B;AAAA;AAAA,0CAAYC,MAA7C,EAAqD;AACjD,kBAAM8B,YAAY,GAAGvD,IAAI,CAAC0C,MAAL,KAAgB,KAAKlC,YAA1C;;AACA,iBAAK,MAAMoB,KAAX,IAAoB,KAAKL,QAAL,CAAcG,WAAlC,EAA+C;AAC3C,kBAAI6B,YAAY,IAAI3B,KAAK,CAACE,UAA1B,EAAsC;AAClC,qBAAKwC,WAAL,CAAiB1C,KAAK,CAAC4B,OAAvB,EAAgCW,WAAhC,EAA6CE,UAA7C;AACA;AACH;AACJ;AACJ,WARD,MAQO;AACH,iBAAKC,WAAL,CAAiB,KAAK/C,QAAL,CAAcG,WAAd,CAA0B,KAAKhB,WAAL,GAAmB,KAAKa,QAAL,CAAcG,WAAd,CAA0Be,MAAvE,EAA+Ee,OAAhG,EAAyGW,WAAzG,EAAsHE,UAAtH;AACH;AACJ;;AAEOC,QAAAA,WAAW,CAACC,OAAD,EAAkBC,MAAlB,EAAgCC,KAAhC,EAA+C;AAC9D,cAAIC,MAAY,GAAG,IAAIxF,IAAJ,CAAS,KAAK0B,QAAd,EAAwB,KAAKC,QAA7B,CAAnB;;AACA,cAAI,KAAKE,KAAT,EAAgB;AACZ;AACA2D,YAAAA,MAAM,GAAG,KAAK3D,KAAL,CAAW4D,MAAX,CAAkB,KAAK5D,KAAL,CAAW6D,QAA7B,EAAuCC,QAAhD;AACH,WAL6D,CAO9D;;;AACA,cAAI,KAAK/D,eAAT,EAA0B;AACtB,kBAAMgE,KAAK,GAAG,KAAKhE,eAAL,CAAqB6D,MAArB,CAA4B,KAAKjE,WAAL,GAAmB,KAAKI,eAAL,CAAqB6D,MAArB,CAA4BlC,MAA3E,CAAd;AACA+B,YAAAA,MAAM,CAAC1B,CAAP,IAAYgC,KAAK,CAAChC,CAAlB;AACA0B,YAAAA,MAAM,CAACzB,CAAP,IAAY+B,KAAK,CAAC/B,CAAlB;AACH;;AAED2B,UAAAA,MAAM,CAAC5B,CAAP,IAAY0B,MAAM,CAAC1B,CAAnB;AACA4B,UAAAA,MAAM,CAAC3B,CAAP,IAAYyB,MAAM,CAACzB,CAAnB;AAEA,eAAKrC,WAAL;;AACA,cAAI,KAAKS,oBAAT,EAA+B;AAC3B,iBAAKA,oBAAL,CAA0BoD,OAA1B,EAAmCG,MAAnC,EAA2CD,KAA3C;;AACA;AACH,WArB6D,CAuB9D;;;AACA,cAAIM,KAAK,GAAG;AAAA;AAAA,kCAAQC,YAAR,CAAqBC,QAArB,CAA8BV,OAA9B,CAAZ;;AACA,cAAIQ,KAAJ,EAAW;AACP,gBAAI,KAAKhE,KAAT,EAAgB;AACZ;AACAgE,cAAAA,KAAK,CAACG,QAAN,CAAeV,MAAM,CAAC1B,CAAtB,EAAyB0B,MAAM,CAACzB,CAAhC,EAAmC,KAAKhC,KAAxC;AACH,aAHD,MAGO;AACHgE,cAAAA,KAAK,CAACI,QAAN,CAAeT,MAAM,CAAC5B,CAAtB,EAAyB4B,MAAM,CAAC3B,CAAhC,EAAmC0B,KAAnC;AACH;;AACDM,YAAAA,KAAK,CAACK,kBAAN,CAAyB;AAAA;AAAA,kDAAeC,GAAxC,EAA6C,KAAKC,UAAL,CAAgBC,IAAhB,CAAqB,IAArB,CAA7C;;AACA,iBAAKrE,aAAL,CAAmBe,IAAnB,CAAwB8C,KAAxB,EARO,CAQyB;;AACnC;AACJ;;AAEOO,QAAAA,UAAU,CAACE,KAAD,EAAoB;AAClCA,UAAAA,KAAK,CAACC,oBAAN,CAA2B;AAAA;AAAA,gDAAeJ,GAA1C,EAA+C,KAAKC,UAAL,CAAgBC,IAAhB,CAAqB,IAArB,CAA/C;;AACA,gBAAMrB,KAAK,GAAG,KAAKhD,aAAL,CAAmBwE,OAAnB,CAA2BF,KAA3B,CAAd;;AACA,cAAItB,KAAK,KAAK,CAAC,CAAf,EAAkB;AACd,iBAAKhD,aAAL,CAAmByE,MAAnB,CAA0BzB,KAA1B,EAAiC,CAAjC;;AAEA,gBAAI,KAAKhD,aAAL,CAAmBuB,MAAnB,KAA8B,CAA9B,IAAmC,KAAKrC,iBAA5C,EAA+D;AAC3D,mBAAKC,eAAL,GAAuB,IAAvB;AACH;AACJ;AACJ;;AAIMuF,QAAAA,sBAAsB,CAACC,IAAD,EAA4D;AACrF,eAAK1E,oBAAL,GAA4B0E,IAA5B;AACH;;AAxQ+B,O;;;;;iBAEb,E;;;;;;;iBAGW;AAAA;AAAA,qC", "sourcesContent": ["import { _decorator, CCBoolean, CCFloat, CCInteger, CCString, Component, Vec2 } from 'cc';\r\nimport { WaveData, FormationGroup, FormationPoint, eSpawnOrder, eWaveCompletion } from '../data/WaveData';\r\nimport { PathData } from '../data/PathData';\r\nimport { GameIns } from 'db://assets/bundles/common/script/game/GameIns';\r\nimport { WaveEventGroup, WaveEventGroupContext } from './WaveEventGroup';\r\nimport EnemyPlane from 'db://assets/bundles/common/script/game/ui/plane/enemy/EnemyPlane';\r\nimport PlaneBase from 'db://assets/bundles/common/script/game/ui/plane/PlaneBase';\r\nimport { PlaneEventType } from 'db://assets/bundles/common/script/game/ui/plane/event/PlaneEventType';\r\nconst { ccclass, property, executeInEditMode, menu } = _decorator;\r\n\r\n@ccclass('Wave')\r\n@menu(\"怪物/波次\")\r\n@executeInEditMode()\r\nexport class Wave extends Component {\r\n    @property({displayName: '名称', editorOnly: true})\r\n    waveName: string = '';                // 备注(策划用)\r\n\r\n    @property({type:WaveData})\r\n    readonly waveData: WaveData = new WaveData();\r\n\r\n    /*\r\n     * 以下是新的wave逻辑, 旧的逻辑主要在WaveManager与EnemyWave\r\n     */\r\n    private _isSpawnCompleted: boolean = false;\r\n    public get isSpawnCompleted() { return this._isSpawnCompleted; }\r\n    private _isAllEnemyDead: boolean = false;\r\n    public get isAllEnemyDead() { return this._isAllEnemyDead; }\r\n    \r\n    private _waveElapsedTime: number = 0;\r\n    private _nextSpawnTime: number = 0;\r\n    private _totalWeight: number = 0;\r\n    // 这个参数可能是时间，也可能是数量，取决于waveData.waveCompletion\r\n    private _waveCompleteParam: number = 0;\r\n    private _spawnIndex: number = 0;\r\n\r\n    // 用在waveCompletion == SpawnCount时的队列\r\n    private _spawnQueue: number[] = [];\r\n    // 当前wave的起点位置\r\n    private _originX: number = 0;\r\n    private _originY: number = 0;\r\n    private _formationGroup: FormationGroup|null = null;\r\n    private _path: PathData|null = null;\r\n    // 事件组\r\n    private _eventGroups: WaveEventGroup[] = [];\r\n    private _eventGroupContext: WaveEventGroupContext|null = null;\r\n    // 怪物\r\n    private _enemyCreated: EnemyPlane[] = [];\r\n\r\n    onLoad() {\r\n        if (this.waveData) {\r\n            if (this.waveData.spawnOrder === eSpawnOrder.Random) {\r\n                this._totalWeight = 0;\r\n                // add up _totalWeight if is random\r\n                this.waveData.spawnGroups.forEach((group) => {\r\n                    this._totalWeight += group.weight;\r\n                    group.selfWeight = this._totalWeight;\r\n                });\r\n            }\r\n            \r\n            if (this.waveData.eventGroupData) {\r\n                if (!this._eventGroupContext) {\r\n                    this._eventGroupContext = new WaveEventGroupContext();\r\n                }\r\n                this.waveData.eventGroupData.forEach((groupData) => {\r\n                    const group = new WaveEventGroup(this._eventGroupContext!, groupData);\r\n                    this._eventGroups.push(group);\r\n                });\r\n            }\r\n\r\n            if (this.waveData.formationAsset) {\r\n                this._formationGroup = new FormationGroup();\r\n                Object.assign(this._formationGroup, this.waveData.formationAsset.json);\r\n            }\r\n\r\n            if (this.waveData.pathAsset) {\r\n                this._path = PathData.fromJSON(this.waveData.pathAsset.json);\r\n            }\r\n        }\r\n    }\r\n\r\n    private reset() {\r\n        this._isSpawnCompleted = false;\r\n        this._isAllEnemyDead = false;\r\n        this._waveElapsedTime = 0;\r\n        this._nextSpawnTime = 0;\r\n        this._spawnIndex = 0;\r\n        this._spawnQueue.length = 0;\r\n        this._eventGroupContext && (this._eventGroupContext!.reset());\r\n        \r\n        this._eventGroups.forEach((group) => {\r\n            group.reset();\r\n        });\r\n\r\n        if (this._enemyCreated.length > 0) {\r\n            this._enemyCreated.length = 0;\r\n        }\r\n    }\r\n\r\n    static random(): number {\r\n        if (GameIns.battleManager) {\r\n            return GameIns.battleManager.random();\r\n        }\r\n\r\n        return Math.random();\r\n    }\r\n\r\n    trigger(x: number, y: number) {\r\n        this.reset();\r\n        this._originX = x;\r\n        this._originY = y;\r\n\r\n        console.log('wave triggered at x: ' + x + ', y: ' + y);\r\n\r\n        // 对于固定数量的波次，可以预先生成队列，每次从里面取即可\r\n        if (this.waveData) {\r\n            this._waveCompleteParam = this.waveData.waveCompletionParam.eval();\r\n            if (this.waveData.waveCompletion === eWaveCompletion.SpawnCount) {\r\n                if (this.waveData.spawnOrder === eSpawnOrder.Random) {\r\n                    for (let i = 0; i < this._waveCompleteParam; i++) {\r\n                        const randomWeight = Wave.random() * this._totalWeight;\r\n                        for (const group of this.waveData.spawnGroups) {\r\n                            if (randomWeight <= group.selfWeight) {\r\n                                this._spawnQueue.push(group.planeID);\r\n                                break;\r\n                            }\r\n                        }\r\n                    }\r\n                } else {\r\n                    for (let i = 0; i < this._waveCompleteParam; i++) {\r\n                        // 通过取余实现循环\r\n                        this._spawnQueue.push(this.waveData.spawnGroups[i % this.waveData.spawnGroups.length].planeID);\r\n                    }\r\n                }\r\n            }\r\n\r\n            // 编辑器下预览这里可能为空\r\n            if (!this._formationGroup && this.waveData.formationAsset) {\r\n                this._formationGroup = new FormationGroup();\r\n                Object.assign(this._formationGroup, this.waveData.formationAsset.json);\r\n            }\r\n\r\n            if (!this._path && this.waveData.pathAsset) {\r\n                this._path = PathData.fromJSON(this.waveData.pathAsset.json);\r\n            }\r\n        }\r\n\r\n        this._eventGroupContext && (this._eventGroupContext!.wave = this);\r\n        this._eventGroups.forEach((group) => {\r\n            group.tryStart();\r\n        });\r\n    }\r\n\r\n    // tick wave\r\n    tick(dtInMiliseconds: number) {\r\n        if (!this._isSpawnCompleted) {\r\n            this.tickSpawn(dtInMiliseconds);\r\n        }\r\n    }\r\n    \r\n    private tickSpawn(dtInMiliseconds: number) {\r\n        this._waveElapsedTime += dtInMiliseconds;\r\n        if (this.waveData.waveCompletion === eWaveCompletion.SpawnCount) {\r\n            // 产出固定数量的波次\r\n            if (this._spawnIndex >= this._waveCompleteParam) {\r\n                this._isSpawnCompleted = true;\r\n            } else {\r\n                if (this._waveElapsedTime >= this._nextSpawnTime) {\r\n                    this.spawnFromQueue();\r\n                }\r\n            }\r\n        } else {\r\n            // 完全根据时间的波次\r\n            if (this._waveElapsedTime >= this._waveCompleteParam) {\r\n                this._isSpawnCompleted = true;\r\n            } else {\r\n                if (this._waveElapsedTime >= this._nextSpawnTime) {\r\n                    this.spawnFromGroup();\r\n                }\r\n            }\r\n        }\r\n\r\n        if (this._eventGroups.length > 0) {\r\n            for (let i = 0; i < this._eventGroups.length; i++) {\r\n                this._eventGroups[i].tick(dtInMiliseconds);\r\n            }\r\n        }\r\n    }\r\n\r\n    private spawnFromQueue(): void {        \r\n        this.spawnSingleFromQueue(this._spawnIndex % this._spawnQueue.length);\r\n        this._nextSpawnTime = this._waveElapsedTime + this.waveData.spawnInterval.eval();\r\n    }\r\n\r\n    private spawnSingleFromQueue(index: number): void {\r\n        if (index >= this._spawnQueue.length) {\r\n            return;\r\n        }\r\n\r\n        let spawnOffset = this.waveData.spawnPosOffset;\r\n        let spawnAngle = this.waveData.spawnAngle.eval();\r\n        // let spawnSpeed = this.waveData.spawnSpeed.eval();\r\n\r\n        this.createPlane(this._spawnQueue[index], spawnOffset, spawnAngle);\r\n    }\r\n\r\n    private spawnFromGroup(): void {\r\n        this._nextSpawnTime = this._waveElapsedTime + this.waveData.spawnInterval.eval();\r\n\r\n        let spawnOffset = this.waveData.spawnPosOffset;\r\n        let spawnAngle = this.waveData.spawnAngle.eval();\r\n\r\n        if (this.waveData.spawnOrder === eSpawnOrder.Random) {\r\n            const randomWeight = Wave.random() * this._totalWeight;\r\n            for (const group of this.waveData.spawnGroups) {\r\n                if (randomWeight <= group.selfWeight) {\r\n                    this.createPlane(group.planeID, spawnOffset, spawnAngle);\r\n                    break;\r\n                }\r\n            }\r\n        } else {\r\n            this.createPlane(this.waveData.spawnGroups[this._spawnIndex % this.waveData.spawnGroups.length].planeID, spawnOffset, spawnAngle);\r\n        }\r\n    }\r\n\r\n    private createPlane(planeId: number, offset: Vec2, angle: number) {\r\n        let origin: Vec2 = new Vec2(this._originX, this._originY);\r\n        if (this._path) {\r\n            // 如果有路径的情况下, 选择路径起点作为原点\r\n            origin = this._path.points[this._path.startIdx].position;\r\n        }\r\n\r\n        // 获取阵型数据\r\n        if (this._formationGroup) {\r\n            const point = this._formationGroup.points[this._spawnIndex % this._formationGroup.points.length];\r\n            offset.x += point.x;\r\n            offset.y += point.y;\r\n        }\r\n\r\n        origin.x += offset.x;\r\n        origin.y += offset.y;\r\n\r\n        this._spawnIndex++;\r\n        if (this._createPlaneDelegate) {\r\n            this._createPlaneDelegate(planeId, origin, angle);\r\n            return;\r\n        }\r\n\r\n        // console.log(`createPlane: ${planeId}, ${offset.x}, ${offset.y}, ${angle}`);\r\n        let enemy = GameIns.enemyManager.addPlane(planeId);\r\n        if (enemy) {\r\n            if (this._path) {\r\n                // 注意: Path已经包含了origin, 因此这里我们只传入origin\r\n                enemy.initPath(offset.x, offset.y, this._path);\r\n            } else {\r\n                enemy.initMove(origin.x, origin.y, angle);\r\n            }\r\n            enemy.PlaneEventRegister(PlaneEventType.Die, this.onEnemyDie.bind(this));\r\n            this._enemyCreated.push(enemy); // 记录下来，方便后面判断是否都死了\r\n        }\r\n    }\r\n\r\n    private onEnemyDie(plane: EnemyPlane) {\r\n        plane.PlaneEventUnRegister(PlaneEventType.Die, this.onEnemyDie.bind(this));\r\n        const index = this._enemyCreated.indexOf(plane);\r\n        if (index !== -1) {\r\n            this._enemyCreated.splice(index, 1);\r\n\r\n            if (this._enemyCreated.length === 0 && this._isSpawnCompleted) {\r\n                this._isAllEnemyDead = true;\r\n            }\r\n        }\r\n    }\r\n\r\n    // 以下几个函数是为了给编辑器预览用\r\n    private _createPlaneDelegate: ((planeId: number, pos: Vec2, angle: number) => void)|null = null;\r\n    public setCreatePlaneDelegate(func: (planeId: number, pos: Vec2, angle: number) => void) {\r\n        this._createPlaneDelegate = func;\r\n    }\r\n}"]}