{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/wave/Wave.ts"], "names": ["_decorator", "Component", "WaveData", "FormationGroup", "eSpawnOrder", "eWaveCompletion", "GameIns", "WaveEventGroup", "WaveEventGroupContext", "PlaneEventType", "ccclass", "property", "executeInEditMode", "menu", "Wave", "displayName", "editor<PERSON><PERSON><PERSON>", "type", "_isSpawnCompleted", "_isAllEnemyDead", "_waveElapsedTime", "_nextSpawnTime", "_totalWeight", "_waveCompleteParam", "_spawnIndex", "_spawnQueue", "_offsetX", "_offsetY", "_formationGroup", "_eventGroups", "_eventGroupContext", "_enemyCreated", "_createPlaneDelegate", "isSpawnCompleted", "isAllEnemyDead", "onLoad", "waveData", "spawnOrder", "Random", "spawnGroups", "for<PERSON>ach", "group", "weight", "selfWeight", "eventGroupData", "groupData", "push", "formationAsset", "Object", "assign", "json", "reset", "length", "trigger", "x", "y", "waveCompletionParam", "eval", "waveCompletion", "SpawnCount", "i", "randomWeight", "battleManager", "random", "planeID", "wave", "tryStart", "tick", "dtInMiliseconds", "tickSpawn", "spawnFromQueue", "spawnFromGroup", "spawnSingleFromQueue", "spawnInterval", "index", "spawnPos", "spawnAngle", "createPlane", "planeId", "pos", "angle", "enemy", "enemyManager", "addPlane", "point", "points", "setPos", "initMove", "PlaneEventRegister", "Die", "onEnemyDie", "bind", "plane", "PlaneEventUnRegister", "indexOf", "splice", "setCreatePlaneDelegate", "func"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAqDC,MAAAA,S,OAAAA,S;;AACrDC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,c,iBAAAA,c;AAAgCC,MAAAA,W,iBAAAA,W;AAAaC,MAAAA,e,iBAAAA,e;;AACvDC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,c,iBAAAA,c;AAAgBC,MAAAA,qB,iBAAAA,qB;;AAGhBC,MAAAA,c,iBAAAA,c;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA,iBAArB;AAAwCC,QAAAA;AAAxC,O,GAAiDb,U;;sBAK1Cc,I,WAHZJ,OAAO,CAAC,MAAD,C,UACPG,IAAI,CAAC,OAAD,C,UACJD,iBAAiB,E,UAEbD,QAAQ,CAAC;AAACI,QAAAA,WAAW,EAAE,IAAd;AAAoBC,QAAAA,UAAU,EAAE;AAAhC,OAAD,C,UAGRL,QAAQ,CAAC;AAACM,QAAAA,IAAI;AAAA;AAAA;AAAL,OAAD,C,yDAPb,MAGaH,IAHb,SAG0Bb,SAH1B,CAGoC;AAAA;AAAA;;AAAA;;AAEM;AAFN;;AAOhC;AACJ;AACA;AAToC,eAUxBiB,iBAVwB,GAUK,KAVL;AAAA,eAYxBC,eAZwB,GAYG,KAZH;AAAA,eAexBC,gBAfwB,GAeG,CAfH;AAAA,eAgBxBC,cAhBwB,GAgBC,CAhBD;AAAA,eAiBxBC,YAjBwB,GAiBD,CAjBC;AAkBhC;AAlBgC,eAmBxBC,kBAnBwB,GAmBK,CAnBL;AAAA,eAoBxBC,WApBwB,GAoBF,CApBE;AAsBhC;AAtBgC,eAuBxBC,WAvBwB,GAuBA,EAvBA;AAwBhC;AAxBgC,eAyBxBC,QAzBwB,GAyBL,CAzBK;AAAA,eA0BxBC,QA1BwB,GA0BL,CA1BK;AAAA,eA2BxBC,eA3BwB,GA2Be,IA3Bf;AA4BhC;AA5BgC,eA6BxBC,YA7BwB,GA6BS,EA7BT;AAAA,eA8BxBC,kBA9BwB,GA8ByB,IA9BzB;AA+BhC;AA/BgC,eAgCxBC,aAhCwB,GAgCM,EAhCN;AAmOhC;AAnOgC,eAoOxBC,oBApOwB,GAoO2D,IApO3D;AAAA;;AAWL,YAAhBC,gBAAgB,GAAG;AAAE,iBAAO,KAAKf,iBAAZ;AAAgC;;AAEvC,YAAdgB,cAAc,GAAG;AAAE,iBAAO,KAAKf,eAAZ;AAA8B;;AAqB5DgB,QAAAA,MAAM,GAAG;AACL,cAAI,KAAKC,QAAT,EAAmB;AACf,gBAAI,KAAKA,QAAL,CAAcC,UAAd,KAA6B;AAAA;AAAA,4CAAYC,MAA7C,EAAqD;AACjD,mBAAKhB,YAAL,GAAoB,CAApB,CADiD,CAEjD;;AACA,mBAAKc,QAAL,CAAcG,WAAd,CAA0BC,OAA1B,CAAmCC,KAAD,IAAW;AACzC,qBAAKnB,YAAL,IAAqBmB,KAAK,CAACC,MAA3B;AACAD,gBAAAA,KAAK,CAACE,UAAN,GAAmB,KAAKrB,YAAxB;AACH,eAHD;AAIH;;AAED,gBAAI,KAAKc,QAAL,CAAcQ,cAAlB,EAAkC;AAC9B,kBAAI,CAAC,KAAKd,kBAAV,EAA8B;AAC1B,qBAAKA,kBAAL,GAA0B;AAAA;AAAA,qEAA1B;AACH;;AACD,mBAAKM,QAAL,CAAcQ,cAAd,CAA6BJ,OAA7B,CAAsCK,SAAD,IAAe;AAChD,sBAAMJ,KAAK,GAAG;AAAA;AAAA,sDAAmB,KAAKX,kBAAxB,EAA6Ce,SAA7C,CAAd;;AACA,qBAAKhB,YAAL,CAAkBiB,IAAlB,CAAuBL,KAAvB;AACH,eAHD;AAIH;;AAED,gBAAI,KAAKL,QAAL,CAAcW,cAAlB,EAAkC;AAC9B,mBAAKnB,eAAL,GAAuB;AAAA;AAAA,qDAAvB;AACAoB,cAAAA,MAAM,CAACC,MAAP,CAAc,KAAKrB,eAAnB,EAAoC,KAAKQ,QAAL,CAAcW,cAAd,CAA6BG,IAAjE;AACH;AACJ;AACJ;;AAEOC,QAAAA,KAAK,GAAG;AACZ,eAAKjC,iBAAL,GAAyB,KAAzB;AACA,eAAKC,eAAL,GAAuB,KAAvB;AACA,eAAKC,gBAAL,GAAwB,CAAxB;AACA,eAAKC,cAAL,GAAsB,CAAtB;AACA,eAAKG,WAAL,GAAmB,CAAnB;AACA,eAAKC,WAAL,CAAiB2B,MAAjB,GAA0B,CAA1B;AACA,eAAKtB,kBAAL,IAA4B,KAAKA,kBAAL,CAAyBqB,KAAzB,EAA5B;;AAEA,eAAKtB,YAAL,CAAkBW,OAAlB,CAA2BC,KAAD,IAAW;AACjCA,YAAAA,KAAK,CAACU,KAAN;AACH,WAFD;;AAIA,cAAI,KAAKpB,aAAL,CAAmBqB,MAAnB,GAA4B,CAAhC,EAAmC;AAC/B,iBAAKrB,aAAL,CAAmBqB,MAAnB,GAA4B,CAA5B;AACH;AACJ;;AAEDC,QAAAA,OAAO,CAACC,CAAD,EAAYC,CAAZ,EAAuB;AAC1B,eAAKJ,KAAL;AACA,eAAKzB,QAAL,GAAgB4B,CAAhB;AACA,eAAK3B,QAAL,GAAgB4B,CAAhB,CAH0B,CAK1B;;AACA,cAAI,KAAKnB,QAAT,EAAmB;AACf,iBAAKb,kBAAL,GAA0B,KAAKa,QAAL,CAAcoB,mBAAd,CAAkCC,IAAlC,EAA1B;;AACA,gBAAI,KAAKrB,QAAL,CAAcsB,cAAd,KAAiC;AAAA;AAAA,oDAAgBC,UAArD,EAAiE;AAC7D,kBAAI,KAAKvB,QAAL,CAAcC,UAAd,KAA6B;AAAA;AAAA,8CAAYC,MAA7C,EAAqD;AACjD,qBAAK,IAAIsB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKrC,kBAAzB,EAA6CqC,CAAC,EAA9C,EAAkD;AAC9C,wBAAMC,YAAY,GAAG;AAAA;AAAA,0CAAQC,aAAR,CAAsBC,MAAtB,KAAiC,KAAKzC,YAA3D;;AACA,uBAAK,MAAMmB,KAAX,IAAoB,KAAKL,QAAL,CAAcG,WAAlC,EAA+C;AAC3C,wBAAIsB,YAAY,IAAIpB,KAAK,CAACE,UAA1B,EAAsC;AAClC,2BAAKlB,WAAL,CAAiBqB,IAAjB,CAAsBL,KAAK,CAACuB,OAA5B;;AACA;AACH;AACJ;AACJ;AACJ,eAVD,MAUO;AACH,qBAAK,IAAIJ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKrC,kBAAzB,EAA6CqC,CAAC,EAA9C,EAAkD;AAC9C;AACA,uBAAKnC,WAAL,CAAiBqB,IAAjB,CAAsB,KAAKV,QAAL,CAAcG,WAAd,CAA0BqB,CAAC,GAAG,KAAKxB,QAAL,CAAcG,WAAd,CAA0Ba,MAAxD,EAAgEY,OAAtF;AACH;AACJ;AACJ;AACJ;;AAED,eAAKlC,kBAAL,KAA4B,KAAKA,kBAAL,CAAyBmC,IAAzB,GAAgC,IAA5D;;AACA,eAAKpC,YAAL,CAAkBW,OAAlB,CAA2BC,KAAD,IAAW;AACjCA,YAAAA,KAAK,CAACyB,QAAN;AACH,WAFD;AAGH,SAhH+B,CAkHhC;;;AACAC,QAAAA,IAAI,CAACC,eAAD,EAA0B;AAC1B,cAAI,CAAC,KAAKlD,iBAAV,EAA6B;AACzB,iBAAKmD,SAAL,CAAeD,eAAf;AACH;AACJ;;AAEOC,QAAAA,SAAS,CAACD,eAAD,EAA0B;AACvC,eAAKhD,gBAAL,IAAyBgD,eAAzB;;AACA,cAAI,KAAKhC,QAAL,CAAcsB,cAAd,KAAiC;AAAA;AAAA,kDAAgBC,UAArD,EAAiE;AAC7D;AACA,gBAAI,KAAKnC,WAAL,IAAoB,KAAKD,kBAA7B,EAAiD;AAC7C,mBAAKL,iBAAL,GAAyB,IAAzB;AACH,aAFD,MAEO;AACH,kBAAI,KAAKE,gBAAL,IAAyB,KAAKC,cAAlC,EAAkD;AAC9C,qBAAKiD,cAAL;AACH;AACJ;AACJ,WATD,MASO;AACH;AACA,gBAAI,KAAKlD,gBAAL,IAAyB,KAAKG,kBAAlC,EAAsD;AAClD,mBAAKL,iBAAL,GAAyB,IAAzB;AACH,aAFD,MAEO;AACH,kBAAI,KAAKE,gBAAL,IAAyB,KAAKC,cAAlC,EAAkD;AAC9C,qBAAKkD,cAAL;AACH;AACJ;AACJ;;AAED,cAAI,KAAK1C,YAAL,CAAkBuB,MAAlB,GAA2B,CAA/B,EAAkC;AAC9B,iBAAK,IAAIQ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK/B,YAAL,CAAkBuB,MAAtC,EAA8CQ,CAAC,EAA/C,EAAmD;AAC/C,mBAAK/B,YAAL,CAAkB+B,CAAlB,EAAqBO,IAArB,CAA0BC,eAA1B;AACH;AACJ;AACJ;;AAEOE,QAAAA,cAAc,GAAS;AAC3B,eAAKE,oBAAL,CAA0B,KAAKhD,WAAL,GAAmB,KAAKC,WAAL,CAAiB2B,MAA9D;AACA,eAAK/B,cAAL,GAAsB,KAAKD,gBAAL,GAAwB,KAAKgB,QAAL,CAAcqC,aAAd,CAA4BhB,IAA5B,EAA9C;AACH;;AAEOe,QAAAA,oBAAoB,CAACE,KAAD,EAAsB;AAC9C,cAAIA,KAAK,IAAI,KAAKjD,WAAL,CAAiB2B,MAA9B,EAAsC;AAClC;AACH;;AAED,cAAIuB,QAAQ,GAAG,KAAKvC,QAAL,CAAcuC,QAA7B;AACA,cAAIC,UAAU,GAAG,KAAKxC,QAAL,CAAcwC,UAAd,CAAyBnB,IAAzB,EAAjB,CAN8C,CAO9C;;AAEA,eAAKoB,WAAL,CAAiB,KAAKpD,WAAL,CAAiBiD,KAAjB,CAAjB,EAA0CC,QAA1C,EAAoDC,UAApD;AACH;;AAEOL,QAAAA,cAAc,GAAS;AAC3B,eAAKlD,cAAL,GAAsB,KAAKD,gBAAL,GAAwB,KAAKgB,QAAL,CAAcqC,aAAd,CAA4BhB,IAA5B,EAA9C;AAEA,cAAIkB,QAAQ,GAAG,KAAKvC,QAAL,CAAcuC,QAA7B;AACA,cAAIC,UAAU,GAAG,KAAKxC,QAAL,CAAcwC,UAAd,CAAyBnB,IAAzB,EAAjB;;AAEA,cAAI,KAAKrB,QAAL,CAAcC,UAAd,KAA6B;AAAA;AAAA,0CAAYC,MAA7C,EAAqD;AACjD,kBAAMuB,YAAY,GAAG;AAAA;AAAA,oCAAQC,aAAR,CAAsBC,MAAtB,KAAiC,KAAKzC,YAA3D;;AACA,iBAAK,MAAMmB,KAAX,IAAoB,KAAKL,QAAL,CAAcG,WAAlC,EAA+C;AAC3C,kBAAIsB,YAAY,IAAIpB,KAAK,CAACE,UAA1B,EAAsC;AAClC,qBAAKkC,WAAL,CAAiBpC,KAAK,CAACuB,OAAvB,EAAgCW,QAAhC,EAA0CC,UAA1C;AACA;AACH;AACJ;AACJ,WARD,MAQO;AACH,iBAAKC,WAAL,CAAiB,KAAKzC,QAAL,CAAcG,WAAd,CAA0B,KAAKf,WAAL,GAAmB,KAAKY,QAAL,CAAcG,WAAd,CAA0Ba,MAAvE,EAA+EY,OAAhG,EAAyGW,QAAzG,EAAmHC,UAAnH;AACH;AACJ;;AAEOC,QAAAA,WAAW,CAACC,OAAD,EAAkBC,GAAlB,EAA6BC,KAA7B,EAA4C;AAC3DD,UAAAA,GAAG,CAACzB,CAAJ,IAAS,KAAK5B,QAAd;AACAqD,UAAAA,GAAG,CAACxB,CAAJ,IAAS,KAAK5B,QAAd;;AAEA,cAAI,KAAKK,oBAAT,EAA+B;AAC3B,iBAAKA,oBAAL,CAA0B8C,OAA1B,EAAmCC,GAAnC,EAAwCC,KAAxC;;AACA,iBAAKxD,WAAL;AACA;AACH;;AAED,cAAIyD,KAAK,GAAG;AAAA;AAAA,kCAAQC,YAAR,CAAqBC,QAArB,CAA8BL,OAA9B,CAAZ;;AACA,cAAIG,KAAJ,EAAW;AACP;AACA,gBAAI,KAAKrD,eAAT,EAA0B;AACtB,oBAAMwD,KAAK,GAAG,KAAKxD,eAAL,CAAqByD,MAArB,CAA4B,KAAK7D,WAAL,GAAmB,KAAKI,eAAL,CAAqByD,MAArB,CAA4BjC,MAA3E,CAAd;AACA2B,cAAAA,GAAG,CAACzB,CAAJ,IAAS8B,KAAK,CAAC9B,CAAf;AACAyB,cAAAA,GAAG,CAACxB,CAAJ,IAAS6B,KAAK,CAAC7B,CAAf;AACH,aANM,CAQP;;;AAEA0B,YAAAA,KAAK,CAACK,MAAN,CAAaP,GAAG,CAACzB,CAAjB,EAAoByB,GAAG,CAACxB,CAAxB;AACA0B,YAAAA,KAAK,CAACM,QAAN,CAAeP,KAAf;AACAC,YAAAA,KAAK,CAACO,kBAAN,CAAyB;AAAA;AAAA,kDAAeC,GAAxC,EAA6C,KAAKC,UAAL,CAAgBC,IAAhB,CAAqB,IAArB,CAA7C;;AACA,iBAAK5D,aAAL,CAAmBe,IAAnB,CAAwBmC,KAAxB,EAbO,CAayB;;AACnC;;AACD,eAAKzD,WAAL;AACH;;AAEOkE,QAAAA,UAAU,CAACE,KAAD,EAAoB;AAClCA,UAAAA,KAAK,CAACC,oBAAN,CAA2B;AAAA;AAAA,gDAAeJ,GAA1C,EAA+C,KAAKC,UAAL,CAAgBC,IAAhB,CAAqB,IAArB,CAA/C;;AACA,gBAAMjB,KAAK,GAAG,KAAK3C,aAAL,CAAmB+D,OAAnB,CAA2BF,KAA3B,CAAd;;AACA,cAAIlB,KAAK,KAAK,CAAC,CAAf,EAAkB;AACd,iBAAK3C,aAAL,CAAmBgE,MAAnB,CAA0BrB,KAA1B,EAAiC,CAAjC;;AAEA,gBAAI,KAAK3C,aAAL,CAAmBqB,MAAnB,KAA8B,CAA9B,IAAmC,KAAKlC,iBAA5C,EAA+D;AAC3D,mBAAKC,eAAL,GAAuB,IAAvB;AACH;AACJ;AACJ;;AAIM6E,QAAAA,sBAAsB,CAACC,IAAD,EAA4D;AACrF,eAAKjE,oBAAL,GAA4BiE,IAA5B;AACH;;AAvO+B,O;;;;;iBAEb,E;;;;;;;iBAGW;AAAA;AAAA,qC", "sourcesContent": ["import { _decorator, CCBoolean, CCFloat, CCInteger, CCString, Component, Vec2 } from 'cc';\r\nimport { WaveData, FormationGroup, FormationPoint, eSpawnOrder, eWaveCompletion } from '../data/WaveData';\r\nimport { GameIns } from 'db://assets/bundles/common/script/game/GameIns';\r\nimport { WaveEventGroup, WaveEventGroupContext } from './WaveEventGroup';\r\nimport EnemyPlane from 'db://assets/bundles/common/script/game/ui/plane/enemy/EnemyPlane';\r\nimport PlaneBase from 'db://assets/bundles/common/script/game/ui/plane/PlaneBase';\r\nimport { PlaneEventType } from 'db://assets/bundles/common/script/game/ui/plane/event/PlaneEventType';\r\nconst { ccclass, property, executeInEditMode, menu } = _decorator;\r\n\r\n@ccclass('Wave')\r\n@menu(\"怪物/波次\")\r\n@executeInEditMode()\r\nexport class Wave extends Component {\r\n    @property({displayName: '名称', editorOnly: true})\r\n    waveName: string = '';                // 备注(策划用)\r\n\r\n    @property({type:WaveData})\r\n    readonly waveData: WaveData = new WaveData();\r\n\r\n    /*\r\n     * 以下是新的wave逻辑, 旧的逻辑主要在WaveManager与EnemyWave\r\n     */\r\n    private _isSpawnCompleted: boolean = false;\r\n    public get isSpawnCompleted() { return this._isSpawnCompleted; }\r\n    private _isAllEnemyDead: boolean = false;\r\n    public get isAllEnemyDead() { return this._isAllEnemyDead; }\r\n    \r\n    private _waveElapsedTime: number = 0;\r\n    private _nextSpawnTime: number = 0;\r\n    private _totalWeight: number = 0;\r\n    // 这个参数可能是时间，也可能是数量，取决于waveData.waveCompletion\r\n    private _waveCompleteParam: number = 0;\r\n    private _spawnIndex: number = 0;\r\n\r\n    // 用在waveCompletion == SpawnCount时的队列\r\n    private _spawnQueue: number[] = [];\r\n    // 当前wave的偏移位置\r\n    private _offsetX: number = 0;\r\n    private _offsetY: number = 0;\r\n    private _formationGroup: FormationGroup|null = null;\r\n    // 事件组\r\n    private _eventGroups: WaveEventGroup[] = [];\r\n    private _eventGroupContext: WaveEventGroupContext|null = null;\r\n    // 怪物\r\n    private _enemyCreated: EnemyPlane[] = [];\r\n\r\n    onLoad() {\r\n        if (this.waveData) {\r\n            if (this.waveData.spawnOrder === eSpawnOrder.Random) {\r\n                this._totalWeight = 0;\r\n                // add up _totalWeight if is random\r\n                this.waveData.spawnGroups.forEach((group) => {\r\n                    this._totalWeight += group.weight;\r\n                    group.selfWeight = this._totalWeight;\r\n                });\r\n            }\r\n            \r\n            if (this.waveData.eventGroupData) {\r\n                if (!this._eventGroupContext) {\r\n                    this._eventGroupContext = new WaveEventGroupContext();\r\n                }\r\n                this.waveData.eventGroupData.forEach((groupData) => {\r\n                    const group = new WaveEventGroup(this._eventGroupContext!, groupData);\r\n                    this._eventGroups.push(group);\r\n                });\r\n            }\r\n\r\n            if (this.waveData.formationAsset) {\r\n                this._formationGroup = new FormationGroup();\r\n                Object.assign(this._formationGroup, this.waveData.formationAsset.json);\r\n            }\r\n        }\r\n    }\r\n\r\n    private reset() {\r\n        this._isSpawnCompleted = false;\r\n        this._isAllEnemyDead = false;\r\n        this._waveElapsedTime = 0;\r\n        this._nextSpawnTime = 0;\r\n        this._spawnIndex = 0;\r\n        this._spawnQueue.length = 0;\r\n        this._eventGroupContext && (this._eventGroupContext!.reset());\r\n        \r\n        this._eventGroups.forEach((group) => {\r\n            group.reset();\r\n        });\r\n\r\n        if (this._enemyCreated.length > 0) {\r\n            this._enemyCreated.length = 0;\r\n        }\r\n    }\r\n\r\n    trigger(x: number, y: number) {\r\n        this.reset();\r\n        this._offsetX = x;\r\n        this._offsetY = y;\r\n\r\n        // 对于固定数量的波次，可以预先生成队列，每次从里面取即可\r\n        if (this.waveData) {\r\n            this._waveCompleteParam = this.waveData.waveCompletionParam.eval();\r\n            if (this.waveData.waveCompletion === eWaveCompletion.SpawnCount) {\r\n                if (this.waveData.spawnOrder === eSpawnOrder.Random) {\r\n                    for (let i = 0; i < this._waveCompleteParam; i++) {\r\n                        const randomWeight = GameIns.battleManager.random() * this._totalWeight;\r\n                        for (const group of this.waveData.spawnGroups) {\r\n                            if (randomWeight <= group.selfWeight) {\r\n                                this._spawnQueue.push(group.planeID);\r\n                                break;\r\n                            }\r\n                        }\r\n                    }\r\n                } else {\r\n                    for (let i = 0; i < this._waveCompleteParam; i++) {\r\n                        // 通过取余实现循环\r\n                        this._spawnQueue.push(this.waveData.spawnGroups[i % this.waveData.spawnGroups.length].planeID);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        this._eventGroupContext && (this._eventGroupContext!.wave = this);\r\n        this._eventGroups.forEach((group) => {\r\n            group.tryStart();\r\n        });\r\n    }\r\n\r\n    // tick wave\r\n    tick(dtInMiliseconds: number) {\r\n        if (!this._isSpawnCompleted) {\r\n            this.tickSpawn(dtInMiliseconds);\r\n        }\r\n    }\r\n    \r\n    private tickSpawn(dtInMiliseconds: number) {\r\n        this._waveElapsedTime += dtInMiliseconds;\r\n        if (this.waveData.waveCompletion === eWaveCompletion.SpawnCount) {\r\n            // 产出固定数量的波次\r\n            if (this._spawnIndex >= this._waveCompleteParam) {\r\n                this._isSpawnCompleted = true;\r\n            } else {\r\n                if (this._waveElapsedTime >= this._nextSpawnTime) {\r\n                    this.spawnFromQueue();\r\n                }\r\n            }\r\n        } else {\r\n            // 完全根据时间的波次\r\n            if (this._waveElapsedTime >= this._waveCompleteParam) {\r\n                this._isSpawnCompleted = true;\r\n            } else {\r\n                if (this._waveElapsedTime >= this._nextSpawnTime) {\r\n                    this.spawnFromGroup();\r\n                }\r\n            }\r\n        }\r\n\r\n        if (this._eventGroups.length > 0) {\r\n            for (let i = 0; i < this._eventGroups.length; i++) {\r\n                this._eventGroups[i].tick(dtInMiliseconds);\r\n            }\r\n        }\r\n    }\r\n\r\n    private spawnFromQueue(): void {        \r\n        this.spawnSingleFromQueue(this._spawnIndex % this._spawnQueue.length);\r\n        this._nextSpawnTime = this._waveElapsedTime + this.waveData.spawnInterval.eval();\r\n    }\r\n\r\n    private spawnSingleFromQueue(index: number): void {\r\n        if (index >= this._spawnQueue.length) {\r\n            return;\r\n        }\r\n\r\n        let spawnPos = this.waveData.spawnPos;\r\n        let spawnAngle = this.waveData.spawnAngle.eval();\r\n        // let spawnSpeed = this.waveData.spawnSpeed.eval();\r\n\r\n        this.createPlane(this._spawnQueue[index], spawnPos, spawnAngle);\r\n    }\r\n\r\n    private spawnFromGroup(): void {\r\n        this._nextSpawnTime = this._waveElapsedTime + this.waveData.spawnInterval.eval();\r\n\r\n        let spawnPos = this.waveData.spawnPos;\r\n        let spawnAngle = this.waveData.spawnAngle.eval();\r\n\r\n        if (this.waveData.spawnOrder === eSpawnOrder.Random) {\r\n            const randomWeight = GameIns.battleManager.random() * this._totalWeight;\r\n            for (const group of this.waveData.spawnGroups) {\r\n                if (randomWeight <= group.selfWeight) {\r\n                    this.createPlane(group.planeID, spawnPos, spawnAngle);\r\n                    break;\r\n                }\r\n            }\r\n        } else {\r\n            this.createPlane(this.waveData.spawnGroups[this._spawnIndex % this.waveData.spawnGroups.length].planeID, spawnPos, spawnAngle);\r\n        }\r\n    }\r\n\r\n    private createPlane(planeId: number, pos: Vec2, angle: number) {\r\n        pos.x += this._offsetX;\r\n        pos.y += this._offsetY;\r\n\r\n        if (this._createPlaneDelegate) {\r\n            this._createPlaneDelegate(planeId, pos, angle);\r\n            this._spawnIndex++;\r\n            return;\r\n        }\r\n\r\n        let enemy = GameIns.enemyManager.addPlane(planeId);\r\n        if (enemy) {\r\n            // 获取阵型数据\r\n            if (this._formationGroup) {\r\n                const point = this._formationGroup.points[this._spawnIndex % this._formationGroup.points.length];\r\n                pos.x += point.x;\r\n                pos.y += point.y;\r\n            }\r\n\r\n            // TODO: 路径数据\r\n\r\n            enemy.setPos(pos.x, pos.y);\r\n            enemy.initMove(angle);\r\n            enemy.PlaneEventRegister(PlaneEventType.Die, this.onEnemyDie.bind(this));\r\n            this._enemyCreated.push(enemy); // 记录下来，方便后面判断是否都死了\r\n        }\r\n        this._spawnIndex++;\r\n    }\r\n\r\n    private onEnemyDie(plane: EnemyPlane) {\r\n        plane.PlaneEventUnRegister(PlaneEventType.Die, this.onEnemyDie.bind(this));\r\n        const index = this._enemyCreated.indexOf(plane);\r\n        if (index !== -1) {\r\n            this._enemyCreated.splice(index, 1);\r\n\r\n            if (this._enemyCreated.length === 0 && this._isSpawnCompleted) {\r\n                this._isAllEnemyDead = true;\r\n            }\r\n        }\r\n    }\r\n\r\n    // 以下几个函数是为了给编辑器预览用\r\n    private _createPlaneDelegate: ((planeId: number, pos: Vec2, angle: number) => void)|null = null;\r\n    public setCreatePlaneDelegate(func: (planeId: number, pos: Vec2, angle: number) => void) {\r\n        this._createPlaneDelegate = func;\r\n    }\r\n}"]}