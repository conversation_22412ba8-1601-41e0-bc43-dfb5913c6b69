import { _decorator, Label, Node, Sprite, sys, Widget } from 'cc';
import { BaseUI, UILayer } from "db://assets/scripts/core/base/UIMgr";
import { BundleName } from '../../const/BundleConst';
import { EventMgr } from '../../event/EventManager';
import { MyApp } from '../../app/MyApp';
const { ccclass, property } = _decorator;

@ccclass('TopUI')
export class TopUI extends BaseUI {
    @property(Label)
    public static getUrl(): string { return "prefab/ui/TopUI"; }
    public static getLayer(): UILayer { return UILayer.Top }
    public static getBundleName(): string { return BundleName.Home }

    @property(Sprite)
    sp: Sprite | null = null;

    @property(Node)
    panel: Node | null = null;

    protected onLoad(): void {
        let notchHeight = MyApp.platformSDK.getStatusBarHeight();
        this.sp!.getComponent(Widget)!.isAlignTop = true;
        this.sp!.getComponent(Widget)!.top = notchHeight - 211;//211是sp的默认top
        this.panel!.getComponent(Widget)!.isAlignTop = true;
        this.panel!.getComponent(Widget)!.top = notchHeight;
    }

    async onShow(...args: any[]): Promise<void> {
    }
    async onHide(...args: any[]): Promise<void> {
    }
    async onClose(...args: any[]): Promise<void> {
    }
    protected update(dt: number): void {
    }
    protected onDestroy(): void {
        EventMgr.targetOff(this)
    }
}

