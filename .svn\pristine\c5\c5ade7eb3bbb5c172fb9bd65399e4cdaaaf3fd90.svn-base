
import { logInfo } from '../../../../scripts/utils/Logger';
import csproto from 'db://assets/bundles/common/script/autogen/pb/cs_proto.js';
import { LoginInfo } from '../network/NetMgr';
import { DevLoginData } from './DevLoginData';
import { IPlatformSDK, PlatformSDKUserInfo } from './IPlatformSDK.js';
// @ts-ignore
import CryptoJ<PERSON> from 'crypto-js';

export class DevLogin implements IPlatformSDK {
    showUserInfoButton() {
    }
    hideUserInfoButton() {
    }
    login(cb: (err: string|null, req: LoginInfo) => void): void {
        logInfo("Login", "dev login")
        cb(null, {
            accountType: csproto.cs.ACCOUNT_TYPE.ACCOUNT_TYPE_NONE,
            code: DevLoginData.instance.user + "#" + CryptoJS.MD5(DevLoginData.instance.password),
            serverAddr: DevLoginData.instance.getServerAddr(),
        })
    }

    getUserInfo(cb: (err: string, req: PlatformSDKUserInfo | null, hasTap: boolean) => void, param: any) {
        cb("", {
            name: 'dev',
            icon: "",
        }, false)
    }

    getStatusBarHeight () {
        return 0;
    }
}