{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/event/GameEvent.ts"], "names": ["GameEvent", "GameLoadEnd", "GameMainPlaneIn", "GameStart", "onNetGameStart", "onNetGameOver", "onLevelSpecialEvent"], "mappings": ";;;;;;;;;;;;;;2BAAaA,S,GAAa;AACtBC,QAAAA,WAAW,EAAE,aADS;AAEtBC,QAAAA,eAAe,EAAE,iBAFK;AAGtBC,QAAAA,SAAS,EAAE,WAHW;AAGE;AAExBC,QAAAA,cAAc,EAAE,gBALM;AAKY;AAClCC,QAAAA,aAAa,EAAE,eANO;AAMU;AAEhCC,QAAAA,mBAAmB,EAAE,qBARC,CAQsB;;AARtB,O", "sourcesContent": ["export const GameEvent  = {\r\n    GameLoadEnd: \"GameLoadEnd\",\r\n    GameMainPlaneIn: \"GameMainPlaneIn\",\r\n    GameStart: \"GameStart\", // 游戏开始事件\r\n\r\n    onNetGameStart: \"onNetGameStart\", // 联网游戏开始事件\r\n    onNetGameOver: \"onNetGameOver\", // 联网游戏结束事件\r\n\r\n    onLevelSpecialEvent: \"onLevelSpecialEvent\", // 关卡配置的特殊事件\r\n}\r\n"]}