{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/data/plane/PlaneCacheInfo.ts"], "names": ["PlaneCacheInfo", "PlaneData", "_isInit", "_planeDataDic", "init", "onNetAllPlaneInfo", "update", "serverPlaneList", "planeId", "data", "planeData", "getPlaneInfoById", "id"], "mappings": ";;;yCAKaA,c;;;;;;;;;;;;;;;;;;AAJJC,MAAAA,S,iBAAAA,S;;;;;;;gCAIID,c,GAAN,MAAMA,cAAN,CAAsC;AAAA;AAAA,eACzCE,OADyC,GAC/B,KAD+B;AAAA,eAEzCC,aAFyC,GAEA,EAFA;AAAA;;AAEI;AAGtCC,QAAAA,IAAI,GAAS;AAChB,eAAKF,OAAL,GAAe,IAAf;AACA,eAAKG,iBAAL;AACH;;AAEDC,QAAAA,MAAM,GAAS,CACd;;AAEDD,QAAAA,iBAAiB,GAAE;AACf,cAAIE,eAAe,GAAG,CAClB;AACIC,YAAAA,OAAO,EAAC;AADZ,WADkB,CAAtB;;AAMA,eAAI,IAAIC,IAAR,IAAgBF,eAAhB,EAAgC;AAC5B,gBAAIG,SAAS,GAAG;AAAA;AAAA,yCAAhB;AACAA,YAAAA,SAAS,CAACF,OAAV,GAAoBC,IAAI,CAACD,OAAzB;AACA,iBAAKL,aAAL,CAAmBO,SAAS,CAACF,OAA7B,IAAwCE,SAAxC;AACH;AACJ;;AAEDC,QAAAA,gBAAgB,CAACC,EAAD,EAAsB;AAAA,cAArBA,EAAqB;AAArBA,YAAAA,EAAqB,GAAT,QAAS;AAAA;;AAClC,cAAI,CAAC,KAAKV,OAAV,EAAkB;AACd,iBAAKE,IAAL;AACH;;AACD,iBAAO,KAAKD,aAAL,CAAmBS,EAAnB,CAAP;AACH;;AAhCwC,O", "sourcesContent": ["import { IData } from \"db://assets/bundles/common/script/data/DataManager\";\nimport { PlaneData } from \"./PlaneData\";\n\n\n\nexport class PlaneCacheInfo implements IData {\n    _isInit = false;\n    _planeDataDic:{[key:string]:PlaneData} = {}; //飞机数据\n\n\n    public init(): void {\n        this._isInit = true;\n        this.onNetAllPlaneInfo()\n    }\n\n    update(): void {\n    }\n\n    onNetAllPlaneInfo(){\n        let serverPlaneList = [\n            {\n                planeId:10003101,\n            }\n        ]\n\n        for(let data of serverPlaneList){\n            let planeData = new PlaneData();\n            planeData.planeId = data.planeId;\n            this._planeDataDic[planeData.planeId] = planeData;\n        }\n    }\n\n    getPlaneInfoById(id:number = 10003101){\n        if (!this._isInit){\n            this.init();\n        }\n        return this._planeDataDic[id]\n    }\n}\n"]}