System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, AttributeConst, DamageType, AttributeData, _crd;

  function _reportPossibleCrUseOfAttributeConst(extras) {
    _reporterNs.report("AttributeConst", "db://assets/bundles/common/script/const/AttributeConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDamageType(extras) {
    _reporterNs.report("DamageType", "../../autogen/luban/schema", _context.meta, extras);
  }

  _export("AttributeData", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      AttributeConst = _unresolved_2.AttributeConst;
    }, function (_unresolved_3) {
      DamageType = _unresolved_3.DamageType;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "3866aHkBO5O07LJvTyo/Lqh", "AttributeData", undefined);

      _export("AttributeData", AttributeData = class AttributeData {
        constructor() {
          this._baseAttributes = new Map();
          // 基础属性
          this._Attributes = new Map();
          // 缓存最终属性值
          this._AttributeModifies = new Map();
          this._sources = new Map();
          // 属性来源
          // 静态属性
          this.bulletHurtRate = 0;
          this.boomHurtRate = 0;
        }

        addBaseAttribute(key, value) {
          this._baseAttributes.set(key, this._baseAttributes.get(key) || 0 + value);

          this.recalculateAttributes(key);
        } // 添加属性来源


        addModify(id, key, value) {
          let modify = this._AttributeModifies.get(key);

          if (!modify) {
            modify = new Map();

            this._AttributeModifies.set(id, modify);
          }

          modify.set(id, value);

          let source = this._sources.get(key);

          if (!source) {
            source = [];

            this._sources.set(key, source);
          }

          source.push(id);
          this.recalculateAttributes(key);
        } // 移除属性来源


        removeModify(id) {
          let source = this._sources.get(id);

          source == null || source.forEach(key => {
            let modify = this._AttributeModifies.get(key);

            if (modify) {
              modify.delete(id);
            }

            this.recalculateAttributes(key);
          });

          this._sources.delete(id);
        } // 重新计算属性


        recalculateAttributes(key) {
          let finalValue = this._baseAttributes.get(key) || 0; // 遍历所有属性来源

          this._AttributeModifies.forEach(source => {
            source.forEach(value => {
              finalValue += value;
            });
          }); // 计算最终属性值: (基础值 + 加法值) * (1 + 百分比值/10000)


          this._Attributes.set(key, finalValue);
        }

        getFinalAttributeByKey(key) {
          return this._Attributes.get(key) || 0;
        }

        getFinialAttributeByOutInKey(outAddKey, outPreKey, inAddKey, inPerKey) {
          return (this.getFinalAttributeByKey(outAddKey) * (1 + this.getFinalAttributeByKey(outPreKey) / 10000) + this.getFinalAttributeByKey(inAddKey)) * (1 + this.getFinalAttributeByKey(inPerKey) / 10000);
        }

        static CalcBulletDamage(attackerAttr, defenderAttr, hurtRate, isBullet, damageType, isBoss, attackPowerFix) {
          // ①局外属性面板上显示的攻击力值=(攻击力局外绝对值1+攻击力局外绝对值2+…)×(1+攻击力局外百分比1+攻击力局外百分比2+…)
          // ②攻击力局内总值=(①局外属性面板上显示的攻击力值+攻击力局内绝对值1+攻击力局内绝对值2+⋯)×(1+攻击力局内百分比1+攻击力局内百分比2+⋯)
          let attack = attackerAttr.getAttack(); // ③子弹or核弹的局内攻击力=[(②攻击力局内总值×攻击转换系数+子弹攻击局外绝对值1+子弹攻击局外绝对值2+⋯)×(1+子弹攻击局外百分比1+子弹攻击局外百分比2+⋯)+子弹攻击局内绝对值1+子弹攻击局内绝对值2+⋯]×(1+子弹攻击局内百分比1+子弹攻击局内百分比2+⋯)

          if (isBullet) {
            let damageAttackOutAddKey = 0;
            ;
            let damageAttackOutPerKey = 0;
            ;
            let damageAttackInAddKey = 0;
            ;
            let damageAttackInPerKey = 0;
            ;

            switch (damageType) {
              case (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
                error: Error()
              }), DamageType) : DamageType).EXPLOSIVE:
                damageAttackOutAddKey = (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).ExplosiveBulletAttackInAdd;
                damageAttackOutPerKey = (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).ExplosiveBulletAttackInPer;
                damageAttackInAddKey = (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).ExplosiveBulletAttackOutAdd;
                damageAttackInPerKey = (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).ExplosiveBulletAttackOutPer;
                break;

              case (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
                error: Error()
              }), DamageType) : DamageType).NORMAL:
                damageAttackOutAddKey = (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).NormalBulletAttackInAdd;
                damageAttackOutPerKey = (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).NormalBulletAttackInPer;
                damageAttackInAddKey = (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).NormalBulletAttackOutAdd;
                damageAttackInPerKey = (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).NormalBulletAttackOutPer;
                break;

              case (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
                error: Error()
              }), DamageType) : DamageType).ENERGETIC:
                damageAttackOutAddKey = (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).EnergeticBulletAttackInAdd;
                damageAttackOutPerKey = (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).EnergeticBulletAttackInPer;
                damageAttackInAddKey = (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).EnergeticBulletAttackOutAdd;
                damageAttackInPerKey = (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).EnergeticBulletAttackOutPer;
                break;

              case (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
                error: Error()
              }), DamageType) : DamageType).PHYSICAL:
                damageAttackOutAddKey = (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).PhysicsBulletAttackInAdd;
                damageAttackOutPerKey = (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).PhysicsBulletAttackInPer;
                damageAttackInAddKey = (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).PhysicsBulletAttackOutAdd;
                damageAttackInPerKey = (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).PhysicsBulletAttackOutPer;
                break;
            }

            if (damageType != (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
              error: Error()
            }), DamageType) : DamageType).ALL) {
              attack = ((attack * hurtRate + attackerAttr.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                error: Error()
              }), AttributeConst) : AttributeConst).BulletAttackOutAdd) + attackerAttr.getFinalAttributeByKey(damageAttackOutAddKey)) * (1 + (attackerAttr.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                error: Error()
              }), AttributeConst) : AttributeConst).BulletAttackOutPer) + attackerAttr.getFinalAttributeByKey(damageAttackOutPerKey)) / 10000) + attackerAttr.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                error: Error()
              }), AttributeConst) : AttributeConst).BulletAttackInAdd) + attackerAttr.getFinalAttributeByKey(damageAttackInAddKey)) * (1 + (attackerAttr.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                error: Error()
              }), AttributeConst) : AttributeConst).BulletAttackInPer) + attackerAttr.getFinalAttributeByKey(damageAttackInPerKey)) / 10000);
            } else {
              attack = ((attack * hurtRate + attackerAttr.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                error: Error()
              }), AttributeConst) : AttributeConst).BulletAttackOutAdd)) * (1 + attackerAttr.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                error: Error()
              }), AttributeConst) : AttributeConst).BulletAttackOutPer) / 10000) + attackerAttr.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                error: Error()
              }), AttributeConst) : AttributeConst).BulletAttackInAdd)) * (1 + attackerAttr.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                error: Error()
              }), AttributeConst) : AttributeConst).BulletAttackInPer) / 10000);
            }
          } else {
            attack = ((attack * hurtRate + attackerAttr.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
              error: Error()
            }), AttributeConst) : AttributeConst).NuclearAttackOutAdd)) * (1 + attackerAttr.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
              error: Error()
            }), AttributeConst) : AttributeConst).NuclearAttackOutPer) / 10000) + attackerAttr.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
              error: Error()
            }), AttributeConst) : AttributeConst).NuclearAttackInAdd)) * (1 + attackerAttr.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
              error: Error()
            }), AttributeConst) : AttributeConst).NuclearAttackInPer) / 10000);
          } // ④子弹or核弹伤害抗性局内总值=[(子弹伤害抗性局外绝对值1+子弹伤害抗性局外绝对值2+⋯)×(1+子弹伤害抗性局外百分比1+子弹伤害抗性局外百分比2+⋯)+子弹伤害抗性局内绝对值1+子弹伤害抗性局内绝对值2+⋯]×(1+子弹伤害抗性局内百分比1+子弹伤害抗性局内百分比2+⋯)


          let hurtResistanceOutAddKey = isBullet ? (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).BulletHurtResistanceOutAdd : (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).NuclearHurtResistanceOutAdd;
          let hurtResistanceOutPerKey = isBullet ? (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).BulletHurtResistanceOutPer : (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).NuclearHurtResistanceOutPer;
          let hurtResistanceInAddKey = isBullet ? (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).BulletHurtResistanceInAdd : (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).NuclearHurtResistanceInAdd;
          let hurtResistanceInPerKey = isBullet ? (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).BulletHurtResistanceInPer : (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).NuclearHurtResistanceInPer;
          let hurtResistance = defenderAttr.getFinialAttributeByOutInKey(hurtResistanceOutAddKey, hurtResistanceOutPerKey, hurtResistanceInAddKey, hurtResistanceInPerKey); // ⑤承受子弹伤害%局内总值=(1-子弹伤害减免局外百分比1-子弹伤害减免局外百分比2-…)×(1-子弹伤害减免局内百分比1-子弹伤害减免局内百分比2-…)

          let hurtDerate = (1 - defenderAttr.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).BulletHurtDerateOut)) * (1 - defenderAttr.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).BulletHurtDerateIn)); // ⑥对boss or 普通怪物伤害%局内总值=(1+对boss or 普通怪物伤害局外百分比1+对boss or 普通怪物伤害局外百分比2+⋯)×(1+对boss or 普通怪物伤害局内百分比1+对boss or 普通怪物伤害局内百分比2+⋯)

          let hurtBonus;

          if (isBoss) {
            hurtBonus = (1 + attackerAttr.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
              error: Error()
            }), AttributeConst) : AttributeConst).BossHurtBonusOut)) * (1 + attackerAttr.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
              error: Error()
            }), AttributeConst) : AttributeConst).BossHurtBonusIn));
          } else {
            hurtBonus = (1 + attackerAttr.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
              error: Error()
            }), AttributeConst) : AttributeConst).NormalHurtBonusOut)) * (1 + attackerAttr.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
              error: Error()
            }), AttributeConst) : AttributeConst).NormalHurtBonusIn));
          } //⑦子弹（分类型）or核弹的局内伤害修正=(1+子弹伤害局外百分比1+子弹伤害局外百分比2+⋯)×(1+子弹伤害局内百分比1+子弹伤害局内百分比2+⋯)


          let hurtFix;

          if (isBullet) {
            let damageHurtFixOutKey = 0;
            let damageHurtFixInKey = 0;

            switch (damageType) {
              case (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
                error: Error()
              }), DamageType) : DamageType).EXPLOSIVE:
                damageHurtFixOutKey = (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).ExplosiveBulletHurtFixOut;
                damageHurtFixInKey = (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).ExplosiveBulletHurtFixIn;
                break;

              case (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
                error: Error()
              }), DamageType) : DamageType).NORMAL:
                damageHurtFixOutKey = (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).NormalBulletHurtFixOut;
                damageHurtFixInKey = (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).NormalBulletHurtFixIn;
                break;

              case (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
                error: Error()
              }), DamageType) : DamageType).ENERGETIC:
                damageHurtFixOutKey = (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).EnergeticBulletHurtFixOut;
                damageHurtFixInKey = (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).EnergeticBulletHurtFixIn;
                break;

              case (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
                error: Error()
              }), DamageType) : DamageType).PHYSICAL:
                damageHurtFixOutKey = (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).PhysicsBulletHurtFixOut;
                damageHurtFixInKey = (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).PhysicsBulletHurtFixIn;
                break;
            }

            if (damageType != (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
              error: Error()
            }), DamageType) : DamageType).ALL) {
              hurtFix = (1 + attackerAttr.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                error: Error()
              }), AttributeConst) : AttributeConst).BulletHurtFixOut) + attackerAttr.getFinalAttributeByKey(damageHurtFixOutKey)) * (1 + attackerAttr.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                error: Error()
              }), AttributeConst) : AttributeConst).BulletHurtFixIn) + attackerAttr.getFinalAttributeByKey(damageHurtFixInKey));
            } else {
              hurtFix = (1 + attackerAttr.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                error: Error()
              }), AttributeConst) : AttributeConst).BulletHurtFixOut)) * (1 + attackerAttr.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                error: Error()
              }), AttributeConst) : AttributeConst).BulletHurtFixIn));
            }
          } else {
            hurtFix = (1 + attackerAttr.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
              error: Error()
            }), AttributeConst) : AttributeConst).NuclearHurtFixOut)) * (1 + attackerAttr.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
              error: Error()
            }), AttributeConst) : AttributeConst).NuclearHurtFixIn));
          } // ⑧子弹or核弹最终造成的伤害=[(③子弹or核弹的局内攻击力×⑦子弹or核弹的局内伤害修正×战斗力差伤害修正百分比)-④受击方子弹伤害抗性局内总值]×⑤受击方承受子弹伤害%局内总值×(1+⑥攻击方对boss or 普通怪物伤害%局内总值)


          let damage = (attack * hurtFix * attackPowerFix - hurtResistance) * hurtDerate * hurtBonus;
          return Math.ceil(damage);
        }

        getMaxHP() {
          return Math.floor(this.getFinialAttributeByOutInKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).MaxHPOutAdd, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).MaxHPOutPer, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).MaxHPInAdd, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).MaxHPInPer));
        }

        getAttack() {
          return Math.floor(this.getFinialAttributeByOutInKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).AttackOutAdd, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).AttackOutPer, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).AttackInAdd, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).AttackInPer));
        }

        getHPRecovery() {
          return Math.floor(this.getFinialAttributeByOutInKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).HPRecoveryOutAdd, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).HPRecoveryOutPer, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).HPRecoveryInAdd, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).HPRecoveryInPer) + this.getMaxHP() * (this.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).MaxHPRecoveryRateOut) + this.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).MaxHPRecoveryRateIn)) / 10000);
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=fdff4dfc58f944cf54aeaf618d77a2da4d9e4018.js.map