System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, CCInteger, MainPlane, PlaneBaseDebug, AttributeConst, _dec, _dec2, _dec3, _class, _class2, _crd, ccclass, property, MainPlaneDebug;

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _reportPossibleCrUseOfMainPlane(extras) {
    _reporterNs.report("MainPlane", "./MainPlane", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneBaseDebug(extras) {
    _reporterNs.report("PlaneBaseDebug", "../PlaneBaseDebug", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAttributeConst(extras) {
    _reporterNs.report("AttributeConst", "../../../../const/AttributeConst", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      CCInteger = _cc.CCInteger;
    }, function (_unresolved_2) {
      MainPlane = _unresolved_2.MainPlane;
    }, function (_unresolved_3) {
      PlaneBaseDebug = _unresolved_3.default;
    }, function (_unresolved_4) {
      AttributeConst = _unresolved_4.AttributeConst;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "0e0d0nFgHVNrJsHEtJVMYt8", "MainPlaneDebug", undefined);

      __checkObsolete__(['_decorator', 'CCBoolean', 'CCFloat', 'CCInteger', 'Component']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", MainPlaneDebug = (_dec = ccclass("MainPlaneDebug"), _dec2 = property(CCInteger), _dec3 = property(CCInteger), _dec(_class = (_class2 = class MainPlaneDebug extends (_crd && PlaneBaseDebug === void 0 ? (_reportPossibleCrUseOfPlaneBaseDebug({
        error: Error()
      }), PlaneBaseDebug) : PlaneBaseDebug) {
        constructor() {
          super(...arguments);
          this.mainPlane = null;
        }

        start() {
          super.start();
          this.mainPlane = this.node.getComponent(_crd && MainPlane === void 0 ? (_reportPossibleCrUseOfMainPlane({
            error: Error()
          }), MainPlane) : MainPlane);
        }

        get maxNuclear() {
          var _this$mainPlane;

          return Math.floor(((_this$mainPlane = this.mainPlane) == null ? void 0 : _this$mainPlane.attribute.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).NuclearMax)) || 0);
        }

        get curNuclear() {
          var _this$mainPlane2;

          return ((_this$mainPlane2 = this.mainPlane) == null ? void 0 : _this$mainPlane2.nuclearNum) || 0;
        }

      }, (_applyDecoratedDescriptor(_class2.prototype, "maxNuclear", [_dec2], Object.getOwnPropertyDescriptor(_class2.prototype, "maxNuclear"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "curNuclear", [_dec3], Object.getOwnPropertyDescriptor(_class2.prototype, "curNuclear"), _class2.prototype)), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=5d454448b8b07f76d492b69a7447a1a3918910db.js.map