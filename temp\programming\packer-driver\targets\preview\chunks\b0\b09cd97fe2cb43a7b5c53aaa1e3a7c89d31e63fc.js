System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Node, Color, Component, JsonAsset, CCInteger, Graphics, Vec3, PathData, PathPoint, PathPointEditor, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _dec9, _dec10, _dec11, _dec12, _class, _class2, _descriptor, _descriptor2, _crd, ccclass, executeInEditMode, property, disallowMultiple, menu, requireComponent, PathEditor;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _reportPossibleCrUseOfPathData(extras) {
    _reporterNs.report("PathData", "db://assets/bundles/common/script/game/data/PathData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathPoint(extras) {
    _reporterNs.report("PathPoint", "db://assets/bundles/common/script/game/data/PathData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathPointEditor(extras) {
    _reporterNs.report("PathPointEditor", "./PathPointEditor", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Node = _cc.Node;
      Color = _cc.Color;
      Component = _cc.Component;
      JsonAsset = _cc.JsonAsset;
      CCInteger = _cc.CCInteger;
      Graphics = _cc.Graphics;
      Vec3 = _cc.Vec3;
    }, function (_unresolved_2) {
      PathData = _unresolved_2.PathData;
      PathPoint = _unresolved_2.PathPoint;
    }, function (_unresolved_3) {
      PathPointEditor = _unresolved_3.PathPointEditor;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "3b164ESPc1DLJK8SqXjxSO0", "PathEditor", undefined);

      __checkObsolete__(['_decorator', 'Node', 'Color', 'Component', 'JsonAsset', 'CCInteger', 'Graphics', 'Vec3']);

      ({
        ccclass,
        executeInEditMode,
        property,
        disallowMultiple,
        menu,
        requireComponent
      } = _decorator);

      _export("PathEditor", PathEditor = (_dec = ccclass('PathEditor'), _dec2 = menu("怪物/编辑器/路径编辑"), _dec3 = requireComponent(Graphics), _dec4 = executeInEditMode(true), _dec5 = disallowMultiple(true), _dec6 = property({
        type: JsonAsset,
        displayName: "路径数据"
      }), _dec7 = property({
        displayName: "路径名称"
      }), _dec8 = property({
        type: CCInteger,
        displayName: '起始点'
      }), _dec9 = property({
        type: CCInteger,
        displayName: '结束点(-1代表默认最后个点)'
      }), _dec10 = property({
        displayName: "是否闭合",

        visible() {
          // @ts-ignore
          return this._pathDataObj.points.length >= 3;
        }

      }), _dec11 = property({
        displayName: "曲线颜色"
      }), _dec12 = property({
        displayName: "显示细分线段"
      }), _dec(_class = _dec2(_class = _dec3(_class = _dec4(_class = _dec5(_class = (_class2 = class PathEditor extends Component {
        constructor() {
          super(...arguments);
          this._graphics = null;

          _initializerDefineProperty(this, "curveColor", _descriptor, this);

          _initializerDefineProperty(this, "showSegments", _descriptor2, this);

          // 是否使用不同颜色来绘制不同的细分线段
          this._showDirectionArrow = true;
          this._pathData = null;
          this._pathDataObj = new (_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
            error: Error()
          }), PathData) : PathData)();
          this._cachedChildrenCount = 0;
        }

        get graphics() {
          if (!this._graphics) {
            this._graphics = this.node.getComponent(Graphics) || this.node.addComponent(Graphics);
          }

          return this._graphics;
        }

        set pathData(value) {
          this._pathData = value;
          this.reload();
        }

        get pathData() {
          return this._pathData;
        }

        get pathName() {
          return this._pathDataObj.name;
        }

        set pathName(value) {
          this._pathDataObj.name = value;
        }

        get startIdx() {
          return this._pathDataObj.startIdx;
        }

        set startIdx(value) {
          this._pathDataObj.startIdx = value;
        }

        get endIdx() {
          return this._pathDataObj.endIdx;
        }

        set endIdx(value) {
          this._pathDataObj.endIdx = value;
        }

        get isClosed() {
          return this._pathDataObj.closed;
        }

        set isClosed(value) {
          this._pathDataObj.closed = value;
        }

        reload() {
          if (!this._pathData) return;
          var pathData = new (_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
            error: Error()
          }), PathData) : PathData)();
          Object.assign(pathData, this._pathData.json);
          this._pathDataObj = pathData;
          this.node.removeAllChildren();

          if (this._pathDataObj && this._pathDataObj.points.length > 0) {
            this._pathDataObj.points.forEach(point => {
              this.addPoint(point);
            });
          }

          this.updateCurve();
        }

        save() {
          // 收集所有路径点数据
          var pointEditors = this.getComponentsInChildren(_crd && PathPointEditor === void 0 ? (_reportPossibleCrUseOfPathPointEditor({
            error: Error()
          }), PathPointEditor) : PathPointEditor);
          this._pathDataObj.points = pointEditors.map(editor => editor.pathPoint);
          return JSON.stringify(this._pathDataObj, null, 2);
        }

        addPoint(point) {
          var pointNode = new Node();
          pointNode.parent = this.node;
          pointNode.setPosition(point.x, point.y, 0);
          var pointEditor = pointNode.addComponent(_crd && PathPointEditor === void 0 ? (_reportPossibleCrUseOfPathPointEditor({
            error: Error()
          }), PathPointEditor) : PathPointEditor);
          pointEditor.pathPoint = point;
        }

        addNewPoint(x, y) {
          var point = new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(x, y);
          this.addPoint(point);
          this.updateCurve();
        }

        updateCurve() {
          // 收集当前所有点的数据
          var pointEditors = this.getComponentsInChildren(_crd && PathPointEditor === void 0 ? (_reportPossibleCrUseOfPathPointEditor({
            error: Error()
          }), PathPointEditor) : PathPointEditor);
          this._pathDataObj.points = pointEditors.map(editor => editor.pathPoint);
        }

        drawPath() {
          var graphics = this.graphics;
          graphics.clear();
          if (this._pathDataObj.points.length < 2) return;

          var subdivided = this._pathDataObj.getSubdividedPoints(true);

          if (subdivided.length > 1) {
            if (this.showSegments) {
              this.drawSegmentedPath(graphics, subdivided);
            } else {
              this.drawUniformPath(graphics, subdivided);
            } // 绘制路径终点的方向箭头（仅对非闭合路径）


            if (this._showDirectionArrow && !this._pathDataObj.closed) {
              this.drawPathDirectionArrow(graphics, subdivided);
            }
          }

          if (this.showSegments) {
            console.log('subdivided points length: ', subdivided.length);
          }
        }
        /**
         * 绘制统一颜色的路径
         */


        drawUniformPath(graphics, subdivided) {
          graphics.strokeColor = this.curveColor;
          graphics.lineWidth = 5;
          graphics.moveTo(subdivided[0].x, subdivided[0].y);

          for (var i = 1; i < subdivided.length; i++) {
            graphics.lineTo(subdivided[i].x, subdivided[i].y);
          } // 如果是闭合路径，连接回起点


          if (this._pathDataObj.closed) {
            graphics.lineTo(subdivided[0].x, subdivided[0].y);
          }

          graphics.stroke();
        }
        /**
         * 绘制分段着色的路径 - 每个细分段用不同颜色
         */


        drawSegmentedPath(graphics, subdivided) {
          graphics.lineWidth = 5;
          if (subdivided.length < 2) return; // 为每个细分段绘制不同的颜色

          for (var i = 0; i < subdivided.length - 1; i++) {
            var t = i / (subdivided.length - 1); // 计算当前位置的归一化参数 [0,1]
            // 从绿色到红色的颜色插值

            var color = this.interpolateColor(Color.GREEN, Color.RED, t);
            graphics.strokeColor = color; // 绘制当前段

            graphics.moveTo(subdivided[i].x, subdivided[i].y);
            graphics.lineTo(subdivided[i + 1].x, subdivided[i + 1].y);
            graphics.stroke();
          } // 如果是闭合路径，绘制最后一段回到起点


          if (this._pathDataObj.closed && subdivided.length > 2) {
            var lastColor = this.interpolateColor(Color.GREEN, Color.RED, 1.0);
            graphics.strokeColor = lastColor;
            graphics.moveTo(subdivided[subdivided.length - 1].x, subdivided[subdivided.length - 1].y);
            graphics.lineTo(subdivided[0].x, subdivided[0].y);
            graphics.stroke();
          }
        }
        /**
         * 颜色插值函数
         * @param color1 起始颜色
         * @param color2 结束颜色
         * @param t 插值参数 [0,1]
         */


        interpolateColor(color1, color2, t) {
          t = Math.max(0, Math.min(1, t)); // 确保t在[0,1]范围内

          var r = color1.r + (color2.r - color1.r) * t;
          var g = color1.g + (color2.g - color1.g) * t;
          var b = color1.b + (color2.b - color1.b) * t;
          var a = color1.a + (color2.a - color1.a) * t;
          return new Color(r, g, b, a);
        }

        update(_dt) {
          var childrenCount = this.node.children.length;

          if (childrenCount !== this._cachedChildrenCount) {
            this._cachedChildrenCount = childrenCount;
          } // 把自己的位置锁定在原点，避免出现绘制偏移


          this.node.position = Vec3.ZERO;
          this.updateCurve();
          this.drawPath();
        }
        /**
         * 绘制路径方向箭头
         */


        drawPathDirectionArrow(graphics, subdivided) {
          if (subdivided.length < 2) return; // 如果是闭合路径，不绘制箭头（因为没有明确的终点）

          if (this._pathDataObj.closed) return; // 计算终点的方向（使用最后几个点来获得更准确的方向）

          var endPoint = subdivided[subdivided.length - 1];
          var prevPoint = subdivided[subdivided.length - 2]; // 如果有足够的点，使用更远的点来计算方向，获得更平滑的方向

          if (subdivided.length >= 5) {
            prevPoint = subdivided[subdivided.length - 5];
          } // 计算方向角度


          var direction = Math.atan2(endPoint.y - prevPoint.y, endPoint.x - prevPoint.x); // 箭头参数

          var arrowLength = 40;
          var arrowHeadLength = 20;
          var arrowHeadAngle = Math.PI / 5; // 36度，更尖锐的箭头
          // 设置箭头样式

          graphics.strokeColor = Color.RED;
          graphics.fillColor = Color.RED;
          graphics.lineWidth = 3; // 计算箭头起点（从路径终点开始）

          var arrowStartX = endPoint.x;
          var arrowStartY = endPoint.y; // 计算箭头终点

          var arrowEndX = arrowStartX + Math.cos(direction) * arrowLength;
          var arrowEndY = arrowStartY + Math.sin(direction) * arrowLength; // 绘制箭头主线

          graphics.moveTo(arrowStartX, arrowStartY);
          graphics.lineTo(arrowEndX, arrowEndY);
          graphics.stroke(); // 绘制箭头头部（填充三角形）

          var leftX = arrowEndX - Math.cos(direction - arrowHeadAngle) * arrowHeadLength;
          var leftY = arrowEndY - Math.sin(direction - arrowHeadAngle) * arrowHeadLength;
          var rightX = arrowEndX - Math.cos(direction + arrowHeadAngle) * arrowHeadLength;
          var rightY = arrowEndY - Math.sin(direction + arrowHeadAngle) * arrowHeadLength; // 绘制填充的箭头头部

          graphics.moveTo(arrowEndX, arrowEndY);
          graphics.lineTo(leftX, leftY);
          graphics.lineTo(rightX, rightY);
          graphics.close();
          graphics.fill();
          graphics.stroke();
        }

      }, (_applyDecoratedDescriptor(_class2.prototype, "pathData", [_dec6], Object.getOwnPropertyDescriptor(_class2.prototype, "pathData"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "pathName", [_dec7], Object.getOwnPropertyDescriptor(_class2.prototype, "pathName"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "startIdx", [_dec8], Object.getOwnPropertyDescriptor(_class2.prototype, "startIdx"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "endIdx", [_dec9], Object.getOwnPropertyDescriptor(_class2.prototype, "endIdx"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "isClosed", [_dec10], Object.getOwnPropertyDescriptor(_class2.prototype, "isClosed"), _class2.prototype), _descriptor = _applyDecoratedDescriptor(_class2.prototype, "curveColor", [_dec11], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return Color.WHITE;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "showSegments", [_dec12], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return false;
        }
      })), _class2)) || _class) || _class) || _class) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=b09cd97fe2cb43a7b5c53aaa1e3a7c89d31e63fc.js.map