System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Vec2, WaveData, FormationGroup, eSpawnOrder, eWaveCompletion, PathData, GameIns, WaveEventGroup, WaveEventGroupContext, PlaneEventType, _dec, _dec2, _dec3, _dec4, _dec5, _class, _class2, _descriptor, _descriptor2, _crd, ccclass, property, executeInEditMode, menu, Wave;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfWaveData(extras) {
    _reporterNs.report("WaveData", "../data/WaveData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFormationGroup(extras) {
    _reporterNs.report("FormationGroup", "../data/WaveData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeSpawnOrder(extras) {
    _reporterNs.report("eSpawnOrder", "../data/WaveData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeWaveCompletion(extras) {
    _reporterNs.report("eWaveCompletion", "../data/WaveData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathData(extras) {
    _reporterNs.report("PathData", "../data/PathData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "db://assets/bundles/common/script/game/GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfWaveEventGroup(extras) {
    _reporterNs.report("WaveEventGroup", "./WaveEventGroup", _context.meta, extras);
  }

  function _reportPossibleCrUseOfWaveEventGroupContext(extras) {
    _reporterNs.report("WaveEventGroupContext", "./WaveEventGroup", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyPlane(extras) {
    _reporterNs.report("EnemyPlane", "db://assets/bundles/common/script/game/ui/plane/enemy/EnemyPlane", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneEventType(extras) {
    _reporterNs.report("PlaneEventType", "db://assets/bundles/common/script/game/ui/plane/event/PlaneEventType", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Vec2 = _cc.Vec2;
    }, function (_unresolved_2) {
      WaveData = _unresolved_2.WaveData;
      FormationGroup = _unresolved_2.FormationGroup;
      eSpawnOrder = _unresolved_2.eSpawnOrder;
      eWaveCompletion = _unresolved_2.eWaveCompletion;
    }, function (_unresolved_3) {
      PathData = _unresolved_3.PathData;
    }, function (_unresolved_4) {
      GameIns = _unresolved_4.GameIns;
    }, function (_unresolved_5) {
      WaveEventGroup = _unresolved_5.WaveEventGroup;
      WaveEventGroupContext = _unresolved_5.WaveEventGroupContext;
    }, function (_unresolved_6) {
      PlaneEventType = _unresolved_6.PlaneEventType;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "194feuuQpZAtYZElOT4+NCI", "Wave", undefined);

      __checkObsolete__(['_decorator', 'CCBoolean', 'CCFloat', 'CCInteger', 'CCString', 'Component', 'Vec2']);

      ({
        ccclass,
        property,
        executeInEditMode,
        menu
      } = _decorator);

      _export("Wave", Wave = (_dec = ccclass('Wave'), _dec2 = menu("怪物/波次"), _dec3 = executeInEditMode(), _dec4 = property({
        displayName: '名称',
        editorOnly: true
      }), _dec5 = property({
        type: _crd && WaveData === void 0 ? (_reportPossibleCrUseOfWaveData({
          error: Error()
        }), WaveData) : WaveData
      }), _dec(_class = _dec2(_class = _dec3(_class = (_class2 = class Wave extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "waveName", _descriptor, this);

          // 备注(策划用)
          _initializerDefineProperty(this, "waveData", _descriptor2, this);

          /*
           * 以下是新的wave逻辑, 旧的逻辑主要在WaveManager与EnemyWave
           */
          this._isSpawnCompleted = false;
          this._isAllEnemyDead = false;
          this._waveElapsedTime = 0;
          this._nextSpawnTime = 0;
          this._totalWeight = 0;
          // 这个参数可能是时间，也可能是数量，取决于waveData.waveCompletion
          this._waveCompleteParam = 0;
          this._spawnIndex = 0;
          // 用在waveCompletion == SpawnCount时的队列
          this._spawnQueue = [];
          // 当前wave的起点位置
          this._originX = 0;
          this._originY = 0;
          this._formationGroup = null;
          this._path = null;
          // 事件组
          this._eventGroups = [];
          this._eventGroupContext = null;
          // 怪物
          this._enemyCreated = [];
          // 以下几个函数是为了给编辑器预览用
          this._createPlaneDelegate = null;
        }

        get isSpawnCompleted() {
          return this._isSpawnCompleted;
        }

        get isAllEnemyDead() {
          return this._isAllEnemyDead;
        }

        onLoad() {
          if (this.waveData) {
            if (this.waveData.spawnOrder === (_crd && eSpawnOrder === void 0 ? (_reportPossibleCrUseOfeSpawnOrder({
              error: Error()
            }), eSpawnOrder) : eSpawnOrder).Random) {
              this._totalWeight = 0; // add up _totalWeight if is random

              this.waveData.spawnGroups.forEach(group => {
                this._totalWeight += group.weight;
                group.selfWeight = this._totalWeight;
              });
            }

            if (this.waveData.eventGroupData) {
              if (!this._eventGroupContext) {
                this._eventGroupContext = new (_crd && WaveEventGroupContext === void 0 ? (_reportPossibleCrUseOfWaveEventGroupContext({
                  error: Error()
                }), WaveEventGroupContext) : WaveEventGroupContext)();
              }

              this.waveData.eventGroupData.forEach(groupData => {
                const group = new (_crd && WaveEventGroup === void 0 ? (_reportPossibleCrUseOfWaveEventGroup({
                  error: Error()
                }), WaveEventGroup) : WaveEventGroup)(this._eventGroupContext, groupData);

                this._eventGroups.push(group);
              });
            }

            if (this.waveData.formationAsset) {
              this._formationGroup = new (_crd && FormationGroup === void 0 ? (_reportPossibleCrUseOfFormationGroup({
                error: Error()
              }), FormationGroup) : FormationGroup)();
              Object.assign(this._formationGroup, this.waveData.formationAsset.json);
            }

            if (this.waveData.pathAsset) {
              this._path = (_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
                error: Error()
              }), PathData) : PathData).fromJSON(this.waveData.pathAsset.json);
            }
          }
        }

        reset() {
          this._isSpawnCompleted = false;
          this._isAllEnemyDead = false;
          this._waveElapsedTime = 0;
          this._nextSpawnTime = 0;
          this._spawnIndex = 0;
          this._spawnQueue.length = 0;
          this._eventGroupContext && this._eventGroupContext.reset();

          this._eventGroups.forEach(group => {
            group.reset();
          });

          if (this._enemyCreated.length > 0) {
            this._enemyCreated.length = 0;
          }
        }

        static random() {
          if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager) {
            return (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.random();
          }

          return Math.random();
        }

        trigger(x, y) {
          this.reset();
          this._originX = x;
          this._originY = y;
          console.log('wave triggered at x: ' + x + ', y: ' + y); // 对于固定数量的波次，可以预先生成队列，每次从里面取即可

          if (this.waveData) {
            this._waveCompleteParam = this.waveData.waveCompletionParam.eval();

            if (this.waveData.waveCompletion === (_crd && eWaveCompletion === void 0 ? (_reportPossibleCrUseOfeWaveCompletion({
              error: Error()
            }), eWaveCompletion) : eWaveCompletion).SpawnCount) {
              if (this.waveData.spawnOrder === (_crd && eSpawnOrder === void 0 ? (_reportPossibleCrUseOfeSpawnOrder({
                error: Error()
              }), eSpawnOrder) : eSpawnOrder).Random) {
                for (let i = 0; i < this._waveCompleteParam; i++) {
                  const randomWeight = Wave.random() * this._totalWeight;

                  for (const group of this.waveData.spawnGroups) {
                    if (randomWeight <= group.selfWeight) {
                      this._spawnQueue.push(group.planeID);

                      break;
                    }
                  }
                }
              } else {
                for (let i = 0; i < this._waveCompleteParam; i++) {
                  // 通过取余实现循环
                  this._spawnQueue.push(this.waveData.spawnGroups[i % this.waveData.spawnGroups.length].planeID);
                }
              }
            } // 编辑器下预览这里可能为空


            if (!this._formationGroup && this.waveData.formationAsset) {
              this._formationGroup = new (_crd && FormationGroup === void 0 ? (_reportPossibleCrUseOfFormationGroup({
                error: Error()
              }), FormationGroup) : FormationGroup)();
              Object.assign(this._formationGroup, this.waveData.formationAsset.json);
            }

            if (!this._path && this.waveData.pathAsset) {
              this._path = (_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
                error: Error()
              }), PathData) : PathData).fromJSON(this.waveData.pathAsset.json);
            }
          }

          this._eventGroupContext && (this._eventGroupContext.wave = this);

          this._eventGroups.forEach(group => {
            group.tryStart();
          });
        } // tick wave


        tick(dtInMiliseconds) {
          if (!this._isSpawnCompleted) {
            this.tickSpawn(dtInMiliseconds);
          }
        }

        tickSpawn(dtInMiliseconds) {
          this._waveElapsedTime += dtInMiliseconds;

          if (this.waveData.waveCompletion === (_crd && eWaveCompletion === void 0 ? (_reportPossibleCrUseOfeWaveCompletion({
            error: Error()
          }), eWaveCompletion) : eWaveCompletion).SpawnCount) {
            // 产出固定数量的波次
            if (this._spawnIndex >= this._waveCompleteParam) {
              this._isSpawnCompleted = true;
            } else {
              if (this._waveElapsedTime >= this._nextSpawnTime) {
                this.spawnFromQueue();
              }
            }
          } else {
            // 完全根据时间的波次
            if (this._waveElapsedTime >= this._waveCompleteParam) {
              this._isSpawnCompleted = true;
            } else {
              if (this._waveElapsedTime >= this._nextSpawnTime) {
                this.spawnFromGroup();
              }
            }
          }

          if (this._eventGroups.length > 0) {
            for (let i = 0; i < this._eventGroups.length; i++) {
              this._eventGroups[i].tick(dtInMiliseconds);
            }
          }
        }

        spawnFromQueue() {
          this.spawnSingleFromQueue(this._spawnIndex % this._spawnQueue.length);
          this._nextSpawnTime = this._waveElapsedTime + this.waveData.spawnInterval.eval();
        }

        spawnSingleFromQueue(index) {
          if (index >= this._spawnQueue.length) {
            return;
          }

          let spawnOffset = this.waveData.spawnPosOffset;
          let spawnAngle = this.waveData.spawnAngle.eval(); // let spawnSpeed = this.waveData.spawnSpeed.eval();

          this.createPlane(this._spawnQueue[index], spawnOffset, spawnAngle);
        }

        spawnFromGroup() {
          this._nextSpawnTime = this._waveElapsedTime + this.waveData.spawnInterval.eval();
          let spawnOffset = this.waveData.spawnPosOffset;
          let spawnAngle = this.waveData.spawnAngle.eval();

          if (this.waveData.spawnOrder === (_crd && eSpawnOrder === void 0 ? (_reportPossibleCrUseOfeSpawnOrder({
            error: Error()
          }), eSpawnOrder) : eSpawnOrder).Random) {
            const randomWeight = Wave.random() * this._totalWeight;

            for (const group of this.waveData.spawnGroups) {
              if (randomWeight <= group.selfWeight) {
                this.createPlane(group.planeID, spawnOffset, spawnAngle);
                break;
              }
            }
          } else {
            this.createPlane(this.waveData.spawnGroups[this._spawnIndex % this.waveData.spawnGroups.length].planeID, spawnOffset, spawnAngle);
          }
        }

        createPlane(planeId, offset, angle) {
          let origin = new Vec2(this._originX, this._originY);

          if (this._path) {
            // 如果有路径的情况下, 选择路径起点作为原点
            origin = this._path.points[this._path.startIdx].position;
          } // 获取阵型数据


          if (this._formationGroup) {
            const point = this._formationGroup.points[this._spawnIndex % this._formationGroup.points.length];
            offset.x += point.x;
            offset.y += point.y;
          }

          origin.x += offset.x;
          origin.y += offset.y;
          this._spawnIndex++;

          if (this._createPlaneDelegate) {
            this._createPlaneDelegate(planeId, origin, angle);

            return;
          } // console.log(`createPlane: ${planeId}, ${offset.x}, ${offset.y}, ${angle}`);


          let enemy = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).enemyManager.addPlane(planeId);

          if (enemy) {
            if (this._path) {
              // 注意: Path已经包含了origin, 因此这里我们只传入origin
              enemy.initPath(offset.x, offset.y, this._path);
            } else {
              enemy.initMove(origin.x, origin.y, angle);
            }

            enemy.PlaneEventRegister((_crd && PlaneEventType === void 0 ? (_reportPossibleCrUseOfPlaneEventType({
              error: Error()
            }), PlaneEventType) : PlaneEventType).Die, this.onEnemyDie.bind(this));

            this._enemyCreated.push(enemy); // 记录下来，方便后面判断是否都死了

          }
        }

        onEnemyDie(plane) {
          plane.PlaneEventUnRegister((_crd && PlaneEventType === void 0 ? (_reportPossibleCrUseOfPlaneEventType({
            error: Error()
          }), PlaneEventType) : PlaneEventType).Die, this.onEnemyDie.bind(this));

          const index = this._enemyCreated.indexOf(plane);

          if (index !== -1) {
            this._enemyCreated.splice(index, 1);

            if (this._enemyCreated.length === 0 && this._isSpawnCompleted) {
              this._isAllEnemyDead = true;
            }
          }
        }

        setCreatePlaneDelegate(func) {
          this._createPlaneDelegate = func;
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "waveName", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return '';
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "waveData", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return new (_crd && WaveData === void 0 ? (_reportPossibleCrUseOfWaveData({
            error: Error()
          }), WaveData) : WaveData)();
        }
      })), _class2)) || _class) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=fd7f5bbc13425698edb92d84e45991d4e1cf1e4a.js.map