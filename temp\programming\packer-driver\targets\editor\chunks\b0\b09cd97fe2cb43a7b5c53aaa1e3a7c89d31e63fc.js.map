{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/wave/PathEditor.ts"], "names": ["_decorator", "Node", "Color", "Component", "JsonAsset", "CCInteger", "Graphics", "Vec3", "PathData", "PathPoint", "PathPointEditor", "ccclass", "executeInEditMode", "property", "disallowMultiple", "menu", "requireComponent", "PathEditor", "type", "displayName", "visible", "_pathDataObj", "points", "length", "_graphics", "_showDirectionArrow", "_pathData", "_cachedChildrenCount", "graphics", "node", "getComponent", "addComponent", "pathData", "value", "reload", "pathName", "name", "startIdx", "endIdx", "isClosed", "closed", "Object", "assign", "json", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "point", "addPoint", "updateCurve", "save", "pointEditors", "getComponentsInChildren", "map", "editor", "pathPoint", "JSON", "stringify", "pointNode", "parent", "setPosition", "x", "y", "pointEditor", "addNewPoint", "drawPath", "clear", "subdivided", "getSubdividedPoints", "showSegments", "drawSegmentedPath", "drawUniformPath", "drawPathDirectionArrow", "console", "log", "strokeColor", "curveColor", "lineWidth", "moveTo", "i", "lineTo", "stroke", "t", "color", "interpolateColor", "GREEN", "RED", "lastColor", "color1", "color2", "Math", "max", "min", "r", "g", "b", "a", "update", "_dt", "childrenCount", "children", "position", "ZERO", "endPoint", "prevPoint", "direction", "atan2", "<PERSON><PERSON><PERSON><PERSON>", "arrowHeadLength", "arrowHeadAngle", "PI", "fillColor", "arrowStartX", "arrowStartY", "arrowEndX", "cos", "arrowEndY", "sin", "leftX", "leftY", "rightX", "rightY", "close", "fill", "WHITE"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,I,OAAAA,I;;AAGpEC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,S,iBAAAA,S;;AACVC,MAAAA,e,iBAAAA,e;;;;;;;;;OAHH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,iBAAX;AAA8BC,QAAAA,QAA9B;AAAwCC,QAAAA,gBAAxC;AAA0DC,QAAAA,IAA1D;AAAgEC,QAAAA;AAAhE,O,GAAqFhB,U;;4BAU9EiB,U,WALZN,OAAO,CAAC,YAAD,C,UACPI,IAAI,CAAC,aAAD,C,UACJC,gBAAgB,CAACV,QAAD,C,UAChBM,iBAAiB,CAAC,IAAD,C,UACjBE,gBAAgB,CAAC,IAAD,C,UAUZD,QAAQ,CAAC;AAAEK,QAAAA,IAAI,EAAEd,SAAR;AAAmBe,QAAAA,WAAW,EAAE;AAAhC,OAAD,C,UASRN,QAAQ,CAAC;AAAEM,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAQRN,QAAQ,CAAC;AAAEK,QAAAA,IAAI,EAAEb,SAAR;AAAmBc,QAAAA,WAAW,EAAE;AAAhC,OAAD,C,UAQRN,QAAQ,CAAC;AAAEK,QAAAA,IAAI,EAAEb,SAAR;AAAmBc,QAAAA,WAAW,EAAE;AAAhC,OAAD,C,WAQRN,QAAQ,CAAC;AAAEM,QAAAA,WAAW,EAAE,MAAf;;AAAuBC,QAAAA,OAAO,GAAG;AACvC;AACA,iBAAO,KAAKC,YAAL,CAAkBC,MAAlB,CAAyBC,MAAzB,IAAmC,CAA1C;AACH;;AAHS,OAAD,C,WAWRV,QAAQ,CAAC;AAAEM,QAAAA,WAAW,EAAE;AAAf,OAAD,C,WAGRN,QAAQ,CAAC;AAAEM,QAAAA,WAAW,EAAE;AAAf,OAAD,C,uFA7Db,MAKaF,UALb,SAKgCd,SALhC,CAK0C;AAAA;AAAA;AAAA,eAC9BqB,SAD8B,GACD,IADC;;AAAA;;AAAA;;AAyDA;AAzDA,eA2D9BC,mBA3D8B,GA2DC,IA3DD;AAAA,eA4D9BC,SA5D8B,GA4DA,IA5DA;AAAA,eA6D9BL,YA7D8B,GA6DL;AAAA;AAAA,qCA7DK;AAAA,eA8D9BM,oBA9D8B,GA8DC,CA9DD;AAAA;;AAEnB,YAARC,QAAQ,GAAa;AAC5B,cAAI,CAAC,KAAKJ,SAAV,EAAqB;AACjB,iBAAKA,SAAL,GAAiB,KAAKK,IAAL,CAAUC,YAAV,CAAuBxB,QAAvB,KAAoC,KAAKuB,IAAL,CAAUE,YAAV,CAAuBzB,QAAvB,CAArD;AACH;;AACD,iBAAO,KAAKkB,SAAZ;AACH;;AAGkB,YAARQ,QAAQ,CAACC,KAAD,EAAmB;AAClC,eAAKP,SAAL,GAAiBO,KAAjB;AACA,eAAKC,MAAL;AACH;;AACkB,YAARF,QAAQ,GAAqB;AACpC,iBAAO,KAAKN,SAAZ;AACH;;AAGkB,YAARS,QAAQ,GAAW;AAC1B,iBAAO,KAAKd,YAAL,CAAkBe,IAAzB;AACH;;AACkB,YAARD,QAAQ,CAACF,KAAD,EAAgB;AAC/B,eAAKZ,YAAL,CAAkBe,IAAlB,GAAyBH,KAAzB;AACH;;AAGkB,YAARI,QAAQ,GAAG;AAClB,iBAAO,KAAKhB,YAAL,CAAkBgB,QAAzB;AACH;;AACkB,YAARA,QAAQ,CAACJ,KAAD,EAAgB;AAC/B,eAAKZ,YAAL,CAAkBgB,QAAlB,GAA6BJ,KAA7B;AACH;;AAGgB,YAANK,MAAM,GAAG;AAChB,iBAAO,KAAKjB,YAAL,CAAkBiB,MAAzB;AACH;;AACgB,YAANA,MAAM,CAACL,KAAD,EAAgB;AAC7B,eAAKZ,YAAL,CAAkBiB,MAAlB,GAA2BL,KAA3B;AACH;;AAMkB,YAARM,QAAQ,GAAY;AAC3B,iBAAO,KAAKlB,YAAL,CAAkBmB,MAAzB;AACH;;AACkB,YAARD,QAAQ,CAACN,KAAD,EAAiB;AAChC,eAAKZ,YAAL,CAAkBmB,MAAlB,GAA2BP,KAA3B;AACH;;AAaMC,QAAAA,MAAM,GAAG;AACZ,cAAI,CAAC,KAAKR,SAAV,EAAqB;AAErB,gBAAMM,QAAQ,GAAG;AAAA;AAAA,qCAAjB;AACAS,UAAAA,MAAM,CAACC,MAAP,CAAcV,QAAd,EAAwB,KAAKN,SAAL,CAAeiB,IAAvC;AACA,eAAKtB,YAAL,GAAoBW,QAApB;AAEA,eAAKH,IAAL,CAAUe,iBAAV;;AACA,cAAI,KAAKvB,YAAL,IAAqB,KAAKA,YAAL,CAAkBC,MAAlB,CAAyBC,MAAzB,GAAkC,CAA3D,EAA8D;AAC1D,iBAAKF,YAAL,CAAkBC,MAAlB,CAAyBuB,OAAzB,CAAkCC,KAAD,IAAW;AACxC,mBAAKC,QAAL,CAAcD,KAAd;AACH,aAFD;AAGH;;AACD,eAAKE,WAAL;AACH;;AAEMC,QAAAA,IAAI,GAAW;AAClB;AACA,gBAAMC,YAAY,GAAG,KAAKC,uBAAL;AAAA;AAAA,iDAArB;AACA,eAAK9B,YAAL,CAAkBC,MAAlB,GAA2B4B,YAAY,CAACE,GAAb,CAAkBC,MAAD,IAAYA,MAAM,CAACC,SAApC,CAA3B;AACA,iBAAOC,IAAI,CAACC,SAAL,CAAe,KAAKnC,YAApB,EAAkC,IAAlC,EAAwC,CAAxC,CAAP;AACH;;AAEM0B,QAAAA,QAAQ,CAACD,KAAD,EAAmB;AAC9B,gBAAMW,SAAS,GAAG,IAAIxD,IAAJ,EAAlB;AACAwD,UAAAA,SAAS,CAACC,MAAV,GAAmB,KAAK7B,IAAxB;AACA4B,UAAAA,SAAS,CAACE,WAAV,CAAsBb,KAAK,CAACc,CAA5B,EAA+Bd,KAAK,CAACe,CAArC,EAAwC,CAAxC;AAEA,gBAAMC,WAAW,GAAGL,SAAS,CAAC1B,YAAV;AAAA;AAAA,iDAApB;AACA+B,UAAAA,WAAW,CAACR,SAAZ,GAAwBR,KAAxB;AACH;;AAEMiB,QAAAA,WAAW,CAACH,CAAD,EAAYC,CAAZ,EAAuB;AACrC,gBAAMf,KAAK,GAAG;AAAA;AAAA,sCAAcc,CAAd,EAAiBC,CAAjB,CAAd;AACA,eAAKd,QAAL,CAAcD,KAAd;AACA,eAAKE,WAAL;AACH;;AAEMA,QAAAA,WAAW,GAAG;AACjB;AACA,gBAAME,YAAY,GAAG,KAAKC,uBAAL;AAAA;AAAA,iDAArB;AACA,eAAK9B,YAAL,CAAkBC,MAAlB,GAA2B4B,YAAY,CAACE,GAAb,CAAkBC,MAAD,IAAYA,MAAM,CAACC,SAApC,CAA3B;AACH;;AAEOU,QAAAA,QAAQ,GAAG;AACf,gBAAMpC,QAAQ,GAAG,KAAKA,QAAtB;AACAA,UAAAA,QAAQ,CAACqC,KAAT;AAEA,cAAI,KAAK5C,YAAL,CAAkBC,MAAlB,CAAyBC,MAAzB,GAAkC,CAAtC,EAAyC;;AAEzC,gBAAM2C,UAAU,GAAG,KAAK7C,YAAL,CAAkB8C,mBAAlB,CAAsC,IAAtC,CAAnB;;AACA,cAAID,UAAU,CAAC3C,MAAX,GAAoB,CAAxB,EAA2B;AACvB,gBAAI,KAAK6C,YAAT,EAAuB;AACnB,mBAAKC,iBAAL,CAAuBzC,QAAvB,EAAiCsC,UAAjC;AACH,aAFD,MAEO;AACH,mBAAKI,eAAL,CAAqB1C,QAArB,EAA+BsC,UAA/B;AACH,aALsB,CAOvB;;;AACA,gBAAI,KAAKzC,mBAAL,IAA4B,CAAC,KAAKJ,YAAL,CAAkBmB,MAAnD,EAA2D;AACvD,mBAAK+B,sBAAL,CAA4B3C,QAA5B,EAAsCsC,UAAtC;AACH;AACJ;;AAED,cAAI,KAAKE,YAAT,EAAuB;AACnBI,YAAAA,OAAO,CAACC,GAAR,CAAY,4BAAZ,EAA0CP,UAAU,CAAC3C,MAArD;AACH;AACJ;AAED;AACJ;AACA;;;AACY+C,QAAAA,eAAe,CAAC1C,QAAD,EAAqBsC,UAArB,EAA8C;AACjEtC,UAAAA,QAAQ,CAAC8C,WAAT,GAAuB,KAAKC,UAA5B;AACA/C,UAAAA,QAAQ,CAACgD,SAAT,GAAqB,CAArB;AAEAhD,UAAAA,QAAQ,CAACiD,MAAT,CAAgBX,UAAU,CAAC,CAAD,CAAV,CAAcN,CAA9B,EAAiCM,UAAU,CAAC,CAAD,CAAV,CAAcL,CAA/C;;AACA,eAAK,IAAIiB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGZ,UAAU,CAAC3C,MAA/B,EAAuCuD,CAAC,EAAxC,EAA4C;AACxClD,YAAAA,QAAQ,CAACmD,MAAT,CAAgBb,UAAU,CAACY,CAAD,CAAV,CAAclB,CAA9B,EAAiCM,UAAU,CAACY,CAAD,CAAV,CAAcjB,CAA/C;AACH,WAPgE,CASjE;;;AACA,cAAI,KAAKxC,YAAL,CAAkBmB,MAAtB,EAA8B;AAC1BZ,YAAAA,QAAQ,CAACmD,MAAT,CAAgBb,UAAU,CAAC,CAAD,CAAV,CAAcN,CAA9B,EAAiCM,UAAU,CAAC,CAAD,CAAV,CAAcL,CAA/C;AACH;;AAEDjC,UAAAA,QAAQ,CAACoD,MAAT;AACH;AAED;AACJ;AACA;;;AACYX,QAAAA,iBAAiB,CAACzC,QAAD,EAAqBsC,UAArB,EAA8C;AACnEtC,UAAAA,QAAQ,CAACgD,SAAT,GAAqB,CAArB;AAEA,cAAIV,UAAU,CAAC3C,MAAX,GAAoB,CAAxB,EAA2B,OAHwC,CAKnE;;AACA,eAAK,IAAIuD,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGZ,UAAU,CAAC3C,MAAX,GAAoB,CAAxC,EAA2CuD,CAAC,EAA5C,EAAgD;AAC5C,kBAAMG,CAAC,GAAGH,CAAC,IAAIZ,UAAU,CAAC3C,MAAX,GAAoB,CAAxB,CAAX,CAD4C,CACL;AAEvC;;AACA,kBAAM2D,KAAK,GAAG,KAAKC,gBAAL,CAAsBjF,KAAK,CAACkF,KAA5B,EAAmClF,KAAK,CAACmF,GAAzC,EAA8CJ,CAA9C,CAAd;AACArD,YAAAA,QAAQ,CAAC8C,WAAT,GAAuBQ,KAAvB,CAL4C,CAO5C;;AACAtD,YAAAA,QAAQ,CAACiD,MAAT,CAAgBX,UAAU,CAACY,CAAD,CAAV,CAAclB,CAA9B,EAAiCM,UAAU,CAACY,CAAD,CAAV,CAAcjB,CAA/C;AACAjC,YAAAA,QAAQ,CAACmD,MAAT,CAAgBb,UAAU,CAACY,CAAC,GAAG,CAAL,CAAV,CAAkBlB,CAAlC,EAAqCM,UAAU,CAACY,CAAC,GAAG,CAAL,CAAV,CAAkBjB,CAAvD;AACAjC,YAAAA,QAAQ,CAACoD,MAAT;AACH,WAjBkE,CAmBnE;;;AACA,cAAI,KAAK3D,YAAL,CAAkBmB,MAAlB,IAA4B0B,UAAU,CAAC3C,MAAX,GAAoB,CAApD,EAAuD;AACnD,kBAAM+D,SAAS,GAAG,KAAKH,gBAAL,CAAsBjF,KAAK,CAACkF,KAA5B,EAAmClF,KAAK,CAACmF,GAAzC,EAA8C,GAA9C,CAAlB;AACAzD,YAAAA,QAAQ,CAAC8C,WAAT,GAAuBY,SAAvB;AACA1D,YAAAA,QAAQ,CAACiD,MAAT,CAAgBX,UAAU,CAACA,UAAU,CAAC3C,MAAX,GAAoB,CAArB,CAAV,CAAkCqC,CAAlD,EAAqDM,UAAU,CAACA,UAAU,CAAC3C,MAAX,GAAoB,CAArB,CAAV,CAAkCsC,CAAvF;AACAjC,YAAAA,QAAQ,CAACmD,MAAT,CAAgBb,UAAU,CAAC,CAAD,CAAV,CAAcN,CAA9B,EAAiCM,UAAU,CAAC,CAAD,CAAV,CAAcL,CAA/C;AACAjC,YAAAA,QAAQ,CAACoD,MAAT;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACYG,QAAAA,gBAAgB,CAACI,MAAD,EAAgBC,MAAhB,EAA+BP,CAA/B,EAAiD;AACrEA,UAAAA,CAAC,GAAGQ,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYD,IAAI,CAACE,GAAL,CAAS,CAAT,EAAYV,CAAZ,CAAZ,CAAJ,CADqE,CACpC;;AAEjC,gBAAMW,CAAC,GAAGL,MAAM,CAACK,CAAP,GAAW,CAACJ,MAAM,CAACI,CAAP,GAAWL,MAAM,CAACK,CAAnB,IAAwBX,CAA7C;AACA,gBAAMY,CAAC,GAAGN,MAAM,CAACM,CAAP,GAAW,CAACL,MAAM,CAACK,CAAP,GAAWN,MAAM,CAACM,CAAnB,IAAwBZ,CAA7C;AACA,gBAAMa,CAAC,GAAGP,MAAM,CAACO,CAAP,GAAW,CAACN,MAAM,CAACM,CAAP,GAAWP,MAAM,CAACO,CAAnB,IAAwBb,CAA7C;AACA,gBAAMc,CAAC,GAAGR,MAAM,CAACQ,CAAP,GAAW,CAACP,MAAM,CAACO,CAAP,GAAWR,MAAM,CAACQ,CAAnB,IAAwBd,CAA7C;AAEA,iBAAO,IAAI/E,KAAJ,CAAU0F,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmBC,CAAnB,CAAP;AACH;;AAEMC,QAAAA,MAAM,CAACC,GAAD,EAAc;AACvB,gBAAMC,aAAa,GAAG,KAAKrE,IAAL,CAAUsE,QAAV,CAAmB5E,MAAzC;;AACA,cAAI2E,aAAa,KAAK,KAAKvE,oBAA3B,EAAiD;AAC7C,iBAAKA,oBAAL,GAA4BuE,aAA5B;AACH,WAJsB,CAMvB;;;AACA,eAAKrE,IAAL,CAAUuE,QAAV,GAAqB7F,IAAI,CAAC8F,IAA1B;AACA,eAAKrD,WAAL;AACA,eAAKgB,QAAL;AACH;AAED;AACJ;AACA;;;AACYO,QAAAA,sBAAsB,CAAC3C,QAAD,EAAqBsC,UAArB,EAAwC;AAClE,cAAIA,UAAU,CAAC3C,MAAX,GAAoB,CAAxB,EAA2B,OADuC,CAGlE;;AACA,cAAI,KAAKF,YAAL,CAAkBmB,MAAtB,EAA8B,OAJoC,CAMlE;;AACA,gBAAM8D,QAAQ,GAAGpC,UAAU,CAACA,UAAU,CAAC3C,MAAX,GAAoB,CAArB,CAA3B;AACA,cAAIgF,SAAS,GAAGrC,UAAU,CAACA,UAAU,CAAC3C,MAAX,GAAoB,CAArB,CAA1B,CARkE,CAUlE;;AACA,cAAI2C,UAAU,CAAC3C,MAAX,IAAqB,CAAzB,EAA4B;AACxBgF,YAAAA,SAAS,GAAGrC,UAAU,CAACA,UAAU,CAAC3C,MAAX,GAAoB,CAArB,CAAtB;AACH,WAbiE,CAelE;;;AACA,gBAAMiF,SAAS,GAAGf,IAAI,CAACgB,KAAL,CAAWH,QAAQ,CAACzC,CAAT,GAAa0C,SAAS,CAAC1C,CAAlC,EAAqCyC,QAAQ,CAAC1C,CAAT,GAAa2C,SAAS,CAAC3C,CAA5D,CAAlB,CAhBkE,CAkBlE;;AACA,gBAAM8C,WAAW,GAAG,EAApB;AACA,gBAAMC,eAAe,GAAG,EAAxB;AACA,gBAAMC,cAAc,GAAGnB,IAAI,CAACoB,EAAL,GAAU,CAAjC,CArBkE,CAqB9B;AAEpC;;AACAjF,UAAAA,QAAQ,CAAC8C,WAAT,GAAuBxE,KAAK,CAACmF,GAA7B;AACAzD,UAAAA,QAAQ,CAACkF,SAAT,GAAqB5G,KAAK,CAACmF,GAA3B;AACAzD,UAAAA,QAAQ,CAACgD,SAAT,GAAqB,CAArB,CA1BkE,CA4BlE;;AACA,gBAAMmC,WAAW,GAAGT,QAAQ,CAAC1C,CAA7B;AACA,gBAAMoD,WAAW,GAAGV,QAAQ,CAACzC,CAA7B,CA9BkE,CAgClE;;AACA,gBAAMoD,SAAS,GAAGF,WAAW,GAAGtB,IAAI,CAACyB,GAAL,CAASV,SAAT,IAAsBE,WAAtD;AACA,gBAAMS,SAAS,GAAGH,WAAW,GAAGvB,IAAI,CAAC2B,GAAL,CAASZ,SAAT,IAAsBE,WAAtD,CAlCkE,CAoClE;;AACA9E,UAAAA,QAAQ,CAACiD,MAAT,CAAgBkC,WAAhB,EAA6BC,WAA7B;AACApF,UAAAA,QAAQ,CAACmD,MAAT,CAAgBkC,SAAhB,EAA2BE,SAA3B;AACAvF,UAAAA,QAAQ,CAACoD,MAAT,GAvCkE,CAyClE;;AACA,gBAAMqC,KAAK,GAAGJ,SAAS,GAAGxB,IAAI,CAACyB,GAAL,CAASV,SAAS,GAAGI,cAArB,IAAuCD,eAAjE;AACA,gBAAMW,KAAK,GAAGH,SAAS,GAAG1B,IAAI,CAAC2B,GAAL,CAASZ,SAAS,GAAGI,cAArB,IAAuCD,eAAjE;AACA,gBAAMY,MAAM,GAAGN,SAAS,GAAGxB,IAAI,CAACyB,GAAL,CAASV,SAAS,GAAGI,cAArB,IAAuCD,eAAlE;AACA,gBAAMa,MAAM,GAAGL,SAAS,GAAG1B,IAAI,CAAC2B,GAAL,CAASZ,SAAS,GAAGI,cAArB,IAAuCD,eAAlE,CA7CkE,CA+ClE;;AACA/E,UAAAA,QAAQ,CAACiD,MAAT,CAAgBoC,SAAhB,EAA2BE,SAA3B;AACAvF,UAAAA,QAAQ,CAACmD,MAAT,CAAgBsC,KAAhB,EAAuBC,KAAvB;AACA1F,UAAAA,QAAQ,CAACmD,MAAT,CAAgBwC,MAAhB,EAAwBC,MAAxB;AACA5F,UAAAA,QAAQ,CAAC6F,KAAT;AACA7F,UAAAA,QAAQ,CAAC8F,IAAT;AACA9F,UAAAA,QAAQ,CAACoD,MAAT;AACH;;AA/QqC,O;;;;;iBAsDX9E,KAAK,CAACyH,K;;;;;;;iBAGF,K", "sourcesContent": ["import { _decorator, Node, Color, Component, JsonAsset, CCInteger, Graphics, Vec3 } from 'cc';\r\nconst { ccclass, executeInEditMode, property, disallowMultiple, menu, requireComponent } = _decorator;\r\nimport { EDITOR } from 'cc/env';\r\nimport { PathData, PathPoint } from 'db://assets/bundles/common/script/game/data/PathData';\r\nimport { PathPointEditor } from './PathPointEditor';\r\n\r\n@ccclass('PathEditor')\r\n@menu(\"怪物/编辑器/路径编辑\")\r\n@requireComponent(Graphics)\r\n@executeInEditMode(true)\r\n@disallowMultiple(true)\r\nexport class PathEditor extends Component {\r\n    private _graphics: Graphics | null = null;\r\n    public get graphics(): Graphics {\r\n        if (!this._graphics) {\r\n            this._graphics = this.node.getComponent(Graphics) || this.node.addComponent(Graphics);\r\n        }\r\n        return this._graphics;\r\n    }\r\n\r\n    @property({ type: JsonAsset, displayName: \"路径数据\" })\r\n    public set pathData(value: JsonAsset) {\r\n        this._pathData = value;\r\n        this.reload();\r\n    }\r\n    public get pathData(): JsonAsset | null {\r\n        return this._pathData;\r\n    }\r\n\r\n    @property({ displayName: \"路径名称\" })\r\n    public get pathName(): string {\r\n        return this._pathDataObj.name;\r\n    }\r\n    public set pathName(value: string) {\r\n        this._pathDataObj.name = value;\r\n    }\r\n\r\n    @property({ type: CCInteger, displayName: '起始点'})\r\n    public get startIdx() {\r\n        return this._pathDataObj.startIdx;\r\n    }\r\n    public set startIdx(value: number) {\r\n        this._pathDataObj.startIdx = value;\r\n    }\r\n\r\n    @property({ type: CCInteger, displayName: '结束点(-1代表默认最后个点)'})\r\n    public get endIdx() {\r\n        return this._pathDataObj.endIdx;\r\n    }\r\n    public set endIdx(value: number) {\r\n        this._pathDataObj.endIdx = value;\r\n    }\r\n\r\n    @property({ displayName: \"是否闭合\", visible() {\r\n        // @ts-ignore\r\n        return this._pathDataObj.points.length >= 3;\r\n    }})\r\n    public get isClosed(): boolean {\r\n        return this._pathDataObj.closed;\r\n    }\r\n    public set isClosed(value: boolean) {\r\n        this._pathDataObj.closed = value;\r\n    }\r\n\r\n    @property({ displayName: \"曲线颜色\" })\r\n    public curveColor: Color = Color.WHITE;\r\n\r\n    @property({ displayName: \"显示细分线段\" })\r\n    public showSegments: boolean = false; // 是否使用不同颜色来绘制不同的细分线段\r\n\r\n    private _showDirectionArrow: boolean = true;\r\n    private _pathData: JsonAsset | null = null;\r\n    private _pathDataObj: PathData = new PathData();\r\n    private _cachedChildrenCount: number = 0;\r\n\r\n    public reload() {\r\n        if (!this._pathData) return;\r\n\r\n        const pathData = new PathData();\r\n        Object.assign(pathData, this._pathData.json);\r\n        this._pathDataObj = pathData;\r\n\r\n        this.node.removeAllChildren();\r\n        if (this._pathDataObj && this._pathDataObj.points.length > 0) {\r\n            this._pathDataObj.points.forEach((point) => {\r\n                this.addPoint(point);\r\n            });\r\n        }\r\n        this.updateCurve();\r\n    }\r\n\r\n    public save(): string {\r\n        // 收集所有路径点数据\r\n        const pointEditors = this.getComponentsInChildren(PathPointEditor);\r\n        this._pathDataObj.points = pointEditors.map((editor) => editor.pathPoint);\r\n        return JSON.stringify(this._pathDataObj, null, 2);\r\n    }\r\n\r\n    public addPoint(point: PathPoint) {\r\n        const pointNode = new Node();\r\n        pointNode.parent = this.node;\r\n        pointNode.setPosition(point.x, point.y, 0);\r\n\r\n        const pointEditor = pointNode.addComponent(PathPointEditor);\r\n        pointEditor.pathPoint = point;\r\n    }\r\n\r\n    public addNewPoint(x: number, y: number) {\r\n        const point = new PathPoint(x, y);\r\n        this.addPoint(point);\r\n        this.updateCurve();\r\n    }\r\n\r\n    public updateCurve() {\r\n        // 收集当前所有点的数据\r\n        const pointEditors = this.getComponentsInChildren(PathPointEditor);\r\n        this._pathDataObj.points = pointEditors.map((editor) => editor.pathPoint);\r\n    }\r\n\r\n    private drawPath() {\r\n        const graphics = this.graphics;\r\n        graphics.clear();\r\n\r\n        if (this._pathDataObj.points.length < 2) return;\r\n\r\n        const subdivided = this._pathDataObj.getSubdividedPoints(true);\r\n        if (subdivided.length > 1) {\r\n            if (this.showSegments) {\r\n                this.drawSegmentedPath(graphics, subdivided);\r\n            } else {\r\n                this.drawUniformPath(graphics, subdivided);\r\n            }\r\n\r\n            // 绘制路径终点的方向箭头（仅对非闭合路径）\r\n            if (this._showDirectionArrow && !this._pathDataObj.closed) {\r\n                this.drawPathDirectionArrow(graphics, subdivided);\r\n            }\r\n        }\r\n        \r\n        if (this.showSegments) {\r\n            console.log('subdivided points length: ', subdivided.length);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 绘制统一颜色的路径\r\n     */\r\n    private drawUniformPath(graphics: Graphics, subdivided: PathPoint[]) {\r\n        graphics.strokeColor = this.curveColor;\r\n        graphics.lineWidth = 5;\r\n\r\n        graphics.moveTo(subdivided[0].x, subdivided[0].y);\r\n        for (let i = 1; i < subdivided.length; i++) {\r\n            graphics.lineTo(subdivided[i].x, subdivided[i].y);\r\n        }\r\n\r\n        // 如果是闭合路径，连接回起点\r\n        if (this._pathDataObj.closed) {\r\n            graphics.lineTo(subdivided[0].x, subdivided[0].y);\r\n        }\r\n\r\n        graphics.stroke();\r\n    }\r\n\r\n    /**\r\n     * 绘制分段着色的路径 - 每个细分段用不同颜色\r\n     */\r\n    private drawSegmentedPath(graphics: Graphics, subdivided: PathPoint[]) {\r\n        graphics.lineWidth = 5;\r\n\r\n        if (subdivided.length < 2) return;\r\n\r\n        // 为每个细分段绘制不同的颜色\r\n        for (let i = 0; i < subdivided.length - 1; i++) {\r\n            const t = i / (subdivided.length - 1); // 计算当前位置的归一化参数 [0,1]\r\n\r\n            // 从绿色到红色的颜色插值\r\n            const color = this.interpolateColor(Color.GREEN, Color.RED, t);\r\n            graphics.strokeColor = color;\r\n\r\n            // 绘制当前段\r\n            graphics.moveTo(subdivided[i].x, subdivided[i].y);\r\n            graphics.lineTo(subdivided[i + 1].x, subdivided[i + 1].y);\r\n            graphics.stroke();\r\n        }\r\n\r\n        // 如果是闭合路径，绘制最后一段回到起点\r\n        if (this._pathDataObj.closed && subdivided.length > 2) {\r\n            const lastColor = this.interpolateColor(Color.GREEN, Color.RED, 1.0);\r\n            graphics.strokeColor = lastColor;\r\n            graphics.moveTo(subdivided[subdivided.length - 1].x, subdivided[subdivided.length - 1].y);\r\n            graphics.lineTo(subdivided[0].x, subdivided[0].y);\r\n            graphics.stroke();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 颜色插值函数\r\n     * @param color1 起始颜色\r\n     * @param color2 结束颜色\r\n     * @param t 插值参数 [0,1]\r\n     */\r\n    private interpolateColor(color1: Color, color2: Color, t: number): Color {\r\n        t = Math.max(0, Math.min(1, t)); // 确保t在[0,1]范围内\r\n\r\n        const r = color1.r + (color2.r - color1.r) * t;\r\n        const g = color1.g + (color2.g - color1.g) * t;\r\n        const b = color1.b + (color2.b - color1.b) * t;\r\n        const a = color1.a + (color2.a - color1.a) * t;\r\n\r\n        return new Color(r, g, b, a);\r\n    }\r\n\r\n    public update(_dt: number) {\r\n        const childrenCount = this.node.children.length;\r\n        if (childrenCount !== this._cachedChildrenCount) {\r\n            this._cachedChildrenCount = childrenCount;\r\n        }\r\n\r\n        // 把自己的位置锁定在原点，避免出现绘制偏移\r\n        this.node.position = Vec3.ZERO;\r\n        this.updateCurve();\r\n        this.drawPath();\r\n    }\r\n\r\n    /**\r\n     * 绘制路径方向箭头\r\n     */\r\n    private drawPathDirectionArrow(graphics: Graphics, subdivided: any[]) {\r\n        if (subdivided.length < 2) return;\r\n\r\n        // 如果是闭合路径，不绘制箭头（因为没有明确的终点）\r\n        if (this._pathDataObj.closed) return;\r\n\r\n        // 计算终点的方向（使用最后几个点来获得更准确的方向）\r\n        const endPoint = subdivided[subdivided.length - 1];\r\n        let prevPoint = subdivided[subdivided.length - 2];\r\n\r\n        // 如果有足够的点，使用更远的点来计算方向，获得更平滑的方向\r\n        if (subdivided.length >= 5) {\r\n            prevPoint = subdivided[subdivided.length - 5];\r\n        }\r\n\r\n        // 计算方向角度\r\n        const direction = Math.atan2(endPoint.y - prevPoint.y, endPoint.x - prevPoint.x);\r\n\r\n        // 箭头参数\r\n        const arrowLength = 40;\r\n        const arrowHeadLength = 20;\r\n        const arrowHeadAngle = Math.PI / 5; // 36度，更尖锐的箭头\r\n\r\n        // 设置箭头样式\r\n        graphics.strokeColor = Color.RED;\r\n        graphics.fillColor = Color.RED;\r\n        graphics.lineWidth = 3;\r\n\r\n        // 计算箭头起点（从路径终点开始）\r\n        const arrowStartX = endPoint.x;\r\n        const arrowStartY = endPoint.y;\r\n\r\n        // 计算箭头终点\r\n        const arrowEndX = arrowStartX + Math.cos(direction) * arrowLength;\r\n        const arrowEndY = arrowStartY + Math.sin(direction) * arrowLength;\r\n\r\n        // 绘制箭头主线\r\n        graphics.moveTo(arrowStartX, arrowStartY);\r\n        graphics.lineTo(arrowEndX, arrowEndY);\r\n        graphics.stroke();\r\n\r\n        // 绘制箭头头部（填充三角形）\r\n        const leftX = arrowEndX - Math.cos(direction - arrowHeadAngle) * arrowHeadLength;\r\n        const leftY = arrowEndY - Math.sin(direction - arrowHeadAngle) * arrowHeadLength;\r\n        const rightX = arrowEndX - Math.cos(direction + arrowHeadAngle) * arrowHeadLength;\r\n        const rightY = arrowEndY - Math.sin(direction + arrowHeadAngle) * arrowHeadLength;\r\n\r\n        // 绘制填充的箭头头部\r\n        graphics.moveTo(arrowEndX, arrowEndY);\r\n        graphics.lineTo(leftX, leftY);\r\n        graphics.lineTo(rightX, rightY);\r\n        graphics.close();\r\n        graphics.fill();\r\n        graphics.stroke();\r\n    }\r\n}"]}