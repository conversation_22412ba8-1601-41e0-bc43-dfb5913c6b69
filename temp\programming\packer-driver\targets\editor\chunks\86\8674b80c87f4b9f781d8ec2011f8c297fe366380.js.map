{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>"], "names": ["_decorator", "Node", "MyApp", "ButtonPlus", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "logError", "BundleName", "HomeUIBaseSystem", "EventMgr", "startGameByMode", "GameEnum", "FriendUI", "MailUI", "PKUI", "StoryUI", "TaskTipUI", "RogueUI", "ccclass", "property", "HomeUI", "_isInit", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Background", "getBundleName", "Home", "onLoad", "btnBattle", "addClick", "onBattleClick", "btnStory", "onStoryClick", "btnPK", "onPKClick", "btnRogue", "onRogueClick", "nodeBaseSystem", "getComponentsInChildren", "for<PERSON>ach", "btn", "onBaseSystemClick", "init", "resMgr", "loadBundle", "HomeTask", "then", "openUI", "onShow", "args", "onHide", "onClose", "targetOff", "onDestroy", "unscheduleAllCallbacks", "update", "dt", "globalMgr", "chapterID", "onBattle", "GameModeId", "ENDLESS", "evt", "nodeName", "target", "name", "Social", "Announcement", "Friend", "Mail", "Setting"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAwBC,MAAAA,I,OAAAA,I;;AACxBC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,gB,iBAAAA,gB;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,e,iBAAAA,e;;AACAC,MAAAA,Q,kBAAAA,Q;;AACAC,MAAAA,Q,kBAAAA,Q;;AACAC,MAAAA,M,kBAAAA,M;;AACAC,MAAAA,I,kBAAAA,I;;AACAC,MAAAA,O,kBAAAA,O;;AACAC,MAAAA,S,kBAAAA,S;;AACAC,MAAAA,O,kBAAAA,O;;;;;;;;;OAGH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBpB,U;;wBAEjBqB,M,WADZF,OAAO,CAAC,QAAD,C,UAKHC,QAAQ,CAACnB,IAAD,C,UAGRmB,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,mC,2BAdb,MACaC,MADb;AAAA;AAAA,4BACmC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAevBC,OAfuB,GAeJ,KAfI;AAAA;;AACX,eAANC,MAAM,GAAW;AAAE,iBAAO,kBAAP;AAA4B;;AACvC,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,UAAf;AAA2B;;AACpC,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,IAAlB;AAAwB;;AActDC,QAAAA,MAAM,GAAS;AACrB,eAAKC,SAAL,CAAgBC,QAAhB,CAAyB,KAAKC,aAA9B,EAA6C,IAA7C;AACA,eAAKC,QAAL,CAAeF,QAAf,CAAwB,KAAKG,YAA7B,EAA2C,IAA3C;AACA,eAAKC,KAAL,CAAYJ,QAAZ,CAAqB,KAAKK,SAA1B,EAAqC,IAArC;AACA,eAAKC,QAAL,CAAeN,QAAf,CAAwB,KAAKO,YAA7B,EAA2C,IAA3C;AACA,eAAKC,cAAL,CAAqBC,uBAArB;AAAA;AAAA,wCAAyDC,OAAzD,CAAkEC,GAAD,IAAS;AACtEA,YAAAA,GAAG,CAACX,QAAJ,CAAa,KAAKY,iBAAlB,EAAqC,IAArC;AACH,WAFD;AAGH;;AAES,cAAJC,IAAI,GAAG;AACT,cAAI,KAAKrB,OAAT,EAAkB,OADT,CAET;;AACA;AAAA;AAAA,8BAAMsB,MAAN,CAAaC,UAAb,CAAwB;AAAA;AAAA,wCAAWC,QAAnC,EAA6CC,IAA7C,CAAkD,YAAY;AAC1D;AAAA;AAAA,gCAAMC,MAAN;AAAA;AAAA;AACH,WAFD;AAGA,eAAK1B,OAAL,GAAe,IAAf;AACH;;AAEW,cAAN2B,MAAM,CAAC,GAAGC,IAAJ,EAAgC;AACxC,gBAAM,KAAKP,IAAL,EAAN;AACH;;AACW,cAANQ,MAAM,CAAC,GAAGD,IAAJ,EAAgC,CAC3C;;AACY,cAAPE,OAAO,CAAC,GAAGF,IAAJ,EAAgC;AACzC;AAAA;AAAA,oCAASG,SAAT,CAAmB,IAAnB;AACH;;AACSC,QAAAA,SAAS,GAAS;AACxB,eAAKC,sBAAL;AACH;;AACSC,QAAAA,MAAM,CAACC,EAAD,EAAmB,CAClC;;AAEkB,cAAb1B,aAAa,GAAG;AAClB;AAAA;AAAA,8BAAM2B,SAAN,CAAgBC,SAAhB,GAA4B,CAA5B;AACA,eAAKC,QAAL;AACH;;AAEa,cAARA,QAAQ,GAAG;AACb;AAAA;AAAA,kDAAgB;AAAA;AAAA,oCAASC,UAAT,CAAoBC,OAApC;AACH;;AAEiB,cAAZ7B,YAAY,GAAG;AACjB,gBAAM;AAAA;AAAA,8BAAMe,MAAN;AAAA;AAAA,iCAAN;AACH;;AAEc,cAATb,SAAS,GAAG;AACd,gBAAM;AAAA;AAAA,8BAAMa,MAAN;AAAA;AAAA,2BAAN;AACH;;AAEiB,cAAZX,YAAY,GAAG;AACjB,gBAAM;AAAA;AAAA,8BAAMW,MAAN;AAAA;AAAA,iCAAN;AACH;;AAEON,QAAAA,iBAAiB,CAACqB,GAAD,EAAkB;AACvC,gBAAMC,QAAQ,GAAGD,GAAG,CAACE,MAAJ,CAAWC,IAA5B;;AACA,kBAAQF,QAAR;AACI,iBAAK;AAAA;AAAA,sDAAiBG,MAAtB;AACI;;AACJ,iBAAK;AAAA;AAAA,sDAAiBC,YAAtB;AACI;;AACJ,iBAAK;AAAA;AAAA,sDAAiBC,MAAtB;AACI;AAAA;AAAA,kCAAMrB,MAAN;AAAA;AAAA;AACA;;AACJ,iBAAK;AAAA;AAAA,sDAAiBsB,IAAtB;AACI;AAAA;AAAA,kCAAMtB,MAAN;AAAA;AAAA;AACA;;AACJ,iBAAK;AAAA;AAAA,sDAAiBuB,OAAtB;AACI;;AACJ;AACI;AAAA;AAAA,wCAAS,QAAT,EAAoB,qDAAoDP,QAAS,EAAjF;AACA;AAfR;AAiBH;;AA1F8B,O;;;;;iBAKD,I;;;;;;;iBAGC,I;;;;;;;iBAED,I;;;;;;;iBAEH,I;;;;;;;iBAEG,I", "sourcesContent": ["import { _decorator, EventTouch, Node } from 'cc';\nimport { MyApp } from 'db://assets/bundles/common/script/app/MyApp';\nimport { ButtonPlus } from 'db://assets/bundles/common/script/ui/common/components/button/ButtonPlus';\nimport { BaseUI, UILayer, UIMgr } from \"db://assets/scripts/core/base/UIMgr\";\nimport { logError } from 'db://assets/scripts/utils/Logger';\nimport { BundleName } from '../../const/BundleConst';\nimport { HomeUIBaseSystem } from '../../const/HomeUIConst';\nimport { EventMgr } from '../../event/EventManager';\nimport { startGameByMode } from '../../game/GameInsStart';\nimport { GameEnum } from '../../game/const/GameEnum';\nimport { FriendUI } from '../friend/FriendUI';\nimport { MailUI } from '../mail/MailUI';\nimport { PK<PERSON> } from '../pk/PKUI';\nimport { StoryUI } from '../story/StoryUI';\nimport { TaskTipUI } from '../task/TaskTipUI';\nimport { RogueUI } from './fight/RogueUI';\n\n\nconst { ccclass, property } = _decorator;\n@ccclass('HomeUI')\nexport class HomeUI extends BaseUI {\n    public static getUrl(): string { return \"prefab/ui/HomeUI\"; }\n    public static getLayer(): UILayer { return UILayer.Background }\n    public static getBundleName(): string { return BundleName.Home }\n    @property(Node)\n    nodeBaseSystem: Node | null = null;\n\n    @property(ButtonPlus)\n    btnBattle: ButtonPlus | null = null;\n    @property(ButtonPlus)\n    btnStory: ButtonPlus | null = null;\n    @property(ButtonPlus)\n    btnPK: ButtonPlus | null = null;\n    @property(ButtonPlus)\n    btnRogue: ButtonPlus | null = null;\n    private _isInit: boolean = false;\n\n    protected onLoad(): void {\n        this.btnBattle!.addClick(this.onBattleClick, this);\n        this.btnStory!.addClick(this.onStoryClick, this);\n        this.btnPK!.addClick(this.onPKClick, this);\n        this.btnRogue!.addClick(this.onRogueClick, this);\n        this.nodeBaseSystem!.getComponentsInChildren(ButtonPlus).forEach((btn) => {\n            btn.addClick(this.onBaseSystemClick, this);\n        })\n    }\n\n    async init() {\n        if (this._isInit) return\n        //异步加载初始化界面内的ui\n        MyApp.resMgr.loadBundle(BundleName.HomeTask).then(async () => {\n            UIMgr.openUI(TaskTipUI)\n        })\n        this._isInit = true;\n    }\n\n    async onShow(...args: any[]): Promise<void> {\n        await this.init();\n    }\n    async onHide(...args: any[]): Promise<void> {\n    }\n    async onClose(...args: any[]): Promise<void> {\n        EventMgr.targetOff(this)\n    }\n    protected onDestroy(): void {\n        this.unscheduleAllCallbacks();\n    }\n    protected update(dt: number): void {\n    }\n\n    async onBattleClick() {\n        MyApp.globalMgr.chapterID = 0;\n        this.onBattle();\n    }\n\n    async onBattle() {\n        startGameByMode(GameEnum.GameModeId.ENDLESS);\n    }\n\n    async onStoryClick() {\n        await UIMgr.openUI(StoryUI);\n    }\n\n    async onPKClick() {\n        await UIMgr.openUI(PKUI);\n    }\n\n    async onRogueClick() {\n        await UIMgr.openUI(RogueUI);\n    }\n\n    private onBaseSystemClick(evt: EventTouch) {\n        const nodeName = evt.target.name\n        switch (nodeName) {\n            case HomeUIBaseSystem.Social:\n                break;\n            case HomeUIBaseSystem.Announcement:\n                break;\n            case HomeUIBaseSystem.Friend:\n                UIMgr.openUI(FriendUI)\n                break;\n            case HomeUIBaseSystem.Mail:\n                UIMgr.openUI(MailUI)\n                break;\n            case HomeUIBaseSystem.Setting:\n                break;\n            default:\n                logError(\"HomeUI\", `onBaseSystemClick nodeName not found click handler${nodeName}`)\n                break;\n        }\n    }\n}\n\n"]}