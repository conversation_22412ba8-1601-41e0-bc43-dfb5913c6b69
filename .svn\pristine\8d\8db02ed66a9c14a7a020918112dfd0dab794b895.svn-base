import { _decorator, Component, instantiate, isValid, Material, MeshRenderer, Node, Prefab, sp, UITransform } from 'cc';
import { MyApp } from 'db://assets/bundles/common/script/app/MyApp';
import { PlaneData } from '../data/plane/PlaneData';
import { StateCallback, StateEvent } from '../plane/StateDefine';
import { StateMachine } from '../plane/StateMachine';
const { ccclass, property } = _decorator;

@ccclass('Plane')
export class Plane extends Component {

    @property(sp.Skeleton)
    spine: sp.Skeleton | null = null
    private _prefabeNode: Node | null = null;

    _planeData: PlaneData | null = null;//飞机数据
    _curEvent: StateEvent = StateEvent.IDLE;//当前动画名称
    _aniCallFunc: StateCallback | undefined = undefined;//动画回调
    _isInit: boolean = false;//是否初始化完成
    _stateMachine =  new StateMachine();
    _hurtActDuration = 0.2; // 受伤动画持续时间

    protected onLoad(): void {
        // if (!this.spine) {
        //     this.spine = this.getComponentInChildren(sp.Skeleton);
        // }
    }

    protected update(dt: number): void {
        if (this._hurtActDuration > 0) {
            this._hurtActDuration -= dt;
            if (this._hurtActDuration <= 0) {
                this.resetRoleEffect();
            }
        }
    }

    reset() {
        this._stateMachine.reset();
        this._curEvent = StateEvent.IDLE;
        this.resetRoleEffect();
        this._prefabeNode?.removeFromParent()
        this._prefabeNode = null
    }

    initPlane(planeData: PlaneData) {
        // 初始化飞机数据
        this._planeData = planeData;
        this._isInit = false;
        let path = this._planeData.recoursePrefab;
        MyApp.resMgr.loadAsync(path, Prefab).then((prefab) => {
            this._prefabeNode = instantiate(prefab)
            this.node.addChild(this._prefabeNode)
            this.spine = this._prefabeNode.getComponentInChildren(sp.Skeleton);
            if (this.spine) {
                this.spine.premultipliedAlpha = false;
                this._isInit = true;
                this.spine.node.setPosition(0, -this.spine.node.getComponent(UITransform)!.height / 2);
                this.setPlaneState(this._curEvent, this._aniCallFunc);
            }
            this._stateMachine.initializeStateMachine(this.spine!);
        });
    }

    // 播放闪白动画
    async playFlashAnim() {
        if (!this.spine) {
            return
        }
        let material = await MyApp.resMgr.loadAsync("effect/flash/flash", Material);
        if (material){
            this.spine.customMaterial = material;
        }
        this._hurtActDuration = 0.2;
    }

    resetRoleEffect(){
        this._hurtActDuration = 0;
        if (!this.spine) {
            return
        }
        this.spine!.customMaterial = null;
    }


    setPlaneState(event: StateEvent,callback?: StateCallback):boolean {
        if (!this._isInit) {
            this._curEvent = event;
            this._aniCallFunc = callback;
            return false;
        }
        return this._stateMachine.handleEvent(event,callback);
    }
    
    // 移动命令（通用移动）
    public onEnter(callback?: StateCallback) {
        return this.setPlaneState(StateEvent.ENTER, callback);
    }

    // 移动命令
    public onMoveCommand(isLeft:boolean = false, callback?: StateCallback) {
        let event = isLeft ? StateEvent.MOVE_LEFT_COMMAND : StateEvent.MOVE_RIGHT_COMMAND;
        return this.setPlaneState(event, callback);
    }

    // 移动结束
    public onMoveEnd(callback?: StateCallback) {
        return this.setPlaneState(StateEvent.MOVE_END, callback);
    }

    // 攻击命令
    public onAttackCommand(callback?: StateCallback) {
        return this.setPlaneState(StateEvent.ATTACK_COMMAND, callback);
    }

    // 受击事件
    public onGetHit(callback?: StateCallback) {
        this.playFlashAnim();
        return this.setPlaneState(StateEvent.GET_HIT, callback);
    }

    // 死亡事件
    public onDie(callback?: StateCallback) {
        return this.setPlaneState(StateEvent.DIE, callback);
    }

    public onDodgeCommand(callback?: StateCallback) {
        return this.setPlaneState(StateEvent.DODGE_COMMAND, callback);
    }

    setAnimSpeed(speed: number) {
        if (this.spine && isValid(this.spine)){
            this.spine.timeScale = speed;
        }
    }
}


