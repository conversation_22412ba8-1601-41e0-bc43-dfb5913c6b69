# PathMove 匀加速直线运动实现

## 概述

这是一个全新的PathMove实现，基于匀加速直线运动原理。每个路径段都被视为一个独立的匀加速直线运动，确保物体严格按照路径移动，同时准确处理速度变化。

## 核心原理

### 匀加速直线运动公式

对于每个路径段（从点A到点B）：
- 初速度：v₀ = A点的速度
- 末速度：v₁ = B点的速度  
- 距离：s = |AB|
- 加速度：a = (v₁² - v₀²) / (2s)

### 运动更新公式

每帧更新：
- 速度：v = v₀ + a×t
- 位移：Δs = v₀×Δt + 0.5×a×Δt²

## 实现特点

### 1. 精确轨迹跟踪
- 物体严格沿着细分后的路径点移动
- 每个段都是直线运动，确保不偏离轨迹
- 使用距离检测（<1像素）判断到达目标点

### 2. 准确的速度控制
- 基于物理公式计算加速度
- 保证在每个路径点达到指定速度
- 支持加速、减速和匀速运动

### 3. 停留功能
- 到达有stayDuration的点时自动停留
- 停留期间暂停移动，但保持倾斜效果
- 停留结束后自动继续移动

### 4. 倾斜效果
- 独立的倾斜计算，不影响主要移动轨迹
- 垂直于移动方向的正弦波偏移
- 可配置频率和幅度

## 关键方法

### setNext(pathPointIndex)
设置下一个目标点，计算运动参数：
```typescript
// 计算移动方向和距离
const direction = Vec2.subtract(new Vec2(), nextPoint.position, currentPoint.position);
const distance = direction.length();

// 计算移动角度
this.speedAngle = Math.atan2(direction.y, direction.x) * 180 / Math.PI;

// 计算加速度
const v0 = currentPoint.speed;
const v1 = nextPoint.speed;
this.acceleration = (v1 * v1 - v0 * v0) / (2 * distance);

// 设置初始速度
this.speed = v0;
```

### tickMovement(dt)
每帧更新运动状态：
```typescript
// 更新速度和位置
const deltaV = this.acceleration * dt;
const deltaS = this.speed * dt + 0.5 * this.acceleration * dt * dt;
this.speed += deltaV;

// 计算移动向量
const angleRad = this.speedAngle * Math.PI / 180;
const deltaX = Math.cos(angleRad) * deltaS;
const deltaY = Math.sin(angleRad) * deltaS;

// 更新位置
this._currentPosition.x += deltaX;
this._currentPosition.y += deltaY;
```

### onReachPoint(pointIndex)
到达路径点时的处理：
```typescript
// 精确设置位置
const point = this.getPathPoint(pointIndex);
this._currentPosition.x = point.position.x;
this._currentPosition.y = point.position.y;

// 检查停留
if (point.stayDuration > 0) {
    this._isStaying = true;
    this._stayTimer = point.stayDuration / 1000.0;
} else {
    this.moveToNextPoint();
}
```

## 使用方法

### 基本使用
```typescript
const pathMove = this.getComponent(PathMove);
pathMove.setPath(pathData);
pathMove.setMovable(true);
```

### 控制移动
```typescript
// 暂停/恢复移动
pathMove.setMovable(false);
pathMove.setMovable(true);

// 重置到起点
pathMove.resetToStartPublic();

// 强制结束停留
pathMove.endStay();
```

### 查询状态
```typescript
// 检查是否在停留
if (pathMove.isStaying()) {
    const remainingTime = pathMove.getRemainingStayTime();
    console.log(`剩余停留时间: ${remainingTime}秒`);
}

// 获取详细调试信息
const debugInfo = pathMove.getDebugInfo();
console.log(debugInfo);
```

## 优势

1. **物理准确性**: 基于真实的物理公式，运动更自然
2. **轨迹精确性**: 严格按照路径移动，不会偏离
3. **速度控制**: 准确达到每个路径点的指定速度
4. **性能优化**: 简单的数学计算，性能优秀
5. **易于调试**: 清晰的状态管理和调试接口

## 注意事项

1. **路径点顺序**: 确保路径点按正确顺序排列
2. **速度设置**: 避免相邻点速度差异过大，可能导致极大加速度
3. **停留检测**: 使用1像素容差检测到达，适合大多数情况
4. **循环路径**: 支持loop模式，到达终点后回到起点

这个实现解决了原有基于距离插值方法的问题，特别是在速度变化较大时的轨迹偏移问题。
