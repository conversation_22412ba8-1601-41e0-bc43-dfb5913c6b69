System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, find, isValid, Label, Node, Tween, tween, UIOpacity, UITransform, EventManager, GameEvent, GameIns, UIMgr, GamePauseUI, getI18StrByKey, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _dec9, _dec10, _dec11, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _descriptor8, _descriptor9, _descriptor10, _class3, _crd, ccclass, property, GameFightUI;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfEventManager(extras) {
    _reporterNs.report("EventManager", "../../../event/EventManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEvent(extras) {
    _reporterNs.report("GameEvent", "../../event/GameEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGamePauseUI(extras) {
    _reporterNs.report("GamePauseUI", "../../../ui/gameui/game/GamePauseUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfgetI18StrByKey(extras) {
    _reporterNs.report("getI18StrByKey", "db://i18n/LanguageData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneBase(extras) {
    _reporterNs.report("PlaneBase", "../plane/PlaneBase", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      find = _cc.find;
      isValid = _cc.isValid;
      Label = _cc.Label;
      Node = _cc.Node;
      Tween = _cc.Tween;
      tween = _cc.tween;
      UIOpacity = _cc.UIOpacity;
      UITransform = _cc.UITransform;
    }, function (_unresolved_2) {
      EventManager = _unresolved_2.default;
    }, function (_unresolved_3) {
      GameEvent = _unresolved_3.GameEvent;
    }, function (_unresolved_4) {
      GameIns = _unresolved_4.GameIns;
    }, function (_unresolved_5) {
      UIMgr = _unresolved_5.UIMgr;
    }, function (_unresolved_6) {
      GamePauseUI = _unresolved_6.GamePauseUI;
    }, function (_unresolved_7) {
      getI18StrByKey = _unresolved_7.getI18StrByKey;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "823f66j9zBN6pId5BnhnjrY", "GameFightUI", undefined);

      __checkObsolete__(['_decorator', 'Component', 'find', 'isValid', 'Label', 'Node', 'Tween', 'tween', 'UIOpacity', 'UITransform']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("GameFightUI", GameFightUI = (_dec = ccclass('GameFightUI'), _dec2 = property(Node), _dec3 = property(Node), _dec4 = property(Node), _dec5 = property(Node), _dec6 = property(Node), _dec7 = property(Label), _dec8 = property(Label), _dec9 = property(Label), _dec10 = property(Node), _dec11 = property(Node), _dec(_class = (_class2 = (_class3 = class GameFightUI extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "tipNode", _descriptor, this);

          _initializerDefineProperty(this, "btnPause", _descriptor2, this);

          _initializerDefineProperty(this, "Mask", _descriptor3, this);

          _initializerDefineProperty(this, "playerHpBar", _descriptor4, this);

          _initializerDefineProperty(this, "playerLevelBar", _descriptor5, this);

          _initializerDefineProperty(this, "labPlayerHp", _descriptor6, this);

          _initializerDefineProperty(this, "labPlayerLv", _descriptor7, this);

          _initializerDefineProperty(this, "labChapterLv", _descriptor8, this);

          _initializerDefineProperty(this, "NodeBoss", _descriptor9, this);

          _initializerDefineProperty(this, "bossHpBar", _descriptor10, this);
        }

        onLoad() {
          GameFightUI.instance = this;
          this.reset();
          this.node.active = false;
        }

        onEnable() {
          (_crd && EventManager === void 0 ? (_reportPossibleCrUseOfEventManager({
            error: Error()
          }), EventManager) : EventManager).Instance.on((_crd && GameEvent === void 0 ? (_reportPossibleCrUseOfGameEvent({
            error: Error()
          }), GameEvent) : GameEvent).GameStart, this.onEventGameStart, this);
          (_crd && EventManager === void 0 ? (_reportPossibleCrUseOfEventManager({
            error: Error()
          }), EventManager) : EventManager).Instance.on((_crd && GameEvent === void 0 ? (_reportPossibleCrUseOfGameEvent({
            error: Error()
          }), GameEvent) : GameEvent).GameMainPlaneIn, this.onEventGamePlaneIn, this);
        }

        onDisable() {
          (_crd && EventManager === void 0 ? (_reportPossibleCrUseOfEventManager({
            error: Error()
          }), EventManager) : EventManager).Instance.off((_crd && GameEvent === void 0 ? (_reportPossibleCrUseOfGameEvent({
            error: Error()
          }), GameEvent) : GameEvent).GameStart, this.onEventGameStart, this);
          (_crd && EventManager === void 0 ? (_reportPossibleCrUseOfEventManager({
            error: Error()
          }), EventManager) : EventManager).Instance.off((_crd && GameEvent === void 0 ? (_reportPossibleCrUseOfGameEvent({
            error: Error()
          }), GameEvent) : GameEvent).GameMainPlaneIn, this.onEventGamePlaneIn, this);
        }

        reset() {
          this.setTipActive(false);
          this.setBossUIActive(false);
          this.btnPause.active = false;
          this.Mask.active = false;
        }

        updatePlayerUI() {
          let mainPlane = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainPlane;

          if (!isValid(mainPlane)) {
            return;
          }

          let maxBarWidth = 0; //hp

          if (this.playerHpBar) {
            maxBarWidth = find("hp_bar", this.playerHpBar).getComponent(UITransform).width;
            this.playerHpBar.getComponent(UITransform).width = mainPlane.curHp / mainPlane.maxHp * maxBarWidth;
            this.labPlayerHp.string = `${mainPlane.curHp}/${mainPlane.maxHp}`;
          } //lv


          if (this.playerLevelBar) {
            maxBarWidth = find("hp_bar", this.playerLevelBar).getComponent(UITransform).width;
            this.playerLevelBar.getComponent(UITransform).width = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).mainPlaneManager.curExp / (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).mainPlaneManager.curMaxExp * maxBarWidth;
            this.labPlayerLv.string = (_crd && getI18StrByKey === void 0 ? (_reportPossibleCrUseOfgetI18StrByKey({
              error: Error()
            }), getI18StrByKey) : getI18StrByKey)("FIGHT_LV") + (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).mainPlaneManager.curLv;
          }
        }

        updateBossUI(bossPlane) {
          //hp
          if (this.bossHpBar) {
            let maxBarWidth = find("hp_bar", this.bossHpBar).getComponent(UITransform).width;
            this.bossHpBar.getComponent(UITransform).width = bossPlane.curHp / bossPlane.maxHp * maxBarWidth;
          }
        }

        setBossUIActive(isActive) {
          this.NodeBoss.active = isActive;
        }

        onEventGameStart() {
          this.setTipActive(false);
          this.labChapterLv.string = String((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.curLevel);
        }

        onEventGamePlaneIn() {
          this.setTipActive(true);
        }

        setTipActive(active) {
          this.tipNode.active = active;
        }

        setTouchState(isTouch) {
          if (this.btnPause) {
            this.btnPause.active = !isTouch;
          }

          this.showMaskAni(isTouch);
        }

        onBtnPauseClicked() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameStateManager.gamePause();
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && GamePauseUI === void 0 ? (_reportPossibleCrUseOfGamePauseUI({
            error: Error()
          }), GamePauseUI) : GamePauseUI);
        }

        showMaskAni(isTouch) {
          this.Mask.active = !isTouch;

          if (!isTouch) {
            let uIOpacity = this.Mask.getComponent(UIOpacity);
            uIOpacity.opacity = 0;
            Tween.stopAllByTarget(uIOpacity);
            tween(uIOpacity).to(0.3, {
              opacity: 160
            }).start();
          }
        }

      }, _class3.instance = void 0, _class3), (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "tipNode", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "btnPause", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "Mask", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "playerHpBar", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "playerLevelBar", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "labPlayerHp", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor7 = _applyDecoratedDescriptor(_class2.prototype, "labPlayerLv", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor8 = _applyDecoratedDescriptor(_class2.prototype, "labChapterLv", [_dec9], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor9 = _applyDecoratedDescriptor(_class2.prototype, "NodeBoss", [_dec10], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor10 = _applyDecoratedDescriptor(_class2.prototype, "bossHpBar", [_dec11], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=68705d49b5f7e0afee059dc3d199d5215e1a8d3d.js.map