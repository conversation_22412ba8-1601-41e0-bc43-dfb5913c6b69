System.register(["__unresolved_0", "cc", "__unresolved_1", "long", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9", "__unresolved_10", "__unresolved_11", "__unresolved_12", "__unresolved_13", "__unresolved_14", "__unresolved_15", "__unresolved_16", "__unresolved_17", "__unresolved_18", "__unresolved_19", "__unresolved_20", "__unresolved_21", "__unresolved_22"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, director, <PERSON><PERSON>, logInfo, log<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>C<PERSON>t, SingletonBase, UIMgr, MyApp, ModeType, DataMgr, EventManager, EventMgr, HomeUIEvent, GameReviveUI, MBoomUI, LoadingUI, BottomUI, RogueUI, HomeUI, TopUI, BulletSystem, GameEvent, GameIns, GameMapRun, lcgRand, GameMain, BattleManager, _crd, RAND_STRATEGY;

  function _reportPossibleCrUseOflogInfo(extras) {
    _reporterNs.report("logInfo", "db://assets/scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogWarn(extras) {
    _reporterNs.report("logWarn", "db://assets/scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLong(extras) {
    _reporterNs.report("Long", "long", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../../../../../scripts/core/base/GameConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfSingletonBase(extras) {
    _reporterNs.report("SingletonBase", "../../../../../scripts/core/base/SingletonBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "../../../../../scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "../../app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfModeType(extras) {
    _reporterNs.report("ModeType", "../../autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfrandStrategy(extras) {
    _reporterNs.report("randStrategy", "../../autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfResChapter(extras) {
    _reporterNs.report("ResChapter", "../../autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfResGameMode(extras) {
    _reporterNs.report("ResGameMode", "../../autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDataMgr(extras) {
    _reporterNs.report("DataMgr", "../../data/DataManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventManager(extras) {
    _reporterNs.report("EventManager", "../../event/EventManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventMgr(extras) {
    _reporterNs.report("EventMgr", "../../event/EventManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfHomeUIEvent(extras) {
    _reporterNs.report("HomeUIEvent", "../../event/HomeUIEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameReviveUI(extras) {
    _reporterNs.report("GameReviveUI", "../../ui/gameui/game/GameReviveUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMBoomUI(extras) {
    _reporterNs.report("MBoomUI", "../../ui/gameui/game/MBoomUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLoadingUI(extras) {
    _reporterNs.report("LoadingUI", "../../ui/gameui/LoadingUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBottomUI(extras) {
    _reporterNs.report("BottomUI", "../../ui/home/<USER>", _context.meta, extras);
  }

  function _reportPossibleCrUseOfRogueUI(extras) {
    _reporterNs.report("RogueUI", "../../ui/home/<USER>/RogueUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfHomeUI(extras) {
    _reporterNs.report("HomeUI", "../../ui/home/<USER>", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTopUI(extras) {
    _reporterNs.report("TopUI", "../../ui/home/<USER>", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletSystem(extras) {
    _reporterNs.report("BulletSystem", "../bullet/BulletSystem", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEvent(extras) {
    _reporterNs.report("GameEvent", "../event/GameEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameMapRun(extras) {
    _reporterNs.report("GameMapRun", "../ui/map/GameMapRun", _context.meta, extras);
  }

  function _reportPossibleCrUseOflcgRand(extras) {
    _reporterNs.report("lcgRand", "../utils/Rand", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameMain(extras) {
    _reporterNs.report("GameMain", "../scenes/GameMain", _context.meta, extras);
  }

  _export("BattleManager", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      director = _cc.director;
      Rect = _cc.Rect;
    }, function (_unresolved_2) {
      logInfo = _unresolved_2.logInfo;
      logWarn = _unresolved_2.logWarn;
    }, function (_long) {
      Long = _long.default;
    }, function (_unresolved_3) {
      GameConst = _unresolved_3.GameConst;
    }, function (_unresolved_4) {
      SingletonBase = _unresolved_4.SingletonBase;
    }, function (_unresolved_5) {
      UIMgr = _unresolved_5.UIMgr;
    }, function (_unresolved_6) {
      MyApp = _unresolved_6.MyApp;
    }, function (_unresolved_7) {
      ModeType = _unresolved_7.ModeType;
    }, function (_unresolved_8) {
      DataMgr = _unresolved_8.DataMgr;
    }, function (_unresolved_9) {
      EventManager = _unresolved_9.default;
      EventMgr = _unresolved_9.EventMgr;
    }, function (_unresolved_10) {
      HomeUIEvent = _unresolved_10.HomeUIEvent;
    }, function (_unresolved_11) {
      GameReviveUI = _unresolved_11.GameReviveUI;
    }, function (_unresolved_12) {
      MBoomUI = _unresolved_12.MBoomUI;
    }, function (_unresolved_13) {
      LoadingUI = _unresolved_13.LoadingUI;
    }, function (_unresolved_14) {
      BottomUI = _unresolved_14.BottomUI;
    }, function (_unresolved_15) {
      RogueUI = _unresolved_15.RogueUI;
    }, function (_unresolved_16) {
      HomeUI = _unresolved_16.HomeUI;
    }, function (_unresolved_17) {
      TopUI = _unresolved_17.TopUI;
    }, function (_unresolved_18) {
      BulletSystem = _unresolved_18.BulletSystem;
    }, function (_unresolved_19) {
      GameEvent = _unresolved_19.GameEvent;
    }, function (_unresolved_20) {
      GameIns = _unresolved_20.GameIns;
    }, function (_unresolved_21) {
      GameMapRun = _unresolved_21.default;
    }, function (_unresolved_22) {
      lcgRand = _unresolved_22.lcgRand;
    }, function (_unresolved_23) {
      GameMain = _unresolved_23.GameMain;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "64d867wWTtD/LPANmmLR3y9", "BattleManager", undefined);

      __checkObsolete__(['director', 'Rect']);

      RAND_STRATEGY = /*#__PURE__*/function (RAND_STRATEGY) {
        RAND_STRATEGY[RAND_STRATEGY["WEIGHT_PRUE"] = 1] = "WEIGHT_PRUE";
        RAND_STRATEGY[RAND_STRATEGY["WEIGHT_NO_REPEAT"] = 2] = "WEIGHT_NO_REPEAT";
        RAND_STRATEGY[RAND_STRATEGY["ORDER"] = 3] = "ORDER";
        return RAND_STRATEGY;
      }(RAND_STRATEGY || {});

      _export("BattleManager", BattleManager = class BattleManager extends (_crd && SingletonBase === void 0 ? (_reportPossibleCrUseOfSingletonBase({
        error: Error()
      }), SingletonBase) : SingletonBase) {
        get chapterConfig() {
          return this._chapterConfig;
        }

        // 随机出来的关卡列表（这个列表长度可能会大于章节表中的关卡数量）
        get levelList() {
          return this._levelList;
        }

        get modeConfig() {
          return this._modeConfig;
        }

        constructor() {
          super();
          this.initBattleEnd = false;
          this.isGameStart = false;
          this.animSpeed = 1;
          this._modeConfig = null;
          this.curLevel = 0;
          //小阶段
          this._chapterConfig = null;
          this._levelList = [];
          this._loadTotal = 0;
          this._loadCount = 0;
          this._rand = new (_crd && lcgRand === void 0 ? (_reportPossibleCrUseOflcgRand({
            error: Error()
          }), lcgRand) : lcgRand)();
          (_crd && EventManager === void 0 ? (_reportPossibleCrUseOfEventManager({
            error: Error()
          }), EventManager) : EventManager).Instance.on((_crd && GameEvent === void 0 ? (_reportPossibleCrUseOfGameEvent({
            error: Error()
          }), GameEvent) : GameEvent).onNetGameStart, this.onNetGameStart, this);
          (_crd && EventManager === void 0 ? (_reportPossibleCrUseOfEventManager({
            error: Error()
          }), EventManager) : EventManager).Instance.on((_crd && GameEvent === void 0 ? (_reportPossibleCrUseOfGameEvent({
            error: Error()
          }), GameEvent) : GameEvent).onNetGameOver, this.onNetGameOver, this);
        } //战斗开始接口


        startGameByMode(modeID, curLevel = 0, randSeed = Date.now()) {
          this._rand.seed = (_crd && Long === void 0 ? (_reportPossibleCrUseOfLong({
            error: Error()
          }), Long) : Long).fromNumber(randSeed);
          let modeConfig = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanTables.TbResGameMode.get(modeID);

          if (modeConfig == null) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("BattleManager", `can not find mode config by id ${modeID}`);
            return;
          }

          this._modeConfig = modeConfig;
          this.curLevel = curLevel;
          (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).gameLogic.cmdGameStart(modeID);
        }

        async onNetGameStart() {
          await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && LoadingUI === void 0 ? (_reportPossibleCrUseOfLoadingUI({
            error: Error()
          }), LoadingUI) : LoadingUI);
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).emit((_crd && HomeUIEvent === void 0 ? (_reportPossibleCrUseOfHomeUIEvent({
            error: Error()
          }), HomeUIEvent) : HomeUIEvent).Leave);
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.setPlaneData((_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).planeInfo.getPlaneInfoById());

          this._initLevelList(this._modeConfig.chapterID);

          director.loadScene("Game");
        }

        onNetGameOver() {} // 根据策略随机出关卡列表


        _initLevelList(chapterID) {
          this._chapterConfig = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanTables.TbResChapter.get(70001);
          /*(chapterID)*/

          ;

          if (this._chapterConfig == null) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("BattleManager", `can not find chapter config by id ${chapterID}`);
            return;
          } // 随机出关卡组


          const levelGroupList = this._randomSelection(this._chapterConfig.strategyList, this._chapterConfig.levelGroupCount, this._chapterConfig.strategy);

          if (levelGroupList.length === 0) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)('BattleManager', " levelGroupList is null");
            return;
          } // 随机出关卡


          this._levelList = [];

          for (const levelGroupID of levelGroupList) {
            const levelGroupData = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).lubanTables.TbResLevelGroup.get(levelGroupID);

            if (levelGroupData == null) {
              (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
                error: Error()
              }), logWarn) : logWarn)('BattleManager', " levelGroupData is null");
              continue;
            }

            this._levelList.push(...this._randomSelection(levelGroupData.normSTList, levelGroupData.normLevelCount, levelGroupData.normLevelST));

            this._levelList.push(...this._randomSelection(levelGroupData.bossSTList, levelGroupData.bossLevelCount, levelGroupData.bossLevelST));
          }

          (_crd && logInfo === void 0 ? (_reportPossibleCrUseOflogInfo({
            error: Error()
          }), logInfo) : logInfo)('BattleManager', ` _levelList: ${this._levelList}`);
        }

        mainReset() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).enemyManager.mainReset();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).bossManager.mainReset();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).waveManager.reset();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainReset();
          (_crd && GameMapRun === void 0 ? (_reportPossibleCrUseOfGameMapRun({
            error: Error()
          }), GameMapRun) : GameMapRun).instance.reset();
          (_crd && GameMapRun === void 0 ? (_reportPossibleCrUseOfGameMapRun({
            error: Error()
          }), GameMapRun) : GameMapRun).instance.clear();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).hurtEffectManager.clear();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameStateManager.reset();
          (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).destroy();
          this.isGameStart = false;
        }

        subReset() {
          this.animSpeed = 1;
          this.isGameStart = false;
          this.initBattleEnd = false;
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.subReset();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameStateManager.reset();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).waveManager.reset();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).enemyManager.subReset();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).bossManager.subReset();
        }
        /**
         * 检查所有资源是否加载完成
         */


        async checkLoadFinish() {
          this._loadCount++; // let loadingUI = UIMgr.get(LoadingUI)
          // loadingUI.updateProgress(this._loadCount / this._loadTotal)

          if (this._loadCount >= this._loadTotal) {
            await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).closeUI(_crd && LoadingUI === void 0 ? (_reportPossibleCrUseOfLoadingUI({
              error: Error()
            }), LoadingUI) : LoadingUI);
            (_crd && EventManager === void 0 ? (_reportPossibleCrUseOfEventManager({
              error: Error()
            }), EventManager) : EventManager).Instance.emit((_crd && GameEvent === void 0 ? (_reportPossibleCrUseOfGameEvent({
              error: Error()
            }), GameEvent) : GameEvent).GameLoadEnd);
            this.initBattle();
          }
        }

        addLoadCount(count) {
          this._loadTotal += count;
        }

        startLoading() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.preload();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).hurtEffectManager.preLoad(); //伤害特效资源

          (_crd && GameMapRun === void 0 ? (_reportPossibleCrUseOfGameMapRun({
            error: Error()
          }), GameMapRun) : GameMapRun).instance.initData(); //地图背景初始化

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).enemyManager.preLoad(); //敌人资源

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).bossManager.preLoad(); //boss资源
        }

        initBattle() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainPlane.planeIn();
          (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).init(new Rect(0, 0, (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).ViewBattleWidth, (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).ViewHeight));
        }

        onPlaneIn() {
          this.initBattleEnd = true;
          (_crd && EventManager === void 0 ? (_reportPossibleCrUseOfEventManager({
            error: Error()
          }), EventManager) : EventManager).Instance.emit((_crd && GameEvent === void 0 ? (_reportPossibleCrUseOfGameEvent({
            error: Error()
          }), GameEvent) : GameEvent).GameMainPlaneIn);
        }

        beginBattle() {
          if (this.initBattleEnd && !this.isGameStart) {
            this.isGameStart = true;
            (_crd && EventManager === void 0 ? (_reportPossibleCrUseOfEventManager({
              error: Error()
            }), EventManager) : EventManager).Instance.emit((_crd && GameEvent === void 0 ? (_reportPossibleCrUseOfGameEvent({
              error: Error()
            }), GameEvent) : GameEvent).GameStart);
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).waveManager.gameStart();
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).gameStateManager.gameStart();
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).mainPlaneManager.mainPlane.begine();
          }
        }
        /**
         * 更新游戏逻辑
         * @param {number} dt 每帧的时间间隔
         */


        updateGameLogic(dt) {
          dt = dt * this.animSpeed;

          if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameStateManager.isGameOver()) {
            if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).gamePlaneManager) {
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).gamePlaneManager.enemyTarget = null;
            }

            return;
          }

          if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameStateManager.isInBattle() || (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameStateManager.isGameWillOver()) {
            var _instance;

            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).gameDataManager.gameTime += dt;
            (_instance = (_crd && GameMapRun === void 0 ? (_reportPossibleCrUseOfGameMapRun({
              error: Error()
            }), GameMapRun) : GameMapRun).instance) == null || _instance.updateGameLogic(dt);
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).gamePlaneManager.updateGameLogic(dt);
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).mainPlaneManager.updateGameLogic(dt);
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).waveManager.updateGameLogic(dt);
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).enemyManager.updateGameLogic(dt);
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).bossManager.updateGameLogic(dt);
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).gameStateManager.updateGameLogic(dt); //子弹发射器系统

            (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
              error: Error()
            }), BulletSystem) : BulletSystem).tick(dt);
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).fColliderManager.updateGameLogic(dt);
          } else if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gamePlaneManager) {
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).gamePlaneManager.enemyTarget = null;
          }
        }

        setTouchState(isTouch) {
          var _instance2;

          if (isTouch) {
            this.beginBattle();
            this.animSpeed = 1;
          } else {
            this.animSpeed = 0.2;
          }

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).enemyManager.setAnimSpeed(this.animSpeed);
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).bossManager.setAnimSpeed(this.animSpeed);
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainPlane.setAnimSpeed(this.animSpeed);
          (_instance2 = (_crd && GameMain === void 0 ? (_reportPossibleCrUseOfGameMain({
            error: Error()
          }), GameMain) : GameMain).instance) == null || _instance2.GameFightUI.setTouchState(isTouch);
        }
        /**
         * 战斗复活逻辑
         */


        relifeBattle() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameStateManager.gameResume();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.revive();
        }

        setGameEnd(isWin) {
          if (isWin) {
            if (this.checkNextlevel()) {
              //判断是否有下一关
              (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
                error: Error()
              }), UIMgr) : UIMgr).openUI(_crd && RogueUI === void 0 ? (_reportPossibleCrUseOfRogueUI({
                error: Error()
              }), RogueUI) : RogueUI, () => {
                this.startNextBattle();
              });
              return;
            }
          } else {
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).gameStateManager.gamePause();

            if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).mainPlaneManager.checkCanRevive()) {
              // 判断是否可以复活
              (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
                error: Error()
              }), UIMgr) : UIMgr).openUI(_crd && GameReviveUI === void 0 ? (_reportPossibleCrUseOfGameReviveUI({
                error: Error()
              }), GameReviveUI) : GameReviveUI);
              return;
            }
          }

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainPlane.hpNode.active = false;
          this.endBattle();
          (_crd && GameMain === void 0 ? (_reportPossibleCrUseOfGameMain({
            error: Error()
          }), GameMain) : GameMain).instance.showGameResult(isWin);
          (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).gameLogic.cmdGameEnd((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameDataManager.getGameResultData(), (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameDataManager.getGameLevelResultData());
        }

        checkNextlevel() {
          if (this._modeConfig.modeType == (_crd && ModeType === void 0 ? (_reportPossibleCrUseOfModeType({
            error: Error()
          }), ModeType) : ModeType).ENDLESS) {
            return true;
          }

          return this.curLevel + 1 <= this._chapterConfig.levelCount;
        }
        /**
         * 继续下一场战斗
         */


        startNextBattle() {
          this.subReset();
          this.curLevel += 1;
          this.initBattle();
        }
        /**
         * 结束战斗
         */


        endBattle() {
          var _instance3;

          (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).destroy(false, false);
          (_instance3 = (_crd && GameMain === void 0 ? (_reportPossibleCrUseOfGameMain({
            error: Error()
          }), GameMain) : GameMain).instance) == null || _instance3.GameFightUI.reset();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameStateManager.gameOver();
        }

        async quitBattle() {
          this.mainReset();
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).closeUI(_crd && MBoomUI === void 0 ? (_reportPossibleCrUseOfMBoomUI({
            error: Error()
          }), MBoomUI) : MBoomUI);
          await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && HomeUI === void 0 ? (_reportPossibleCrUseOfHomeUI({
            error: Error()
          }), HomeUI) : HomeUI);
          await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && BottomUI === void 0 ? (_reportPossibleCrUseOfBottomUI({
            error: Error()
          }), BottomUI) : BottomUI);
          await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && TopUI === void 0 ? (_reportPossibleCrUseOfTopUI({
            error: Error()
          }), TopUI) : TopUI);
          director.loadScene("Main");
        }

        bossChangeFinish(tip) {// const bossEnterDialog = GameIns.uiManager.getDialog(BossEnterDialog.default);
          // if (bossEnterDialog) {
          //     bossEnterDialog.node.active = true;
          //     GameIns.mainPlaneManager.moveAble = false;
          //     bossEnterDialog.showTips(bossName);
          // }
        }

        bossWillEnter() {// GameIns.mainPlaneManager.mainPlane!.setFireEnable(false);
          // GameIns.mainPlaneManager.moveAble = false;
        }
        /**
         * 开始Boss战斗
         */


        bossFightStart() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainPlane.setFireEnable(true);
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainPlane.setMoveAble(true);
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).bossManager.bossFightStart();
        }

        random() {
          return this._rand.random();
        }
        /**
         * 策略：
         * 1.严格按权重比例随机选择元素
         * 2.严格按权重比例随机选择元素，不重复
         * 3.按顺序选择元素
         * @param STList 带权重的元素数组
         * @param count 需要选择的元素数量
         * @returns 选中元素的ID数组
         */


        _randomSelection(STList, count, strategy) {
          if (STList.length === 0 || count <= 0) return [];
          const results = [];

          if (strategy === RAND_STRATEGY.WEIGHT_PRUE) {
            // 计算总权重
            const totalWeight = STList.reduce((sum, item) => sum + item.Weight, 0); // 如果所有权重都为0，则转为均匀随机

            if (totalWeight === 0) {
              for (let i = 0; i < count; i++) {
                const randomIndex = Math.floor((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                  error: Error()
                }), GameIns) : GameIns).battleManager.random() * STList.length);
                results.push(STList[randomIndex].ID);
              }

              return results;
            } // 严格按权重比例随机选择


            for (let i = 0; i < count; i++) {
              // 生成[0, totalWeight)区间的随机数
              const randomValue = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).battleManager.random() * totalWeight; // 遍历查找随机数对应的元素

              let cumulativeWeight = 0;

              for (const item of STList) {
                cumulativeWeight += item.Weight;

                if (randomValue < cumulativeWeight) {
                  results.push(item.ID);
                  break;
                }
              }
            }
          } else if (strategy === RAND_STRATEGY.WEIGHT_NO_REPEAT) {
            // 计算总权重
            const totalWeight = STList.reduce((sum, item) => sum + item.Weight, 0); // 如果所有权重都为0，则转为均匀随机

            if (totalWeight === 0) {
              for (let i = 0; i < count; i++) {
                let randomIndex = Math.floor((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                  error: Error()
                }), GameIns) : GameIns).battleManager.random() * STList.length); // 避免重复选择相同的ID

                if (i > 0 && STList[randomIndex].ID === results[i - 1]) {
                  // 如果与上一次选择的相同，选择下一个（循环）
                  randomIndex = (randomIndex + 1) % STList.length;
                }

                results.push(STList[randomIndex].ID);
              }

              return results;
            } // 创建副本以避免修改原始数据


            const tempList = [...STList];
            let lastSelectedId = -1;

            for (let i = 0; i <= count; i++) {
              // 如果上一次选择的ID存在，且它在当前列表中，调整其位置
              if (lastSelectedId !== -1) {
                const lastSelectedIndex = tempList.findIndex(item => item.ID === lastSelectedId);

                if (lastSelectedIndex !== -1 && lastSelectedIndex < tempList.length - 1) {
                  // 将上一次选择的ID与下一个元素交换位置
                  [tempList[lastSelectedIndex], tempList[lastSelectedIndex + 1]] = [tempList[lastSelectedIndex + 1], tempList[lastSelectedIndex]];
                }
              } // 生成[0, totalWeight)区间的随机数


              const randomValue = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).battleManager.random() * totalWeight; // 遍历查找随机数对应的元素

              let cumulativeWeight = 0;
              let selectedIndex = -1;

              for (let j = 0; j < tempList.length; j++) {
                cumulativeWeight += tempList[j].Weight;

                if (randomValue < cumulativeWeight) {
                  selectedIndex = j;
                  break;
                }
              } // 如果未找到有效索引，选择最后一个元素


              if (selectedIndex === -1) {
                selectedIndex = tempList.length - 1;
              } // 获取选中的ID


              const selectedId = tempList[selectedIndex].ID;
              results.push(selectedId); // 更新上一次选择的ID

              lastSelectedId = selectedId;
            }
          } else if (strategy === RAND_STRATEGY.ORDER) {
            // 按顺序选择元素，遇到ID为0时从数组开头重新开始
            let currentIndex = 0;

            for (let i = 0; i < count; i++) {
              // 如果当前元素的ID为0，则重置到数组开头
              if (STList[currentIndex].ID === 0) {
                currentIndex = 0; // 如果数组开头的元素ID也为0，则跳过所有ID为0的元素

                while (currentIndex < STList.length && STList[currentIndex].ID === 0) {
                  currentIndex++;
                } // 如果所有元素ID都为0，则无法选择，跳出循环


                if (currentIndex >= STList.length) {
                  break;
                }
              } // 选择当前元素


              results.push(STList[currentIndex].ID); // 移动到下一个元素

              currentIndex++; // 如果到达数组末尾，回到开头

              if (currentIndex >= STList.length) {
                currentIndex = 0;
              }
            }
          }

          return results;
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=c5d8871e59eaec0a77652d199c7da288c1619860.js.map