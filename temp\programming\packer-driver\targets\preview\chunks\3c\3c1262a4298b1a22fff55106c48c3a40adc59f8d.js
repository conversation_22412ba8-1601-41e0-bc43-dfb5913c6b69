System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Enum, Vec2, Vec3, UITransform, BulletSystem, _dec, _class, _descriptor, _class2, _crd, ccclass, property, eEasing, eSpriteDefaultFacing, eMoveEvent, MoveBase;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfBulletSystem(extras) {
    _reporterNs.report("BulletSystem", "../bullet/BulletSystem", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Enum = _cc.Enum;
      Vec2 = _cc.Vec2;
      Vec3 = _cc.Vec3;
      UITransform = _cc.UITransform;
    }, function (_unresolved_2) {
      BulletSystem = _unresolved_2.BulletSystem;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "f66affuZgJGr7LaYjgZCHpD", "IMovable", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'Enum', 'Vec2', 'Vec3', 'UITransform']);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * Base interface for all move-able objects
       */

      // export enum eMoveModifier {
      //     Speed, SpeedAngle, Acceleration, AccelerationAngle
      // }
      _export("eEasing", eEasing = /*#__PURE__*/function (eEasing) {
        eEasing[eEasing["Linear"] = 0] = "Linear";
        eEasing[eEasing["InSine"] = 1] = "InSine";
        eEasing[eEasing["OutSine"] = 2] = "OutSine";
        eEasing[eEasing["InOutSine"] = 3] = "InOutSine";
        eEasing[eEasing["InQuad"] = 4] = "InQuad";
        eEasing[eEasing["OutQuad"] = 5] = "OutQuad";
        eEasing[eEasing["InOutQuad"] = 6] = "InOutQuad";
        return eEasing;
      }({}));

      _export("eSpriteDefaultFacing", eSpriteDefaultFacing = /*#__PURE__*/function (eSpriteDefaultFacing) {
        eSpriteDefaultFacing[eSpriteDefaultFacing["Right"] = 0] = "Right";
        eSpriteDefaultFacing[eSpriteDefaultFacing["Up"] = -90] = "Up";
        eSpriteDefaultFacing[eSpriteDefaultFacing["Down"] = 90] = "Down";
        eSpriteDefaultFacing[eSpriteDefaultFacing["Left"] = 180] = "Left";
        return eSpriteDefaultFacing;
      }({}));

      _export("eMoveEvent", eMoveEvent = /*#__PURE__*/function (eMoveEvent) {
        eMoveEvent[eMoveEvent["onBecomeVisible"] = 0] = "onBecomeVisible";
        eMoveEvent[eMoveEvent["onBecomeInvisible"] = 1] = "onBecomeInvisible";
        return eMoveEvent;
      }({}));

      _export("MoveBase", MoveBase = (_dec = property({
        type: Enum(eSpriteDefaultFacing),
        displayName: '图片默认朝向'
      }), (_class = (_class2 = class MoveBase extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "defaultFacing", _descriptor, this);

          this.speed = 100;
          this.speedAngle = 0;
          this.acceleration = 0;
          this.accelerationAngle = 0;
          this._wasVisible = false;
          this._isVisible = false;
          this._isMovable = true;
          this._selfSize = new Vec2();
          this._position = new Vec3();
          this._visibilityCheckCounter = 0;
          // 每x帧检查一次可见性
          // Event system:
          this._eventListeners = new Map();
        }

        // 是否可见
        get isVisible() {
          return this._isVisible;
        }

        // 是否可移动
        get isMovable() {
          return this._isMovable;
        }

        onLoad() {
          var uiTransform = this.node.getComponent(UITransform);
          var self_size = uiTransform ? uiTransform.contentSize : {
            width: 0,
            height: 0
          };

          this._selfSize.set(self_size.width / 2, self_size.height / 2);
        }

        onDestroy() {
          // clear all event listeners
          this._eventListeners.clear();
        }
        /**
         * 添加事件监听器
         * @param event 事件类型
         * @param listener 监听器函数
         */


        on(event, listener) {
          if (!this._eventListeners.has(event)) {
            this._eventListeners.set(event, []);
          }

          var listeners = this._eventListeners.get(event);

          if (!listeners.includes(listener)) {
            listeners.push(listener);
          }
        }
        /**
         * 移除事件监听器
         * @param event 事件类型
         * @param listener 监听器函数
         */


        off(event, listener) {
          var listeners = this._eventListeners.get(event);

          if (listeners) {
            var index = listeners.indexOf(listener);

            if (index !== -1) {
              listeners.splice(index, 1);
            }
          }
        }

        removeAllListeners() {
          this._eventListeners.clear();
        }
        /**
         * 触发事件
         * @param event 事件类型
         */


        emit(event) {
          var listeners = this._eventListeners.get(event);

          if (listeners && listeners.length > 0) {
            listeners.forEach(listener => listener());
          }
        }

        checkVisibility() {
          // 降低可见性检查频率
          if (++this._visibilityCheckCounter >= MoveBase.VISIBILITY_CHECK_INTERVAL) {
            this._visibilityCheckCounter = 0; // 这里目前的检查逻辑没有考虑旋转和缩放
            // 正常来说需要判定world corners，如果四个角有一个在屏幕内，就认为是可见的

            var visibleSize = (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
              error: Error()
            }), BulletSystem) : BulletSystem).worldBounds;
            var position = this.node.worldPosition;
            var isVisible = position.x + this._selfSize.x >= visibleSize.xMin && position.x - this._selfSize.x <= visibleSize.xMax && position.y - this._selfSize.y <= visibleSize.yMax && position.y + this._selfSize.y >= visibleSize.yMin; // debug visibility
            // if (!isVisible) {
            //     console.log("Movable", "checkVisibility", this.node.name + " is not visible");
            //     console.log("Movable", "checkLeftBound  :", (this._position.x - this._selfSize.x) <= visibleSize.xMax, (this._position.x - this._selfSize.x), "<=", visibleSize.xMax);
            //     console.log("Movable", "checkRightBound :", (this._position.x + this._selfSize.x) >= visibleSize.xMin, (this._position.x + this._selfSize.x), ">=", visibleSize.xMin);
            //     console.log("Movable", "checkTopBound   :", (this._position.y + this._selfSize.y) <= visibleSize.yMax, (this._position.y + this._selfSize.y), "<=", visibleSize.yMax);
            //     console.log("Movable", "checkBottomBound:", (this._position.y - this._selfSize.y) >= visibleSize.yMin, (this._position.y - this._selfSize.y), ">=", visibleSize.yMin);
            // }

            this.setVisible(isVisible);
          }
        }

        setVisible(visible) {
          // console.log('setVisible: ', this._wasVisible, ', ', visible);
          if (visible) {
            if (!this._wasVisible) this.emit(eMoveEvent.onBecomeVisible);
          } else {
            if (this._wasVisible) this.emit(eMoveEvent.onBecomeInvisible);
          }

          this._wasVisible = this._isVisible;
          this._isVisible = visible;
        }

      }, _class2.VISIBILITY_CHECK_INTERVAL = 5, _class2), (_descriptor = _applyDecoratedDescriptor(_class.prototype, "defaultFacing", [_dec], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return eSpriteDefaultFacing.Up;
        }
      })), _class)));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=3c1262a4298b1a22fff55106c48c3a40adc59f8d.js.map