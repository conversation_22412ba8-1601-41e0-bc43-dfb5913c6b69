import type FColliderManager from "./collider-system/FColliderManager";
import { type BattleManager } from "./manager/BattleManager";
import { type BossManager } from "./manager/BossManager";
import { type EnemyManager } from "./manager/EnemyManager";
import { GameDataManager } from "./manager/GameDataManager";
import { type GamePlaneManager } from "./manager/GamePlaneManager";
import { type GameStateManager } from "./manager/GameStateManager";
import { type HurtEffectManager } from "./manager/HurtEffectManager";
import { type MainPlaneManager } from "./manager/MainPlaneManager";
import type WaveManager from "./manager/WaveManager";

class _GameIns {
    _battleManager: BattleManager | null = null;
    get battleManager() { return this._battleManager!; }
    _bossManager: BossManager | null = null;
    get bossManager() { return this._bossManager!; }
    _enemyManager: EnemyManager | null = null;
    get enemyManager() { return this._enemyManager!; }
    _gameStateManager: GameStateManager | null = null;
    get gameStateManager() { return this._gameStateManager!; }
    _hurtEffectManager: HurtEffectManager | null = null;
    get hurtEffectManager() { return this._hurtEffectManager!; }
    _mainPlaneManager: MainPlaneManager | null = null;
    get mainPlaneManager() { return this._mainPlaneManager!; }
    _gamePlaneManager: GamePlaneManager | null = null;
    get gamePlaneManager() { return this._gamePlaneManager!; }
    _waveManager: WaveManager | null = null;
    get waveManager() { return this._waveManager!; }
    _fColliderManager: FColliderManager | null = null;
    get fColliderManager() { return this._fColliderManager!; }
    _gameDataManager: GameDataManager | null = null;
    get gameDataManager() { return this._gameDataManager!; }
}

export const GameIns: _GameIns = new _GameIns();