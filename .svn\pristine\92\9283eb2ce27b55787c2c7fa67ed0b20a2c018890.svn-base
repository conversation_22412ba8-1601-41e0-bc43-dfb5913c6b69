import { _decorator, Component, find, isValid, Label, Node, Tween, tween, UIOpacity, UITransform } from 'cc';
import EventManager from '../../../event/EventManager';
import { GameEvent } from '../../event/GameEvent';
import { GameIns } from '../../GameIns';
import { UIMgr } from 'db://assets/scripts/core/base/UIMgr';
import { GamePauseUI } from '../../../ui/gameui/game/GamePauseUI';
import { getI18StrByKey } from 'db://i18n/LanguageData';
import PlaneBase from '../plane/PlaneBase';
const { ccclass, property } = _decorator;

@ccclass('GameFightUI')
export class GameFightUI extends Component {

    @property(Node)
    tipNode: Node | null = null
    @property(Node)
    btnPause: Node | null = null
    @property(Node)
    Mask: Node | null = null

    @property(Node)
    playerHpBar: Node | null = null
    @property(Node)
    playerLevelBar: Node | null = null
    @property(Label)
    labPlayerHp: Label | null = null
    @property(Label)
    labPlayerLv: Label | null = null
    @property(Label)
    labChapterLv: Label | null = null
    
    @property(Node)
    NodeBoss: Node | null = null
    @property(Node)
    bossHpBar: Node | null = null

    static instance: GameFightUI;
    protected onLoad(): void {
        GameFightUI.instance = this;
        this.reset();
        this.node.active = false;
    }

    protected onEnable(): void {
        EventManager.Instance.on(GameEvent.GameStart, this.onEventGameStart, this);
        EventManager.Instance.on(GameEvent.GameMainPlaneIn, this.onEventGamePlaneIn, this);
    }

    protected onDisable(): void {
        EventManager.Instance.off(GameEvent.GameStart, this.onEventGameStart, this);
        EventManager.Instance.off(GameEvent.GameMainPlaneIn, this.onEventGamePlaneIn, this);
    }

    reset(){
        this.setTipActive(false);
        this.setBossUIActive(false);
        this.btnPause!.active = false;
        this.Mask!.active = false;
    }

    updatePlayerUI(){
        let mainPlane = GameIns.mainPlaneManager.mainPlane;
        if (!isValid(mainPlane)){
            return;
        }

        let maxBarWidth:number = 0;
        //hp
        if (this.playerHpBar){
            maxBarWidth = find("hp_bar",this.playerHpBar)!.getComponent(UITransform)!.width;
            this.playerHpBar!.getComponent(UITransform)!.width = mainPlane.curHp/mainPlane.maxHp*maxBarWidth;
            this.labPlayerHp!.string = `${mainPlane.curHp}/${mainPlane.maxHp}`
        }

        //lv
        if (this.playerLevelBar){
            maxBarWidth = find("hp_bar",this.playerLevelBar)!.getComponent(UITransform)!.width;
            this.playerLevelBar!.getComponent(UITransform)!.width = GameIns.mainPlaneManager.curExp/GameIns.mainPlaneManager.curMaxExp*maxBarWidth;
            this.labPlayerLv!.string = getI18StrByKey("FIGHT_LV")+GameIns.mainPlaneManager.curLv;
        }
    }

    updateBossUI(bossPlane:PlaneBase){
        //hp
        if (this.bossHpBar){
            let maxBarWidth = find("hp_bar",this.bossHpBar)!.getComponent(UITransform)!.width;
            this.bossHpBar!.getComponent(UITransform)!.width = bossPlane.curHp/bossPlane.maxHp*maxBarWidth;
        }
    }

    setBossUIActive(isActive:boolean){
        this.NodeBoss!.active = isActive;
    }

    onEventGameStart() {
        this.setTipActive(false);
        this.labChapterLv!.string = String(GameIns.battleManager.curLevel);
    }

    onEventGamePlaneIn() {
        this.setTipActive(true);
    }

    setTipActive(active: boolean) {
        this.tipNode!.active = active;
    }

    setTouchState(isTouch: boolean) {
        if (this.btnPause){
            this.btnPause.active = !isTouch;
        }
        this.showMaskAni(isTouch);
    }

    onBtnPauseClicked() {
        GameIns.gameStateManager.gamePause();
        UIMgr.openUI(GamePauseUI);

    }

    showMaskAni(isTouch: boolean) {
        this.Mask!.active = !isTouch;
        if (!isTouch) {
            let uIOpacity = this.Mask!.getComponent(UIOpacity);
            uIOpacity!.opacity = 0;
            Tween.stopAllByTarget(uIOpacity!);
            tween(uIOpacity!)
                .to(0.3, { opacity: 160 })
                .start();
        }
    }
}


