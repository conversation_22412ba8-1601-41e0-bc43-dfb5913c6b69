{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/IPlatformSDK.ts"], "names": ["PlatformSDKUserInfo", "CreateLoginSDK", "WECHAT", "WXLogin", "DevLogin", "name", "icon"], "mappings": ";;;yDAMaA,mB;;AAaN,WAASC,cAAT,GACP;AACI,QAAIC,MAAJ,EACA;AACI,aAAO;AAAA;AAAA,+BAAP;AACH,KAHD,MAKA;AACI,aAAO;AAAA;AAAA,iCAAP;AACH;AACJ;;;;;;;;;;;;;;;;oBAVeD;;;;;;;;;AAnBPE,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAF,MAAAA,M,UAAAA,M;;;;;;;qCAIIF,mB,GAAN,MAAMA,mBAAN,CAA0B;AAAA;AAAA,eAC7BK,IAD6B,GACb,EADa;AAAA,eAE7BC,IAF6B,GAEb,EAFa;AAAA;;AAAA,O", "sourcesContent": ["import { WXLogin } from \"./WXLogin\";\r\nimport { DevLogin } from \"./DevLogin\";\r\nimport { WECHAT } from \"cc/env\";\r\nimport { LoginInfo } from \"../network/NetMgr\";\r\n\r\n\r\nexport class PlatformSDKUserInfo {\r\n    name : string = \"\";\r\n    icon : string = \"\";\r\n}\r\n\r\nexport interface IPlatformSDK {\r\n    login:(cb:(err:string|null, req:LoginInfo)=>void)=>void\r\n    getUserInfo:(cb:(err:string, userInfo:PlatformSDKUserInfo|null, hasTap:boolean)=>void, param:any)=>void\r\n    showUserInfoButton:()=>void\r\n    hideUserInfoButton:()=>void\r\n    getStatusBarHeight:()=>number\r\n}\r\n\r\nexport function CreateLoginSDK()\r\n{\r\n    if (WECHAT)\r\n    {\r\n        return new WXLogin();\r\n    }\r\n    else\r\n    {\r\n        return new DevLogin();\r\n    }\r\n}\r\n"]}