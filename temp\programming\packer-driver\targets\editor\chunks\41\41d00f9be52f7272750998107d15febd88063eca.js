System.register(["__unresolved_0", "cc"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, _GameIns, _crd, GameIns;

  function _reportPossibleCrUseOfFColliderManager(extras) {
    _reporterNs.report("FColliderManager", "./collider-system/FColliderManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBattleManager(extras) {
    _reporterNs.report("BattleManager", "./manager/BattleManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBossManager(extras) {
    _reporterNs.report("BossManager", "./manager/BossManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyManager(extras) {
    _reporterNs.report("EnemyManager", "./manager/EnemyManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameDataManager(extras) {
    _reporterNs.report("GameDataManager", "./manager/GameDataManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGamePlaneManager(extras) {
    _reporterNs.report("GamePlaneManager", "./manager/GamePlaneManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameStateManager(extras) {
    _reporterNs.report("GameStateManager", "./manager/GameStateManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfHurtEffectManager(extras) {
    _reporterNs.report("HurtEffectManager", "./manager/HurtEffectManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMainPlaneManager(extras) {
    _reporterNs.report("MainPlaneManager", "./manager/MainPlaneManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfWaveManager(extras) {
    _reporterNs.report("WaveManager", "./manager/WaveManager", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "28f96SlzaZCLJsFVJcKZyjG", "GameIns", undefined);

      _GameIns = class _GameIns {
        constructor() {
          this._battleManager = null;
          this._bossManager = null;
          this._enemyManager = null;
          this._gameStateManager = null;
          this._hurtEffectManager = null;
          this._mainPlaneManager = null;
          this._gamePlaneManager = null;
          this._waveManager = null;
          this._fColliderManager = null;
          this._gameDataManager = null;
        }

        get battleManager() {
          return this._battleManager;
        }

        get bossManager() {
          return this._bossManager;
        }

        get enemyManager() {
          return this._enemyManager;
        }

        get gameStateManager() {
          return this._gameStateManager;
        }

        get hurtEffectManager() {
          return this._hurtEffectManager;
        }

        get mainPlaneManager() {
          return this._mainPlaneManager;
        }

        get gamePlaneManager() {
          return this._gamePlaneManager;
        }

        get waveManager() {
          return this._waveManager;
        }

        get fColliderManager() {
          return this._fColliderManager;
        }

        get gameDataManager() {
          return this._gameDataManager;
        }

      };

      _export("GameIns", GameIns = new _GameIns());

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=41d00f9be52f7272750998107d15febd88063eca.js.map