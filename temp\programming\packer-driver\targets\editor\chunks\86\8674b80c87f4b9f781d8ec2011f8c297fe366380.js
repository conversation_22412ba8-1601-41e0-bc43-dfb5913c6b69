System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9", "__unresolved_10", "__unresolved_11", "__unresolved_12", "__unresolved_13", "__unresolved_14", "__unresolved_15"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Node, MyApp, ButtonPlus, BaseUI, UILayer, UIMgr, logError, BundleName, HomeUIBaseSystem, EventMgr, startGameByMode, GameEnum, FriendUI, MailUI, PKUI, StoryUI, TaskTipUI, Rogue<PERSON>, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _crd, ccclass, property, HomeUI;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/bundles/common/script/app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfButtonPlus(extras) {
    _reporterNs.report("ButtonPlus", "db://assets/bundles/common/script/ui/common/components/button/ButtonPlus", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBaseUI(extras) {
    _reporterNs.report("BaseUI", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUILayer(extras) {
    _reporterNs.report("UILayer", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogError(extras) {
    _reporterNs.report("logError", "db://assets/scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBundleName(extras) {
    _reporterNs.report("BundleName", "../../const/BundleConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfHomeUIBaseSystem(extras) {
    _reporterNs.report("HomeUIBaseSystem", "../../const/HomeUIConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventMgr(extras) {
    _reporterNs.report("EventMgr", "../../event/EventManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfstartGameByMode(extras) {
    _reporterNs.report("startGameByMode", "../../game/GameInsStart", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEnum(extras) {
    _reporterNs.report("GameEnum", "../../game/const/GameEnum", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFriendUI(extras) {
    _reporterNs.report("FriendUI", "../friend/FriendUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMailUI(extras) {
    _reporterNs.report("MailUI", "../mail/MailUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPKUI(extras) {
    _reporterNs.report("PKUI", "../pk/PKUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfStoryUI(extras) {
    _reporterNs.report("StoryUI", "../story/StoryUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTaskTipUI(extras) {
    _reporterNs.report("TaskTipUI", "../task/TaskTipUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfRogueUI(extras) {
    _reporterNs.report("RogueUI", "./fight/RogueUI", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Node = _cc.Node;
    }, function (_unresolved_2) {
      MyApp = _unresolved_2.MyApp;
    }, function (_unresolved_3) {
      ButtonPlus = _unresolved_3.ButtonPlus;
    }, function (_unresolved_4) {
      BaseUI = _unresolved_4.BaseUI;
      UILayer = _unresolved_4.UILayer;
      UIMgr = _unresolved_4.UIMgr;
    }, function (_unresolved_5) {
      logError = _unresolved_5.logError;
    }, function (_unresolved_6) {
      BundleName = _unresolved_6.BundleName;
    }, function (_unresolved_7) {
      HomeUIBaseSystem = _unresolved_7.HomeUIBaseSystem;
    }, function (_unresolved_8) {
      EventMgr = _unresolved_8.EventMgr;
    }, function (_unresolved_9) {
      startGameByMode = _unresolved_9.startGameByMode;
    }, function (_unresolved_10) {
      GameEnum = _unresolved_10.GameEnum;
    }, function (_unresolved_11) {
      FriendUI = _unresolved_11.FriendUI;
    }, function (_unresolved_12) {
      MailUI = _unresolved_12.MailUI;
    }, function (_unresolved_13) {
      PKUI = _unresolved_13.PKUI;
    }, function (_unresolved_14) {
      StoryUI = _unresolved_14.StoryUI;
    }, function (_unresolved_15) {
      TaskTipUI = _unresolved_15.TaskTipUI;
    }, function (_unresolved_16) {
      RogueUI = _unresolved_16.RogueUI;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "67c83AwYYJM05MbEU5BoQUc", "HomeUI", undefined);

      __checkObsolete__(['_decorator', 'EventTouch', 'Node']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("HomeUI", HomeUI = (_dec = ccclass('HomeUI'), _dec2 = property(Node), _dec3 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec4 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec5 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec6 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec(_class = (_class2 = class HomeUI extends (_crd && BaseUI === void 0 ? (_reportPossibleCrUseOfBaseUI({
        error: Error()
      }), BaseUI) : BaseUI) {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "nodeBaseSystem", _descriptor, this);

          _initializerDefineProperty(this, "btnBattle", _descriptor2, this);

          _initializerDefineProperty(this, "btnStory", _descriptor3, this);

          _initializerDefineProperty(this, "btnPK", _descriptor4, this);

          _initializerDefineProperty(this, "btnRogue", _descriptor5, this);

          this._isInit = false;
        }

        static getUrl() {
          return "prefab/ui/HomeUI";
        }

        static getLayer() {
          return (_crd && UILayer === void 0 ? (_reportPossibleCrUseOfUILayer({
            error: Error()
          }), UILayer) : UILayer).Background;
        }

        static getBundleName() {
          return (_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).Home;
        }

        onLoad() {
          this.btnBattle.addClick(this.onBattleClick, this);
          this.btnStory.addClick(this.onStoryClick, this);
          this.btnPK.addClick(this.onPKClick, this);
          this.btnRogue.addClick(this.onRogueClick, this);
          this.nodeBaseSystem.getComponentsInChildren(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
            error: Error()
          }), ButtonPlus) : ButtonPlus).forEach(btn => {
            btn.addClick(this.onBaseSystemClick, this);
          });
        }

        async init() {
          if (this._isInit) return; //异步加载初始化界面内的ui

          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.loadBundle((_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).HomeTask).then(async () => {
            (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).openUI(_crd && TaskTipUI === void 0 ? (_reportPossibleCrUseOfTaskTipUI({
              error: Error()
            }), TaskTipUI) : TaskTipUI);
          });
          this._isInit = true;
        }

        async onShow(...args) {
          await this.init();
        }

        async onHide(...args) {}

        async onClose(...args) {
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).targetOff(this);
        }

        onDestroy() {
          this.unscheduleAllCallbacks();
        }

        update(dt) {}

        async onBattleClick() {
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).globalMgr.chapterID = 0;
          this.onBattle();
        }

        async onBattle() {
          (_crd && startGameByMode === void 0 ? (_reportPossibleCrUseOfstartGameByMode({
            error: Error()
          }), startGameByMode) : startGameByMode)((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameModeId.ENDLESS);
        }

        async onStoryClick() {
          await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && StoryUI === void 0 ? (_reportPossibleCrUseOfStoryUI({
            error: Error()
          }), StoryUI) : StoryUI);
        }

        async onPKClick() {
          await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && PKUI === void 0 ? (_reportPossibleCrUseOfPKUI({
            error: Error()
          }), PKUI) : PKUI);
        }

        async onRogueClick() {
          await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && RogueUI === void 0 ? (_reportPossibleCrUseOfRogueUI({
            error: Error()
          }), RogueUI) : RogueUI);
        }

        onBaseSystemClick(evt) {
          const nodeName = evt.target.name;

          switch (nodeName) {
            case (_crd && HomeUIBaseSystem === void 0 ? (_reportPossibleCrUseOfHomeUIBaseSystem({
              error: Error()
            }), HomeUIBaseSystem) : HomeUIBaseSystem).Social:
              break;

            case (_crd && HomeUIBaseSystem === void 0 ? (_reportPossibleCrUseOfHomeUIBaseSystem({
              error: Error()
            }), HomeUIBaseSystem) : HomeUIBaseSystem).Announcement:
              break;

            case (_crd && HomeUIBaseSystem === void 0 ? (_reportPossibleCrUseOfHomeUIBaseSystem({
              error: Error()
            }), HomeUIBaseSystem) : HomeUIBaseSystem).Friend:
              (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
                error: Error()
              }), UIMgr) : UIMgr).openUI(_crd && FriendUI === void 0 ? (_reportPossibleCrUseOfFriendUI({
                error: Error()
              }), FriendUI) : FriendUI);
              break;

            case (_crd && HomeUIBaseSystem === void 0 ? (_reportPossibleCrUseOfHomeUIBaseSystem({
              error: Error()
            }), HomeUIBaseSystem) : HomeUIBaseSystem).Mail:
              (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
                error: Error()
              }), UIMgr) : UIMgr).openUI(_crd && MailUI === void 0 ? (_reportPossibleCrUseOfMailUI({
                error: Error()
              }), MailUI) : MailUI);
              break;

            case (_crd && HomeUIBaseSystem === void 0 ? (_reportPossibleCrUseOfHomeUIBaseSystem({
              error: Error()
            }), HomeUIBaseSystem) : HomeUIBaseSystem).Setting:
              break;

            default:
              (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
                error: Error()
              }), logError) : logError)("HomeUI", `onBaseSystemClick nodeName not found click handler${nodeName}`);
              break;
          }
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "nodeBaseSystem", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "btnBattle", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "btnStory", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "btnPK", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "btnRogue", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=8674b80c87f4b9f781d8ec2011f8c297fe366380.js.map