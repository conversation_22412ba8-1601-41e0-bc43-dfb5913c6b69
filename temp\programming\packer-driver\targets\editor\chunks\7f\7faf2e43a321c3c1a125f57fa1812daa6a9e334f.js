System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, CCFloat, Component, Node, CCString, assetManager, instantiate, UITransform, view, Graphics, Color, Rect, Label, Vec3, Quat, LayerRandomRange, LayerSplicingMode, LayerType, LevelDataBackgroundLayer, LevelDataLayer, LevelDataRandTerrain, LevelDataRandTerrains, LevelDataRandTerrainsGroup, LevelDataScroll, LevelEditorLayerUI, LayerEditorRandomRange, LevelBackgroundLayer, LevelEditorUtils, LevelLayer, LevelRandTerrainsLayersUI, LevelRandTerrainsLayerUI, LevelRandTerrainUI, RandTerrain, WavePreview, EmittierTerrain, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _crd, ccclass, property, executeInEditMode, BackgroundsNodeName, LevelEditorBaseUI;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfLayerRandomRange(extras) {
    _reporterNs.report("LayerRandomRange", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLayerSplicingMode(extras) {
    _reporterNs.report("LayerSplicingMode", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLayerType(extras) {
    _reporterNs.report("LayerType", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelData(extras) {
    _reporterNs.report("LevelData", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataBackgroundLayer(extras) {
    _reporterNs.report("LevelDataBackgroundLayer", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataLayer(extras) {
    _reporterNs.report("LevelDataLayer", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataRandTerrain(extras) {
    _reporterNs.report("LevelDataRandTerrain", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataRandTerrains(extras) {
    _reporterNs.report("LevelDataRandTerrains", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataRandTerrainsGroup(extras) {
    _reporterNs.report("LevelDataRandTerrainsGroup", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataScroll(extras) {
    _reporterNs.report("LevelDataScroll", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelEditorLayerUI(extras) {
    _reporterNs.report("LevelEditorLayerUI", "./LevelEditorLayerUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLayerEditorRandomRange(extras) {
    _reporterNs.report("LayerEditorRandomRange", "./utils", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelBackgroundLayer(extras) {
    _reporterNs.report("LevelBackgroundLayer", "./utils", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelEditorUtils(extras) {
    _reporterNs.report("LevelEditorUtils", "./utils", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelLayer(extras) {
    _reporterNs.report("LevelLayer", "./utils", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelRandTerrainsLayersUI(extras) {
    _reporterNs.report("LevelRandTerrainsLayersUI", "./utils", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelRandTerrainsLayerUI(extras) {
    _reporterNs.report("LevelRandTerrainsLayerUI", "./utils", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelRandTerrainUI(extras) {
    _reporterNs.report("LevelRandTerrainUI", "./utils", _context.meta, extras);
  }

  function _reportPossibleCrUseOfRandTerrain(extras) {
    _reporterNs.report("RandTerrain", "db://assets/bundles/common/script/game/dyncTerrain/RandTerrain", _context.meta, extras);
  }

  function _reportPossibleCrUseOfWavePreview(extras) {
    _reporterNs.report("WavePreview", "./preview/WavePreview", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmittierTerrain(extras) {
    _reporterNs.report("EmittierTerrain", "../../bundles/common/script/game/dyncTerrain/EmittierTerrain", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      CCFloat = _cc.CCFloat;
      Component = _cc.Component;
      Node = _cc.Node;
      CCString = _cc.CCString;
      assetManager = _cc.assetManager;
      instantiate = _cc.instantiate;
      UITransform = _cc.UITransform;
      view = _cc.view;
      Graphics = _cc.Graphics;
      Color = _cc.Color;
      Rect = _cc.Rect;
      Label = _cc.Label;
      Vec3 = _cc.Vec3;
      Quat = _cc.Quat;
    }, function (_unresolved_2) {
      LayerRandomRange = _unresolved_2.LayerRandomRange;
      LayerSplicingMode = _unresolved_2.LayerSplicingMode;
      LayerType = _unresolved_2.LayerType;
      LevelDataBackgroundLayer = _unresolved_2.LevelDataBackgroundLayer;
      LevelDataLayer = _unresolved_2.LevelDataLayer;
      LevelDataRandTerrain = _unresolved_2.LevelDataRandTerrain;
      LevelDataRandTerrains = _unresolved_2.LevelDataRandTerrains;
      LevelDataRandTerrainsGroup = _unresolved_2.LevelDataRandTerrainsGroup;
      LevelDataScroll = _unresolved_2.LevelDataScroll;
    }, function (_unresolved_3) {
      LevelEditorLayerUI = _unresolved_3.LevelEditorLayerUI;
    }, function (_unresolved_4) {
      LayerEditorRandomRange = _unresolved_4.LayerEditorRandomRange;
      LevelBackgroundLayer = _unresolved_4.LevelBackgroundLayer;
      LevelEditorUtils = _unresolved_4.LevelEditorUtils;
      LevelLayer = _unresolved_4.LevelLayer;
      LevelRandTerrainsLayersUI = _unresolved_4.LevelRandTerrainsLayersUI;
      LevelRandTerrainsLayerUI = _unresolved_4.LevelRandTerrainsLayerUI;
      LevelRandTerrainUI = _unresolved_4.LevelRandTerrainUI;
    }, function (_unresolved_5) {
      RandTerrain = _unresolved_5.RandTerrain;
    }, function (_unresolved_6) {
      WavePreview = _unresolved_6.WavePreview;
    }, function (_unresolved_7) {
      EmittierTerrain = _unresolved_7.EmittierTerrain;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "a4bf2J2KGJJV7RbX1jwoQWJ", "LevelEditorBaseUI", undefined);

      __checkObsolete__(['_decorator', 'CCFloat', 'Component', 'Node', 'CCString', 'Prefab', 'assetManager', 'instantiate', 'UITransform', 'view', 'Graphics', 'Color', 'Rect', 'Label', 'Vec3', 'Quat']);

      ({
        ccclass,
        property,
        executeInEditMode
      } = _decorator);
      BackgroundsNodeName = "backgrounds";

      _export("LevelEditorBaseUI", LevelEditorBaseUI = (_dec = ccclass('LevelEditorBaseUI'), _dec2 = executeInEditMode(), _dec3 = property(CCString), _dec4 = property({
        type: CCFloat,
        displayName: "关卡时长(ms)",
        min: 2000
      }), _dec5 = property({
        type: _crd && LevelBackgroundLayer === void 0 ? (_reportPossibleCrUseOfLevelBackgroundLayer({
          error: Error()
        }), LevelBackgroundLayer) : LevelBackgroundLayer,
        displayName: "背景"
      }), _dec6 = property({
        type: [_crd && LevelLayer === void 0 ? (_reportPossibleCrUseOfLevelLayer({
          error: Error()
        }), LevelLayer) : LevelLayer],
        displayName: "地面"
      }), _dec7 = property({
        type: [_crd && LevelLayer === void 0 ? (_reportPossibleCrUseOfLevelLayer({
          error: Error()
        }), LevelLayer) : LevelLayer],
        displayName: "天空"
      }), _dec(_class = _dec2(_class = (_class2 = class LevelEditorBaseUI extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "levelname", _descriptor, this);

          _initializerDefineProperty(this, "totalTime", _descriptor2, this);

          this._totalHeight = 0;

          _initializerDefineProperty(this, "backgroundLayer", _descriptor3, this);

          _initializerDefineProperty(this, "floorLayers", _descriptor4, this);

          _initializerDefineProperty(this, "skyLayers", _descriptor5, this);

          this.backgroundLayerNode = null;
          this.floorLayersNode = null;
          this.skyLayersNode = null;
          this._isLoadingScrollNodes = false;
          this._play = false;
          this._drawNode = null;
        }

        onLoad() {
          var _this$floorLayersNode;

          console.log(`LevelEditorBaseUI start.`);
          this.backgroundLayerNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
            error: Error()
          }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(this.node, "BackgroundLayer");
          this.floorLayersNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
            error: Error()
          }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(this.node, "FloorLayers");
          this.skyLayersNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
            error: Error()
          }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(this.node, "SkyLayers");
          this._drawNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
            error: Error()
          }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(this.node, "DrawNode");
          console.log(`LevelEditorBaseUI start ${(_this$floorLayersNode = this.floorLayersNode) == null ? void 0 : _this$floorLayersNode.uuid}`);
        }

        update(dt) {
          this.checkLayerNode(this.floorLayersNode, this.floorLayers, dt);
          this.checkLayerNode(this.skyLayersNode, this.skyLayers, dt);
        }

        setBackgroundNodePosition(node, yOff) {
          const height = node.getComponent(UITransform).contentSize.height;
          node.setPosition(0, yOff - view.getVisibleSize().height / 2 + height / 2);
          return height;
        }

        tick(progress) {
          let yOff = 0;

          for (let i = 0; i < this.backgroundLayer.backgroundsNode.children.length; i++) {
            var bg = this.backgroundLayer.backgroundsNode.children[i];
            yOff += this.setBackgroundNodePosition(bg, yOff);
          }

          while (this.backgroundLayer.backgrounds.length > 0 && yOff < this._totalHeight) {
            let bg = null;
            let bgIndex = this.backgroundLayer.backgroundsNode.children.length % this.backgroundLayer.backgrounds.length;
            const prefab = this.backgroundLayer.backgrounds[bgIndex];

            if (prefab != null) {
              bg = instantiate(prefab);
            }

            if (bg == null) {
              bg = new Node("empty");
              bg.addComponent(UITransform).height = 1024;
            }

            this.backgroundLayer.backgroundsNode.addChild(bg);
            yOff += this.setBackgroundNodePosition(bg, yOff);
          }

          for (let i = this.backgroundLayer.backgroundsNode.children.length - 1; i >= 0; i--) {
            const bg = this.backgroundLayer.backgroundsNode.children[i];

            if (bg.position.y - bg.getComponent(UITransform).height / 2 > this._totalHeight) {
              bg.removeFromParent();
            } else {
              break;
            }
          }

          this.backgroundLayer.node.getComponent(_crd && LevelEditorLayerUI === void 0 ? (_reportPossibleCrUseOfLevelEditorLayerUI({
            error: Error()
          }), LevelEditorLayerUI) : LevelEditorLayerUI).tick(progress, this.totalTime, this.backgroundLayer.speed);
          this.floorLayers.forEach(layer => {
            if (layer.node && layer.node.getComponent(_crd && LevelEditorLayerUI === void 0 ? (_reportPossibleCrUseOfLevelEditorLayerUI({
              error: Error()
            }), LevelEditorLayerUI) : LevelEditorLayerUI)) {
              layer.node.getComponent(_crd && LevelEditorLayerUI === void 0 ? (_reportPossibleCrUseOfLevelEditorLayerUI({
                error: Error()
              }), LevelEditorLayerUI) : LevelEditorLayerUI).tick(progress, this.totalTime, layer.speed);
            }
          });
          this.skyLayers.forEach(layer => {
            if (layer.node && layer.node.getComponent(_crd && LevelEditorLayerUI === void 0 ? (_reportPossibleCrUseOfLevelEditorLayerUI({
              error: Error()
            }), LevelEditorLayerUI) : LevelEditorLayerUI)) {
              layer.node.getComponent(_crd && LevelEditorLayerUI === void 0 ? (_reportPossibleCrUseOfLevelEditorLayerUI({
                error: Error()
              }), LevelEditorLayerUI) : LevelEditorLayerUI).tick(progress, this.totalTime, layer.speed);
            }
          });
        }

        static addLayer(parentNode, name) {
          var layerNode = new Node(name);
          var layerCom = layerNode.addComponent(_crd && LevelEditorLayerUI === void 0 ? (_reportPossibleCrUseOfLevelEditorLayerUI({
            error: Error()
          }), LevelEditorLayerUI) : LevelEditorLayerUI);
          parentNode.addChild(layerNode);
          return layerCom;
        }

        checkLayerNode(parentNode, layers, dt) {
          var removeLayerNodes = [];
          parentNode.children.forEach(node => {
            var layerCom = node.getComponent(_crd && LevelEditorLayerUI === void 0 ? (_reportPossibleCrUseOfLevelEditorLayerUI({
              error: Error()
            }), LevelEditorLayerUI) : LevelEditorLayerUI);

            if (layerCom == null) {
              console.log(`Level checkLayerNode remove ${node.name} because layerCom == null"`);
              removeLayerNodes.push(node);
              return;
            }

            if (layers.find(layer => layer.node == node) == null) {
              console.log(`Level checkLayerNode remove ${node.name} because not in layers"`);
              removeLayerNodes.push(node);
              return;
            }
          });
          removeLayerNodes.forEach(element => {
            element.removeFromParent();
          });
          layers.forEach((layer, i) => {
            if (layer.node == null || layer.node.isValid == false) {
              console.log(`Level checkLayerNode add because layer == null`);
              layer.node = LevelEditorBaseUI.addLayer(parentNode, `layer_${i}`).node;
            }

            if (layer.type === (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
              error: Error()
            }), LayerType) : LayerType).Scroll) {
              this._checkScrollNode(layer, layer.node);
            } else if (layer.type === (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
              error: Error()
            }), LayerType) : LayerType).Random) {
              this._checkRandTerrainNode(layer, layer.node);
            } else if (layer.type === (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
              error: Error()
            }), LayerType) : LayerType).Emittier) {
              this._checkEmittierNode(layer, layer.node);

              this._updateEmittierNode(dt, layer.node);
            }
          });
        }

        async _checkScrollNode(data, parentNode) {
          const scrollsNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
            error: Error()
          }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(parentNode, "scrolls");

          if (data.type != (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
            error: Error()
          }), LayerType) : LayerType).Scroll) {
            scrollsNode.removeAllChildren();
            return;
          }

          if (this._isLoadingScrollNodes) {
            //console.log(`LevelEditorBaseUI _checkScrollNode _isLoadingScrollNodes ${this._isLoadingScrollNodes} scrollsNode.children.length ${scrollsNode.children.length}`);
            return;
          }

          const isCountMatch = scrollsNode.children.length === data.scrollLayers.length; //console.log(`LevelEditorBaseUI _checkScrollNode children.length ${scrollsNode.children.length} data.scrollLayers.length ${data.scrollLayers.length}`); 

          if (!isCountMatch) {
            const loadPromises = [];
            scrollsNode.removeAllChildren();
            this._isLoadingScrollNodes = true; // 标记为加载中

            data.scrollLayers.forEach((scroll, index) => {
              if (scroll.scrollPrefabs.length <= 0) return;
              scroll.scrollPrefabs.forEach(prefab => {
                const loadPromise = new Promise(resolve => {
                  assetManager.loadAny({
                    uuid: prefab.uuid
                  }, (err, loadedPrefab) => {
                    if (err) {
                      console.error("Failed to load prefab:", err);
                      resolve();
                    } else {
                      resolve();
                    }
                  });
                });
                loadPromises.push(loadPromise);
              });
            });
            await Promise.all(loadPromises); //console.log(`LevelEditorBaseUI _checkScrollNode data.scrollLayers ${data.scrollLayers.length}`);

            data.scrollLayers.forEach((scroll, index) => {
              const scrollNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
                error: Error()
              }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(scrollsNode, `scroll_${index}`);
              const totalHeight = data.speed * this.totalTime / 1000;
              console.log(`LevelEditorBaseUI _checkScrollNode totalHeight ${totalHeight}`);
              let posOffsetY = 0;
              let height = 0;
              let prefabIndex = 0;

              while (height < totalHeight) {
                const curPrefab = scroll.scrollPrefabs[prefabIndex];
                const child = instantiate(curPrefab);
                const randomOffsetX = Math.random() * (scroll.splicingOffsetX.max - scroll.splicingOffsetX.min) + scroll.splicingOffsetX.min;
                child.setPosition(randomOffsetX, posOffsetY, 0);
                let offY = 0;

                if (scroll.splicingMode === (_crd && LayerSplicingMode === void 0 ? (_reportPossibleCrUseOfLayerSplicingMode({
                  error: Error()
                }), LayerSplicingMode) : LayerSplicingMode).node_height) {
                  offY = child.getComponent(UITransform).contentSize.height;
                } else if (scroll.splicingMode === (_crd && LayerSplicingMode === void 0 ? (_reportPossibleCrUseOfLayerSplicingMode({
                  error: Error()
                }), LayerSplicingMode) : LayerSplicingMode).fix_height) {
                  offY = 1334;
                } else if (scroll.splicingMode === (_crd && LayerSplicingMode === void 0 ? (_reportPossibleCrUseOfLayerSplicingMode({
                  error: Error()
                }), LayerSplicingMode) : LayerSplicingMode).random_height) {
                  offY = Math.max(scroll.splicingOffsetY.min, scroll.splicingOffsetY.max) + child.getComponent(UITransform).contentSize.height;
                }

                scrollNode.addChild(child);
                posOffsetY += offY;
                height += offY;
                prefabIndex = (prefabIndex + 1) % scroll.scrollPrefabs.length;
              }
            });
            this._isLoadingScrollNodes = false;
          }
        }

        _checkRandTerrainNode(data, parentNode) {
          const dynamicNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
            error: Error()
          }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(parentNode, "dynamic"); // 删除所有多余的dyna节点

          const currentDynaNodes = dynamicNode.children;

          for (let i = currentDynaNodes.length - 1; i >= 0; i--) {
            const node = currentDynaNodes[i];
            const match = node.name.match(/^dyna_(\d+)$/);

            if (match) {
              const index = parseInt(match[1]);

              if (index >= data.randomLayers.length) {
                node.removeFromParent();
              }
            }
          }

          if (data.type != (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
            error: Error()
          }), LayerType) : LayerType).Random || data.randomLayers.length === 0) {
            dynamicNode.removeAllChildren();
            return;
          }

          let needRebuild = false;
          const rebuildList = [];

          for (let i = 0; i < data.randomLayers.length; i++) {
            const randTerrains = data.randomLayers[i];
            const dynaNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
              error: Error()
            }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(dynamicNode, `dyna_${i}`); // 计算该dyna节点应有的总地形元素数量

            let expectedChildCount = 0;

            for (const terrains of randTerrains.dynamicTerrains) {
              expectedChildCount += terrains.dynamicTerrain.length;
            } // 检查子节点数量是否匹配


            if (dynaNode.children.length !== expectedChildCount) {
              needRebuild = true;
              rebuildList.push(i);
              continue;
            } // 检查每个子节点对应的预制体UUID是否匹配


            let childIndex = 0;
            let isUUIDMatch = true;

            for (const terrains of randTerrains.dynamicTerrains) {
              for (const terrain of terrains.dynamicTerrain) {
                var _childNode$_prefab, _terrain$terrainEleme;

                const childNode = dynaNode.children[childIndex]; // @ts-ignore

                const childPrefabUUID = (_childNode$_prefab = childNode._prefab) == null || (_childNode$_prefab = _childNode$_prefab.asset) == null ? void 0 : _childNode$_prefab._uuid;
                const terrainUUID = terrain == null || (_terrain$terrainEleme = terrain.terrainElement) == null ? void 0 : _terrain$terrainEleme.uuid;

                if (childPrefabUUID !== terrainUUID) {
                  isUUIDMatch = false;
                  break;
                }

                childIndex++;
              }

              if (!isUUIDMatch) break;
            }

            if (!isUUIDMatch) {
              needRebuild = true;
              rebuildList.push(i);
            }
          }

          if (needRebuild) {
            //console.log("LevelEditorBaseUI _checkRandTerrainNode need rebuild");
            for (const index of rebuildList) {
              const dynaNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
                error: Error()
              }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(dynamicNode, `dyna_${index}`);
              dynaNode.removeAllChildren();
              const randTerrains = data.randomLayers[index]; // 遍历所有地形组

              for (let j = 0; j < randTerrains.dynamicTerrains.length; j++) {
                const terrains = randTerrains.dynamicTerrains[j]; // 遍历地形组中的每个地形元素

                for (let k = 0; k < terrains.dynamicTerrain.length; k++) {
                  var _terrain$terrainEleme2;

                  const terrain = terrains.dynamicTerrain[k];
                  assetManager.loadAny({
                    uuid: terrain == null || (_terrain$terrainEleme2 = terrain.terrainElement) == null ? void 0 : _terrain$terrainEleme2.uuid
                  }, (err, prefab) => {
                    if (err) {
                      //console.error(`加载地形元素失败: ${terrain?.terrainElements?.uuid}`, err);
                      return;
                    }

                    const node = instantiate(prefab);
                    node.name = `rand_${j}_${k}`;
                    dynaNode.addChild(node);
                    const randomOffsetX = Math.random() * (terrain.offSetX.max - terrain.offSetX.min) + terrain.offSetX.min;
                    const randomOffsetY = Math.random() * (terrain.offSetY.max - terrain.offSetY.min) + terrain.offSetY.min;
                    dynamicNode.setPosition(randomOffsetX, randomOffsetY, 0);
                  });
                }
              }
            }
          }
        }

        _checkEmittierNode(data, parentNode) {
          const emittierNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
            error: Error()
          }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(parentNode, "emittiers");

          if (data.type != (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
            error: Error()
          }), LayerType) : LayerType).Emittier) {
            emittierNode.removeAllChildren();
            return;
          } // 1. 按索引遍历数据层


          for (let i = 0; i < data.emittierLayers.length; i++) {
            const emitterData = data.emittierLayers[i]; // 安全获取节点 - 检查索引是否有效

            const existingNode = i < emittierNode.children.length ? emittierNode.children[i] : null; // 检查是否需要重新加载节点

            let needReload = false;

            if (!existingNode || emitterData === null) {
              // 节点不存在，需要创建新节点
              needReload = true;
            } else {
              var _existingNode$_prefab;

              // 检查UUID是否匹配
              // @ts-ignore
              if (((_existingNode$_prefab = existingNode._prefab) == null || (_existingNode$_prefab = _existingNode$_prefab.asset) == null ? void 0 : _existingNode$_prefab.uuid) !== emitterData.uuid) {
                needReload = true;
              }
            } // 重新加载节点


            if (needReload) {
              // 保存原有位置和缩放信息（如果节点存在）
              const oldPosition = existingNode ? existingNode.position.clone() : new Vec3(0, 0, 0);
              const oldRotation = existingNode ? existingNode.rotation.clone() : new Quat();
              const oldScale = existingNode ? existingNode.scale.clone() : new Vec3(1, 1, 1); // 移除旧节点（如果存在）

              if (existingNode) {
                existingNode.removeFromParent();
              }

              if (emitterData && emitterData.uuid) {
                assetManager.loadAny({
                  uuid: emitterData.uuid
                }, (err, prefab) => {
                  if (err) {
                    return;
                  } else {
                    const emitterNode = instantiate(prefab);
                    emitterNode.name = `emittier_${i}`;
                    emitterNode.position = oldPosition;
                    emitterNode.rotation = oldRotation;
                    emitterNode.scale = oldScale;
                    emittierNode.addChild(emitterNode);
                    emitterNode.setSiblingIndex(i);
                  }
                });
              } else {
                return;
              }
            }
          } // 2. 删除多余的节点


          while (emittierNode.children.length > data.emittierLayers.length) {
            const lastIndex = emittierNode.children.length - 1;
            emittierNode.children[lastIndex].removeFromParent();
          }
        }

        _playEmittierNode(value, parentNode) {
          parentNode.children.forEach(node => {
            const emittierNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
              error: Error()
            }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(node, "emittiers");
            console.log("play emittier   ---------- 0");

            if (emittierNode === null || emittierNode.children.length === 0) {
              return;
            }

            console.log("play emittier   ---------- 0 <USER> <GROUP>");

            for (let i = 0; i < emittierNode.children.length; i++) {
              const emitterNode = emittierNode.children[i];
              const emittier = emitterNode.getComponent(_crd && EmittierTerrain === void 0 ? (_reportPossibleCrUseOfEmittierTerrain({
                error: Error()
              }), EmittierTerrain) : EmittierTerrain);
              console.log("play emittier", value);

              if (emittier) {
                console.log("play emittier ------ 1", value);
                emittier.play(value);
              }
            }
          });
        }

        _updateEmittierNode(dt, parentNode) {
          const emittierNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
            error: Error()
          }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(parentNode, "emittiers");

          if (emittierNode === null || emittierNode.children.length === 0) {
            return;
          }

          for (let i = 0; i < emittierNode.children.length; i++) {
            const emitterNode = emittierNode.children[i];
            const emittier = emitterNode.getComponent(_crd && EmittierTerrain === void 0 ? (_reportPossibleCrUseOfEmittierTerrain({
              error: Error()
            }), EmittierTerrain) : EmittierTerrain);

            if (emittier) {
              emittier.tick(dt);
            }
          }
        }

        initByLevelData(data) {
          var _data$backgroundLayer, _data$backgroundLayer2, _data$backgroundLayer3, _instance;

          this.levelname = data.name;
          this.totalTime = data.totalTime;
          this.backgroundLayerNode.removeAllChildren();
          this.backgroundLayer = new (_crd && LevelBackgroundLayer === void 0 ? (_reportPossibleCrUseOfLevelBackgroundLayer({
            error: Error()
          }), LevelBackgroundLayer) : LevelBackgroundLayer)();
          this.backgroundLayer.backgrounds = [];
          (_data$backgroundLayer = data.backgroundLayer) == null || (_data$backgroundLayer = _data$backgroundLayer.backgrounds) == null || _data$backgroundLayer.forEach(background => {
            assetManager.loadAny({
              uuid: background
            }, (err, prefab) => {
              if (err) {
                console.error("LevelEditorBaseUI initByLevelData load background prefab err", err);
                return;
              }

              this.backgroundLayer.backgrounds.push(prefab);
            });
          });
          this.backgroundLayer.speed = (_data$backgroundLayer2 = data.backgroundLayer) == null ? void 0 : _data$backgroundLayer2.speed;
          this.backgroundLayer.remark = (_data$backgroundLayer3 = data.backgroundLayer) == null ? void 0 : _data$backgroundLayer3.remark;
          this._totalHeight = this.backgroundLayer.speed * this.totalTime / 1000;
          this.backgroundLayer.node = LevelEditorBaseUI.addLayer(this.backgroundLayerNode, "layer").node;
          this.backgroundLayer.backgroundsNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
            error: Error()
          }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(this.backgroundLayer.node, BackgroundsNodeName);
          this.backgroundLayer.backgroundsNode.setSiblingIndex(0);
          this.backgroundLayer.node.getComponent(_crd && LevelEditorLayerUI === void 0 ? (_reportPossibleCrUseOfLevelEditorLayerUI({
            error: Error()
          }), LevelEditorLayerUI) : LevelEditorLayerUI).initByLevelData(data.backgroundLayer);
          this.floorLayers = [];
          this.skyLayers = [];
          LevelEditorBaseUI.initLayers(this.floorLayersNode, this.floorLayers, data.floorLayers);
          LevelEditorBaseUI.initLayers(this.skyLayersNode, this.skyLayers, data.skyLayers);
          (_instance = (_crd && WavePreview === void 0 ? (_reportPossibleCrUseOfWavePreview({
            error: Error()
          }), WavePreview) : WavePreview).instance) == null || _instance.reset();

          this._drawNodeGraphics();
        }

        static initLayers(parentNode, layers, dataLayers) {
          parentNode.removeAllChildren();
          dataLayers.forEach((layer, i) => {
            var levelLayer = new (_crd && LevelLayer === void 0 ? (_reportPossibleCrUseOfLevelLayer({
              error: Error()
            }), LevelLayer) : LevelLayer)();
            levelLayer.speed = layer.speed;
            levelLayer.type = layer.type;
            levelLayer.remark = layer.remark;
            levelLayer.zIndex = layer.zIndex;
            levelLayer.node = LevelEditorBaseUI.addLayer(parentNode, `layer_${i}`).node;
            levelLayer.node.setSiblingIndex(layer.zIndex);
            const levelEditorLayerUI = levelLayer.node.getComponent(_crd && LevelEditorLayerUI === void 0 ? (_reportPossibleCrUseOfLevelEditorLayerUI({
              error: Error()
            }), LevelEditorLayerUI) : LevelEditorLayerUI);

            if (layer.type === (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
              error: Error()
            }), LayerType) : LayerType).Scroll) {
              console.log("initScorllsByLevelData levelLayer.length ", levelLayer.scrollLayers.length);
              levelEditorLayerUI.initScorllsByLevelData(levelLayer, layer);
            } else if (layer.type === (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
              error: Error()
            }), LayerType) : LayerType).Random) {
              levelLayer.randomLayers = [];
              layer.dynamics.forEach(dynamic => {
                var randomLayers = new (_crd && LevelRandTerrainsLayersUI === void 0 ? (_reportPossibleCrUseOfLevelRandTerrainsLayersUI({
                  error: Error()
                }), LevelRandTerrainsLayersUI) : LevelRandTerrainsLayersUI)();
                dynamic.group.forEach(randTerrains => {
                  console.log("initRandom ByLevelData randTerrains ", randTerrains.scale.x, randTerrains.scale.y);
                  var randomLayer = new (_crd && LevelRandTerrainsLayerUI === void 0 ? (_reportPossibleCrUseOfLevelRandTerrainsLayerUI({
                    error: Error()
                  }), LevelRandTerrainsLayerUI) : LevelRandTerrainsLayerUI)();
                  randomLayer.dynamicTerrain = [];
                  randomLayer.weight = randTerrains.weight;
                  randTerrains.terrains.forEach(terrain => {
                    var dynamicTerrain = new (_crd && LevelRandTerrainUI === void 0 ? (_reportPossibleCrUseOfLevelRandTerrainUI({
                      error: Error()
                    }), LevelRandTerrainUI) : LevelRandTerrainUI)();
                    dynamicTerrain.weight = terrain.weight;
                    assetManager.loadAny({
                      uuid: terrain.uuid
                    }, (err, prefab) => {
                      if (err) {
                        return;
                      }

                      dynamicTerrain.terrainElement = prefab;
                      dynamicTerrain.offSetX = new (_crd && LayerEditorRandomRange === void 0 ? (_reportPossibleCrUseOfLayerEditorRandomRange({
                        error: Error()
                      }), LayerEditorRandomRange) : LayerEditorRandomRange)();
                      dynamicTerrain.offSetX.min = terrain.offSetX.min;
                      dynamicTerrain.offSetX.max = terrain.offSetX.max;
                      dynamicTerrain.offSetY = new (_crd && LayerEditorRandomRange === void 0 ? (_reportPossibleCrUseOfLayerEditorRandomRange({
                        error: Error()
                      }), LayerEditorRandomRange) : LayerEditorRandomRange)();
                      dynamicTerrain.offSetY.min = terrain.offSetY.min;
                      dynamicTerrain.offSetY.max = terrain.offSetY.max;
                      randomLayer.dynamicTerrain.push(dynamicTerrain);
                    });
                  });
                  randomLayers.dynamicTerrains.push(randomLayer);
                });
                levelLayer.randomLayers.push(randomLayers);
              });
            } else if (layer.type === (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
              error: Error()
            }), LayerType) : LayerType).Emittier) {
              levelEditorLayerUI.initEmittierLevelData(levelLayer, layer);
            }

            levelEditorLayerUI.initByLevelData(layer);
            layers.push(levelLayer);
          });
        }

        _fillLevelLayerData(layer, dataLayer) {
          dataLayer.speed = layer.speed;
          dataLayer.type = layer.type;
          dataLayer.remark = layer.remark;
          dataLayer.zIndex = layer.zIndex;
          dataLayer.totalTime = this.totalTime;

          if (layer.type === (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
            error: Error()
          }), LayerType) : LayerType).Scroll) {
            dataLayer.scrolls = [];
            layer.scrollLayers.forEach(scrollLayer => {
              var dataScroll = new (_crd && LevelDataScroll === void 0 ? (_reportPossibleCrUseOfLevelDataScroll({
                error: Error()
              }), LevelDataScroll) : LevelDataScroll)();
              scrollLayer.scrollPrefabs.forEach(scrollPrefab => {
                dataScroll.uuids.push(scrollPrefab == null ? void 0 : scrollPrefab.uuid);
              });
              dataScroll.weight = scrollLayer.weight;
              dataScroll.splicingMode = scrollLayer.splicingMode;
              dataScroll.offSetY = new (_crd && LayerRandomRange === void 0 ? (_reportPossibleCrUseOfLayerRandomRange({
                error: Error()
              }), LayerRandomRange) : LayerRandomRange)();
              dataScroll.offSetY.min = scrollLayer.splicingOffsetY.min;
              dataScroll.offSetY.max = scrollLayer.splicingOffsetY.max;
              dataScroll.offSetX = new (_crd && LayerRandomRange === void 0 ? (_reportPossibleCrUseOfLayerRandomRange({
                error: Error()
              }), LayerRandomRange) : LayerRandomRange)();
              dataScroll.offSetX.min = scrollLayer.splicingOffsetX.min;
              dataScroll.offSetX.max = scrollLayer.splicingOffsetX.max;
              dataLayer.scrolls.push(dataScroll);
              console.log("LevelEditorBaseUI fill scrollLayersData", dataLayer);
            });
          } else if (layer.type === (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
            error: Error()
          }), LayerType) : LayerType).Random) {
            dataLayer.dynamics = [];
            layer.randomLayers.forEach(randomLayer => {
              var datas = new (_crd && LevelDataRandTerrainsGroup === void 0 ? (_reportPossibleCrUseOfLevelDataRandTerrainsGroup({
                error: Error()
              }), LevelDataRandTerrainsGroup) : LevelDataRandTerrainsGroup)();
              randomLayer.dynamicTerrains.forEach(terrains => {
                var data = new (_crd && LevelDataRandTerrains === void 0 ? (_reportPossibleCrUseOfLevelDataRandTerrains({
                  error: Error()
                }), LevelDataRandTerrains) : LevelDataRandTerrains)();
                data.terrains = [];
                data.weight = terrains.weight;
                terrains.dynamicTerrain.forEach(terrainElement => {
                  var _terrainElement$terra, _terrainElement$offSe, _terrainElement$offSe2, _terrainElement$offSe3, _terrainElement$offSe4;

                  var terrainData = new (_crd && LevelDataRandTerrain === void 0 ? (_reportPossibleCrUseOfLevelDataRandTerrain({
                    error: Error()
                  }), LevelDataRandTerrain) : LevelDataRandTerrain)();
                  terrainData.weight = terrainElement.weight;
                  terrainData.uuid = (_terrainElement$terra = terrainElement.terrainElement) == null ? void 0 : _terrainElement$terra.uuid;
                  terrainData.offSetX = new (_crd && LayerRandomRange === void 0 ? (_reportPossibleCrUseOfLayerRandomRange({
                    error: Error()
                  }), LayerRandomRange) : LayerRandomRange)((_terrainElement$offSe = terrainElement.offSetX) == null ? void 0 : _terrainElement$offSe.min, (_terrainElement$offSe2 = terrainElement.offSetX) == null ? void 0 : _terrainElement$offSe2.max);
                  terrainData.offSetY = new (_crd && LayerRandomRange === void 0 ? (_reportPossibleCrUseOfLayerRandomRange({
                    error: Error()
                  }), LayerRandomRange) : LayerRandomRange)((_terrainElement$offSe3 = terrainElement.offSetY) == null ? void 0 : _terrainElement$offSe3.min, (_terrainElement$offSe4 = terrainElement.offSetY) == null ? void 0 : _terrainElement$offSe4.max);
                  data.terrains.push(terrainData);
                });
                datas.group.push(data);
              });
              dataLayer.dynamics.push(datas);
            });
          } else if (layer.type === (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
            error: Error()
          }), LayerType) : LayerType).Emittier) {}

          layer.node.getComponent(_crd && LevelEditorLayerUI === void 0 ? (_reportPossibleCrUseOfLevelEditorLayerUI({
            error: Error()
          }), LevelEditorLayerUI) : LevelEditorLayerUI).fillLevelData(dataLayer);
        }

        _fillLevelLayersData(layers, dataLayers) {
          layers.sort((a, b) => a.zIndex - b.zIndex);
          layers.forEach(layer => {
            var levelLayer = new (_crd && LevelDataLayer === void 0 ? (_reportPossibleCrUseOfLevelDataLayer({
              error: Error()
            }), LevelDataLayer) : LevelDataLayer)();

            this._fillLevelLayerData(layer, levelLayer);

            dataLayers.push(levelLayer);
          });
        }

        fillLevelData(data) {
          data.name = this.levelname;
          data.totalTime = this.totalTime;
          data.backgroundLayer = new (_crd && LevelDataBackgroundLayer === void 0 ? (_reportPossibleCrUseOfLevelDataBackgroundLayer({
            error: Error()
          }), LevelDataBackgroundLayer) : LevelDataBackgroundLayer)();

          for (let i = 0; i < this.backgroundLayer.backgrounds.length; i++) {
            const prefab = this.backgroundLayer.backgrounds[i];

            if (prefab == null) {
              continue;
            }

            data.backgroundLayer.backgrounds.push(prefab.uuid);
          }

          this._fillLevelLayerData(this.backgroundLayer, data.backgroundLayer);

          data.floorLayers = [];
          data.skyLayers = [];

          this._fillLevelLayersData(this.floorLayers, data.floorLayers);

          this._fillLevelLayersData(this.skyLayers, data.skyLayers);
        }

        playLevel(bPlay, progress) {
          this._setTimeNode(progress);

          this._playLayer([this.backgroundLayer], bPlay, progress);

          this._playLayer(this.floorLayers, bPlay, progress);

          this._playLayer(this.skyLayers, bPlay, progress);

          if (this._play === bPlay) {
            return;
          }

          this._play = bPlay;

          if (bPlay) {
            this._drawMask();
          } else {
            this._drawMaskClear();
          }

          this._playEmittierNode(this._play, this.floorLayersNode);

          this._playEmittierNode(this._play, this.skyLayersNode);

          this._randLayerActive(this.floorLayers, this.floorLayersNode, this._play);

          this._randLayerActive(this.skyLayers, this.skyLayersNode, this._play);
        }

        _playLayer(layers, bPlay, progress) {
          layers.forEach(layer => {
            layer.node.getComponent(_crd && LevelEditorLayerUI === void 0 ? (_reportPossibleCrUseOfLevelEditorLayerUI({
              error: Error()
            }), LevelEditorLayerUI) : LevelEditorLayerUI).play(bPlay, progress * this.totalTime * layer.speed);
          });
        }

        _randLayerActive(layers, parentNode, bPlay) {
          layers.forEach((layer, index) => {
            if (layer.type === (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
              error: Error()
            }), LayerType) : LayerType).Random) {
              const layerNode = parentNode.getChildByName(`layer_${index}`);
              const dynamicNode = layerNode.getChildByName("dynamic");

              if (bPlay) {
                if (dynamicNode.children.length <= 0) return; // 先激活所有dyna_x节点

                dynamicNode.children.forEach(dynaNode => dynaNode.active = true); // 遍历所有dyna_x节点（每个对应一个地形策略组）

                dynamicNode.children.forEach((dynaNode, groupIndex) => {
                  let length = 0;

                  for (let i = 0; i < layer.randomLayers[groupIndex].dynamicTerrains.length; i++) {
                    length += layer.randomLayers[groupIndex].dynamicTerrains[i].dynamicTerrain.length;
                  }

                  if (dynaNode.children.length != length) {
                    return;
                  }

                  let layerRandIndex = -1;
                  let groupRandIndex = -1; // 策略的总权重

                  let layersTotalWeight = 0;

                  for (const randomTerrain of layer.randomLayers[groupIndex].dynamicTerrains) {
                    layersTotalWeight += randomTerrain.weight;
                  }

                  let layersRandWeight = Math.random() * layersTotalWeight;
                  let layersAccWeight = 0;

                  if (layer.randomLayers[groupIndex].dynamicTerrains.length === 1) {
                    layerRandIndex = 0;
                  } else {
                    for (let j = 0; j < layer.randomLayers[groupIndex].dynamicTerrains.length; j++) {
                      layersAccWeight += layer.randomLayers[groupIndex].dynamicTerrains[j].weight;

                      if (layersRandWeight <= layersAccWeight) {
                        layerRandIndex = j;
                        console.log("LevelEditorBaseUI _rand random layerRandIndex", layerRandIndex);
                        break;
                      }
                    }
                  } // 组的总权重


                  let groupTotalWeight = 0;

                  if (layer.randomLayers[groupIndex] === null || layer.randomLayers[groupIndex].dynamicTerrains.length === 0 || layer.randomLayers[groupIndex].dynamicTerrains[layerRandIndex] === null) {
                    console.error("策划佬地形策略组没有配东西!!!!!! 在layer:", dynaNode.parent.parent.name);
                    return;
                  }

                  layer.randomLayers[groupIndex].dynamicTerrains[layerRandIndex].dynamicTerrain.forEach(terrains => {
                    groupTotalWeight += terrains.weight;
                  });
                  let groupRandWeight = Math.random() * groupTotalWeight;
                  let groupAccWeight = 0;

                  if (layer.randomLayers[groupIndex].dynamicTerrains[layerRandIndex].dynamicTerrain.length === 1) {
                    groupRandIndex = 0;
                  } else {
                    console.log("LevelEditorBaseUI _rand random dynamicTerrains length", layer.randomLayers[groupIndex].dynamicTerrains[layerRandIndex].dynamicTerrain.length);

                    for (let j = 0; j < layer.randomLayers[groupIndex].dynamicTerrains[layerRandIndex].dynamicTerrain.length; j++) {
                      groupAccWeight += layer.randomLayers[groupIndex].dynamicTerrains[layerRandIndex].dynamicTerrain[j].weight;

                      if (groupRandWeight <= groupAccWeight) {
                        groupRandIndex = j;
                        console.log("LevelEditorBaseUI _rand random groupRandIndex", groupRandIndex);
                        break;
                      }
                    }
                  } // 显示选中的策略


                  dynaNode.children.forEach(terrainNode => {
                    const match = terrainNode.name.match(/^rand_(\d+)_(\d+)$/);
                    let active = false;

                    if (match) {
                      const index = parseInt(match[1]);
                      const itemIndex = parseInt(match[2]);

                      if (index === layerRandIndex && itemIndex === groupRandIndex) {
                        active = true;
                      }

                      terrainNode.active = active;
                      const terrain = terrainNode.getComponent(_crd && RandTerrain === void 0 ? (_reportPossibleCrUseOfRandTerrain({
                        error: Error()
                      }), RandTerrain) : RandTerrain);

                      if (terrain) {
                        terrain.play(active);
                      }
                    } else {
                      terrainNode.active = active;
                    }
                  });
                });
              } else {
                dynamicNode.children.forEach(dynaNode => {
                  dynaNode.active = true;
                  dynaNode.children.forEach(terrainNode => {
                    terrainNode.active = true;
                    const terrain = terrainNode.getComponent(_crd && RandTerrain === void 0 ? (_reportPossibleCrUseOfRandTerrain({
                      error: Error()
                    }), RandTerrain) : RandTerrain);

                    if (terrain) {
                      terrain.play(false);
                    }
                  });
                });
              }
            } else if (layer.type === (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
              error: Error()
            }), LayerType) : LayerType).Scroll) {
              parentNode.children.forEach(layerNode => {
                // 滚动层逻辑
                const scrollsNode = layerNode.getChildByName("scrolls");

                if (scrollsNode.children.length !== layer.scrollLayers.length) {
                  return;
                }

                scrollsNode.children.forEach(layerNode => layerNode.active = true);

                if (bPlay) {
                  // 计算总权重
                  let totalWeight = 0;

                  for (const scrollLayer of layer.scrollLayers) {
                    totalWeight += scrollLayer.weight;
                  } // 随机选择要显示的滚动体


                  let randomWeight = Math.random() * totalWeight;
                  let selectedIndex = -1;

                  for (let i = 0; i < layer.scrollLayers.length; i++) {
                    randomWeight -= layer.scrollLayers[i].weight;

                    if (randomWeight <= 0) {
                      selectedIndex = i;
                      console.log("LevelEditorBase selectedIndex", selectedIndex);
                      break;
                    }
                  }

                  scrollsNode.children.forEach((child, index) => {
                    const active = index === selectedIndex;
                    child.active = active;

                    if (active) {
                      child.children.forEach(children => {
                        if (children.getComponent(_crd && RandTerrain === void 0 ? (_reportPossibleCrUseOfRandTerrain({
                          error: Error()
                        }), RandTerrain) : RandTerrain)) {
                          children.getComponent(_crd && RandTerrain === void 0 ? (_reportPossibleCrUseOfRandTerrain({
                            error: Error()
                          }), RandTerrain) : RandTerrain).play(true);
                        }
                      });
                    }

                    console.log("LevelEditorBase scrollsNode name", child.name, index, "selectedIndex", selectedIndex, "active", child.active);
                  });
                } else {
                  scrollsNode.children.forEach(child => {
                    child.active = true;
                    child.children.forEach(children => {
                      if (children.getComponent(_crd && RandTerrain === void 0 ? (_reportPossibleCrUseOfRandTerrain({
                        error: Error()
                      }), RandTerrain) : RandTerrain)) {
                        children.getComponent(_crd && RandTerrain === void 0 ? (_reportPossibleCrUseOfRandTerrain({
                          error: Error()
                        }), RandTerrain) : RandTerrain).play(false);
                      }
                    });
                  });
                }
              });
            }
          });
        }

        _drawNodeGraphics() {
          const graphics = this._drawNode.getComponent(Graphics);

          if (!graphics) return;

          const drawTransform = this._drawNode.getComponent(UITransform);

          const drawport = new Rect(this._drawNode.getPosition().x - drawTransform.contentSize.width / 2, this._drawNode.getPosition().y - drawTransform.contentSize.height / 2, drawTransform.contentSize.width, this._totalHeight); // Draw drawport rectangle

          graphics.strokeColor = Color.BLUE;
          graphics.lineWidth = 10;
          graphics.rect(drawport.x, drawport.y, drawport.width, drawport.height);
          graphics.stroke();

          const graphicsView = this._drawNode.getChildByName("drawView").getComponent(Graphics);

          if (!graphicsView) return;
          const drawview = new Rect(-750 / 2, -1334 / 2, 750, this._totalHeight);
          graphicsView.strokeColor = Color.RED;
          graphicsView.lineWidth = 10;
          graphicsView.rect(drawview.x, drawview.y, drawview.width, drawview.height);
          graphicsView.stroke();
        }

        _drawMask() {
          if (!this._play) return;

          const maskGraphics = this._drawNode.getChildByName("drawMask").getComponent(Graphics);

          if (!maskGraphics) return; // 绘制4个填充矩形表示视口边界

          const maskWidth = 10000;
          const maskHeight = 1334;
          maskGraphics.fillColor = Color.BLACK; // 顶部矩形

          maskGraphics.fillRect(-maskWidth / 2, this._drawNode.getPosition().y + maskHeight - maskHeight / 2, maskWidth, maskHeight); // 底部矩形

          maskGraphics.fillRect(-maskWidth / 2, this._drawNode.getPosition().y - maskHeight - maskHeight / 2, maskWidth, maskHeight); // 左侧矩形

          maskGraphics.fillRect(-maskWidth - 750 / 2, this._drawNode.getPosition().y - maskHeight / 2, maskWidth, maskHeight); // 右侧矩形

          maskGraphics.fillRect(750 / 2, this._drawNode.getPosition().y - maskHeight / 2, maskWidth, maskHeight);
        }

        _setTimeNode(progress) {
          if (!this._play) return;

          const timeNode = this._drawNode.getChildByName("time");

          const progressNode = this._drawNode.getChildByName("progress");

          if (timeNode && progressNode) {
            timeNode.active = true;
            progressNode.active = true;
            timeNode.getComponent(Label).string = `时间：${(this.totalTime * progress / 1000).toFixed(2)}`;
            progressNode.getComponent(Label).string = `进度：${progress.toFixed(2)}`;
          }
        }

        _drawMaskClear() {
          const timeNode = this._drawNode.getChildByName("time");

          const progressNode = this._drawNode.getChildByName("progress");

          if (timeNode && progressNode) {
            timeNode.active = false;
            progressNode.active = false;
          }

          const maskGraphics = this._drawNode.getChildByName("drawMask").getComponent(Graphics);

          if (!maskGraphics) return;
          maskGraphics.clear();
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "levelname", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return "";
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "totalTime", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 2000;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "backgroundLayer", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return new (_crd && LevelBackgroundLayer === void 0 ? (_reportPossibleCrUseOfLevelBackgroundLayer({
            error: Error()
          }), LevelBackgroundLayer) : LevelBackgroundLayer)();
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "floorLayers", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return [];
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "skyLayers", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return [];
        }
      })), _class2)) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=7faf2e43a321c3c1a125f57fa1812daa6a9e334f.js.map