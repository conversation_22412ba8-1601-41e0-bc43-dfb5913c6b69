{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelLayerUI.ts"], "names": ["_decorator", "Component", "instantiate", "Node", "UITransform", "view", "MyApp", "LayerSplicingMode", "GameMapRun", "LevelEventRun", "Tools", "GameIns", "ccclass", "TerrainsNodeName", "DynamicNodeName", "ScrollsNodeName", "EmittiersNodeName", "LevelLayerUI", "backgrounds", "_offSetY", "_bTrackBackground", "terrainsNode", "dynamicNode", "scrollsNode", "emittiersNode", "events", "eventRunners", "TrackBackground", "value", "onLoad", "initByLevelData", "data", "offSetY", "node", "setPosition", "_getOrAddNode", "terrains", "for<PERSON>ach", "terrain", "path", "resMgr", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultBundleName", "uuid", "load", "err", "prefab", "console", "error", "terrainNode", "position", "x", "y", "setScale", "scale", "setRotationFromEuler", "rotation", "<PERSON><PERSON><PERSON><PERSON>", "_initDynamicsByLevelData", "_initEmittierLevelData", "_initScorllsByLevelData", "sort", "a", "b", "event", "push", "dynamics", "length", "loadPromises", "index", "dynaNode", "weights", "group", "dynamic", "weight", "dynaIndex", "getRandomIndexByWeights", "battleManager", "random", "terrainIndex", "loadPromise", "Promise", "resolve", "name", "randomOffsetX", "offSetX", "max", "min", "randomOffsetY", "all", "emittiers", "emittier", "emittierNode", "scrolls", "element", "srocllIndex", "scroll", "prefabList", "uuids", "totalHeight", "speed", "totalTime", "posOffsetY", "height", "prefabIndex", "curPrefab", "child", "offY", "splicingMode", "node_height", "getComponent", "contentSize", "fix_height", "random_height", "Math", "tick", "deltaTime", "posY", "topPosY", "getVisibleSize", "scrollY", "VIEWPORT_TOP", "i", "eventRunner", "isTriggered", "splice", "_instantiateTerrain", "node_parent", "getChildByName", "getEventByElemID", "elemID"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;AAAcC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;;AAC/DC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,iB,iBAAAA,iB;;AACFC,MAAAA,U;;AACEC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,O,iBAAAA,O;;;;;;;;;OAEH;AAAEC,QAAAA;AAAF,O,GAAcZ,U;AAEda,MAAAA,gB,GAAmB,U;AACnBC,MAAAA,e,GAAkB,S;AAClBC,MAAAA,e,GAAkB,S;AAClBC,MAAAA,iB,GAAoB,W;;8BAGbC,Y,WADZL,OAAO,CAAC,cAAD,C,gBAAR,MACaK,YADb,SACkChB,SADlC,CAC4C;AAAA;AAAA;AAAA,eACjCiB,WADiC,GACT,EADS;AAAA,eAEhCC,QAFgC,GAEb,CAFa;AAEV;AAFU,eAGhCC,iBAHgC,GAGH,IAHG;AAGG;AAHH,eAKhCC,YALgC,GAKJ,IALI;AAAA,eAMhCC,WANgC,GAML,IANK;AAAA,eAOhCC,WAPgC,GAOL,IAPK;AAAA,eAQhCC,aARgC,GAQH,IARG;AAAA,eAShCC,MATgC,GASL,EATK;AAAA,eAUhCC,YAVgC,GAUA,EAVA;AAAA;;AAYd,YAAfC,eAAe,GAAY;AAClC,iBAAO,KAAKP,iBAAZ;AACH;;AACyB,YAAfO,eAAe,CAACC,KAAD,EAAiB;AACvC,eAAKR,iBAAL,GAAyBQ,KAAzB;AACH;;AAEDC,QAAAA,MAAM,GAAS,CAEd;;AAE2B,cAAfC,eAAe,CAACC,IAAD,EAAuBC,OAAvB,EAAuD;AAAA;;AAC/E,eAAKb,QAAL,GAAgBa,OAAhB;AACA,eAAKC,IAAL,CAAUC,WAAV,CAAsB,CAAtB,EAAyBF,OAAzB,EAAkC,CAAlC;AAEA,eAAKX,YAAL,GAAoB,KAAKc,aAAL,CAAmB,KAAKF,IAAxB,EAA8BpB,gBAA9B,CAApB;AACA,eAAKS,WAAL,GAAmB,KAAKa,aAAL,CAAmB,KAAKF,IAAxB,EAA8BnB,eAA9B,CAAnB;AACA,eAAKS,WAAL,GAAmB,KAAKY,aAAL,CAAmB,KAAKF,IAAxB,EAA8BlB,eAA9B,CAAnB;AACA,eAAKS,aAAL,GAAqB,KAAKW,aAAL,CAAmB,KAAKF,IAAxB,EAA8BjB,iBAA9B,CAArB;AAEA,eAAKE,WAAL,GAAmB,EAAnB;AAEA,4BAAAa,IAAI,CAACK,QAAL,4BAAeC,OAAf,CAAwBC,OAAD,IAAa;AAChC,kBAAMC,IAAI,GAAG;AAAA;AAAA,gCAAMC,MAAN,CAAaC,YAAb,CAA0B;AAAA;AAAA,gCAAMD,MAAN,CAAaE,iBAAvC,EAA0DJ,OAAO,CAACK,IAAlE,CAAb;AACA;AAAA;AAAA,gCAAMH,MAAN,CAAaI,IAAb,CAAkBL,IAAlB,EAAwB,CAACM,GAAD,EAAoBC,MAApB,KAAuC;AAC3D,kBAAID,GAAJ,EAAS;AACLE,gBAAAA,OAAO,CAACC,KAAR,CAAc,cAAd,EAA8B,0CAA9B,EAA0EH,GAA1E;AACA;AACH;;AACD,kBAAII,WAAW,GAAG/C,WAAW,CAAC4C,MAAD,CAA7B;AACAG,cAAAA,WAAW,CAACf,WAAZ,CAAwBI,OAAO,CAACY,QAAR,CAAiBC,CAAzC,EAA4Cb,OAAO,CAACY,QAAR,CAAiBE,CAA7D,EAAgE,CAAhE;AACAH,cAAAA,WAAW,CAACI,QAAZ,CAAqBf,OAAO,CAACgB,KAAR,CAAcH,CAAnC,EAAsCb,OAAO,CAACgB,KAAR,CAAcF,CAApD,EAAuD,CAAvD;AACAH,cAAAA,WAAW,CAACM,oBAAZ,CAAiC,CAAjC,EAAoC,CAApC,EAAuCjB,OAAO,CAACkB,QAA/C;AACA,mBAAKnC,YAAL,CAAmBoC,QAAnB,CAA4BR,WAA5B;AACH,aAVD;AAWH,WAbD;AAeA,gBAAM,KAAKS,wBAAL,CAA8B3B,IAA9B,CAAN;AACA,gBAAM,KAAK4B,sBAAL,CAA4B5B,IAA5B,CAAN;AACA,gBAAM,KAAK6B,uBAAL,CAA6B7B,IAA7B,CAAN;AAEA,eAAKN,MAAL,GAAc,CAAC,GAAGM,IAAI,CAACN,MAAT,CAAd;AACA,eAAKA,MAAL,CAAYoC,IAAZ,CAAiB,CAACC,CAAD,EAAIC,CAAJ,KAAUD,CAAC,CAACZ,QAAF,CAAWE,CAAX,GAAeW,CAAC,CAACb,QAAF,CAAWE,CAArD;AACA,eAAK3B,MAAL,CAAYY,OAAZ,CAAqB2B,KAAD,IAAW;AAC3B,iBAAKtC,YAAL,CAAkBuC,IAAlB,CAAuB;AAAA;AAAA,gDAAkBD,KAAlB,EAAyB,IAAzB,CAAvB;AACH,WAFD;AAGH;;AAEqC,cAAxBN,wBAAwB,CAAC3B,IAAD,EAAsC;AAAA;;AACxE,cAAI,CAACA,IAAD,IAAS,KAAKT,WAAL,KAAqB,IAA9B,IAAsCS,IAAI,CAACmC,QAAL,CAAcC,MAAd,KAAyB,CAAnE,EAAsE;AAAE;AAAS;;AAEjF,gBAAMC,YAA6B,GAAG,EAAtC;AACA,4BAAArC,IAAI,CAACmC,QAAL,4BAAe7B,OAAf,CAAuB,CAAC6B,QAAD,EAAUG,KAAV,KAAoB;AACvC,gBAAIC,QAAQ,GAAG,KAAKnC,aAAL,CAAmB,KAAKb,WAAxB,EAAuC,QAAO+C,KAAM,EAApD,CAAf;;AACA,gBAAIE,OAAiB,GAAG,EAAxB;AACAL,YAAAA,QAAQ,CAACM,KAAT,CAAenC,OAAf,CAAwBoC,OAAD,IAAa;AAChCF,cAAAA,OAAO,CAACN,IAAR,CAAaQ,OAAO,CAACC,MAArB;AACH,aAFD;AAGA,kBAAMC,SAAS,GAAG;AAAA;AAAA,gCAAMC,uBAAN,CAA8BL,OAA9B,EAAuC;AAAA;AAAA,oCAAQM,aAAR,CAAsBC,MAAtB,EAAvC,CAAlB;AACA,kBAAML,OAAO,GAAGP,QAAQ,CAACM,KAAT,CAAeG,SAAf,CAAhB;AAEAL,YAAAA,QAAQ,CAACpC,WAAT,CAAqBuC,OAAO,CAACvB,QAAR,CAAiBC,CAAtC,EAAyCsB,OAAO,CAACvB,QAAR,CAAiBE,CAA1D,EAA6D,CAA7D;AACAkB,YAAAA,QAAQ,CAACjB,QAAT,CAAkBoB,OAAO,CAACnB,KAAR,CAAcH,CAAhC,EAAmCsB,OAAO,CAACnB,KAAR,CAAcF,CAAjD,EAAoD,CAApD;AACAkB,YAAAA,QAAQ,CAACf,oBAAT,CAA8B,CAA9B,EAAiC,CAAjC,EAAoCkB,OAAO,CAACjB,QAA5C;AAEAe,YAAAA,OAAO,GAAG,EAAV;AACAE,YAAAA,OAAO,CAACrC,QAAR,CAAiBC,OAAjB,CAA0BC,OAAD,IAAa;AAClCiC,cAAAA,OAAO,CAACN,IAAR,CAAa3B,OAAO,CAACoC,MAArB;AACH,aAFD;AAGA,kBAAMK,YAAY,GAAG;AAAA;AAAA,gCAAMH,uBAAN,CAA8BL,OAA9B,EAAuC;AAAA;AAAA,oCAAQM,aAAR,CAAsBC,MAAtB,EAAvC,CAArB;AACA,kBAAMxC,OAAO,GAAGmC,OAAO,CAACrC,QAAR,CAAiB2C,YAAjB,CAAhB;AAEA,kBAAMC,WAAW,GAAG,IAAIC,OAAJ,CAAmBC,OAAD,IAAa;AAC/C,oBAAM3C,IAAI,GAAG;AAAA;AAAA,kCAAMC,MAAN,CAAaC,YAAb,CAA0B;AAAA;AAAA,kCAAMD,MAAN,CAAaE,iBAAvC,EAA0DJ,OAAO,CAACK,IAAlE,CAAb;AACA;AAAA;AAAA,kCAAMH,MAAN,CAAaI,IAAb,CAAkBL,IAAlB,EAAwB,CAACM,GAAD,EAAoBC,MAApB,KAAuC;AAC3D,oBAAID,GAAJ,EAAS;AACLE,kBAAAA,OAAO,CAACC,KAAR,CAAc,4DAAd,EAA4EH,GAA5E;AACAqC,kBAAAA,OAAO;AACP;AACH;;AACD,oBAAI5D,WAAW,GAAGpB,WAAW,CAAC4C,MAAD,CAA7B;AACAxB,gBAAAA,WAAW,CAAC6D,IAAZ,GAAoB,QAAOR,SAAU,IAAGI,YAAa,EAArD;AACAT,gBAAAA,QAAQ,CAAEb,QAAV,CAAmBnC,WAAnB;AACA,sBAAM8D,aAAa,GAAG;AAAA;AAAA,wCAAQP,aAAR,CAAsBC,MAAtB,MAAkCxC,OAAO,CAAC+C,OAAR,CAAgBC,GAAhB,GAAsBhD,OAAO,CAAC+C,OAAR,CAAgBE,GAAxE,IAA+EjD,OAAO,CAAC+C,OAAR,CAAgBE,GAArH;AACA,sBAAMC,aAAa,GAAG;AAAA;AAAA,wCAAQX,aAAR,CAAsBC,MAAtB,MAAkCxC,OAAO,CAACN,OAAR,CAAgBsD,GAAhB,GAAsBhD,OAAO,CAACN,OAAR,CAAgBuD,GAAxE,IAA+EjD,OAAO,CAACN,OAAR,CAAgBuD,GAArH;AACAjE,gBAAAA,WAAW,CAACY,WAAZ,CAAwBkD,aAAxB,EAAuCI,aAAvC,EAAsD,CAAtD;AACAN,gBAAAA,OAAO;AACV,eAbD;AAcH,aAhBmB,CAApB;AAiBAd,YAAAA,YAAY,CAACH,IAAb,CAAkBe,WAAlB;AACH,WAtCD;AAwCA,gBAAMC,OAAO,CAACQ,GAAR,CAAYrB,YAAZ,CAAN;AACH;;AAEmC,cAAtBT,sBAAsB,CAAC5B,IAAD,EAAsC;AAAA;;AACtE,cAAI,CAACA,IAAD,IAAS,KAAKP,aAAL,KAAuB,IAAhC,IAAwCO,IAAI,CAAC2D,SAAL,CAAevB,MAAf,KAA0B,CAAtE,EAAyE;AAAE;AAAS;;AAEpF,gBAAMC,YAA6B,GAAG,EAAtC;AACA,6BAAArC,IAAI,CAAC2D,SAAL,6BAAgBrD,OAAhB,CAAwB,CAACsD,QAAD,EAAWtB,KAAX,KAAqB;AACzC,kBAAMW,WAAW,GAAG,IAAIC,OAAJ,CAAmBC,OAAD,IAAa;AAC/C,oBAAM3C,IAAI,GAAG;AAAA;AAAA,kCAAMC,MAAN,CAAaC,YAAb,CAA0B;AAAA;AAAA,kCAAMD,MAAN,CAAaE,iBAAvC,EAA0DiD,QAAQ,CAAChD,IAAnE,CAAb;AACA;AAAA;AAAA,kCAAMH,MAAN,CAAaI,IAAb,CAAkBL,IAAlB,EAAwB,CAACM,GAAD,EAAoBC,MAApB,KAAuC;AAC3D,oBAAID,GAAJ,EAAS;AACLqC,kBAAAA,OAAO;AACP;AACH;;AACD,oBAAIU,YAAY,GAAG1F,WAAW,CAAC4C,MAAD,CAA9B;AACA8C,gBAAAA,YAAY,CAACT,IAAb,GAAqB,YAAWd,KAAM,EAAtC;AACAuB,gBAAAA,YAAY,CAAC1D,WAAb,CAAyByD,QAAQ,CAACzC,QAAT,CAAkBC,CAA3C,EAA8CwC,QAAQ,CAACzC,QAAT,CAAkBE,CAAhE,EAAmE,CAAnE;AACAwC,gBAAAA,YAAY,CAACvC,QAAb,CAAsBsC,QAAQ,CAACrC,KAAT,CAAeH,CAArC,EAAwCwC,QAAQ,CAACrC,KAAT,CAAeF,CAAvD,EAA0D,CAA1D;AACAwC,gBAAAA,YAAY,CAACrC,oBAAb,CAAkC,CAAlC,EAAqC,CAArC,EAAwCoC,QAAQ,CAACnC,QAAjD;AACA,qBAAKhC,aAAL,CAAoBiC,QAApB,CAA6BmC,YAA7B;AACAV,gBAAAA,OAAO;AACV,eAZD;AAaH,aAfmB,CAApB;AAgBAd,YAAAA,YAAY,CAACH,IAAb,CAAkBe,WAAlB;AACH,WAlBD;AAoBA,gBAAMC,OAAO,CAACQ,GAAR,CAAYrB,YAAZ,CAAN;AACH;;AAEmC,cAAvBR,uBAAuB,CAAC7B,IAAD,EAAqC;AACrE,cAAI,CAACA,IAAD,IAAS,KAAKR,WAAL,KAAqB,IAA9B,IAAsCQ,IAAI,CAAC8D,OAAL,CAAa1B,MAAb,KAAwB,CAAlE,EAAqE;AAAE;AAAS,WADX,CAGrE;;;AACA,gBAAMI,OAAiB,GAAG,EAA1B;AACAxC,UAAAA,IAAI,CAAC8D,OAAL,CAAaxD,OAAb,CAAqByD,OAAO,IAAI;AAC5BvB,YAAAA,OAAO,CAACN,IAAR,CAAa6B,OAAO,CAACpB,MAArB;AACH,WAFD;AAGA,gBAAMqB,WAAW,GAAG;AAAA;AAAA,8BAAMnB,uBAAN,CAA8BL,OAA9B,EAAuC;AAAA;AAAA,kCAAQM,aAAR,CAAsBC,MAAtB,EAAvC,CAApB;AACA,gBAAMkB,MAAM,GAAGjE,IAAI,CAAC8D,OAAL,CAAaE,WAAb,CAAf;AAEA,gBAAM3B,YAA6B,GAAG,EAAtC;AACA,cAAI6B,UAAoB,GAAG,EAA3B,CAZqE,CAarE;;AACAD,UAAAA,MAAM,CAACE,KAAP,CAAa7D,OAAb,CAAsBM,IAAD,IAAU;AAC3B,kBAAMqC,WAAW,GAAG,IAAIC,OAAJ,CAAmBC,OAAD,IAAa;AAC/C,oBAAM3C,IAAI,GAAG;AAAA;AAAA,kCAAMC,MAAN,CAAaC,YAAb,CAA0B;AAAA;AAAA,kCAAMD,MAAN,CAAaE,iBAAvC,EAA0DC,IAA1D,CAAb;AACA;AAAA;AAAA,kCAAMH,MAAN,CAAaI,IAAb,CAAkBL,IAAlB,EAAwB,CAACM,GAAD,EAAoBC,MAApB,KAAuC;AAC3D,oBAAID,GAAJ,EAAS;AACLqC,kBAAAA,OAAO;AACP;AACH;;AACDe,gBAAAA,UAAU,CAAChC,IAAX,CAAgBnB,MAAhB;AACAoC,gBAAAA,OAAO;AACV,eAPD;AAQH,aAVmB,CAApB;AAWAd,YAAAA,YAAY,CAACH,IAAb,CAAkBe,WAAlB;AACH,WAbD;AAeA,gBAAMC,OAAO,CAACQ,GAAR,CAAYrB,YAAZ,CAAN;;AAEA,gBAAM7C,WAAW,GAAG,KAAKY,aAAL,CAAmB,KAAKZ,WAAxB,EAAuC,UAASwE,WAAY,EAA5D,CAApB;;AACA,cAAII,WAAW,GAAGpE,IAAI,CAACqE,KAAL,GAAarE,IAAI,CAACsE,SAAlB,GAA8B,IAAhD;AACA,cAAIC,UAAU,GAAG,CAAjB;AACA,cAAIC,MAAM,GAAG,CAAb;AACA,cAAIC,WAAW,GAAG,CAAlB,CAnCqE,CAmChD;;AACrB,iBAAOD,MAAM,GAAGJ,WAAhB,EAA6B;AACzB;AACA,kBAAMM,SAAS,GAAGR,UAAU,CAACO,WAAD,CAA5B;AACA,kBAAME,KAAK,GAAGxG,WAAW,CAACuG,SAAD,CAAzB;AACA,kBAAMrB,aAAa,GAAG;AAAA;AAAA,oCAAQP,aAAR,CAAsBC,MAAtB,MAAkCkB,MAAM,CAACX,OAAP,CAAgBC,GAAhB,GAAsBU,MAAM,CAACX,OAAP,CAAgBE,GAAxE,IAA+ES,MAAM,CAACX,OAAP,CAAgBE,GAArH;AACAmB,YAAAA,KAAK,CAACxE,WAAN,CAAkBkD,aAAlB,EAAiCkB,UAAjC,EAA6C,CAA7C;AACA,gBAAIK,IAAI,GAAG,CAAX;;AACA,gBAAIX,MAAM,CAACY,YAAP,KAAwB;AAAA;AAAA,wDAAkBC,WAA9C,EAA2D;AACvDF,cAAAA,IAAI,GAAGD,KAAK,CAACI,YAAN,CAAmB1G,WAAnB,EAAiC2G,WAAjC,CAA6CR,MAApD;AACH,aAFD,MAEO,IAAIP,MAAM,CAACY,YAAP,KAAwB;AAAA;AAAA,wDAAkBI,UAA9C,EAA0D;AAC7DL,cAAAA,IAAI,GAAG,IAAP;AACH,aAFM,MAEA,IAAIX,MAAM,CAACY,YAAP,KAAwB;AAAA;AAAA,wDAAkBK,aAA9C,EAA6D;AAChEN,cAAAA,IAAI,GAAGO,IAAI,CAAC5B,GAAL,CAASU,MAAM,CAAChE,OAAP,CAAgBuD,GAAzB,EAA6BS,MAAM,CAAChE,OAAP,CAAgBsD,GAA7C,IAAoDoB,KAAK,CAACI,YAAN,CAAmB1G,WAAnB,EAAiC2G,WAAjC,CAA6CR,MAAxG;AACH;;AACDhF,YAAAA,WAAW,CAACkC,QAAZ,CAAqBiD,KAArB;AACAJ,YAAAA,UAAU,IAAIK,IAAd;AACAJ,YAAAA,MAAM,IAAII,IAAV;AACAH,YAAAA,WAAW,GAAG,CAACA,WAAW,GAAG,CAAf,IAAoBR,MAAM,CAACE,KAAP,CAAa/B,MAA/C;AACH;AACJ;;AAEMgD,QAAAA,IAAI,CAACC,SAAD,EAAoBhB,KAApB,EAAyC;AAChD,cAAIiB,IAAI,GAAG,KAAKpF,IAAL,CAAUiB,QAAV,CAAmBE,CAA9B;;AACA,cAAI,KAAKzB,eAAL,KAAyB,IAA7B,EAAmC;AAC/B,kBAAM2F,OAAO,GAAGjH,IAAI,CAACkH,cAAL,GAAsBhB,MAAtB,GAA+B,CAA/C;;AACA,gBAAIc,IAAI,GAAGC,OAAX,EAAoB;AAChB,mBAAKlG,iBAAL,GAAyB,KAAzB;AACH;AACJ;;AACDiG,UAAAA,IAAI,IAAID,SAAS,GAAGhB,KAApB;AACA,eAAKnE,IAAL,CAAUC,WAAV,CAAsB,CAAtB,EAAyBmF,IAAzB,EAA+B,CAA/B,EATgD,CAWhD;AACA;;AACA,gBAAMG,OAAO,GAAI,CAACH,IAAD,GAAQ,KAAKlG,QAAb,GAAwB;AAAA;AAAA,wCAAWsG,YAApD,CAbgD,CAchD;;AACA,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKhG,YAAL,CAAkByC,MAAtC,EAA8CuD,CAAC,EAA/C,EAAmD;AAC/C,kBAAMC,WAAW,GAAG,KAAKjG,YAAL,CAAkBgG,CAAlB,CAApB;AACAC,YAAAA,WAAW,CAACR,IAAZ,CAAiBK,OAAjB;;AACA,gBAAIG,WAAW,CAACC,WAAhB,EAA6B;AACzB;AACA,mBAAKlG,YAAL,CAAkBmG,MAAlB,CAAyBH,CAAzB,EAA4B,CAA5B;AACAA,cAAAA,CAAC;AACJ;AACJ;AACJ,SAvNuC,CAyNxC;;;AACQI,QAAAA,mBAAmB,GAAG,CAE7B;;AAEO3F,QAAAA,aAAa,CAAC4F,WAAD,EAAoB5C,IAApB,EAAwC;AACzD,cAAIlD,IAAI,GAAG8F,WAAW,CAACC,cAAZ,CAA2B7C,IAA3B,CAAX;;AACA,cAAIlD,IAAI,IAAI,IAAZ,EAAkB;AACdA,YAAAA,IAAI,GAAG,IAAI9B,IAAJ,CAASgF,IAAT,CAAP;AACA4C,YAAAA,WAAW,CAACtE,QAAZ,CAAqBxB,IAArB;AACH;;AACD,iBAAOA,IAAP;AACH;;AAEMgG,QAAAA,gBAAgB,CAACC,MAAD,EAAwC;AAC3D,eAAK,IAAIlE,KAAT,IAAkB,KAAKvC,MAAvB,EAA+B;AAC3B,gBAAIuC,KAAK,CAACkE,MAAN,IAAgBA,MAApB,EAA4B;AACxB,qBAAOlE,KAAP;AACH;AACJ;;AACD,iBAAO,IAAP;AACH;;AA9OuC,O", "sourcesContent": ["import { _decorator, Component, instantiate, Node, Prefab, UITransform, view } from \"cc\";\r\nimport { MyApp } from 'db://assets/bundles/common/script/app/MyApp';\r\nimport { LayerSplicingMode, LevelDataEvent, LevelDataLayer } from \"../../../leveldata/leveldata\";\r\nimport GameMapRun from \"./GameMapRun\";\r\nimport { LevelEventRun } from \"./LevelEventRun\"\r\nimport { Tools } from \"../../utils/Tools\";\r\nimport { GameIns } from \"../../GameIns\";\r\n\r\nconst { ccclass } = _decorator;\r\n\r\nconst TerrainsNodeName = \"terrains\";\r\nconst DynamicNodeName = \"dynamic\";\r\nconst ScrollsNodeName = \"scrolls\";\r\nconst EmittiersNodeName = \"emittiers\";\r\n\r\n@ccclass('LevelLayerUI')\r\nexport class LevelLayerUI extends Component {\r\n    public backgrounds: Prefab[] = [];\r\n    private _offSetY: number = 0; // 当前关卡的偏移量\r\n    private _bTrackBackground: boolean = true; // 是否跟随背景移动（预加载关卡未在显示区域的时候跟随）\r\n\r\n    private terrainsNode: Node | null = null;\r\n    private dynamicNode: Node | null = null;\r\n    private scrollsNode: Node | null = null;\r\n    private emittiersNode: Node | null = null;\r\n    private events: LevelDataEvent[] = [];\r\n    private eventRunners: LevelEventRun[] = [];\r\n\r\n    public get TrackBackground(): boolean {\r\n        return this._bTrackBackground;\r\n    }\r\n    public set TrackBackground(value: boolean) {\r\n        this._bTrackBackground = value;\r\n    }\r\n\r\n    onLoad(): void {\r\n\r\n    }\r\n\r\n    public async initByLevelData(data: LevelDataLayer, offSetY: number): Promise<void> {\r\n        this._offSetY = offSetY;\r\n        this.node.setPosition(0, offSetY, 0);\r\n\r\n        this.terrainsNode = this._getOrAddNode(this.node, TerrainsNodeName);\r\n        this.dynamicNode = this._getOrAddNode(this.node, DynamicNodeName);\r\n        this.scrollsNode = this._getOrAddNode(this.node, ScrollsNodeName);\r\n        this.emittiersNode = this._getOrAddNode(this.node, EmittiersNodeName);\r\n\r\n        this.backgrounds = [];\r\n\r\n        data.terrains?.forEach((terrain) => {\r\n            const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrain.uuid)\r\n            MyApp.resMgr.load(path, (err: Error | null, prefab: Prefab) => {\r\n                if (err) {\r\n                    console.error('LevelLayerUI', \" initByLevelData load terrain prefab err\", err);\r\n                    return;\r\n                }\r\n                var terrainNode = instantiate(prefab);\r\n                terrainNode.setPosition(terrain.position.x, terrain.position.y, 0);\r\n                terrainNode.setScale(terrain.scale.x, terrain.scale.y, 1);\r\n                terrainNode.setRotationFromEuler(0, 0, terrain.rotation);\r\n                this.terrainsNode!.addChild(terrainNode);\r\n            });\r\n        });\r\n\r\n        await this._initDynamicsByLevelData(data);\r\n        await this._initEmittierLevelData(data);\r\n        await this._initScorllsByLevelData(data);\r\n        \r\n        this.events = [...data.events]\r\n        this.events.sort((a, b) => a.position.y - b.position.y);\r\n        this.events.forEach((event) => {\r\n            this.eventRunners.push(new LevelEventRun(event, this));\r\n        });\r\n    }\r\n\r\n    private async _initDynamicsByLevelData(data: LevelDataLayer): Promise<void> {\r\n        if (!data || this.dynamicNode === null || data.dynamics.length === 0) { return; } \r\n\r\n        const loadPromises: Promise<void>[] = [];\r\n        data.dynamics?.forEach((dynamics,index) => {  \r\n            var dynaNode = this._getOrAddNode(this.dynamicNode!, `dyna_${index}`);\r\n            let weights: number[] = [];\r\n            dynamics.group.forEach((dynamic) => {\r\n                weights.push(dynamic.weight);\r\n            });\r\n            const dynaIndex = Tools.getRandomIndexByWeights(weights, GameIns.battleManager.random());\r\n            const dynamic = dynamics.group[dynaIndex];\r\n             \r\n            dynaNode.setPosition(dynamic.position.x, dynamic.position.y, 0);\r\n            dynaNode.setScale(dynamic.scale.x, dynamic.scale.y, 1);\r\n            dynaNode.setRotationFromEuler(0, 0, dynamic.rotation);\r\n\r\n            weights = [];\r\n            dynamic.terrains.forEach((terrain) => {\r\n                weights.push(terrain.weight);\r\n            });\r\n            const terrainIndex = Tools.getRandomIndexByWeights(weights, GameIns.battleManager.random());\r\n            const terrain = dynamic.terrains[terrainIndex];\r\n            \r\n            const loadPromise = new Promise<void>((resolve) => {\r\n                const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrain.uuid)\r\n                MyApp.resMgr.load(path, (err: Error | null, prefab: Prefab) => {\r\n                    if (err) {\r\n                        console.error(\"LevelEditorLayerUI initByLevelData load dynamic prefab err\", err);\r\n                        resolve();\r\n                        return\r\n                    } \r\n                    var dynamicNode = instantiate(prefab);\r\n                    dynamicNode.name = `rand_${dynaIndex}_${terrainIndex}`;\r\n                    dynaNode!.addChild(dynamicNode); \r\n                    const randomOffsetX = GameIns.battleManager.random() * (terrain.offSetX.max - terrain.offSetX.min) + terrain.offSetX.min;   \r\n                    const randomOffsetY = GameIns.battleManager.random() * (terrain.offSetY.max - terrain.offSetY.min) + terrain.offSetY.min;\r\n                    dynamicNode.setPosition(randomOffsetX, randomOffsetY, 0);\r\n                    resolve();\r\n                });\r\n            });\r\n            loadPromises.push(loadPromise);\r\n        });\r\n\r\n        await Promise.all(loadPromises);\r\n    }\r\n\r\n    private async _initEmittierLevelData(data: LevelDataLayer): Promise<void> {\r\n        if (!data || this.emittiersNode === null || data.emittiers.length === 0) { return; } \r\n\r\n        const loadPromises: Promise<void>[] = [];    \r\n        data.emittiers?.forEach((emittier, index) => {\r\n            const loadPromise = new Promise<void>((resolve) => {\r\n                const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, emittier.uuid);\r\n                MyApp.resMgr.load(path, (err: Error | null, prefab: Prefab) => {\r\n                    if (err) {\r\n                        resolve();\r\n                        return\r\n                    }           \r\n                    var emittierNode = instantiate(prefab);\r\n                    emittierNode.name = `emittier_${index}`;\r\n                    emittierNode.setPosition(emittier.position.x, emittier.position.y, 0);\r\n                    emittierNode.setScale(emittier.scale.x, emittier.scale.y, 1);\r\n                    emittierNode.setRotationFromEuler(0, 0, emittier.rotation);\r\n                    this.emittiersNode!.addChild(emittierNode); \r\n                    resolve();\r\n                });\r\n            });\r\n            loadPromises.push(loadPromise);\r\n        });\r\n\r\n        await Promise.all(loadPromises);  \r\n    }\r\n\r\n    public async _initScorllsByLevelData(data: LevelDataLayer):Promise<void> {\r\n        if (!data || this.scrollsNode === null || data.scrolls.length === 0) { return; } \r\n\r\n        // 根据权重随机出一个滚动组\r\n        const weights: number[] = [];\r\n        data.scrolls.forEach(element => {\r\n            weights.push(element.weight);\r\n        });\r\n        const srocllIndex = Tools.getRandomIndexByWeights(weights, GameIns.battleManager.random());\r\n        const scroll = data.scrolls[srocllIndex];\r\n\r\n        const loadPromises: Promise<void>[] = [];\r\n        let prefabList: Prefab[] = [];\r\n        // 先加载成功所有预制体\r\n        scroll.uuids.forEach((uuid) => {\r\n            const loadPromise = new Promise<void>((resolve) => {\r\n                const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, uuid);\r\n                MyApp.resMgr.load(path, (err: Error | null, prefab: Prefab) => {\r\n                    if (err) {\r\n                        resolve();\r\n                        return;\r\n                    }        \r\n                    prefabList.push(prefab);   \r\n                    resolve();\r\n                });\r\n            });\r\n            loadPromises.push(loadPromise);\r\n        });\r\n\r\n        await Promise.all(loadPromises); \r\n        \r\n        const scrollsNode = this._getOrAddNode(this.scrollsNode!, `scroll_${srocllIndex}`);\r\n        var totalHeight = data.speed * data.totalTime / 1000;\r\n        var posOffsetY = 0;\r\n        var height = 0;\r\n        let prefabIndex = 0; // 当前使用的 prefab 索引\r\n        while (height < totalHeight) {\r\n            // 循环使用 prefab\r\n            const curPrefab = prefabList[prefabIndex];\r\n            const child = instantiate(curPrefab);\r\n            const randomOffsetX = GameIns.battleManager.random() * (scroll.offSetX!.max - scroll.offSetX!.min) + scroll.offSetX!.min;\r\n            child.setPosition(randomOffsetX, posOffsetY, 0);\r\n            var offY = 0;\r\n            if (scroll.splicingMode === LayerSplicingMode.node_height) {    \r\n                offY = child.getComponent(UITransform)!.contentSize.height;\r\n            } else if (scroll.splicingMode === LayerSplicingMode.fix_height) {\r\n                offY = 1334;\r\n            } else if (scroll.splicingMode === LayerSplicingMode.random_height) {\r\n                offY = Math.max(scroll.offSetY!.min,scroll.offSetY!.max) + child.getComponent(UITransform)!.contentSize.height;\r\n            }\r\n            scrollsNode.addChild(child);\r\n            posOffsetY += offY;\r\n            height += offY;\r\n            prefabIndex = (prefabIndex + 1) % scroll.uuids.length;\r\n        }\r\n    }\r\n\r\n    public tick(deltaTime: number, speed: number): void {\r\n        let posY = this.node.position.y;\r\n        if (this.TrackBackground === true) {\r\n            const topPosY = view.getVisibleSize().height / 2;\r\n            if (posY < topPosY) {\r\n                this._bTrackBackground = false;\r\n            }\r\n        }\r\n        posY -= deltaTime * speed;\r\n        this.node.setPosition(0, posY, 0);\r\n\r\n        // 说明: event的激活，是从进入世界范围开始。\r\n        // 又：因为游戏是从上往下滚动，因为我们用一个固定大小变量定义上边界，一旦进入这个范围，就激活。\r\n        const scrollY =  -posY + this._offSetY + GameMapRun.VIEWPORT_TOP;\r\n        // console.log('LevelLayerUI tick scrollY', scrollY, -posY, this._offSetY, GameMapRun.VIEWPORT_TOP);\r\n        for (let i = 0; i < this.eventRunners.length; i++) {\r\n            const eventRunner = this.eventRunners[i];\r\n            eventRunner.tick(scrollY);\r\n            if (eventRunner.isTriggered) {\r\n                // 条件已触发\r\n                this.eventRunners.splice(i, 1);\r\n                i--;\r\n            }\r\n        }\r\n    }\r\n\r\n    // 动态实例化场景元素，当元素的位置在在一个屏幕以上位置是，就实例化\r\n    private _instantiateTerrain() {\r\n        \r\n    }\r\n\r\n    private _getOrAddNode(node_parent: Node, name: string): Node {\r\n        var node = node_parent.getChildByName(name);\r\n        if (node == null) {\r\n            node = new Node(name);\r\n            node_parent.addChild(node);\r\n        }\r\n        return node;\r\n    }\r\n\r\n    public getEventByElemID(elemID: string): LevelDataEvent | null {\r\n        for (let event of this.events) {\r\n            if (event.elemID == elemID) {\r\n                return event;\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n}"]}