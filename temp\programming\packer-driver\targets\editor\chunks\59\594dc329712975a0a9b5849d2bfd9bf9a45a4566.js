System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, Prefab, instantiate, MyApp, GameResourceList, GameIns, BattleLayer, MainPlaneManager, _crd;

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/bundles/common/script/app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneData(extras) {
    _reporterNs.report("PlaneData", "db://assets/bundles/common/script/data/plane/PlaneData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameResourceList(extras) {
    _reporterNs.report("GameResourceList", "../const/GameResourceList", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBattleLayer(extras) {
    _reporterNs.report("BattleLayer", "../ui/layer/BattleLayer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMainPlane(extras) {
    _reporterNs.report("MainPlane", "../ui/plane/mainPlane/MainPlane", _context.meta, extras);
  }

  _export("MainPlaneManager", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      Prefab = _cc.Prefab;
      instantiate = _cc.instantiate;
    }, function (_unresolved_2) {
      MyApp = _unresolved_2.MyApp;
    }, function (_unresolved_3) {
      GameResourceList = _unresolved_3.default;
    }, function (_unresolved_4) {
      GameIns = _unresolved_4.GameIns;
    }, function (_unresolved_5) {
      BattleLayer = _unresolved_5.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "33796Bg3oRMVJbIb/vss6Db", "MainPlaneManager", undefined);

      __checkObsolete__(['Prefab', 'instantiate']);

      _export("MainPlaneManager", MainPlaneManager = class MainPlaneManager {
        constructor() {
          this._planeData = null;
          //飞机数据
          this.mainPlane = null;
          //飞机战斗UI
          this.hurtTotal = 0;
          //造成的总伤害
          this.reviveCount = 0;
          //已复活次数
          this.maxReviveCount = 0;
          //可复活次数
          this.curExp = 0;
          this.curMaxExp = 0;
          this.curLv = 1;
        }

        setPlaneData(planeData) {
          this._planeData = planeData;
        }

        async preload() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.addLoadCount(1);
          await this.createMainPlane();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.checkLoadFinish();
          this.reset();
        }

        reset() {
          this.hurtTotal = 0;
          this.subReset();
        }

        subReset() {
          this.reviveCount = 0;
          this.maxReviveCount = 1; // 默认可复活1次

          if (this.mainPlane) {
            this.mainPlane.resetPlane();
          }
        }
        /**
         * 创建主飞机
         * @param isTrans 是否为特殊状态
         * @returns 主飞机对象
         */


        async createMainPlane() {
          var _instance, _this$mainPlane;

          if (this.mainPlane) {
            this.mainPlane.resetPlane();
            return this.mainPlane;
          }

          const prefab = await (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.loadAsync((_crd && GameResourceList === void 0 ? (_reportPossibleCrUseOfGameResourceList({
            error: Error()
          }), GameResourceList) : GameResourceList).MainPlane, Prefab);
          let planeNode = instantiate(prefab);
          this.mainPlane = planeNode.getComponent("MainPlane");
          (_instance = (_crd && BattleLayer === void 0 ? (_reportPossibleCrUseOfBattleLayer({
            error: Error()
          }), BattleLayer) : BattleLayer).instance) == null || _instance.addMainPlane();
          (_this$mainPlane = this.mainPlane) == null || _this$mainPlane.initPlane(this._planeData);
          return this.mainPlane;
        }

        mainReset() {
          if (this.mainPlane) {
            this.mainPlane.node.destroy();
            this.mainPlane = null;
          }
        }

        checkCanRevive() {
          return this.reviveCount < this.maxReviveCount;
        }

        revive() {
          var _this$mainPlane2;

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.reviveCount += 1; // 增加复活次数

          (_this$mainPlane2 = this.mainPlane) == null || _this$mainPlane2.revive();
        }

        updateGameLogic(dt) {
          var _this$mainPlane3;

          (_this$mainPlane3 = this.mainPlane) == null || _this$mainPlane3.updateGameLogic(dt);
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=594dc329712975a0a9b5849d2bfd9bf9a45a4566.js.map