{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>"], "names": ["_decorator", "Node", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "MyApp", "BundleName", "DataMgr", "ccclass", "property", "PlaneShowUI", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "getBundleName", "Home", "getUIOption", "isClickBgCloseUI", "onLoad", "planeData", "planeInfo", "getPlaneInfoById", "plane", "planeMgr", "getPlane", "planeCon", "<PERSON><PERSON><PERSON><PERSON>", "onShow", "onHide", "onClose"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;;AACZC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;;AACRC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,O,iBAAAA,O;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBR,U;;6BAGjBS,W,WADZF,OAAO,CAAC,aAAD,C,UAGHC,QAAQ,CAACP,IAAD,C,2BAHb,MACaQ,WADb;AAAA;AAAA,4BACwC;AAAA;AAAA;;AAAA;AAAA;;AAKhB,eAANC,MAAM,GAAW;AAAE,iBAAO,uBAAP;AAAiC;;AAC5C,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,OAAf;AAAwB;;AACjC,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,IAAlB;AAAwB;;AACvC,eAAXC,WAAW,GAAU;AAC/B,iBAAO;AAAEC,YAAAA,gBAAgB,EAAE;AAApB,WAAP;AACH;;AAEqB,cAANC,MAAM,GAAkB;AACpC,cAAIC,SAAS,GAAG;AAAA;AAAA,kCAAQC,SAAR,CAAkBC,gBAAlB,EAAhB;AACA,cAAIC,KAAK,GAAG;AAAA;AAAA,8BAAMC,QAAN,CAAeC,QAAf,CAAwBL,SAAxB,CAAZ;AACA,eAAKM,QAAL,CAAeC,QAAf,CAAwBJ,KAAxB;AACH;;AAEW,cAANK,MAAM,GAAkB,CAE7B;;AACW,cAANC,MAAM,GAAkB,CAC7B;;AACY,cAAPC,OAAO,GAAkB,CAC9B;;AAxBmC,O;;;;;iBAGZ,I", "sourcesContent": ["import { _decorator, Node, Prefab } from 'cc';\r\nimport { BaseUI, UILayer, UIOpt } from \"db://assets/scripts/core/base/UIMgr\";\r\nimport { MyApp } from '../../app/MyApp';\r\nimport { BundleName } from '../../const/BundleConst';\r\nimport { DataMgr } from '../../data/DataManager';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('PlaneShowUI')\r\nexport class PlaneShowUI extends BaseUI {\r\n\r\n    @property(Node)\r\n    planeCon: Node | null = null;\r\n\r\n    public static getUrl(): string { return \"prefab/ui/PlaneShowUI\"; }\r\n    public static getLayer(): UILayer { return UILayer.Default }\r\n    public static getBundleName(): string { return BundleName.Home }\r\n    public static getUIOption(): UIOpt {\r\n        return { isClickBgCloseUI: false }\r\n    }\r\n\r\n    protected async onLoad(): Promise<void> {\r\n        let planeData = DataMgr.planeInfo.getPlaneInfoById()\r\n        let plane = MyApp.planeMgr.getPlane(planeData);\r\n        this.planeCon!.addChild(plane);\r\n    }\r\n\r\n    async onShow(): Promise<void> {\r\n\r\n    }\r\n    async onHide(): Promise<void> {\r\n    }\r\n    async onClose(): Promise<void> {\r\n    }\r\n\r\n}\r\n"]}