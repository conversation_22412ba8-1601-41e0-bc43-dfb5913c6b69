import { _decorator, Color, Component, Node, Sprite, tween, UIOpacity, UITransform } from 'cc';
import { GameConst } from 'db://assets/scripts/core/base/GameConst';

const { ccclass, property } = _decorator;

@ccclass('EffectLayer')
export default class EffectLayer extends Component {
    @property(Node)
    whiteNode: Node | null = null;

    @property(Node)
    redNode: Node | null = null;

    static instance: EffectLayer;

    onLoad() {
        EffectLayer.instance = this;
        this.whiteNode!.getComponent(UITransform)!.width = GameConst.ViewWidth;
        this.whiteNode!.getComponent(UITransform)!.height = GameConst.ViewHeight;
    }

    showWhiteScreen(delay: number, opacity: number = 255) {
        if (!this.whiteNode) return;
        this.whiteNode.active = true;
        this.whiteNode.getComponent(UIOpacity)!.opacity = opacity;
        this.whiteNode.getComponent(Sprite)!.color = Color.WHITE;

        tween(this.whiteNode.getComponent(UIOpacity)!)
            .delay(4 * GameConst.ActionFrameTime)
            .to(0.33, { opacity: 0 })
            .call(() => {
                this.whiteNode!.active = false;
            })
            .start();
    }

    lightingShow() {
        if (!this.whiteNode) return;
        this.whiteNode.active = true;
        this.whiteNode.getComponent(UIOpacity)!.opacity = 140.25;
        this.whiteNode.getComponent(Sprite)!.color = Color.WHITE;

        tween(this.whiteNode)
            .delay(2 / 30)
            .call(() => {
                this.whiteNode!.getComponent(UIOpacity)!.opacity = 178.5;
                this.whiteNode!.getComponent(Sprite)!.color = Color.BLACK;
            })
            .delay(2 / 30)
            .call(() => {
                this.whiteNode!.active = false;
            })
            .delay(1 / 30)
            .call(() => {
                this.whiteNode!.getComponent(UIOpacity)!.opacity = 127.5;
                this.whiteNode!.getComponent(Sprite)!.color = Color.BLACK;
            })
            .delay(1 / 30)
            .call(() => {
                this.whiteNode!.active = false;
            })
            .start();
    }

    showRedScreen() {
        if (!this.redNode) return;
        this.redNode.getComponent(UIOpacity)!.opacity = 0;
        this.redNode.getComponent(UITransform)!.width = GameConst.ViewWidth;
        this.redNode.getComponent(UITransform)!.height = GameConst.ViewHeight;

        const frameTime = GameConst.ActionFrameTime;

        tween(this.redNode.getComponent(UIOpacity)!)
            .to(0, { opacity: 204 })
            .to(4 * frameTime, { opacity: 255 })
            .to(2 * frameTime, { opacity: 224 })
            .to(15 * frameTime, { opacity: 0 })
            .start();
    }
}