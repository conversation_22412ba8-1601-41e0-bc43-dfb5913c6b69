<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>ad.png</key>
            <dict>
                <key>frame</key>
                <string>{{367,246},{42,32}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{42,32}}</string>
                <key>sourceSize</key>
                <string>{42,32}</string>
            </dict>
            <key>bg1.png</key>
            <dict>
                <key>frame</key>
                <string>{{184,48},{181,353}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{181,353}}</string>
                <key>sourceSize</key>
                <string>{181,353}</string>
            </dict>
            <key>bg2.png</key>
            <dict>
                <key>frame</key>
                <string>{{1,48},{181,353}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{181,353}}</string>
                <key>sourceSize</key>
                <string>{181,353}</string>
            </dict>
            <key>bgText.png</key>
            <dict>
                <key>frame</key>
                <string>{{572,127},{162,92}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{162,92}}</string>
                <key>sourceSize</key>
                <string>{162,92}</string>
            </dict>
            <key>btn.png</key>
            <dict>
                <key>frame</key>
                <string>{{367,66},{203,67}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{203,67}}</string>
                <key>sourceSize</key>
                <string>{203,67}</string>
            </dict>
            <key>kaung1.png</key>
            <dict>
                <key>frame</key>
                <string>{{367,135},{108,109}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{108,109}}</string>
                <key>sourceSize</key>
                <string>{108,109}</string>
            </dict>
            <key>kuang2.png</key>
            <dict>
                <key>frame</key>
                <string>{{480,291},{111,110}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{111,110}}</string>
                <key>sourceSize</key>
                <string>{111,110}</string>
            </dict>
            <key>null.png</key>
            <dict>
                <key>frame</key>
                <string>{{367,291},{111,110}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{111,110}}</string>
                <key>sourceSize</key>
                <string>{111,110}</string>
            </dict>
            <key>progress1.png</key>
            <dict>
                <key>frame</key>
                <string>{{367,48},{209,16}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{209,16}}</string>
                <key>sourceSize</key>
                <string>{209,16}</string>
            </dict>
            <key>progress2.png</key>
            <dict>
                <key>frame</key>
                <string>{{611,291},{55,55}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{55,55}}</string>
                <key>sourceSize</key>
                <string>{55,55}</string>
            </dict>
            <key>progress3.png</key>
            <dict>
                <key>frame</key>
                <string>{{578,48},{78,77}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{78,77}}</string>
                <key>sourceSize</key>
                <string>{78,77}</string>
            </dict>
            <key>progress4.png</key>
            <dict>
                <key>frame</key>
                <string>{{611,348},{55,46}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{55,46}}</string>
                <key>sourceSize</key>
                <string>{55,46}</string>
            </dict>
            <key>progress5.png</key>
            <dict>
                <key>frame</key>
                <string>{{593,291},{99,16}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{99,16}}</string>
                <key>sourceSize</key>
                <string>{99,16}</string>
            </dict>
            <key>title.png</key>
            <dict>
                <key>frame</key>
                <string>{{1,1},{665,45}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{665,45}}</string>
                <key>sourceSize</key>
                <string>{665,45}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>rogue.png</string>
            <key>size</key>
            <string>{667,402}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:630f119e934819479090805e93ee6ea9$</string>
            <key>textureFileName</key>
            <string>rogue.png</string>
        </dict>
    </dict>
</plist>
