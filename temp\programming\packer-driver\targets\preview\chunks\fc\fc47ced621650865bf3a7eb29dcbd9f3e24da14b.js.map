{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/base/Controller.ts"], "names": ["_decorator", "Component", "NodeEventType", "Vec3", "GameConst", "GameIns", "GameEnum", "ccclass", "property", "Controller", "target", "_targetStartPos", "onLoad", "start", "node", "on", "TOUCH_START", "onTouchStart", "TOUCH_END", "onTouchEnd", "TOUCH_CANCEL", "TOUCH_MOVE", "onTouchMove", "event", "mainPlaneManager", "mainPlane", "gameStateManager", "gameState", "GameState", "WillOver", "Over", "getPosition", "battleManager", "setTouchState", "Battle", "startPos", "getUIStartLocation", "location", "getUILocation", "deltaX", "x", "deltaY", "y", "uiWidth", "designWidth", "battleWidth", "ViewBattleWidth", "scaleFactor", "battleDeltaX", "battleDeltaY", "newX", "newY", "onControl"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAuBC,MAAAA,a,OAAAA,a;AAAeC,MAAAA,I,OAAAA,I;;AAClDC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Q,iBAAAA,Q;;;;;;;;;OAGH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBR,U;;4BAGjBS,U,WADZF,OAAO,CAAC,YAAD,C,gBAAR,MACaE,UADb,SACgCR,SADhC,CAC0C;AAAA;AAAA;AAAA,eAEtCS,MAFsC,GAEX,IAFW;AAEL;AAFK,eAGtCC,eAHsC,GAGd,IAAIR,IAAJ,CAAS,CAAT,EAAY,CAAZ,CAHc;AAAA;;AAGE;;AAExC;AACJ;AACA;AACIS,QAAAA,MAAM,GAAG,CAER;AAED;AACJ;AACA;;;AACIC,QAAAA,KAAK,GAAG;AACJ,eAAKC,IAAL,CAAUC,EAAV,CAAab,aAAa,CAACc,WAA3B,EAAwC,KAAKC,YAA7C,EAA2D,IAA3D;AACA,eAAKH,IAAL,CAAUC,EAAV,CAAab,aAAa,CAACgB,SAA3B,EAAsC,KAAKC,UAA3C,EAAuD,IAAvD;AACA,eAAKL,IAAL,CAAUC,EAAV,CAAab,aAAa,CAACkB,YAA3B,EAAyC,KAAKD,UAA9C,EAA0D,IAA1D;AACA,eAAKL,IAAL,CAAUC,EAAV,CAAab,aAAa,CAACmB,UAA3B,EAAuC,KAAKC,WAA5C,EAAyD,IAAzD;AACH;AAED;AACJ;AACA;AACA;;;AACIL,QAAAA,YAAY,CAACM,KAAD,EAAoB;AAC5B,cAAIb,MAAM,GAAG;AAAA;AAAA,kCAAQc,gBAAR,CAAyBC,SAAtC,CAD4B,CACqB;;AACjD,cAAI,CAACf,MAAL,EAAa;AACT,mBADS,CACD;AACX;;AAED,cAAI;AAAA;AAAA,kCAAQgB,gBAAR,CAAyBC,SAAzB,IAAsC;AAAA;AAAA,oCAASC,SAAT,CAAmBC,QAAzD,IAAqE;AAAA;AAAA,kCAAQH,gBAAR,CAAyBC,SAAzB,IAAsC;AAAA;AAAA,oCAASC,SAAT,CAAmBE,IAAlI,EAAwI;AACpI;AACH;;AAED,eAAKnB,eAAL,GAAuBD,MAAM,CAACI,IAAP,CAAYiB,WAAZ,EAAvB,CAV4B,CAUsB;;AAClD;AAAA;AAAA,kCAAQC,aAAR,CAAsBC,aAAtB,CAAoC,IAApC;AACH;AAED;AACJ;AACA;AACA;;;AACIX,QAAAA,WAAW,CAACC,KAAD,EAAoB;AAC3B,cAAI;AAAA;AAAA,kCAAQG,gBAAR,CAAyBC,SAAzB,IAAsC;AAAA;AAAA,oCAASC,SAAT,CAAmBM,MAA7D,EAAqE;AACjE,mBADiE,CACzD;AACX;;AAED,cAAIxB,MAAM,GAAG;AAAA;AAAA,kCAAQc,gBAAR,CAAyBC,SAAtC,CAL2B,CAKsB;;AACjD,cAAI,CAACf,MAAL,EAAa;AACT,mBADS,CACD;AACX;;AAED,cAAIyB,QAAQ,GAAGZ,KAAK,CAACa,kBAAN,EAAf,CAV2B,CAUgB;;AAC3C,cAAIC,QAAQ,GAAGd,KAAK,CAACe,aAAN,EAAf,CAX2B,CAWe;AAE1C;;AACA,cAAMC,MAAM,GAAGF,QAAQ,CAACG,CAAT,GAAaL,QAAQ,CAACK,CAArC;AACA,cAAMC,MAAM,GAAGJ,QAAQ,CAACK,CAAT,GAAaP,QAAQ,CAACO,CAArC,CAf2B,CAiB3B;;AACA,cAAMC,OAAO,GAAG;AAAA;AAAA,sCAAUC,WAA1B,CAlB2B,CAkBY;;AACvC,cAAMC,WAAW,GAAG;AAAA;AAAA,sCAAUC,eAA9B,CAnB2B,CAmBoB;;AAC/C,cAAMC,WAAW,GAAGF,WAAW,GAAGF,OAAlC,CApB2B,CAsB3B;;AACA,cAAMK,YAAY,GAAGT,MAAM,GAAGQ,WAA9B;AACA,cAAME,YAAY,GAAGR,MAAM,GAAGM,WAA9B,CAxB2B,CA0B3B;;AACA,cAAMG,IAAI,GAAG,KAAKvC,eAAL,CAAqB6B,CAArB,GAAyBQ,YAAtC;AACA,cAAMG,IAAI,GAAG,KAAKxC,eAAL,CAAqB+B,CAArB,GAAyBO,YAAtC;AAEAvC,UAAAA,MAAM,CAAC0C,SAAP,CAAiBF,IAAjB,EAAuBC,IAAvB,EA9B2B,CAgC3B;AACA;AACA;AACH;AAED;AACJ;AACA;AACA;;;AACIhC,QAAAA,UAAU,CAACI,KAAD,EAAoB;AAC1B,cAAI;AAAA;AAAA,kCAAQG,gBAAR,CAAyBC,SAAzB,IAAsC;AAAA;AAAA,oCAASC,SAAT,CAAmBC,QAAzD,IAAqE;AAAA;AAAA,kCAAQH,gBAAR,CAAyBC,SAAzB,IAAsC;AAAA;AAAA,oCAASC,SAAT,CAAmBE,IAAlI,EAAwI;AACpI;AACH;;AACD;AAAA;AAAA,kCAAQE,aAAR,CAAsBC,aAAtB,CAAoC,KAApC;AACH;;AA1FqC,O", "sourcesContent": ["import { _decorator, Component, EventTouch, NodeEventType, Vec3 } from 'cc';\r\nimport { GameConst } from 'db://assets/scripts/core/base/GameConst';\r\nimport { GameIns } from '../../GameIns';\r\nimport { GameEnum } from '../../const/GameEnum';\r\nimport { type MainPlane } from '../plane/mainPlane/MainPlane';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('Controller')\r\nexport class Controller extends Component {\r\n\r\n    target: MainPlane | null = null; // 目标对象（主飞机）\r\n    _targetStartPos: Vec3 = new Vec3(0, 0); // 目标起始位置\r\n\r\n    /**\r\n     * 加载时初始化\r\n     */\r\n    onLoad() {\r\n\r\n    }\r\n\r\n    /**\r\n     * 开始时绑定触摸事件\r\n     */\r\n    start() {\r\n        this.node.on(NodeEventType.TOUCH_START, this.onTouchStart, this);\r\n        this.node.on(NodeEventType.TOUCH_END, this.onTouchEnd, this);\r\n        this.node.on(NodeEventType.TOUCH_CANCEL, this.onTouchEnd, this);\r\n        this.node.on(NodeEventType.TOUCH_MOVE, this.onTouchMove, this);\r\n    }\r\n\r\n    /**\r\n     * 触摸开始事件\r\n     * @param {Event.EventTouch} event 触摸事件\r\n     */\r\n    onTouchStart(event: EventTouch) {\r\n        let target = GameIns.mainPlaneManager.mainPlane; // 获取主飞机\r\n        if (!target) {\r\n            return; // 如果主飞机不存在，则不处理\r\n        }\r\n\r\n        if (GameIns.gameStateManager.gameState == GameEnum.GameState.WillOver || GameIns.gameStateManager.gameState == GameEnum.GameState.Over) {\r\n            return;\r\n        }\r\n\r\n        this._targetStartPos = target.node.getPosition(); // 记录主飞机的起始位置\r\n        GameIns.battleManager.setTouchState(true);\r\n    }\r\n\r\n    /**\r\n     * 触摸移动事件\r\n     * @param {Event.EventTouch} event 触摸事件\r\n     */\r\n    onTouchMove(event: EventTouch) {\r\n        if (GameIns.gameStateManager.gameState != GameEnum.GameState.Battle) {\r\n            return; // 游戏未进入战斗状态时不处理\r\n        }\r\n\r\n        let target = GameIns.mainPlaneManager.mainPlane; // 获取主飞机\r\n        if (!target) {\r\n            return; // 如果主飞机不存在，则不处理\r\n        }\r\n\r\n        let startPos = event.getUIStartLocation(); // UI坐标系（左下角原点）\r\n        let location = event.getUILocation();     // UI坐标系（左下角原点）\r\n\r\n        // 直接计算偏移量（UI坐标系）\r\n        const deltaX = location.x - startPos.x;\r\n        const deltaY = location.y - startPos.y;\r\n\r\n        // 计算UI坐标系到战斗坐标系的转换比例\r\n        const uiWidth = GameConst.designWidth; // UI坐标系宽度 (750)\r\n        const battleWidth = GameConst.ViewBattleWidth; // 战斗坐标系宽度 (950)\r\n        const scaleFactor = battleWidth / uiWidth;\r\n\r\n        // 将UI偏移量转换为战斗坐标系偏移量\r\n        const battleDeltaX = deltaX * scaleFactor;\r\n        const battleDeltaY = deltaY * scaleFactor;\r\n\r\n        // 应用偏移量到飞机初始位置（战斗坐标系）\r\n        const newX = this._targetStartPos.x + battleDeltaX;\r\n        const newY = this._targetStartPos.y + battleDeltaY;\r\n\r\n        target.onControl(newX, newY);\r\n\r\n        // 调试日志\r\n        //logInfo(\"Controller\", `UI坐标: 开始(${startPos.x},${startPos.y}) 当前(${location.x},${location.y})`);\r\n        //logInfo(\"Controller\", `UI偏移量: (${deltaX},${deltaY}), 战斗偏移量: (${battleDeltaX},${battleDeltaY})`);\r\n    }\r\n\r\n    /**\r\n     * 触摸结束事件\r\n     * @param {Event.EventTouch} event 触摸事件\r\n     */\r\n    onTouchEnd(event: EventTouch) {\r\n        if (GameIns.gameStateManager.gameState == GameEnum.GameState.WillOver || GameIns.gameStateManager.gameState == GameEnum.GameState.Over) {\r\n            return;\r\n        }\r\n        GameIns.battleManager.setTouchState(false);\r\n    }\r\n}"]}