{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/PlaneBaseDebug.ts"], "names": ["_decorator", "CCBoolean", "CCFloat", "CCInteger", "Component", "PlaneBase", "ccclass", "property", "PlaneBaseDebug", "type", "_planeBase", "start", "node", "getComponent", "maxHP", "maxHp", "hpRecovery", "attribute", "getHPRecovery", "curHP", "curHp", "isDead", "attack", "getAttack", "bufferList", "buff<PERSON><PERSON>p", "list", "buffs", "for<PERSON>ach", "buff", "push", "res", "id", "applyBuff", "value", "A<PERSON><PERSON><PERSON><PERSON>", "applyBuffID"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,O,OAAAA,O;AAASC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;;AAC7CC,MAAAA,S;;;;;;;;;OACD;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBP,U;;yBAGTQ,c,WADpBF,OAAO,CAAC,gBAAD,C,UAMHC,QAAQ,CAACL,OAAD,C,UAIRK,QAAQ,CAACL,OAAD,C,UAIRK,QAAQ,CAACL,OAAD,C,UAIRK,QAAQ,CAACN,SAAD,C,UAIRM,QAAQ,CAACL,OAAD,C,UAKRK,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAE,CAACN,SAAD;AADA,OAAD,C,UAcRI,QAAQ,CAACJ,SAAD,C,UAERI,QAAQ,CAACN,SAAD,C,2BA3Cb,MACqBO,cADrB,SAC4CJ,SAD5C,CACqD;AAAA;AAAA;AAAA,eACvCM,UADuC,GACR,IADQ;;AAAA;AAAA;;AAEjDC,QAAAA,KAAK,GAAG;AACJ,eAAKD,UAAL,GAAkB,KAAKE,IAAL,CAAUC,YAAV;AAAA;AAAA,qCAAlB;AACH;;AAEQ,YAALC,KAAK,GAAS;AAAA;;AACd,iBAAO,0BAAKJ,UAAL,sCAAiBK,KAAjB,KAA0B,CAAjC;AACH;;AAEa,YAAVC,UAAU,GAAU;AAAA;;AACpB,iBAAO,2BAAKN,UAAL,uCAAiBO,SAAjB,CAA2BC,aAA3B,OAA4C,CAAnD;AACH;;AAEQ,YAALC,KAAK,GAAU;AAAA;;AACf,iBAAO,2BAAKT,UAAL,uCAAiBU,KAAjB,KAA0B,CAAjC;AACH;;AAES,YAANC,MAAM,GAAW;AAAA;;AACjB,iBAAO,2BAAKX,UAAL,uCAAiBW,MAAjB,KAA2B,KAAlC;AACH;;AAES,YAANC,MAAM,GAAG;AAAA;;AACT,iBAAO,2BAAKZ,UAAL,uCAAiBa,SAAjB,OAAgC,CAAvC;AACH;;AAKa,YAAVC,UAAU,GAAY;AACtB,cAAI,CAAC,KAAKd,UAAN,IAAoB,CAAC,KAAKA,UAAL,CAAgBe,QAAzC,EAAmD;AAC/C,mBAAO,EAAP;AACH;;AACD,cAAIC,IAAa,GAAG,EAApB;;AACA,eAAKhB,UAAL,CAAgBe,QAAhB,CAAyBE,KAAzB,CAA+BC,OAA/B,CAAwCC,IAAD,IAAU;AAC7CH,YAAAA,IAAI,CAACI,IAAL,CAAUD,IAAI,CAACE,GAAL,CAASC,EAAnB;AACH,WAFD;;AAGA,iBAAON,IAAP;AACH;;AAKY,YAATO,SAAS,GAAG;AACZ,iBAAO,KAAP;AACH;;AACY,YAATA,SAAS,CAACC,KAAD,EAAgB;AAAA;;AACzB,cAAI,CAACA,KAAL,EAAY;AACR;AACH;;AACD,oCAAKxB,UAAL,+BAAiBe,QAAjB,CAA0BU,SAA1B,CAAoC,KAApC,EAA2C,KAAKC,WAAhD;AACH;;AAnDgD,O;;;;;iBAyC5B,C", "sourcesContent": ["import { _decorator, CCBoolean, CCFloat, CC<PERSON><PERSON>ger, Component } from \"cc\";\r\nimport PlaneBase from \"./PlaneBase\";\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass(\"PlaneBaseDebug\")\r\nexport default class PlaneBaseDebug extends Component{\r\n    protected _planeBase: PlaneBase | null = null;\r\n    start() {\r\n        this._planeBase = this.node.getComponent(PlaneBase);\r\n    }\r\n    @property(CCFloat)\r\n    get maxHP():number{\r\n        return this._planeBase?.maxHp || 0;\r\n    }\r\n    @property(CCFloat)\r\n    get hpRecovery():number {\r\n        return this._planeBase?.attribute.getHPRecovery()||0;\r\n    }\r\n    @property(CCFloat)\r\n    get curHP():number {\r\n        return this._planeBase?.curHp || 0;\r\n    }\r\n    @property(CCBoolean)\r\n    get isDead():boolean {\r\n        return this._planeBase?.isDead || false;\r\n    }\r\n    @property(CCFloat)\r\n    get attack() {\r\n        return this._planeBase?.getAttack() || 0;\r\n    }\r\n\r\n    @property({\r\n        type: [CCInteger]\r\n    })\r\n    get bufferList():number[] {\r\n        if (!this._planeBase || !this._planeBase.buffComp) {\r\n            return [];\r\n        }\r\n        let list:number[] = [];\r\n        this._planeBase.buffComp.buffs.forEach((buff) => {\r\n            list.push(buff.res.id);\r\n        })\r\n        return list;\r\n    }\r\n\r\n    @property(CCInteger)\r\n    applyBuffID:number = 0;\r\n    @property(CCBoolean)\r\n    get applyBuff() {\r\n        return false;\r\n    }\r\n    set applyBuff(value:boolean) {\r\n        if (!value) {\r\n            return\r\n        }\r\n        this._planeBase?.buffComp.ApplyBuff(false, this.applyBuffID);\r\n    }\r\n}"]}