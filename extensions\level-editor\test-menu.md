# 测试右键菜单功能

## 测试步骤

### 测试阵型编辑功能

1. 打开Cocos Creator编辑器
2. 确保level-editor扩展已启用
3. 在资源管理器中导航到 `assets/resources/game/level/wave/formation/`
4. 右键点击 `f_01.json` 文件
5. 在弹出的上下文菜单中应该看到"飞机游戏"选项
6. 点击"飞机游戏" -> "编辑阵型"
7. 验证是否自动打开了FormationEditor场景
8. 验证是否正确加载了f_01.json的数据

### 测试路径编辑功能

1. 在资源管理器中导航到 `assets/resources/game/level/wave/path/`
2. 右键点击 `p_01.json` 文件
3. 在弹出的上下文菜单中应该看到"飞机游戏"选项
4. 点击"飞机游戏" -> "编辑路径"
5. 验证是否自动打开了PathEditor场景
6. 验证是否正确加载了p_01.json的数据

### 测试其他文件

1. 右键点击其他目录下的JSON文件
2. 验证不应该显示"编辑阵型"或"编辑路径"选项
3. 只应该显示原有的"创建关卡Prefab"和"创建子弹prefab"选项

## 预期结果

- 只有在指定目录下的JSON文件才会显示对应的编辑选项
- 点击编辑选项后能正确打开对应的编辑器场景
- 数据能正确加载到编辑器组件中
- 控制台应该输出相应的日志信息

## 故障排除

如果功能不工作，请检查：

1. 扩展是否正确编译（运行 `npm run build`）
2. 扩展是否在编辑器中启用
3. 控制台是否有错误信息
4. 场景文件是否存在于正确路径
5. FormationEditor和PathEditor组件是否正确配置
