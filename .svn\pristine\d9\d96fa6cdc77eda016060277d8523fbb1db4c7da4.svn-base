import { _decorator, Component, Label, ProgressBar, Sprite, tween } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('StatisticsHurtCell')
export class StatisticsHurtCell extends Component {

    @property(Sprite)
    icon: Sprite | null = null;
    @property(Label)
    txtType: Label | null = null;
    @property(ProgressBar)
    bar: ProgressBar | null = null;
    @property(Label)
    txt: Label | null = null;
    @property(Label)
    txt2: Label | null = null;

    private _expTween: any;   // 经验条动画的 tween 引用

    start() {

    }

    update(deltaTime: number) {

    }

    onDestroy() {
        if (this._expTween) {
            this._expTween.stop();
            this._expTween = null;
        }
    }

    public setType(stat: { type: string; score: string }, totalScore: number) {
        this.txtType!.string = stat.type;
        this.txt2!.string = stat.score;
        this.bar!.progress = 0;
        this.txt!.string = this.bar!.progress.toFixed(1) + "%";
        if (totalScore > 0) {
            const gap = 0.5;
            const exp = Number(stat.score) / totalScore;
            this._expTween = tween(this.bar!)
                .to(gap, { progress: exp }, {
                    onUpdate: () => {
                        if (!this.bar) return;
                        this.txt!.string = (this.bar!.progress * 100).toFixed(1) + "%";
                    }
                })
                .start();
        }
    }

}


