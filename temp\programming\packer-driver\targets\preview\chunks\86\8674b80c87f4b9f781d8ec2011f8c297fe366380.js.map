{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>"], "names": ["_decorator", "Label", "Node", "Sprite", "MyApp", "ButtonPlus", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "logError", "BundleName", "HomeUIBaseSystem", "EventMgr", "GameEnum", "startGameByMode", "FriendUI", "MailUI", "StoryUI", "TaskTipUI", "DataMgr", "ccclass", "property", "HomeUI", "_isInit", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Background", "getBundleName", "Home", "onLoad", "globalMgr", "setUIResolution", "btnBattle", "addClick", "onBattleClick", "btnStory", "onStoryClick", "nodeBaseSystem", "getComponentsInChildren", "for<PERSON>ach", "btn", "onBaseSystemClick", "setPlaneInfo", "planeData", "planeInfo", "getPlaneInfoById", "undefined", "config", "star", "starLevel", "planeStars", "children", "element", "index", "active", "planeName", "string", "name", "init", "resMgr", "loadBundle", "HomeTask", "then", "openUI", "onShow", "onHide", "onClose", "targetOff", "onDestroy", "unscheduleAllCallbacks", "update", "dt", "chapterID", "onBattle", "GameModeId", "ENDLESS", "evt", "nodeName", "target", "Social", "Announcement", "Friend", "Mail", "Setting"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAwBC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;;AACrCC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,gB,iBAAAA,gB;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,e,kBAAAA,e;;AACAC,MAAAA,Q,kBAAAA,Q;;AACAC,MAAAA,M,kBAAAA,M;;AACAC,MAAAA,O,kBAAAA,O;;AACAC,MAAAA,S,kBAAAA,S;;AACAC,MAAAA,O,kBAAAA,O;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBrB,U;;wBAEjBsB,M,WADZF,OAAO,CAAC,QAAD,C,UAKHC,QAAQ,CAACnB,IAAD,C,UAGRmB,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,mC,UAGRA,QAAQ,CAACnB,IAAD,C,UAERmB,QAAQ,CAACnB,IAAD,C,UAERmB,QAAQ,CAACpB,KAAD,C,UAERoB,QAAQ,CAAClB,MAAD,C,UAGRkB,QAAQ,CAACnB,IAAD,C,2BAtBb,MACaoB,MADb;AAAA;AAAA,4BACmC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAwBvBC,OAxBuB,GAwBJ,KAxBI;AAAA;;AACX,eAANC,MAAM,GAAW;AAAE,iBAAO,kBAAP;AAA4B;;AACvC,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,UAAf;AAA2B;;AACpC,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,IAAlB;AAAwB;;AAuBtDC,QAAAA,MAAM,GAAS;AACrB;AAAA;AAAA,8BAAMC,SAAN,CAAgBC,eAAhB;AACA,eAAKC,SAAL,CAAgBC,QAAhB,CAAyB,KAAKC,aAA9B,EAA6C,IAA7C;AACA,eAAKC,QAAL,CAAeF,QAAf,CAAwB,KAAKG,YAA7B,EAA2C,IAA3C;AACA,eAAKC,cAAL,CAAqBC,uBAArB;AAAA;AAAA,wCAAyDC,OAAzD,CAAkEC,GAAD,IAAS;AACtEA,YAAAA,GAAG,CAACP,QAAJ,CAAa,KAAKQ,iBAAlB,EAAqC,IAArC;AACH,WAFD;AAGA,eAAKC,YAAL;AACH;;AAEDA,QAAAA,YAAY,GAAG;AACX,cAAIC,SAAS,GAAG;AAAA;AAAA,kCAAQC,SAAR,CAAkBC,gBAAlB,EAAhB;;AACA,cAAIF,SAAS,KAAKG,SAAlB,EAA6B;AACzB;AACH;;AACA,cAAIH,SAAS,CAACI,MAAV,KAAqBD,SAAzB,EAAoC;AACjC;AACH;;AACD,cAAIE,IAAI,GAAGL,SAAS,CAACI,MAAV,CAAkBE,SAA7B;AACA,eAAKC,UAAL,CAAiBC,QAAjB,CAA0BZ,OAA1B,CAAkC,CAACa,OAAD,EAAUC,KAAV,KAAoB;AAClDD,YAAAA,OAAO,CAAED,QAAT,CAAkB,CAAlB,EAAqBG,MAArB,GAA8BD,KAAK,GAAGL,IAAtC;AACH,WAFD;AAGA,eAAKO,SAAL,CAAgBC,MAAhB,GAAyBb,SAAS,CAACI,MAAV,CAAkBU,IAA3C;AACH;;AAEKC,QAAAA,IAAI,GAAG;AAAA;;AAAA;AACT,gBAAI,KAAI,CAACnC,OAAT,EAAkB,OADT,CAET;;AACA;AAAA;AAAA,gCAAMoC,MAAN,CAAaC,UAAb,CAAwB;AAAA;AAAA,0CAAWC,QAAnC,EAA6CC,IAA7C,iCAAkD,aAAY;AAC1D;AAAA;AAAA,kCAAMC,MAAN;AAAA;AAAA;AACH,aAFD;AAGA,YAAA,KAAI,CAACxC,OAAL,GAAe,IAAf;AANS;AAOZ;;AAEKyC,QAAAA,MAAM,GAAgC;AAAA;;AAAA;AACxC,kBAAM,MAAI,CAACN,IAAL,EAAN;AADwC;AAE3C;;AACKO,QAAAA,MAAM,GAAgC;AAAA;AAC3C;;AACKC,QAAAA,OAAO,GAAgC;AAAA;;AAAA;AACzC;AAAA;AAAA,sCAASC,SAAT,CAAmB,MAAnB;AADyC;AAE5C;;AACSC,QAAAA,SAAS,GAAS;AACxB,eAAKC,sBAAL;AACH;;AACSC,QAAAA,MAAM,CAACC,EAAD,EAAmB,CAClC;;AAEKrC,QAAAA,aAAa,GAAG;AAAA;;AAAA;AAClB;AAAA;AAAA,gCAAMJ,SAAN,CAAgB0C,SAAhB,GAA4B,CAA5B;;AACA,YAAA,MAAI,CAACC,QAAL;AAFkB;AAGrB;;AAEKA,QAAAA,QAAQ,GAAG;AAAA;AACb;AAAA;AAAA,oDAAgB;AAAA;AAAA,sCAASC,UAAT,CAAoBC,OAApC;AADa;AAEhB;;AAEKvC,QAAAA,YAAY,GAAG;AAAA;AACjB,kBAAM;AAAA;AAAA,gCAAM2B,MAAN;AAAA;AAAA,mCAAN;AADiB;AAEpB;;AAEOtB,QAAAA,iBAAiB,CAACmC,GAAD,EAAkB;AACvC,cAAMC,QAAQ,GAAGD,GAAG,CAACE,MAAJ,CAAWrB,IAA5B;;AACA,kBAAQoB,QAAR;AACI,iBAAK;AAAA;AAAA,sDAAiBE,MAAtB;AACI;;AACJ,iBAAK;AAAA;AAAA,sDAAiBC,YAAtB;AACI;;AACJ,iBAAK;AAAA;AAAA,sDAAiBC,MAAtB;AACI;AAAA;AAAA,kCAAMlB,MAAN;AAAA;AAAA;AACA;;AACJ,iBAAK;AAAA;AAAA,sDAAiBmB,IAAtB;AACI;AAAA;AAAA,kCAAMnB,MAAN;AAAA;AAAA;AACA;;AACJ,iBAAK;AAAA;AAAA,sDAAiBoB,OAAtB;AACI;;AACJ;AACI;AAAA;AAAA,wCAAS,QAAT,yDAAwEN,QAAxE;AACA;AAfR;AAiBH;;AA1G8B,O;;;;;iBAKD,I;;;;;;;iBAGC,I;;;;;;;iBAED,I;;;;;;;iBAGD,I;;;;;;;iBAEH,I;;;;;;;iBAEA,I;;;;;;;iBAEE,I;;;;;;;iBAGH,I", "sourcesContent": ["import { _decorator, EventTouch, Label, Node, Sprite } from 'cc';\nimport { MyApp } from 'db://assets/bundles/common/script/app/MyApp';\nimport { ButtonPlus } from 'db://assets/bundles/common/script/ui/common/components/button/ButtonPlus';\nimport { BaseUI, UILayer, UIMgr } from \"db://assets/scripts/core/base/UIMgr\";\nimport { logError } from 'db://assets/scripts/utils/Logger';\nimport { BundleName } from '../../const/BundleConst';\nimport { HomeUIBaseSystem } from '../../const/HomeUIConst';\nimport { EventMgr } from '../../event/EventManager';\nimport { GameEnum } from '../../game/const/GameEnum';\nimport { startGameByMode } from '../../game/GameInsStart';\nimport { FriendUI } from '../friend/FriendUI';\nimport { MailUI } from '../mail/MailUI';\nimport { StoryUI } from '../story/StoryUI';\nimport { TaskTipUI } from '../task/TaskTipUI';\nimport { DataMgr } from '../../data/DataManager';\n\nconst { ccclass, property } = _decorator;\n@ccclass('HomeUI')\nexport class HomeUI extends BaseUI {\n    public static getUrl(): string { return \"prefab/ui/HomeUI\"; }\n    public static getLayer(): UILayer { return UILayer.Background }\n    public static getBundleName(): string { return BundleName.Home }\n    @property(Node)\n    nodeBaseSystem: Node | null = null;\n\n    @property(ButtonPlus)\n    btnBattle: ButtonPlus | null = null;\n    @property(ButtonPlus)\n    btnStory: ButtonPlus | null = null;\n\n    @property(Node)\n    nodePlaneInfo: Node | null = null;\n    @property(Node)\n    planeStars: Node | null = null;\n    @property(Label)\n    planeName: Label | null = null;\n    @property(Sprite)\n    planeImage: Sprite | null = null;\n\n    @property(Node)\n    nodePlane: Node | null = null;\n\n    private _isInit: boolean = false;\n\n    protected onLoad(): void {\n        MyApp.globalMgr.setUIResolution();\n        this.btnBattle!.addClick(this.onBattleClick, this);\n        this.btnStory!.addClick(this.onStoryClick, this);\n        this.nodeBaseSystem!.getComponentsInChildren(ButtonPlus).forEach((btn) => {\n            btn.addClick(this.onBaseSystemClick, this);\n        })\n        this.setPlaneInfo();\n    }\n\n    setPlaneInfo() {\n        let planeData = DataMgr.planeInfo.getPlaneInfoById();\n        if (planeData === undefined) {  \n            return;\n        }\n         if (planeData.config === undefined) {  \n            return;\n        }\n        let star = planeData.config!.starLevel;\n        this.planeStars!.children.forEach((element, index) => {\n            element!.children[0].active = index < star;\n        });\n        this.planeName!.string = planeData.config!.name;\n    }\n\n    async init() {\n        if (this._isInit) return\n        //异步加载初始化界面内的ui\n        MyApp.resMgr.loadBundle(BundleName.HomeTask).then(async () => {\n            UIMgr.openUI(TaskTipUI)\n        })\n        this._isInit = true;\n    }\n\n    async onShow(...args: any[]): Promise<void> {\n        await this.init();\n    }\n    async onHide(...args: any[]): Promise<void> {\n    }\n    async onClose(...args: any[]): Promise<void> {\n        EventMgr.targetOff(this)\n    }\n    protected onDestroy(): void {\n        this.unscheduleAllCallbacks();\n    }\n    protected update(dt: number): void {\n    }\n\n    async onBattleClick() {\n        MyApp.globalMgr.chapterID = 0;\n        this.onBattle();\n    }\n\n    async onBattle() {\n        startGameByMode(GameEnum.GameModeId.ENDLESS);\n    }\n\n    async onStoryClick() {\n        await UIMgr.openUI(StoryUI);\n    }\n\n    private onBaseSystemClick(evt: EventTouch) {\n        const nodeName = evt.target.name\n        switch (nodeName) {\n            case HomeUIBaseSystem.Social:\n                break;\n            case HomeUIBaseSystem.Announcement:\n                break;\n            case HomeUIBaseSystem.Friend:\n                UIMgr.openUI(FriendUI)\n                break;\n            case HomeUIBaseSystem.Mail:\n                UIMgr.openUI(MailUI)\n                break;\n            case HomeUIBaseSystem.Setting:\n                break;\n            default:\n                logError(\"HomeUI\", `onBaseSystemClick nodeName not found click handler${nodeName}`)\n                break;\n        }\n    }\n}\n\n"]}