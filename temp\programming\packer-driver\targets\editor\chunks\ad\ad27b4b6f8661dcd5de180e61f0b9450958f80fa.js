System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, Enum, resources, warn, TTFUtils, _crd;

  _export("TTFUtils", void 0);

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      Enum = _cc.Enum;
      resources = _cc.resources;
      warn = _cc.warn;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "7035aHp0r5LiZXS2DyDoVOs", "TTFUtils", undefined);

      /**
       * 字体工具类，兼容不同平台是否能使用自定义TTF字体功能
       * （目前只有加载单自定义字体的功能，后续拓展）
       * 加载方式：
       * 1、TTFUtils.init设置字体
       * 2、在Label节点挂载TTFComponent脚本组件
       */
      __checkObsolete__(['Enum', 'Font', 'Label', 'resources', 'RichText', 'warn']);

      _export("TTFUtils", TTFUtils = class TTFUtils {
        constructor() {
          this.customLabelUrl = {};
          this.customLabel = {};
          this.isLoading = {};
          this.labelList = {};
        }

        static getInstance() {
          if (!TTFUtils.instance) {
            TTFUtils.instance = new TTFUtils();
          }

          return TTFUtils.instance;
        }

        /**
         * 设置自定义字体路径
         */
        init(type, url) {
          this.customLabelUrl[type] = url;
        }
        /**
         * 设置自定义字体
         * @param label type: Label
         */


        setCustomTTF(label, type) {
          let labFont = this.customLabel[type];

          if (labFont) {
            label.font = labFont;
            return;
          }

          this.loadCustomTTF(label, type);
        }

        loadCustomTTF(label, type) {
          if (!this.labelList[type]) {
            this.labelList[type] = [];
          }

          this.labelList[type].push(label);

          if (this.isLoading[type] || !this.customLabelUrl[type]) {
            return;
          }

          this.isLoading[type] = true;
          resources.load(this.customLabelUrl[type], (err, asset) => {
            this.isLoading[type] = false;

            if (err) {
              warn("字体加载失败", this.customLabelUrl[type], err);
              return;
            }

            warn("字体加载完成", type, this.customLabelUrl[type]);
            this.customLabel[type] = asset;
            let loadLab = null;

            for (let i = this.labelList[type].length - 1; i >= 0; i--) {
              loadLab = this.labelList[type][i];

              if (loadLab) {
                loadLab.font = asset;
              }
            }

            delete this.labelList[type];
          });
        }

      });

      TTFUtils.instance = void 0;
      TTFUtils.CUSTOM_TYPE = Enum({
        NONE: 0,
        TEXT: 1,
        NUMBER: 2
      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=ad27b4b6f8661dcd5de180e61f0b9450958f80fa.js.map