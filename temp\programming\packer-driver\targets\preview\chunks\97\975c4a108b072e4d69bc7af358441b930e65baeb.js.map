{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts"], "names": ["_decorator", "MyApp", "BundleName", "IBundleEntry", "ResManager", "UIMgr", "logDebug", "EventMgr", "HomeUIEvent", "FriendUI", "DevLoginUI", "BottomTab", "BottomUI", "HomeUI", "TopUI", "MailUI", "PKUI", "PlaneUI", "ShopUI", "SkyIslandUI", "StoryUI", "TalentUI", "TTFUtils", "ccclass", "CommonEntry", "initEntry", "GetInstance", "init", "getInstance", "CUSTOM_TYPE", "TEXT", "planeMgr", "load", "instance", "loadBundle", "<PERSON><PERSON>", "lubanMgr", "loadUI", "resMgr", "Home", "openUI", "on", "Leave", "closeUI", "emit", "BottomTabRegister", "get", "preloadHomeSubBundles", "HomePlane", "then", "Plane", "HomeTalent", "Talent", "HomeShop", "Shop", "HomeSkyIsland", "SkyIsLand", "HomeFriend", "HomeMail", "HomePK", "HomeStory"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,Q,kBAAAA,Q;;AACAC,MAAAA,U,kBAAAA,U;;AACAC,MAAAA,S,kBAAAA,S;;AACAC,MAAAA,Q,kBAAAA,Q;;AACAC,MAAAA,M,kBAAAA,M;;AACAC,MAAAA,K,kBAAAA,K;;AACAC,MAAAA,M,kBAAAA,M;;AACAC,MAAAA,I,kBAAAA,I;;AACAC,MAAAA,O,kBAAAA,O;;AACAC,MAAAA,M,kBAAAA,M;;AACAC,MAAAA,W,kBAAAA,W;;AACAC,MAAAA,O,kBAAAA,O;;AACAC,MAAAA,Q,kBAAAA,Q;;AACAC,MAAAA,Q,kBAAAA,Q;;;;;;;;;OACH;AAAEC,QAAAA;AAAF,O,GAAcvB,U;;6BAEPwB,W,WADZD,OAAO,CAAC,aAAD,C,gBAAR,MACaC,WADb;AAAA;AAAA,wCAC8C;AAC7BC,QAAAA,SAAS,GAAgC;AAAA;;AAAA;AAClD;AAAA;AAAA,sCAAS,aAAT,EAAwB,WAAxB;AACA;AAAA;AAAA,gCAAMC,WAAN,GAAoBC,IAApB;AACA;AAAA;AAAA,sCAASC,WAAT,GAAuBD,IAAvB,CAA4B;AAAA;AAAA,sCAASE,WAAT,CAAqBC,IAAjD,EAAsD,eAAtD;AACA,kBAAM;AAAA;AAAA,gCAAMC,QAAN,CAAeC,IAAf,EAAN;AACA,kBAAM;AAAA;AAAA,0CAAWC,QAAX,CAAoBC,UAApB,CAA+B;AAAA;AAAA,0CAAWC,KAA1C,CAAN,CALkD,CAKK;;AACvD,kBAAM;AAAA;AAAA,gCAAMC,QAAN,CAAeJ,IAAf,EAAN;AACA,kBAAM;AAAA;AAAA,gCAAMK,MAAN;AAAA;AAAA,yCAAN;AACA,kBAAM;AAAA;AAAA,gCAAMC,MAAN,CAAaJ,UAAb,CAAwB;AAAA;AAAA,0CAAWK,IAAnC,CAAN;AACA,kBAAM;AAAA;AAAA,gCAAMC,MAAN;AAAA;AAAA,iCAAN;AACA,kBAAM;AAAA;AAAA,gCAAMA,MAAN;AAAA;AAAA,qCAAN;AACA,kBAAM;AAAA;AAAA,gCAAMA,MAAN;AAAA;AAAA,+BAAN,CAXkD,CAYlD;;AACA;AAAA;AAAA,sCAASC,EAAT,CAAY;AAAA;AAAA,4CAAYC,KAAxB,EAA+B,MAAM;AACjC;AAAA;AAAA,kCAAMC,OAAN;AAAA;AAAA;AACA;AAAA;AAAA,kCAAMA,OAAN;AAAA;AAAA,wCAFiC,CAET;;AACxB;AAAA;AAAA,kCAAMA,OAAN;AAAA;AAAA;AACH,aAJD;AAKA;AAAA;AAAA,sCAASC,IAAT,CAAc;AAAA;AAAA,4CAAYC,iBAA1B,EAA6C;AAAA;AAAA,wCAAUN,IAAvD,EAA6D;AAAA;AAAA,gCAAMO,GAAN;AAAA;AAAA,iCAA7D;;AACA,YAAA,KAAI,CAACC,qBAAL;AAnBkD;AAoBrD;;AAEOA,QAAAA,qBAAqB,GAAG;AAC5B;AACA;AAAA;AAAA,8BAAMT,MAAN,CAAaJ,UAAb,CAAwB;AAAA;AAAA,wCAAWc,SAAnC,EAA8CC,IAA9C,iCAAmD,aAAY;AAC3D,kBAAM;AAAA;AAAA,gCAAMZ,MAAN;AAAA;AAAA,mCAAN;AACA;AAAA;AAAA,sCAASO,IAAT,CAAc;AAAA;AAAA,4CAAYC,iBAA1B,EAA6C;AAAA;AAAA,wCAAUK,KAAvD,EAA8D;AAAA;AAAA,gCAAMJ,GAAN;AAAA;AAAA,mCAA9D;AACH,WAHD;AAIA;AAAA;AAAA,8BAAMR,MAAN,CAAaJ,UAAb,CAAwB;AAAA;AAAA,wCAAWiB,UAAnC,EAA+CF,IAA/C,iCAAoD,aAAY;AAC5D,kBAAM;AAAA;AAAA,gCAAMZ,MAAN;AAAA;AAAA,qCAAN;AACA;AAAA;AAAA,sCAASO,IAAT,CAAc;AAAA;AAAA,4CAAYC,iBAA1B,EAA6C;AAAA;AAAA,wCAAUO,MAAvD,EAA+D;AAAA;AAAA,gCAAMN,GAAN;AAAA;AAAA,qCAA/D;AACH,WAHD;AAIA;AAAA;AAAA,8BAAMR,MAAN,CAAaJ,UAAb,CAAwB;AAAA;AAAA,wCAAWmB,QAAnC,EAA6CJ,IAA7C,iCAAkD,aAAY;AAC1D,kBAAM;AAAA;AAAA,gCAAMZ,MAAN;AAAA;AAAA,iCAAN;AACA;AAAA;AAAA,sCAASO,IAAT,CAAc;AAAA;AAAA,4CAAYC,iBAA1B,EAA6C;AAAA;AAAA,wCAAUS,IAAvD,EAA6D;AAAA;AAAA,gCAAMR,GAAN;AAAA;AAAA,iCAA7D;AACH,WAHD;AAIA;AAAA;AAAA,8BAAMR,MAAN,CAAaJ,UAAb,CAAwB;AAAA;AAAA,wCAAWqB,aAAnC,EAAkDN,IAAlD,iCAAuD,aAAY;AAC/D,kBAAM;AAAA;AAAA,gCAAMZ,MAAN;AAAA;AAAA,2CAAN;AACA;AAAA;AAAA,sCAASO,IAAT,CAAc;AAAA;AAAA,4CAAYC,iBAA1B,EAA6C;AAAA;AAAA,wCAAUW,SAAvD,EAAkE;AAAA;AAAA,gCAAMV,GAAN;AAAA;AAAA,2CAAlE;AACH,WAHD;AAIA;AAAA;AAAA,8BAAMR,MAAN,CAAaJ,UAAb,CAAwB;AAAA;AAAA,wCAAWuB,UAAnC,EAA+CR,IAA/C,iCAAoD,aAAY;AAC5D,kBAAM;AAAA;AAAA,gCAAMZ,MAAN;AAAA;AAAA,qCAAN;AACH,WAFD;AAGA;AAAA;AAAA,8BAAMC,MAAN,CAAaJ,UAAb,CAAwB;AAAA;AAAA,wCAAWwB,QAAnC,EAA6CT,IAA7C,iCAAkD,aAAY;AAC1D,kBAAM;AAAA;AAAA,gCAAMZ,MAAN;AAAA;AAAA,iCAAN;AACH,WAFD;AAGA;AAAA;AAAA,8BAAMC,MAAN,CAAaJ,UAAb,CAAwB;AAAA;AAAA,wCAAWyB,MAAnC,EAA2CV,IAA3C,iCAAgD,aAAY;AACxD,kBAAM;AAAA;AAAA,gCAAMZ,MAAN;AAAA;AAAA,6BAAN;AACH,WAFD;AAGA;AAAA;AAAA,8BAAMC,MAAN,CAAaJ,UAAb,CAAwB;AAAA;AAAA,wCAAW0B,SAAnC,EAA8CX,IAA9C,iCAAmD,aAAY;AAC3D,kBAAM;AAAA;AAAA,gCAAMZ,MAAN;AAAA;AAAA,mCAAN;AACH,WAFD;AAGH;;AArDyC,O", "sourcesContent": ["import { _decorator } from \"cc\";\nimport { MyApp } from \"db://assets/bundles/common/script/app/MyApp\";\nimport { BundleName } from \"db://assets/bundles/common/script/const/BundleConst\";\nimport { IBundleEntry } from \"db://assets/scripts/core/base/Bundle\";\nimport { ResManager } from \"db://assets/scripts/core/base/ResManager\";\nimport { UIMgr } from \"db://assets/scripts/core/base/UIMgr\";\nimport { logDebug } from \"db://assets/scripts/utils/Logger\";\nimport { EventMgr } from \"./event/EventManager\";\nimport { HomeUIEvent } from \"./event/HomeUIEvent\";\nimport { FriendUI } from \"./ui/friend/FriendUI\";\nimport { DevLoginUI } from \"./ui/gameui/DevLoginUI\";\nimport { BottomTab } from \"./ui/home/<USER>\";\nimport { BottomUI } from \"./ui/home/<USER>\";\nimport { HomeUI } from \"./ui/home/<USER>\";\nimport { TopUI } from \"./ui/home/<USER>\";\nimport { MailUI } from \"./ui/mail/MailUI\";\nimport { PKUI } from \"./ui/pk/PKUI\";\nimport { PlaneUI } from \"./ui/plane/PlaneUI\";\nimport { ShopUI } from \"./ui/shop/ShopUI\";\nimport { SkyIslandUI } from \"./ui/skyisland/SkyIslandUI\";\nimport { StoryUI } from \"./ui/story/StoryUI\";\nimport { TalentUI } from \"./ui/talent/TalentUI\";\nimport { TTFUtils } from \"./utils/TTFUtils\";\nconst { ccclass } = _decorator;\n@ccclass('CommonEntry')\nexport class CommonEntry extends IBundleEntry {\n    public async initEntry(...args: any[]): Promise<void> {\n        logDebug(\"CommonEntry\", \"initEntry\")\n        MyApp.GetInstance().init();\n        TTFUtils.getInstance().init(TTFUtils.CUSTOM_TYPE.TEXT,\"font/gamefont\")\n        await MyApp.planeMgr.load();\n        await ResManager.instance.loadBundle(BundleName.Luban) //优先加载完配置\n        await MyApp.lubanMgr.load();\n        await UIMgr.loadUI(DevLoginUI)\n        await MyApp.resMgr.loadBundle(BundleName.Home)\n        await UIMgr.openUI(HomeUI)\n        await UIMgr.openUI(BottomUI)\n        await UIMgr.openUI(TopUI)\n        //暂时这样 后面再优化\n        EventMgr.on(HomeUIEvent.Leave, () => {\n            UIMgr.closeUI(HomeUI)\n            UIMgr.closeUI(BottomUI) //这里会把下面的主界面都关闭  \n            UIMgr.closeUI(TopUI)\n        })\n        EventMgr.emit(HomeUIEvent.BottomTabRegister, BottomTab.Home, UIMgr.get(HomeUI))\n        this.preloadHomeSubBundles();\n    }\n\n    private preloadHomeSubBundles() {\n        //异步加载其他bundle\n        MyApp.resMgr.loadBundle(BundleName.HomePlane).then(async () => {\n            await UIMgr.loadUI(PlaneUI)\n            EventMgr.emit(HomeUIEvent.BottomTabRegister, BottomTab.Plane, UIMgr.get(PlaneUI))\n        })\n        MyApp.resMgr.loadBundle(BundleName.HomeTalent).then(async () => {\n            await UIMgr.loadUI(TalentUI)\n            EventMgr.emit(HomeUIEvent.BottomTabRegister, BottomTab.Talent, UIMgr.get(TalentUI))\n        })\n        MyApp.resMgr.loadBundle(BundleName.HomeShop).then(async () => {\n            await UIMgr.loadUI(ShopUI)\n            EventMgr.emit(HomeUIEvent.BottomTabRegister, BottomTab.Shop, UIMgr.get(ShopUI))\n        })\n        MyApp.resMgr.loadBundle(BundleName.HomeSkyIsland).then(async () => {\n            await UIMgr.loadUI(SkyIslandUI)\n            EventMgr.emit(HomeUIEvent.BottomTabRegister, BottomTab.SkyIsLand, UIMgr.get(SkyIslandUI))\n        })\n        MyApp.resMgr.loadBundle(BundleName.HomeFriend).then(async () => {\n            await UIMgr.loadUI(FriendUI)\n        })\n        MyApp.resMgr.loadBundle(BundleName.HomeMail).then(async () => {\n            await UIMgr.loadUI(MailUI)\n        })\n        MyApp.resMgr.loadBundle(BundleName.HomePK).then(async () => {\n            await UIMgr.loadUI(PKUI)\n        })\n        MyApp.resMgr.loadBundle(BundleName.HomeStory).then(async () => {\n            await UIMgr.loadUI(StoryUI)\n        })\n    }\n}"]}