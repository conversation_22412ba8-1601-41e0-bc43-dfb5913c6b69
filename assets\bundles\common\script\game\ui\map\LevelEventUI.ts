import { _decorator, assetManager, AudioClip, CCInteger, CCString, Prefab } from 'cc';
import { LevelDataEventCondtionType } from '../../../leveldata/condition/LevelDataEventCondtion';
import { LevelDataEventCondtionWave } from '../../../leveldata/condition/LevelDataEventCondtionWave';
import { LevelDataEvent } from '../../../leveldata/leveldata';
import { LevelDataEventTrigger, LevelDataEventTriggerType } from '../../../leveldata/trigger/LevelDataEventTrigger';
import { LevelDataEventTriggerAudio } from '../../../leveldata/trigger/LevelDataEventTriggerAudio';
import { LevelDataEventTriggerLog } from '../../../leveldata/trigger/LevelDataEventTriggerLog';
import { LevelDataEventTriggerWave, LevelDataEventWaveGroup } from '../../../leveldata/trigger/LevelDataEventTriggerWave';
import { LevelDataEventTriggerSpecialEvent, eLevelSpecialEvent } from '../../../leveldata/trigger/LevelDataEventTriggerSpecialEvent';
import { newTrigger } from '../../../leveldata/trigger/newTrigger';
import { LevelCondition } from './LevelCondition';
import { LevelElemUI } from './LevelElemUI';
import { LevelWaveGroup } from './LevelWaveUI';
const { ccclass, property } = _decorator;

@ccclass('LevelEventTrigger')
export class LevelEventTrigger {
    public _index = 0;
    public data: LevelDataEventTrigger = new LevelDataEventTriggerLog();

    public get type(): LevelDataEventTriggerType {
        return this.data._type;
    }
    public set type(value: LevelDataEventTriggerType) {
        if (this.data._type != value) {
            this.data = newTrigger({ _type: value });
        }
    }

    @property({
        type: CCString,
        visible() {
            return this.type == LevelDataEventTriggerType.Log;
        }
    })
    public get message(): string {
        return (this.data as LevelDataEventTriggerLog).message;
    }
    public set message(value: string) {
        (this.data as LevelDataEventTriggerLog).message = value;
    }

    public _audio: AudioClip | null = null;
    @property({
        type: AudioClip,
        visible() {
            return this.type == LevelDataEventTriggerType.Audio;
        }
    })
    public get audio(): AudioClip | null {
        return this._audio;
    }
    public set audio(value: AudioClip | null) {
        this._audio = value;
        if (value) {
            (this.data as LevelDataEventTriggerAudio).audioUUID = value.uuid;
        } else {
            (this.data as LevelDataEventTriggerAudio).audioUUID = "";
        }
    }

    public _waveGroup: LevelWaveGroup[] = [];
    @property({
        type: [LevelWaveGroup],
        visible() {
            return this.type == LevelDataEventTriggerType.Wave;
        }
    })
    public get waveGroup(): LevelWaveGroup[] {
        return this._waveGroup;
    }
    public set waveGroup(value: LevelWaveGroup[]) {
        this._waveGroup = value;
        if (value) {
            (this.data as LevelDataEventTriggerWave).waveGroup = [];
            value.forEach((waveGroup) => {
                let levelDataWaveGroup = new LevelDataEventWaveGroup();
                waveGroup.wavePrefab.forEach((prefab) => {
                    levelDataWaveGroup.waveUUID.push(prefab.uuid);
                });
                levelDataWaveGroup.weight = waveGroup.weight;
                (this.data as LevelDataEventTriggerWave).waveGroup.push(levelDataWaveGroup);
            });
        } else {
            (this.data as LevelDataEventTriggerWave).waveGroup = [];
        }
    }

    @property({
        type: CCInteger,
        visible() {
            return this.type == LevelDataEventTriggerType.SpecialEvent;
        }
    })
    public get eventType(): eLevelSpecialEvent {
        return (this.data as LevelDataEventTriggerSpecialEvent).eventType;
    }
    public set eventType(value: eLevelSpecialEvent) {
        (this.data as LevelDataEventTriggerSpecialEvent).eventType = value;
    }
}

@ccclass('LevelEventUI')
export class LevelEventUI extends LevelElemUI {
    @property([LevelCondition])
    public conditions: LevelCondition[] = [];
    @property([LevelEventTrigger])
    public triggers: LevelEventTrigger[] = [];

    public updateGameLogic(dt: number): void {
        for (let i = 0; i < this.conditions.length; i++) {
            const cond = this.conditions[i];
            cond._index = i;
            if (cond.type == LevelDataEventCondtionType.Wave
                && (cond.data as LevelDataEventCondtionWave).targetElemID != ""
                && cond._targetElem == null) {
                const elems = this.node.scene.getComponentsInChildren(LevelElemUI);
                for (let elem of elems) {
                    if (elem.elemID == (cond.data as LevelDataEventCondtionWave).targetElemID) {
                        cond._targetElem = elem;
                        break;
                    }
                }
            }
        }
    }

    public initByLevelData(data: LevelDataEvent) {
        super.initByLevelData(data)
        if (data.conditions) {
            for (let i = 0; i < data.conditions.length; i++) {
                const condition = new LevelCondition();
                condition._index = i;
                condition.data = data.conditions[i];
                this.conditions.push(condition);
            }
        }
        if (data.triggers) {
            for (let i = 0; i < data.triggers.length; i++) {
                const trigger = new LevelEventTrigger();
                trigger._index = i;
                trigger.data = data.triggers[i];
                this.triggers.push(trigger);
                if (trigger.data._type == LevelDataEventTriggerType.Audio) {
                    let uuid = (trigger.data as LevelDataEventTriggerAudio).audioUUID;
                    assetManager.loadAny({ uuid: uuid }, (err, audio: AudioClip) => {
                        if (err) {
                            console.error("LevelEventUI initByLevelData load audio err", err);
                            return;
                        }
                        trigger._audio = audio;
                    });
                }
                if (trigger.data._type == LevelDataEventTriggerType.Wave) {
                    let waveTrigger = trigger.data as LevelDataEventTriggerWave;
                    waveTrigger.waveGroup.forEach((waveGroup) => {
                        let levelWaveGroup = new LevelWaveGroup();
                        waveGroup.waveUUID.forEach((uuid) => {
                            assetManager.loadAny({uuid:uuid}, (err, prefab:Prefab) => {
                                if (err) {
                                    console.error("LevelEventUI initByLevelData load wave prefab err", err);
                                    return;
                                }
                                levelWaveGroup.wavePrefab.push(prefab);
                            });
                        });
                        levelWaveGroup.weight = waveGroup.weight;
                        trigger.waveGroup.push(levelWaveGroup);
                    });
                }
            }
        }
    }
}