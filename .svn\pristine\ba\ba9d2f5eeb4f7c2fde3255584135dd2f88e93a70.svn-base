/**
 * 阵型编辑器
 */
'use strict';

const { updatePropByDump, disconnectGroup } = require('./../../prop');

type Selector<$> = { $: Record<keyof $, any | null> }

export const template = `
<div class="component-container"></div>
<ui-prop>
    <ui-button class="btn-add"">添加阵型点</ui-button>
    <ui-button class="btn-save">保存</ui-button>
</ui-prop>
`;

export const $ = {
    componentContainer: '.component-container',
    btnAdd: '.btn-add',
    btnSave: '.btn-save',
};

type PanelThis = Selector<typeof $> & { dump: any };

export function update(this: PanelThis, dump: any) {
    updatePropByDump(this, dump);
    this.dump = dump;
}

export async function ready(this: PanelThis) {
    disconnectGroup(this);
    this.$.btnAdd.addEventListener('confirm', async () => {
        // console.log('add formation point', this.dump);
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'level-editor',
            method: 'addFormationPoint',
            args: [this.dump?.value.uuid.value]
        });
    });

    this.$.btnSave.addEventListener('confirm', async () => {
        // console.log('save formation', this.dump);
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'level-editor',
            method: 'saveFormationGroup',
            args: [this.dump?.value.uuid.value]
        });
    });
}