# Level Editor Extension

Level editor extension for Cocos Creator airplane game.
This extension provides tools for editing formations and paths through context menus.

## Development Environment

Node.js

## Install

```bash
# Install dependent modules
npm install
# build
npm run build
```

## New Features

### Context Menu Editing

The extension now supports context menu editing for specific JSON files in the asset browser:

#### 1. Edit Formation
- **Trigger**: Right-click on `.json` files in `assets/resources/game/level/wave/formation/` directory
- **Menu Item**: "编辑阵型" (Edit Formation)
- **Function**: Automatically opens `FormationEditor` scene and loads the selected formation JSON file

#### 2. Edit Path
- **Trigger**: Right-click on `.json` files in `assets/resources/game/level/wave/path/` directory
- **Menu Item**: "编辑路径" (Edit Path)
- **Function**: Automatically opens `PathEditor` scene and loads the selected path JSON file

### Usage

1. Navigate to the corresponding JSON file in the asset browser
2. Right-click on the file
3. Select "飞机游戏" (Airplane Game) from the context menu
4. Choose the appropriate editing option ("编辑阵型" or "编辑路径")
5. The system will automatically open the corresponding editor scene and load the file data

### Technical Implementation

- Modified `assets-menu.ts` to add path detection logic
- Added `loadFormationData` and `loadPathData` methods in `scene.ts`
- Supports automatic scene switching and data loading
