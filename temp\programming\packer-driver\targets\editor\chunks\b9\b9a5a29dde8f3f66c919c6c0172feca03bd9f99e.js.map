{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>"], "names": ["_decorator", "Label", "Node", "Sprite", "Widget", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "BundleName", "EventMgr", "MyApp", "ccclass", "property", "TopUI", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Top", "getBundleName", "Home", "onLoad", "notchHeight", "platformSDK", "getStatusBarHeight", "sp", "getComponent", "isAlignTop", "top", "panel", "onShow", "args", "onHide", "onClose", "update", "dt", "onDestroy", "targetOff"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAaC,MAAAA,M,OAAAA,M;;AACtCC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;;AACRC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,K,iBAAAA,K;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;;uBAGjBY,K,WADZF,OAAO,CAAC,OAAD,C,UAEHC,QAAQ,CAACV,KAAD,C,UAKRU,QAAQ,CAACR,MAAD,C,UAGRQ,QAAQ,CAACT,IAAD,C,2BAVb,MACaU,KADb;AAAA;AAAA,4BACkC;AAAA;AAAA;;AAAA;;AAAA;AAAA;;AAEV,eAANC,MAAM,GAAW;AAAE,iBAAO,iBAAP;AAA2B;;AACtC,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,GAAf;AAAoB;;AAC7B,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,IAAlB;AAAwB;;AAQtDC,QAAAA,MAAM,GAAS;AACrB,cAAIC,WAAW,GAAG;AAAA;AAAA,8BAAMC,WAAN,CAAkBC,kBAAlB,EAAlB;AACA,eAAKC,EAAL,CAASC,YAAT,CAAsBnB,MAAtB,EAA+BoB,UAA/B,GAA4C,IAA5C;AACA,eAAKF,EAAL,CAASC,YAAT,CAAsBnB,MAAtB,EAA+BqB,GAA/B,IAAsCN,WAAtC;AACA,eAAKO,KAAL,CAAYH,YAAZ,CAAyBnB,MAAzB,EAAkCoB,UAAlC,GAA+C,IAA/C;AACA,eAAKE,KAAL,CAAYH,YAAZ,CAAyBnB,MAAzB,EAAkCqB,GAAlC,GAAwCN,WAAxC;AACH;;AAEW,cAANQ,MAAM,CAAC,GAAGC,IAAJ,EAAgC,CAC3C;;AACW,cAANC,MAAM,CAAC,GAAGD,IAAJ,EAAgC,CAC3C;;AACY,cAAPE,OAAO,CAAC,GAAGF,IAAJ,EAAgC,CAC5C;;AACSG,QAAAA,MAAM,CAACC,EAAD,EAAmB,CAClC;;AACSC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AA9B6B,O;;;;;iBAOV,I;;;;;;;iBAGC,I", "sourcesContent": ["import { _decorator, Label, Node, Sprite, sys, Widget } from 'cc';\nimport { BaseUI, UILayer } from \"db://assets/scripts/core/base/UIMgr\";\nimport { BundleName } from '../../const/BundleConst';\nimport { EventMgr } from '../../event/EventManager';\nimport { MyApp } from '../../app/MyApp';\nconst { ccclass, property } = _decorator;\n\n@ccclass('TopUI')\nexport class TopUI extends BaseUI {\n    @property(Label)\n    public static getUrl(): string { return \"prefab/ui/TopUI\"; }\n    public static getLayer(): UILayer { return UILayer.Top }\n    public static getBundleName(): string { return BundleName.Home }\n\n    @property(Sprite)\n    sp: Sprite | null = null;\n\n    @property(Node)\n    panel: Node | null = null;\n\n    protected onLoad(): void {\n        let notchHeight = MyApp.platformSDK.getStatusBarHeight();\n        this.sp!.getComponent(Widget)!.isAlignTop = true;\n        this.sp!.getComponent(Widget)!.top += notchHeight;\n        this.panel!.getComponent(Widget)!.isAlignTop = true;\n        this.panel!.getComponent(Widget)!.top = notchHeight;\n    }\n\n    async onShow(...args: any[]): Promise<void> {\n    }\n    async onHide(...args: any[]): Promise<void> {\n    }\n    async onClose(...args: any[]): Promise<void> {\n    }\n    protected update(dt: number): void {\n    }\n    protected onDestroy(): void {\n        EventMgr.targetOff(this)\n    }\n}\n\n"]}