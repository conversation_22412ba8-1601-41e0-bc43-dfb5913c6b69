import { _decorator, Component, Vec2 } from 'cc';
import { LevelDataTerrain } from "db://assets/bundles/common/script/leveldata/leveldata";
const { ccclass, property, executeInEditMode } = _decorator;

@ccclass('LevelPrefabParse')
@executeInEditMode()
export class LevelPrefabParse extends Component {

    protected onLoad(): void {
        console.log("LevelPrefabParse onLoad");
    }

    protected onDisable(): void {
        console.log("LevelPrefabParse onDisable");
        let data: LevelDataTerrain[] = [];
        this.node.children.forEach((node) => {
            data.push({
                // @ts-ignore
                uuid: node._prefab.asset._uuid,
                position: new Vec2(node.position.x, node.position.y),
                scale: new Vec2(node.scale.x, node.scale.y),  
                rotation: node.rotation.z,
            });
        });

        this._exportDataAsJson(data);
    }

    /**
     * 将数据导出为JSON文件
     * @param data 要导出的数据
     */
    private async _exportDataAsJson(data: LevelDataTerrain[]) {
        const jsonData = JSON.stringify(data, null, 2);
        
        const fileName = `${this.node.name}.json`;
        const assetPath = `db://assets/resources/game/level/background/Prefab/Config/${fileName}`;
        
        console.log("LevelPrefabParse _exportDataAsJson", assetPath);
        // @ts-ignore
        const sourceAssetInfo = await Editor.Message.request('asset-db', 'query-asset-info', assetPath);
        if (sourceAssetInfo === null) {
            console.error('查询资源信息失败:');
            this._createAsset(assetPath, jsonData);
        } else {
            console.log('导出预制体配置信息:', sourceAssetInfo);
            this._saveAsset(sourceAssetInfo!.uuid, jsonData);
        }
    } 

    /**
     * 创建新资源
     */
    private async _createAsset(assetPath: string, jsonData: string) {
        // @ts-ignore
        var createRsp = await Editor.Message.request('asset-db', 'create-asset', assetPath, jsonData);
    }
    
    /**
     * 更新现有资源
     */
    private async _saveAsset(uuid: string, jsonData: string) {
        // @ts-ignore
        const rsp = await Editor.Message.send('asset-db', 'save-asset', uuid, jsonData);
        
        this._refreshAssetDb();
    }
    
    /**
     * 刷新资源数据库
     */
    private async _refreshAssetDb() {
        // @ts-ignore
        Editor.Message.send('asset-db', 'refresh-asset', `db://assets/resources/game/level/background/Prefab/Config`);
    }
}

