{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/data/EnemyData.ts"], "names": ["EnemyData", "error", "MyApp", "AttributeConst", "AttributeData", "_planeId", "config", "planeId", "value", "lubanTables", "TbResEnemy", "get", "updateData", "recourseSpine", "addBaseAttribute", "MaxHPOutAdd", "baseHp", "AttackOutAdd", "baseAtk"], "mappings": ";;;4HASaA,S;;;;;;;;;;;;;;;;;;;;;;;;;;;AATJC,MAAAA,K,OAAAA,K;;AAIAC,MAAAA,K,iBAAAA,K;;AAEAC,MAAAA,c,iBAAAA,c;;AACAC,MAAAA,a,iBAAAA,a;;;;;;;;;2BAEIJ,S,GAAN,MAAMA,SAAN;AAAA;AAAA,0CAAsC;AAAA;AAAA;AAAA,eACzCK,QADyC,GACtB,CADsB;AACpB;AADoB,eAGzCC,MAHyC,GAGf,IAHe;AAAA;;AAGV;AAEpB,YAAPC,OAAO,GAAG;AACV,iBAAO,KAAKF,QAAZ;AACH;;AAEU,YAAPE,OAAO,CAACC,KAAD,EAAQ;AACf,cAAIA,KAAK,IAAI,KAAKH,QAAlB,EAA4B;AACxB,iBAAKA,QAAL,GAAgBG,KAAhB;AACA,iBAAKF,MAAL,GAAc;AAAA;AAAA,gCAAMG,WAAN,CAAkBC,UAAlB,CAA6BC,GAA7B,CAAiC,KAAKN,QAAtC,CAAd;AACA,iBAAKO,UAAL;AACH;AACJ;;AAEgB,YAAbC,aAAa,GAAG;AAChB,cAAI,CAAC,KAAKP,MAAV,EAAkB;AACd,mBAAO,EAAP;AACH;AACJ;;AAEDM,QAAAA,UAAU,GAAG;AACT,cAAI,CAAC,KAAKN,MAAV,EAAkB;AACdL,YAAAA,KAAK,CAAE,cAAa,KAAKI,QAAS,6CAA7B,CAAL;AACA;AACH;;AACD,eAAKS,gBAAL,CAAsB;AAAA;AAAA,gDAAeC,WAArC,EAAkD,KAAKT,MAAL,CAAYU,MAA9D;AACA,eAAKF,gBAAL,CAAsB;AAAA;AAAA,gDAAeG,YAArC,EAAmD,KAAKX,MAAL,CAAYY,OAA/D;AACH;;AA9BwC,O", "sourcesContent": ["import { error } from \"cc\";\r\n\r\nimport { ResEnemy } from \"../../autogen/luban/schema\";\r\n\r\nimport { MyApp } from \"db://assets/bundles/common/script/app/MyApp\";\r\n\r\nimport { AttributeConst } from \"db://assets/bundles/common/script/const/AttributeConst\";\r\nimport { AttributeData } from \"db://assets/bundles/common/script/data/base/AttributeData\";\r\n\r\nexport class EnemyData extends AttributeData {\r\n    _planeId: number = 0;//飞机id\r\n\r\n    config: ResEnemy | null = null;//飞机静态配置\r\n\r\n    get planeId() {\r\n        return this._planeId;\r\n    }\r\n\r\n    set planeId(value) {\r\n        if (value != this._planeId) {\r\n            this._planeId = value;\r\n            this.config = MyApp.lubanTables.TbResEnemy.get(this._planeId)!;\r\n            this.updateData();\r\n        }\r\n    }\r\n\r\n    get recourseSpine() {\r\n        if (!this.config) {\r\n            return \"\";\r\n        }\r\n    }\r\n\r\n    updateData() {\r\n        if (!this.config) {\r\n            error(`enemyPlane ${this._planeId}: config is null, cannot update attributes.`);\r\n            return;\r\n        }\r\n        this.addBaseAttribute(AttributeConst.MaxHPOutAdd, this.config.baseHp);\r\n        this.addBaseAttribute(AttributeConst.AttackOutAdd, this.config.baseAtk);\r\n    }\r\n}"]}