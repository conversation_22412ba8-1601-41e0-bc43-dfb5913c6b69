{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/PlaneBase.ts"], "names": ["_decorator", "Label", "Node", "Sprite", "tween", "Tween", "DamageType", "EffectType", "AttributeConst", "AttributeData", "GameEnum", "Entity", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>llComp", "forEachEntityByTargetType", "GameIns", "PlaneEventType", "PlaneEventComp", "ccclass", "property", "PlaneBase", "constructor", "enemy", "type", "bDamageable", "curHp", "hurtTime", "collide<PERSON>omp", "_skillComp", "_buffComp", "_eventComp", "_attributeData", "maxHp", "attribute", "getMaxHP", "init", "addComp", "skillComp", "buff<PERSON><PERSON>p", "colliderEnabled", "value", "isEnable", "CastSkill", "skillID", "Cast", "addHp", "heal", "Math", "min", "updateHpUI", "hurt", "damage", "isDead", "gameDataManager", "gameTime", "cutHp", "PlaneEventTrigger", "FatalInjuryHurt", "playHurtAnim", "to<PERSON><PERSON>", "collisionLevel", "collisionHurt", "collisionPlane", "plane", "max", "getFinialAttributeByOutInKey", "CollisionHurtResistanceOutAdd", "CollisionHurtResistanceOutPer", "CollisionHurtResistanceInAdd", "CollisionHurtResistanceInPer", "getFinalAttributeByKey", "CollisionHurtDerateOut", "CollisionHurtDerateIn", "newHp", "destroyType", "EnemyDestroyType", "Die", "hpBar", "fill<PERSON><PERSON><PERSON>", "hpAniSprite", "duration", "abs", "stopAllByTarget", "to", "call", "start", "hpfont", "string", "toFixed", "ApplyBuffEffect", "buff", "effectData", "AttrMaxHPPer", "ApplyBuffAttributeOutInEffect", "MaxHPOutPer", "MaxHPInPer", "AttrMaxHPAdd", "MaxHPOutAdd", "MaxHPInAdd", "AttrHPRecoveryPer", "HPRecoveryOutPer", "HPRecoveryInPer", "AttrHPRecoveryAdd", "HPRecoveryOutAdd", "HPRecoveryInAdd", "AttrHPRecoveryMaxHPPerAdd", "MaxHPRecoveryRateOut", "MaxHPRecoveryRateIn", "HealMaxHPPer", "param", "length", "floor", "Heal<PERSON>ose<PERSON><PERSON><PERSON>", "HealHP", "AttrAttackPer", "AttackOutPer", "AttackInPer", "AttrAttackAdd", "AttackOutAdd", "AttackInAdd", "AttrAttackBossPer", "BossHurtBonusOut", "BossHurtBonusIn", "AttrAttackNormalPer", "NormalHurtBonusOut", "NormalHurtBonusIn", "AttrFortunatePer", "FortunateOutPer", "FortunateInPer", "AttrFortunateAdd", "FortunateOutAdd", "FortunateInAdd", "AttrMissAdd", "MissRateOut", "MissRateIn", "AttrBulletHurtResistancePer", "BulletHurtResistanceOutPer", "BulletHurtResistanceInPer", "AttrBulletHurtResistanceAdd", "BulletHurtResistanceOutAdd", "BulletHurtResistanceInAdd", "AttrBulletHurtDerateAdd", "BulletHurtDerateOut", "BulletHurtDerateIn", "AttrCollisionHurtResistancePer", "AttrCollisionHurtResistanceAdd", "AttrCollisionHurtDerateAdd", "AttrFinalScoreAdd", "FinalScoreRateOut", "FinalScoreRateIn", "AttrKillScoreAdd", "KillScoreRateOut", "KillScoreRateIn", "AttrEnergyRecoveryPerAdd", "EnergyRecoveryOutPer", "EnergyRecoveryInPer", "AttrEnergyRecoveryAdd", "EnergyRecoveryOutAdd", "EnergyRecoveryInAdd", "AttrPickRadiusPer", "PickRadiusOutPer", "PickRadiusInPer", "AttrPickRadiusAdd", "PickRadiusOutAdd", "PickRadiusInAdd", "A<PERSON><PERSON><PERSON><PERSON>", "buff<PERSON>", "target", "entity", "isOutside", "ImmuneBulletHurt", "addModify", "id", "StatusImmuneBulletHurt", "ImmuneCollisionHurt", "StatusImmuneCollisionHurt", "IgnoreBullet", "StatusIgnoreBullet", "IgnoreCollision", "StatusIgnoreCollision", "ImmuneNuclearHurt", "StatusImmuneNuclearHurt", "ImmuneActiveSkillHurt", "StatusImmuneActiveSkillHurt", "Invincible", "StatusInvincible", "AttrNuclearMax", "ApplyBuffAttributeEffect", "NuclearMax", "AttrBulletAttackAdd", "damageType", "ALL", "BulletAttackOutAdd", "BulletAttackInAdd", "EXPLOSIVE", "ExplosiveBulletAttackOutAdd", "ExplosiveBulletAttackInAdd", "NORMAL", "NormalBulletAttackOutAdd", "NormalBulletAttackInAdd", "ENERGETIC", "EnergeticBulletAttackOutAdd", "EnergeticBulletAttackInAdd", "PHYSICAL", "PhysicsBulletAttackOutAdd", "PhysicsBulletAttackInAdd", "AttrBulletAttackPer", "BulletAttackOutPer", "BulletAttackInPer", "ExplosiveBulletAttackOutPer", "ExplosiveBulletAttackInPer", "NormalBulletAttackOutPer", "NormalBulletAttackInPer", "EnergeticBulletAttackOutPer", "EnergeticBulletAttackInPer", "PhysicsBulletAttackOutPer", "PhysicsBulletAttackInPer", "AttrBulletHurtFix", "BulletHurtFixOut", "BulletHurtFixIn", "ExplosiveBulletHurtFixOut", "ExplosiveBulletHurtFixIn", "NormalBulletHurtFixOut", "NormalBulletHurtFixIn", "EnergeticBulletHurtFixOut", "EnergeticBulletHurtFixIn", "PhysicsBulletHurtFixOut", "PhysicsBulletHurtFixIn", "HurtMaxHPPer", "HurtCurHPPer", "AttrNuclearAttackPer", "NuclearAttackOutPer", "NuclearAttackInPer", "AttrNuclearAttackAdd", "NuclearAttackOutAdd", "NuclearAttackInAdd", "FireBullet", "AttrNuclearHurtFix", "NuclearHurtFixOut", "NuclearHurtFixIn", "AddNuclear", "addNuclear", "inKey", "outKey", "key", "RemoveBuffEffect", "removeModify", "PlaneEventRegister", "et", "cb", "Register", "PlaneEventUnRegister", "UnRegister", "params", "<PERSON><PERSON>", "setAnimSpeed", "speed", "get<PERSON><PERSON><PERSON>", "num", "pickDiamond<PERSON>um", "killEnemyNum", "usedNuclearNum", "usedSuperNum", "nuclearNum"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,K,OAAAA,K;AAAaC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,K,OAAAA,K;;AAG9CC,MAAAA,U,iBAAAA,U;AAAyBC,MAAAA,U,iBAAAA,U;;AACzBC,MAAAA,c,iBAAAA,c;;AACAC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,Q,iBAAAA,Q;;AAEFC,MAAAA,M;;AAMCC,MAAAA,Q,iBAAAA,Q;;AACDC,MAAAA,S;;AACAC,MAAAA,yB;;AACEC,MAAAA,O,kBAAAA,O;;AACAC,MAAAA,c,kBAAAA,c;;AACFC,MAAAA,c;;;;;;;;;OAlBD;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBnB,U;;yBAsBToB,S,WADpBF,OAAO,CAAC,WAAD,C,UAKHC,QAAQ,CAACjB,IAAD,C,UAERiB,QAAQ,CAAChB,MAAD,C,UAERgB,QAAQ,CAAChB,MAAD,C,UAERgB,QAAQ,CAAClB,KAAD,C,2BAXb,MACqBmB,SADrB;AAAA;AAAA,4BAC8C;AAC1CC,QAAAA,WAAW,GAAG;AACV;;AADU;;AAAA;;AAMe;AANf;;AAQqB;AARrB;;AAUe;AAVf,eAYdC,KAZc,GAYN,IAZM;AAYA;AAZA,eAadC,IAbc,GAaP,CAbO;AAaJ;AAbI,eAcdC,WAdc,GAcS,IAdT;AAAA,eAmBdC,KAnBc,GAmBE,CAnBF;AAAA,eAoBdC,QApBc,GAoBI,CApBJ;AAAA,eAsBdC,WAtBc,GAsB0D,IAtB1D;AAsBgE;AAtBhE,eAwBNC,UAxBM,GAwByB,IAxBzB;AAAA,eAyBNC,SAzBM,GAyBuB,IAzBvB;AAAA,eA0BNC,UA1BM,GA0B8B,IA1B9B;AA4Bd;AA5Bc,eA6BNC,cA7BM,GA6B0B;AAAA;AAAA,+CA7B1B;AAEb;;AAY4B;AAEpB,YAALC,KAAK,GAAU;AACf,iBAAQ,KAAKC,SAAL,CAAeC,QAAf,EAAR;AACH;;AAaDC,QAAAA,IAAI,GAAG;AACH,eAAKP,UAAL,GAAkB;AAAA;AAAA,uCAAlB;AACA,eAAKQ,OAAL,CAAa,OAAb,EAAsB,KAAKR,UAA3B;AACA,eAAKC,SAAL,GAAiB;AAAA;AAAA,qCAAjB;AACA,eAAKO,OAAL,CAAa,MAAb,EAAqB,KAAKP,SAA1B;AACA,eAAKC,UAAL,GAAkB;AAAA;AAAA,iDAAlB;AACA,eAAKM,OAAL,CAAa,OAAb,EAAsB,KAAKN,UAA3B;AACA,gBAAMK,IAAN;AACH;;AAEY,YAATE,SAAS,GAAG;AACZ,iBAAO,KAAKT,UAAZ;AACH;;AAEW,YAARU,QAAQ,GAAG;AACX,iBAAO,KAAKT,SAAZ;AACH;;AAEY,YAATI,SAAS,GAAkB;AAC3B,iBAAO,KAAKF,cAAZ;AACH;;AAEkB,YAAfQ,eAAe,CAACC,KAAD,EAAiB;AAChC,cAAI,KAAKb,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiBc,QAAjB,GAA4BD,KAA5B;AACH;AACJ;;AACkB,YAAfD,eAAe,GAAY;AAC3B,iBAAO,KAAKZ,WAAL,GAAmB,KAAKA,WAAL,CAAiBc,QAApC,GAA+C,KAAtD;AACH;;AAEDC,QAAAA,SAAS,CAACC,OAAD,EAAkB;AACvB,eAAKN,SAAL,CAAeO,IAAf,CAAoB,IAApB,EAA0BD,OAA1B;AACH;;AAEDE,QAAAA,KAAK,CAACC,IAAD,EAAe;AAChB,eAAKrB,KAAL,GAAasB,IAAI,CAACC,GAAL,CACT,KAAKhB,KADI,EAET,KAAKP,KAAL,GAAaqB,IAFJ,CAAb;AAIA,eAAKG,UAAL;AACH;;AAEDC,QAAAA,IAAI,CAACC,MAAD,EAAiB;AACjB,cAAI,KAAKC,MAAT,EAAiB;AACb;AACH;;AACD,eAAK1B,QAAL,GAAgB;AAAA;AAAA,kCAAQ2B,eAAR,CAAwBC,QAAxC;AACA,eAAKC,KAAL,CAAWJ,MAAX;;AACA,cAAI,KAAK1B,KAAL,IAAc,CAAlB,EAAqB;AACjB,iBAAK+B,iBAAL,CAAuB;AAAA;AAAA,kDAAeC,eAAtC;AACH;;AACD,eAAKC,YAAL;;AACA,cAAI,KAAKjC,KAAL,IAAc,CAAlB,EAAqB;AACjB,iBAAKkC,KAAL;AACH;AACJ;;AAEiB,YAAdC,cAAc,GAAG;AACjB,iBAAO,CAAP;AACH;;AACgB,YAAbC,aAAa,GAAG;AAChB,iBAAO,CAAP;AACH,SA/FyC,CAiG1C;;;AACAC,QAAAA,cAAc,CAACC,KAAD,EAAmB;AAC7B,cAAI,KAAKX,MAAL,IAAeW,KAAK,CAACX,MAAzB,EAAiC;AAC7B;AACH;;AACD,cAAI,KAAKQ,cAAL,GAAsBG,KAAK,CAACH,cAAhC,EAAgD;AAC5C;AACH;;AACD,cAAIV,IAAJ;;AACA,cAAI,KAAKU,cAAL,GAAsBG,KAAK,CAACH,cAAhC,EAAgD;AAC5CV,YAAAA,IAAI,GAAGH,IAAI,CAACiB,GAAL,CAAS,KAAKhC,KAAd,EAAqB+B,KAAK,CAACF,aAA3B,CAAP;AACH,WAFD,MAEO;AACHX,YAAAA,IAAI,GAAGa,KAAK,CAACF,aAAN,IAAuB,CAAC,CAAxB,GAA4B,KAAK7B,KAAjC,GAAyC+B,KAAK,CAACF,aAAtD;AACH;;AAEDX,UAAAA,IAAI,GAAG,CAACA,IAAI,GAAG,KAAKjB,SAAL,CAAegC,4BAAf,CACP;AAAA;AAAA,gDAAeC,6BADR,EACuC;AAAA;AAAA,gDAAeC,6BADtD,EAEP;AAAA;AAAA,gDAAeC,4BAFR,EAEsC;AAAA;AAAA,gDAAeC,4BAFrD,CAAR,KAGA,IAAI,KAAKpC,SAAL,CAAeqC,sBAAf,CAAsC;AAAA;AAAA,gDAAeC,sBAArD,CAHJ,KAIA,IAAI,KAAKtC,SAAL,CAAeqC,sBAAf,CAAsC;AAAA;AAAA,gDAAeE,qBAArD,CAJJ,CAAP;AAKA,eAAKtB,IAAL,CAAUA,IAAV;AACH;AAED;AACJ;AACA;AACA;;;AACIK,QAAAA,KAAK,CAACJ,MAAD,EAAiB;AAClB,cAAMsB,KAAK,GAAG,KAAKhD,KAAL,GAAa0B,MAA3B;AACA,eAAK1B,KAAL,GAAasB,IAAI,CAACiB,GAAL,CAAS,CAAT,EAAYS,KAAZ,CAAb;AAEA,eAAKxB,UAAL;AACH;;AAEDU,QAAAA,KAAK,CAACe,WAAD,EAAkF;AAAA,cAAjFA,WAAiF;AAAjFA,YAAAA,WAAiF,GAAxC;AAAA;AAAA,sCAASC,gBAAT,CAA0BC,GAAc;AAAA;;AACnF,cAAI,KAAKxB,MAAT,EAAiB;AACb,mBAAO,KAAP;AACH;;AAED,eAAKI,iBAAL,CAAuB;AAAA;AAAA,gDAAeoB,GAAtC,EAA2C,IAA3C;AACA,eAAKxB,MAAL,GAAc,IAAd;AACA,eAAKb,eAAL,GAAuB,KAAvB;AACA,iBAAO,IAAP;AACH;AACD;AACJ;AACA;;;AACIU,QAAAA,UAAU,GAAG;AACT,cAAI,KAAK4B,KAAT,EAAgB;AACZ;AACA,iBAAKA,KAAL,CAAWC,SAAX,GAAuB,KAAKrD,KAAL,GAAa,KAAKO,KAAzC;;AAEA,gBAAI,KAAK+C,WAAT,EAAsB;AAClB;AACA,kBAAMC,QAAQ,GAAGjC,IAAI,CAACkC,GAAL,CAAS,KAAKF,WAAL,CAAiBD,SAAjB,GAA6B,KAAKD,KAAL,CAAWC,SAAjD,CAAjB;AAEAzE,cAAAA,KAAK,CAAC6E,eAAN,CAAsB,KAAKH,WAA3B,EAJkB,CAKlB;;AACA3E,cAAAA,KAAK,CAAC,KAAK2E,WAAN,CAAL,CACKI,EADL,CACQH,QADR,EACkB;AAAEF,gBAAAA,SAAS,EAAE,KAAKD,KAAL,CAAWC;AAAxB,eADlB,EAEKM,IAFL,CAEU,MAAM,CAEX,CAJL,EAKKC,KALL;AAMH;AACJ,WAlBQ,CAoBT;;;AACA,eAAKC,MAAL,KAAgB,KAAKA,MAAL,CAAaC,MAAb,GAAsB,KAAK9D,KAAL,CAAW+D,OAAX,CAAmB,CAAnB,CAAtC;AACH;;AAED9B,QAAAA,YAAY,GAAG,CACX;AACH;;AAED+B,QAAAA,eAAe,CAACC,IAAD,EAAoBC,UAApB,EAA6C;AACxD,kBAAQA,UAAU,CAACpE,IAAnB;AACI,iBAAK;AAAA;AAAA,0CAAWqE,YAAhB;AACI,mBAAKC,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,oDAAeI,WAAxD,EAAqE;AAAA;AAAA,oDAAeC,UAApF,EAAgGJ,UAAhG;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWK,YAAhB;AACI,mBAAKH,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,oDAAeO,WAAxD,EAAqE;AAAA;AAAA,oDAAeC,UAApF,EAAgGP,UAAhG;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWQ,iBAAhB;AACI,mBAAKN,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,oDAAeU,gBAAxD,EAA0E;AAAA;AAAA,oDAAeC,eAAzF,EAA0GV,UAA1G;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWW,iBAAhB;AACI,mBAAKT,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,oDAAea,gBAAxD,EAA0E;AAAA;AAAA,oDAAeC,eAAzF,EAA0Gb,UAA1G;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWc,yBAAhB;AACI,mBAAKZ,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,oDAAegB,oBAAxD,EAA8E;AAAA;AAAA,oDAAeC,mBAA7F,EAAkHhB,UAAlH;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWiB,YAAhB;AACI,kBAAIjB,UAAU,CAACkB,KAAX,CAAiBC,MAAjB,IAA2B,CAA/B,EAAkC;AAC9B,qBAAKjE,KAAL,CAAWE,IAAI,CAACgE,KAAL,CAAW,KAAK/E,KAAL,GAAa2D,UAAU,CAACkB,KAAX,CAAiB,CAAjB,CAAb,GAAiC,KAA5C,CAAX;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWG,aAAhB;AACI,kBAAIrB,UAAU,CAACkB,KAAX,CAAiBC,MAAjB,IAA2B,CAA/B,EAAkC;AAC9B,qBAAKjE,KAAL,CAAWE,IAAI,CAACgE,KAAL,CAAW,CAAC,KAAK/E,KAAL,GAAa,KAAKP,KAAnB,IAA4BkE,UAAU,CAACkB,KAAX,CAAiB,CAAjB,CAA5B,GAAgD,KAA3D,CAAX;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWI,MAAhB;AACI,kBAAItB,UAAU,CAACkB,KAAX,CAAiBC,MAAjB,IAA2B,CAA/B,EAAkC;AAC9B,qBAAKjE,KAAL,CAAW8C,UAAU,CAACkB,KAAX,CAAiB,CAAjB,CAAX;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWK,aAAhB;AACI,mBAAKrB,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,oDAAeyB,YAAxD,EAAsE;AAAA;AAAA,oDAAeC,WAArF,EAAkGzB,UAAlG;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAW0B,aAAhB;AACI,mBAAKxB,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,oDAAe4B,YAAxD,EAAsE;AAAA;AAAA,oDAAeC,WAArF,EAAkG5B,UAAlG;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAW6B,iBAAhB;AACI,mBAAK3B,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,oDAAe+B,gBAAxD,EAA0E;AAAA;AAAA,oDAAeC,eAAzF,EAA0G/B,UAA1G;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWgC,mBAAhB;AACI,mBAAK9B,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,oDAAekC,kBAAxD,EAA4E;AAAA;AAAA,oDAAeC,iBAA3F,EAA8GlC,UAA9G;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWmC,gBAAhB;AACI,mBAAKjC,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,oDAAeqC,eAAxD,EAAyE;AAAA;AAAA,oDAAeC,cAAxF,EAAwGrC,UAAxG;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWsC,gBAAhB;AACI,mBAAKpC,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,oDAAewC,eAAxD,EAAyE;AAAA;AAAA,oDAAeC,cAAxF,EAAwGxC,UAAxG;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWyC,WAAhB;AACI,mBAAKvC,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,oDAAe2C,WAAxD,EAAqE;AAAA;AAAA,oDAAeC,UAApF,EAAgG3C,UAAhG;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAW4C,2BAAhB;AACI,mBAAK1C,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,oDAAe8C,0BAAxD,EAAoF;AAAA;AAAA,oDAAeC,yBAAnG,EAA8H9C,UAA9H;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAW+C,2BAAhB;AACI,mBAAK7C,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,oDAAeiD,0BAAxD,EAAoF;AAAA;AAAA,oDAAeC,yBAAnG,EAA8HjD,UAA9H;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWkD,uBAAhB;AACI,mBAAKhD,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,oDAAeoD,mBAAxD,EAA6E;AAAA;AAAA,oDAAeC,kBAA5F,EAAgHpD,UAAhH;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWqD,8BAAhB;AACI,mBAAKnD,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,oDAAevB,6BAAxD,EAAuF;AAAA;AAAA,oDAAeE,4BAAtG,EAAoIsB,UAApI;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWsD,8BAAhB;AACI,mBAAKpD,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,oDAAexB,6BAAxD,EAAuF;AAAA;AAAA,oDAAeE,4BAAtG,EAAoIuB,UAApI;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWuD,0BAAhB;AACI,mBAAKrD,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,oDAAenB,sBAAxD,EAAgF;AAAA;AAAA,oDAAeC,qBAA/F,EAAsHmB,UAAtH;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWwD,iBAAhB;AACI,mBAAKtD,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,oDAAe0D,iBAAxD,EAA2E;AAAA;AAAA,oDAAeC,gBAA1F,EAA4G1D,UAA5G;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAW2D,gBAAhB;AACI,mBAAKzD,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,oDAAe6D,gBAAxD,EAA0E;AAAA;AAAA,oDAAeC,eAAzF,EAA0G7D,UAA1G;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAW8D,wBAAhB;AACI,mBAAK5D,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,oDAAegE,oBAAxD,EAA8E;AAAA;AAAA,oDAAeC,mBAA7F,EAAkHhE,UAAlH;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWiE,qBAAhB;AACI,mBAAK/D,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,oDAAemE,oBAAxD,EAA8E;AAAA;AAAA,oDAAeC,mBAA7F,EAAkHnE,UAAlH;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWoE,iBAAhB;AACI,mBAAKlE,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,oDAAesE,gBAAxD,EAA0E;AAAA;AAAA,oDAAeC,eAAzF,EAA0GtE,UAA1G;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWuE,iBAAhB;AACI,mBAAKrE,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,oDAAeyE,gBAAxD,EAA0E;AAAA;AAAA,oDAAeC,eAAzF,EAA0GzE,UAA1G;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAW0E,SAAhB;AACI,kBAAI1E,UAAU,CAACkB,KAAX,CAAiBC,MAAjB,GAA0B,CAA9B,EAAiC;AAC7B;AACH;;AACD,kBAAMwD,MAAM,GAAG3E,UAAU,CAACkB,KAAX,CAAiB,CAAjB,CAAf;AACA,kBAAM0D,MAAM,GAAG5E,UAAU,CAACkB,KAAX,CAAiB,CAAjB,CAAf;AACA;AAAA;AAAA,0EAA0B,IAA1B,EAAgC0D,MAAhC,EAAyCC,MAAD,IAAY;AAChDA,gBAAAA,MAAM,CAAClI,QAAP,CAAgB+H,SAAhB,CAA0B,CAAA3E,IAAI,QAAJ,YAAAA,IAAI,CAAE+E,SAAN,KAAiB,KAA3C,EAAkDH,MAAlD;AACH,eAFD;AAGA;;AACJ,iBAAK;AAAA;AAAA,0CAAWI,gBAAhB;AACI,kBAAIhF,IAAJ,EAAU;AACN,qBAAKzD,SAAL,CAAe0I,SAAf,CAAyBjF,IAAI,CAACkF,EAA9B,EAAkC;AAAA;AAAA,sDAAeC,sBAAjD,EAAyE,CAAzE;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWC,mBAAhB;AACI,kBAAIpF,IAAJ,EAAU;AACN,qBAAKzD,SAAL,CAAe0I,SAAf,CAAyBjF,IAAI,CAACkF,EAA9B,EAAkC;AAAA;AAAA,sDAAeG,yBAAjD,EAA4E,CAA5E;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWC,YAAhB;AACI,kBAAItF,IAAJ,EAAU;AACN,qBAAKzD,SAAL,CAAe0I,SAAf,CAAyBjF,IAAI,CAACkF,EAA9B,EAAkC;AAAA;AAAA,sDAAeK,kBAAjD,EAAqE,CAArE;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWC,eAAhB;AACI,kBAAIxF,IAAJ,EAAU;AACN,qBAAKzD,SAAL,CAAe0I,SAAf,CAAyBjF,IAAI,CAACkF,EAA9B,EAAkC;AAAA;AAAA,sDAAeO,qBAAjD,EAAwE,CAAxE;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWC,iBAAhB;AACI,kBAAI1F,IAAJ,EAAU;AACN,qBAAKzD,SAAL,CAAe0I,SAAf,CAAyBjF,IAAI,CAACkF,EAA9B,EAAkC;AAAA;AAAA,sDAAeS,uBAAjD,EAA0E,CAA1E;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWC,qBAAhB;AACI,kBAAI5F,IAAJ,EAAU;AACN,qBAAKzD,SAAL,CAAe0I,SAAf,CAAyBjF,IAAI,CAACkF,EAA9B,EAAkC;AAAA;AAAA,sDAAeW,2BAAjD,EAA8E,CAA9E;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWC,UAAhB;AACI,kBAAI9F,IAAJ,EAAU;AACN,qBAAKzD,SAAL,CAAe0I,SAAf,CAAyBjF,IAAI,CAACkF,EAA9B,EAAkC;AAAA;AAAA,sDAAea,gBAAjD,EAAmE,CAAnE;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWC,cAAhB;AACI,mBAAKC,wBAAL,CAA8BjG,IAA9B,EAAoC;AAAA;AAAA,oDAAekG,UAAnD,EAA+DjG,UAA/D;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWkG,mBAAhB;AACI,kBAAIC,UAAU,GAAG;AAAA;AAAA,4CAAWC,GAA5B;;AACA,kBAAIpG,UAAU,CAACkB,KAAX,CAAiBC,MAAjB,GAA0B,CAA9B,EAAiC;AAC7BgF,gBAAAA,UAAU,GAAGnG,UAAU,CAACkB,KAAX,CAAiB,CAAjB,CAAb;AACH;;AACD,sBAAOiF,UAAP;AACI,qBAAK;AAAA;AAAA,8CAAWC,GAAhB;AACI,uBAAKlG,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,wDAAesG,kBAAxD,EAA4E;AAAA;AAAA,wDAAeC,iBAA3F,EAA8GtG,UAA9G;AACA;;AACJ,qBAAK;AAAA;AAAA,8CAAWuG,SAAhB;AACI,uBAAKrG,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,wDAAeyG,2BAAxD,EAAqF;AAAA;AAAA,wDAAeC,0BAApG,EAAgIzG,UAAhI;AACA;;AACJ,qBAAK;AAAA;AAAA,8CAAW0G,MAAhB;AACI,uBAAKxG,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,wDAAe4G,wBAAxD,EAAkF;AAAA;AAAA,wDAAeC,uBAAjG,EAA0H5G,UAA1H;AACA;;AACJ,qBAAK;AAAA;AAAA,8CAAW6G,SAAhB;AACI,uBAAK3G,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,wDAAe+G,2BAAxD,EAAqF;AAAA;AAAA,wDAAeC,0BAApG,EAAgI/G,UAAhI;AACA;;AACJ,qBAAK;AAAA;AAAA,8CAAWgH,QAAhB;AACI,uBAAK9G,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,wDAAekH,yBAAxD,EAAmF;AAAA;AAAA,wDAAeC,wBAAlG,EAA4HlH,UAA5H;AACA;AAfR;;AAiBJ,iBAAK;AAAA;AAAA,0CAAWmH,mBAAhB;AACIhB,cAAAA,UAAU,GAAG;AAAA;AAAA,4CAAWC,GAAxB;;AACA,kBAAIpG,UAAU,CAACkB,KAAX,CAAiBC,MAAjB,GAA0B,CAA9B,EAAiC;AAC7BgF,gBAAAA,UAAU,GAAGnG,UAAU,CAACkB,KAAX,CAAiB,CAAjB,CAAb;AACH;;AACD,sBAAOiF,UAAP;AACI,qBAAK;AAAA;AAAA,8CAAWC,GAAhB;AACI,uBAAKlG,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,wDAAeqH,kBAAxD,EAA4E;AAAA;AAAA,wDAAeC,iBAA3F,EAA8GrH,UAA9G;AACA;;AACJ,qBAAK;AAAA;AAAA,8CAAWuG,SAAhB;AACI,uBAAKrG,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,wDAAeuH,2BAAxD,EAAqF;AAAA;AAAA,wDAAeC,0BAApG,EAAgIvH,UAAhI;AACA;;AACJ,qBAAK;AAAA;AAAA,8CAAW0G,MAAhB;AACI,uBAAKxG,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,wDAAeyH,wBAAxD,EAAkF;AAAA;AAAA,wDAAeC,uBAAjG,EAA0HzH,UAA1H;AACA;;AACJ,qBAAK;AAAA;AAAA,8CAAW6G,SAAhB;AACI,uBAAK3G,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,wDAAe2H,2BAAxD,EAAqF;AAAA;AAAA,wDAAeC,0BAApG,EAAgI3H,UAAhI;AACA;;AACJ,qBAAK;AAAA;AAAA,8CAAWgH,QAAhB;AACI,uBAAK9G,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,wDAAe6H,yBAAxD,EAAmF;AAAA;AAAA,wDAAeC,wBAAlG,EAA4H7H,UAA5H;AACA;AAfR;;AAiBA;;AACJ,iBAAK;AAAA;AAAA,0CAAW8H,iBAAhB;AACI3B,cAAAA,UAAU,GAAG;AAAA;AAAA,4CAAWC,GAAxB;;AACA,kBAAIpG,UAAU,CAACkB,KAAX,CAAiBC,MAAjB,GAA0B,CAA9B,EAAiC;AAC7BgF,gBAAAA,UAAU,GAAGnG,UAAU,CAACkB,KAAX,CAAiB,CAAjB,CAAb;AACH;;AACD,sBAAOiF,UAAP;AACI,qBAAK;AAAA;AAAA,8CAAWC,GAAhB;AACI,uBAAKlG,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,wDAAegI,gBAAxD,EAA0E;AAAA;AAAA,wDAAeC,eAAzF,EAA0GhI,UAA1G;AACA;;AACJ,qBAAK;AAAA;AAAA,8CAAWuG,SAAhB;AACI,uBAAKrG,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,wDAAekI,yBAAxD,EAAmF;AAAA;AAAA,wDAAeC,wBAAlG,EAA4HlI,UAA5H;AACA;;AACJ,qBAAK;AAAA;AAAA,8CAAW0G,MAAhB;AACI,uBAAKxG,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,wDAAeoI,sBAAxD,EAAgF;AAAA;AAAA,wDAAeC,qBAA/F,EAAsHpI,UAAtH;AACA;;AACJ,qBAAK;AAAA;AAAA,8CAAW6G,SAAhB;AACI,uBAAK3G,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,wDAAesI,yBAAxD,EAAmF;AAAA;AAAA,wDAAeC,wBAAlG,EAA4HtI,UAA5H;AACA;;AACJ,qBAAK;AAAA;AAAA,8CAAWgH,QAAhB;AACI,uBAAK9G,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,wDAAewI,uBAAxD,EAAiF;AAAA;AAAA,wDAAeC,sBAAhG,EAAwHxI,UAAxH;AACA;AAfR;;AAiBA;;AACJ,iBAAK;AAAA;AAAA,0CAAWyI,YAAhB;AACI,kBAAIzI,UAAU,CAACkB,KAAX,CAAiBC,MAAjB,GAA0B,CAA9B,EAAiC;AAC7B;AACH;;AACD,mBAAK5D,IAAL,CAAU,KAAKlB,KAAL,GAAa2D,UAAU,CAACkB,KAAX,CAAiB,CAAjB,CAAvB;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWwH,YAAhB;AACI,kBAAI1I,UAAU,CAACkB,KAAX,CAAiBC,MAAjB,GAA0B,CAA9B,EAAiC;AAC7B;AACH;;AACD,mBAAK5D,IAAL,CAAU,KAAKzB,KAAL,GAAakE,UAAU,CAACkB,KAAX,CAAiB,CAAjB,CAAvB;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWyH,oBAAhB;AACI,mBAAKzI,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,oDAAe6I,mBAAxD,EAA6E;AAAA;AAAA,oDAAeC,kBAA5F,EAAgH7I,UAAhH;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAW8I,oBAAhB;AACI,mBAAK5I,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,oDAAegJ,mBAAxD,EAA6E;AAAA;AAAA,oDAAeC,kBAA5F,EAAgHhJ,UAAhH;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWiJ,UAAhB;AACI;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWC,kBAAhB;AACI,mBAAKhJ,6BAAL,CAAmCH,IAAnC,EAAyC;AAAA;AAAA,oDAAeoJ,iBAAxD,EAA2E;AAAA;AAAA,oDAAeC,gBAA1F,EAA4GpJ,UAA5G;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWqJ,UAAhB;AACI,kBAAIrJ,UAAU,CAACkB,KAAX,CAAiBC,MAAjB,GAA0B,CAA9B,EAAiC;AAC7B,qBAAKmI,UAAL,CAAgBtJ,UAAU,CAACkB,KAAX,CAAiB,CAAjB,CAAhB;AACH;;AACD;;AACJ;AACI;AA1OR;AA4OH;;AAEOhB,QAAAA,6BAA6B,CAACH,IAAD,EAAoBwJ,KAApB,EAA2CC,MAA3C,EAAkExJ,UAAlE,EAA2F;AAC5H,cAAI,CAACD,IAAL,EAAW;AACP;AACH;;AACD,cAAIA,IAAI,CAAC+E,SAAT,EAAoB;AAChB,iBAAKkB,wBAAL,CAA8BjG,IAA9B,EAAoCyJ,MAApC,EAA4CxJ,UAA5C;AACH,WAFD,MAEO;AACH,iBAAKgG,wBAAL,CAA8BjG,IAA9B,EAAoCwJ,KAApC,EAA2CvJ,UAA3C;AACH;AACJ;;AACOgG,QAAAA,wBAAwB,CAACjG,IAAD,EAAoB0J,GAApB,EAAiCzJ,UAAjC,EAA0D;AACtF,cAAI,CAACD,IAAL,EAAW;AACP;AACH;;AACD,cAAIC,UAAU,CAACkB,KAAX,CAAiBC,MAAjB,GAA0B,CAA9B,EAAiC;AAC7B;AACH;;AACD,eAAK7E,SAAL,CAAe0I,SAAf,CAAyBjF,IAAI,CAACkF,EAA9B,EAAkCwE,GAAlC,EAAuCzJ,UAAU,CAACkB,KAAX,CAAiB,CAAjB,CAAvC;AACH;;AACDwI,QAAAA,gBAAgB,CAAC3J,IAAD,EAAaC,UAAb,EAAsC;AAClD,eAAK1D,SAAL,CAAeqN,YAAf,CAA4B5J,IAAI,CAACkF,EAAjC;AACH;;AAED2E,QAAAA,kBAAkB,CAACC,EAAD,EAAoBC,EAApB,EAAkC;AAAA;;AAChD,mCAAK3N,UAAL,8BAAiB4N,QAAjB,CAA0BF,EAA1B,EAA8BC,EAA9B;AACH;;AACDE,QAAAA,oBAAoB,CAACH,EAAD,EAAoBC,EAApB,EAAkC;AAAA;;AAClD,oCAAK3N,UAAL,+BAAiB8N,UAAjB,CAA4BJ,EAA5B,EAAgCC,EAAhC;AACH;;AACDjM,QAAAA,iBAAiB,CAACgM,EAAD,EAAsC;AAAA;;AAAA,4CAAfK,MAAe;AAAfA,YAAAA,MAAe;AAAA;;AACnD,oCAAK/N,UAAL,+BAAiBgO,OAAjB,CAAyBN,EAAzB,EAA6B,GAAGK,MAAhC;AACH;;AAEDE,QAAAA,YAAY,CAACC,KAAD,EAAgB,CACxB;AACH,SA9byC,CAgc1C;AACA;AACA;;;AACOC,QAAAA,SAAS,GAAmB;AAC/B;AACA,iBAAO,IAAP;AACH,SAtcyC,CAwc1C;;;AACAhB,QAAAA,UAAU,CAACiB,GAAD,EAAc,CACpB;AACH,SA3cyC,CA6c1C;;;AACkB,YAAdC,cAAc,GAAU;AAAC,iBAAO,CAAP;AAAS,SA9cI,CA+c1C;;;AACgB,YAAZC,YAAY,GAAU;AAAC,iBAAO,CAAP;AAAS,SAhdM,CAid1C;;;AACkB,YAAdC,cAAc,GAAU;AAAC,iBAAO,CAAP;AAAS,SAldI,CAmd1C;;;AACgB,YAAZC,YAAY,GAAU;AAAC,iBAAO,CAAP;AAAS,SApdM,CAqd1C;;;AACc,YAAVC,UAAU,GAAU;AAAC,iBAAO,CAAP;AAAS;;AAtdQ,O;;;;;iBAKpB,I;;;;;;;iBAEC,I;;;;;;;iBAEM,I;;;;;;;iBAEN,I", "sourcesContent": ["import { _decorator, Label, math, Node, Sprite, tween, Tween } from 'cc';\r\nconst { ccclass, property } = _decorator;\r\n\r\nimport { DamageType, EffectParam, EffectType } from 'db://assets/bundles/common/script/autogen/luban/schema';\r\nimport { AttributeConst } from 'db://assets/bundles/common/script/const/AttributeConst';\r\nimport { AttributeData } from 'db://assets/bundles/common/script/data/base/AttributeData';\r\nimport { GameEnum } from 'db://assets/bundles/common/script/game/const/GameEnum';\r\n\r\nimport Entity from 'db://assets/bundles/common/script/game/ui/base/Entity';\r\n\r\nimport FBoxCollider from 'db://assets/bundles/common/script/game/collider-system/FBoxCollider';\r\nimport FCircleCollider from 'db://assets/bundles/common/script/game/collider-system/FCircleCollider';\r\nimport FPolygonCollider from 'db://assets/bundles/common/script/game/collider-system/FPolygonCollider';\r\n\r\nimport {BuffComp} from './skill/BuffComp';\r\nimport SkillComp from './skill/SkillComp';\r\nimport forEachEntityByTargetType from './skill/SearchTarget';\r\nimport { GameIns } from 'db://assets/bundles/common/script/game/GameIns';\r\nimport { PlaneEventType } from './event/PlaneEventType';\r\nimport PlaneEventComp from './event/PlaneEventComp';\r\nimport { Buff } from './skill/Buff';\r\n\r\n@ccclass('PlaneBase')\r\nexport default class PlaneBase extends Entity {\r\n    constructor() {\r\n        super();\r\n    }\r\n    @property(Node)\r\n    hpNode: Node | null = null;\r\n    @property(Sprite)\r\n    hpBar: Sprite | null = null; // 血条\r\n    @property(Sprite)\r\n    hpAniSprite: Sprite | null = null; // 血条动画条\r\n    @property(Label)\r\n    hpfont: Label | null = null; // 血条文本\r\n\r\n    enemy = true; // 是否为敌机\r\n    type = 0; // 敌人类型\r\n    bDamageable: boolean = true; // 是否可以被造成伤害\r\n\r\n    get maxHp(): number{\r\n        return  this.attribute.getMaxHP();\r\n    };\r\n    curHp: number = 0;\r\n    hurtTime:number = 0;\r\n\r\n    collideComp: FCircleCollider | FBoxCollider | FPolygonCollider | null = null; // 碰撞组件\r\n\r\n    private _skillComp: SkillComp | null = null;\r\n    private _buffComp: BuffComp | null = null;\r\n    private _eventComp: PlaneEventComp | null = null;\r\n\r\n    // TODO 临时做法，后续应该挪到 PlaneBase\r\n    private _attributeData: AttributeData = new AttributeData();\r\n\r\n    init() {\r\n        this._skillComp = new SkillComp();\r\n        this.addComp(\"skill\", this._skillComp);\r\n        this._buffComp = new BuffComp();\r\n        this.addComp(\"buff\", this._buffComp)\r\n        this._eventComp = new PlaneEventComp();\r\n        this.addComp(\"event\", this._eventComp)\r\n        super.init();\r\n    }\r\n\r\n    get skillComp() {\r\n        return this._skillComp!;\r\n    }\r\n\r\n    get buffComp() {\r\n        return this._buffComp!;\r\n    }\r\n\r\n    get attribute(): AttributeData {\r\n        return this._attributeData;\r\n    }\r\n\r\n    set colliderEnabled(value: boolean) {\r\n        if (this.collideComp) {\r\n            this.collideComp.isEnable = value;\r\n        }\r\n    }\r\n    get colliderEnabled(): boolean {\r\n        return this.collideComp ? this.collideComp.isEnable : false;\r\n    }\r\n\r\n    CastSkill(skillID: number) {\r\n        this.skillComp.Cast(this, skillID);\r\n    }\r\n\r\n    addHp(heal: number) {\r\n        this.curHp = Math.min(\r\n            this.maxHp,\r\n            this.curHp + heal\r\n        );\r\n        this.updateHpUI();\r\n    }\r\n\r\n    hurt(damage: number) {\r\n        if (this.isDead) {\r\n            return;\r\n        }\r\n        this.hurtTime = GameIns.gameDataManager.gameTime;\r\n        this.cutHp(damage);\r\n        if (this.curHp <= 0) {\r\n            this.PlaneEventTrigger(PlaneEventType.FatalInjuryHurt)\r\n        }\r\n        this.playHurtAnim();\r\n        if (this.curHp <= 0) {\r\n            this.toDie();\r\n        }\r\n    }\r\n\r\n    get collisionLevel() {\r\n        return 0;\r\n    }\r\n    get collisionHurt() {\r\n        return 0;\r\n    }\r\n\r\n    // 撞机\r\n    collisionPlane(plane: PlaneBase) {\r\n        if (this.isDead || plane.isDead) {\r\n            return;\r\n        }\r\n        if (this.collisionLevel > plane.collisionLevel) {\r\n            return\r\n        }\r\n        let hurt:number\r\n        if (this.collisionLevel < plane.collisionLevel) {\r\n            hurt = Math.max(this.maxHp, plane.collisionHurt)\r\n        } else {\r\n            hurt = plane.collisionHurt == -1 ? this.maxHp : plane.collisionHurt\r\n        }\r\n\r\n        hurt = (hurt - this.attribute.getFinialAttributeByOutInKey(\r\n                AttributeConst.CollisionHurtResistanceOutAdd, AttributeConst.CollisionHurtResistanceOutPer, \r\n                AttributeConst.CollisionHurtResistanceInAdd, AttributeConst.CollisionHurtResistanceInPer))\r\n            * (1 - this.attribute.getFinalAttributeByKey(AttributeConst.CollisionHurtDerateOut))\r\n            * (1 - this.attribute.getFinalAttributeByKey(AttributeConst.CollisionHurtDerateIn));\r\n        this.hurt(hurt);\r\n    }\r\n\r\n    /**\r\n     * 减少血量\r\n     * @param {number} damage 受到的伤害值\r\n     */\r\n    cutHp(damage: number) {\r\n        const newHp = this.curHp - damage;\r\n        this.curHp = Math.max(0, newHp);\r\n\r\n        this.updateHpUI();\r\n    }\r\n\r\n    toDie(destroyType: GameEnum.EnemyDestroyType = GameEnum.EnemyDestroyType.Die): boolean {\r\n        if (this.isDead) {\r\n            return false\r\n        }\r\n\r\n        this.PlaneEventTrigger(PlaneEventType.Die, this);\r\n        this.isDead = true;\r\n        this.colliderEnabled = false;\r\n        return true\r\n    }\r\n    /**\r\n     * 更新血量显示\r\n     */\r\n    updateHpUI() {\r\n        if (this.hpBar) {\r\n            // 更新血条前景的填充范围\r\n            this.hpBar.fillRange = this.curHp / this.maxHp;\r\n\r\n            if (this.hpAniSprite) {\r\n                // 计算血条动画时间\r\n                const duration = Math.abs(this.hpAniSprite.fillRange - this.hpBar.fillRange);\r\n\r\n                Tween.stopAllByTarget(this.hpAniSprite);\r\n                // 血条中间部分的动画\r\n                tween(this.hpAniSprite)\r\n                    .to(duration, { fillRange: this.hpBar.fillRange })\r\n                    .call(() => {\r\n\r\n                    })\r\n                    .start();\r\n            }\r\n        }\r\n\r\n        // 更新血量文字\r\n        this.hpfont && (this.hpfont!.string = this.curHp.toFixed(0));\r\n    }\r\n\r\n    playHurtAnim() {\r\n        // 子类实现\r\n    }\r\n\r\n    ApplyBuffEffect(buff: Buff | null, effectData: EffectParam) {\r\n        switch (effectData.type) {\r\n            case EffectType.AttrMaxHPPer:\r\n                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.MaxHPOutPer, AttributeConst.MaxHPInPer, effectData);\r\n                break;\r\n            case EffectType.AttrMaxHPAdd:\r\n                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.MaxHPOutAdd, AttributeConst.MaxHPInAdd, effectData);\r\n                break;\r\n            case EffectType.AttrHPRecoveryPer:\r\n                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.HPRecoveryOutPer, AttributeConst.HPRecoveryInPer, effectData);\r\n                break;\r\n            case EffectType.AttrHPRecoveryAdd:\r\n                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.HPRecoveryOutAdd, AttributeConst.HPRecoveryInAdd, effectData);\r\n                break;\r\n            case EffectType.AttrHPRecoveryMaxHPPerAdd:\r\n                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.MaxHPRecoveryRateOut, AttributeConst.MaxHPRecoveryRateIn, effectData);\r\n                break;\r\n            case EffectType.HealMaxHPPer:\r\n                if (effectData.param.length >= 1) {\r\n                    this.addHp(Math.floor(this.maxHp * effectData.param[0]/10000));\r\n                }\r\n                break;\r\n            case EffectType.HealLoseHPPer:\r\n                if (effectData.param.length >= 1) {\r\n                    this.addHp(Math.floor((this.maxHp - this.curHp) * effectData.param[0]/10000));\r\n                }\r\n                break;\r\n            case EffectType.HealHP:\r\n                if (effectData.param.length >= 1) {\r\n                    this.addHp(effectData.param[0]);\r\n                }\r\n                break;\r\n            case EffectType.AttrAttackPer:\r\n                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.AttackOutPer, AttributeConst.AttackInPer, effectData);\r\n                break;\r\n            case EffectType.AttrAttackAdd:\r\n                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.AttackOutAdd, AttributeConst.AttackInAdd, effectData);\r\n                break;\r\n            case EffectType.AttrAttackBossPer:\r\n                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.BossHurtBonusOut, AttributeConst.BossHurtBonusIn, effectData);\r\n                break;\r\n            case EffectType.AttrAttackNormalPer:\r\n                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.NormalHurtBonusOut, AttributeConst.NormalHurtBonusIn, effectData);\r\n                break;\r\n            case EffectType.AttrFortunatePer:\r\n                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.FortunateOutPer, AttributeConst.FortunateInPer, effectData);\r\n                break;\r\n            case EffectType.AttrFortunateAdd:\r\n                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.FortunateOutAdd, AttributeConst.FortunateInAdd, effectData);\r\n                break;\r\n            case EffectType.AttrMissAdd:\r\n                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.MissRateOut, AttributeConst.MissRateIn, effectData);\r\n                break;\r\n            case EffectType.AttrBulletHurtResistancePer:\r\n                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.BulletHurtResistanceOutPer, AttributeConst.BulletHurtResistanceInPer, effectData);\r\n                break;\r\n            case EffectType.AttrBulletHurtResistanceAdd:\r\n                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.BulletHurtResistanceOutAdd, AttributeConst.BulletHurtResistanceInAdd, effectData);\r\n                break;\r\n            case EffectType.AttrBulletHurtDerateAdd:\r\n                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.BulletHurtDerateOut, AttributeConst.BulletHurtDerateIn, effectData);\r\n                break;\r\n            case EffectType.AttrCollisionHurtResistancePer:\r\n                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.CollisionHurtResistanceOutPer, AttributeConst.CollisionHurtResistanceInPer, effectData);\r\n                break;\r\n            case EffectType.AttrCollisionHurtResistanceAdd:\r\n                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.CollisionHurtResistanceOutAdd, AttributeConst.CollisionHurtResistanceInAdd, effectData);\r\n                break;\r\n            case EffectType.AttrCollisionHurtDerateAdd:\r\n                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.CollisionHurtDerateOut, AttributeConst.CollisionHurtDerateIn, effectData);\r\n                break;\r\n            case EffectType.AttrFinalScoreAdd:\r\n                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.FinalScoreRateOut, AttributeConst.FinalScoreRateIn, effectData);\r\n                break;\r\n            case EffectType.AttrKillScoreAdd:\r\n                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.KillScoreRateOut, AttributeConst.KillScoreRateIn, effectData);\r\n                break;\r\n            case EffectType.AttrEnergyRecoveryPerAdd:\r\n                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.EnergyRecoveryOutPer, AttributeConst.EnergyRecoveryInPer, effectData);\r\n                break;\r\n            case EffectType.AttrEnergyRecoveryAdd:\r\n                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.EnergyRecoveryOutAdd, AttributeConst.EnergyRecoveryInAdd, effectData);\r\n                break;\r\n            case EffectType.AttrPickRadiusPer:\r\n                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.PickRadiusOutPer, AttributeConst.PickRadiusInPer, effectData);\r\n                break;\r\n            case EffectType.AttrPickRadiusAdd:\r\n                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.PickRadiusOutAdd, AttributeConst.PickRadiusInAdd, effectData);\r\n                break;\r\n            case EffectType.ApplyBuff:\r\n                if (effectData.param.length < 2) {\r\n                    return;\r\n                }\r\n                const buffID = effectData.param[0];\r\n                const target = effectData.param[1];\r\n                forEachEntityByTargetType(this, target, (entity) => {\r\n                    entity.buffComp.ApplyBuff(buff?.isOutside||false, buffID);\r\n                })\r\n                break;\r\n            case EffectType.ImmuneBulletHurt:\r\n                if (buff) {\r\n                    this.attribute.addModify(buff.id, AttributeConst.StatusImmuneBulletHurt, 1);\r\n                }\r\n                break;\r\n            case EffectType.ImmuneCollisionHurt:\r\n                if (buff) {\r\n                    this.attribute.addModify(buff.id, AttributeConst.StatusImmuneCollisionHurt, 1);\r\n                }\r\n                break;\r\n            case EffectType.IgnoreBullet:\r\n                if (buff) {\r\n                    this.attribute.addModify(buff.id, AttributeConst.StatusIgnoreBullet, 1);\r\n                }\r\n                break;\r\n            case EffectType.IgnoreCollision:\r\n                if (buff) {\r\n                    this.attribute.addModify(buff.id, AttributeConst.StatusIgnoreCollision, 1);\r\n                }\r\n                break;\r\n            case EffectType.ImmuneNuclearHurt:\r\n                if (buff) {\r\n                    this.attribute.addModify(buff.id, AttributeConst.StatusImmuneNuclearHurt, 1);\r\n                }\r\n                break;\r\n            case EffectType.ImmuneActiveSkillHurt:\r\n                if (buff) {\r\n                    this.attribute.addModify(buff.id, AttributeConst.StatusImmuneActiveSkillHurt, 1);\r\n                }\r\n                break;\r\n            case EffectType.Invincible:\r\n                if (buff) {\r\n                    this.attribute.addModify(buff.id, AttributeConst.StatusInvincible, 1);\r\n                }\r\n                break;\r\n            case EffectType.AttrNuclearMax:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.NuclearMax, effectData);\r\n                break;\r\n            case EffectType.AttrBulletAttackAdd:\r\n                let damageType = DamageType.ALL;\r\n                if (effectData.param.length > 1) {\r\n                    damageType = effectData.param[1];\r\n                }\r\n                switch(damageType) {\r\n                    case DamageType.ALL:\r\n                        this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.BulletAttackOutAdd, AttributeConst.BulletAttackInAdd, effectData);\r\n                        break;\r\n                    case DamageType.EXPLOSIVE:\r\n                        this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.ExplosiveBulletAttackOutAdd, AttributeConst.ExplosiveBulletAttackInAdd, effectData);\r\n                        break;\r\n                    case DamageType.NORMAL:\r\n                        this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.NormalBulletAttackOutAdd, AttributeConst.NormalBulletAttackInAdd, effectData);\r\n                        break;\r\n                    case DamageType.ENERGETIC:\r\n                        this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.EnergeticBulletAttackOutAdd, AttributeConst.EnergeticBulletAttackInAdd, effectData);\r\n                        break;\r\n                    case DamageType.PHYSICAL:\r\n                        this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.PhysicsBulletAttackOutAdd, AttributeConst.PhysicsBulletAttackInAdd, effectData);\r\n                        break;\r\n                }\r\n            case EffectType.AttrBulletAttackPer:\r\n                damageType = DamageType.ALL;\r\n                if (effectData.param.length > 1) {\r\n                    damageType = effectData.param[1];\r\n                }\r\n                switch(damageType) {\r\n                    case DamageType.ALL:\r\n                        this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.BulletAttackOutPer, AttributeConst.BulletAttackInPer, effectData);\r\n                        break;\r\n                    case DamageType.EXPLOSIVE:\r\n                        this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.ExplosiveBulletAttackOutPer, AttributeConst.ExplosiveBulletAttackInPer, effectData);\r\n                        break;\r\n                    case DamageType.NORMAL:\r\n                        this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.NormalBulletAttackOutPer, AttributeConst.NormalBulletAttackInPer, effectData);\r\n                        break;\r\n                    case DamageType.ENERGETIC:\r\n                        this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.EnergeticBulletAttackOutPer, AttributeConst.EnergeticBulletAttackInPer, effectData);\r\n                        break;\r\n                    case DamageType.PHYSICAL:\r\n                        this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.PhysicsBulletAttackOutPer, AttributeConst.PhysicsBulletAttackInPer, effectData);\r\n                        break;\r\n                }\r\n                break;\r\n            case EffectType.AttrBulletHurtFix:\r\n                damageType = DamageType.ALL;\r\n                if (effectData.param.length > 1) {\r\n                    damageType = effectData.param[1];\r\n                }\r\n                switch(damageType) {\r\n                    case DamageType.ALL:\r\n                        this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.BulletHurtFixOut, AttributeConst.BulletHurtFixIn, effectData);\r\n                        break;\r\n                    case DamageType.EXPLOSIVE:\r\n                        this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.ExplosiveBulletHurtFixOut, AttributeConst.ExplosiveBulletHurtFixIn, effectData);\r\n                        break;\r\n                    case DamageType.NORMAL:\r\n                        this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.NormalBulletHurtFixOut, AttributeConst.NormalBulletHurtFixIn, effectData);\r\n                        break;\r\n                    case DamageType.ENERGETIC:\r\n                        this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.EnergeticBulletHurtFixOut, AttributeConst.EnergeticBulletHurtFixIn, effectData);\r\n                        break;\r\n                    case DamageType.PHYSICAL:\r\n                        this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.PhysicsBulletHurtFixOut, AttributeConst.PhysicsBulletHurtFixIn, effectData);\r\n                        break;\r\n                }\r\n                break;\r\n            case EffectType.HurtMaxHPPer:\r\n                if (effectData.param.length < 1) {\r\n                    return;\r\n                }\r\n                this.hurt(this.maxHp * effectData.param[0]);\r\n                break;\r\n            case EffectType.HurtCurHPPer:\r\n                if (effectData.param.length < 1) {\r\n                    return;\r\n                }\r\n                this.hurt(this.curHp * effectData.param[0]);\r\n                break;\r\n            case EffectType.AttrNuclearAttackPer:\r\n                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.NuclearAttackOutPer, AttributeConst.NuclearAttackInPer, effectData);\r\n                break;\r\n            case EffectType.AttrNuclearAttackAdd:\r\n                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.NuclearAttackOutAdd, AttributeConst.NuclearAttackInAdd, effectData);\r\n                break;\r\n            case EffectType.FireBullet:\r\n                // TODO not implement\r\n                break;\r\n            case EffectType.AttrNuclearHurtFix:\r\n                this.ApplyBuffAttributeOutInEffect(buff, AttributeConst.NuclearHurtFixOut, AttributeConst.NuclearHurtFixIn, effectData);\r\n                break;\r\n            case EffectType.AddNuclear:\r\n                if (effectData.param.length > 0) {\r\n                    this.addNuclear(effectData.param[0])\r\n                }\r\n                break;\r\n            default:\r\n                break;\r\n        }\r\n    }\r\n\r\n    private ApplyBuffAttributeOutInEffect(buff: Buff | null, inKey: AttributeConst, outKey:AttributeConst, effectData: EffectParam) {\r\n        if (!buff) {\r\n            return;\r\n        }\r\n        if (buff.isOutside) {\r\n            this.ApplyBuffAttributeEffect(buff, outKey, effectData);\r\n        } else {\r\n            this.ApplyBuffAttributeEffect(buff, inKey, effectData);\r\n        }\r\n    }\r\n    private ApplyBuffAttributeEffect(buff: Buff | null, key: number, effectData: EffectParam) {\r\n        if (!buff) {\r\n            return;\r\n        }\r\n        if (effectData.param.length < 1) {\r\n            return;\r\n        }\r\n        this.attribute.addModify(buff.id, key, effectData.param[0]);\r\n    }\r\n    RemoveBuffEffect(buff: Buff, effectData: EffectParam) {\r\n        this.attribute.removeModify(buff.id);\r\n    }\r\n\r\n    PlaneEventRegister(et:PlaneEventType, cb: Function) {\r\n        this._eventComp?.Register(et, cb);\r\n    }\r\n    PlaneEventUnRegister(et:PlaneEventType, cb: Function) {\r\n        this._eventComp?.UnRegister(et, cb);\r\n    }\r\n    PlaneEventTrigger(et:PlaneEventType, ...params: any[]) {\r\n        this._eventComp?.Trigger(et, ...params);\r\n    }\r\n\r\n    setAnimSpeed(speed: number) {\r\n        // 子类实现\r\n    }\r\n\r\n    // 获取当前的攻击目标\r\n    // 对于敌机，返回玩家飞机；\r\n    // 对于玩家飞机，可以按策划规则（距离或者其他规则）\r\n    public getTarget(): PlaneBase|null {\r\n        // 子类实现\r\n        return null;\r\n    }\r\n\r\n    // 增加核弹\r\n    addNuclear(num: number) {\r\n        // 子类实现\r\n    }\r\n\r\n    // 获取已拾取宝石数量\r\n    get pickDiamondNum():number {return 0}\r\n    // 获取已击杀敌机数量\r\n    get killEnemyNum():number {return 0}\r\n    // 获取已使用核弹数量\r\n    get usedNuclearNum():number {return 0}\r\n    // 获取已使用大招数量\r\n    get usedSuperNum():number {return 0}\r\n    // 获取当前核弹数量\r\n    get nuclearNum():number {return 0}\r\n}"]}