{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/GlobalDataManager.ts"], "names": ["_decorator", "ResolutionPolicy", "view", "GameConst", "IMgr", "ccclass", "property", "GlobalDataManager", "_chapterID", "chapterID", "value", "init", "setUIResolution", "fitType", "SHOW_ALL", "getVisibleSize", "height", "width", "designWidth", "designHeight", "FIXED_WIDTH", "setResolutionPolicy", "resizeWithBrowserSize", "setBattleResolution"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,gB,OAAAA,gB;AAAkBC,MAAAA,I,OAAAA,I;;AAC9BC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,I,iBAAAA,I;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBN,U;;mCAEjBO,iB,WADZF,OAAO,CAAC,mBAAD,C,gBAAR,MACaE,iBADb;AAAA;AAAA,wBAC4C;AAAA;AAAA;AAAA,eAEhCC,UAFgC,GAEX,CAFW;AAAA;;AAI3B,YAATC,SAAS,GAAW;AACpB,iBAAO,KAAKD,UAAZ;AACH;;AAEY,YAATC,SAAS,CAACC,KAAD,EAAgB;AACzB,eAAKF,UAAL,GAAkBE,KAAlB;AACH;;AAEDC,QAAAA,IAAI,GAAS;AACT,eAAKC,eAAL;AACH;;AAEDA,QAAAA,eAAe,GAAG;AACd,cAAIC,OAAO,GAAGZ,gBAAgB,CAACa,QAA/B;;AACA,cAAIZ,IAAI,CAACa,cAAL,GAAsBC,MAAtB,GAA+Bd,IAAI,CAACa,cAAL,GAAsBE,KAArD,GAA6D;AAAA;AAAA,sCAAUC,WAAvE,IAAsF;AAAA;AAAA,sCAAUC,YAApG,EAAkH;AAAC;AAC/GN,YAAAA,OAAO,GAAGZ,gBAAgB,CAACmB,WAA3B,CAD8G,CACxE;AACzC,WAFD,MAEO;AAAC;AACJP,YAAAA,OAAO,GAAGZ,gBAAgB,CAACa,QAA3B,CADG,CACgC;AACtC;;AACDZ,UAAAA,IAAI,CAACmB,mBAAL,CAAyBR,OAAzB;AACAX,UAAAA,IAAI,CAACoB,qBAAL,CAA2B,IAA3B;AACH;;AAEDC,QAAAA,mBAAmB,GAAG;AAClB;AACA,cAAIV,OAAO,GAAGZ,gBAAgB,CAACa,QAA/B,CAFkB,CAGlB;AACA;AACA;AACA;AACA;;AACAZ,UAAAA,IAAI,CAACmB,mBAAL,CAAyBR,OAAzB;AACAX,UAAAA,IAAI,CAACoB,qBAAL,CAA2B,IAA3B;AACH;;AArCuC,O", "sourcesContent": ["import { _decorator, ResolutionPolicy, view } from \"cc\";\r\nimport { GameConst } from \"db://assets/scripts/core/base/GameConst\";\r\nimport { IMgr } from \"../../../../../scripts/core/base/IMgr\";\r\nconst { ccclass, property } = _decorator;\r\n@ccclass(\"GlobalDataManager\")\r\nexport class GlobalDataManager extends IMgr {\r\n\r\n    private _chapterID: number = 0;\r\n\r\n    get chapterID(): number {\r\n        return this._chapterID;\r\n    }\r\n\r\n    set chapterID(value: number) {\r\n        this._chapterID = value;\r\n    }\r\n\r\n    init(): void {\r\n        this.setUIResolution();\r\n    }\r\n\r\n    setUIResolution() {\r\n        let fitType = ResolutionPolicy.SHOW_ALL\r\n        if (view.getVisibleSize().height / view.getVisibleSize().width * GameConst.designWidth >= GameConst.designHeight) {//高度大于 16:9\r\n            fitType = ResolutionPolicy.FIXED_WIDTH//宽度全部显示，高度做适配拉长\r\n        } else {//宽屏,比如平板或者电脑\r\n            fitType = ResolutionPolicy.SHOW_ALL//两边黑边       //FIXED_HEIGHT// 全部铺满\r\n        }\r\n        view.setResolutionPolicy(fitType);\r\n        view.resizeWithBrowserSize(true);\r\n    }\r\n\r\n    setBattleResolution() {\r\n        // 设置分辨率适配策略\r\n        let fitType = ResolutionPolicy.SHOW_ALL\r\n        // if (view.getVisibleSize().height / view.getVisibleSize().width * GameConst.designWidth >= GameConst.designHeight){//高度大于 16:9\r\n        //     fitType = ResolutionPolicy.SHOW_ALL\r\n        // }else{//宽屏,比如平板或者电脑\r\n        //     fitType = ResolutionPolicy.FIXED_HEIGHT\r\n        // }\r\n        view.setResolutionPolicy(fitType);\r\n        view.resizeWithBrowserSize(true);\r\n    }\r\n\r\n}"]}