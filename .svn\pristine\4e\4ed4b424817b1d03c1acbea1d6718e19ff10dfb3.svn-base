import { _decorator, instantiate, Prefab } from "cc";
import { MyApp } from 'db://assets/bundles/common/script/app/MyApp';
import { Wave } from "../../game/wave/Wave";
import { GameIns } from "../../game/GameIns";
import EventManager from "db://assets/bundles/common/script/event/EventManager";
import { GameEvent } from "db://assets/bundles/common/script/game/event/GameEvent";
import { LevelDataEventTrigger, LevelDataEventTriggerType } from "./LevelDataEventTrigger";

export class LevelDataEventWaveGroup {
    public waveUUID: string[] = [];
    public weight: number = 50;
}

export class LevelDataEventTriggerWave extends LevelDataEventTrigger {
    public waveGroup: LevelDataEventWaveGroup[] = [];
    private _isTriggered: boolean = false;
    private _selectedWaveGroup: LevelDataEventWaveGroup | null = null;
    
    constructor() {
        super(LevelDataEventTriggerType.Wave);
    }
    
    public onInit() {
        // 提前创建好wave，但不执行
        if (this.waveGroup.length > 0) {
            let totalWeight = 0;
            this.waveGroup.forEach(waveGroup => {
                totalWeight += waveGroup.weight;
            });

            let randomWeight = Math.floor(GameIns.battleManager.random() * totalWeight);
            let curWeight = 0;
            
            for (let waveGroup of this.waveGroup) {
                curWeight += waveGroup.weight;
                if (randomWeight <= curWeight) {
                    this._selectedWaveGroup = waveGroup;
                    break;
                }
            }
        }
    }

    public onTrigger(x: number, y: number) {
        this._selectedWaveGroup!.waveUUID.forEach((waveUUID) => {
            const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, waveUUID)
            MyApp.resMgr.load(path, (err: Error | null, prefab: Prefab) => {
                if (err) {
                    console.error('LevelDataEventTriggerWave', " onInit load wave prefab err", err);
                    return;
                }
                const waveComp = instantiate(prefab).getComponent(Wave);
                GameIns.waveManager.addWaveByLevel(waveComp!, x, y);
            });
        });

        this._isTriggered = true;
    }

    public isWaveSpawned(): boolean {
        if (!this._isTriggered) return false;

        this._selectedWaveGroup!.waveUUID.forEach((waveUUID) => {
            if (!GameIns.waveManager.isWaveSpawnCompleted(waveUUID)) {
                return false;
            }
        });

        return true;
    }

    public isWaveCleared(): boolean {
        if (!this._isTriggered) return false;

        this._selectedWaveGroup!.waveUUID.forEach((waveUUID) => {
            if (!GameIns.waveManager.isWaveEnemyDefeated(waveUUID)) {
                return false;
            }
        });

        return true;
    }

    public isWaveTriggered(): boolean {
        return this._isTriggered;
    }

    public fromJSON(obj: any): void {
        super.fromJSON(obj);
    }

    public toJSON(): any {
        // avoid private properties
        return {
            _type: this._type,
            waveGroup: this.waveGroup,
        };
    }
}

