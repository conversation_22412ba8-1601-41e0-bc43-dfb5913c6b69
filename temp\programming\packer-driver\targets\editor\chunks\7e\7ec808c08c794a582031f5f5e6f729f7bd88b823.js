System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, PlaneData, PlaneCacheInfo, _crd;

  function _reportPossibleCrUseOfIData(extras) {
    _reporterNs.report("IData", "db://assets/bundles/common/script/data/DataManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneData(extras) {
    _reporterNs.report("PlaneData", "./PlaneData", _context.meta, extras);
  }

  _export("PlaneCacheInfo", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      PlaneData = _unresolved_2.PlaneData;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "09c979s2npD7Jr9aMgf/Uw+", "PlaneCacheInfo", undefined);

      _export("PlaneCacheInfo", PlaneCacheInfo = class PlaneCacheInfo {
        constructor() {
          this._isInit = false;
          this._planeDataDic = {};
        }

        //飞机数据
        init() {
          this._isInit = true;
          this.onNetAllPlaneInfo();
        }

        update() {}

        onNetAllPlaneInfo() {
          let serverPlaneList = [{
            planeId: 10003101
          }];

          for (let data of serverPlaneList) {
            let planeData = new (_crd && PlaneData === void 0 ? (_reportPossibleCrUseOfPlaneData({
              error: Error()
            }), PlaneData) : PlaneData)();
            planeData.planeId = data.planeId;
            this._planeDataDic[planeData.planeId] = planeData;
          }
        }

        getPlaneInfoById(id = 10003101) {
          if (!this._isInit) {
            this.init();
          }

          return this._planeDataDic[id];
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=7ec808c08c794a582031f5f5e6f729f7bd88b823.js.map