{"version": 3, "sources": ["file:///E:/M2Game/Client/extensions/i18n/assets/TTFUtils.ts"], "names": ["TTFUtils", "Enum", "resources", "warn", "customLabelUrl", "customLabel", "isLoading", "labelList", "getInstance", "instance", "init", "type", "url", "setCustomTTF", "label", "labFont", "font", "loadCustomTTF", "push", "load", "err", "asset", "loadLab", "i", "length", "CUSTOM_TYPE", "NONE", "TEXT", "NUMBER"], "mappings": ";;;yFASaA,Q;;;;;;;;;AATJC,MAAAA,I,OAAAA,I;AAAmBC,MAAAA,S,OAAAA,S;AAAqBC,MAAAA,I,OAAAA,I;;;;;;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA;;;0BACaH,Q,GAAN,MAAMA,QAAN,CAAe;AAAA;AAAA,eASlBI,cATkB,GASqB,EATrB;AAAA,eAUlBC,WAVkB,GAUgB,EAVhB;AAAA,eAWlBC,SAXkB,GAWiB,EAXjB;AAAA,eAYlBC,SAZkB,GAY8B,EAZ9B;AAAA;;AAeA,eAAXC,WAAW,GAAE;AAChB,cAAG,CAACR,QAAQ,CAACS,QAAb,EAAsB;AAClBT,YAAAA,QAAQ,CAACS,QAAT,GAAoB,IAAIT,QAAJ,EAApB;AACH;;AAED,iBAAOA,QAAQ,CAACS,QAAhB;AACH;;AAED;AACJ;AACA;AACIC,QAAAA,IAAI,CAACC,IAAD,EAAcC,GAAd,EAAyB;AACzB,eAAKR,cAAL,CAAoBO,IAApB,IAA4BC,GAA5B;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,YAAY,CAACC,KAAD,EAAyBH,IAAzB,EAAsC;AAC9C,cAAII,OAAO,GAAG,KAAKV,WAAL,CAAiBM,IAAjB,CAAd;;AACA,cAAGI,OAAH,EAAW;AACPD,YAAAA,KAAK,CAACE,IAAN,GAAaD,OAAb;AACA;AACH;;AACD,eAAKE,aAAL,CAAmBH,KAAnB,EAA0BH,IAA1B;AACH;;AAEDM,QAAAA,aAAa,CAACH,KAAD,EAAyBH,IAAzB,EAAsC;AAC/C,cAAG,CAAC,KAAKJ,SAAL,CAAeI,IAAf,CAAJ,EAAyB;AACrB,iBAAKJ,SAAL,CAAeI,IAAf,IAAuB,EAAvB;AACH;;AACD,eAAKJ,SAAL,CAAeI,IAAf,EAAqBO,IAArB,CAA0BJ,KAA1B;;AACA,cAAG,KAAKR,SAAL,CAAeK,IAAf,KAAwB,CAAC,KAAKP,cAAL,CAAoBO,IAApB,CAA5B,EAAsD;AAClD;AACH;;AACD,eAAKL,SAAL,CAAeK,IAAf,IAAuB,IAAvB;AACAT,UAAAA,SAAS,CAACiB,IAAV,CAAe,KAAKf,cAAL,CAAoBO,IAApB,CAAf,EAA0C,CAACS,GAAD,EAAMC,KAAN,KAAgB;AACtD,iBAAKf,SAAL,CAAeK,IAAf,IAAuB,KAAvB;;AACA,gBAAGS,GAAH,EAAO;AACHjB,cAAAA,IAAI,CAAC,QAAD,EAAW,KAAKC,cAAL,CAAoBO,IAApB,CAAX,EAAsCS,GAAtC,CAAJ;AACA;AACH;;AACDjB,YAAAA,IAAI,CAAC,QAAD,EAAWQ,IAAX,EAAiB,KAAKP,cAAL,CAAoBO,IAApB,CAAjB,CAAJ;AACA,iBAAKN,WAAL,CAAiBM,IAAjB,IAAyBU,KAAzB;AACA,gBAAIC,OAAO,GAAG,IAAd;;AACA,iBAAI,IAAIC,CAAC,GAAG,KAAKhB,SAAL,CAAeI,IAAf,EAAqBa,MAArB,GAA4B,CAAxC,EAA2CD,CAAC,IAAI,CAAhD,EAAmDA,CAAC,EAApD,EAAuD;AACnDD,cAAAA,OAAO,GAAG,KAAKf,SAAL,CAAeI,IAAf,EAAqBY,CAArB,CAAV;;AACA,kBAAGD,OAAH,EAAW;AACPA,gBAAAA,OAAO,CAACN,IAAR,GAAeK,KAAf;AACH;AACJ;;AACD,mBAAO,KAAKd,SAAL,CAAeI,IAAf,CAAP;AACH,WAhBD;AAiBH;;AArEiB,O;;AAATX,MAAAA,Q,CACFS,Q;AADET,MAAAA,Q,CAGFyB,W,GAAcxB,IAAI,CAAC;AACtByB,QAAAA,IAAI,EAAE,CADgB;AAEtBC,QAAAA,IAAI,EAAE,CAFgB;AAGtBC,QAAAA,MAAM,EAAE;AAHc,OAAD,C", "sourcesContent": ["import { Enum, Font, Label, resources, RichText, warn } from \"cc\";\r\n\r\n/**\r\n * 字体工具类，兼容不同平台是否能使用自定义TTF字体功能\r\n * （目前只有加载单自定义字体的功能，后续拓展）\r\n * 加载方式：\r\n * 1、TTFUtils.init设置字体\r\n * 2、在Label节点挂载TTFComponent脚本组件\r\n */\r\nexport class TTFUtils {\r\n    static instance:TTFUtils;\r\n\r\n    static CUSTOM_TYPE = Enum({\r\n        NONE: 0,\r\n        TEXT: 1,\r\n        NUMBER: 2,\r\n    });\r\n\r\n    customLabelUrl:{[key:number]:string} = {};\r\n    customLabel:{[key:number]:Font} = {};\r\n    isLoading:{[key:number]:boolean} = {};\r\n    labelList:{[key:number]:(Label | RichText)[]} = {};\r\n\r\n\r\n    static getInstance(){\r\n        if(!TTFUtils.instance){\r\n            TTFUtils.instance = new TTFUtils();\r\n        }\r\n    \r\n        return TTFUtils.instance;\r\n    };\r\n\r\n    /**\r\n     * 设置自定义字体路径\r\n     */\r\n    init(type:number, url:string){\r\n        this.customLabelUrl[type] = url;\r\n    }\r\n\r\n    /**\r\n     * 设置自定义字体\r\n     * @param label type: Label\r\n     */\r\n    setCustomTTF(label:Label | RichText, type:number) {\r\n        let labFont = this.customLabel[type];\r\n        if(labFont){\r\n            label.font = labFont;\r\n            return;\r\n        }\r\n        this.loadCustomTTF(label, type);\r\n    }\r\n\r\n    loadCustomTTF(label:Label | RichText, type:number) {\r\n        if(!this.labelList[type]){\r\n            this.labelList[type] = [];\r\n        }\r\n        this.labelList[type].push(label);\r\n        if(this.isLoading[type] || !this.customLabelUrl[type]){\r\n            return;\r\n        }\r\n        this.isLoading[type] = true;\r\n        resources.load(this.customLabelUrl[type], (err, asset) => {\r\n            this.isLoading[type] = false;\r\n            if(err){\r\n                warn(\"字体加载失败\", this.customLabelUrl[type], err);\r\n                return;\r\n            }\r\n            warn(\"字体加载完成\", type, this.customLabelUrl[type]);\r\n            this.customLabel[type] = asset;\r\n            let loadLab = null;\r\n            for(let i = this.labelList[type].length-1; i >= 0; i--){\r\n                loadLab = this.labelList[type][i];\r\n                if(loadLab){\r\n                    loadLab.font = asset;\r\n                }\r\n            }\r\n            delete this.labelList[type];\r\n        });\r\n    }\r\n}"]}