System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Label, ProgressBar, Sprite, tween, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _crd, ccclass, property, StatisticsHurtCell;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Label = _cc.Label;
      ProgressBar = _cc.ProgressBar;
      Sprite = _cc.Sprite;
      tween = _cc.tween;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "2469323FFpNDIa1MK9Co79n", "StatisticsHurtCell", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Label', 'ProgressBar', 'Sprite', 'tween']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("StatisticsHurtCell", StatisticsHurtCell = (_dec = ccclass('StatisticsHurtCell'), _dec2 = property(Sprite), _dec3 = property(Label), _dec4 = property(ProgressBar), _dec5 = property(Label), _dec6 = property(Label), _dec(_class = (_class2 = class StatisticsHurtCell extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "icon", _descriptor, this);

          _initializerDefineProperty(this, "txtType", _descriptor2, this);

          _initializerDefineProperty(this, "bar", _descriptor3, this);

          _initializerDefineProperty(this, "txt", _descriptor4, this);

          _initializerDefineProperty(this, "txt2", _descriptor5, this);

          this._expTween = void 0;
        }

        // 经验条动画的 tween 引用
        start() {}

        update(deltaTime) {}

        onDestroy() {
          if (this._expTween) {
            this._expTween.stop();

            this._expTween = null;
          }
        }

        setType(stat, totalScore) {
          this.txtType.string = stat.type;
          this.txt2.string = stat.score;
          this.bar.progress = 0;
          this.txt.string = this.bar.progress.toFixed(1) + "%";

          if (totalScore > 0) {
            var gap = 0.5;
            var exp = Number(stat.score) / totalScore;
            this._expTween = tween(this.bar).to(gap, {
              progress: exp
            }, {
              onUpdate: () => {
                if (!this.bar) return;
                this.txt.string = (this.bar.progress * 100).toFixed(1) + "%";
              }
            }).start();
          }
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "icon", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "txtType", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "bar", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "txt", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "txt2", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=69d37b1710fe8ed0da86d97cd25abd62dbfd06fb.js.map