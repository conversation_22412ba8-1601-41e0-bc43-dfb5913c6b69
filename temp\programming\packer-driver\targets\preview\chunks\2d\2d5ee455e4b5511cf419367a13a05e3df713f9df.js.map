{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/DevLoginUI.ts"], "names": ["_decorator", "<PERSON><PERSON>", "EditBox", "MyApp", "logDebug", "csproto", "DevLoginData", "BundleName", "initBundle", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "uiSelect", "ccclass", "property", "DevLoginUI", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Top", "getBundleName", "Common", "onHide", "onShow", "loginButton", "node", "on", "EventType", "CLICK", "onLoginButtonClick", "netMgr", "registerHandler", "cs", "CS_CMD", "CS_CMD_GET_ROLE", "onGetRole", "serverList", "for<PERSON>ach", "value", "key", "serverSelect", "itemDatas", "push", "setChooseItemData", "instance", "servername", "onChooseItem", "itemData", "usernameEditBox", "string", "user", "passwordEditBox", "password", "onClose", "unregister<PERSON><PERSON><PERSON>", "username", "connect", "msg", "closeUI", "needLogin", "Gm"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,O,OAAAA,O;;AACpBC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,Q,iBAAAA,Q;;AACFC,MAAAA,O;;AACEC,MAAAA,Y,iBAAAA,Y;;AAEAC,MAAAA,U,iBAAAA,U;;AAEAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,Q,iBAAAA,Q;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBd,U;;4BAGjBe,U,WADZF,OAAO,CAAC,YAAD,C,UAOHC,QAAQ,CAACb,MAAD,C,UAERa,QAAQ,CAACZ,OAAD,C,UAERY,QAAQ,CAACZ,OAAD,C,UAGRY,QAAQ;AAAA;AAAA,+B,sCAdb,MACaC,UADb;AAAA;AAAA,4BACuC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AACf,eAANC,MAAM,GAAW;AAAE,iBAAO,sBAAP;AAA+B;;AAC1C,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,GAAf;AAAoB;;AAC7B,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,MAAlB;AAA0B;;AAa5DC,QAAAA,MAAM,GAAgC;AAAA;AAC3C;;AAEKC,QAAAA,MAAM,GAAgC;AAAA;;AAAA;AACxC,YAAA,KAAI,CAACC,WAAL,CAAiBC,IAAjB,CAAsBC,EAAtB,CAAyBxB,MAAM,CAACyB,SAAP,CAAiBC,KAA1C,EAAiD,KAAI,CAACC,kBAAtD,EAA0E,KAA1E;;AACA;AAAA;AAAA,gCAAMC,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,oCAAQC,EAAR,CAAWC,MAAX,CAAkBC,eAA/C,EAAgE,KAAI,CAACC,SAArE,EAAgF,KAAhF;AAEA;AAAA;AAAA,8CAAaC,UAAb,CAAwBC,OAAxB,CAAgC,CAACC,KAAD,EAAQC,GAAR,KAAgB;AAC5C,cAAA,KAAI,CAACC,YAAL,CAAkBC,SAAlB,CAA4BC,IAA5B,CAAiCH,GAAjC;;AACA,cAAA,KAAI,CAACC,YAAL,CAAkBC,SAAlB,CAA4BC,IAA5B,CAAiCH,GAAG,GAAG,GAAvC;AACH,aAHD;;AAIA,YAAA,KAAI,CAACC,YAAL,CAAkBG,iBAAlB,CAAoC;AAAA;AAAA,8CAAaC,QAAb,CAAsBC,UAA1D;;AACA,YAAA,KAAI,CAACL,YAAL,CAAkBM,YAAlB,GAAkCC,QAAD,IAAsB;AACnD;AAAA;AAAA,wCAAS,SAAT,qBAAqCA,QAArC;AACA;AAAA;AAAA,gDAAaH,QAAb,CAAsBC,UAAtB,GAAmCE,QAAnC;AACH,aAHD;;AAIA,YAAA,KAAI,CAACC,eAAL,CAAqBC,MAArB,GAA8B;AAAA;AAAA,8CAAaL,QAAb,CAAsBM,IAApD;AACA,YAAA,KAAI,CAACC,eAAL,CAAqBF,MAArB,GAA8B;AAAA;AAAA,8CAAaL,QAAb,CAAsBQ,QAApD;AAdwC;AAe3C;;AAEKC,QAAAA,OAAO,GAAgC;AAAA;;AAAA;AACzC;AAAA;AAAA,gCAAMvB,MAAN,CAAawB,iBAAb,CAA+B;AAAA;AAAA,oCAAQtB,EAAR,CAAWC,MAAX,CAAkBC,eAAjD,EAAkE,MAAI,CAACC,SAAvE,EAAkF,MAAlF;AADyC;AAE5C;;AAEDN,QAAAA,kBAAkB,GAAG;AACjB,cAAI0B,QAAQ,GAAG,KAAKP,eAAL,CAAqBC,MAApC;AACA,cAAIG,QAAQ,GAAG,KAAKD,eAAL,CAAqBF,MAApC;AACA;AAAA;AAAA,4CAAaL,QAAb,CAAsBM,IAAtB,GAA6BK,QAA7B;AACA;AAAA;AAAA,4CAAaX,QAAb,CAAsBQ,QAAtB,GAAiCA,QAAjC;AAEA;AAAA;AAAA,8BAAMtB,MAAN,CAAa0B,OAAb;AACH;;AAEDrB,QAAAA,SAAS,CAACsB,GAAD,EAA0B;AAC/B;AAAA;AAAA,8BAAMC,OAAN,CAAc1C,UAAd;AACAA,UAAAA,UAAU,CAAC2C,SAAX,GAAuB,KAAvB;AACA;AAAA;AAAA,wCAAW;AAAA;AAAA,wCAAWC,EAAtB;AACH;;AArDkC,O,UAI5BD,S,GAAY,I;;;;;iBAGG,I;;;;;;;iBAEK,I;;;;;;;iBAEA,I;;;;;;;iBAGF,I", "sourcesContent": ["import { _decorator, But<PERSON>, EditBox } from 'cc';\nimport { MyApp } from 'db://assets/bundles/common/script/app/MyApp';\nimport { logDebug, logError } from '../../../../../scripts/utils/Logger';\nimport csproto from 'db://assets/bundles/common/script/autogen/pb/cs_proto.js';\nimport { DevLoginData } from '../../platformsdk/DevLoginData';\n\nimport { BundleName } from 'db://assets/bundles/common/script/const/BundleConst';\nimport { DataMgr } from 'db://assets/bundles/common/script/data/DataManager';\nimport { initBundle } from 'db://assets/scripts/core/base/Bundle';\nimport { BaseUI, UILayer, UIMgr } from '../../../../../scripts/core/base/UIMgr';\nimport { uiSelect } from '../common/components/SelectList/uiSelect';\nimport { GameIns } from '../../game/GameIns';\nconst { ccclass, property } = _decorator;\n\n@ccclass('DevLoginUI')\nexport class DevLoginUI extends BaseUI {\n    public static getUrl(): string { return \"prefab/ui/DevLoginUI\" };\n    public static getLayer(): UILayer { return UILayer.Top }\n    public static getBundleName(): string { return BundleName.Common }\n    static needLogin = true;\n\n    @property(Button)\n    loginButton: Button = null!;\n    @property(EditBox)\n    usernameEditBox: EditBox = null!;\n    @property(EditBox)\n    passwordEditBox: EditBox = null!;\n\n    @property(uiSelect)\n    serverSelect: uiSelect = null!;\n\n    async onHide(...args: any[]): Promise<void> {\n    }\n\n    async onShow(...args: any[]): Promise<void> {\n        this.loginButton.node.on(Button.EventType.CLICK, this.onLoginButtonClick, this);\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GET_ROLE, this.onGetRole, this);\n\n        DevLoginData.serverList.forEach((value, key) => {\n            this.serverSelect.itemDatas.push(key)\n            this.serverSelect.itemDatas.push(key + \"1\")\n        });\n        this.serverSelect.setChooseItemData(DevLoginData.instance.servername)\n        this.serverSelect.onChooseItem = (itemData: string) => {\n            logDebug(\"LoginUI\", `choose server ${itemData}`)\n            DevLoginData.instance.servername = itemData;\n        }\n        this.usernameEditBox.string = DevLoginData.instance.user;\n        this.passwordEditBox.string = DevLoginData.instance.password;\n    }\n\n    async onClose(...args: any[]): Promise<void> {\n        MyApp.netMgr.unregisterHandler(csproto.cs.CS_CMD.CS_CMD_GET_ROLE, this.onGetRole, this)\n    }\n\n    onLoginButtonClick() {\n        var username = this.usernameEditBox.string;\n        var password = this.passwordEditBox.string;\n        DevLoginData.instance.user = username;\n        DevLoginData.instance.password = password;\n\n        MyApp.netMgr.connect();\n    }\n\n    onGetRole(msg: csproto.cs.IS2CMsg) {\n        UIMgr.closeUI(DevLoginUI);\n        DevLoginUI.needLogin = false;\n        initBundle(BundleName.Gm);\n    }\n}"]}