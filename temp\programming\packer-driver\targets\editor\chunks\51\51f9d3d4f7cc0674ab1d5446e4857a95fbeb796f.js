System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, _crd, HomeUIBaseSystem;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "baabbmAYolLeYj9jagQtRqp", "HomeUIConst", undefined);

      _export("HomeUIBaseSystem", HomeUIBaseSystem = /*#__PURE__*/function (HomeUIBaseSystem) {
        HomeUIBaseSystem["Social"] = "Social";
        HomeUIBaseSystem["Announcement"] = "Announcement";
        HomeUIBaseSystem["Friend"] = "Friend";
        HomeUIBaseSystem["Mail"] = "Mail";
        HomeUIBaseSystem["Setting"] = "Setting";
        return HomeUIBaseSystem;
      }({}));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=51f9d3d4f7cc0674ab1d5446e4857a95fbeb796f.js.map