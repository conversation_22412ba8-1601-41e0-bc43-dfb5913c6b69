{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/mail/MailUI.ts"], "names": ["_decorator", "BundleName", "ButtonPlus", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "DataMgr", "EventMgr", "HomeUIEvent", "List", "MailCellUI", "DataEvent", "ccclass", "property", "MailUI", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "getBundleName", "HomeMail", "getUIOption", "isClickBgCloseUI", "onLoad", "btnClose", "addClick", "closeUI", "once", "Leave", "onLeave", "on", "MailRefresh", "onMailRefresh", "off", "onShow", "list", "node", "active", "numItems", "mail", "mail_list", "length", "onHide", "onClose", "onDestroy", "start", "onList<PERSON>ender", "listItem", "row", "mailInfo", "getMailByIndex", "undefined", "cell", "getComponent", "setData"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,W,iBAAAA,W;;AACFC,MAAAA,I;;AACEC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,S,kBAAAA,S;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBb,U;;wBAGjBc,M,WADZF,OAAO,CAAC,QAAD,C,UAEHC,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,uB,2BAJb,MACaC,MADb;AAAA;AAAA,4BACmC;AAAA;AAAA;;AAAA;;AAAA;AAAA;;AAKX,eAANC,MAAM,GAAW;AAAE,iBAAO,kBAAP;AAA4B;;AACvC,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,OAAf;AAAwB;;AACjC,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,QAAlB;AAA6B;;AAC5C,eAAXC,WAAW,GAAU;AAAE,iBAAO;AAAEC,YAAAA,gBAAgB,EAAE;AAApB,WAAP;AAAmC;;AAC9DC,QAAAA,MAAM,GAAS;AACrB,eAAKC,QAAL,CAAeC,QAAf,CAAwB,KAAKC,OAA7B,EAAsC,IAAtC;AACA;AAAA;AAAA,oCAASC,IAAT,CAAc;AAAA;AAAA,0CAAYC,KAA1B,EAAiC,KAAKC,OAAtC,EAA+C,IAA/C;AACA;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,sCAAUC,WAAtB,EAAmC,KAAKC,aAAxC,EAAuD,IAAvD;AACH;;AACOH,QAAAA,OAAO,GAAG;AACd;AAAA;AAAA,oCAASI,GAAT,CAAa;AAAA;AAAA,0CAAYL,KAAzB,EAAgC,KAAKC,OAArC,EAA8C,IAA9C;AACA;AAAA;AAAA,8BAAMH,OAAN,CAAcX,MAAd;AACH;;AACOiB,QAAAA,aAAa,GAAG,CACvB;;AACKN,QAAAA,OAAO,GAAG;AAAA;AACZ;AAAA;AAAA,gCAAMA,OAAN,CAAcX,MAAd;AADY;AAEf;;AACKmB,QAAAA,MAAM,GAAkB;AAAA;;AAAA;AAC1B,YAAA,KAAI,CAACC,IAAL,CAAWC,IAAX,CAAgBC,MAAhB,GAAyB,IAAzB;AACA,YAAA,KAAI,CAACF,IAAL,CAAWG,QAAX,GAAsB;AAAA;AAAA,oCAAQC,IAAR,CAAaC,SAAb,CAAuBC,MAA7C;AAF0B;AAG7B;;AACKC,QAAAA,MAAM,GAAkB;AAAA;AAC7B;;AACKC,QAAAA,OAAO,GAAkB;AAAA;AAC9B;;AACSC,QAAAA,SAAS,GAAS,CAC3B;;AACDC,QAAAA,KAAK,GAAG,CAEP;;AACDC,QAAAA,YAAY,CAACC,QAAD,EAAiBC,GAAjB,EAA8B;AAAC;AACvC,cAAMC,QAAQ,GAAG;AAAA;AAAA,kCAAQV,IAAR,CAAaW,cAAb,CAA4BF,GAA5B,CAAjB;;AACA,cAAIC,QAAQ,KAAKE,SAAjB,EAA4B;AACxB;AACH;;AACD,cAAMC,IAAI,GAAGL,QAAQ,CAACM,YAAT;AAAA;AAAA,uCAAb;;AACA,cAAID,IAAI,KAAK,IAAb,EAAmB;AACfA,YAAAA,IAAI,CAACE,OAAL,CAAaL,QAAb;AACH;AACJ;;AA7C8B,O;;;;;iBAED,I;;;;;;;iBAEV,I", "sourcesContent": ["import { _decorator, Node } from 'cc';\r\nimport { BundleName } from 'db://assets/bundles/common/script/const/BundleConst';\r\nimport { ButtonPlus } from 'db://assets/bundles/common/script/ui/common/components/button/ButtonPlus';\r\nimport { BaseUI, UILayer, UIMgr, UIOpt } from 'db://assets/scripts/core/base/UIMgr';\r\nimport { DataMgr } from '../../data/DataManager';\r\nimport { EventMgr } from '../../event/EventManager';\r\nimport { HomeUIEvent } from '../../event/HomeUIEvent';\r\nimport List from '../common/components/list/List';\r\nimport { MailCellUI } from './MailCellUI';\r\nimport { DataEvent } from '../../event/DataEvent';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('MailUI')\r\nexport class MailUI extends BaseUI {\r\n    @property(ButtonPlus)\r\n    btnClose: ButtonPlus | null = null;\r\n    @property(List)\r\n    list: List | null = null;\r\n    public static getUrl(): string { return \"prefab/ui/MailUI\"; }\r\n    public static getLayer(): UILayer { return UILayer.Default }\r\n    public static getBundleName(): string { return BundleName.HomeMail; }\r\n    public static getUIOption(): UIOpt { return { isClickBgCloseUI: true } }\r\n    protected onLoad(): void {\r\n        this.btnClose!.addClick(this.closeUI, this);\r\n        EventMgr.once(HomeUIEvent.Leave, this.onLeave, this);\r\n        EventMgr.on(DataEvent.MailRefresh, this.onMailRefresh, this)\r\n    }\r\n    private onLeave() {\r\n        EventMgr.off(HomeUIEvent.Leave, this.onLeave, this)\r\n        UIMgr.closeUI(MailUI)\r\n    }\r\n    private onMailRefresh() {\r\n    }\r\n    async closeUI() {\r\n        UIMgr.closeUI(MailUI);\r\n    }\r\n    async onShow(): Promise<void> {\r\n        this.list!.node.active = true;\r\n        this.list!.numItems = DataMgr.mail.mail_list.length;\r\n    }\r\n    async onHide(): Promise<void> {\r\n    }\r\n    async onClose(): Promise<void> {\r\n    }\r\n    protected onDestroy(): void {\r\n    }\r\n    start() {\r\n\r\n    }\r\n    onListRender(listItem: Node, row: number) {// 有数据要在 this.list.numItems 之前设置\r\n        const mailInfo = DataMgr.mail.getMailByIndex(row);\r\n        if (mailInfo === undefined) {\r\n            return;\r\n        }\r\n        const cell = listItem.getComponent(MailCellUI);\r\n        if (cell !== null) {\r\n            cell.setData(mailInfo);\r\n        }\r\n    }\r\n}\r\n\r\n\r\n"]}