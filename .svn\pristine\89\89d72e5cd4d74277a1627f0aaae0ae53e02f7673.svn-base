import { _decorator, Node } from 'cc';
import { BundleName } from 'db://assets/bundles/common/script/const/BundleConst';
import { ButtonPlus } from 'db://assets/bundles/common/script/ui/common/components/button/ButtonPlus';
import { BaseUI, UILayer, UIMgr, UIOpt } from 'db://assets/scripts/core/base/UIMgr';
import { DataMgr } from '../../data/DataManager';
import { EventMgr } from '../../event/EventManager';
import { HomeUIEvent } from '../../event/HomeUIEvent';
import List from '../common/components/list/List';
import { MailCellUI } from './MailCellUI';
import { DataEvent } from '../../event/DataEvent';

const { ccclass, property } = _decorator;

@ccclass('MailUI')
export class MailUI extends BaseUI {
    @property(ButtonPlus)
    btnClose: ButtonPlus | null = null;
    @property(List)
    list: List | null = null;
    public static getUrl(): string { return "prefab/ui/MailUI"; }
    public static getLayer(): UILayer { return UILayer.Default }
    public static getBundleName(): string { return BundleName.HomeMail; }
    public static getUIOption(): UIOpt { return { isClickBgCloseUI: true } }
    protected onLoad(): void {
        this.btnClose!.addClick(this.closeUI, this);
        EventMgr.once(HomeUIEvent.Leave, this.onLeave, this);
        EventMgr.on(DataEvent.MailRefresh, this.onMailRefresh, this)
    }
    private onLeave() {
        EventMgr.off(HomeUIEvent.Leave, this.onLeave, this)
        UIMgr.closeUI(MailUI)
    }
    private onMailRefresh() {
    }
    async closeUI() {
        UIMgr.closeUI(MailUI);
    }
    async onShow(): Promise<void> {
        this.list!.node.active = true;
        this.list!.numItems = DataMgr.mail.mail_list.length;
    }
    async onHide(): Promise<void> {
    }
    async onClose(): Promise<void> {
    }
    protected onDestroy(): void {
    }
    start() {

    }
    onListRender(listItem: Node, row: number) {// 有数据要在 this.list.numItems 之前设置
        const mailInfo = DataMgr.mail.getMailByIndex(row);
        if (mailInfo === undefined) {
            return;
        }
        const cell = listItem.getComponent(MailCellUI);
        if (cell !== null) {
            cell.setData(mailInfo);
        }
    }
}


