[{"__type__": "cc.Prefab", "_name": "wave_1", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "wave_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 2}], "_prefab": {"__id__": 20}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "194feuuQpZAtYZElOT4+NCI", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 3}, "waveName": "", "waveData": {"__id__": 4}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "79jIYdhXpCTKOCBcNLg1bY"}, {"__type__": "WaveData", "useFormation": false, "spawnGroups": [{"__id__": 5}, {"__id__": 6}], "formationAsset": {"__uuid__": "30ba4125-5f81-4175-bc13-1a965f6299a3", "__expectedType__": "cc.Json<PERSON>set"}, "pathAsset": {"__uuid__": "830ed543-4e69-4b5e-94ae-ab5aac4713a4", "__expectedType__": "cc.Json<PERSON>set"}, "spawnOrder": 1, "waveCompletion": 1, "waveCompletionParam": {"__id__": 7}, "spawnInterval": {"__id__": 8}, "spawnPosX": {"__id__": 9}, "spawnPosY": {"__id__": 10}, "spawnAngle": {"__id__": 11}, "eventGroupData": [{"__id__": 12}]}, {"__type__": "SpawnGroup", "weight": 50}, {"__type__": "SpawnGroup", "weight": 50}, {"__type__": "ExpressionValue", "type": 2, "value": 1, "expression": "1", "values": [], "serializedProgram": null}, {"__type__": "ExpressionValue", "type": 2, "value": 1000, "expression": "1000", "values": [500, 1000, 1500], "serializedProgram": null}, {"__type__": "ExpressionValue", "type": 2, "value": 0, "expression": "0", "values": [], "serializedProgram": null}, {"__type__": "ExpressionValue", "type": 2, "value": 0, "expression": "0", "values": [], "serializedProgram": null}, {"__type__": "ExpressionValue", "type": 2, "value": 270, "expression": "270", "values": [], "serializedProgram": null}, {"__type__": "WaveEventGroupData", "name": "", "triggerCount": 1, "conditions": [{"__id__": 13}], "actions": [{"__id__": 15}]}, {"__type__": "WaveConditionData", "op": 0, "type": 0, "compareOp": 0, "targetValue": {"__id__": 14}}, {"__type__": "ExpressionValue", "type": 2, "value": 0, "expression": "0", "values": [], "serializedProgram": null}, {"__type__": "WaveActionData", "name": "", "type": 0, "delay": {"__id__": 16}, "duration": {"__id__": 17}, "targetValue": {"__id__": 18}, "targetValueType": 1, "transitionDuration": {"__id__": 19}, "wrapMode": 0, "easing": 0}, {"__type__": "ExpressionValue", "type": 2, "value": 0, "expression": "0", "values": [], "serializedProgram": null}, {"__type__": "ExpressionValue", "type": 2, "value": 0, "expression": "0", "values": [], "serializedProgram": null}, {"__type__": "ExpressionValue", "type": 2, "value": 0, "expression": "0", "values": [], "serializedProgram": null}, {"__type__": "ExpressionValue", "type": 2, "value": 0, "expression": "0", "values": [], "serializedProgram": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": null, "targetOverrides": null}]