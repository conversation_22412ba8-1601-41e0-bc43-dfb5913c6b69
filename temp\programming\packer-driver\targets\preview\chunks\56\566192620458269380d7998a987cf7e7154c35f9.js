System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, misc, v2, Vec2, GameIns, DYTools, _crd, Tools;

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../GameIns", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      misc = _cc.misc;
      v2 = _cc.v2;
      Vec2 = _cc.Vec2;
    }, function (_unresolved_2) {
      GameIns = _unresolved_2.GameIns;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "836a4KfuzNEJoGo3LRAtuST", "Tools", undefined);

      __checkObsolete__(['Component', 'misc', 'Node', 'v2', 'Vec2']);

      DYTools = class DYTools {
        /**
         * 打印日志
         */
        log(message) {
          for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
            args[_key - 1] = arguments[_key];
          }

          console.log(message, ...args);
        }
        /**
         * 打印错误日志
         */


        error(message) {
          for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {
            args[_key2 - 1] = arguments[_key2];
          }

          console.error(message, ...args);
        }
        /**
         * 打印警告日志
         */


        warn(message) {
          for (var _len3 = arguments.length, args = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++) {
            args[_key3 - 1] = arguments[_key3];
          }

          console.warn(message, ...args);
        }
        /**
         * 生成随机整数
         */


        random_int(min, max) {
          var random = Math.floor((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.random() * (max - min + 1)) + min;
          return Math.max(min, Math.min(max, random));
        }
        /**
         * 从数组中随机获取一个元素
         */


        getRandomInArray(array, remove) {
          if (remove === void 0) {
            remove = false;
          }

          var length = array.length;
          if (length === 0) return null;
          var index = this.random_int(0, length - 1);
          var element = array[index];

          if (remove) {
            array.splice(index, 1);
          }

          return element;
        }
        /**
         * 将字符串转换为 Vec2
         * @param str 原字符串
         * @param delimiter 分隔符
         * @returns Vec2 对象
         */


        stringToPoint(str, delimiter) {
          var parts = str.split(delimiter);

          if (parts.length > 1) {
            return v2(Number(parts[0]), Number(parts[1]));
          }

          return Vec2.ZERO;
        }
        /**
         * 将字符串转换为数字数组
         * @param str 原字符串
         * @param delimiter 分隔符
         * @returns 数字数组
         */


        stringToNumber(str, delimiter) {
          var parts = str.split(delimiter);
          return parts.map(part => Number(part)).filter(num => !isNaN(num));
        }
        /**
         * 从数组中移除某个元素
         * @param array 数组
         * @param element 元素
         * @returns 是否成功移除
         */


        arrRemove(array, element) {
          var index = array.indexOf(element);

          if (index >= 0) {
            array.splice(index, 1);
            return true;
          }

          return false;
        }
        /**
         * 检查数组是否包含某个元素（与 arrContains 类似）
         * @param array 数组
         * @param element 元素
         * @returns 是否包含
         */


        arrContain(array, element) {
          return array.indexOf(element) != -1;
          ;
        }
        /**
         * 添加脚本组件到节点
         * @param node 节点
         * @param script 脚本类
         * @returns 添加的脚本组件
         */


        addScript(node, script) {
          if (!node || !script) return null;
          return node.getComponent(script) || node.addComponent(script);
        }
        /**
         * 根据名称移除子节点
         * @param parent 父节点
         * @param name 子节点名称
         */


        removeChildByName(parent, name) {
          if (!parent) return;
          var child = parent.getChildByName(name);

          if (child) {
            child.destroy();
          }
        }
        /**
         * 获取贝塞尔曲线上的点
         * @param p0 起点
         * @param p1 控制点1
         * @param p2 控制点2
         * @param p3 终点
         * @param t 参数 t（0 到 1）
         * @returns 贝塞尔曲线上的点
         */


        getBezier(p0, p1, p2, p3, t) {
          return p0 * Math.pow(1 - t, 3) + 3 * p1 * t * Math.pow(1 - t, 2) + 3 * p2 * Math.pow(t, 2) * (1 - t) + p3 * Math.pow(t, 3);
        }
        /**
         * 获取直线上的点
         * @param start 起点
         * @param direction 方向向量
         * @param distance 距离
         * @param t 参数 t（比例）
         * @returns 直线上的点
         */


        getStraight(start, direction, distance, t) {
          var normalizedDir = direction.subtract(start).normalize();
          return start.add(normalizedDir.multiplyScalar(distance * t));
        }
        /**
         * 获取方向向量上的点
         * @param start 起点
         * @param direction 方向向量
         * @param distance 距离
         * @param t 参数 t（比例）
         * @returns 方向向量上的点
         */


        getStraightForDir(start, direction, distance, t) {
          return start.add(direction.multiplyScalar(distance * t));
        }
        /**
         * 获取两点之间的角度
         * @param start 起点
         * @param end 终点
         * @returns 角度
         */


        getAngle(start, end) {
          var dx = end.x - start.x;
          var dy = end.y - start.y;
          var distance = Math.sqrt(dx * dx + dy * dy);
          var angle = Math.asin(dx / distance);
          return dy < 0 ? 180 - misc.radiansToDegrees(angle) : misc.radiansToDegrees(angle);
        }
        /**
         * 根据角度获取点的位置
         * @param point 点
         * @param angle 角度
         * @returns 新位置
         */


        getPositionByAngle(point, angle) {
          var radius = Math.sqrt(point.x * point.x + point.y * point.y);
          var radian = Math.atan2(point.y, point.x) + misc.degreesToRadians(angle);
          return v2(Math.cos(radian) * radius, Math.sin(radian) * radius);
        }
        /**
         * 获取方向向量
         * @param x1 起点 x 坐标
         * @param y1 起点 y 坐标
         * @param x2 终点 x 坐标
         * @param y2 终点 y 坐标
         * @returns 方向向量
         */


        getDir(x1, y1, x2, y2) {
          return v2(x2, y2).subtract(v2(x1, y1)).normalize();
        }
        /**
         * 获取方向向量的角度（以度为单位）
         * @param dir 方向向量
         * @returns 角度
         */


        getDegreeForDir(dir) {
          var reference = v2(0, -1); // 参考向量

          if (dir.equals(reference) || dir.equals(Vec2.ZERO)) {
            return 0;
          }

          var angle = dir.signAngle(reference);
          return misc.radiansToDegrees(angle);
        }
        /**
         * 清空 Map 中的组件数组
         * @param map 组件数组的 Map
         */


        clearMapForCompArr(map) {
          if (!map) return;
          map.forEach(compArray => {
            compArray.forEach(comp => {
              if (comp && comp.node) {
                comp.node.destroy();
              }
            });
          });
          map.clear();
        }
        /**
         * 根据权重数组随机返回一个下标
         * @param weights 权重数组（每个元素代表对应下标的权重）
         * @param rand 随机数（统一的随机数）
         * @returns 随机选中的下标
         */


        getRandomIndexByWeights(weights, rand) {
          if (!weights || weights.length === 0) {
            return -1; // 空数组返回-1
          } // 1. 计算权重总和


          var totalWeight = weights.reduce((sum, weight) => sum + weight, 0); // 2. 生成0到总权重之间的随机数

          var randomValue = rand * totalWeight; // 3. 遍历权重数组，找到随机值对应的下标

          var accumulatedWeight = 0;

          for (var i = 0; i < weights.length; i++) {
            accumulatedWeight += weights[i];

            if (randomValue < accumulatedWeight) {
              return i;
            }
          }

          return weights.length - 1;
        }

      };

      _export("Tools", Tools = new DYTools());

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=566192620458269380d7998a987cf7e7154c35f9.js.map