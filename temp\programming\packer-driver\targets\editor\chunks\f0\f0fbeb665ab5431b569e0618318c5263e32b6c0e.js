System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, csproto, MessageBox, logWarn, MyApp, DataEvent, EventMgr, Role, _crd, ROLE_SETTING_TYPE;

  function _reportPossibleCrUseOfcsproto(extras) {
    _reporterNs.report("csproto", "db://assets/bundles/common/script/autogen/pb/cs_proto.js", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMessageBox(extras) {
    _reporterNs.report("MessageBox", "db://assets/bundles/common/script/ui/common/components/messagebox/MessageBox", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogWarn(extras) {
    _reporterNs.report("logWarn", "db://assets/scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLong(extras) {
    _reporterNs.report("Long", "long", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "../../app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDataEvent(extras) {
    _reporterNs.report("DataEvent", "../../event/DataEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventMgr(extras) {
    _reporterNs.report("EventMgr", "../../event/EventManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIData(extras) {
    _reporterNs.report("IData", "../DataManager", _context.meta, extras);
  }

  _export("Role", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      csproto = _unresolved_2.default;
    }, function (_unresolved_3) {
      MessageBox = _unresolved_3.MessageBox;
    }, function (_unresolved_4) {
      logWarn = _unresolved_4.logWarn;
    }, function (_unresolved_5) {
      MyApp = _unresolved_5.MyApp;
    }, function (_unresolved_6) {
      DataEvent = _unresolved_6.DataEvent;
    }, function (_unresolved_7) {
      EventMgr = _unresolved_7.EventMgr;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "03d4bdxHJdP+7y4MXMoWRFA", "Role", undefined);

      _export("ROLE_SETTING_TYPE", ROLE_SETTING_TYPE = /*#__PURE__*/function (ROLE_SETTING_TYPE) {
        ROLE_SETTING_TYPE["ROLE_SETTING_UI_DATA"] = "ROLE_SETTING_UI_DATA";
        ROLE_SETTING_TYPE["ROLE_SETTING_BATTLE_DATA"] = "ROLE_SETTING_BATTLE_DATA";
        return ROLE_SETTING_TYPE;
      }({}));

      _export("Role", Role = class Role {
        constructor() {
          this.roleBaseAttr = new (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).comm.RoleBaseAttr();
          this.roleExtAttr = new (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).comm.RoleExtAttr();
          this.maxLevel = 0;
          this.curLevel = 0;
          this.curExp = 0;
          this.maxExp = 0;
          this.localSettings = [];
          this.settingDataUI = new (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).comm.SettingDataUI();
          this.settingDataBattle = new (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).comm.SettingDataBattle();
          this.dailyCounter = new Map();
          this.weekCounter = new Map();
          this.monthlytCounter = new Map();
          this.permanentCounter = new Map();
        }

        init() {
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GET_ROLE_BASE_ATTR, this.onGetRoleBaseAttr, this);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_LEVEL_UP, this.onLevelUp, this);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GET_ROLE_EXT_ATTR, this.onGetRoleExtAttr, this);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GET_ROLE_SIMPLE, this.onGetRoleSimple, this);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GET_COUNTER, this.onGetCounter, this);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GET_CLIENT_SETTING, this.onGetClientSetting, this);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_SET_CLIENT_SETTING, this.onSetClientSetting, this);
          this.cmdGetClientSettingUI();
          this.cmdGetCounterDaily();
          this.cmdGetCounterWeekly();
          this.cmdGetCounterMonthly();
          this.cmdGetCounterPermanent();
        }

        update() {}

        getCurLevelData(lvl) {
          const tbResUpgrade = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanTables.TbResUpgrade;
          const data = tbResUpgrade.get(lvl);
          return data;
        }

        setRole(data) {
          if (!data) {
            return;
          }

          this.setRoleBaseAttr(data.base);
        }

        setRoleBaseAttr(data) {
          var _data$level, _data$xp;

          if (!data) {
            return;
          }

          this.roleBaseAttr = data;
          const tbResUpgrade = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanTables.TbResUpgrade;
          const dataList = tbResUpgrade.getDataList();

          if (dataList.length > 0) {
            this.maxLevel = dataList[dataList.length - 1].roleLevel;
          }

          this.curLevel = (_data$level = data.level) != null ? _data$level : 0;
          this.curExp = (_data$xp = data.xp) != null ? _data$xp : 0;

          if (this.curLevel >= this.maxLevel) {
            this.maxLevel = this.curLevel;
            this.maxExp = 0;
          } else {
            var _lubanTables$TbResUpg, _lubanTables$TbResUpg2;

            this.maxExp = (_lubanTables$TbResUpg = (_lubanTables$TbResUpg2 = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).lubanTables.TbResUpgrade.get(this.curLevel + 1)) == null ? void 0 : _lubanTables$TbResUpg2.xp) != null ? _lubanTables$TbResUpg : 0;
          }

          if (this.curExp > this.maxExp) {
            this.curExp = this.maxExp;
          }

          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).emit((_crd && DataEvent === void 0 ? (_reportPossibleCrUseOfDataEvent({
            error: Error()
          }), DataEvent) : DataEvent).RoleBaseAttrChange); //MessageBox.show("获取成功" + JSON.stringify(this.roleBaseAttr));
        }

        getSetting(key) {
          if (this.localSettings) {
            const setting = this.localSettings.find(s => s.key === key);

            if (setting) {
              return setting.data;
            }
          }

          return null;
        }

        getEndlessBestScore() {
          var _this$permanentCounte;

          return (_this$permanentCounte = this.permanentCounter.get((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).comm.COUNTER_ID.COUNTER_ID_COMMON_GAME_ENDLESS_BEST_SCORE)) != null ? _this$permanentCounte : 0;
        }

        getEndlessWeekBestScore() {
          var _this$weekCounter$get;

          return (_this$weekCounter$get = this.weekCounter.get((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).comm.COUNTER_ID.COUNTER_ID_COMMON_GAME_ENDLESS_WEEK_BEST_SCORE)) != null ? _this$weekCounter$get : 0;
        }

        getEndlessDoubleRewardCount() {
          var _this$dailyCounter$ge;

          return (_this$dailyCounter$ge = this.dailyCounter.get((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).comm.COUNTER_ID.COUNTER_ID_COMMON_GAME_ENDLESS_DOUBLE_REWARD_COUNT)) != null ? _this$dailyCounter$ge : 0;
        }

        getStoryDoubleRewardCount() {
          var _this$dailyCounter$ge2;

          return (_this$dailyCounter$ge2 = this.dailyCounter.get((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).comm.COUNTER_ID.COUNTER_ID_COMMON_GAME_STORY_DOUBLE_REWARD_COUNT)) != null ? _this$dailyCounter$ge2 : 0;
        } //#region 收协议


        onGetRoleBaseAttr(msg) {
          var _msg$body;

          if (msg.ret_code != (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).comm.RET_CODE.RET_CODE_NO_ERROR) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", `onGetRoleBaseAttr failed ${msg.ret_code}`);
          }

          var data = (_msg$body = msg.body) == null ? void 0 : _msg$body.get_role_base_attr;

          if (!data) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", "onGetRoleBaseAttr data is null");
            return;
          }

          this.setRoleBaseAttr(data.base);
        }

        onLevelUp(msg) {
          var _msg$body2;

          if (msg.ret_code != (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).comm.RET_CODE.RET_CODE_NO_ERROR) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", `onLevelUp failed ${msg.ret_code}`);
            return;
          }

          var data = (_msg$body2 = msg.body) == null ? void 0 : _msg$body2.level_up;

          if (!data) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", "onLevelUp data is null");
            return;
          }

          (_crd && MessageBox === void 0 ? (_reportPossibleCrUseOfMessageBox({
            error: Error()
          }), MessageBox) : MessageBox).testShow("升级成功");
        }

        onGetRoleExtAttr(msg) {
          var _msg$body3;

          if (msg.ret_code != (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).comm.RET_CODE.RET_CODE_NO_ERROR) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", `onGetRoleExtAttr failed ${msg.ret_code}`);
            return;
          }

          var data = (_msg$body3 = msg.body) == null ? void 0 : _msg$body3.get_role_ext_attr;

          if (!data) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", "onGetRoleExtAttr data is null");
            return;
          }

          this.roleExtAttr = data.ext;
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).emit((_crd && DataEvent === void 0 ? (_reportPossibleCrUseOfDataEvent({
            error: Error()
          }), DataEvent) : DataEvent).RoleExtAttrChange); //MessageBox.show("获取成功" + JSON.stringify(this.roleExtAttr));
        }

        onGetRoleSimple(msg) {
          var _msg$body4;

          if (msg.ret_code != (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).comm.RET_CODE.RET_CODE_NO_ERROR) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", `onGetRoleSimple failed ${msg.ret_code}`);
            return;
          }

          var data = (_msg$body4 = msg.body) == null ? void 0 : _msg$body4.get_role_simple;

          if (!data) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", "onGetRoleSimple data is null");
          }
        }

        onGetClientSetting(msg) {
          var _msg$body5;

          if (msg.ret_code != (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).comm.RET_CODE.RET_CODE_NO_ERROR) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", `onGetClientSetting failed ${msg.ret_code}`);
            return;
          }

          var data = (_msg$body5 = msg.body) == null ? void 0 : _msg$body5.get_client_setting;

          if (!data) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", "onGetClientSetting data is null");
            return;
          }

          const settings = data.settings;

          if (settings) {
            settings.forEach(setting => {
              const key = setting.key;
              const data = setting.data;

              if (key === ROLE_SETTING_TYPE.ROLE_SETTING_UI_DATA) {
                this.settingDataUI = (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
                  error: Error()
                }), csproto) : csproto).comm.SettingDataUI.decode(data); //MessageBox.show("获取成功" + JSON.stringify(this.settingDataUI));
              } else if (key === ROLE_SETTING_TYPE.ROLE_SETTING_BATTLE_DATA) {
                this.settingDataBattle = (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
                  error: Error()
                }), csproto) : csproto).comm.SettingDataBattle.decode(data); //MessageBox.show("获取成功" + JSON.stringify(this.settingDataBattle));
              }
            });
          }
        }

        onSetClientSetting(msg) {
          var _msg$body6;

          if (msg.ret_code != (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).comm.RET_CODE.RET_CODE_NO_ERROR) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", `onSetClientSetting failed ${msg.ret_code}`);
            return;
          }

          var data = (_msg$body6 = msg.body) == null ? void 0 : _msg$body6.set_client_setting;

          if (!data) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", "onSetClientSetting data is null");
            return;
          } //MessageBox.show("设置成功");
          //没有Setting数据返回

        }

        onGetCounter(msg) {
          var _msg$body7;

          if (msg.ret_code != (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).comm.RET_CODE.RET_CODE_NO_ERROR) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", `onGetCounter failed ${msg.ret_code}`);
            return;
          }

          var data = (_msg$body7 = msg.body) == null ? void 0 : _msg$body7.get_counter;

          if (!data) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", "onGetCounter data is null");
            return;
          }

          const counters = data.counters;

          if (counters) {
            counters.forEach(counter => {
              const id = counter.id;
              const value = counter.value;

              if (counter.class === (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
                error: Error()
              }), csproto) : csproto).comm.COUNTER_CLASS.COUNTER_CLASS_COMMON_DAILY) {
                this.dailyCounter.set(id, value);
              } else if (counter.class === (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
                error: Error()
              }), csproto) : csproto).comm.COUNTER_CLASS.COUNTER_CLASS_COMMON_WEEKLY) {
                this.weekCounter.set(id, value);
              } else if (counter.class === (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
                error: Error()
              }), csproto) : csproto).comm.COUNTER_CLASS.COUNTER_CLASS_COMMON_MONTHLY) {
                this.monthlytCounter.set(id, value);
              } else if (counter.class === (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
                error: Error()
              }), csproto) : csproto).comm.COUNTER_CLASS.COUNTER_CLASS_COMMON_PERMANENT) {
                this.permanentCounter.set(id, value);
              }
            });
          }
        }

        onDelClientSetting(msg) {
          var _msg$body8;

          if (msg.ret_code != (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).comm.RET_CODE.RET_CODE_NO_ERROR) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", `onDelClientSetting failed ${msg.ret_code}`);
            return;
          }

          var data = (_msg$body8 = msg.body) == null ? void 0 : _msg$body8.del_client_setting;

          if (!data) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", "onDelClientSetting data is null");
            return;
          }
        } //#endregion
        //#region 发协议


        cmdGetRoleBaseAttr() {
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.sendMessage((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GET_ROLE_BASE_ATTR, {
            get_role_base_attr: {}
          });
        } //获取角色的扩展信息


        cmdGetRoleExtAttr() {
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.sendMessage((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GET_ROLE_EXT_ATTR, {
            get_role_ext_attr: {}
          });
        }

        CmdGetRoleSimple(uin, area_id) {
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.sendMessage((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GET_ROLE_SIMPLE, {
            get_role_simple: {
              uin: uin,
              area_id: area_id
            }
          });
        }

        cmdGetClientSettingUI() {
          let getKeys = [ROLE_SETTING_TYPE.ROLE_SETTING_UI_DATA];
          this.cmdGetClientSetting(getKeys);
        }

        cmdGetClientSettingBattle() {
          let getKeys = [ROLE_SETTING_TYPE.ROLE_SETTING_BATTLE_DATA];
          this.cmdGetClientSetting(getKeys);
        }

        cmdGetClientSetting(getKeys) {
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.sendMessage((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GET_CLIENT_SETTING, {
            get_client_setting: {
              keys: getKeys
            }
          });
        }

        cmdSetClientSettingUI(settingDataUI) {
          let settingDataUIBytes = (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).comm.SettingDataUI.encode(settingDataUI).finish();
          this.cmdSetClientSetting([{
            key: ROLE_SETTING_TYPE.ROLE_SETTING_UI_DATA,
            data: settingDataUIBytes
          }]);
        }

        cmdSetClientSettingBattle(settingDataBattle) {
          let settingDataBattleBytes = (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).comm.SettingDataBattle.encode(settingDataBattle).finish();
          this.cmdSetClientSetting([{
            key: ROLE_SETTING_TYPE.ROLE_SETTING_BATTLE_DATA,
            data: settingDataBattleBytes
          }]);
        }

        cmdSetClientSetting(newSettings) {
          if (newSettings.length === 0) {
            return;
          }

          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.sendMessage((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_SET_CLIENT_SETTING, {
            set_client_setting: {
              settings: newSettings
            }
          });
        }

        cmdDelClientSetting(delKeys) {
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.sendMessage((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_DEL_CLIENT_SETTING, {
            del_client_setting: {
              keys: delKeys
            }
          });
        } //每日数据  COUNTER_CLASS_COMMON_DAILY;


        cmdGetCounterDaily(counterID = 0) {
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.sendMessage((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GET_COUNTER, {
            get_counter: {
              "class": (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
                error: Error()
              }), csproto) : csproto).comm.COUNTER_CLASS.COUNTER_CLASS_COMMON_DAILY,
              id: counterID
            }
          });
        } //每周数据  COUNTER_CLASS_COMMON_WEEKLY


        cmdGetCounterWeekly(counterID = 0) {
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.sendMessage((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GET_COUNTER, {
            get_counter: {
              "class": (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
                error: Error()
              }), csproto) : csproto).comm.COUNTER_CLASS.COUNTER_CLASS_COMMON_WEEKLY,
              id: counterID
            }
          });
        } //每月数据  COUNTER_CLASS_COMMON_MONTHLY


        cmdGetCounterMonthly(counterID = 0) {
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.sendMessage((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GET_COUNTER, {
            get_counter: {
              "class": (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
                error: Error()
              }), csproto) : csproto).comm.COUNTER_CLASS.COUNTER_CLASS_COMMON_MONTHLY,
              id: counterID
            }
          });
        } //永久数据  COUNTER_CLASS_COMMON_PERMANENT


        cmdGetCounterPermanent(counterID = 0) {
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.sendMessage((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GET_COUNTER, {
            get_counter: {
              "class": (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
                error: Error()
              }), csproto) : csproto).comm.COUNTER_CLASS.COUNTER_CLASS_COMMON_PERMANENT,
              id: counterID
            }
          });
        } //#endregion


      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=f0fbeb665ab5431b569e0618318c5263e32b6c0e.js.map