import { _decorator, Component, Node, Enum, Vec2, Vec3, UITransform } from 'cc';
import { BulletSystem } from '../bullet/BulletSystem';
const { ccclass, property } = _decorator;

/**
 * Base interface for all move-able objects
 */
export interface IMovable {
    speed : number;                 // 速度
    speedAngle : number;            // 速度方向 (用角度表示)
    acceleration : number;          // 加速度
    accelerationAngle : number;     // 加速度方向 (用角度表示)
}

// export enum eMoveModifier {
//     Speed, SpeedAngle, Acceleration, AccelerationAngle
// }

export enum eEasing {
    Linear,
    InSine, OutSine, InOutSine,
    InQuad, OutQuad, InOutQuad
}

export enum eSpriteDefaultFacing {
    Right = 0,    // →
    Up = -90,     // ↑
    Down = 90,    // ↓
    Left = 180    // ←
}

export enum eMoveEvent {
    onBecomeVisible,
    onBecomeInvisible,
}

export abstract class MoveBase extends Component implements IMovable {
    @property({ type: Enum(eSpriteDefaultFacing), displayName: '图片默认朝向' })
    public defaultFacing: eSpriteDefaultFacing = eSpriteDefaultFacing.Up;

    public speed: number = 100;
    public speedAngle: number = 0;
    public acceleration: number = 0;
    public accelerationAngle: number = 0;

    // @property({displayName: '振荡偏移速度', tooltip: '控制倾斜振荡的频率'})
    public tiltSpeed: number = 0;                 // 偏移速度
    // @property({displayName: '振荡偏移幅度', tooltip: '控制倾斜振荡的幅度'})
    public tiltOffset: number = 100;               // 偏移距离

    protected _wasVisible: boolean = false;
    protected _isVisible: boolean = false;           // 是否可见
    public get isVisible() { return this._isVisible; }

    protected _isMovable: boolean = true;           // 是否可移动
    public get isMovable() { return this._isMovable; }

    protected _tiltTime: number = 0;                // 用于计算倾斜偏移的累积时间
    protected _selfSize: Vec2 = new Vec2();
    protected _position: Vec3 = new Vec3();
    protected _basePosition: Vec3 = new Vec3();     // 基础位置（不包含倾斜偏移）

    protected _visibilityCheckCounter: number = 0;  // 可见性检查计数器
    protected static readonly VISIBILITY_CHECK_INTERVAL = 5; // 每x帧检查一次可见性

    // Event system:
    protected _eventListeners: Map<eMoveEvent, Array<() => void>> = new Map();
    
    onLoad() {
        const uiTransform = this.node.getComponent(UITransform);
        const self_size = uiTransform ? uiTransform.contentSize : { width: 0, height: 0 };
        this._selfSize.set(self_size.width / 2, self_size.height / 2);
    }

    onDestroy() {
        // clear all event listeners
        this._eventListeners.clear();
    }

    /**
     * 添加事件监听器
     * @param event 事件类型
     * @param listener 监听器函数
     */
    public on(event: eMoveEvent, listener: () => void): void {
        if (!this._eventListeners.has(event)) {
            this._eventListeners.set(event, []);
        }
        const listeners = this._eventListeners.get(event)!;
        if (!listeners.includes(listener)) {
            listeners.push(listener);
        }
    }

    /**
     * 移除事件监听器
     * @param event 事件类型
     * @param listener 监听器函数
     */
    public off(event: eMoveEvent, listener: () => void): void {
        const listeners = this._eventListeners.get(event);
        if (listeners) {
            const index = listeners.indexOf(listener);
            if (index !== -1) {
                listeners.splice(index, 1);
            }
        }
    }

    public removeAllListeners(): void {
        this._eventListeners.clear();
    }

    /**
     * 触发事件
     * @param event 事件类型
     */
    protected emit(event: eMoveEvent): void {
        const listeners = this._eventListeners.get(event);
        if (listeners && listeners.length > 0) {
            listeners.forEach(listener => listener());
        }
    }

    public checkVisibility(): void {
        // 降低可见性检查频率
        if (++this._visibilityCheckCounter >= MoveBase.VISIBILITY_CHECK_INTERVAL) {
            this._visibilityCheckCounter = 0;
            // 这里目前的检查逻辑没有考虑旋转和缩放
            // 正常来说需要判定world corners，如果四个角有一个在屏幕内，就认为是可见的
            const visibleSize = BulletSystem.worldBounds;
            const position = this.node.worldPosition;
            const isVisible = (position.x + this._selfSize.x) >= visibleSize.xMin &&
                (position.x - this._selfSize.x) <= visibleSize.xMax &&
                (position.y - this._selfSize.y) <= visibleSize.yMax &&
                (position.y + this._selfSize.y) >= visibleSize.yMin;

            // debug visibility
            // if (!isVisible) {
            //     console.log("Movable", "checkVisibility", this.node.name + " is not visible");
            //     console.log("Movable", "checkLeftBound  :", (this._position.x - this._selfSize.x) <= visibleSize.xMax, (this._position.x - this._selfSize.x), "<=", visibleSize.xMax);
            //     console.log("Movable", "checkRightBound :", (this._position.x + this._selfSize.x) >= visibleSize.xMin, (this._position.x + this._selfSize.x), ">=", visibleSize.xMin);
            //     console.log("Movable", "checkTopBound   :", (this._position.y + this._selfSize.y) <= visibleSize.yMax, (this._position.y + this._selfSize.y), "<=", visibleSize.yMax);
            //     console.log("Movable", "checkBottomBound:", (this._position.y - this._selfSize.y) >= visibleSize.yMin, (this._position.y - this._selfSize.y), ">=", visibleSize.yMin);
            // }

            this.setVisible(isVisible);
        }
    }

    protected setVisible(visible: boolean) {
        // console.log('setVisible: ', this._wasVisible, ', ', visible);
        if (visible) {
            if (!this._wasVisible)
                this.emit(eMoveEvent.onBecomeVisible);
        } else {
            if (this._wasVisible)
                this.emit(eMoveEvent.onBecomeInvisible);
        }
        this._wasVisible = this._isVisible;
        this._isVisible = visible;
    }

    // 沿angleInRadian的垂直方向振荡移动
    protected updateTilting(angleInRadian: number, dt: number, outPosition: Vec3): void {
        // Apply tilting behavior if enabled
        if (this.tiltSpeed > 0 && this.tiltOffset > 0) {
            // Update tilt time
            this._tiltTime += dt;

            // Calculate perpendicular direction to movement
            // If moving in direction (cos(angle), sin(angle)), perpendicular is (-sin(angle), cos(angle))
            const moveAngleRad = angleInRadian;
            const perpX = -Math.sin(moveAngleRad);
            const perpY = Math.cos(moveAngleRad);

            // Calculate tilt offset using sine wave
            const tiltAmount = Math.sin(this._tiltTime * this.tiltSpeed) * this.tiltOffset;

            // Apply tilt offset in perpendicular direction (as position offset, not velocity)
            outPosition.x += perpX * tiltAmount;
            outPosition.y += perpY * tiltAmount;
        }
    }
}