System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, logError, logInfo, csproto, WXLogin, _crd;

  function _reportPossibleCrUseOflogError(extras) {
    _reporterNs.report("logError", "../../../../scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogInfo(extras) {
    _reporterNs.report("logInfo", "../../../../scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfcsproto(extras) {
    _reporterNs.report("csproto", "../autogen/pb/cs_proto.js", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLoginInfo(extras) {
    _reporterNs.report("LoginInfo", "../network/NetMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIPlatformSDK(extras) {
    _reporterNs.report("IPlatformSDK", "./IPlatformSDK.js", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlatformSDKUserInfo(extras) {
    _reporterNs.report("PlatformSDKUserInfo", "./IPlatformSDK.js", _context.meta, extras);
  }

  _export("WXLogin", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      logError = _unresolved_2.logError;
      logInfo = _unresolved_2.logInfo;
    }, function (_unresolved_3) {
      csproto = _unresolved_3.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "b7d7evTqC5BM7oY1+X3GaFx", "WXLogin", undefined);

      _export("WXLogin", WXLogin = class WXLogin {
        constructor() {
          this.authButton = null;
        }

        login(cb) {
          (_crd && logInfo === void 0 ? (_reportPossibleCrUseOflogInfo({
            error: Error()
          }), logInfo) : logInfo)("Login", "start wx login"); // @ts-ignore

          wx.login({
            // @ts-ignore
            success(res) {
              (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
                error: Error()
              }), logError) : logError)("Login", "complete wx login " + res.errMsg + " " + res.code);
              cb(null, {
                accountType: (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
                  error: Error()
                }), csproto) : csproto).cs.ACCOUNT_TYPE.ACCOUNT_TYPE_WXMINIGAME,
                code: res.code,
                serverAddr: "wss://m2test.5600.online:9101/"
              });
            }

          });
        }

        showUserInfoButton() {
          if (this.authButton) {
            // @ts-ignore
            this.authButton.show();
          }
        }

        hideUserInfoButton() {
          if (this.authButton) {
            // @ts-ignore
            this.authButton.hide();
          }
        }

        getStatusBarHeight() {
          // @ts-ignore
          var systemInfo = wx.getSystemInfoSync();
          var safeAreaTop = systemInfo.safeArea.top; // 安全区域顶部到屏幕顶部的距离

          var statusBarHeight = systemInfo.statusBarHeight; // 状态栏高度（仅原生刘海）

          var navBarHeight = safeAreaTop - statusBarHeight; // 小程序导航栏高度

          console.log('安全区域顶部到屏幕顶部的距离', safeAreaTop);
          console.log('状态栏高度（仅原生刘海）', statusBarHeight);
          console.log('小程序导航栏高度', navBarHeight);
          return statusBarHeight;
        }

        getUserInfo(cb, param) {
          var THIS = this; // @ts-ignore

          wx.getSetting({
            // @ts-ignore
            success(res) {
              console.error('WXLogin get userinfo auth setting', res.authSetting);

              if (!res.authSetting['scope.userInfo']) {
                console.error('WXLogin start authorize userinfo'); // @ts-ignore

                var sysInfo = wx.getSystemInfoSync(); // @ts-ignore

                var button = wx.createUserInfoButton({
                  type: 'text',
                  text: '',
                  //image : "SDK/WXGetUserInfo.png",
                  plain: true,
                  style: {
                    left: sysInfo.screenWidth * param.x,
                    //0,//buttonPosition.x,
                    top: sysInfo.screenHeight * (1 - param.y) + (sysInfo.safeArea.top - 20),
                    //1334-100,//buttonPosition.y+buttonPosition.height,
                    width: sysInfo.screenWidth * param.w,
                    //750,//buttonPosition.width,
                    height: sysInfo.screenHeight * param.h,
                    //100,//buttonPosition.height,
                    lineHeight: 40,
                    backgroundColor: '#ff0000',
                    color: '#ffffff',
                    textAlign: 'center',
                    fontSize: 16,
                    borderRadius: 4
                  }
                });
                button.show(); // @ts-ignore

                button.onTap(res => {
                  if (res.userInfo) {
                    button.destroy();
                    THIS.authButton = null;
                    console.log('WXLogin get wx userinfo success', res.userInfo);
                    THIS.getUserInfo__(res.userInfo, cb, true);
                  }
                });
                THIS.authButton = button;
              } else {
                THIS.getUserInfo_(cb, false);
              }
            }

          });
        }

        getUserInfo__(userInfo, cb, hasTap) {
          cb("", {
            name: userInfo.nickName,
            icon: userInfo.avatarUrl
          }, true);
        }

        getUserInfo_(cb, hasTap) {
          var THIS = this; // @ts-ignore

          wx.getUserInfo({
            // @ts-ignore
            complete(res) {
              console.error("WXLogin getUserInfo complete");

              if (res.userInfo) {
                THIS.getUserInfo__(res.userInfo, cb, hasTap);
              } else {
                cb("get userinfo error", null, false);
              }
            }

          });
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=07437f8f3e0b17c81d2d426d702b5eb5ff92fc45.js.map