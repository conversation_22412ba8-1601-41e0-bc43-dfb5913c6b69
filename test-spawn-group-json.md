# SpawnGroup toJSON 问题修复测试

## 问题描述

在FormationEditor的save()方法中调用JSON.stringify时，遇到错误：
```
group.toJSON is not a function
```

## 问题原因

1. FormationPointEditor.spawnGroups 是通过Cocos Creator的@property系统声明的数组
2. Cocos Creator序列化时创建的SpawnGroup实例不是真正的类实例，而是普通对象
3. 这些对象没有SpawnGroup类的方法，包括新添加的toJSON方法

## 解决方案

### 方案1：在FormationPoint.toJSON()中处理（已实现）
- 检查group是否有toJSON方法
- 如果有，调用group.toJSON()
- 如果没有，手动创建JSON对象，排除selfWeight属性

### 方案2：在FormationPointEditor中确保正确的实例（已实现）
- 在get formationPoint()中检查每个group是否是SpawnGroup实例
- 如果不是，创建新的SpawnGroup实例并复制属性

## 测试步骤

1. 打开FormationEditor场景
2. 加载一个阵型JSON文件
3. 添加一些阵型点
4. 为阵型点添加SpawnGroup
5. 点击保存按钮
6. 验证是否能正常保存，不再报错

## 预期结果

- 不再出现"group.toJSON is not a function"错误
- JSON输出中不包含selfWeight属性
- 保存的JSON格式正确

## 代码变更

### FormationPoint.toJSON()
```typescript
public toJSON(): any {
    return {
        x: this.x,
        y: this.y,
        spawnGroups: this.spawnGroups.map((group) => {
            if (typeof group.toJSON === 'function') {
                return group.toJSON();
            } else {
                return {
                    planeID: group.planeID,
                    weight: group.weight
                };
            }
        }),
    };
}
```

### FormationPointEditor.formationPoint getter
```typescript
public get formationPoint(): FormationPoint {
    let point = new FormationPoint();
    point.spawnGroups = this.spawnGroups.map(group => {
        if (group instanceof SpawnGroup) {
            return group;
        } else {
            const newGroup = new SpawnGroup();
            newGroup.planeID = (group as any).planeID;
            newGroup.weight = (group as any).weight;
            return newGroup;
        }
    });
    point.x = this.node.position.x;
    point.y = this.node.position.y;
    return point;
}
```
