{"__type__": "cc.Json<PERSON>set", "_name": "tbresgamemode", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "json": [{"modeID": 101, "modeType": 1, "chapterID": 1100001, "order": 1, "resourceID": 1, "description": "主游戏模式，打分", "conList": [], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 0, "rogueID": 1, "LevelLimit": 25, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 102, "modeType": 5, "chapterID": 1100001, "order": 1, "resourceID": 2, "description": "邀请好友PK", "conList": [{"cond_type": 1, "params": [10]}], "periodType": 1, "times": 5, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 0, "rogueID": 2, "LevelLimit": 25, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 103, "modeType": 4, "chapterID": 1100001, "order": 1, "resourceID": 3, "description": "金币PK", "conList": [{"cond_type": 1, "params": [10]}], "periodType": 1, "times": 10, "costEnergy": 0, "monType": 1, "costParam1": 1000, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 0, "rogueID": 3, "LevelLimit": 25, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 104, "modeType": 4, "chapterID": 1100001, "order": 2, "resourceID": 4, "description": "钻石PK", "conList": [{"cond_type": 1, "params": [10]}], "periodType": 1, "times": 10, "costEnergy": 0, "monType": 2, "costParam1": 200, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 0, "rogueID": 4, "LevelLimit": 25, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 105, "modeType": 4, "chapterID": 1100001, "order": 3, "resourceID": 5, "description": "高级钻石PK", "conList": [{"cond_type": 1, "params": [10]}], "periodType": 1, "times": 10, "costEnergy": 0, "monType": 2, "costParam1": 500, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 0, "rogueID": 5, "LevelLimit": 25, "rogueFirst": 0, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2001, "modeType": 2, "chapterID": 1100001, "order": 1, "resourceID": 6, "description": "简单·第一章", "conList": [], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 10000, "rogueID": 6, "LevelLimit": 15, "rogueFirst": 0, "sweepLimit": 2, "rewardID": 0, "ratingList": []}, {"modeID": 2002, "modeType": 2, "chapterID": 1100001, "order": 2, "resourceID": 7, "description": "简单·第二章", "conList": [{"cond_type": 2, "params": [2001]}, {"cond_type": 1, "params": [1]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 20000, "rogueID": 7, "LevelLimit": 15, "rogueFirst": 0, "sweepLimit": 2, "rewardID": 0, "ratingList": []}, {"modeID": 2003, "modeType": 2, "chapterID": 1100001, "order": 3, "resourceID": 8, "description": "简单·第三章", "conList": [{"cond_type": 2, "params": [2002]}, {"cond_type": 1, "params": [2]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 30000, "rogueID": 8, "LevelLimit": 15, "rogueFirst": 0, "sweepLimit": 5, "rewardID": 0, "ratingList": []}, {"modeID": 1, "modeType": 3, "chapterID": 1100001, "order": 1, "resourceID": 9, "description": "第一关", "conList": [{"cond_type": 1, "params": [20]}], "periodType": 2, "times": 2, "costEnergy": 0, "monType": 3, "costParam1": 111, "costParam2": 1, "rebirthTimes": 0, "rebirthCost": 0, "power": 0, "rogueID": 9, "LevelLimit": 30, "rogueFirst": 5, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 2, "modeType": 3, "chapterID": 1100001, "order": 2, "resourceID": 10, "description": "第二关", "conList": [{"cond_type": 2, "params": [1]}], "periodType": 2, "times": 2, "costEnergy": 0, "monType": 3, "costParam1": 111, "costParam2": 1, "rebirthTimes": 0, "rebirthCost": 0, "power": 0, "rogueID": 10, "LevelLimit": 30, "rogueFirst": 5, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3, "modeType": 3, "chapterID": 1100001, "order": 3, "resourceID": 11, "description": "第三关", "conList": [{"cond_type": 2, "params": [2]}], "periodType": 2, "times": 2, "costEnergy": 0, "monType": 3, "costParam1": 111, "costParam2": 1, "rebirthTimes": 0, "rebirthCost": 0, "power": 0, "rogueID": 11, "LevelLimit": 30, "rogueFirst": 5, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 4, "modeType": 3, "chapterID": 1100001, "order": 4, "resourceID": 12, "description": "第四关", "conList": [{"cond_type": 2, "params": [3]}], "periodType": 2, "times": 2, "costEnergy": 0, "monType": 3, "costParam1": 111, "costParam2": 1, "rebirthTimes": 0, "rebirthCost": 0, "power": 0, "rogueID": 12, "LevelLimit": 30, "rogueFirst": 5, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 5, "modeType": 3, "chapterID": 1100001, "order": 5, "resourceID": 13, "description": "第五关", "conList": [{"cond_type": 2, "params": [4]}], "periodType": 2, "times": 2, "costEnergy": 0, "monType": 3, "costParam1": 111, "costParam2": 1, "rebirthTimes": 0, "rebirthCost": 0, "power": 0, "rogueID": 13, "LevelLimit": 30, "rogueFirst": 5, "sweepLimit": 0, "rewardID": 0, "ratingList": []}, {"modeID": 3001, "modeType": 2, "chapterID": 1100001, "order": 1, "resourceID": 6, "description": "困难·第一章", "conList": [], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 50000, "rogueID": 6, "LevelLimit": 20, "rogueFirst": 0, "sweepLimit": 2, "rewardID": 0, "ratingList": []}, {"modeID": 3002, "modeType": 2, "chapterID": 1100001, "order": 2, "resourceID": 7, "description": "困难·第二章", "conList": [{"cond_type": 2, "params": [2001]}, {"cond_type": 1, "params": [1]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 60000, "rogueID": 7, "LevelLimit": 20, "rogueFirst": 0, "sweepLimit": 2, "rewardID": 0, "ratingList": []}, {"modeID": 3003, "modeType": 2, "chapterID": 1100001, "order": 3, "resourceID": 8, "description": "困难·第三章", "conList": [{"cond_type": 2, "params": [2002]}, {"cond_type": 1, "params": [2]}], "periodType": 0, "times": 0, "costEnergy": 0, "monType": 0, "costParam1": 0, "costParam2": 0, "rebirthTimes": 0, "rebirthCost": 0, "power": 80000, "rogueID": 8, "LevelLimit": 20, "rogueFirst": 0, "sweepLimit": 5, "rewardID": 0, "ratingList": []}]}