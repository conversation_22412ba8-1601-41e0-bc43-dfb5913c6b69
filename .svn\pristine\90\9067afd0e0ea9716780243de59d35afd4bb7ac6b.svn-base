import { _decorator, Node } from 'cc';
import csproto from 'db://assets/bundles/common/script/autogen/pb/cs_proto.js';
import { BaseUI, UILayer, UIMgr, UIOpt } from "db://assets/scripts/core/base/UIMgr";
import { BundleName } from '../../const/BundleConst';
import { DataMgr } from '../../data/DataManager';
import { ButtonPlus } from './components/button/ButtonPlus';
import List from './components/list/List';
import { StatisticsHurtCell } from './StatisticsHurtCell';
import { StatisticsScoreCell } from './StatisticsScoreCell';

const { ccclass, property } = _decorator;


@ccclass('StatisticsUI')
export class StatisticsUI extends BaseUI {

    @property(ButtonPlus)
    btnOK: ButtonPlus | null = null;
    @property(ButtonPlus)
    btnNext: ButtonPlus | null = null;

    @property(List)
    list: List | null = null;

    @property(Node)
    cellsNode: Node | null = null;

    cells: StatisticsHurtCell[] = [];

    statsScore: { type: string; val: string; score: string }[] = [];
    statsHurt: { type: string; score: string }[] = [];

    public static getUrl(): string { return "prefab/ui/StatisticsUI"; }
    public static getLayer(): UILayer { return UILayer.Top }
    public static getBundleName(): string { return BundleName.Home }
    public static getUIOption(): UIOpt {
        return { isClickBgCloseUI: false }
    }

    protected onLoad(): void {
        this.btnOK!.addClick(this.onOKClick, this);
        this.btnNext!.addClick(this.onNextClick, this);

        const INIT_SCORE_DATA = [
            { type: '杀怪得分', val: '0击落', score: '0' },
            { type: '宝石得分', val: '0颗', score: '0' },
            { type: '飞行距离', val: '0KM', score: '0' },
            { type: '剩余核弹数', val: '余0颗', score: '0' },
            { type: '传送得分', val: '前0关', score: '0' }
        ];
        const INIT_HURT_DATA = [
            { type: '战机', score: '0' },
            { type: '装备', score: '0' },
            { type: '武器', score: '0' },
            { type: '道具', score: '0' },
            { type: '其他', score: '0' }
        ];

        this.statsScore = [...INIT_SCORE_DATA];
        this.statsHurt = [...INIT_HURT_DATA];

        let totalScore = 0;
        let result = DataMgr.gameLogic.result;
        if (result && result.game_stats) {
            result.game_stats.forEach(stat => {
                if (stat.id == csproto.comm.GAME_STAT_ID.GAME_STAT_ID_SCORE_KILL) {
                    this.statsScore[0].score = stat.value!.toString();
                }
                else if (stat.id == csproto.comm.GAME_STAT_ID.GAME_STAT_ID_SCORE_GEM) {
                    this.statsScore[1].score = stat.value!.toString();
                }
                else if (stat.id == csproto.comm.GAME_STAT_ID.GAME_STAT_ID_SCORE_DISTANCE) {
                    this.statsScore[2].score = stat.value!.toString();
                }
                else if (stat.id == csproto.comm.GAME_STAT_ID.GAME_STAT_ID_SCORE_NBOMB) {
                    this.statsScore[3].score = stat.value!.toString();
                }
                else if (stat.id == csproto.comm.GAME_STAT_ID.GAME_STAT_ID_SCORE_TRANS) {
                    this.statsScore[4].score = stat.value!.toString();
                }

                else if (stat.id == csproto.comm.GAME_STAT_ID.GAME_STAT_ID_KILL_MONSTER) {
                    this.statsScore[0].val = this.statsScore[0].val.replace(/\d+/g, stat.value!.toString()) ;
                }
                else if (stat.id == csproto.comm.GAME_STAT_ID.GAME_STAT_ID_PICKUP_GEM) {
                    this.statsScore[1].val = this.statsScore[1].val.replace(/\d+/g, stat.value!.toString()) ;
                }
                else if (stat.id == csproto.comm.GAME_STAT_ID.GAME_STAT_ID_DISTANCE) {
                    this.statsScore[2].val = this.statsScore[2].val.replace(/\d+/g, stat.value!.toString()) ;
                }
                else if (stat.id == csproto.comm.GAME_STAT_ID.GAME_STAT_ID_REMAIM_NBOMB) {
                    this.statsScore[3].val = this.statsScore[3].val.replace(/\d+/g, stat.value!.toString()) ;
                }
                else if (stat.id == csproto.comm.GAME_STAT_ID.GAME_STAT_ID_DISTANCE_TRANS) {
                    this.statsScore[4].val = this.statsScore[4].val.replace(/\d+/g, stat.value!.toString()) ;
                }

                else if (stat.id == csproto.comm.GAME_STAT_ID.GAME_STAT_ID_HURT_PLANE) {
                    this.statsHurt[0].score = stat.value!.toString();
                    totalScore += Number(stat.value);
                }
                else if (stat.id == csproto.comm.GAME_STAT_ID.GAME_STAT_ID_HURT_SECOND_WEAPON) {
                    this.statsHurt[1].score = stat.value!.toString();
                    totalScore += Number(stat.value);
                }
                else if (stat.id == csproto.comm.GAME_STAT_ID.GAME_STAT_ID_HURT_WINGPLANE) {
                    this.statsHurt[2].score = stat.value!.toString();
                    totalScore += Number(stat.value);
                }
                else if (stat.id == csproto.comm.GAME_STAT_ID.GAME_STAT_ID_HURT_NBOMB) {
                    this.statsHurt[3].score = stat.value!.toString();
                    totalScore += Number(stat.value);
                }
                else if (stat.id == csproto.comm.GAME_STAT_ID.GAME_STAT_ID_HURT_OTHER) {
                    this.statsHurt[4].score = stat.value!.toString();
                    totalScore += Number(stat.value);
                }
            });
        }

        this.list!.numItems = this.statsScore.length;//目前只有5个

        let idx: number = 0;
        this.cellsNode!.children.forEach(node => {
            const cell = node.getComponent(StatisticsHurtCell);
            if (cell) {
                cell.setType(this.statsHurt[idx++], 150);
            }
        });

    }
    async onOKClick() {
        UIMgr.closeUI(StatisticsUI);
    }
    async onNextClick() {
        UIMgr.closeUI(StatisticsUI);
    }
    async onShow(): Promise<void> {


    }
    async onHide(): Promise<void> {
    }
    async onClose(): Promise<void> {

    }
    onListRender(listItem: Node, row: number) {//这个函数在onShow()之前调用
        const cell = listItem.getComponent(StatisticsScoreCell);
        if (cell !== null) {
            cell.setType(this.statsScore[row]);
        }
    }
}
