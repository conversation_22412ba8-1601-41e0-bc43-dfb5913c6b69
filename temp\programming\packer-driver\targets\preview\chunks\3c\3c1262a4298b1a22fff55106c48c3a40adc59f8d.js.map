{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/move/IMovable.ts"], "names": ["_decorator", "Component", "Enum", "Vec2", "Vec3", "UITransform", "BulletSystem", "ccclass", "property", "eEasing", "eSpriteDefaultFacing", "eMoveEvent", "MoveBase", "type", "displayName", "speed", "speedAngle", "acceleration", "accelerationAngle", "_wasVisible", "_isVisible", "_isMovable", "_selfSize", "_position", "_visibility<PERSON><PERSON><PERSON><PERSON>ounter", "_eventListeners", "Map", "isVisible", "isMovable", "onLoad", "uiTransform", "node", "getComponent", "self_size", "contentSize", "width", "height", "set", "onDestroy", "clear", "on", "event", "listener", "has", "listeners", "get", "includes", "push", "off", "index", "indexOf", "splice", "removeAllListeners", "emit", "length", "for<PERSON>ach", "checkVisibility", "VISIBILITY_CHECK_INTERVAL", "visibleSize", "worldBounds", "position", "worldPosition", "x", "xMin", "xMax", "y", "yMax", "yMin", "setVisible", "visible", "onBecomeVisible", "onBecomeInvisible", "Up"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,W,OAAAA,W;;AAC/CC,MAAAA,Y,iBAAAA,Y;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBR,U;AAE9B;AACA;AACA;;AAQA;AACA;AACA;yBAEYS,O,0BAAAA,O;AAAAA,QAAAA,O,CAAAA,O;AAAAA,QAAAA,O,CAAAA,O;AAAAA,QAAAA,O,CAAAA,O;AAAAA,QAAAA,O,CAAAA,O;AAAAA,QAAAA,O,CAAAA,O;AAAAA,QAAAA,O,CAAAA,O;AAAAA,QAAAA,O,CAAAA,O;eAAAA,O;;;sCAMAC,oB,0BAAAA,oB;AAAAA,QAAAA,oB,CAAAA,oB;AAAAA,QAAAA,oB,CAAAA,oB;AAAAA,QAAAA,oB,CAAAA,oB;AAAAA,QAAAA,oB,CAAAA,oB;eAAAA,oB;;;4BAOAC,U,0BAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;eAAAA,U;;;0BAKUC,Q,WACjBJ,QAAQ,CAAC;AAAEK,QAAAA,IAAI,EAAEX,IAAI,CAACQ,oBAAD,CAAZ;AAAoCI,QAAAA,WAAW,EAAE;AAAjD,OAAD,C,uBADN,MAAeF,QAAf,SAAgCX,SAAhC,CAA8D;AAAA;AAAA;;AAAA;;AAAA,eAI1Dc,KAJ0D,GAI1C,GAJ0C;AAAA,eAK1DC,UAL0D,GAKrC,CALqC;AAAA,eAM1DC,YAN0D,GAMnC,CANmC;AAAA,eAO1DC,iBAP0D,GAO9B,CAP8B;AAAA,eASvDC,WATuD,GAShC,KATgC;AAAA,eAUvDC,UAVuD,GAUjC,KAViC;AAAA,eAavDC,UAbuD,GAajC,IAbiC;AAAA,eAgBvDC,SAhBuD,GAgBrC,IAAInB,IAAJ,EAhBqC;AAAA,eAiBvDoB,SAjBuD,GAiBrC,IAAInB,IAAJ,EAjBqC;AAAA,eAmBvDoB,uBAnBuD,GAmBrB,CAnBqB;AAoBR;AAEzD;AAtBiE,eAuBvDC,eAvBuD,GAuBD,IAAIC,GAAJ,EAvBC;AAAA;;AAUhB;AAC7B,YAATC,SAAS,GAAG;AAAE,iBAAO,KAAKP,UAAZ;AAAyB;;AAEF;AAC5B,YAATQ,SAAS,GAAG;AAAE,iBAAO,KAAKP,UAAZ;AAAyB;;AAWlDQ,QAAAA,MAAM,GAAG;AACL,cAAMC,WAAW,GAAG,KAAKC,IAAL,CAAUC,YAAV,CAAuB3B,WAAvB,CAApB;AACA,cAAM4B,SAAS,GAAGH,WAAW,GAAGA,WAAW,CAACI,WAAf,GAA6B;AAAEC,YAAAA,KAAK,EAAE,CAAT;AAAYC,YAAAA,MAAM,EAAE;AAApB,WAA1D;;AACA,eAAKd,SAAL,CAAee,GAAf,CAAmBJ,SAAS,CAACE,KAAV,GAAkB,CAArC,EAAwCF,SAAS,CAACG,MAAV,GAAmB,CAA3D;AACH;;AAEDE,QAAAA,SAAS,GAAG;AACR;AACA,eAAKb,eAAL,CAAqBc,KAArB;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACWC,QAAAA,EAAE,CAACC,KAAD,EAAoBC,QAApB,EAAgD;AACrD,cAAI,CAAC,KAAKjB,eAAL,CAAqBkB,GAArB,CAAyBF,KAAzB,CAAL,EAAsC;AAClC,iBAAKhB,eAAL,CAAqBY,GAArB,CAAyBI,KAAzB,EAAgC,EAAhC;AACH;;AACD,cAAMG,SAAS,GAAG,KAAKnB,eAAL,CAAqBoB,GAArB,CAAyBJ,KAAzB,CAAlB;;AACA,cAAI,CAACG,SAAS,CAACE,QAAV,CAAmBJ,QAAnB,CAAL,EAAmC;AAC/BE,YAAAA,SAAS,CAACG,IAAV,CAAeL,QAAf;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACWM,QAAAA,GAAG,CAACP,KAAD,EAAoBC,QAApB,EAAgD;AACtD,cAAME,SAAS,GAAG,KAAKnB,eAAL,CAAqBoB,GAArB,CAAyBJ,KAAzB,CAAlB;;AACA,cAAIG,SAAJ,EAAe;AACX,gBAAMK,KAAK,GAAGL,SAAS,CAACM,OAAV,CAAkBR,QAAlB,CAAd;;AACA,gBAAIO,KAAK,KAAK,CAAC,CAAf,EAAkB;AACdL,cAAAA,SAAS,CAACO,MAAV,CAAiBF,KAAjB,EAAwB,CAAxB;AACH;AACJ;AACJ;;AAEMG,QAAAA,kBAAkB,GAAS;AAC9B,eAAK3B,eAAL,CAAqBc,KAArB;AACH;AAED;AACJ;AACA;AACA;;;AACcc,QAAAA,IAAI,CAACZ,KAAD,EAA0B;AACpC,cAAMG,SAAS,GAAG,KAAKnB,eAAL,CAAqBoB,GAArB,CAAyBJ,KAAzB,CAAlB;;AACA,cAAIG,SAAS,IAAIA,SAAS,CAACU,MAAV,GAAmB,CAApC,EAAuC;AACnCV,YAAAA,SAAS,CAACW,OAAV,CAAkBb,QAAQ,IAAIA,QAAQ,EAAtC;AACH;AACJ;;AAEMc,QAAAA,eAAe,GAAS;AAC3B;AACA,cAAI,EAAE,KAAKhC,uBAAP,IAAkCZ,QAAQ,CAAC6C,yBAA/C,EAA0E;AACtE,iBAAKjC,uBAAL,GAA+B,CAA/B,CADsE,CAEtE;AACA;;AACA,gBAAMkC,WAAW,GAAG;AAAA;AAAA,8CAAaC,WAAjC;AACA,gBAAMC,QAAQ,GAAG,KAAK7B,IAAL,CAAU8B,aAA3B;AACA,gBAAMlC,SAAS,GAAIiC,QAAQ,CAACE,CAAT,GAAa,KAAKxC,SAAL,CAAewC,CAA7B,IAAmCJ,WAAW,CAACK,IAA/C,IACbH,QAAQ,CAACE,CAAT,GAAa,KAAKxC,SAAL,CAAewC,CAA7B,IAAmCJ,WAAW,CAACM,IADjC,IAEbJ,QAAQ,CAACK,CAAT,GAAa,KAAK3C,SAAL,CAAe2C,CAA7B,IAAmCP,WAAW,CAACQ,IAFjC,IAGbN,QAAQ,CAACK,CAAT,GAAa,KAAK3C,SAAL,CAAe2C,CAA7B,IAAmCP,WAAW,CAACS,IAHnD,CANsE,CAWtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,iBAAKC,UAAL,CAAgBzC,SAAhB;AACH;AACJ;;AAESyC,QAAAA,UAAU,CAACC,OAAD,EAAmB;AACnC;AACA,cAAIA,OAAJ,EAAa;AACT,gBAAI,CAAC,KAAKlD,WAAV,EACI,KAAKkC,IAAL,CAAU1C,UAAU,CAAC2D,eAArB;AACP,WAHD,MAGO;AACH,gBAAI,KAAKnD,WAAT,EACI,KAAKkC,IAAL,CAAU1C,UAAU,CAAC4D,iBAArB;AACP;;AACD,eAAKpD,WAAL,GAAmB,KAAKC,UAAxB;AACA,eAAKA,UAAL,GAAkBiD,OAAlB;AACH;;AAtHgE,O,UAoBvCZ,yB,GAA4B,C;;;;;iBAlBT/C,oBAAoB,CAAC8D,E", "sourcesContent": ["import { _decorator, Component, Node, Enum, Vec2, Vec3, UITransform } from 'cc';\r\nimport { BulletSystem } from '../bullet/BulletSystem';\r\nconst { ccclass, property } = _decorator;\r\n\r\n/**\r\n * Base interface for all move-able objects\r\n */\r\nexport interface IMovable {\r\n    speed : number;                 // 速度\r\n    speedAngle : number;            // 速度方向 (用角度表示)\r\n    acceleration : number;          // 加速度\r\n    accelerationAngle : number;     // 加速度方向 (用角度表示)\r\n}\r\n\r\n// export enum eMoveModifier {\r\n//     Speed, SpeedAngle, Acceleration, AccelerationAngle\r\n// }\r\n\r\nexport enum eEasing {\r\n    Linear,\r\n    InSine, OutSine, InOutSine,\r\n    InQuad, OutQuad, InOutQuad\r\n}\r\n\r\nexport enum eSpriteDefaultFacing {\r\n    Right = 0,    // →\r\n    Up = -90,     // ↑\r\n    Down = 90,    // ↓\r\n    Left = 180    // ←\r\n}\r\n\r\nexport enum eMoveEvent {\r\n    onBecomeVisible,\r\n    onBecomeInvisible,\r\n}\r\n\r\nexport abstract class MoveBase extends Component implements IMovable {\r\n    @property({ type: Enum(eSpriteDefaultFacing), displayName: '图片默认朝向' })\r\n    public defaultFacing: eSpriteDefaultFacing = eSpriteDefaultFacing.Up;\r\n\r\n    public speed: number = 100;\r\n    public speedAngle: number = 0;\r\n    public acceleration: number = 0;\r\n    public accelerationAngle: number = 0;\r\n\r\n    protected _wasVisible: boolean = false;\r\n    protected _isVisible: boolean = false;           // 是否可见\r\n    public get isVisible() { return this._isVisible; }\r\n\r\n    protected _isMovable: boolean = true;           // 是否可移动\r\n    public get isMovable() { return this._isMovable; }\r\n\r\n    protected _selfSize: Vec2 = new Vec2();\r\n    protected _position: Vec3 = new Vec3();\r\n\r\n    protected _visibilityCheckCounter: number = 0;  // 可见性检查计数器\r\n    protected static readonly VISIBILITY_CHECK_INTERVAL = 5; // 每x帧检查一次可见性\r\n\r\n    // Event system:\r\n    protected _eventListeners: Map<eMoveEvent, Array<() => void>> = new Map();\r\n    \r\n    onLoad() {\r\n        const uiTransform = this.node.getComponent(UITransform);\r\n        const self_size = uiTransform ? uiTransform.contentSize : { width: 0, height: 0 };\r\n        this._selfSize.set(self_size.width / 2, self_size.height / 2);\r\n    }\r\n\r\n    onDestroy() {\r\n        // clear all event listeners\r\n        this._eventListeners.clear();\r\n    }\r\n\r\n    /**\r\n     * 添加事件监听器\r\n     * @param event 事件类型\r\n     * @param listener 监听器函数\r\n     */\r\n    public on(event: eMoveEvent, listener: () => void): void {\r\n        if (!this._eventListeners.has(event)) {\r\n            this._eventListeners.set(event, []);\r\n        }\r\n        const listeners = this._eventListeners.get(event)!;\r\n        if (!listeners.includes(listener)) {\r\n            listeners.push(listener);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 移除事件监听器\r\n     * @param event 事件类型\r\n     * @param listener 监听器函数\r\n     */\r\n    public off(event: eMoveEvent, listener: () => void): void {\r\n        const listeners = this._eventListeners.get(event);\r\n        if (listeners) {\r\n            const index = listeners.indexOf(listener);\r\n            if (index !== -1) {\r\n                listeners.splice(index, 1);\r\n            }\r\n        }\r\n    }\r\n\r\n    public removeAllListeners(): void {\r\n        this._eventListeners.clear();\r\n    }\r\n\r\n    /**\r\n     * 触发事件\r\n     * @param event 事件类型\r\n     */\r\n    protected emit(event: eMoveEvent): void {\r\n        const listeners = this._eventListeners.get(event);\r\n        if (listeners && listeners.length > 0) {\r\n            listeners.forEach(listener => listener());\r\n        }\r\n    }\r\n\r\n    public checkVisibility(): void {\r\n        // 降低可见性检查频率\r\n        if (++this._visibilityCheckCounter >= MoveBase.VISIBILITY_CHECK_INTERVAL) {\r\n            this._visibilityCheckCounter = 0;\r\n            // 这里目前的检查逻辑没有考虑旋转和缩放\r\n            // 正常来说需要判定world corners，如果四个角有一个在屏幕内，就认为是可见的\r\n            const visibleSize = BulletSystem.worldBounds;\r\n            const position = this.node.worldPosition;\r\n            const isVisible = (position.x + this._selfSize.x) >= visibleSize.xMin &&\r\n                (position.x - this._selfSize.x) <= visibleSize.xMax &&\r\n                (position.y - this._selfSize.y) <= visibleSize.yMax &&\r\n                (position.y + this._selfSize.y) >= visibleSize.yMin;\r\n\r\n            // debug visibility\r\n            // if (!isVisible) {\r\n            //     console.log(\"Movable\", \"checkVisibility\", this.node.name + \" is not visible\");\r\n            //     console.log(\"Movable\", \"checkLeftBound  :\", (this._position.x - this._selfSize.x) <= visibleSize.xMax, (this._position.x - this._selfSize.x), \"<=\", visibleSize.xMax);\r\n            //     console.log(\"Movable\", \"checkRightBound :\", (this._position.x + this._selfSize.x) >= visibleSize.xMin, (this._position.x + this._selfSize.x), \">=\", visibleSize.xMin);\r\n            //     console.log(\"Movable\", \"checkTopBound   :\", (this._position.y + this._selfSize.y) <= visibleSize.yMax, (this._position.y + this._selfSize.y), \"<=\", visibleSize.yMax);\r\n            //     console.log(\"Movable\", \"checkBottomBound:\", (this._position.y - this._selfSize.y) >= visibleSize.yMin, (this._position.y - this._selfSize.y), \">=\", visibleSize.yMin);\r\n            // }\r\n\r\n            this.setVisible(isVisible);\r\n        }\r\n    }\r\n\r\n    protected setVisible(visible: boolean) {\r\n        // console.log('setVisible: ', this._wasVisible, ', ', visible);\r\n        if (visible) {\r\n            if (!this._wasVisible)\r\n                this.emit(eMoveEvent.onBecomeVisible);\r\n        } else {\r\n            if (this._wasVisible)\r\n                this.emit(eMoveEvent.onBecomeInvisible);\r\n        }\r\n        this._wasVisible = this._isVisible;\r\n        this._isVisible = visible;\r\n    }\r\n}"]}