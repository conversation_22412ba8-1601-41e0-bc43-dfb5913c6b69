
import { Vec3 } from "cc";
import { SingletonBase } from "../../../../../scripts/core/base/SingletonBase";
import FCollider from "../collider-system/FCollider";
import { GameEnum } from "../const/GameEnum";
import { GameIns } from "../GameIns";
import BattleLayer from "../ui/layer/BattleLayer";

export class GamePlaneManager extends SingletonBase<GamePlaneManager> {
    enemyTarget: any = null;
    enemyCollider: FCollider | null = null;
    frienPlane: any[] = [];
    m_planeTable: any;
    enemyTargetPos = { x: 0, y: 0 };

    /**
     * 清理数据
     */
    clear() {
        this.enemyTarget = null;
        this.enemyCollider = null;
        this.frienPlane.forEach((plane) => {
            if (plane && plane.node) {
                plane.node.destroy();
            }
        });
        this.frienPlane = [];
    }

    /**
     * 获取飞机配置
     * @param id 配置 ID
     * @returns 配置数据
     */
    getConfig(id: number) {
        return this.m_planeTable.getRecorder(id);
    }

    /**
     * 添加友军飞机
     * @param plane 飞机对象
     */
    addFriendPlane(plane: any) {
        BattleLayer.instance.addFriendPlane(plane);
        this.frienPlane.push(plane);
    }

    /**
     * 移除友军飞机
     * @param plane 飞机对象
     */
    removeFriendPlane(plane: any) {
        const index = this.frienPlane.indexOf(plane);
        if (index >= 0) {
            this.frienPlane.splice(index, 1);
        }
        if (plane && plane.node) {
            plane.node.parent = null;
        }
    }




    /**
     * 获取两点之间的距离
     * @param point1 点1
     * @param point2 点2
     * @returns 距离
     */
    getLength(point1: Vec3, point2: Vec3): number {
        return Math.sqrt(
            (point1.x - point2.x) * (point1.x - point2.x) +
            (point1.y - point2.y) * (point1.y - point2.y)
        );
    }


    getRandomTargetEnemy(): FCollider | null {
        const targets: FCollider[] = [];

        let EnemyType = GameEnum.EnemyType;
        // 遍历敌机
        GameIns.enemyManager.planes.forEach((enemy) => {
            switch (enemy.type) {
                case EnemyType.Normal:
                    const normalCollider = enemy.getComponent(FCollider);
                    if (!GameIns.fColliderManager.isOutOfScreen(normalCollider?.aabb!)) {
                        targets.push(normalCollider!);
                    }
                    break;
            }
        });

        // 随机选择一个目标
        const randomIndex = Math.floor(GameIns.battleManager.random() * targets.length);
        return targets[randomIndex] || null;
    }

    getTargetEnemy(): void {
        const mainPlanePos = GameIns.mainPlaneManager.mainPlane!.node.position;
        let closestDistance = Infinity;
        let closestEnemy: any = null;
        let closestCollider: FCollider | null = null;

        let EnemyType = GameEnum.EnemyType;
        // 遍历敌机
        GameIns.enemyManager.planes.forEach((enemy) => {
            if (!enemy.isDead) {
                switch (enemy.type) {
                    case EnemyType.Normal:
                        if (enemy.colliderEnabled) {
                            const normalCollider = enemy.getComponent(FCollider);
                            if (!GameIns.fColliderManager.isOutOfScreen(normalCollider?.aabb!)) {
                                const enemyPos = enemy.node.position;
                                const distance = this.getLength(mainPlanePos, enemyPos);
                                if (distance < closestDistance) {
                                    closestDistance = distance;
                                    closestEnemy = enemy;
                                    closestCollider = enemy.getComponent(FCollider);
                                }
                            }
                        }
                        break;
                }
            }
        });

        // 更新目标敌人和碰撞器
        if (closestEnemy) {
            this.enemyTarget = closestEnemy;
            this.enemyTargetPos.x = closestEnemy.node.position.x;
            this.enemyTargetPos.y = closestEnemy.node.position.y;
            this.enemyCollider = closestCollider;
        }
    }

    /**
     * 更新函数
     * @param deltaTime 帧间隔时间
     */
    updateGameLogic(deltaTime: number) {
        this.getTargetEnemy();
    }
}