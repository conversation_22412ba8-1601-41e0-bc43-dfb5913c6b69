System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Vec2, CC<PERSON><PERSON>, CC<PERSON><PERSON><PERSON>, Enum, eOrientationType, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _dec9, _dec10, _dec11, _dec12, _dec13, _dec14, _class4, _class5, _descriptor8, _descriptor9, _descriptor10, _descriptor11, _descriptor12, _crd, ccclass, property, PathPoint, PathData;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfeOrientationType(extras) {
    _reporterNs.report("eOrientationType", "./WaveData", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Vec2 = _cc.Vec2;
      CCFloat = _cc.CCFloat;
      CCInteger = _cc.CCInteger;
      Enum = _cc.Enum;
    }, function (_unresolved_2) {
      eOrientationType = _unresolved_2.eOrientationType;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "762b8DNnsxGiYntrIbD801l", "PathData", undefined);

      __checkObsolete__(['_decorator', 'Vec2', 'CCFloat', 'CCInteger', 'Enum']);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * 路径点数据
       */

      _export("PathPoint", PathPoint = (_dec = ccclass("PathPoint"), _dec2 = property({
        type: CCFloat,
        displayName: "X坐标"
      }), _dec3 = property({
        type: CCFloat,
        displayName: "Y坐标"
      }), _dec4 = property({
        type: CCFloat,
        displayName: "平滑程度",
        range: [0, 1],
        slide: true,
        tooltip: "0=直线连接, 1=最大平滑曲线"
      }), _dec5 = property({
        type: CCInteger,
        displayName: "速度",
        tooltip: "飞机在此点的速度"
      }), _dec6 = property({
        type: CCInteger,
        displayName: "停留时间",
        tooltip: "飞机到达此点后停留时间（毫秒）"
      }), _dec7 = property({
        type: Enum(_crd && eOrientationType === void 0 ? (_reportPossibleCrUseOfeOrientationType({
          error: Error()
        }), eOrientationType) : eOrientationType),
        displayName: "朝向类型",
        tooltip: "飞机在此点的朝向"
      }), _dec8 = property({
        type: CCInteger,
        displayName: "朝向参数",
        tooltip: "根据朝向类型不同而不同"
      }), _dec(_class = (_class2 = class PathPoint {
        get isSubdivided() {
          return this._isSubdivided;
        }

        set isSubdivided(value) {
          this._isSubdivided = value;
        }

        constructor(x, y) {
          if (x === void 0) {
            x = 0;
          }

          if (y === void 0) {
            y = 0;
          }

          _initializerDefineProperty(this, "x", _descriptor, this);

          _initializerDefineProperty(this, "y", _descriptor2, this);

          _initializerDefineProperty(this, "smoothness", _descriptor3, this);

          _initializerDefineProperty(this, "speed", _descriptor4, this);

          _initializerDefineProperty(this, "stayDuration", _descriptor5, this);

          _initializerDefineProperty(this, "orientationType", _descriptor6, this);

          _initializerDefineProperty(this, "orientationParam", _descriptor7, this);

          // 标记是否是插值的点（非原始点）
          this._isSubdivided = false;
          this.x = x;
          this.y = y;
        }

        get position() {
          return new Vec2(this.x, this.y);
        }

        set position(value) {
          this.x = value.x;
          this.y = value.y;
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "x", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "y", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "smoothness", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 1;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "speed", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 500;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "stayDuration", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "orientationType", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor7 = _applyDecoratedDescriptor(_class2.prototype, "orientationParam", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      })), _class2)) || _class));
      /**
       * 路径数据
       */


      _export("PathData", PathData = (_dec9 = ccclass("PathData"), _dec10 = property({
        displayName: '路径名称',
        editorOnly: true
      }), _dec11 = property({
        type: CCInteger,
        displayName: '起始点(默认0)'
      }), _dec12 = property({
        type: CCInteger,
        displayName: '结束点(-1代表使用路径终点)'
      }), _dec13 = property({
        type: [PathPoint],
        displayName: '路径点'
      }), _dec14 = property({
        displayName: "是否闭合路径",
        tooltip: "路径是否形成闭环"
      }), _dec9(_class4 = (_class5 = class PathData {
        constructor() {
          _initializerDefineProperty(this, "name", _descriptor8, this);

          _initializerDefineProperty(this, "startIdx", _descriptor9, this);

          _initializerDefineProperty(this, "endIdx", _descriptor10, this);

          _initializerDefineProperty(this, "points", _descriptor11, this);

          _initializerDefineProperty(this, "closed", _descriptor12, this);

          // 缓存的路径数据（不参与序列化）
          this._cachedSubdividedPoints = null;
        }

        /**
         * 获取Catmull-Rom曲线上的点
         * @param t 参数值 [0, 1]
         * @param p0 前一个控制点（用于计算切线）
         * @param p1 起始点（曲线经过此点）
         * @param p2 结束点（曲线经过此点）
         * @param p3 后一个控制点（用于计算切线）
         * @param smoothness 平滑程度 [0, 1]，0=直线，1=最平滑曲线
         */
        static catmullRomPoint(t, p0, p1, p2, p3, smoothness) {
          if (smoothness === void 0) {
            smoothness = 0.5;
          }

          // 当smoothness为0时，直接返回线性插值（直线）
          if (smoothness === 0) {
            return Vec2.lerp(new Vec2(), p1, p2, t);
          }

          var t2 = t * t;
          var t3 = t2 * t; // 标准Catmull-Rom插值公式

          var catmullRom = new Vec2();
          catmullRom.x = 0.5 * (2 * p1.x + (-p0.x + p2.x) * t + (2 * p0.x - 5 * p1.x + 4 * p2.x - p3.x) * t2 + (-p0.x + 3 * p1.x - 3 * p2.x + p3.x) * t3);
          catmullRom.y = 0.5 * (2 * p1.y + (-p0.y + p2.y) * t + (2 * p0.y - 5 * p1.y + 4 * p2.y - p3.y) * t2 + (-p0.y + 3 * p1.y - 3 * p2.y + p3.y) * t3); // 当smoothness不为1时，在线性插值和Catmull-Rom之间混合

          if (smoothness < 1) {
            var linear = Vec2.lerp(new Vec2(), p1, p2, t);
            return Vec2.lerp(new Vec2(), linear, catmullRom, smoothness);
          }

          return catmullRom;
        }
        /**
         * 获取细分后的路径点（包含完整的PathPoint信息）
         * 这是推荐的新方法，替代generateCurvePoints + 重新采样的方式
         */


        getSubdividedPoints(regen) {
          if (regen === void 0) {
            regen = false;
          }

          if (!this._cachedSubdividedPoints || regen) {
            this._cachedSubdividedPoints = this.generateSubdividedPointsInternal();
          }

          return this._cachedSubdividedPoints;
        }
        /**
         * 内部方法：生成细分后的PathPoint数组
         */


        generateSubdividedPointsInternal() {
          var effectivePoints = this.getPoints();

          if (effectivePoints.length < 2) {
            return effectivePoints;
          }

          var subdivided = [];
          var pointCount = effectivePoints.length; // 添加第一个点

          var firstPoint = effectivePoints[0];
          subdivided.push(firstPoint); // 计算需要处理的段数

          var segmentCount = this.closed ? pointCount : pointCount - 1; // 为每一段生成细分点

          for (var i = 0; i < segmentCount; i++) {
            var p0 = this.getControlPoint(effectivePoints, i - 1);
            var p1 = effectivePoints[i].position;
            var p2 = this.getControlPoint(effectivePoints, i + 1);
            var p3 = this.getControlPoint(effectivePoints, i + 2);
            var point = effectivePoints[i];
            var pointNext = effectivePoints[(i + 1) % pointCount];
            var startSmoothness = point.smoothness;
            var endSmoothness = pointNext.smoothness; // 如果任一端点的smoothness为0，则整段使用直线

            if (startSmoothness === 0 || endSmoothness === 0) {
              // 直线连接：只需要添加终点
              subdivided.push(pointNext);
            } else {
              // 使用自适应细分算法
              var segmentPoints = this.adaptiveSubdivision(p0, p1, p2, p3, point, pointNext);
              subdivided.push(...segmentPoints);
            }
          } // 处理闭合路径的重复点


          if (this.closed && subdivided.length > 1) {
            var _firstPoint = subdivided[0];
            var lastPoint = subdivided[subdivided.length - 1];
            var distance = Vec2.distance(_firstPoint.position, lastPoint.position);

            if (distance < 0.1) {
              subdivided.pop();
            }
          }

          return subdivided;
        }
        /**
         * 获取有效的路径点范围（考虑startIdx和endIdx）
         */


        getPoints() {
          if (this.points.length === 0) return [];
          var startIndex = Math.max(0, Math.min(this.startIdx, this.points.length - 1));
          var endIndex = this.endIdx === -1 ? this.points.length - 1 : Math.max(startIndex, Math.min(this.endIdx, this.points.length - 1));
          return this.points.slice(startIndex, endIndex + 1);
        }
        /**
         * 自适应细分算法 - 基于曲率和误差的智能细分
         * @param p0 前一个控制点
         * @param p1 起始点
         * @param p2 结束点
         * @param p3 后一个控制点
         * @param point1 起始PathPoint
         * @param point2 结束PathPoint
         * @returns 细分后的PathPoint数组
         */


        adaptiveSubdivision(p0, p1, p2, p3, point1, point2, maxDepth) {
          if (maxDepth === void 0) {
            maxDepth = 6;
          }

          var avgSmoothness = (point1.smoothness + point2.smoothness) / 2; // 如果平滑度为0，直接返回终点

          if (avgSmoothness === 0) {
            return [point2];
          } // 递归细分（从深度0开始）


          return this.subdivideRecursive(p0, p1, p2, p3, point1, point2, 0, 1, 0, maxDepth, avgSmoothness);
        }
        /**
         * 递归细分方法
         */


        subdivideRecursive(p0, p1, p2, p3, point1, point2, t1, t2, depth, maxDepth, smoothness) {
          // 达到最大深度，停止细分
          if (depth >= maxDepth) {
            return [this.createCurveInterpolatedPoint(p0, p1, p2, p3, point1, point2, t2, smoothness)];
          }

          var tMid = (t1 + t2) / 2; // 计算三个点：起点、中点、终点

          var startPos = PathData.catmullRomPoint(t1, p0, p1, p2, p3, smoothness);
          var midPos = PathData.catmullRomPoint(tMid, p0, p1, p2, p3, smoothness);
          var endPos = PathData.catmullRomPoint(t2, p0, p1, p2, p3, smoothness); // 计算线性插值的中点

          var linearMid = Vec2.lerp(new Vec2(), startPos, endPos, 0.5); // 计算误差（曲线中点与线性中点的距离）

          var error = Vec2.distance(midPos, linearMid); // 计算曲率（使用三点法）

          var curvature = this.calculateCurvature(startPos, midPos, endPos); // 动态误差阈值：考虑距离和曲率

          var distance = Vec2.distance(startPos, endPos);
          var baseThreshold = Math.max(0.5, distance * 0.01); // 基础阈值

          var curvatureThreshold = baseThreshold * (1 + curvature * 10); // 曲率调整
          // console.log('error:', error, 'curvatureThreshold:', curvatureThreshold);
          // 如果误差小于阈值，不需要进一步细分

          if (error < curvatureThreshold) {
            return [this.createCurveInterpolatedPoint(p0, p1, p2, p3, point1, point2, t2, smoothness)];
          } // 需要细分：递归处理两个子段


          var leftPoints = this.subdivideRecursive(p0, p1, p2, p3, point1, point2, t1, tMid, depth + 1, maxDepth, smoothness);
          var rightPoints = this.subdivideRecursive(p0, p1, p2, p3, point1, point2, tMid, t2, depth + 1, maxDepth, smoothness);
          return [...leftPoints, ...rightPoints];
        }
        /**
         * 计算三点的曲率
         */


        calculateCurvature(p1, p2, p3) {
          var v1 = Vec2.subtract(new Vec2(), p2, p1);
          var v2 = Vec2.subtract(new Vec2(), p3, p2); // 避免除零

          var len1 = v1.length();
          var len2 = v2.length();
          if (len1 < 0.001 || len2 < 0.001) return 0;
          v1.normalize();
          v2.normalize(); // 计算角度变化

          var dot = Vec2.dot(v1, v2);
          var clampedDot = Math.max(-1, Math.min(1, dot));
          var angle = Math.acos(clampedDot); // 归一化曲率值

          return angle / Math.PI;
        }
        /**
         * 创建曲线插值的PathPoint（使用Catmull-Rom曲线）
         */


        createCurveInterpolatedPoint(p0, p1, p2, p3, point1, point2, t, smoothness) {
          // 使用Catmull-Rom曲线计算位置
          var pos = PathData.catmullRomPoint(t, p0, p1, p2, p3, smoothness);
          var newPoint = new PathPoint(pos.x, pos.y); // 插值其他属性

          newPoint.speed = point1.speed + (point2.speed - point1.speed) * t;
          newPoint.smoothness = point1.smoothness + (point2.smoothness - point1.smoothness) * t;
          newPoint.orientationType = point1.orientationType;
          newPoint.orientationParam = point1.orientationParam + (point2.orientationParam - point1.orientationParam) * t;
          newPoint.isSubdivided = true;
          return newPoint;
        }
        /**
         * 获取有效路径点的控制点（处理边界情况）
         */


        getControlPoint(effectivePoints, index) {
          var pointCount = effectivePoints.length;

          if (this.closed) {
            // 闭合路径，使用循环索引
            var wrappedIndex = (index % pointCount + pointCount) % pointCount;
            return effectivePoints[wrappedIndex].position;
          } else {
            // 开放路径，边界处理
            if (index < 0) {
              // 延伸第一个点
              var p0 = effectivePoints[0].position;
              var p1 = effectivePoints[1].position;
              return Vec2.subtract(new Vec2(), p0, Vec2.subtract(new Vec2(), p1, p0));
            } else if (index >= pointCount) {
              // 延伸最后一个点
              var _p = effectivePoints[pointCount - 2].position;
              var _p2 = effectivePoints[pointCount - 1].position;
              return Vec2.add(new Vec2(), _p2, Vec2.subtract(new Vec2(), _p2, _p));
            } else {
              return effectivePoints[index].position;
            }
          }
        }
        /**
         * 获取控制点（处理边界情况）- 保留用于兼容性
         */
        // private getControlPoint(index: number): Vec2 {
        //     const pointCount = this.points.length;
        //     if (this.closed) {
        //         // 闭合路径，使用循环索引
        //         const wrappedIndex = ((index % pointCount) + pointCount) % pointCount;
        //         return this.points[wrappedIndex].position;
        //     } else {
        //         // 开放路径，边界处理
        //         if (index < 0) {
        //             // 延伸第一个点
        //             const p0 = this.points[0].position;
        //             const p1 = this.points[1].position;
        //             return Vec2.subtract(new Vec2(), p0, Vec2.subtract(new Vec2(), p1, p0));
        //         } else if (index >= pointCount) {
        //             // 延伸最后一个点
        //             const p0 = this.points[pointCount - 2].position;
        //             const p1 = this.points[pointCount - 1].position;
        //             return Vec2.add(new Vec2(), p1, Vec2.subtract(new Vec2(), p1, p0));
        //         } else {
        //             return this.points[index].position;
        //         }
        //     }
        // }

        /**
         * 自定义序列化 - 排除缓存数据
         */


        toJSON() {
          return {
            name: this.name,
            startIdx: this.startIdx,
            endIdx: this.endIdx,
            points: this.points,
            closed: this.closed
          };
        }
        /**
         * 自定义反序列化 - 清除缓存确保重新计算
         */


        fromJSON(data) {
          this.name = data.name || "";
          this.startIdx = data.startIdx || 0;
          this.endIdx = data.endIdx || -1;
          this.points = data.points || [];
          this.closed = data.closed || false; // 清除缓存，确保使用新数据重新计算

          this._cachedSubdividedPoints = null;
        }
        /**
         * 静态工厂方法 - 从JSON创建PathData实例
         */


        static fromJSON(data) {
          var pathData = new PathData();
          pathData.fromJSON(data);
          return pathData;
        }

      }, (_descriptor8 = _applyDecoratedDescriptor(_class5.prototype, "name", [_dec10], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return "";
        }
      }), _descriptor9 = _applyDecoratedDescriptor(_class5.prototype, "startIdx", [_dec11], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor10 = _applyDecoratedDescriptor(_class5.prototype, "endIdx", [_dec12], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return -1;
        }
      }), _descriptor11 = _applyDecoratedDescriptor(_class5.prototype, "points", [_dec13], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return [];
        }
      }), _descriptor12 = _applyDecoratedDescriptor(_class5.prototype, "closed", [_dec14], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return false;
        }
      })), _class5)) || _class4));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=bb9b33b2273a99d958fbdd91f2e7ce7081eb88a7.js.map