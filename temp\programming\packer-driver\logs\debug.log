10:36:57.149 debug: 2025/9/29 10:36:57
10:36:57.149 debug: Project: E:\M2Game\Client
10:36:57.150 debug: Targets: editor,preview
10:36:57.167 debug: Incremental file seems great.
10:36:57.168 debug: Engine path: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine
10:36:57.176 debug: Initializing target [Editor]
10:36:57.177 debug: Loading cache
10:36:57.188 debug: Loading cache costs 11.043299999999817ms.
10:36:57.188 debug: Engine features shipped in editor: base,gfx-webgl,gfx-webgl2,gfx-empty,gfx-webgpu,3d,animation,skeletal-animation,2d,rich-text,mask,graphics,ui-skew,ui,affine-transform,particle,particle-2d,physics-framework,physics-cannon,physics-physx,physics-ammo,physics-builtin,physics-2d-framework,physics-2d-box2d-jsb,physics-2d-box2d,physics-2d-builtin,physics-2d-box2d-wasm,intersection-2d,primitive,profiler,occlusion-query,geometry-renderer,debug-renderer,audio,video,xr,light-probe,terrain,webview,tween,tiled-map,vendor-google,spine-3.8,spine-4.2,dragon-bones,marionette,procedural-animation,custom-pipeline,custom-pipeline-builtin-scripts,custom-pipeline-post-process,legacy-pipeline,websocket,websocket-server,meshopt
10:36:57.188 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc"
  }
}
10:36:57.188 debug: Initializing target [Preview]
10:36:57.189 debug: Loading cache
10:36:57.196 debug: Loading cache costs 7.286799999999857ms.
10:36:57.196 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc"
  }
}
10:36:57.223 debug: Sync engine features: 2d,3d,affine-transform,animation,audio,base,custom-pipeline,debug-renderer,gfx-webgl,gfx-webgl2,graphics,intersection-2d,marionette,mask,meshopt,particle-2d,physics-2d-builtin,profiler,rich-text,skeletal-animation,spine-3.8,tween,ui,video,websocket,webview,custom-pipeline
10:36:57.226 debug: Reset databases. Enumerated domains: [
  {
    "root": "db://internal/",
    "physical": "C:\\ProgramData\\cocos\\editors\\Creator\\3.8.6\\resources\\resources\\3d\\engine\\editor\\assets"
  },
  {
    "root": "db://assets/",
    "physical": "E:\\M2Game\\Client\\assets"
  },
  {
    "root": "db://i18n/",
    "physical": "E:\\M2Game\\Client\\extensions\\i18n\\assets",
    "jail": "E:\\M2Game\\Client\\extensions\\i18n\\assets"
  }
]
10:36:57.226 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc",
    "db://internal/": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/",
    "db://assets/": "file:///E:/M2Game/Client/assets/",
    "db://i18n/": "file:///E:/M2Game/Client/extensions/i18n/assets/"
  }
}
10:36:57.227 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc",
    "db://internal/": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/",
    "db://assets/": "file:///E:/M2Game/Client/assets/",
    "db://i18n/": "file:///E:/M2Game/Client/extensions/i18n/assets/"
  }
}
10:36:57.228 debug: Pulling asset-db.
10:36:57.265 debug: Fetch asset-db cost: 36.9078999999997ms.
10:36:57.265 debug: Build iteration starts.
Number of accumulated asset changes: 277
Feature changed: false
10:36:57.266 debug: Target(editor) build started.
10:36:57.268 debug: Detected change: cce:/internal/x/cc. Last mtime: Thu Jan 01 1970 08:00:04 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:03 GMT+0800 (中国标准时间)
