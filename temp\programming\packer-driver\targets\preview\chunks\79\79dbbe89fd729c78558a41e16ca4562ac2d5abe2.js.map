{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/scenes/GameMain.ts"], "names": ["_decorator", "Component", "EPhysics2DDrawFlags", "find", "Node", "PhysicsSystem2D", "ResolutionPolicy", "view", "GameConst", "UIMgr", "EventManager", "MBoomUI", "GameIns", "BulletSystem", "GameEvent", "BattleLayer", "GameFightUI", "ccclass", "property", "GameMain", "onLoad", "instance", "CoverBg", "active", "bulletParent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fitType", "SHOW_ALL", "setResolutionPolicy", "resizeWithBrowserSize", "fColliderManager", "enable", "setGlobalColliderEnterCall", "colliderA", "colliderB", "entity", "onCollide", "ColliderDraw", "debugDrawFlags", "<PERSON><PERSON><PERSON>", "onEnable", "Instance", "on", "GameLoadEnd", "onEventGameLoadEnd", "onDisable", "off", "node", "start", "battleManager", "startLoading", "openUI", "showGameResult", "isSuccess", "gameEnd", "LabelWin", "LabelFail", "onBtnAgainClicked", "quitBattle", "update", "deltaTime", "updateGameLogic"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,mB,OAAAA,mB;AAAqBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,e,OAAAA,e;AAAiBC,MAAAA,gB,OAAAA,gB;AAAkBC,MAAAA,I,OAAAA,I;;AAC3FC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,K,iBAAAA,K;;AACFC,MAAAA,Y;;AACEC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Y,iBAAAA,Y;;AAEAC,MAAAA,S,iBAAAA,S;;AACFC,MAAAA,W;;AACEC,MAAAA,W,kBAAAA,W;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBlB,U;;0BAGjBmB,Q,WADZF,OAAO,CAAC,UAAD,C,UAIHC,QAAQ;AAAA;AAAA,qC,UAGRA,QAAQ,CAACd,IAAD,C,UAERc,QAAQ,CAACd,IAAD,C,UAERc,QAAQ;AAAA;AAAA,qC,sCAXb,MACaC,QADb,SAC8BlB,SAD9B,CACwC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAe1BmB,QAAAA,MAAM,GAAS;AACrBD,UAAAA,QAAQ,CAACE,QAAT,GAAoB,IAApB;AACA,eAAKC,OAAL,CAAcC,MAAd,GAAuB,IAAvB;AACA;AAAA;AAAA,4CAAaC,YAAb,GAA4B,KAAKT,WAAL,CAAkBU,eAA9C,CAHqB,CAG0C;AAE/D;;AACA,cAAIC,OAAO,GAAGpB,gBAAgB,CAACqB,QAA/B,CANqB,CAOrB;AACA;AACA;AACA;AACA;;AACApB,UAAAA,IAAI,CAACqB,mBAAL,CAAyBF,OAAzB;AACAnB,UAAAA,IAAI,CAACsB,qBAAL,CAA2B,IAA3B;AAEA;AAAA;AAAA,kCAAQC,gBAAR,CAAyBC,MAAzB,GAAkC,IAAlC;AACA;AAAA;AAAA,kCAAQD,gBAAR,CAAyBE,0BAAzB,CAAoD,CAACC,SAAD,EAAuBC,SAAvB,KAAgD;AAAA;;AAChG,iCAAAD,SAAS,CAACE,MAAV,+BAAkBC,SAAlB,8BAAkBA,SAAlB,CAA8BF,SAA9B;AACA,iCAAAA,SAAS,CAACC,MAAV,+BAAkBC,SAAlB,8BAAkBA,SAAlB,CAA8BH,SAA9B;AACH,WAHD;;AAKA,cAAI;AAAA;AAAA,sCAAUI,YAAd,EAA4B;AACxBhC,YAAAA,eAAe,CAACgB,QAAhB,CAAyBU,MAAzB,GAAkC,IAAlC;AACA1B,YAAAA,eAAe,CAACgB,QAAhB,CAAyBiB,cAAzB,GAA0CpC,mBAAmB,CAACqC,IAA9D;AACH;AACJ;;AAESC,QAAAA,QAAQ,GAAS;AACvB;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,EAAtB,CAAyB;AAAA;AAAA,sCAAUC,WAAnC,EAAgD,KAAKC,kBAArD,EAAyE,IAAzE;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,4CAAaJ,QAAb,CAAsBK,GAAtB,CAA0B;AAAA;AAAA,sCAAUH,WAApC,EAAiD,KAAKC,kBAAtD,EAA0E,IAA1E;AACH;;AAEDA,QAAAA,kBAAkB,GAAG;AACjB;AACA,eAAK5B,WAAL,CAAkB+B,IAAlB,CAAuBxB,MAAvB,GAAgC,IAAhC;AACH;;AAEDyB,QAAAA,KAAK,GAAG;AACJ;AAAA;AAAA,kCAAQC,aAAR,CAAsBC,YAAtB;AACA;AAAA;AAAA,8BAAMC,MAAN;AAAA;AAAA;AACH;;AAEDC,QAAAA,cAAc,CAACC,SAAD,EAAqB;AAC/B,eAAKC,OAAL,CAAc/B,MAAd,GAAuB,IAAvB;AAEA,cAAIgC,QAAQ,GAAGpD,IAAI,CAAC,UAAD,EAAa,KAAKmD,OAAlB,CAAnB;AACA,cAAIE,SAAS,GAAGrD,IAAI,CAAC,WAAD,EAAc,KAAKmD,OAAnB,CAApB;AACAC,UAAAA,QAAQ,CAAEhC,MAAV,GAAmB8B,SAAnB;AACAG,UAAAA,SAAS,CAAEjC,MAAX,GAAoB,CAAC8B,SAArB;AACH;;AAEKI,QAAAA,iBAAiB,GAAG;AAAA;;AAAA;AACtB,YAAA,KAAI,CAACH,OAAL,CAAc/B,MAAd,GAAuB,KAAvB;AACA;AAAA;AAAA,oCAAQ0B,aAAR,CAAsBS,UAAtB;AAFsB;AAGzB;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,MAAM,CAACC,SAAD,EAA0B;AAC5B;AAAA;AAAA,kCAAQX,aAAR,CAAsBY,eAAtB,CAAsCD,SAAtC;AACH;;AAhFmC,O,UAc7BvC,Q;;;;;iBAV2B,I;;;;;;;iBAGX,I;;;;;;;iBAEA,I;;;;;;;iBAEW,I", "sourcesContent": ["import { _decorator, Component, EPhysics2DDrawFlags, find, Node, PhysicsSystem2D, ResolutionPolicy, view } from 'cc';\r\nimport { GameConst } from 'db://assets/scripts/core/base/GameConst';\r\nimport { UIMgr } from 'db://assets/scripts/core/base/UIMgr';\r\nimport EventManager from '../../event/EventManager';\r\nimport { MBoomUI } from '../../ui/gameui/game/MBoomUI';\r\nimport { GameIns } from '../GameIns';\r\nimport { BulletSystem } from '../bullet/BulletSystem';\r\nimport FCollider from '../collider-system/FCollider';\r\nimport { GameEvent } from '../event/GameEvent';\r\nimport BattleLayer from '../ui/layer/BattleLayer';\r\nimport { GameFightUI } from '../ui/layer/GameFightUI';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('GameMain')\r\nexport class GameMain extends Component {\r\n\r\n\r\n    @property(BattleLayer)\r\n    BattleLayer: BattleLayer | null = null;\r\n\r\n    @property(Node)\r\n    gameEnd: Node | null = null;\r\n    @property(Node)\r\n    CoverBg: Node | null = null;\r\n    @property(GameFightUI)\r\n    GameFightUI: GameFightUI | null = null;\r\n\r\n\r\n    static instance: GameMain;\r\n    protected onLoad(): void {\r\n        GameMain.instance = this;\r\n        this.CoverBg!.active = true;\r\n        BulletSystem.bulletParent = this.BattleLayer!.selfBulletLayer!;//设置子弹父节点\r\n\r\n        // 设置分辨率适配策略\r\n        let fitType = ResolutionPolicy.SHOW_ALL\r\n        // if (view.getVisibleSize().height / view.getVisibleSize().width * GameConst.designWidth >= GameConst.designHeight){//高度大于 16:9\r\n        //     fitType = ResolutionPolicy.SHOW_ALL\r\n        // }else{//宽屏,比如平板或者电脑\r\n        //     fitType = ResolutionPolicy.FIXED_HEIGHT\r\n        // }\r\n        view.setResolutionPolicy(fitType);\r\n        view.resizeWithBrowserSize(true);\r\n\r\n        GameIns.fColliderManager.enable = true;\r\n        GameIns.fColliderManager.setGlobalColliderEnterCall((colliderA: FCollider, colliderB: FCollider) => {\r\n            colliderA.entity?.onCollide?.(colliderB);\r\n            colliderB.entity?.onCollide?.(colliderA);\r\n        });\r\n\r\n        if (GameConst.ColliderDraw) {\r\n            PhysicsSystem2D.instance.enable = true;\r\n            PhysicsSystem2D.instance.debugDrawFlags = EPhysics2DDrawFlags.Aabb;\r\n        }\r\n    }\r\n\r\n    protected onEnable(): void {\r\n        EventManager.Instance.on(GameEvent.GameLoadEnd, this.onEventGameLoadEnd, this);\r\n    }\r\n\r\n    protected onDisable(): void {\r\n        EventManager.Instance.off(GameEvent.GameLoadEnd, this.onEventGameLoadEnd, this);\r\n    }\r\n\r\n    onEventGameLoadEnd() {\r\n        //由于gameui,使用了独立的摄影机，层级太高，会在loading界面之上，所以先隐藏，加载完，再显示\r\n        this.GameFightUI!.node.active = true;\r\n    }\r\n\r\n    start() {\r\n        GameIns.battleManager.startLoading();\r\n        UIMgr.openUI(MBoomUI)\r\n    }\r\n\r\n    showGameResult(isSuccess: boolean) {\r\n        this.gameEnd!.active = true;\r\n\r\n        let LabelWin = find(\"LabelWin\", this.gameEnd!);\r\n        let LabelFail = find(\"LabelFail\", this.gameEnd!);\r\n        LabelWin!.active = isSuccess;\r\n        LabelFail!.active = !isSuccess;\r\n    }\r\n\r\n    async onBtnAgainClicked() {\r\n        this.gameEnd!.active = false;\r\n        GameIns.battleManager.quitBattle();\r\n    }\r\n\r\n    /**\r\n     * 每帧更新逻辑\r\n     * @param deltaTime 时间增量\r\n     */\r\n    update(deltaTime: number): void {\r\n        GameIns.battleManager.updateGameLogic(deltaTime);\r\n    }\r\n}\r\n\r\n\r\n"]}