{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/scenes/GameMain.ts"], "names": ["_decorator", "Component", "EPhysics2DDrawFlags", "find", "Node", "PhysicsSystem2D", "GameConst", "UIMgr", "MyApp", "EventManager", "MBoomUI", "GameIns", "BulletSystem", "GameEvent", "BattleLayer", "GameFightUI", "ccclass", "property", "GameMain", "onLoad", "instance", "CoverBg", "active", "bulletParent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "globalMgr", "setBattleResolution", "fColliderManager", "enable", "setGlobalColliderEnterCall", "colliderA", "colliderB", "entity", "onCollide", "ColliderDraw", "debugDrawFlags", "<PERSON><PERSON><PERSON>", "onEnable", "Instance", "on", "GameLoadEnd", "onEventGameLoadEnd", "onDisable", "off", "node", "start", "battleManager", "startLoading", "openUI", "showGameResult", "isSuccess", "gameEnd", "LabelWin", "LabelFail", "onBtnAgainClicked", "quitBattle", "update", "deltaTime", "updateGameLogic"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,mB,OAAAA,mB;AAAqBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,e,OAAAA,e;;AACxDC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,K,iBAAAA,K;;AACFC,MAAAA,Y;;AACEC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Y,iBAAAA,Y;;AAEAC,MAAAA,S,iBAAAA,S;;AACFC,MAAAA,W;;AACEC,MAAAA,W,kBAAAA,W;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBjB,U;;0BAGjBkB,Q,WADZF,OAAO,CAAC,UAAD,C,UAIHC,QAAQ;AAAA;AAAA,qC,UAGRA,QAAQ,CAACb,IAAD,C,UAERa,QAAQ,CAACb,IAAD,C,UAERa,QAAQ;AAAA;AAAA,qC,sCAXb,MACaC,QADb,SAC8BjB,SAD9B,CACwC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAe1BkB,QAAAA,MAAM,GAAS;AACrBD,UAAAA,QAAQ,CAACE,QAAT,GAAoB,IAApB;AACA,eAAKC,OAAL,CAAcC,MAAd,GAAuB,IAAvB;AACA;AAAA;AAAA,4CAAaC,YAAb,GAA4B,KAAKT,WAAL,CAAkBU,eAA9C,CAHqB,CAG0C;;AAE/D;AAAA;AAAA,8BAAMC,SAAN,CAAgBC,mBAAhB;AAEA;AAAA;AAAA,kCAAQC,gBAAR,CAAyBC,MAAzB,GAAkC,IAAlC;AACA;AAAA;AAAA,kCAAQD,gBAAR,CAAyBE,0BAAzB,CAAoD,CAACC,SAAD,EAAuBC,SAAvB,KAAgD;AAAA;;AAChG,iCAAAD,SAAS,CAACE,MAAV,+BAAkBC,SAAlB,8BAAkBA,SAAlB,CAA8BF,SAA9B;AACA,iCAAAA,SAAS,CAACC,MAAV,+BAAkBC,SAAlB,8BAAkBA,SAAlB,CAA8BH,SAA9B;AACH,WAHD;;AAKA,cAAI;AAAA;AAAA,sCAAUI,YAAd,EAA4B;AACxB7B,YAAAA,eAAe,CAACe,QAAhB,CAAyBQ,MAAzB,GAAkC,IAAlC;AACAvB,YAAAA,eAAe,CAACe,QAAhB,CAAyBe,cAAzB,GAA0CjC,mBAAmB,CAACkC,IAA9D;AACH;AACJ;;AAESC,QAAAA,QAAQ,GAAS;AACvB;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,EAAtB,CAAyB;AAAA;AAAA,sCAAUC,WAAnC,EAAgD,KAAKC,kBAArD,EAAyE,IAAzE;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,4CAAaJ,QAAb,CAAsBK,GAAtB,CAA0B;AAAA;AAAA,sCAAUH,WAApC,EAAiD,KAAKC,kBAAtD,EAA0E,IAA1E;AACH;;AAEDA,QAAAA,kBAAkB,GAAG;AACjB;AACA,eAAK1B,WAAL,CAAkB6B,IAAlB,CAAuBtB,MAAvB,GAAgC,IAAhC;AACH;;AAEDuB,QAAAA,KAAK,GAAG;AACJ;AAAA;AAAA,kCAAQC,aAAR,CAAsBC,YAAtB;AACA;AAAA;AAAA,8BAAMC,MAAN;AAAA;AAAA;AACH;;AAEDC,QAAAA,cAAc,CAACC,SAAD,EAAqB;AAC/B,eAAKC,OAAL,CAAc7B,MAAd,GAAuB,IAAvB;AAEA,cAAI8B,QAAQ,GAAGjD,IAAI,CAAC,UAAD,EAAa,KAAKgD,OAAlB,CAAnB;AACA,cAAIE,SAAS,GAAGlD,IAAI,CAAC,WAAD,EAAc,KAAKgD,OAAnB,CAApB;AACAC,UAAAA,QAAQ,CAAE9B,MAAV,GAAmB4B,SAAnB;AACAG,UAAAA,SAAS,CAAE/B,MAAX,GAAoB,CAAC4B,SAArB;AACH;;AAEKI,QAAAA,iBAAiB,GAAG;AAAA;;AAAA;AACtB,YAAA,KAAI,CAACH,OAAL,CAAc7B,MAAd,GAAuB,KAAvB;AACA;AAAA;AAAA,oCAAQwB,aAAR,CAAsBS,UAAtB;AAFsB;AAGzB;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,MAAM,CAACC,SAAD,EAA0B;AAC5B;AAAA;AAAA,kCAAQX,aAAR,CAAsBY,eAAtB,CAAsCD,SAAtC;AACH;;AAxEmC,O,UAc7BrC,Q;;;;;iBAV2B,I;;;;;;;iBAGX,I;;;;;;;iBAEA,I;;;;;;;iBAEW,I", "sourcesContent": ["import { _decorator, Component, EPhysics2DDrawFlags, find, Node, PhysicsSystem2D } from 'cc';\r\nimport { GameConst } from 'db://assets/scripts/core/base/GameConst';\r\nimport { UIMgr } from 'db://assets/scripts/core/base/UIMgr';\r\nimport { MyApp } from '../../app/MyApp';\r\nimport EventManager from '../../event/EventManager';\r\nimport { MBoomUI } from '../../ui/gameui/game/MBoomUI';\r\nimport { GameIns } from '../GameIns';\r\nimport { BulletSystem } from '../bullet/BulletSystem';\r\nimport FCollider from '../collider-system/FCollider';\r\nimport { GameEvent } from '../event/GameEvent';\r\nimport BattleLayer from '../ui/layer/BattleLayer';\r\nimport { GameFightUI } from '../ui/layer/GameFightUI';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('GameMain')\r\nexport class GameMain extends Component {\r\n\r\n\r\n    @property(BattleLayer)\r\n    BattleLayer: BattleLayer | null = null;\r\n\r\n    @property(Node)\r\n    gameEnd: Node | null = null;\r\n    @property(Node)\r\n    CoverBg: Node | null = null;\r\n    @property(GameFightUI)\r\n    GameFightUI: GameFightUI | null = null;\r\n\r\n\r\n    static instance: GameMain;\r\n    protected onLoad(): void {\r\n        GameMain.instance = this;\r\n        this.CoverBg!.active = true;\r\n        BulletSystem.bulletParent = this.BattleLayer!.selfBulletLayer!;//设置子弹父节点\r\n\r\n        MyApp.globalMgr.setBattleResolution();\r\n\r\n        GameIns.fColliderManager.enable = true;\r\n        GameIns.fColliderManager.setGlobalColliderEnterCall((colliderA: FCollider, colliderB: FCollider) => {\r\n            colliderA.entity?.onCollide?.(colliderB);\r\n            colliderB.entity?.onCollide?.(colliderA);\r\n        });\r\n\r\n        if (GameConst.ColliderDraw) {\r\n            PhysicsSystem2D.instance.enable = true;\r\n            PhysicsSystem2D.instance.debugDrawFlags = EPhysics2DDrawFlags.Aabb;\r\n        }\r\n    }\r\n\r\n    protected onEnable(): void {\r\n        EventManager.Instance.on(GameEvent.GameLoadEnd, this.onEventGameLoadEnd, this);\r\n    }\r\n\r\n    protected onDisable(): void {\r\n        EventManager.Instance.off(GameEvent.GameLoadEnd, this.onEventGameLoadEnd, this);\r\n    }\r\n\r\n    onEventGameLoadEnd() {\r\n        //由于gameui,使用了独立的摄影机，层级太高，会在loading界面之上，所以先隐藏，加载完，再显示\r\n        this.GameFightUI!.node.active = true;\r\n    }\r\n\r\n    start() {\r\n        GameIns.battleManager.startLoading();\r\n        UIMgr.openUI(MBoomUI)\r\n    }\r\n\r\n    showGameResult(isSuccess: boolean) {\r\n        this.gameEnd!.active = true;\r\n\r\n        let LabelWin = find(\"LabelWin\", this.gameEnd!);\r\n        let LabelFail = find(\"LabelFail\", this.gameEnd!);\r\n        LabelWin!.active = isSuccess;\r\n        LabelFail!.active = !isSuccess;\r\n    }\r\n\r\n    async onBtnAgainClicked() {\r\n        this.gameEnd!.active = false;\r\n        GameIns.battleManager.quitBattle();\r\n    }\r\n\r\n    /**\r\n     * 每帧更新逻辑\r\n     * @param deltaTime 时间增量\r\n     */\r\n    update(deltaTime: number): void {\r\n        GameIns.battleManager.updateGameLogic(deltaTime);\r\n    }\r\n}\r\n\r\n\r\n"]}