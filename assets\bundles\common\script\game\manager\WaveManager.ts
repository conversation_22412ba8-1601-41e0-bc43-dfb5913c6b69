import { SingletonBase } from "db://assets/scripts/core/base/SingletonBase";
import { Wave } from "../wave/Wave";

export default class WaveManager extends SingletonBase<WaveManager> {

    private _activeWaves: Wave[] = [];
    private _completedWaves: Wave[] = [];
    isAllWaveCompleted: boolean = false;

    reset(): void {
        this._activeWaves = [];
        this._completedWaves = [];
        this.isAllWaveCompleted = false;
    }

    gameStart(): void {
        this.reset();
    }

    addWaveByLevel(wave: Wave, posX: number, posY: number): void {
        if (wave) {
            wave.trigger(posX, posY);
            this._activeWaves.push(wave);
        }
    }

    isWaveSpawnCompleted(waveUuid: string): boolean {
        return this._completedWaves.some((wave) => wave.uuid === waveUuid);
    }

    isWaveEnemyDefeated(waveUuid: string): boolean {
        return this._completedWaves.some((wave) => wave.uuid === waveUuid && wave.isAllEnemyDead);
    }

    async updateGameLogic(deltaTime: number) {
        this._updateWaves(deltaTime);
    }

    private _updateWaves(deltaTime: number) {
        const dtInMiliseconds = deltaTime * 1000;
        for (let i = this._activeWaves.length - 1; i >= 0; i--) {
            const wave = this._activeWaves[i];
            wave.tick(dtInMiliseconds);
            if (wave.isSpawnCompleted) {
                this._activeWaves.splice(i, 1);
                this._completedWaves.push(wave);
                this.isAllWaveCompleted = true;// test，测试用，只展示一波
            }
        }
    }
}