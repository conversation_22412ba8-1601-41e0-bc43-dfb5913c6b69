System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, instantiate, isValid, Material, Prefab, sp, UITransform, MyApp, StateEvent, StateMachine, _dec, _dec2, _class, _class2, _descriptor, _crd, ccclass, property, Plane;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/bundles/common/script/app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneData(extras) {
    _reporterNs.report("PlaneData", "../data/plane/PlaneData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfStateCallback(extras) {
    _reporterNs.report("StateCallback", "../plane/StateDefine", _context.meta, extras);
  }

  function _reportPossibleCrUseOfStateEvent(extras) {
    _reporterNs.report("StateEvent", "../plane/StateDefine", _context.meta, extras);
  }

  function _reportPossibleCrUseOfStateMachine(extras) {
    _reporterNs.report("StateMachine", "../plane/StateMachine", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      instantiate = _cc.instantiate;
      isValid = _cc.isValid;
      Material = _cc.Material;
      Prefab = _cc.Prefab;
      sp = _cc.sp;
      UITransform = _cc.UITransform;
    }, function (_unresolved_2) {
      MyApp = _unresolved_2.MyApp;
    }, function (_unresolved_3) {
      StateEvent = _unresolved_3.StateEvent;
    }, function (_unresolved_4) {
      StateMachine = _unresolved_4.StateMachine;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "59b556RePRMF656CxkARk2/", "Plane", undefined);

      __checkObsolete__(['_decorator', 'Component', 'instantiate', 'isValid', 'Material', 'MeshRenderer', 'Node', 'Prefab', 'sp', 'UITransform']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("Plane", Plane = (_dec = ccclass('Plane'), _dec2 = property(sp.Skeleton), _dec(_class = (_class2 = class Plane extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "spine", _descriptor, this);

          this._prefabeNode = null;
          this._planeData = null;
          //飞机数据
          this._curEvent = (_crd && StateEvent === void 0 ? (_reportPossibleCrUseOfStateEvent({
            error: Error()
          }), StateEvent) : StateEvent).IDLE;
          //当前动画名称
          this._aniCallFunc = undefined;
          //动画回调
          this._isInit = false;
          //是否初始化完成
          this._stateMachine = new (_crd && StateMachine === void 0 ? (_reportPossibleCrUseOfStateMachine({
            error: Error()
          }), StateMachine) : StateMachine)();
          this._hurtActDuration = 0.2;
        }

        // 受伤动画持续时间
        onLoad() {// if (!this.spine) {
          //     this.spine = this.getComponentInChildren(sp.Skeleton);
          // }
        }

        update(dt) {
          if (this._hurtActDuration > 0) {
            this._hurtActDuration -= dt;

            if (this._hurtActDuration <= 0) {
              this.resetRoleEffect();
            }
          }
        }

        reset() {
          var _this$_prefabeNode;

          this._stateMachine.reset();

          this._curEvent = (_crd && StateEvent === void 0 ? (_reportPossibleCrUseOfStateEvent({
            error: Error()
          }), StateEvent) : StateEvent).IDLE;
          this.resetRoleEffect();
          (_this$_prefabeNode = this._prefabeNode) == null || _this$_prefabeNode.removeFromParent();
          this._prefabeNode = null;
        }

        initPlane(planeData) {
          // 初始化飞机数据
          this._planeData = planeData;
          this._isInit = false;
          let path = this._planeData.recoursePrefab;
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.loadAsync(path, Prefab).then(prefab => {
            this._prefabeNode = instantiate(prefab);
            this.node.addChild(this._prefabeNode);
            this.spine = this._prefabeNode.getComponentInChildren(sp.Skeleton);

            if (this.spine) {
              this.spine.premultipliedAlpha = false;
              this._isInit = true;
              this.spine.node.setPosition(0, -this.spine.node.getComponent(UITransform).height / 2);
              this.setPlaneState(this._curEvent, this._aniCallFunc);
            }

            this._stateMachine.initializeStateMachine(this.spine);
          });
        } // 播放闪白动画


        async playFlashAnim() {
          if (!this.spine) {
            return;
          }

          let material = await (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.loadAsync("effect/flash/flash", Material);

          if (material) {
            this.spine.customMaterial = material;
          }

          this._hurtActDuration = 0.2;
        }

        resetRoleEffect() {
          this._hurtActDuration = 0;

          if (!this.spine) {
            return;
          }

          this.spine.customMaterial = null;
        }

        setPlaneState(event, callback) {
          if (!this._isInit) {
            this._curEvent = event;
            this._aniCallFunc = callback;
            return false;
          }

          return this._stateMachine.handleEvent(event, callback);
        } // 移动命令（通用移动）


        onEnter(callback) {
          return this.setPlaneState((_crd && StateEvent === void 0 ? (_reportPossibleCrUseOfStateEvent({
            error: Error()
          }), StateEvent) : StateEvent).ENTER, callback);
        } // 移动命令


        onMoveCommand(isLeft = false, callback) {
          let event = isLeft ? (_crd && StateEvent === void 0 ? (_reportPossibleCrUseOfStateEvent({
            error: Error()
          }), StateEvent) : StateEvent).MOVE_LEFT_COMMAND : (_crd && StateEvent === void 0 ? (_reportPossibleCrUseOfStateEvent({
            error: Error()
          }), StateEvent) : StateEvent).MOVE_RIGHT_COMMAND;
          return this.setPlaneState(event, callback);
        } // 移动结束


        onMoveEnd(callback) {
          return this.setPlaneState((_crd && StateEvent === void 0 ? (_reportPossibleCrUseOfStateEvent({
            error: Error()
          }), StateEvent) : StateEvent).MOVE_END, callback);
        } // 攻击命令


        onAttackCommand(callback) {
          return this.setPlaneState((_crd && StateEvent === void 0 ? (_reportPossibleCrUseOfStateEvent({
            error: Error()
          }), StateEvent) : StateEvent).ATTACK_COMMAND, callback);
        } // 受击事件


        onGetHit(callback) {
          this.playFlashAnim();
          return this.setPlaneState((_crd && StateEvent === void 0 ? (_reportPossibleCrUseOfStateEvent({
            error: Error()
          }), StateEvent) : StateEvent).GET_HIT, callback);
        } // 死亡事件


        onDie(callback) {
          return this.setPlaneState((_crd && StateEvent === void 0 ? (_reportPossibleCrUseOfStateEvent({
            error: Error()
          }), StateEvent) : StateEvent).DIE, callback);
        }

        onDodgeCommand(callback) {
          return this.setPlaneState((_crd && StateEvent === void 0 ? (_reportPossibleCrUseOfStateEvent({
            error: Error()
          }), StateEvent) : StateEvent).DODGE_COMMAND, callback);
        }

        setAnimSpeed(speed) {
          if (this.spine && isValid(this.spine)) {
            this.spine.timeScale = speed;
          }
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "spine", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=e6fd3f922ee66a80d1b7cb20f98803f4634b49e9.js.map