System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "long", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, IMgr, logDebug, logError, logInfo, logWarn, Long, csproto, DataMgr, LoginInfo, registerElem, _dec, _class3, _crd, ccclass, NetStatus, NetMgr;

  function _reportPossibleCrUseOfIMgr(extras) {
    _reporterNs.report("IMgr", "db://assets/scripts/core/base/IMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogDebug(extras) {
    _reporterNs.report("logDebug", "db://assets/scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogError(extras) {
    _reporterNs.report("logError", "db://assets/scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogInfo(extras) {
    _reporterNs.report("logInfo", "db://assets/scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogWarn(extras) {
    _reporterNs.report("logWarn", "db://assets/scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLong(extras) {
    _reporterNs.report("Long", "long", _context.meta, extras);
  }

  function _reportPossibleCrUseOfcsproto(extras) {
    _reporterNs.report("csproto", "../autogen/pb/cs_proto.js", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDataMgr(extras) {
    _reporterNs.report("DataMgr", "../data/DataManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIPlatformSDK(extras) {
    _reporterNs.report("IPlatformSDK", "../platformsdk/IPlatformSDK.js", _context.meta, extras);
  }

  _export("LoginInfo", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_unresolved_2) {
      IMgr = _unresolved_2.IMgr;
    }, function (_unresolved_3) {
      logDebug = _unresolved_3.logDebug;
      logError = _unresolved_3.logError;
      logInfo = _unresolved_3.logInfo;
      logWarn = _unresolved_3.logWarn;
    }, function (_long) {
      Long = _long.default;
    }, function (_unresolved_4) {
      csproto = _unresolved_4.default;
    }, function (_unresolved_5) {
      DataMgr = _unresolved_5.DataMgr;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "7ba0fcmwhtIpZwXNoD5utwu", "NetMgr", undefined);

      __checkObsolete__(['_decorator']);

      ({
        ccclass
      } = _decorator);

      _export("NetStatus", NetStatus = /*#__PURE__*/function (NetStatus) {
        NetStatus[NetStatus["NotConnect"] = 0] = "NotConnect";
        NetStatus[NetStatus["Connecting"] = 1] = "Connecting";
        NetStatus[NetStatus["ServerPassed"] = 2] = "ServerPassed";
        NetStatus[NetStatus["Disconnected"] = 3] = "Disconnected";
        NetStatus[NetStatus["Connected"] = 4] = "Connected";
        return NetStatus;
      }({}));

      _export("LoginInfo", LoginInfo = class LoginInfo {
        constructor() {
          this.accountType = void 0;
          this.code = void 0;
          this.serverAddr = void 0;
        }

      });

      registerElem = class registerElem {
        constructor(handler, target) {
          this.handler = void 0;
          this.target = void 0;
          this.handler = handler;
          this.target = target;
        }

        eq(handler, target) {
          return this.handler === handler && this.target === target;
        }

      };

      _export("NetMgr", NetMgr = (_dec = ccclass("NetMgr"), _dec(_class3 = class NetMgr extends (_crd && IMgr === void 0 ? (_reportPossibleCrUseOfIMgr({
        error: Error()
      }), IMgr) : IMgr) {
        constructor(platformSDK) {
          super();
          this.platformSDK = null;
          this._websocket = null;
          this._status = NetStatus.NotConnect;
          this.loginInfo = null;
          this._reconnectAttempts = 0;
          this._maxReconnectAttempts = 5;
          this._reconnectDelay = 3000;
          // 3 seconds
          this._heartbeatInterval = 3000;
          // 30 seconds
          this._heartbeatTimer = 0;
          this._lastHeartbeatTime = 0;
          this._messageHandlers = new Map();
          this._messageQueue = [];
          this._cmdSeq = new Map();
          this.platformSDK = platformSDK;
        }

        uninitRegistered() {
          this.unregisterHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GET_SESSION, this.onGetSession, this);
          this.unregisterHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_HEARTBEAT, this.onHeartbeat, this);
          this.unregisterHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GET_ROLE, this.onGetRole, this);
        }

        init() {
          (_crd && logInfo === void 0 ? (_reportPossibleCrUseOflogInfo({
            error: Error()
          }), logInfo) : logInfo)("NetMgr", "Network manager initialized");
          this.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GET_SESSION, this.onGetSession, this);
          this.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_HEARTBEAT, this.onHeartbeat, this);
          this.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GET_ROLE, this.onGetRole, this);
        }

        unInit() {
          this.uninitRegistered();
          this.disconnect();

          this._messageHandlers.clear();

          this._messageQueue.length = 0;
          super.unInit();
        }

        onUpdate(dt) {
          this._heartbeatTimer += dt * 1000; // Send heartbeat

          if (this._status === NetStatus.Connected && this._heartbeatTimer >= this._heartbeatInterval) {
            this.sendHeartbeat();
            this._heartbeatTimer = 0;
          } // Check connection timeout


          if (this._status === NetStatus.Connected && Date.now() - this._lastHeartbeatTime > this._heartbeatInterval * 2) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", "Connection timeout, attempting to reconnect");
            this.handleDisconnection();
          }
        }
        /**
         * Connect to server
         * @param url WebSocket server URL
         */


        connect() {
          var _this$platformSDK;

          if (this._status === NetStatus.Connecting || this._status === NetStatus.Connected) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", "Already connecting or connected");
            return;
          }

          this._status = NetStatus.Connecting;
          this._reconnectAttempts = 0;
          (_this$platformSDK = this.platformSDK) == null || _this$platformSDK.login((err, info) => {
            var _this$loginInfo;

            if (err) {
              (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
                error: Error()
              }), logError) : logError)("NetMgr", `login failed ${err}`);
              return;
            }

            this.loginInfo = info;
            (_crd && logInfo === void 0 ? (_reportPossibleCrUseOflogInfo({
              error: Error()
            }), logInfo) : logInfo)("NetMgr", `Connecting to ${(_this$loginInfo = this.loginInfo) == null ? void 0 : _this$loginInfo.serverAddr}`);
            this.createWebSocket();
          });
        }

        login(info) {
          this.loginInfo = info;
          this.connect();
        }
        /**
         * Disconnect from server
         */


        disconnect() {
          if (this._websocket) {
            this._websocket.close();

            this._websocket = null;
          }

          this._status = NetStatus.Disconnected;
          this._reconnectAttempts = 0;
          (_crd && logInfo === void 0 ? (_reportPossibleCrUseOflogInfo({
            error: Error()
          }), logInfo) : logInfo)("NetMgr", "Disconnected from server");
        }
        /**
         * Get current connection status
         */


        getStatus() {
          return this._status;
        }
        /**
         * Check if connected
         */


        isConnected() {
          return this._status === NetStatus.Connected;
        }
        /**
         * Create WebSocket connection
         */


        createWebSocket() {
          try {
            this._websocket = new WebSocket(this.loginInfo.serverAddr);
            this._websocket.binaryType = 'arraybuffer';
            this._websocket.onopen = this.onWebSocketOpen.bind(this);
            this._websocket.onmessage = this.onWebSocketMessage.bind(this);
            this._websocket.onclose = this.onWebSocketClose.bind(this);
            this._websocket.onerror = this.onWebSocketError.bind(this);
          } catch (err) {
            (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
              error: Error()
            }), logError) : logError)("NetMgr", `Failed to create WebSocket: ${err}`);
            this.handleDisconnection();
          }
        }
        /**
         * WebSocket open event handler
         */


        onWebSocketOpen(_event) {
          (_crd && logInfo === void 0 ? (_reportPossibleCrUseOflogInfo({
            error: Error()
          }), logInfo) : logInfo)("NetMgr", "WebSocket connected");
          this._status = NetStatus.Connected;
          this._reconnectAttempts = 0;
          this._lastHeartbeatTime = Date.now();
          this.sendMessage((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GET_SESSION, {
            get_session: {
              account_type: this.loginInfo.accountType,
              // 账号类型
              platform: (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
                error: Error()
              }), csproto) : csproto).cs.PLATFORM.PLATFORM_EDITOR,
              // 平台类型
              code: this.loginInfo.code,
              // 账号名
              version: 1 // 版本号

            }
          });
        }
        /**
         * WebSocket message event handler
         */


        onWebSocketMessage(event) {
          try {
            (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
              error: Error()
            }), logDebug) : logDebug)("NetMgr", `WebSocket message received ${event}`);
            const buffer = new Uint8Array(event.data);
            this.handleMessage(buffer);
            this._lastHeartbeatTime = Date.now();
          } catch (err) {
            (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
              error: Error()
            }), logError) : logError)("NetMgr", `Failed to handle message: ${err}`);
          }
        }
        /**
         * WebSocket close event handler
         */


        onWebSocketClose(event) {
          (_crd && logInfo === void 0 ? (_reportPossibleCrUseOflogInfo({
            error: Error()
          }), logInfo) : logInfo)("NetMgr", `WebSocket closed: ${event.code} - ${event.reason}`);
          this.handleDisconnection();
        }
        /**
         * WebSocket error event handler
         */


        onWebSocketError(_event) {
          (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
            error: Error()
          }), logError) : logError)("NetMgr", "WebSocket error occurred");
          this.handleDisconnection();
        }
        /**
         * Handle disconnection and attempt reconnection
         */


        handleDisconnection() {
          if (this._websocket) {
            this._websocket.close();

            this._websocket = null;
          }

          this._status = NetStatus.NotConnect; // Attempt reconnection if not manually disconnected

          if (this._reconnectAttempts < this._maxReconnectAttempts) {
            this._reconnectAttempts++;
            (_crd && logInfo === void 0 ? (_reportPossibleCrUseOflogInfo({
              error: Error()
            }), logInfo) : logInfo)("NetMgr", `Attempting reconnection ${this._reconnectAttempts}/${this._maxReconnectAttempts}`);
            setTimeout(() => {
              if (this.loginInfo.serverAddr && this._status !== NetStatus.Disconnected) {
                this.connect();
              }
            }, this._reconnectDelay);
          } else {
            (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
              error: Error()
            }), logError) : logError)("NetMgr", "Max reconnection attempts reached");
            this._status = NetStatus.Disconnected;
          }
        }
        /**
         * Process queued messages
         */


        processMessageQueue() {
          while (this._messageQueue.length > 0 && this.isConnected()) {
            const message = this._messageQueue.shift();

            if (message) {
              this.sendRawMessage(message);
            }
          }
        }
        /**
         * Handle incoming message
         */


        handleMessage(buffer) {
          try {
            // Parse message header (assuming first 4 bytes are message ID)
            var msg = (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
              error: Error()
            }), csproto) : csproto).cs.S2CMsg.decode(buffer);
            (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
              error: Error()
            }), logDebug) : logDebug)("NetMgr", `Received message ${(_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
              error: Error()
            }), csproto) : csproto).cs.CS_CMD[msg.cmd]} ${JSON.stringify(msg)}`);
            this.dispatchMessage(msg);
          } catch (err) {
            (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
              error: Error()
            }), logError) : logError)("NetMgr", `Failed to parse message: ${err}`);
          }
        }
        /**
         * Dispatch message to registered handlers
         */


        dispatchMessage(msg) {
          if (!!msg.seq) {
            this._cmdSeq.set(msg.cmd, msg.seq);
          }

          const handlers = this._messageHandlers.get(msg.cmd);

          if (handlers && handlers.length > 0) {
            try {
              handlers.forEach(elem => {
                try {
                  elem.handler.call(elem.target, msg);
                } catch (err) {
                  (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
                    error: Error()
                  }), logError) : logError)("NetMgr", `Handler error for msgId ${(_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
                    error: Error()
                  }), csproto) : csproto).cs.CS_CMD[msg.cmd]}: ${err.stack}`);
                }
              });
            } catch (err) {
              (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
                error: Error()
              }), logError) : logError)("NetMgr", `Failed to decode message ${(_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
                error: Error()
              }), csproto) : csproto).cs.CS_CMD[msg.cmd]}: ${err.stack}`);
            }
          } else {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", `No handler registered for msgId: ${(_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
              error: Error()
            }), csproto) : csproto).cs.CS_CMD[msg.cmd]}`);
          }
        }
        /**
         * Decode protobuf message based on message ID
         */


        decodeProtobufMessage(_msgId, data) {
          // This is a simplified example - you would need to map msgId to specific protobuf types
          // For now, return the raw data
          return data;
        }
        /**
         * Register message handler
         */


        registerHandler(msgId, handler, target) {
          if (!this._messageHandlers.has(msgId)) {
            this._messageHandlers.set(msgId, []);
          }

          this._messageHandlers.get(msgId).push(new registerElem(handler, target));

          (_crd && logInfo === void 0 ? (_reportPossibleCrUseOflogInfo({
            error: Error()
          }), logInfo) : logInfo)("NetMgr", `Registered handler for msgId: ${(_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD[msgId]}`);
        }
        /**
         * Unregister message handler
         */


        unregisterHandler(msgId, handler, target) {
          const handlers = this._messageHandlers.get(msgId);

          if (handlers) {
            const index = handlers.findIndex(elem => {
              return elem.eq(handler, target);
            });

            if (index !== -1) {
              handlers.splice(index, 1);
              (_crd && logInfo === void 0 ? (_reportPossibleCrUseOflogInfo({
                error: Error()
              }), logInfo) : logInfo)("NetMgr", `Unregistered handler for msgId: ${(_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
                error: Error()
              }), csproto) : csproto).cs.CS_CMD[msgId]}`); // Clean up empty handler arrays to prevent memory leaks

              if (handlers.length === 0) {
                this._messageHandlers.delete(msgId);

                (_crd && logInfo === void 0 ? (_reportPossibleCrUseOflogInfo({
                  error: Error()
                }), logInfo) : logInfo)("NetMgr", `Removed empty handler array for msgId: ${(_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
                  error: Error()
                }), csproto) : csproto).cs.CS_CMD[msgId]}`);
              }
            } else {
              (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
                error: Error()
              }), logWarn) : logWarn)("NetMgr", `Handler not found for msgId: ${(_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
                error: Error()
              }), csproto) : csproto).cs.CS_CMD[msgId]}`);
            }
          } else {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", `No handlers registered for msgId: ${(_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
              error: Error()
            }), csproto) : csproto).cs.CS_CMD[msgId]}`);
          }
        }
        /**
         * Send protobuf message
         */


        sendMessage(msgId, message) {
          (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
            error: Error()
          }), logDebug) : logDebug)("NetMgr", `sendMessage ${(_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD[msgId]} ${JSON.stringify((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.C2SMsgBody.create(message))}`);

          try {
            // Encode protobuf message
            const netMessage = this.encodeProtobufMessage(msgId, message);
            (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
              error: Error()
            }), logDebug) : logDebug)("NetMgr", `sendMessage ${(_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
              error: Error()
            }), csproto) : csproto).cs.CS_CMD[msgId]} seq ${netMessage.seq}`);

            if (this.isConnected()) {
              this.sendRawMessage(netMessage);
            } else {
              // Queue message if not connected
              this._messageQueue.push(netMessage);

              (_crd && logInfo === void 0 ? (_reportPossibleCrUseOflogInfo({
                error: Error()
              }), logInfo) : logInfo)("NetMgr", `Queued message ${(_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
                error: Error()
              }), csproto) : csproto).cs.CS_CMD[msgId]} seq ${netMessage.seq} (not connected)`);
            }
          } catch (err) {
            (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
              error: Error()
            }), logError) : logError)("NetMgr", `Failed to send message ${(_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
              error: Error()
            }), csproto) : csproto).cs.CS_CMD[msgId]}: ${err}`);
          }
        }
        /**
         * Send raw message over WebSocket
         */


        sendRawMessage(message) {
          if (!this._websocket || this._websocket.readyState !== WebSocket.OPEN) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", "WebSocket not ready for sending");
            return;
          }

          try {
            let buffData = message.data.buffer.slice(message.data.byteOffset, message.data.byteOffset + message.data.byteLength);

            this._websocket.send(buffData);

            (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
              error: Error()
            }), logDebug) : logDebug)("NetMgr", `Sent message ${(_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
              error: Error()
            }), csproto) : csproto).cs.CS_CMD[message.msgId]}, size: ${message.data.byteLength}`);
          } catch (err) {
            (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
              error: Error()
            }), logError) : logError)("NetMgr", `Failed to send raw message: ${err}`);
          }
        }
        /**
         * Encode protobuf message
         */


        encodeProtobufMessage(_msgId, message) {
          var _this$_cmdSeq$get;

          // This is a simplified example - you would need to map msgId to specific protobuf types
          // For now, if message is already Uint8Array, return it; otherwise encode as ClientData
          let seq = (_this$_cmdSeq$get = this._cmdSeq.get(_msgId)) != null ? _this$_cmdSeq$get : 0;

          if (message instanceof Uint8Array) {
            const netMessage = {
              msgId: _msgId,
              seq: seq,
              data: message
            };
            return netMessage;
          }

          var msg = new (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.C2SMsg();
          msg.cmd = _msgId;
          msg.seq = seq;
          msg.body = message;
          const clientData = (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.C2SMsg.encode(msg).finish();
          const netMessage = {
            msgId: _msgId,
            seq: seq,
            data: clientData
          };
          return netMessage;
        }
        /**
         * Send heartbeat message
         */


        sendHeartbeat() {
          // Send a simple heartbeat message (you can define a specific heartbeat message type)
          const heartbeatData = {
            heartbeat: {
              clent_time: (_crd && Long === void 0 ? (_reportPossibleCrUseOfLong({
                error: Error()
              }), Long) : Long).fromNumber(Date.now()),
              // 客户端时间
              is_fighting: 0 // 是否战斗中

            }
          };
          this.sendMessage((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_HEARTBEAT, heartbeatData);
        }
        /**
         * Clear all message handlers
         */


        clearAllHandlers() {
          this._messageHandlers.clear();

          (_crd && logInfo === void 0 ? (_reportPossibleCrUseOflogInfo({
            error: Error()
          }), logInfo) : logInfo)("NetMgr", "Cleared all message handlers");
        }
        /**
         * Get message queue length
         */


        getQueueLength() {
          return this._messageQueue.length;
        }

        onHeartbeat(msg) {
          (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
            error: Error()
          }), logDebug) : logDebug)("NetMgr", `onHeartbeat ${msg}`);
        }

        onGetSession(msg) {
          var _msg$body;

          (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
            error: Error()
          }), logDebug) : logDebug)("NetMgr", `onGetSession ${msg}`);

          if (msg.ret_code != (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).comm.RET_CODE.RET_CODE_NO_ERROR) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", `onGetSession failed ${msg.ret_code}`);
            return;
          }

          var sessionRsp = (_msg$body = msg.body) == null ? void 0 : _msg$body.get_session;

          if (!sessionRsp) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", "onGetSession data is null");
            return;
          }

          (_crd && logInfo === void 0 ? (_reportPossibleCrUseOflogInfo({
            error: Error()
          }), logInfo) : logInfo)("NetMgr", `onGetSession ${sessionRsp.openid}:${sessionRsp.uin_list}`);
          this.sendMessage((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GET_ROLE, {
            get_role: {
              uin: (_crd && Long === void 0 ? (_reportPossibleCrUseOfLong({
                error: Error()
              }), Long) : Long).ZERO,
              area_id: 0
            }
          });
        }

        onGetRole(msg) {
          var _msg$body2, _roleRsp$cmd_seq;

          if (msg.ret_code != (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).comm.RET_CODE.RET_CODE_NO_ERROR) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", `onGetRole failed ${msg.ret_code}`);
            return;
          }

          var roleRsp = (_msg$body2 = msg.body) == null ? void 0 : _msg$body2.get_role;

          if (!roleRsp) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", "onGetRole data is null");
            return;
          }

          (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).init();
          (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).role.setRole(roleRsp);

          if ((_roleRsp$cmd_seq = roleRsp.cmd_seq) != null && (_roleRsp$cmd_seq = _roleRsp$cmd_seq.items) != null && _roleRsp$cmd_seq.forEach(item => {
            this._cmdSeq.set(item.cmd, item.seq_no);
          })) {
            (_crd && logInfo === void 0 ? (_reportPossibleCrUseOflogInfo({
              error: Error()
            }), logInfo) : logInfo)("NetMgr", `onGetRole cmd_seq ${JSON.stringify(roleRsp.cmd_seq.items)}`);
          }

          (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).gameLogic.checkGameID(roleRsp.game_id);

          if (roleRsp.pvp_status != (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).comm.GAME_PVP_STATUS.GAME_PVP_STATUS_NONE) {
            this.sendMessage((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
              error: Error()
            }), csproto) : csproto).cs.CS_CMD.CS_CMD_GAME_PVP_GET_INFO, {
              game_pvp_get_info: {}
            });
          }

          this.sendMessage((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GAME_PVP_GET_LIST, {
            game_pvp_get_list: {}
          });
        }

        disableReconnect() {
          this._reconnectAttempts = this._maxReconnectAttempts;
        }

      }) || _class3));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=a5692f162b6a03431d245ba4ece01b6bcb8a6fb9.js.map