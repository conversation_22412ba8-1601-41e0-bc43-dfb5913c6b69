{"version": 3, "sources": ["file:///E:/M2Game/Client/extensions/i18n/assets/LocalizedLabel.ts"], "names": ["_decorator", "Component", "Label", "RichText", "warn", "i18n", "TTFUtils", "ccclass", "property", "executeInEditMode", "LocalizedLabel", "visible", "displayName", "type", "CUSTOM_TYPE", "tooltip", "label", "_key", "key", "str", "updateLabel", "onLoad", "ready", "init", "fetch<PERSON><PERSON>", "start", "lab", "getComponent", "customType", "NUMBER", "TEXT", "useSystemFont", "getInstance", "setCustomTTF", "fontFamily", "node", "name", "string", "getI18StrByKey"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAISA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,I,OAAAA,I;;AAHrCC,MAAAA,I;;AACHC,MAAAA,Q,iBAAAA,Q;;;;;;;;;OAGH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CT,U;;gCAIpCU,c,WAFZH,OAAO,CAAC,gBAAD,C,UAKHC,QAAQ,CAAC;AAAEG,QAAAA,OAAO,EAAE;AAAX,OAAD,C,UAGRH,QAAQ,CAAC;AAAEI,QAAAA,WAAW,EAAE,KAAf;AAAsBD,QAAAA,OAAO,EAAE;AAA/B,OAAD,C,UASRH,QAAQ,CAAC;AACNK,QAAAA,IAAI,EAAE;AAAA;AAAA,kCAASC,WADT;AAENC,QAAAA,OAAO,EAAE;AAFH,OAAD,C,gBAhBZN,iB,qBADD,MAEaC,cAFb,SAEoCT,SAFpC,CAE8C;AAAA;AAAA;AAAA,eAC1Ce,KAD0C,GACT,IADS;;AAAA;;AAAA;AAAA;;AAOlC,YAAJC,IAAI,GAAG;AACP,iBAAO,KAAKC,GAAZ;AACH;;AACO,YAAJD,IAAI,CAACE,GAAD,EAAc;AAClB,eAAKC,WAAL;AACA,eAAKF,GAAL,GAAWC,GAAX;AACH;;AAQDE,QAAAA,MAAM,GAAG;AACL,cAAI,CAAChB,IAAI,CAACiB,KAAV,EAAiB;AACbjB,YAAAA,IAAI,CAACkB,IAAL,CAAU,IAAV;AACH;;AACD,eAAKC,WAAL;AACH;;AAEDC,QAAAA,KAAK,GAAG;AACJ,cAAIC,GAAJ;AACAA,UAAAA,GAAG,GAAG,KAAKC,YAAL,CAAkBzB,KAAlB,CAAN;;AACA,cAAI,CAACwB,GAAL,EAAU;AACNA,YAAAA,GAAG,GAAG,KAAKC,YAAL,CAAkBxB,QAAlB,CAAN;AACH;;AACD,cAAIuB,GAAJ,EAAS;AACL,gBAAI,KAAKE,UAAL,KAAoB;AAAA;AAAA,sCAASd,WAAT,CAAqBe,MAAzC,IACG,KAAKD,UAAL,KAAoB;AAAA;AAAA,sCAASd,WAAT,CAAqBgB,IADhD,EACsD;AAClDJ,cAAAA,GAAG,CAACK,aAAJ,GAAoB,KAApB;AACA;AAAA;AAAA,wCAASC,WAAT,GAAuBC,YAAvB,CAAoCP,GAApC,EAAyC,KAAKE,UAA9C;AACH,aAJD,MAIO;AACHF,cAAAA,GAAG,CAACK,aAAJ,GAAoB,IAApB;AACAL,cAAAA,GAAG,CAACQ,UAAJ,GAAiB,OAAjB;AACH;AACJ,WATD,MASO;AACH9B,YAAAA,IAAI,CAAC,gBAAD,EAAmB,KAAK+B,IAAL,CAAUC,IAA7B,EAAmC,4BAAnC,CAAJ;AACH;AACJ;;AAEDZ,QAAAA,WAAW,GAAG;AACV,cAAIE,GAAJ;AACAA,UAAAA,GAAG,GAAG,KAAKC,YAAL,CAAkBzB,KAAlB,CAAN;;AACA,cAAI,CAACwB,GAAL,EAAU;AACNA,YAAAA,GAAG,GAAG,KAAKC,YAAL,CAAkBxB,QAAlB,CAAN;AACH;;AACD,cAAIuB,GAAJ,EAAS;AACL,iBAAKV,KAAL,GAAaU,GAAb;AACA,iBAAKN,WAAL;AACA;AACH;AACJ;;AAEDA,QAAAA,WAAW,GAAG;AACV,cAAI,KAAKF,GAAL,KAAa,EAAjB,EAAqB;AAAE;AAAQ;;AAC/B,eAAKF,KAAL,KAAe,KAAKA,KAAL,CAAWqB,MAAX,GAAoBhC,IAAI,CAACiC,cAAL,CAAoB,KAAKpB,GAAzB,CAAnC;AACH;;AAhEyC,O;;;;;iBAI5B,E;;;;;;;iBAeO;AAAA;AAAA,oCAASJ,WAAT,CAAqBgB,I", "sourcesContent": ["\r\nimport * as i18n from './LanguageData';\r\nimport { TTFUtils } from './TTFUtils';\r\n\r\nimport { _decorator, Component, Label, RichText, warn } from 'cc';\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\n\r\n@ccclass('LocalizedLabel')\r\n@executeInEditMode\r\nexport class LocalizedLabel extends Component {\r\n    label: Label | RichText | null = null;\r\n\r\n    @property({ visible: false })\r\n    key: string = '';\r\n\r\n    @property({ displayName: 'Key', visible: true })\r\n    get _key() {\r\n        return this.key;\r\n    }\r\n    set _key(str: string) {\r\n        this.updateLabel();\r\n        this.key = str;\r\n    }\r\n\r\n    @property({\r\n        type: TTFUtils.CUSTOM_TYPE,\r\n        tooltip: \"加载字体类型\"\r\n    })\r\n    customType: number = TTFUtils.CUSTOM_TYPE.TEXT;\r\n\r\n    onLoad() {\r\n        if (!i18n.ready) {\r\n            i18n.init('zh');\r\n        }\r\n        this.fetchRender();\r\n    }\r\n\r\n    start() {\r\n        let lab: Label | RichText | null;\r\n        lab = this.getComponent(Label);\r\n        if (!lab) {\r\n            lab = this.getComponent(RichText);\r\n        }\r\n        if (lab) {\r\n            if (this.customType === TTFUtils.CUSTOM_TYPE.NUMBER\r\n                || this.customType === TTFUtils.CUSTOM_TYPE.TEXT) {\r\n                lab.useSystemFont = false;\r\n                TTFUtils.getInstance().setCustomTTF(lab, this.customType);\r\n            } else {\r\n                lab.useSystemFont = true;\r\n                lab.fontFamily = \"Arial\";\r\n            }\r\n        } else {\r\n            warn(\"TTFComponent: \", this.node.name, \" 节点没有Label 或 RichText组件！！！\");\r\n        }\r\n    }\r\n\r\n    fetchRender() {\r\n        let lab: Label | RichText | null;\r\n        lab = this.getComponent(Label);\r\n        if (!lab) {\r\n            lab = this.getComponent(RichText);\r\n        }\r\n        if (lab) {\r\n            this.label = lab;\r\n            this.updateLabel();\r\n            return;\r\n        }\r\n    }\r\n\r\n    updateLabel() {\r\n        if (this.key === '') { return }\r\n        this.label && (this.label.string = i18n.getI18StrByKey(this.key));\r\n    }\r\n}\r\n"]}