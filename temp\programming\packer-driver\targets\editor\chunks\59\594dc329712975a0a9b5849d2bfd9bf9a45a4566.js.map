{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/MainPlaneManager.ts"], "names": ["MainPlaneManager", "Prefab", "instantiate", "MyApp", "GameResourceList", "GameIns", "BattleLayer", "_planeData", "mainPlane", "hurtTotal", "reviveCount", "maxReviveCount", "curExp", "curMaxExp", "curLv", "setPlaneData", "planeData", "preload", "battleManager", "addLoadCount", "createMainPlane", "checkLoadFinish", "reset", "subReset", "reset<PERSON>lane", "prefab", "resMgr", "loadAsync", "MainPlane", "planeNode", "getComponent", "instance", "addMainPlane", "initPlane", "mainReset", "node", "destroy", "checkCanRevive", "revive", "mainPlaneManager", "updateGameLogic", "dt"], "mappings": ";;;mJASaA,gB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AARJC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;;AACRC,MAAAA,K,iBAAAA,K;;AAEFC,MAAAA,gB;;AACEC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,W;;;;;;;;;kCAGMN,gB,GAAN,MAAMA,gBAAN,CAAuB;AAAA;AAAA,eAE1BO,UAF0B,GAEK,IAFL;AAEU;AAFV,eAG1BC,SAH0B,GAGI,IAHJ;AAGS;AAHT,eAK1BC,SAL0B,GAKN,CALM;AAKJ;AALI,eAM1BC,WAN0B,GAMJ,CANI;AAMF;AANE,eAO1BC,cAP0B,GAOD,CAPC;AAOC;AAPD,eAS1BC,MAT0B,GAST,CATS;AAAA,eAU1BC,SAV0B,GAUN,CAVM;AAAA,eAW1BC,KAX0B,GAWV,CAXU;AAAA;;AAa1BC,QAAAA,YAAY,CAACC,SAAD,EAAuB;AAC/B,eAAKT,UAAL,GAAkBS,SAAlB;AACH;;AAEY,cAAPC,OAAO,GAAG;AACZ;AAAA;AAAA,kCAAQC,aAAR,CAAsBC,YAAtB,CAAmC,CAAnC;AACA,gBAAM,KAAKC,eAAL,EAAN;AACA;AAAA;AAAA,kCAAQF,aAAR,CAAsBG,eAAtB;AACA,eAAKC,KAAL;AACH;;AAEDA,QAAAA,KAAK,GAAG;AACJ,eAAKb,SAAL,GAAiB,CAAjB;AACA,eAAKc,QAAL;AACH;;AAEDA,QAAAA,QAAQ,GAAG;AACP,eAAKb,WAAL,GAAmB,CAAnB;AACA,eAAKC,cAAL,GAAsB,CAAtB,CAFO,CAEkB;;AACzB,cAAI,KAAKH,SAAT,EAAoB;AAChB,iBAAKA,SAAL,CAAegB,UAAf;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACyB,cAAfJ,eAAe,GAA8B;AAAA;;AAC/C,cAAI,KAAKZ,SAAT,EAAoB;AAChB,iBAAKA,SAAL,CAAegB,UAAf;AACA,mBAAO,KAAKhB,SAAZ;AACH;;AAED,gBAAMiB,MAAM,GAAG,MAAM;AAAA;AAAA,8BAAMC,MAAN,CAAaC,SAAb,CAAuB;AAAA;AAAA,oDAAiBC,SAAxC,EAAmD3B,MAAnD,CAArB;AACA,cAAI4B,SAAS,GAAG3B,WAAW,CAACuB,MAAD,CAA3B;AACA,eAAKjB,SAAL,GAAiBqB,SAAS,CAACC,YAAV,CAAuB,WAAvB,CAAjB;AACA;AAAA;AAAA,0CAAYC,QAAZ,uBAAsBC,YAAtB;AACA,kCAAKxB,SAAL,6BAAgByB,SAAhB,CAA0B,KAAK1B,UAA/B;AACA,iBAAO,KAAKC,SAAZ;AACH;;AAED0B,QAAAA,SAAS,GAAG;AACR,cAAI,KAAK1B,SAAT,EAAoB;AAChB,iBAAKA,SAAL,CAAe2B,IAAf,CAAoBC,OAApB;AACA,iBAAK5B,SAAL,GAAiB,IAAjB;AACH;AACJ;;AAED6B,QAAAA,cAAc,GAAY;AACtB,iBAAO,KAAK3B,WAAL,GAAmB,KAAKC,cAA/B;AACH;;AAED2B,QAAAA,MAAM,GAAG;AAAA;;AACL;AAAA;AAAA,kCAAQC,gBAAR,CAAyB7B,WAAzB,IAAwC,CAAxC,CADK,CACsC;;AAC3C,mCAAKF,SAAL,8BAAgB8B,MAAhB;AACH;;AAEDE,QAAAA,eAAe,CAACC,EAAD,EAAmB;AAAA;;AAC9B,mCAAKjC,SAAL,8BAAgBgC,eAAhB,CAAgCC,EAAhC;AACH;;AA1EyB,O", "sourcesContent": ["\r\nimport { Prefab, instantiate } from \"cc\";\r\nimport { MyApp } from 'db://assets/bundles/common/script/app/MyApp';\r\nimport { PlaneData } from \"db://assets/bundles/common/script/data/plane/PlaneData\";\r\nimport GameResourceList from \"../const/GameResourceList\";\r\nimport { GameIns } from \"../GameIns\";\r\nimport BattleLayer from \"../ui/layer/BattleLayer\";\r\nimport { type MainPlane } from \"../ui/plane/mainPlane/MainPlane\";\r\n\r\nexport class MainPlaneManager {\r\n\r\n    _planeData: PlaneData | null = null;//飞机数据\r\n    mainPlane: MainPlane | null = null;//飞机战斗UI\r\n\r\n    hurtTotal: number = 0;//造成的总伤害\r\n    reviveCount: number = 0;//已复活次数\r\n    maxReviveCount: number = 0;//可复活次数\r\n\r\n    curExp: number = 0;\r\n    curMaxExp: number = 0;\r\n    curLv: number = 1;\r\n\r\n    setPlaneData(planeData: PlaneData) {\r\n        this._planeData = planeData;\r\n    }\r\n\r\n    async preload() {\r\n        GameIns.battleManager.addLoadCount(1);\r\n        await this.createMainPlane();\r\n        GameIns.battleManager.checkLoadFinish();\r\n        this.reset();\r\n    }\r\n\r\n    reset() {\r\n        this.hurtTotal = 0;\r\n        this.subReset();\r\n    }\r\n\r\n    subReset() {\r\n        this.reviveCount = 0;\r\n        this.maxReviveCount = 1; // 默认可复活1次\r\n        if (this.mainPlane) {\r\n            this.mainPlane.resetPlane();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 创建主飞机\r\n     * @param isTrans 是否为特殊状态\r\n     * @returns 主飞机对象\r\n     */\r\n    async createMainPlane(): Promise<MainPlane | null> {\r\n        if (this.mainPlane) {\r\n            this.mainPlane.resetPlane();\r\n            return this.mainPlane;\r\n        }\r\n\r\n        const prefab = await MyApp.resMgr.loadAsync(GameResourceList.MainPlane, Prefab);\r\n        let planeNode = instantiate(prefab);\r\n        this.mainPlane = planeNode.getComponent(\"MainPlane\") as MainPlane\r\n        BattleLayer.instance?.addMainPlane();\r\n        this.mainPlane?.initPlane(this._planeData!);\r\n        return this.mainPlane;\r\n    }\r\n\r\n    mainReset() {\r\n        if (this.mainPlane) {\r\n            this.mainPlane.node.destroy();\r\n            this.mainPlane = null;\r\n        }\r\n    }\r\n\r\n    checkCanRevive(): boolean {\r\n        return this.reviveCount < this.maxReviveCount;\r\n    }\r\n\r\n    revive() {\r\n        GameIns.mainPlaneManager.reviveCount += 1; // 增加复活次数\r\n        this.mainPlane?.revive();\r\n    }\r\n\r\n    updateGameLogic(dt: number): void {\r\n        this.mainPlane?.updateGameLogic(dt);\r\n    }\r\n}\r\n"]}