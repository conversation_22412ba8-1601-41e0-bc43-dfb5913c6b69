{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/autogen/luban/schema.ts"], "names": ["A<PERSON><PERSON><PERSON><PERSON>", "ConParam", "ConsumeItem", "ConsumeMoney", "EffectParam", "GM", "PlaneEffect", "PlaneMaterial", "PlaneProperty", "PlanePropertyElem", "randStrategy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ResAchievement", "ResActivity", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ResChapter", "ResCondition", "ResEffect", "ResEmitter", "ResEnemy", "ResEquip", "ResEquipUpgrade", "ResGameMode", "ResGlobalAttr", "ResItem", "ResLevel", "ResLevelGroup", "ResLoot", "ResLootItem", "ResModeReward", "ResPlane", "ResSkill", "ResSkillCondition", "ResSkillConditionElem", "ResStore", "ResTask", "ResTaskGoal", "ResTaskOrbit", "ResUpgrade", "TbGlobalAttr", "TbEquipUpgrade", "TbGM", "TbPlane", "TbResTask", "TbResAchievement", "TbResActivity", "TbRes<PERSON>uffer", "TbResEmitter", "TbResChapter", "TbResEffect", "TbResEnemy", "TbResSkill", "TbResLevel", "TbResLoot", "TbResEquip", "TbResItem", "TbResGameMode", "TbResLevelGroup", "TbResModeReward", "TbResUpgrade", "TbResTaskOrbit", "TbResStore", "TbResSkillCondition", "Tables", "BindSocket", "BoolOpType", "BuffType", "BulletSourceType", "BulletType", "CondOPType", "DamageType", "EffectType", "EquipClass", "FunctionID", "GMTabID", "GoodsClass", "ItemEffectType", "ItemUseType", "ModeType", "MoneyType", "OrientationType", "PlanePropType", "QualityType", "ResCondType", "ResGoalType", "ResLootType", "ResPeriodType", "ResTaskClass", "SkillConditionType", "TargetType", "constructor", "_json_", "target", "buff<PERSON>", "undefined", "Error", "resolve", "tables", "vector2", "x", "y", "builtin", "vector3", "z", "vector4", "w", "con", "param", "id", "num", "type", "_ele0", "_e0", "push", "tabID", "tabName", "name", "cmd", "desc", "effectId", "effect_id", "materialId", "materialCount", "material_id", "material_count", "MaxHP", "HPRecovery", "Attack", "Fortunate", "Miss", "BulletHurtResistance", "CollisionHurtResistance", "PickRadius", "FinalScore", "NuclearMax", "MaxEnergy", "EnergyRecovery", "propType", "prop<PERSON><PERSON><PERSON>", "prop_type", "prop_param", "ID", "Weight", "rating", "taskId", "groupId", "prevId", "periodType", "taskCond", "taskGoal", "accumulate", "rewardId", "linkTo", "task_id", "group_id", "prev_id", "period_type", "task_cond", "task_goal", "reward_id", "link_to", "_e", "uiRes", "precvId", "accumlate", "openDate", "openTime", "closeDate", "closeTime", "ui_res", "precv_id", "open_date", "open_time", "close_date", "close_time", "buffType", "triggerCondition", "forbinCondition", "removeCondition", "duration", "durationBonus", "maxStack", "refreshType", "cycle", "cycleTimes", "EffectPath", "EffectSocket", "EffectPos", "effects", "levelCount", "levelGroupCount", "strategy", "chapterTitle", "<PERSON><PERSON><PERSON>", "enemyFaction", "levelDesc", "combatRequire", "strategyList", "condType", "params", "cond_type", "description", "icon", "effectType", "effectValue", "effectParams", "effect_type", "effect_value", "effect_params", "source", "damageType", "prefab", "attackCoefficient", "penetrationCount", "penetrationCooldown", "hitBounceCount", "boundaryBounceCount", "damage_type", "attack_coefficient", "penetration_count", "penetration_cooldown", "hit_bounce_count", "boundary_bounce_count", "comment", "rank", "purpose", "baseHp", "baseAtk", "moveSpeed", "turnSpeed", "orientation", "killScore", "showHpBar", "useHitCount", "hitCountToKill", "hitCountInterval", "targetPriority", "immuneBulletDamage", "immuneCollideDamage", "ignoreBullet", "ignoreCollide", "immuneNuke", "immuneActiveSkill", "invincible", "collideLevel", "collideDamage", "dropRadius", "base_hp", "base_atk", "move_speed", "turn_speed", "kill_score", "show_hp_bar", "use_hit_count", "hit_count_to_kill", "hit_count_interval", "target_priority", "immune_bullet_damage", "immune_collide_damage", "ignore_bullet", "ignore_collide", "immune_nuke", "immune_active_skill", "collide_level", "collide_damage", "drop_radius", "quality", "qualitySub", "equipClass", "props", "consumeItems", "quality_sub", "equip_class", "consume_items", "levelFrom", "levelTo", "propInc", "consumeMoney", "level_from", "level_to", "prop_inc", "consume_money", "modeID", "modeType", "chapterID", "order", "resourceID", "conList", "times", "costEnergy", "monType", "costParam1", "costParam2", "rebirthTimes", "rebirthCost", "power", "rogueID", "LevelLimit", "rogueFirst", "sweepLimit", "rewardID", "ratingList", "GoldProducion", "EnergyRecoverInterval", "EnergyRecoverValue", "ItemPickUpRadius", "PostHitProtection", "CameraTranslationMaxMoveSpeed", "CameraTranslationMoveDelay", "MaxEndlessDoubleRewardTimes", "MaxStoryDoubleRewardTimes", "MaxMailKeepDays", "useType", "effectParam1", "effectParam2", "maxStack<PERSON>um", "use_type", "effect_param1", "effect_param2", "max_stack_num", "forbidFire", "forbidNBomb", "forbidActSkill", "planeCollisionScaling", "levelType", "normLevelCount", "normLevelST", "normSTList", "bossLevelCount", "bossLevelST", "bossSTList", "lootId", "lootGroup", "lootType", "itemList", "loot_id", "loot_group", "loot_type", "item_list", "itemId", "count", "rate", "protectTimes", "item_id", "protect_times", "levelReward", "passStar1Reward", "passStar2Reward", "passStar3Reward", "starLevel", "portrait", "property", "materials", "star_level", "cd", "CostID", "CostNum", "conditionID", "ApplyBuffs", "boolType", "conditions", "op", "value", "goodsClass", "price", "moneyType", "moneyItemID", "discount", "limitPeriod", "limitCount", "itemID", "listing", "delisting", "newFlag", "freeFlag", "hotFlag", "adPos", "taskClass", "needPay", "orbitGroupId", "orbitValue", "task_class", "need_pay", "orbit_group_id", "orbit_value", "goalType", "goal_type", "index", "roleLevel", "xp", "functionUnlock", "role_level", "function_unlock", "_data", "length", "getData", "_dataList", "_json2_", "_v", "getDataList", "get", "data", "_dataMap", "Map", "set", "getDataMap", "key", "_TbGlobalAttr", "_TbEquipUpgrade", "_TbGM", "_TbPlane", "_TbResTask", "_TbResAchievement", "_TbResActivity", "_Tb<PERSON>es<PERSON>uffer", "_TbResEmitter", "_TbResChapter", "_TbResEffect", "_TbResEnemy", "_TbResSkill", "_TbResLevel", "_TbResLoot", "_TbResEquip", "_TbResItem", "_TbResGameMode", "_TbResLevelGroup", "_TbResModeReward", "_TbResUpgrade", "_TbResTaskOrbit", "_TbResStore", "_TbResSkillCondition", "loader"], "mappings": ";;;iBAmlCaA,S,EAoGAC,Q,EAyBAC,W,EAsBAC,Y,EA4BAC,W,EA0BAC,E,EAoDAC,W,EAqBAC,a,EAyBAC,a,EAiEAC,iB,EAyBAC,Y,EA4BAC,W,EAsBAC,c,EAuEAC,W,EAiHAC,S,EAoHAC,U,EAoFAC,Y,EAsBAC,S,EA+DAC,U,EA2FAC,Q,EA2MAC,Q,EAsEAC,e,EAwDAC,W,EAkKAC,a,EA2FAC,O,EAoFAC,Q,EA+DAC,a,EAyDAC,O,EA0CAC,W,EAiCAC,a,EAiDAC,Q,EAsGAC,Q,EA8EAC,iB,EAuCAC,qB,EA8BAC,Q,EA4IAC,O,EAqIAC,W,EA0BAC,Y,EAuEAC,U,EA2CAC,Y,EAiEAC,c,EA4BAC,I,EA4BAC,O,EA+BAC,S,EA+BAC,gB,EA+BAC,a,EA+BAC,W,EA+BAC,Y,EA+BAC,Y,EA+BAC,W,EA+BAC,U,EA+BAC,U,EA+BAC,U,EA+BAC,S,EA+BAC,U,EA+BAC,S,EA+BAC,a,EA+BAC,e,EA+BAC,e,EA+BAC,Y,EA+BAC,c,EA+BAC,U,EA+BAC,mB,EAiCAC,M;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAn2Ib;AACA;AACA;AACA;AACA;AACA;AACA;;AAIA;AACA;AACA;4BACYC,U,0BAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;eAAAA,U;;AAaZ;AACA;AACA;;;4BACYC,U,0BAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;eAAAA,U;;AAaZ;AACA;AACA;;;0BACYC,Q,0BAAAA,Q;AAAAA,QAAAA,Q,CAAAA,Q;AAAAA,QAAAA,Q,CAAAA,Q;AAAAA,QAAAA,Q,CAAAA,Q;eAAAA,Q;;AAiBZ;AACA;AACA;;;kCACYC,gB,0BAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;eAAAA,gB;;AAiBZ;AACA;AACA;;;4BACYC,U,0BAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;eAAAA,U;;AAaZ;AACA;AACA;;;4BACYC,U,0BAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;eAAAA,U;;AA6BZ;AACA;AACA;;;4BACYC,U,0BAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;eAAAA,U;;AAyBZ;AACA;AACA;;;4BACYC,U,0BAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;eAAAA,U;;AA6LZ;AACA;AACA;;;4BACYC,U,0BAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;eAAAA,U;;AA6BZ;AACA;AACA;;;4BACYC,U,0BAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;eAAAA,U;;AAyBZ;AACA;AACA;;;yBACYC,O,0BAAAA,O;AAAAA,QAAAA,O,CAAAA,O;AAAAA,QAAAA,O,CAAAA,O;eAAAA,O;;AAaZ;AACA;AACA;;;4BACYC,U,0BAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;eAAAA,U;;AA6BZ;AACA;AACA;;;gCACYC,c,0BAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;eAAAA,c;;AAqDZ;AACA;AACA;;;6BACYC,W,0BAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;eAAAA,W;;AAiBZ;AACA;AACA;;;0BACYC,Q,0BAAAA,Q;AAAAA,QAAAA,Q,CAAAA,Q;AAAAA,QAAAA,Q,CAAAA,Q;AAAAA,QAAAA,Q,CAAAA,Q;AAAAA,QAAAA,Q,CAAAA,Q;AAAAA,QAAAA,Q,CAAAA,Q;AAAAA,QAAAA,Q,CAAAA,Q;eAAAA,Q;;AA6BZ;AACA;AACA;;;2BACYC,S,0BAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;eAAAA,S;;AA6BZ;AACA;AACA;;;iCACYC,e,0BAAAA,e;AAAAA,QAAAA,e,CAAAA,e;AAAAA,QAAAA,e,CAAAA,e;AAAAA,QAAAA,e,CAAAA,e;eAAAA,e;;AAiBZ;AACA;AACA;;;+BACYC,a,0BAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;eAAAA,a;;AA6EZ;AACA;AACA;;;6BACYC,W,0BAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;eAAAA,W;;AA6BZ;AACA;AACA;;;6BACYC,W,0BAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;eAAAA,W;;AAyCZ;AACA;AACA;;;6BACYC,W,0BAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;eAAAA,W;;AAyHZ;AACA;AACA;;;6BACYC,W,0BAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;eAAAA,W;;AAaZ;AACA;AACA;;;+BACYC,a,0BAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;eAAAA,a;;AAqBZ;AACA;AACA;;;8BACYC,Y,0BAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;eAAAA,Y;;AAqCZ;AACA;AACA;;;oCACYC,kB,0BAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;eAAAA,kB;;AAyFZ;AACA;AACA;;;4BACYC,U,0BAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;eAAAA,U;;;2BAiCCzF,S,GAAN,MAAMA,SAAN,CAAgB;AAEnB0F,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAOhBC,MAPgB;AAAA,eAQhBC,MARgB;;AACrB,cAAIF,MAAM,CAACC,MAAP,KAAkBE,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKH,MAAL,GAAcD,MAAM,CAACC,MAArB;;AACA,cAAID,MAAM,CAACE,MAAP,KAAkBC,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKF,MAAL,GAAcF,MAAM,CAACE,MAArB;AACH;;AAKDG,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfkB,O;;;AAsBhB,cAAMC,OAAN,CAAc;AAEjBR,UAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,iBAOhBQ,CAPgB;AAAA,iBAQhBC,CARgB;;AACrB,gBAAIT,MAAM,CAACQ,CAAP,KAAaL,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKI,CAAL,GAASR,MAAM,CAACQ,CAAhB;;AACA,gBAAIR,MAAM,CAACS,CAAP,KAAaN,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKK,CAAL,GAAST,MAAM,CAACS,CAAhB;AACH;;AAKDJ,UAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfgB;;;SADJI,O,uBAAAA,O;;;AAuBV,cAAMC,OAAN,CAAc;AAEjBZ,UAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,iBAShBQ,CATgB;AAAA,iBAUhBC,CAVgB;AAAA,iBAWhBG,CAXgB;;AACrB,gBAAIZ,MAAM,CAACQ,CAAP,KAAaL,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKI,CAAL,GAASR,MAAM,CAACQ,CAAhB;;AACA,gBAAIR,MAAM,CAACS,CAAP,KAAaN,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKK,CAAL,GAAST,MAAM,CAACS,CAAhB;;AACA,gBAAIT,MAAM,CAACY,CAAP,KAAaT,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKQ,CAAL,GAASZ,MAAM,CAACY,CAAhB;AACH;;AAMDP,UAAAA,OAAO,CAACC,MAAD,EAAgB,CAItB;;AAnBgB;;;SADJI,O,uBAAAA,O;;;AA2BV,cAAMG,OAAN,CAAc;AAEjBd,UAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,iBAWhBQ,CAXgB;AAAA,iBAYhBC,CAZgB;AAAA,iBAahBG,CAbgB;AAAA,iBAchBE,CAdgB;;AACrB,gBAAId,MAAM,CAACQ,CAAP,KAAaL,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKI,CAAL,GAASR,MAAM,CAACQ,CAAhB;;AACA,gBAAIR,MAAM,CAACS,CAAP,KAAaN,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKK,CAAL,GAAST,MAAM,CAACS,CAAhB;;AACA,gBAAIT,MAAM,CAACY,CAAP,KAAaT,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKQ,CAAL,GAASZ,MAAM,CAACY,CAAhB;;AACA,gBAAIZ,MAAM,CAACc,CAAP,KAAaX,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKU,CAAL,GAASd,MAAM,CAACc,CAAhB;AACH;;AAODT,UAAAA,OAAO,CAACC,MAAD,EAAgB,CAKtB;;AAvBgB;;;SADJI,O,uBAAAA,O;;0BA+BJpG,Q,GAAN,MAAMA,QAAN,CAAe;AAElByF,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAOhBe,GAPgB;AAAA,eAQhBC,KARgB;;AACrB,cAAIhB,MAAM,CAACe,GAAP,KAAeZ,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKW,GAAL,GAAWf,MAAM,CAACe,GAAlB;;AACA,cAAIf,MAAM,CAACgB,KAAP,KAAiBb,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKY,KAAL,GAAahB,MAAM,CAACgB,KAApB;AACH;;AAKDX,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfiB,O;AAsBtB;AACA;AACA;;;6BACa/F,W,GAAN,MAAMA,WAAN,CAAkB;AAErBwF,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAOhBiB,EAPgB;AAAA,eAQhBC,GARgB;;AACrB,cAAIlB,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAACkB,GAAP,KAAef,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKc,GAAL,GAAWlB,MAAM,CAACkB,GAAlB;AACH;;AAKDb,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfoB,O;;8BAsBZ9F,Y,GAAN,MAAMA,YAAN,CAAmB;AAEtBuF,QAAAA,WAAW,CAACC,MAAD,EAAc;AAOzB;AACJ;AACA;AAT6B,eAUhBmB,IAVgB;;AAWzB;AACJ;AACA;AAb6B,eAchBD,GAdgB;;AACrB,cAAIlB,MAAM,CAACmB,IAAP,KAAgBhB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKe,IAAL,GAAYnB,MAAM,CAACmB,IAAnB;;AACA,cAAInB,MAAM,CAACkB,GAAP,KAAef,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKc,GAAL,GAAWlB,MAAM,CAACkB,GAAlB;AACH;;AAWDb,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AArBqB,O;;6BA4Bb7F,W,GAAN,MAAMA,WAAN,CAAkB;AAErBsF,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAShBmB,IATgB;AAAA,eAUhBlB,MAVgB;AAAA,eAWhBe,KAXgB;;AACrB,cAAIhB,MAAM,CAACmB,IAAP,KAAgBhB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKe,IAAL,GAAYnB,MAAM,CAACmB,IAAnB;;AACA,cAAInB,MAAM,CAACC,MAAP,KAAkBE,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKH,MAAL,GAAcD,MAAM,CAACC,MAArB;;AACA,cAAID,MAAM,CAACgB,KAAP,KAAiBb,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD;AAAE,iBAAKY,KAAL,GAAa,EAAb;;AAAiB,iBAAI,IAAII,KAAR,IAAiBpB,MAAM,CAACgB,KAAxB,EAA+B;AAAE,kBAAIK,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKJ,KAAL,CAAWM,IAAX,CAAgBD,GAAhB;AAAsB;AAAC;AACpG;;AAMDhB,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAItB;;AAnBoB,O;;oBA0BZ5F,E,GAAN,MAAMA,EAAN,CAAS;AAEZqF,QAAAA,WAAW,CAACC,MAAD,EAAc;AAazB;AACJ;AACA;AAf6B,eAgBhBuB,KAhBgB;;AAiBzB;AACJ;AACA;AAnB6B,eAoBhBC,OApBgB;;AAqBzB;AACJ;AACA;AAvB6B,eAwBhBC,IAxBgB;;AAyBzB;AACJ;AACA;AA3B6B,eA4BhBC,GA5BgB;;AA6BzB;AACJ;AACA;AA/B6B,eAgChBC,IAhCgB;;AACrB,cAAI3B,MAAM,CAACuB,KAAP,KAAiBpB,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKmB,KAAL,GAAavB,MAAM,CAACuB,KAApB;;AACA,cAAIvB,MAAM,CAACwB,OAAP,KAAmBrB,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKoB,OAAL,GAAexB,MAAM,CAACwB,OAAtB;;AACA,cAAIxB,MAAM,CAACyB,IAAP,KAAgBtB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKqB,IAAL,GAAYzB,MAAM,CAACyB,IAAnB;;AACA,cAAIzB,MAAM,CAAC0B,GAAP,KAAevB,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKsB,GAAL,GAAW1B,MAAM,CAAC0B,GAAlB;;AACA,cAAI1B,MAAM,CAAC2B,IAAP,KAAgBxB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKuB,IAAL,GAAY3B,MAAM,CAAC2B,IAAnB;AACH;;AAuBDtB,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAMtB;;AA1CW,O;AAiDhB;AACA;AACA;;;6BACa3F,W,GAAN,MAAMA,WAAN,CAAkB;AAErBoF,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAKhB4B,QALgB;;AACrB,cAAI5B,MAAM,CAAC6B,SAAP,KAAqB1B,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKwB,QAAL,GAAgB5B,MAAM,CAAC6B,SAAvB;AACH;;AAIDxB,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAEtB;;AAXoB,O;AAkBzB;AACA;AACA;;;+BACa1F,a,GAAN,MAAMA,aAAN,CAAoB;AAEvBmF,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAOhB8B,UAPgB;AAAA,eAQhBC,aARgB;;AACrB,cAAI/B,MAAM,CAACgC,WAAP,KAAuB7B,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAK0B,UAAL,GAAkB9B,MAAM,CAACgC,WAAzB;;AACA,cAAIhC,MAAM,CAACiC,cAAP,KAA0B9B,SAA9B,EAAyC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC9D,eAAK2B,aAAL,GAAqB/B,MAAM,CAACiC,cAA5B;AACH;;AAKD5B,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfsB,O;AAsB3B;AACA;AACA;;;+BACazF,a,GAAN,MAAMA,aAAN,CAAoB;AAEvBkF,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eA2BhBkC,KA3BgB;AAAA,eA4BhBC,UA5BgB;AAAA,eA6BhBC,MA7BgB;AAAA,eA8BhBC,SA9BgB;AAAA,eA+BhBC,IA/BgB;AAAA,eAgChBC,oBAhCgB;AAAA,eAiChBC,uBAjCgB;AAAA,eAkChBC,UAlCgB;AAAA,eAmChBC,UAnCgB;AAAA,eAoChBC,UApCgB;AAAA,eAqChBC,SArCgB;AAAA,eAsChBC,cAtCgB;;AACrB,cAAI7C,MAAM,CAACkC,KAAP,KAAiB/B,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAK8B,KAAL,GAAalC,MAAM,CAACkC,KAApB;;AACA,cAAIlC,MAAM,CAACmC,UAAP,KAAsBhC,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK+B,UAAL,GAAkBnC,MAAM,CAACmC,UAAzB;;AACA,cAAInC,MAAM,CAACoC,MAAP,KAAkBjC,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKgC,MAAL,GAAcpC,MAAM,CAACoC,MAArB;;AACA,cAAIpC,MAAM,CAACqC,SAAP,KAAqBlC,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKiC,SAAL,GAAiBrC,MAAM,CAACqC,SAAxB;;AACA,cAAIrC,MAAM,CAACsC,IAAP,KAAgBnC,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKkC,IAAL,GAAYtC,MAAM,CAACsC,IAAnB;;AACA,cAAItC,MAAM,CAACuC,oBAAP,KAAgCpC,SAApC,EAA+C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpE,eAAKmC,oBAAL,GAA4BvC,MAAM,CAACuC,oBAAnC;;AACA,cAAIvC,MAAM,CAACwC,uBAAP,KAAmCrC,SAAvC,EAAkD;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvE,eAAKoC,uBAAL,GAA+BxC,MAAM,CAACwC,uBAAtC;;AACA,cAAIxC,MAAM,CAACyC,UAAP,KAAsBtC,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKqC,UAAL,GAAkBzC,MAAM,CAACyC,UAAzB;;AACA,cAAIzC,MAAM,CAAC0C,UAAP,KAAsBvC,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKsC,UAAL,GAAkB1C,MAAM,CAAC0C,UAAzB;;AACA,cAAI1C,MAAM,CAAC2C,UAAP,KAAsBxC,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKuC,UAAL,GAAkB3C,MAAM,CAAC2C,UAAzB;;AACA,cAAI3C,MAAM,CAAC4C,SAAP,KAAqBzC,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKwC,SAAL,GAAiB5C,MAAM,CAAC4C,SAAxB;;AACA,cAAI5C,MAAM,CAAC6C,cAAP,KAA0B1C,SAA9B,EAAyC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC9D,eAAKyC,cAAL,GAAsB7C,MAAM,CAAC6C,cAA7B;AACH;;AAeDxC,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAatB;;AAvDsB,O;AA8D3B;AACA;AACA;;;mCACaxF,iB,GAAN,MAAMA,iBAAN,CAAwB;AAE3BiF,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAOhB8C,QAPgB;AAAA,eAQhBC,SARgB;;AACrB,cAAI/C,MAAM,CAACgD,SAAP,KAAqB7C,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAK0C,QAAL,GAAgB9C,MAAM,CAACgD,SAAvB;;AACA,cAAIhD,MAAM,CAACiD,UAAP,KAAsB9C,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK2C,SAAL,GAAiB/C,MAAM,CAACiD,UAAxB;AACH;;AAKD5C,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAf0B,O;AAsB/B;AACA;AACA;;;8BACavF,Y,GAAN,MAAMA,YAAN,CAAmB;AAEtBgF,QAAAA,WAAW,CAACC,MAAD,EAAc;AAOzB;AACJ;AACA;AAT6B,eAUhBkD,EAVgB;;AAWzB;AACJ;AACA;AAb6B,eAchBC,MAdgB;;AACrB,cAAInD,MAAM,CAACkD,EAAP,KAAc/C,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAK8C,EAAL,GAAUlD,MAAM,CAACkD,EAAjB;;AACA,cAAIlD,MAAM,CAACmD,MAAP,KAAkBhD,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAK+C,MAAL,GAAcnD,MAAM,CAACmD,MAArB;AACH;;AAWD9C,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AArBqB,O;;6BA4BbtF,W,GAAN,MAAMA,WAAN,CAAkB;AAErB+E,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAOhBoD,MAPgB;AAAA,eAQhBpC,KARgB;;AACrB,cAAIhB,MAAM,CAACoD,MAAP,KAAkBjD,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKgD,MAAL,GAAcpD,MAAM,CAACoD,MAArB;;AACA,cAAIpD,MAAM,CAACgB,KAAP,KAAiBb,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKY,KAAL,GAAahB,MAAM,CAACgB,KAApB;AACH;;AAKDX,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfoB,O;;gCAsBZrF,c,GAAN,MAAMA,cAAN,CAAqB;AAExB8E,QAAAA,WAAW,CAACC,MAAD,EAAc;AAqBzB;AACJ;AACA;AAvB6B,eAwBhBqD,MAxBgB;;AAyBzB;AACJ;AACA;AA3B6B,eA4BhBC,OA5BgB;;AA6BzB;AACJ;AACA;AA/B6B,eAgChBC,MAhCgB;;AAiCzB;AACJ;AACA;AAnC6B,eAoChBC,UApCgB;AAAA,eAqChBC,QArCgB;AAAA,eAsChBC,QAtCgB;;AAuCzB;AACJ;AACA;AAzC6B,eA0ChBC,UA1CgB;;AA2CzB;AACJ;AACA;AA7C6B,eA8ChBC,QA9CgB;;AA+CzB;AACJ;AACA;AAjD6B,eAkDhBC,MAlDgB;;AACrB,cAAI7D,MAAM,CAAC8D,OAAP,KAAmB3D,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKiD,MAAL,GAAcrD,MAAM,CAAC8D,OAArB;;AACA,cAAI9D,MAAM,CAAC+D,QAAP,KAAoB5D,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKkD,OAAL,GAAetD,MAAM,CAAC+D,QAAtB;;AACA,cAAI/D,MAAM,CAACgE,OAAP,KAAmB7D,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKmD,MAAL,GAAcvD,MAAM,CAACgE,OAArB;;AACA,cAAIhE,MAAM,CAACiE,WAAP,KAAuB9D,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKoD,UAAL,GAAkBxD,MAAM,CAACiE,WAAzB;;AACA,cAAIjE,MAAM,CAACkE,SAAP,KAAqB/D,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD;AAAE,iBAAKqD,QAAL,GAAgB,EAAhB;;AAAoB,iBAAI,IAAIrC,KAAR,IAAiBpB,MAAM,CAACkE,SAAxB,EAAmC;AAAE,kBAAI7C,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAIhG,YAAJ,CAAiB+F,KAAjB,CAAN;AAA+B,mBAAKqC,QAAL,CAAcnC,IAAd,CAAmBD,GAAnB;AAAyB;AAAC;;AAC7H,cAAIrB,MAAM,CAACmE,SAAP,KAAqBhE,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKsD,QAAL,GAAgB,IAAIjH,WAAJ,CAAgBuD,MAAM,CAACmE,SAAvB,CAAhB;;AACA,cAAInE,MAAM,CAAC2D,UAAP,KAAsBxD,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKuD,UAAL,GAAkB3D,MAAM,CAAC2D,UAAzB;;AACA,cAAI3D,MAAM,CAACoE,SAAP,KAAqBjE,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKwD,QAAL,GAAgB5D,MAAM,CAACoE,SAAvB;;AACA,cAAIpE,MAAM,CAACqE,OAAP,KAAmBlE,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKyD,MAAL,GAAc7D,MAAM,CAACqE,OAArB;AACH;;AAiCDhE,QAAAA,OAAO,CAACC,MAAD,EAAgB;AAAA;;AAKnB,eAAK,IAAIgE,EAAT,IAAe,KAAKb,QAApB,EAA8B;AAAEa,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAEjE,OAAJ,CAAYC,MAAZ;AAAsB;;AACtD,iCAAKoD,QAAL,4BAAerD,OAAf,CAAuBC,MAAvB;AAIH;;AAhEuB,O;;6BAuEfpF,W,GAAN,MAAMA,WAAN,CAAkB;AAErB6E,QAAAA,WAAW,CAACC,MAAD,EAAc;AAiCzB;AACJ;AACA;AAnC6B,eAoChBqD,MApCgB;;AAqCzB;AACJ;AACA;AAvC6B,eAwChBC,OAxCgB;;AAyCzB;AACJ;AACA;AA3C6B,eA4ChB3B,IA5CgB;;AA6CzB;AACJ;AACA;AA/C6B,eAgDhB4C,KAhDgB;;AAiDzB;AACJ;AACA;AAnD6B,eAoDhBC,OApDgB;;AAqDzB;AACJ;AACA;AAvD6B,eAwDhBhB,UAxDgB;AAAA,eAyDhBC,QAzDgB;AAAA,eA0DhBC,QA1DgB;;AA2DzB;AACJ;AACA;AA7D6B,eA8DhBe,SA9DgB;;AA+DzB;AACJ;AACA;AAjE6B,eAkEhBb,QAlEgB;;AAmEzB;AACJ;AACA;AArE6B,eAsEhBc,QAtEgB;;AAuEzB;AACJ;AACA;AAzE6B,eA0EhBC,QA1EgB;;AA2EzB;AACJ;AACA;AA7E6B,eA8EhBC,SA9EgB;;AA+EzB;AACJ;AACA;AAjF6B,eAkFhBC,SAlFgB;;AAmFzB;AACJ;AACA;AArF6B,eAsFhBhB,MAtFgB;;AACrB,cAAI7D,MAAM,CAAC8D,OAAP,KAAmB3D,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKiD,MAAL,GAAcrD,MAAM,CAAC8D,OAArB;;AACA,cAAI9D,MAAM,CAAC+D,QAAP,KAAoB5D,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKkD,OAAL,GAAetD,MAAM,CAAC+D,QAAtB;;AACA,cAAI/D,MAAM,CAAC2B,IAAP,KAAgBxB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKuB,IAAL,GAAY3B,MAAM,CAAC2B,IAAnB;;AACA,cAAI3B,MAAM,CAAC8E,MAAP,KAAkB3E,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKmE,KAAL,GAAavE,MAAM,CAAC8E,MAApB;;AACA,cAAI9E,MAAM,CAAC+E,QAAP,KAAoB5E,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKoE,OAAL,GAAexE,MAAM,CAAC+E,QAAtB;;AACA,cAAI/E,MAAM,CAACiE,WAAP,KAAuB9D,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKoD,UAAL,GAAkBxD,MAAM,CAACiE,WAAzB;;AACA,cAAIjE,MAAM,CAACkE,SAAP,KAAqB/D,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD;AAAE,iBAAKqD,QAAL,GAAgB,EAAhB;;AAAoB,iBAAI,IAAIrC,KAAR,IAAiBpB,MAAM,CAACkE,SAAxB,EAAmC;AAAE,kBAAI7C,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAIhG,YAAJ,CAAiB+F,KAAjB,CAAN;AAA+B,mBAAKqC,QAAL,CAAcnC,IAAd,CAAmBD,GAAnB;AAAyB;AAAC;;AAC7H,cAAIrB,MAAM,CAACmE,SAAP,KAAqBhE,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKsD,QAAL,GAAgB,IAAIjH,WAAJ,CAAgBuD,MAAM,CAACmE,SAAvB,CAAhB;;AACA,cAAInE,MAAM,CAACyE,SAAP,KAAqBtE,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKqE,SAAL,GAAiBzE,MAAM,CAACyE,SAAxB;;AACA,cAAIzE,MAAM,CAACoE,SAAP,KAAqBjE,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKwD,QAAL,GAAgB5D,MAAM,CAACoE,SAAvB;;AACA,cAAIpE,MAAM,CAACgF,SAAP,KAAqB7E,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKsE,QAAL,GAAgB1E,MAAM,CAACgF,SAAvB;;AACA,cAAIhF,MAAM,CAACiF,SAAP,KAAqB9E,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKuE,QAAL,GAAgB3E,MAAM,CAACiF,SAAvB;;AACA,cAAIjF,MAAM,CAACkF,UAAP,KAAsB/E,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKwE,SAAL,GAAiB5E,MAAM,CAACkF,UAAxB;;AACA,cAAIlF,MAAM,CAACmF,UAAP,KAAsBhF,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKyE,SAAL,GAAiB7E,MAAM,CAACmF,UAAxB;;AACA,cAAInF,MAAM,CAACqE,OAAP,KAAmBlE,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKyD,MAAL,GAAc7D,MAAM,CAACqE,OAArB;AACH;;AAyDDhE,QAAAA,OAAO,CAACC,MAAD,EAAgB;AAAA;;AAOnB,eAAK,IAAIgE,EAAT,IAAe,KAAKb,QAApB,EAA8B;AAAEa,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAEjE,OAAJ,CAAYC,MAAZ;AAAsB;;AACtD,kCAAKoD,QAAL,6BAAerD,OAAf,CAAuBC,MAAvB;AAQH;;AA1GoB,O;;2BAiHZnF,S,GAAN,MAAMA,SAAN,CAAgB;AAEnB4E,QAAAA,WAAW,CAACC,MAAD,EAAc;AAiCzB;AACJ;AACA;AAnC6B,eAoChBiB,EApCgB;;AAqCzB;AACJ;AACA;AAvC6B,eAwChBmE,QAxCgB;;AAyCzB;AACJ;AACA;AA3C6B,eA4ChBC,gBA5CgB;;AA6CzB;AACJ;AACA;AA/C6B,eAgDhBC,eAhDgB;;AAiDzB;AACJ;AACA;AAnD6B,eAoDhBC,eApDgB;;AAqDzB;AACJ;AACA;AAvD6B,eAwDhBC,QAxDgB;;AAyDzB;AACJ;AACA;AA3D6B,eA4DhBC,aA5DgB;;AA6DzB;AACJ;AACA;AA/D6B,eAgEhBC,QAhEgB;;AAiEzB;AACJ;AACA;AAnE6B,eAoEhBC,WApEgB;;AAqEzB;AACJ;AACA;AAvE6B,eAwEhBC,KAxEgB;;AAyEzB;AACJ;AACA;AA3E6B,eA4EhBC,UA5EgB;;AA6EzB;AACJ;AACA;AA/E6B,eAgFhBC,UAhFgB;;AAiFzB;AACJ;AACA;AAnF6B,eAoFhBC,YApFgB;;AAqFzB;AACJ;AACA;AAvF6B,eAwFhBC,SAxFgB;AAAA,eAyFhBC,OAzFgB;;AACrB,cAAIjG,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAACoF,QAAP,KAAoBjF,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKgF,QAAL,GAAgBpF,MAAM,CAACoF,QAAvB;;AACA,cAAIpF,MAAM,CAACqF,gBAAP,KAA4BlF,SAAhC,EAA2C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAChE,eAAKiF,gBAAL,GAAwBrF,MAAM,CAACqF,gBAA/B;;AACA,cAAIrF,MAAM,CAACsF,eAAP,KAA2BnF,SAA/B,EAA0C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC/D,eAAKkF,eAAL,GAAuBtF,MAAM,CAACsF,eAA9B;;AACA,cAAItF,MAAM,CAACuF,eAAP,KAA2BpF,SAA/B,EAA0C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC/D,eAAKmF,eAAL,GAAuBvF,MAAM,CAACuF,eAA9B;;AACA,cAAIvF,MAAM,CAACwF,QAAP,KAAoBrF,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKoF,QAAL,GAAgBxF,MAAM,CAACwF,QAAvB;;AACA,cAAIxF,MAAM,CAACyF,aAAP,KAAyBtF,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAKqF,aAAL,GAAqBzF,MAAM,CAACyF,aAA5B;;AACA,cAAIzF,MAAM,CAAC0F,QAAP,KAAoBvF,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKsF,QAAL,GAAgB1F,MAAM,CAAC0F,QAAvB;;AACA,cAAI1F,MAAM,CAAC2F,WAAP,KAAuBxF,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKuF,WAAL,GAAmB3F,MAAM,CAAC2F,WAA1B;;AACA,cAAI3F,MAAM,CAAC4F,KAAP,KAAiBzF,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKwF,KAAL,GAAa5F,MAAM,CAAC4F,KAApB;;AACA,cAAI5F,MAAM,CAAC6F,UAAP,KAAsB1F,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKyF,UAAL,GAAkB7F,MAAM,CAAC6F,UAAzB;;AACA,cAAI7F,MAAM,CAAC8F,UAAP,KAAsB3F,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK0F,UAAL,GAAkB9F,MAAM,CAAC8F,UAAzB;;AACA,cAAI9F,MAAM,CAAC+F,YAAP,KAAwB5F,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D,eAAK2F,YAAL,GAAoB/F,MAAM,CAAC+F,YAA3B;;AACA,cAAI/F,MAAM,CAACgG,SAAP,KAAqB7F,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAK4F,SAAL,GAAiB,IAAItF,OAAO,CAACH,OAAZ,CAAoBP,MAAM,CAACgG,SAA3B,CAAjB;;AACA,cAAIhG,MAAM,CAACiG,OAAP,KAAmB9F,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD;AAAE,iBAAK6F,OAAL,GAAe,EAAf;;AAAmB,iBAAI,IAAI7E,KAAR,IAAiBpB,MAAM,CAACiG,OAAxB,EAAiC;AAAE,kBAAI5E,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAI5G,WAAJ,CAAgB2G,KAAhB,CAAN;AAA8B,mBAAK6E,OAAL,CAAa3E,IAAb,CAAkBD,GAAlB;AAAwB;AAAC;AAC3H;;AA4DDhB,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAgBtB;;AA7GkB,O;;4BAoHVlF,U,GAAN,MAAMA,UAAN,CAAiB;AAEpB2E,QAAAA,WAAW,CAACC,MAAD,EAAc;AAuBzB;AACJ;AACA;AAzB6B,eA0BhBiB,EA1BgB;;AA2BzB;AACJ;AACA;AA7B6B,eA8BhBiF,UA9BgB;;AA+BzB;AACJ;AACA;AAjC6B,eAkChBC,eAlCgB;;AAmCzB;AACJ;AACA;AArC6B,eAsChBC,QAtCgB;;AAuCzB;AACJ;AACA;AAzC6B,eA0ChBC,YA1CgB;;AA2CzB;AACJ;AACA;AA7C6B,eA8ChBC,QA9CgB;;AA+CzB;AACJ;AACA;AAjD6B,eAkDhBC,YAlDgB;;AAmDzB;AACJ;AACA;AArD6B,eAsDhBC,SAtDgB;;AAuDzB;AACJ;AACA;AAzD6B,eA0DhBC,aA1DgB;AAAA,eA2DhBC,YA3DgB;;AACrB,cAAI1G,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAACkG,UAAP,KAAsB/F,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK8F,UAAL,GAAkBlG,MAAM,CAACkG,UAAzB;;AACA,cAAIlG,MAAM,CAACmG,eAAP,KAA2BhG,SAA/B,EAA0C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC/D,eAAK+F,eAAL,GAAuBnG,MAAM,CAACmG,eAA9B;;AACA,cAAInG,MAAM,CAACoG,QAAP,KAAoBjG,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKgG,QAAL,GAAgBpG,MAAM,CAACoG,QAAvB;;AACA,cAAIpG,MAAM,CAACqG,YAAP,KAAwBlG,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D,eAAKiG,YAAL,GAAoBrG,MAAM,CAACqG,YAA3B;;AACA,cAAIrG,MAAM,CAACsG,QAAP,KAAoBnG,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKkG,QAAL,GAAgBtG,MAAM,CAACsG,QAAvB;;AACA,cAAItG,MAAM,CAACuG,YAAP,KAAwBpG,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D,eAAKmG,YAAL,GAAoBvG,MAAM,CAACuG,YAA3B;;AACA,cAAIvG,MAAM,CAACwG,SAAP,KAAqBrG,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKoG,SAAL,GAAiBxG,MAAM,CAACwG,SAAxB;;AACA,cAAIxG,MAAM,CAACyG,aAAP,KAAyBtG,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAKqG,aAAL,GAAqBzG,MAAM,CAACyG,aAA5B;;AACA,cAAIzG,MAAM,CAAC0G,YAAP,KAAwBvG,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D;AAAE,iBAAKsG,YAAL,GAAoB,EAApB;;AAAwB,iBAAI,IAAItF,KAAR,IAAiBpB,MAAM,CAAC0G,YAAxB,EAAsC;AAAE,kBAAIrF,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAItG,YAAJ,CAAiBqG,KAAjB,CAAN;AAA+B,mBAAKsF,YAAL,CAAkBpF,IAAlB,CAAuBD,GAAvB;AAA6B;AAAC;AAC3I;;AAwCDhB,QAAAA,OAAO,CAACC,MAAD,EAAgB;AAUnB,eAAK,IAAIgE,EAAT,IAAe,KAAKoC,YAApB,EAAkC;AAAEpC,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAEjE,OAAJ,CAAYC,MAAZ;AAAsB;AAC7D;;AA1EmB,O;AAiFxB;AACA;AACA;;;8BACajF,Y,GAAN,MAAMA,YAAN,CAAmB;AAEtB0E,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAOhB2G,QAPgB;AAAA,eAQhBC,MARgB;;AACrB,cAAI5G,MAAM,CAAC6G,SAAP,KAAqB1G,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKuG,QAAL,GAAgB3G,MAAM,CAAC6G,SAAvB;;AACA,cAAI7G,MAAM,CAAC4G,MAAP,KAAkBzG,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD;AAAE,iBAAKwG,MAAL,GAAc,EAAd;;AAAkB,iBAAI,IAAIxF,KAAR,IAAiBpB,MAAM,CAAC4G,MAAxB,EAAgC;AAAE,kBAAIvF,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKwF,MAAL,CAAYtF,IAAZ,CAAiBD,GAAjB;AAAuB;AAAC;AACvG;;AAKDhB,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfqB,O;;2BAsBbhF,S,GAAN,MAAMA,SAAN,CAAgB;AAEnByE,QAAAA,WAAW,CAACC,MAAD,EAAc;AAiBzB;AACJ;AACA;AAnB6B,eAoBhBiB,EApBgB;;AAqBzB;AACJ;AACA;AAvB6B,eAwBhBQ,IAxBgB;;AAyBzB;AACJ;AACA;AA3B6B,eA4BhBqF,WA5BgB;;AA6BzB;AACJ;AACA;AA/B6B,eAgChBC,IAhCgB;;AAiCzB;AACJ;AACA;AAnC6B,eAoChBC,UApCgB;;AAqCzB;AACJ;AACA;AAvC6B,eAwChBC,WAxCgB;;AAyCzB;AACJ;AACA;AA3C6B,eA4ChBC,YA5CgB;;AACrB,cAAIlH,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAACyB,IAAP,KAAgBtB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKqB,IAAL,GAAYzB,MAAM,CAACyB,IAAnB;;AACA,cAAIzB,MAAM,CAAC8G,WAAP,KAAuB3G,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAK0G,WAAL,GAAmB9G,MAAM,CAAC8G,WAA1B;;AACA,cAAI9G,MAAM,CAAC+G,IAAP,KAAgB5G,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAK2G,IAAL,GAAY/G,MAAM,CAAC+G,IAAnB;;AACA,cAAI/G,MAAM,CAACmH,WAAP,KAAuBhH,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAK4G,UAAL,GAAkBhH,MAAM,CAACmH,WAAzB;;AACA,cAAInH,MAAM,CAACoH,YAAP,KAAwBjH,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D,eAAK6G,WAAL,GAAmBjH,MAAM,CAACoH,YAA1B;;AACA,cAAIpH,MAAM,CAACqH,aAAP,KAAyBlH,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAK8G,YAAL,GAAoBlH,MAAM,CAACqH,aAA3B;AACH;;AA+BDhH,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAQtB;;AAxDkB,O;;4BA+DV/E,U,GAAN,MAAMA,UAAN,CAAiB;AAEpBwE,QAAAA,WAAW,CAACC,MAAD,EAAc;AAyBzB;AACJ;AACA;AA3B6B,eA4BhBiB,EA5BgB;;AA6BzB;AACJ;AACA;AA/B6B,eAgChBQ,IAhCgB;;AAiCzB;AACJ;AACA;AAnC6B,eAoChB6F,MApCgB;;AAqCzB;AACJ;AACA;AAvC6B,eAwChBnG,IAxCgB;;AAyCzB;AACJ;AACA;AA3C6B,eA4ChBoG,UA5CgB;;AA6CzB;AACJ;AACA;AA/C6B,eAgDhBC,MAhDgB;;AAiDzB;AACJ;AACA;AAnD6B,eAoDhBC,iBApDgB;;AAqDzB;AACJ;AACA;AAvD6B,eAwDhBC,gBAxDgB;;AAyDzB;AACJ;AACA;AA3D6B,eA4DhBC,mBA5DgB;;AA6DzB;AACJ;AACA;AA/D6B,eAgEhBC,cAhEgB;;AAiEzB;AACJ;AACA;AAnE6B,eAoEhBC,mBApEgB;;AACrB,cAAI7H,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAACyB,IAAP,KAAgBtB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKqB,IAAL,GAAYzB,MAAM,CAACyB,IAAnB;;AACA,cAAIzB,MAAM,CAACsH,MAAP,KAAkBnH,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKkH,MAAL,GAActH,MAAM,CAACsH,MAArB;;AACA,cAAItH,MAAM,CAACmB,IAAP,KAAgBhB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKe,IAAL,GAAYnB,MAAM,CAACmB,IAAnB;;AACA,cAAInB,MAAM,CAAC8H,WAAP,KAAuB3H,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKmH,UAAL,GAAkBvH,MAAM,CAAC8H,WAAzB;;AACA,cAAI9H,MAAM,CAACwH,MAAP,KAAkBrH,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKoH,MAAL,GAAcxH,MAAM,CAACwH,MAArB;;AACA,cAAIxH,MAAM,CAAC+H,kBAAP,KAA8B5H,SAAlC,EAA6C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClE,eAAKqH,iBAAL,GAAyBzH,MAAM,CAAC+H,kBAAhC;;AACA,cAAI/H,MAAM,CAACgI,iBAAP,KAA6B7H,SAAjC,EAA4C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjE,eAAKsH,gBAAL,GAAwB1H,MAAM,CAACgI,iBAA/B;;AACA,cAAIhI,MAAM,CAACiI,oBAAP,KAAgC9H,SAApC,EAA+C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpE,eAAKuH,mBAAL,GAA2B3H,MAAM,CAACiI,oBAAlC;;AACA,cAAIjI,MAAM,CAACkI,gBAAP,KAA4B/H,SAAhC,EAA2C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAChE,eAAKwH,cAAL,GAAsB5H,MAAM,CAACkI,gBAA7B;;AACA,cAAIlI,MAAM,CAACmI,qBAAP,KAAiChI,SAArC,EAAgD;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrE,eAAKyH,mBAAL,GAA2B7H,MAAM,CAACmI,qBAAlC;AACH;;AA+CD9H,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAYtB;;AApFmB,O;;0BA2FX9E,Q,GAAN,MAAMA,QAAN,CAAe;AAElBuE,QAAAA,WAAW,CAACC,MAAD,EAAc;AAyDzB;AACJ;AACA;AA3D6B,eA4DhBiB,EA5DgB;;AA6DzB;AACJ;AACA;AA/D6B,eAgEhBQ,IAhEgB;;AAiEzB;AACJ;AACA;AAnE6B,eAoEhB2G,OApEgB;;AAqEzB;AACJ;AACA;AAvE6B,eAwEhBZ,MAxEgB;;AAyEzB;AACJ;AACA;AA3E6B,eA4EhBa,IA5EgB;;AA6EzB;AACJ;AACA;AA/E6B,eAgFhBC,OAhFgB;;AAiFzB;AACJ;AACA;AAnF6B,eAoFhBC,MApFgB;;AAqFzB;AACJ;AACA;AAvF6B,eAwFhBC,OAxFgB;;AAyFzB;AACJ;AACA;AA3F6B,eA4FhBC,SA5FgB;;AA6FzB;AACJ;AACA;AA/F6B,eAgGhBC,SAhGgB;;AAiGzB;AACJ;AACA;AAnG6B,eAoGhBC,WApGgB;;AAqGzB;AACJ;AACA;AAvG6B,eAwGhBC,SAxGgB;;AAyGzB;AACJ;AACA;AA3G6B,eA4GhBC,SA5GgB;;AA6GzB;AACJ;AACA;AA/G6B,eAgHhBC,WAhHgB;;AAiHzB;AACJ;AACA;AAnH6B,eAoHhBC,cApHgB;;AAqHzB;AACJ;AACA;AAvH6B,eAwHhBC,gBAxHgB;;AAyHzB;AACJ;AACA;AA3H6B,eA4HhBC,cA5HgB;;AA6HzB;AACJ;AACA;AA/H6B,eAgIhBC,kBAhIgB;;AAiIzB;AACJ;AACA;AAnI6B,eAoIhBC,mBApIgB;;AAqIzB;AACJ;AACA;AAvI6B,eAwIhBC,YAxIgB;;AAyIzB;AACJ;AACA;AA3I6B,eA4IhBC,aA5IgB;;AA6IzB;AACJ;AACA;AA/I6B,eAgJhBC,UAhJgB;;AAiJzB;AACJ;AACA;AAnJ6B,eAoJhBC,iBApJgB;;AAqJzB;AACJ;AACA;AAvJ6B,eAwJhBC,UAxJgB;;AAyJzB;AACJ;AACA;AA3J6B,eA4JhBC,YA5JgB;;AA6JzB;AACJ;AACA;AA/J6B,eAgKhBC,aAhKgB;;AAiKzB;AACJ;AACA;AAnK6B,eAoKhBC,UApKgB;;AACrB,cAAI3J,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAACyB,IAAP,KAAgBtB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKqB,IAAL,GAAYzB,MAAM,CAACyB,IAAnB;;AACA,cAAIzB,MAAM,CAACoI,OAAP,KAAmBjI,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKgI,OAAL,GAAepI,MAAM,CAACoI,OAAtB;;AACA,cAAIpI,MAAM,CAACwH,MAAP,KAAkBrH,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKoH,MAAL,GAAcxH,MAAM,CAACwH,MAArB;;AACA,cAAIxH,MAAM,CAACqI,IAAP,KAAgBlI,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKiI,IAAL,GAAYrI,MAAM,CAACqI,IAAnB;;AACA,cAAIrI,MAAM,CAACsI,OAAP,KAAmBnI,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKkI,OAAL,GAAetI,MAAM,CAACsI,OAAtB;;AACA,cAAItI,MAAM,CAAC4J,OAAP,KAAmBzJ,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKmI,MAAL,GAAcvI,MAAM,CAAC4J,OAArB;;AACA,cAAI5J,MAAM,CAAC6J,QAAP,KAAoB1J,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKoI,OAAL,GAAexI,MAAM,CAAC6J,QAAtB;;AACA,cAAI7J,MAAM,CAAC8J,UAAP,KAAsB3J,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKqI,SAAL,GAAiBzI,MAAM,CAAC8J,UAAxB;;AACA,cAAI9J,MAAM,CAAC+J,UAAP,KAAsB5J,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKsI,SAAL,GAAiB1I,MAAM,CAAC+J,UAAxB;;AACA,cAAI/J,MAAM,CAAC2I,WAAP,KAAuBxI,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKuI,WAAL,GAAmB3I,MAAM,CAAC2I,WAA1B;;AACA,cAAI3I,MAAM,CAACgK,UAAP,KAAsB7J,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKwI,SAAL,GAAiB5I,MAAM,CAACgK,UAAxB;;AACA,cAAIhK,MAAM,CAACiK,WAAP,KAAuB9J,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKyI,SAAL,GAAiB7I,MAAM,CAACiK,WAAxB;;AACA,cAAIjK,MAAM,CAACkK,aAAP,KAAyB/J,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAK0I,WAAL,GAAmB9I,MAAM,CAACkK,aAA1B;;AACA,cAAIlK,MAAM,CAACmK,iBAAP,KAA6BhK,SAAjC,EAA4C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjE,eAAK2I,cAAL,GAAsB/I,MAAM,CAACmK,iBAA7B;;AACA,cAAInK,MAAM,CAACoK,kBAAP,KAA8BjK,SAAlC,EAA6C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClE,eAAK4I,gBAAL,GAAwBhJ,MAAM,CAACoK,kBAA/B;;AACA,cAAIpK,MAAM,CAACqK,eAAP,KAA2BlK,SAA/B,EAA0C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC/D,eAAK6I,cAAL,GAAsBjJ,MAAM,CAACqK,eAA7B;;AACA,cAAIrK,MAAM,CAACsK,oBAAP,KAAgCnK,SAApC,EAA+C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpE,eAAK8I,kBAAL,GAA0BlJ,MAAM,CAACsK,oBAAjC;;AACA,cAAItK,MAAM,CAACuK,qBAAP,KAAiCpK,SAArC,EAAgD;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrE,eAAK+I,mBAAL,GAA2BnJ,MAAM,CAACuK,qBAAlC;;AACA,cAAIvK,MAAM,CAACwK,aAAP,KAAyBrK,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAKgJ,YAAL,GAAoBpJ,MAAM,CAACwK,aAA3B;;AACA,cAAIxK,MAAM,CAACyK,cAAP,KAA0BtK,SAA9B,EAAyC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC9D,eAAKiJ,aAAL,GAAqBrJ,MAAM,CAACyK,cAA5B;;AACA,cAAIzK,MAAM,CAAC0K,WAAP,KAAuBvK,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKkJ,UAAL,GAAkBtJ,MAAM,CAAC0K,WAAzB;;AACA,cAAI1K,MAAM,CAAC2K,mBAAP,KAA+BxK,SAAnC,EAA8C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnE,eAAKmJ,iBAAL,GAAyBvJ,MAAM,CAAC2K,mBAAhC;;AACA,cAAI3K,MAAM,CAACwJ,UAAP,KAAsBrJ,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKoJ,UAAL,GAAkBxJ,MAAM,CAACwJ,UAAzB;;AACA,cAAIxJ,MAAM,CAAC4K,aAAP,KAAyBzK,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAKqJ,YAAL,GAAoBzJ,MAAM,CAAC4K,aAA3B;;AACA,cAAI5K,MAAM,CAAC6K,cAAP,KAA0B1K,SAA9B,EAAyC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC9D,eAAKsJ,aAAL,GAAqB1J,MAAM,CAAC6K,cAA5B;;AACA,cAAI7K,MAAM,CAAC8K,WAAP,KAAuB3K,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKuJ,UAAL,GAAkB3J,MAAM,CAAC8K,WAAzB;AACH;;AA+GDzK,QAAAA,OAAO,CAACC,MAAD,EAAgB,CA4BtB;;AApMiB,O;;0BA2MT7E,Q,GAAN,MAAMA,QAAN,CAAe;AAElBsE,QAAAA,WAAW,CAACC,MAAD,EAAc;AAmBzB;AACJ;AACA;AArB6B,eAsBhBiB,EAtBgB;;AAuBzB;AACJ;AACA;AAzB6B,eA0BhBQ,IA1BgB;;AA2BzB;AACJ;AACA;AA7B6B,eA8BhBsF,IA9BgB;;AA+BzB;AACJ;AACA;AAjC6B,eAkChBgE,OAlCgB;;AAmCzB;AACJ;AACA;AArC6B,eAsChBC,UAtCgB;;AAuCzB;AACJ;AACA;AAzC6B,eA0ChBC,UA1CgB;;AA2CzB;AACJ;AACA;AA7C6B,eA8ChBC,KA9CgB;;AA+CzB;AACJ;AACA;AAjD6B,eAkDhBC,YAlDgB;;AACrB,cAAInL,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAACyB,IAAP,KAAgBtB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKqB,IAAL,GAAYzB,MAAM,CAACyB,IAAnB;;AACA,cAAIzB,MAAM,CAAC+G,IAAP,KAAgB5G,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAK2G,IAAL,GAAY/G,MAAM,CAAC+G,IAAnB;;AACA,cAAI/G,MAAM,CAAC+K,OAAP,KAAmB5K,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAK2K,OAAL,GAAe/K,MAAM,CAAC+K,OAAtB;;AACA,cAAI/K,MAAM,CAACoL,WAAP,KAAuBjL,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAK4K,UAAL,GAAkBhL,MAAM,CAACoL,WAAzB;;AACA,cAAIpL,MAAM,CAACqL,WAAP,KAAuBlL,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAK6K,UAAL,GAAkBjL,MAAM,CAACqL,WAAzB;;AACA,cAAIrL,MAAM,CAACkL,KAAP,KAAiB/K,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD;AAAE,iBAAK8K,KAAL,GAAa,EAAb;;AAAiB,iBAAI,IAAI9J,KAAR,IAAiBpB,MAAM,CAACkL,KAAxB,EAA+B;AAAE,kBAAI7J,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAIvG,iBAAJ,CAAsBsG,KAAtB,CAAN;AAAoC,mBAAK8J,KAAL,CAAW5J,IAAX,CAAgBD,GAAhB;AAAsB;AAAC;;AACxH,cAAIrB,MAAM,CAACsL,aAAP,KAAyBnL,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D;AAAE,iBAAK+K,YAAL,GAAoB,EAApB;;AAAwB,iBAAI,IAAI/J,IAAR,IAAiBpB,MAAM,CAACsL,aAAxB,EAAuC;AAAE,kBAAIjK,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAI9G,WAAJ,CAAgB6G,IAAhB,CAAN;AAA8B,mBAAK+J,YAAL,CAAkB7J,IAAlB,CAAuBD,GAAvB;AAA6B;AAAC;AAC3I;;AAmCDhB,QAAAA,OAAO,CAACC,MAAD,EAAgB;AAOnB,eAAK,IAAIgE,EAAT,IAAe,KAAK4G,KAApB,EAA2B;AAAE5G,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAEjE,OAAJ,CAAYC,MAAZ;AAAsB;;AACnD,eAAK,IAAIgE,GAAT,IAAe,KAAK6G,YAApB,EAAkC;AAAE7G,YAAAA,GAAE,QAAF,IAAAA,GAAE,CAAEjE,OAAJ,CAAYC,MAAZ;AAAsB;AAC7D;;AA/DiB,O;;iCAsET5E,e,GAAN,MAAMA,eAAN,CAAsB;AAEzBqE,QAAAA,WAAW,CAACC,MAAD,EAAc;AAezB;AACJ;AACA;AAjB6B,eAkBhBiL,UAlBgB;;AAmBzB;AACJ;AACA;AArB6B,eAsBhBM,SAtBgB;;AAuBzB;AACJ;AACA;AAzB6B,eA0BhBC,OA1BgB;;AA2BzB;AACJ;AACA;AA7B6B,eA8BhBC,OA9BgB;;AA+BzB;AACJ;AACA;AAjC6B,eAkChBC,YAlCgB;;AAmCzB;AACJ;AACA;AArC6B,eAsChBP,YAtCgB;;AACrB,cAAInL,MAAM,CAACqL,WAAP,KAAuBlL,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAK6K,UAAL,GAAkBjL,MAAM,CAACqL,WAAzB;;AACA,cAAIrL,MAAM,CAAC2L,UAAP,KAAsBxL,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKmL,SAAL,GAAiBvL,MAAM,CAAC2L,UAAxB;;AACA,cAAI3L,MAAM,CAAC4L,QAAP,KAAoBzL,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKoL,OAAL,GAAexL,MAAM,CAAC4L,QAAtB;;AACA,cAAI5L,MAAM,CAAC6L,QAAP,KAAoB1L,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD;AAAE,iBAAKqL,OAAL,GAAe,EAAf;;AAAmB,iBAAI,IAAIrK,KAAR,IAAiBpB,MAAM,CAAC6L,QAAxB,EAAkC;AAAE,kBAAIxK,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAIvG,iBAAJ,CAAsBsG,KAAtB,CAAN;AAAoC,mBAAKqK,OAAL,CAAanK,IAAb,CAAkBD,GAAlB;AAAwB;AAAC;;AAC/H,cAAIrB,MAAM,CAAC8L,aAAP,KAAyB3L,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAKsL,YAAL,GAAoB,IAAIlR,YAAJ,CAAiBwF,MAAM,CAAC8L,aAAxB,CAApB;;AACA,cAAI9L,MAAM,CAACsL,aAAP,KAAyBnL,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D;AAAE,iBAAK+K,YAAL,GAAoB,EAApB;;AAAwB,iBAAI,IAAI/J,KAAR,IAAiBpB,MAAM,CAACsL,aAAxB,EAAuC;AAAE,kBAAIjK,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAI9G,WAAJ,CAAgB6G,KAAhB,CAAN;AAA8B,mBAAK+J,YAAL,CAAkB7J,IAAlB,CAAuBD,GAAvB;AAA6B;AAAC;AAC3I;;AA2BDhB,QAAAA,OAAO,CAACC,MAAD,EAAgB;AAAA;;AAInB,eAAK,IAAIgE,EAAT,IAAe,KAAKmH,OAApB,EAA6B;AAAEnH,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAEjE,OAAJ,CAAYC,MAAZ;AAAsB;;AACrD,qCAAKoL,YAAL,gCAAmBrL,OAAnB,CAA2BC,MAA3B;;AACA,eAAK,IAAIgE,GAAT,IAAe,KAAK6G,YAApB,EAAkC;AAAE7G,YAAAA,GAAE,QAAF,IAAAA,GAAE,CAAEjE,OAAJ,CAAYC,MAAZ;AAAsB;AAC7D;;AAjDwB,O;;6BAwDhB3E,W,GAAN,MAAMA,WAAN,CAAkB;AAErBoE,QAAAA,WAAW,CAACC,MAAD,EAAc;AA+CzB;AACJ;AACA;AAjD6B,eAkDhB+L,MAlDgB;;AAmDzB;AACJ;AACA;AArD6B,eAsDhBC,QAtDgB;;AAuDzB;AACJ;AACA;AAzD6B,eA0DhBC,SA1DgB;;AA2DzB;AACJ;AACA;AA7D6B,eA8DhBC,KA9DgB;;AA+DzB;AACJ;AACA;AAjE6B,eAkEhBC,UAlEgB;;AAmEzB;AACJ;AACA;AArE6B,eAsEhBrF,WAtEgB;AAAA,eAuEhBsF,OAvEgB;;AAwEzB;AACJ;AACA;AA1E6B,eA2EhB5I,UA3EgB;;AA4EzB;AACJ;AACA;AA9E6B,eA+EhB6I,KA/EgB;;AAgFzB;AACJ;AACA;AAlF6B,eAmFhBC,UAnFgB;;AAoFzB;AACJ;AACA;AAtF6B,eAuFhBC,OAvFgB;;AAwFzB;AACJ;AACA;AA1F6B,eA2FhBC,UA3FgB;;AA4FzB;AACJ;AACA;AA9F6B,eA+FhBC,UA/FgB;;AAgGzB;AACJ;AACA;AAlG6B,eAmGhBC,YAnGgB;;AAoGzB;AACJ;AACA;AAtG6B,eAuGhBC,WAvGgB;;AAwGzB;AACJ;AACA;AA1G6B,eA2GhBC,KA3GgB;;AA4GzB;AACJ;AACA;AA9G6B,eA+GhBC,OA/GgB;;AAgHzB;AACJ;AACA;AAlH6B,eAmHhBC,UAnHgB;;AAoHzB;AACJ;AACA;AAtH6B,eAuHhBC,UAvHgB;;AAwHzB;AACJ;AACA;AA1H6B,eA2HhBC,UA3HgB;;AA4HzB;AACJ;AACA;AA9H6B,eA+HhBC,QA/HgB;AAAA,eAgIhBC,UAhIgB;;AACrB,cAAIlN,MAAM,CAAC+L,MAAP,KAAkB5L,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAK2L,MAAL,GAAc/L,MAAM,CAAC+L,MAArB;;AACA,cAAI/L,MAAM,CAACgM,QAAP,KAAoB7L,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAK4L,QAAL,GAAgBhM,MAAM,CAACgM,QAAvB;;AACA,cAAIhM,MAAM,CAACiM,SAAP,KAAqB9L,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAK6L,SAAL,GAAiBjM,MAAM,CAACiM,SAAxB;;AACA,cAAIjM,MAAM,CAACkM,KAAP,KAAiB/L,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAK8L,KAAL,GAAalM,MAAM,CAACkM,KAApB;;AACA,cAAIlM,MAAM,CAACmM,UAAP,KAAsBhM,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK+L,UAAL,GAAkBnM,MAAM,CAACmM,UAAzB;;AACA,cAAInM,MAAM,CAAC8G,WAAP,KAAuB3G,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAK0G,WAAL,GAAmB9G,MAAM,CAAC8G,WAA1B;;AACA,cAAI9G,MAAM,CAACoM,OAAP,KAAmBjM,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD;AAAE,iBAAKgM,OAAL,GAAe,EAAf;;AAAmB,iBAAI,IAAIhL,KAAR,IAAiBpB,MAAM,CAACoM,OAAxB,EAAiC;AAAE,kBAAI/K,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAIhG,YAAJ,CAAiB+F,KAAjB,CAAN;AAA+B,mBAAKgL,OAAL,CAAa9K,IAAb,CAAkBD,GAAlB;AAAwB;AAAC;;AACzH,cAAIrB,MAAM,CAACwD,UAAP,KAAsBrD,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKoD,UAAL,GAAkBxD,MAAM,CAACwD,UAAzB;;AACA,cAAIxD,MAAM,CAACqM,KAAP,KAAiBlM,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKiM,KAAL,GAAarM,MAAM,CAACqM,KAApB;;AACA,cAAIrM,MAAM,CAACsM,UAAP,KAAsBnM,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKkM,UAAL,GAAkBtM,MAAM,CAACsM,UAAzB;;AACA,cAAItM,MAAM,CAACuM,OAAP,KAAmBpM,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKmM,OAAL,GAAevM,MAAM,CAACuM,OAAtB;;AACA,cAAIvM,MAAM,CAACwM,UAAP,KAAsBrM,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKoM,UAAL,GAAkBxM,MAAM,CAACwM,UAAzB;;AACA,cAAIxM,MAAM,CAACyM,UAAP,KAAsBtM,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKqM,UAAL,GAAkBzM,MAAM,CAACyM,UAAzB;;AACA,cAAIzM,MAAM,CAAC0M,YAAP,KAAwBvM,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D,eAAKsM,YAAL,GAAoB1M,MAAM,CAAC0M,YAA3B;;AACA,cAAI1M,MAAM,CAAC2M,WAAP,KAAuBxM,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKuM,WAAL,GAAmB3M,MAAM,CAAC2M,WAA1B;;AACA,cAAI3M,MAAM,CAAC4M,KAAP,KAAiBzM,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKwM,KAAL,GAAa5M,MAAM,CAAC4M,KAApB;;AACA,cAAI5M,MAAM,CAAC6M,OAAP,KAAmB1M,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKyM,OAAL,GAAe7M,MAAM,CAAC6M,OAAtB;;AACA,cAAI7M,MAAM,CAAC8M,UAAP,KAAsB3M,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK0M,UAAL,GAAkB9M,MAAM,CAAC8M,UAAzB;;AACA,cAAI9M,MAAM,CAAC+M,UAAP,KAAsB5M,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK2M,UAAL,GAAkB/M,MAAM,CAAC+M,UAAzB;;AACA,cAAI/M,MAAM,CAACgN,UAAP,KAAsB7M,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK4M,UAAL,GAAkBhN,MAAM,CAACgN,UAAzB;;AACA,cAAIhN,MAAM,CAACiN,QAAP,KAAoB9M,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAK6M,QAAL,GAAgBjN,MAAM,CAACiN,QAAvB;;AACA,cAAIjN,MAAM,CAACkN,UAAP,KAAsB/M,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D;AAAE,iBAAK8M,UAAL,GAAkB,EAAlB;;AAAsB,iBAAI,IAAI9L,KAAR,IAAiBpB,MAAM,CAACkN,UAAxB,EAAoC;AAAE,kBAAI7L,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAIrG,WAAJ,CAAgBoG,KAAhB,CAAN;AAA8B,mBAAK8L,UAAL,CAAgB5L,IAAhB,CAAqBD,GAArB;AAA2B;AAAC;AACpI;;AAqFDhB,QAAAA,OAAO,CAACC,MAAD,EAAgB;AAOnB,eAAK,IAAIgE,EAAT,IAAe,KAAK8H,OAApB,EAA6B;AAAE9H,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAEjE,OAAJ,CAAYC,MAAZ;AAAsB;AAgBxD;;AA3JoB,O;;+BAkKZ1E,a,GAAN,MAAMA,aAAN,CAAoB;AAEvBmE,QAAAA,WAAW,CAACC,MAAD,EAAc;AAyBzB;AACJ;AACA;AA3B6B,eA4BhBmN,aA5BgB;;AA6BzB;AACJ;AACA;AA/B6B,eAgChBvK,SAhCgB;;AAiCzB;AACJ;AACA;AAnC6B,eAoChBwK,qBApCgB;;AAqCzB;AACJ;AACA;AAvC6B,eAwChBC,kBAxCgB;;AAyCzB;AACJ;AACA;AA3C6B,eA4ChBC,gBA5CgB;;AA6CzB;AACJ;AACA;AA/C6B,eAgDhBC,iBAhDgB;;AAiDzB;AACJ;AACA;AAnD6B,eAoDhBC,6BApDgB;;AAqDzB;AACJ;AACA;AAvD6B,eAwDhBC,0BAxDgB;;AAyDzB;AACJ;AACA;AA3D6B,eA4DhBC,2BA5DgB;;AA6DzB;AACJ;AACA;AA/D6B,eAgEhBC,yBAhEgB;;AAiEzB;AACJ;AACA;AAnE6B,eAoEhBC,eApEgB;;AACrB,cAAI5N,MAAM,CAACmN,aAAP,KAAyBhN,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAK+M,aAAL,GAAqBnN,MAAM,CAACmN,aAA5B;;AACA,cAAInN,MAAM,CAAC4C,SAAP,KAAqBzC,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKwC,SAAL,GAAiB5C,MAAM,CAAC4C,SAAxB;;AACA,cAAI5C,MAAM,CAACoN,qBAAP,KAAiCjN,SAArC,EAAgD;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrE,eAAKgN,qBAAL,GAA6BpN,MAAM,CAACoN,qBAApC;;AACA,cAAIpN,MAAM,CAACqN,kBAAP,KAA8BlN,SAAlC,EAA6C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClE,eAAKiN,kBAAL,GAA0BrN,MAAM,CAACqN,kBAAjC;;AACA,cAAIrN,MAAM,CAACsN,gBAAP,KAA4BnN,SAAhC,EAA2C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAChE,eAAKkN,gBAAL,GAAwBtN,MAAM,CAACsN,gBAA/B;;AACA,cAAItN,MAAM,CAACuN,iBAAP,KAA6BpN,SAAjC,EAA4C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjE,eAAKmN,iBAAL,GAAyBvN,MAAM,CAACuN,iBAAhC;;AACA,cAAIvN,MAAM,CAACwN,6BAAP,KAAyCrN,SAA7C,EAAwD;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7E,eAAKoN,6BAAL,GAAqCxN,MAAM,CAACwN,6BAA5C;;AACA,cAAIxN,MAAM,CAACyN,0BAAP,KAAsCtN,SAA1C,EAAqD;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1E,eAAKqN,0BAAL,GAAkCzN,MAAM,CAACyN,0BAAzC;;AACA,cAAIzN,MAAM,CAAC0N,2BAAP,KAAuCvN,SAA3C,EAAsD;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3E,eAAKsN,2BAAL,GAAmC1N,MAAM,CAAC0N,2BAA1C;;AACA,cAAI1N,MAAM,CAAC2N,yBAAP,KAAqCxN,SAAzC,EAAoD;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzE,eAAKuN,yBAAL,GAAiC3N,MAAM,CAAC2N,yBAAxC;;AACA,cAAI3N,MAAM,CAAC4N,eAAP,KAA2BzN,SAA/B,EAA0C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC/D,eAAKwN,eAAL,GAAuB5N,MAAM,CAAC4N,eAA9B;AACH;;AA+CDvN,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAYtB;;AApFsB,O;;yBA2FdzE,O,GAAN,MAAMA,OAAN,CAAc;AAEjBkE,QAAAA,WAAW,CAACC,MAAD,EAAc;AAuBzB;AACJ;AACA;AAzB6B,eA0BhBiB,EA1BgB;;AA2BzB;AACJ;AACA;AA7B6B,eA8BhBQ,IA9BgB;;AA+BzB;AACJ;AACA;AAjC6B,eAkChBsF,IAlCgB;;AAmCzB;AACJ;AACA;AArC6B,eAsChBgE,OAtCgB;;AAuCzB;AACJ;AACA;AAzC6B,eA0ChBC,UA1CgB;;AA2CzB;AACJ;AACA;AA7C6B,eA8ChB6C,OA9CgB;;AA+CzB;AACJ;AACA;AAjD6B,eAkDhBjM,QAlDgB;;AAmDzB;AACJ;AACA;AArD6B,eAsDhBkM,YAtDgB;;AAuDzB;AACJ;AACA;AAzD6B,eA0DhBC,YA1DgB;;AA2DzB;AACJ;AACA;AA7D6B,eA8DhBC,WA9DgB;;AACrB,cAAIhO,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAACyB,IAAP,KAAgBtB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKqB,IAAL,GAAYzB,MAAM,CAACyB,IAAnB;;AACA,cAAIzB,MAAM,CAAC+G,IAAP,KAAgB5G,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAK2G,IAAL,GAAY/G,MAAM,CAAC+G,IAAnB;;AACA,cAAI/G,MAAM,CAAC+K,OAAP,KAAmB5K,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAK2K,OAAL,GAAe/K,MAAM,CAAC+K,OAAtB;;AACA,cAAI/K,MAAM,CAACoL,WAAP,KAAuBjL,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAK4K,UAAL,GAAkBhL,MAAM,CAACoL,WAAzB;;AACA,cAAIpL,MAAM,CAACiO,QAAP,KAAoB9N,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKyN,OAAL,GAAe7N,MAAM,CAACiO,QAAtB;;AACA,cAAIjO,MAAM,CAAC6B,SAAP,KAAqB1B,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKwB,QAAL,GAAgB5B,MAAM,CAAC6B,SAAvB;;AACA,cAAI7B,MAAM,CAACkO,aAAP,KAAyB/N,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAK0N,YAAL,GAAoB9N,MAAM,CAACkO,aAA3B;;AACA,cAAIlO,MAAM,CAACmO,aAAP,KAAyBhO,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAK2N,YAAL,GAAoB/N,MAAM,CAACmO,aAA3B;;AACA,cAAInO,MAAM,CAACoO,aAAP,KAAyBjO,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAK4N,WAAL,GAAmBhO,MAAM,CAACoO,aAA1B;AACH;;AA2CD/N,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAWtB;;AA7EgB,O;;0BAoFRxE,Q,GAAN,MAAMA,QAAN,CAAe;AAElBiE,QAAAA,WAAW,CAACC,MAAD,EAAc;AAiBzB;AACJ;AACA;AAnB6B,eAoBhBiB,EApBgB;;AAqBzB;AACJ;AACA;AAvB6B,eAwBhBuG,MAxBgB;;AAyBzB;AACJ;AACA;AA3B6B,eA4BhB6G,UA5BgB;;AA6BzB;AACJ;AACA;AA/B6B,eAgChBC,WAhCgB;;AAiCzB;AACJ;AACA;AAnC6B,eAoChBC,cApCgB;;AAqCzB;AACJ;AACA;AAvC6B,eAwChBC,qBAxCgB;;AAyCzB;AACJ;AACA;AA3C6B,eA4ChBC,SA5CgB;;AACrB,cAAIzO,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAACwH,MAAP,KAAkBrH,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKoH,MAAL,GAAcxH,MAAM,CAACwH,MAArB;;AACA,cAAIxH,MAAM,CAACqO,UAAP,KAAsBlO,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKiO,UAAL,GAAkBrO,MAAM,CAACqO,UAAzB;;AACA,cAAIrO,MAAM,CAACsO,WAAP,KAAuBnO,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKkO,WAAL,GAAmBtO,MAAM,CAACsO,WAA1B;;AACA,cAAItO,MAAM,CAACuO,cAAP,KAA0BpO,SAA9B,EAAyC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC9D,eAAKmO,cAAL,GAAsBvO,MAAM,CAACuO,cAA7B;;AACA,cAAIvO,MAAM,CAACwO,qBAAP,KAAiCrO,SAArC,EAAgD;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrE,eAAKoO,qBAAL,GAA6BxO,MAAM,CAACwO,qBAApC;;AACA,cAAIxO,MAAM,CAACyO,SAAP,KAAqBtO,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKqO,SAAL,GAAiBzO,MAAM,CAACyO,SAAxB;AACH;;AA+BDpO,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAQtB;;AAxDiB,O;;+BA+DTvE,a,GAAN,MAAMA,aAAN,CAAoB;AAEvBgE,QAAAA,WAAW,CAACC,MAAD,EAAc;AAiBzB;AACJ;AACA;AAnB6B,eAoBhBiB,EApBgB;;AAqBzB;AACJ;AACA;AAvB6B,eAwBhByN,cAxBgB;;AAyBzB;AACJ;AACA;AA3B6B,eA4BhBC,WA5BgB;AAAA,eA6BhBC,UA7BgB;;AA8BzB;AACJ;AACA;AAhC6B,eAiChBC,cAjCgB;;AAkCzB;AACJ;AACA;AApC6B,eAqChBC,WArCgB;AAAA,eAsChBC,UAtCgB;;AACrB,cAAI/O,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAAC0O,cAAP,KAA0BvO,SAA9B,EAAyC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC9D,eAAKsO,cAAL,GAAsB1O,MAAM,CAAC0O,cAA7B;;AACA,cAAI1O,MAAM,CAAC2O,WAAP,KAAuBxO,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKuO,WAAL,GAAmB3O,MAAM,CAAC2O,WAA1B;;AACA,cAAI3O,MAAM,CAAC4O,UAAP,KAAsBzO,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D;AAAE,iBAAKwO,UAAL,GAAkB,EAAlB;;AAAsB,iBAAI,IAAIxN,KAAR,IAAiBpB,MAAM,CAAC4O,UAAxB,EAAoC;AAAE,kBAAIvN,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAItG,YAAJ,CAAiBqG,KAAjB,CAAN;AAA+B,mBAAKwN,UAAL,CAAgBtN,IAAhB,CAAqBD,GAArB;AAA2B;AAAC;;AAClI,cAAIrB,MAAM,CAAC6O,cAAP,KAA0B1O,SAA9B,EAAyC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC9D,eAAKyO,cAAL,GAAsB7O,MAAM,CAAC6O,cAA7B;;AACA,cAAI7O,MAAM,CAAC8O,WAAP,KAAuB3O,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAK0O,WAAL,GAAmB9O,MAAM,CAAC8O,WAA1B;;AACA,cAAI9O,MAAM,CAAC+O,UAAP,KAAsB5O,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D;AAAE,iBAAK2O,UAAL,GAAkB,EAAlB;;AAAsB,iBAAI,IAAI3N,KAAR,IAAiBpB,MAAM,CAAC+O,UAAxB,EAAoC;AAAE,kBAAI1N,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAItG,YAAJ,CAAiBqG,KAAjB,CAAN;AAA+B,mBAAK2N,UAAL,CAAgBzN,IAAhB,CAAqBD,GAArB;AAA2B;AAAC;AACrI;;AAyBDhB,QAAAA,OAAO,CAACC,MAAD,EAAgB;AAInB,eAAK,IAAIgE,EAAT,IAAe,KAAKsK,UAApB,EAAgC;AAAEtK,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAEjE,OAAJ,CAAYC,MAAZ;AAAsB;;AAGxD,eAAK,IAAIgE,GAAT,IAAe,KAAKyK,UAApB,EAAgC;AAAEzK,YAAAA,GAAE,QAAF,IAAAA,GAAE,CAAEjE,OAAJ,CAAYC,MAAZ;AAAsB;AAC3D;;AAlDsB,O;;yBAyDdtE,O,GAAN,MAAMA,OAAN,CAAc;AAEjB+D,QAAAA,WAAW,CAACC,MAAD,EAAc;AAWzB;AACJ;AACA;AAb6B,eAchBgP,MAdgB;;AAezB;AACJ;AACA;AAjB6B,eAkBhBC,SAlBgB;;AAmBzB;AACJ;AACA;AArB6B,eAsBhBC,QAtBgB;AAAA,eAuBhBC,QAvBgB;;AACrB,cAAInP,MAAM,CAACoP,OAAP,KAAmBjP,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAK4O,MAAL,GAAchP,MAAM,CAACoP,OAArB;;AACA,cAAIpP,MAAM,CAACqP,UAAP,KAAsBlP,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK6O,SAAL,GAAiBjP,MAAM,CAACqP,UAAxB;;AACA,cAAIrP,MAAM,CAACsP,SAAP,KAAqBnP,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAK8O,QAAL,GAAgBlP,MAAM,CAACsP,SAAvB;;AACA,cAAItP,MAAM,CAACuP,SAAP,KAAqBpP,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD;AAAE,iBAAK+O,QAAL,GAAgB,EAAhB;;AAAoB,iBAAI,IAAI/N,KAAR,IAAiBpB,MAAM,CAACuP,SAAxB,EAAmC;AAAE,kBAAIlO,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAIpF,WAAJ,CAAgBmF,KAAhB,CAAN;AAA8B,mBAAK+N,QAAL,CAAc7N,IAAd,CAAmBD,GAAnB;AAAyB;AAAC;AAC/H;;AAgBDhB,QAAAA,OAAO,CAACC,MAAD,EAAgB;AAInB,eAAK,IAAIgE,EAAT,IAAe,KAAK6K,QAApB,EAA8B;AAAE7K,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAEjE,OAAJ,CAAYC,MAAZ;AAAsB;AACzD;;AAhCgB,O;AAuCrB;AACA;AACA;;;6BACarE,W,GAAN,MAAMA,WAAN,CAAkB;AAErB8D,QAAAA,WAAW,CAACC,MAAD,EAAc;AAWzB;AACJ;AACA;AAb6B,eAchBwP,MAdgB;AAAA,eAehBC,KAfgB;AAAA,eAgBhBC,IAhBgB;AAAA,eAiBhBC,YAjBgB;;AACrB,cAAI3P,MAAM,CAAC4P,OAAP,KAAmBzP,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKoP,MAAL,GAAcxP,MAAM,CAAC4P,OAArB;;AACA,cAAI5P,MAAM,CAACyP,KAAP,KAAiBtP,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKqP,KAAL,GAAazP,MAAM,CAACyP,KAApB;;AACA,cAAIzP,MAAM,CAAC0P,IAAP,KAAgBvP,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKsP,IAAL,GAAY1P,MAAM,CAAC0P,IAAnB;;AACA,cAAI1P,MAAM,CAAC6P,aAAP,KAAyB1P,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAKuP,YAAL,GAAoB3P,MAAM,CAAC6P,aAA3B;AACH;;AAUDxP,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAKtB;;AA1BoB,O;;+BAiCZpE,a,GAAN,MAAMA,aAAN,CAAoB;AAEvB6D,QAAAA,WAAW,CAACC,MAAD,EAAc;AAazB;AACJ;AACA;AAf6B,eAgBhB+L,MAhBgB;;AAiBzB;AACJ;AACA;AAnB6B,eAoBhB+D,WApBgB;;AAqBzB;AACJ;AACA;AAvB6B,eAwBhBC,eAxBgB;;AAyBzB;AACJ;AACA;AA3B6B,eA4BhBC,eA5BgB;;AA6BzB;AACJ;AACA;AA/B6B,eAgChBC,eAhCgB;;AACrB,cAAIjQ,MAAM,CAAC+L,MAAP,KAAkB5L,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAK2L,MAAL,GAAc/L,MAAM,CAAC+L,MAArB;;AACA,cAAI/L,MAAM,CAAC8P,WAAP,KAAuB3P,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D;AAAE,iBAAK0P,WAAL,GAAmB,EAAnB;;AAAuB,iBAAI,IAAI1O,KAAR,IAAiBpB,MAAM,CAAC8P,WAAxB,EAAqC;AAAE,kBAAIzO,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAK0O,WAAL,CAAiBxO,IAAjB,CAAsBD,GAAtB;AAA4B;AAAC;;AACnH,cAAIrB,MAAM,CAAC+P,eAAP,KAA2B5P,SAA/B,EAA0C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC/D,eAAK2P,eAAL,GAAuB/P,MAAM,CAAC+P,eAA9B;;AACA,cAAI/P,MAAM,CAACgQ,eAAP,KAA2B7P,SAA/B,EAA0C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC/D,eAAK4P,eAAL,GAAuBhQ,MAAM,CAACgQ,eAA9B;;AACA,cAAIhQ,MAAM,CAACiQ,eAAP,KAA2B9P,SAA/B,EAA0C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC/D,eAAK6P,eAAL,GAAuBjQ,MAAM,CAACiQ,eAA9B;AACH;;AAuBD5P,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAMtB;;AA1CsB,O;;0BAiDdnE,Q,GAAN,MAAMA,QAAN,CAAe;AAElB4D,QAAAA,WAAW,CAACC,MAAD,EAAc;AA6BzB;AACJ;AACA;AA/B6B,eAgChBiB,EAhCgB;;AAiCzB;AACJ;AACA;AAnC6B,eAoChBiP,SApCgB;;AAqCzB;AACJ;AACA;AAvC6B,eAwChBzO,IAxCgB;;AAyCzB;AACJ;AACA;AA3C6B,eA4ChBsF,IA5CgB;;AA6CzB;AACJ;AACA;AA/C6B,eAgDhBoJ,QAhDgB;;AAiDzB;AACJ;AACA;AAnD6B,eAoDhB3I,MApDgB;;AAqDzB;AACJ;AACA;AAvD6B,eAwDhBV,WAxDgB;;AAyDzB;AACJ;AACA;AA3D6B,eA4DhBiE,OA5DgB;;AA6DzB;AACJ;AACA;AA/D6B,eAgEhBtB,YAhEgB;;AAiEzB;AACJ;AACA;AAnE6B,eAoEhBC,aApEgB;AAAA,eAqEhB0G,QArEgB;;AAsEzB;AACJ;AACA;AAxE6B,eAyEhBnK,OAzEgB;;AA0EzB;AACJ;AACA;AA5E6B,eA6EhBoK,SA7EgB;;AACrB,cAAIrQ,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAACsQ,UAAP,KAAsBnQ,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK8P,SAAL,GAAiBlQ,MAAM,CAACsQ,UAAxB;;AACA,cAAItQ,MAAM,CAACyB,IAAP,KAAgBtB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKqB,IAAL,GAAYzB,MAAM,CAACyB,IAAnB;;AACA,cAAIzB,MAAM,CAAC+G,IAAP,KAAgB5G,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAK2G,IAAL,GAAY/G,MAAM,CAAC+G,IAAnB;;AACA,cAAI/G,MAAM,CAACmQ,QAAP,KAAoBhQ,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAK+P,QAAL,GAAgBnQ,MAAM,CAACmQ,QAAvB;;AACA,cAAInQ,MAAM,CAACwH,MAAP,KAAkBrH,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKoH,MAAL,GAAcxH,MAAM,CAACwH,MAArB;;AACA,cAAIxH,MAAM,CAAC8G,WAAP,KAAuB3G,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAK0G,WAAL,GAAmB9G,MAAM,CAAC8G,WAA1B;;AACA,cAAI9G,MAAM,CAAC+K,OAAP,KAAmB5K,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAK2K,OAAL,GAAe/K,MAAM,CAAC+K,OAAtB;;AACA,cAAI/K,MAAM,CAAC4K,aAAP,KAAyBzK,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAKqJ,YAAL,GAAoBzJ,MAAM,CAAC4K,aAA3B;;AACA,cAAI5K,MAAM,CAAC6K,cAAP,KAA0B1K,SAA9B,EAAyC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC9D,eAAKsJ,aAAL,GAAqB1J,MAAM,CAAC6K,cAA5B;;AACA,cAAI7K,MAAM,CAACoQ,QAAP,KAAoBjQ,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKgQ,QAAL,GAAgB,IAAIvV,aAAJ,CAAkBmF,MAAM,CAACoQ,QAAzB,CAAhB;;AACA,cAAIpQ,MAAM,CAACiG,OAAP,KAAmB9F,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD;AAAE,iBAAK6F,OAAL,GAAe,EAAf;;AAAmB,iBAAI,IAAI7E,KAAR,IAAiBpB,MAAM,CAACiG,OAAxB,EAAiC;AAAE,kBAAI5E,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAI1G,WAAJ,CAAgByG,KAAhB,CAAN;AAA8B,mBAAK6E,OAAL,CAAa3E,IAAb,CAAkBD,GAAlB;AAAwB;AAAC;;AACxH,cAAIrB,MAAM,CAACqQ,SAAP,KAAqBlQ,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD;AAAE,iBAAKiQ,SAAL,GAAiB,EAAjB;;AAAqB,iBAAI,IAAIjP,KAAR,IAAiBpB,MAAM,CAACqQ,SAAxB,EAAmC;AAAE,kBAAIhP,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAIzG,aAAJ,CAAkBwG,KAAlB,CAAN;AAAgC,mBAAKiP,SAAL,CAAe/O,IAAf,CAAoBD,GAApB;AAA0B;AAAC;AACnI;;AAoDDhB,QAAAA,OAAO,CAACC,MAAD,EAAgB;AAAA;;AAWnB,iCAAK8P,QAAL,4BAAe/P,OAAf,CAAuBC,MAAvB;;AACA,eAAK,IAAIgE,EAAT,IAAe,KAAK2B,OAApB,EAA6B;AAAE3B,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAEjE,OAAJ,CAAYC,MAAZ;AAAsB;;AACrD,eAAK,IAAIgE,IAAT,IAAe,KAAK+L,SAApB,EAA+B;AAAE/L,YAAAA,IAAE,QAAF,IAAAA,IAAE,CAAEjE,OAAJ,CAAYC,MAAZ;AAAsB;AAC1D;;AA/FiB,O;;0BAsGTlE,Q,GAAN,MAAMA,QAAN,CAAe;AAElB2D,QAAAA,WAAW,CAACC,MAAD,EAAc;AAuBzB;AACJ;AACA;AAzB6B,eA0BhBiB,EA1BgB;;AA2BzB;AACJ;AACA;AA7B6B,eA8BhBQ,IA9BgB;;AA+BzB;AACJ;AACA;AAjC6B,eAkChBE,IAlCgB;;AAmCzB;AACJ;AACA;AArC6B,eAsChBoF,IAtCgB;;AAuCzB;AACJ;AACA;AAzC6B,eA0ChBwJ,EA1CgB;;AA2CzB;AACJ;AACA;AA7C6B,eA8ChBC,MA9CgB;;AA+CzB;AACJ;AACA;AAjD6B,eAkDhBC,OAlDgB;;AAmDzB;AACJ;AACA;AArD6B,eAsDhBC,WAtDgB;AAAA,eAuDhBC,UAvDgB;AAAA,eAwDhB1K,OAxDgB;;AACrB,cAAIjG,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAACyB,IAAP,KAAgBtB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKqB,IAAL,GAAYzB,MAAM,CAACyB,IAAnB;;AACA,cAAIzB,MAAM,CAAC2B,IAAP,KAAgBxB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKuB,IAAL,GAAY3B,MAAM,CAAC2B,IAAnB;;AACA,cAAI3B,MAAM,CAAC+G,IAAP,KAAgB5G,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAK2G,IAAL,GAAY/G,MAAM,CAAC+G,IAAnB;;AACA,cAAI/G,MAAM,CAACuQ,EAAP,KAAcpQ,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKmQ,EAAL,GAAUvQ,MAAM,CAACuQ,EAAjB;;AACA,cAAIvQ,MAAM,CAACwQ,MAAP,KAAkBrQ,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKoQ,MAAL,GAAcxQ,MAAM,CAACwQ,MAArB;;AACA,cAAIxQ,MAAM,CAACyQ,OAAP,KAAmBtQ,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKqQ,OAAL,GAAezQ,MAAM,CAACyQ,OAAtB;;AACA,cAAIzQ,MAAM,CAAC0Q,WAAP,KAAuBvQ,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKsQ,WAAL,GAAmB1Q,MAAM,CAAC0Q,WAA1B;;AACA,cAAI1Q,MAAM,CAAC2Q,UAAP,KAAsBxQ,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D;AAAE,iBAAKuQ,UAAL,GAAkB,EAAlB;;AAAsB,iBAAI,IAAIvP,KAAR,IAAiBpB,MAAM,CAAC2Q,UAAxB,EAAoC;AAAE,kBAAItP,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAIhH,SAAJ,CAAc+G,KAAd,CAAN;AAA4B,mBAAKuP,UAAL,CAAgBrP,IAAhB,CAAqBD,GAArB;AAA2B;AAAC;;AAC/H,cAAIrB,MAAM,CAACiG,OAAP,KAAmB9F,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD;AAAE,iBAAK6F,OAAL,GAAe,EAAf;;AAAmB,iBAAI,IAAI7E,KAAR,IAAiBpB,MAAM,CAACiG,OAAxB,EAAiC;AAAE,kBAAI5E,IAAG,SAAP;;AAASA,cAAAA,IAAG,GAAG,IAAI5G,WAAJ,CAAgB2G,KAAhB,CAAN;AAA8B,mBAAK6E,OAAL,CAAa3E,IAAb,CAAkBD,IAAlB;AAAwB;AAAC;AAC3H;;AAqCDhB,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAWtB;;AAvEiB,O;;mCA8ETjE,iB,GAAN,MAAMA,iBAAN,CAAwB;AAE3B0D,QAAAA,WAAW,CAACC,MAAD,EAAc;AAWzB;AACJ;AACA;AAb6B,eAchBiB,EAdgB;;AAezB;AACJ;AACA;AAjB6B,eAkBhBhB,MAlBgB;;AAmBzB;AACJ;AACA;AArB6B,eAsBhB2Q,QAtBgB;AAAA,eAuBhBC,UAvBgB;;AACrB,cAAI7Q,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAACC,MAAP,KAAkBE,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKH,MAAL,GAAcD,MAAM,CAACC,MAArB;;AACA,cAAID,MAAM,CAAC4Q,QAAP,KAAoBzQ,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKwQ,QAAL,GAAgB5Q,MAAM,CAAC4Q,QAAvB;;AACA,cAAI5Q,MAAM,CAAC6Q,UAAP,KAAsB1Q,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D;AAAE,iBAAKyQ,UAAL,GAAkB,EAAlB;;AAAsB,iBAAI,IAAIzP,KAAR,IAAiBpB,MAAM,CAAC6Q,UAAxB,EAAoC;AAAE,kBAAIxP,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAI/E,qBAAJ,CAA0B8E,KAA1B,CAAN;AAAwC,mBAAKyP,UAAL,CAAgBvP,IAAhB,CAAqBD,GAArB;AAA2B;AAAC;AAC9I;;AAgBDhB,QAAAA,OAAO,CAACC,MAAD,EAAgB;AAInB,eAAK,IAAIgE,EAAT,IAAe,KAAKuM,UAApB,EAAgC;AAAEvM,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAEjE,OAAJ,CAAYC,MAAZ;AAAsB;AAC3D;;AAhC0B,O;;uCAuClBhE,qB,GAAN,MAAMA,qBAAN,CAA4B;AAE/ByD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAWhBmB,IAXgB;AAAA,eAYhByF,MAZgB;AAAA,eAahBkK,EAbgB;AAAA,eAchBC,KAdgB;;AACrB,cAAI/Q,MAAM,CAACmB,IAAP,KAAgBhB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKe,IAAL,GAAYnB,MAAM,CAACmB,IAAnB;;AACA,cAAInB,MAAM,CAAC4G,MAAP,KAAkBzG,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD;AAAE,iBAAKwG,MAAL,GAAc,EAAd;;AAAkB,iBAAI,IAAIxF,KAAR,IAAiBpB,MAAM,CAAC4G,MAAxB,EAAgC;AAAE,kBAAIvF,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKwF,MAAL,CAAYtF,IAAZ,CAAiBD,GAAjB;AAAuB;AAAC;;AACpG,cAAIrB,MAAM,CAAC8Q,EAAP,KAAc3Q,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAK0Q,EAAL,GAAU9Q,MAAM,CAAC8Q,EAAjB;;AACA,cAAI9Q,MAAM,CAAC+Q,KAAP,KAAiB5Q,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAK2Q,KAAL,GAAa/Q,MAAM,CAAC+Q,KAApB;AACH;;AAOD1Q,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAKtB;;AAvB8B,O;;0BA8BtB/D,Q,GAAN,MAAMA,QAAN,CAAe;AAElBwD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAuCzB;AACJ;AACA;AAzC6B,eA0ChBkD,EA1CgB;;AA2CzB;AACJ;AACA;AA7C6B,eA8ChBzB,IA9CgB;;AA+CzB;AACJ;AACA;AAjD6B,eAkDhBsF,IAlDgB;;AAmDzB;AACJ;AACA;AArD6B,eAsDhBiK,UAtDgB;;AAuDzB;AACJ;AACA;AAzD6B,eA0DhBC,KA1DgB;;AA2DzB;AACJ;AACA;AA7D6B,eA8DhBC,SA9DgB;;AA+DzB;AACJ;AACA;AAjE6B,eAkEhBC,WAlEgB;;AAmEzB;AACJ;AACA;AArE6B,eAsEhBC,QAtEgB;;AAuEzB;AACJ;AACA;AAzE6B,eA0EhBC,WA1EgB;;AA2EzB;AACJ;AACA;AA7E6B,eA8EhBC,UA9EgB;;AA+EzB;AACJ;AACA;AAjF6B,eAkFhBC,MAlFgB;;AAmFzB;AACJ;AACA;AArF6B,eAsFhB9B,KAtFgB;;AAuFzB;AACJ;AACA;AAzF6B,eA0FhB+B,OA1FgB;;AA2FzB;AACJ;AACA;AA7F6B,eA8FhBC,SA9FgB;;AA+FzB;AACJ;AACA;AAjG6B,eAkGhBC,OAlGgB;;AAmGzB;AACJ;AACA;AArG6B,eAsGhBC,QAtGgB;;AAuGzB;AACJ;AACA;AAzG6B,eA0GhBC,OA1GgB;;AA2GzB;AACJ;AACA;AA7G6B,eA8GhBC,KA9GgB;;AACrB,cAAI7R,MAAM,CAACkD,EAAP,KAAc/C,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAK8C,EAAL,GAAUlD,MAAM,CAACkD,EAAjB;;AACA,cAAIlD,MAAM,CAACyB,IAAP,KAAgBtB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKqB,IAAL,GAAYzB,MAAM,CAACyB,IAAnB;;AACA,cAAIzB,MAAM,CAAC+G,IAAP,KAAgB5G,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAK2G,IAAL,GAAY/G,MAAM,CAAC+G,IAAnB;;AACA,cAAI/G,MAAM,CAACgR,UAAP,KAAsB7Q,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK4Q,UAAL,GAAkBhR,MAAM,CAACgR,UAAzB;;AACA,cAAIhR,MAAM,CAACiR,KAAP,KAAiB9Q,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAK6Q,KAAL,GAAajR,MAAM,CAACiR,KAApB;;AACA,cAAIjR,MAAM,CAACkR,SAAP,KAAqB/Q,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAK8Q,SAAL,GAAiBlR,MAAM,CAACkR,SAAxB;;AACA,cAAIlR,MAAM,CAACmR,WAAP,KAAuBhR,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAK+Q,WAAL,GAAmBnR,MAAM,CAACmR,WAA1B;;AACA,cAAInR,MAAM,CAACoR,QAAP,KAAoBjR,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKgR,QAAL,GAAgBpR,MAAM,CAACoR,QAAvB;;AACA,cAAIpR,MAAM,CAACqR,WAAP,KAAuBlR,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKiR,WAAL,GAAmBrR,MAAM,CAACqR,WAA1B;;AACA,cAAIrR,MAAM,CAACsR,UAAP,KAAsBnR,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKkR,UAAL,GAAkBtR,MAAM,CAACsR,UAAzB;;AACA,cAAItR,MAAM,CAACuR,MAAP,KAAkBpR,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKmR,MAAL,GAAcvR,MAAM,CAACuR,MAArB;;AACA,cAAIvR,MAAM,CAACyP,KAAP,KAAiBtP,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKqP,KAAL,GAAazP,MAAM,CAACyP,KAApB;;AACA,cAAIzP,MAAM,CAACwR,OAAP,KAAmBrR,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKoR,OAAL,GAAexR,MAAM,CAACwR,OAAtB;;AACA,cAAIxR,MAAM,CAACyR,SAAP,KAAqBtR,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKqR,SAAL,GAAiBzR,MAAM,CAACyR,SAAxB;;AACA,cAAIzR,MAAM,CAAC0R,OAAP,KAAmBvR,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKsR,OAAL,GAAe1R,MAAM,CAAC0R,OAAtB;;AACA,cAAI1R,MAAM,CAAC2R,QAAP,KAAoBxR,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKuR,QAAL,GAAgB3R,MAAM,CAAC2R,QAAvB;;AACA,cAAI3R,MAAM,CAAC4R,OAAP,KAAmBzR,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKwR,OAAL,GAAe5R,MAAM,CAAC4R,OAAtB;;AACA,cAAI5R,MAAM,CAAC6R,KAAP,KAAiB1R,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKyR,KAAL,GAAa7R,MAAM,CAAC6R,KAApB;AACH;;AA2EDxR,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAmBtB;;AArIiB,O;;yBA4IT9D,O,GAAN,MAAMA,OAAN,CAAc;AAEjBuD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAqCzB;AACJ;AACA;AAvC6B,eAwChBqD,MAxCgB;;AAyCzB;AACJ;AACA;AA3C6B,eA4ChBC,OA5CgB;;AA6CzB;AACJ;AACA;AA/C6B,eAgDhBwO,SAhDgB;;AAiDzB;AACJ;AACA;AAnD6B,eAoDhBvO,MApDgB;;AAqDzB;AACJ;AACA;AAvD6B,eAwDhBC,UAxDgB;;AAyDzB;AACJ;AACA;AA3D6B,eA4DhBuO,OA5DgB;;AA6DzB;AACJ;AACA;AA/D6B,eAgEhBtO,QAhEgB;AAAA,eAiEhBC,QAjEgB;;AAkEzB;AACJ;AACA;AApE6B,eAqEhBC,UArEgB;;AAsEzB;AACJ;AACA;AAxE6B,eAyEhBC,QAzEgB;;AA0EzB;AACJ;AACA;AA5E6B,eA6EhBoO,YA7EgB;;AA8EzB;AACJ;AACA;AAhF6B,eAiFhBC,UAjFgB;;AAkFzB;AACJ;AACA;AApF6B,eAqFhBvN,QArFgB;;AAsFzB;AACJ;AACA;AAxF6B,eAyFhBC,QAzFgB;;AA0FzB;AACJ;AACA;AA5F6B,eA6FhBC,SA7FgB;;AA8FzB;AACJ;AACA;AAhG6B,eAiGhBC,SAjGgB;;AAkGzB;AACJ;AACA;AApG6B,eAqGhBhB,MArGgB;;AACrB,cAAI7D,MAAM,CAAC8D,OAAP,KAAmB3D,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKiD,MAAL,GAAcrD,MAAM,CAAC8D,OAArB;;AACA,cAAI9D,MAAM,CAAC+D,QAAP,KAAoB5D,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKkD,OAAL,GAAetD,MAAM,CAAC+D,QAAtB;;AACA,cAAI/D,MAAM,CAACkS,UAAP,KAAsB/R,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK0R,SAAL,GAAiB9R,MAAM,CAACkS,UAAxB;;AACA,cAAIlS,MAAM,CAACgE,OAAP,KAAmB7D,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKmD,MAAL,GAAcvD,MAAM,CAACgE,OAArB;;AACA,cAAIhE,MAAM,CAACiE,WAAP,KAAuB9D,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKoD,UAAL,GAAkBxD,MAAM,CAACiE,WAAzB;;AACA,cAAIjE,MAAM,CAACmS,QAAP,KAAoBhS,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAK2R,OAAL,GAAe/R,MAAM,CAACmS,QAAtB;;AACA,cAAInS,MAAM,CAACkE,SAAP,KAAqB/D,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD;AAAE,iBAAKqD,QAAL,GAAgB,EAAhB;;AAAoB,iBAAI,IAAIrC,KAAR,IAAiBpB,MAAM,CAACkE,SAAxB,EAAmC;AAAE,kBAAI7C,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAIhG,YAAJ,CAAiB+F,KAAjB,CAAN;AAA+B,mBAAKqC,QAAL,CAAcnC,IAAd,CAAmBD,GAAnB;AAAyB;AAAC;;AAC7H,cAAIrB,MAAM,CAACmE,SAAP,KAAqBhE,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKsD,QAAL,GAAgB,IAAIjH,WAAJ,CAAgBuD,MAAM,CAACmE,SAAvB,CAAhB;;AACA,cAAInE,MAAM,CAAC2D,UAAP,KAAsBxD,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKuD,UAAL,GAAkB3D,MAAM,CAAC2D,UAAzB;;AACA,cAAI3D,MAAM,CAACoE,SAAP,KAAqBjE,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKwD,QAAL,GAAgB5D,MAAM,CAACoE,SAAvB;;AACA,cAAIpE,MAAM,CAACoS,cAAP,KAA0BjS,SAA9B,EAAyC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC9D,eAAK4R,YAAL,GAAoBhS,MAAM,CAACoS,cAA3B;;AACA,cAAIpS,MAAM,CAACqS,WAAP,KAAuBlS,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAK6R,UAAL,GAAkBjS,MAAM,CAACqS,WAAzB;;AACA,cAAIrS,MAAM,CAACgF,SAAP,KAAqB7E,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKsE,QAAL,GAAgB1E,MAAM,CAACgF,SAAvB;;AACA,cAAIhF,MAAM,CAACiF,SAAP,KAAqB9E,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKuE,QAAL,GAAgB3E,MAAM,CAACiF,SAAvB;;AACA,cAAIjF,MAAM,CAACkF,UAAP,KAAsB/E,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKwE,SAAL,GAAiB5E,MAAM,CAACkF,UAAxB;;AACA,cAAIlF,MAAM,CAACmF,UAAP,KAAsBhF,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKyE,SAAL,GAAiB7E,MAAM,CAACmF,UAAxB;;AACA,cAAInF,MAAM,CAACqE,OAAP,KAAmBlE,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKyD,MAAL,GAAc7D,MAAM,CAACqE,OAArB;AACH;;AAoEDhE,QAAAA,OAAO,CAACC,MAAD,EAAgB;AAAA;;AAOnB,eAAK,IAAIgE,EAAT,IAAe,KAAKb,QAApB,EAA8B;AAAEa,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAEjE,OAAJ,CAAYC,MAAZ;AAAsB;;AACtD,kCAAKoD,QAAL,6BAAerD,OAAf,CAAuBC,MAAvB;AAUH;;AA3HgB,O;AAkIrB;AACA;AACA;;;6BACa7D,W,GAAN,MAAMA,WAAN,CAAkB;AAErBsD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAShBsS,QATgB;AAAA,eAUhB1L,MAVgB;AAAA,eAWhBjF,IAXgB;;AACrB,cAAI3B,MAAM,CAACuS,SAAP,KAAqBpS,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKkS,QAAL,GAAgBtS,MAAM,CAACuS,SAAvB;;AACA,cAAIvS,MAAM,CAAC4G,MAAP,KAAkBzG,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD;AAAE,iBAAKwG,MAAL,GAAc,EAAd;;AAAkB,iBAAI,IAAIxF,KAAR,IAAiBpB,MAAM,CAAC4G,MAAxB,EAAgC;AAAE,kBAAIvF,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKwF,MAAL,CAAYtF,IAAZ,CAAiBD,GAAjB;AAAuB;AAAC;;AACpG,cAAIrB,MAAM,CAAC2B,IAAP,KAAgBxB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKuB,IAAL,GAAY3B,MAAM,CAAC2B,IAAnB;AACH;;AAMDtB,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAItB;;AAnBoB,O;;8BA0BZ5D,Y,GAAN,MAAMA,YAAN,CAAmB;AAEtBqD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAqBzB;AACJ;AACA;AAvB6B,eAwBhBqD,MAxBgB;;AAyBzB;AACJ;AACA;AA3B6B,eA4BhBC,OA5BgB;;AA6BzB;AACJ;AACA;AA/B6B,eAgChBkP,KAhCgB;;AAiCzB;AACJ;AACA;AAnC6B,eAoChBhP,UApCgB;;AAqCzB;AACJ;AACA;AAvC6B,eAwChBuO,OAxCgB;AAAA,eAyChBtO,QAzCgB;AAAA,eA0ChBC,QA1CgB;;AA2CzB;AACJ;AACA;AA7C6B,eA8ChBC,UA9CgB;;AA+CzB;AACJ;AACA;AAjD6B,eAkDhBC,QAlDgB;;AACrB,cAAI5D,MAAM,CAAC8D,OAAP,KAAmB3D,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKiD,MAAL,GAAcrD,MAAM,CAAC8D,OAArB;;AACA,cAAI9D,MAAM,CAAC+D,QAAP,KAAoB5D,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKkD,OAAL,GAAetD,MAAM,CAAC+D,QAAtB;;AACA,cAAI/D,MAAM,CAACwS,KAAP,KAAiBrS,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKoS,KAAL,GAAaxS,MAAM,CAACwS,KAApB;;AACA,cAAIxS,MAAM,CAACiE,WAAP,KAAuB9D,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKoD,UAAL,GAAkBxD,MAAM,CAACiE,WAAzB;;AACA,cAAIjE,MAAM,CAACmS,QAAP,KAAoBhS,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAK2R,OAAL,GAAe/R,MAAM,CAACmS,QAAtB;;AACA,cAAInS,MAAM,CAACkE,SAAP,KAAqB/D,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD;AAAE,iBAAKqD,QAAL,GAAgB,EAAhB;;AAAoB,iBAAI,IAAIrC,KAAR,IAAiBpB,MAAM,CAACkE,SAAxB,EAAmC;AAAE,kBAAI7C,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAIhG,YAAJ,CAAiB+F,KAAjB,CAAN;AAA+B,mBAAKqC,QAAL,CAAcnC,IAAd,CAAmBD,GAAnB;AAAyB;AAAC;;AAC7H,cAAIrB,MAAM,CAACmE,SAAP,KAAqBhE,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKsD,QAAL,GAAgB,IAAIjH,WAAJ,CAAgBuD,MAAM,CAACmE,SAAvB,CAAhB;;AACA,cAAInE,MAAM,CAAC2D,UAAP,KAAsBxD,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKuD,UAAL,GAAkB3D,MAAM,CAAC2D,UAAzB;;AACA,cAAI3D,MAAM,CAACoE,SAAP,KAAqBjE,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKwD,QAAL,GAAgB5D,MAAM,CAACoE,SAAvB;AACH;;AAiCD/D,QAAAA,OAAO,CAACC,MAAD,EAAgB;AAAA;;AAMnB,eAAK,IAAIgE,EAAT,IAAe,KAAKb,QAApB,EAA8B;AAAEa,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAEjE,OAAJ,CAAYC,MAAZ;AAAsB;;AACtD,kCAAKoD,QAAL,6BAAerD,OAAf,CAAuBC,MAAvB;AAGH;;AAhEqB,O;;4BAuEb3D,U,GAAN,MAAMA,UAAN,CAAiB;AAEpBoD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAWzB;AACJ;AACA;AAb6B,eAchByS,SAdgB;;AAezB;AACJ;AACA;AAjB6B,eAkBhBC,EAlBgB;;AAmBzB;AACJ;AACA;AArB6B,eAsBhB9O,QAtBgB;;AAuBzB;AACJ;AACA;AAzB6B,eA0BhB+O,cA1BgB;;AACrB,cAAI3S,MAAM,CAAC4S,UAAP,KAAsBzS,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKqS,SAAL,GAAiBzS,MAAM,CAAC4S,UAAxB;;AACA,cAAI5S,MAAM,CAAC0S,EAAP,KAAcvS,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKsS,EAAL,GAAU1S,MAAM,CAAC0S,EAAjB;;AACA,cAAI1S,MAAM,CAACoE,SAAP,KAAqBjE,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKwD,QAAL,GAAgB5D,MAAM,CAACoE,SAAvB;;AACA,cAAIpE,MAAM,CAAC6S,eAAP,KAA2B1S,SAA/B,EAA0C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC/D;AAAE,iBAAKuS,cAAL,GAAsB,EAAtB;;AAA0B,iBAAI,IAAIvR,KAAR,IAAiBpB,MAAM,CAAC6S,eAAxB,EAAyC;AAAE,kBAAIxR,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKuR,cAAL,CAAoBrR,IAApB,CAAyBD,GAAzB;AAA+B;AAAC;AAChI;;AAmBDhB,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAKtB;;AAnCmB,O;;8BA2CX1D,Y,GAAN,MAAMA,YAAN,CAAmB;AAGtBmD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eADjB8S,KACiB;AACrB,cAAI9S,MAAM,CAAC+S,MAAP,IAAiB,CAArB,EAAwB,MAAM,IAAI3S,KAAJ,CAAU,+BAAV,CAAN;AACxB,eAAK0S,KAAL,GAAa,IAAIlX,aAAJ,CAAkBoE,MAAM,CAAC,CAAD,CAAxB,CAAb;AACH;;AAEDgT,QAAAA,OAAO,GAAkB;AAAE,iBAAO,KAAKF,KAAZ;AAAoB;AAE/C;AACJ;AACA;;;AACsB,YAAb3F,aAAa,GAAW;AAAE,iBAAO,KAAK2F,KAAL,CAAW3F,aAAlB;AAAkC;AACjE;AACJ;AACA;;;AACkB,YAATvK,SAAS,GAAW;AAAE,iBAAO,KAAKkQ,KAAL,CAAWlQ,SAAlB;AAA8B;AACzD;AACJ;AACA;;;AAC8B,YAArBwK,qBAAqB,GAAW;AAAE,iBAAO,KAAK0F,KAAL,CAAW1F,qBAAlB;AAA0C;AACjF;AACJ;AACA;;;AAC2B,YAAlBC,kBAAkB,GAAW;AAAE,iBAAO,KAAKyF,KAAL,CAAWzF,kBAAlB;AAAuC;AAC3E;AACJ;AACA;;;AACyB,YAAhBC,gBAAgB,GAAW;AAAE,iBAAO,KAAKwF,KAAL,CAAWxF,gBAAlB;AAAqC;AACvE;AACJ;AACA;;;AAC0B,YAAjBC,iBAAiB,GAAW;AAAE,iBAAO,KAAKuF,KAAL,CAAWvF,iBAAlB;AAAsC;AACzE;AACJ;AACA;;;AACsC,YAA7BC,6BAA6B,GAAW;AAAE,iBAAO,KAAKsF,KAAL,CAAWtF,6BAAlB;AAAkD;AACjG;AACJ;AACA;;;AACmC,YAA1BC,0BAA0B,GAAW;AAAE,iBAAO,KAAKqF,KAAL,CAAWrF,0BAAlB;AAA+C;AAC3F;AACJ;AACA;;;AACoC,YAA3BC,2BAA2B,GAAW;AAAE,iBAAO,KAAKoF,KAAL,CAAWpF,2BAAlB;AAAgD;AAC7F;AACJ;AACA;;;AACkC,YAAzBC,yBAAyB,GAAW;AAAE,iBAAO,KAAKmF,KAAL,CAAWnF,yBAAlB;AAA8C;AACzF;AACJ;AACA;;;AACwB,YAAfC,eAAe,GAAW;AAAE,iBAAO,KAAKkF,KAAL,CAAWlF,eAAlB;AAAoC;;AAErEvN,QAAAA,OAAO,CAACC,MAAD,EACP;AACI,eAAKwS,KAAL,CAAWzS,OAAX,CAAmBC,MAAnB;AACH;;AA1DqB,O;;gCAiEbzD,c,GAAN,MAAMA,cAAN,CAAqB;AAGxBkD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBiT,SAEiB;AACrB,eAAKA,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBlT,MAAnB,EAA2B;AACvB,gBAAImT,EAAmB,SAAvB;;AACAA,YAAAA,EAAE,GAAG,IAAIzX,eAAJ,CAAoBwX,OAApB,CAAL;;AACA,iBAAKD,SAAL,CAAe3R,IAAf,CAAoB6R,EAApB;AACH;AACJ;;AAEDC,QAAAA,WAAW,GAAsB;AAAE,iBAAO,KAAKH,SAAZ;AAAuB;;AAE1DI,QAAAA,GAAG,CAACb,KAAD,EAA6C;AAAE,iBAAO,KAAKS,SAAL,CAAeT,KAAf,CAAP;AAA8B;;AAEhFnS,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKgT,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAACjT,OAAL,CAAaC,MAAb;AACH;AACJ;;AArBuB,O;;sBA4BfxD,I,GAAN,MAAMA,IAAN,CAAW;AAGdiD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBiT,SAEiB;AACrB,eAAKA,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBlT,MAAnB,EAA2B;AACvB,gBAAImT,EAAM,SAAV;;AACAA,YAAAA,EAAE,GAAG,IAAIzY,EAAJ,CAAOwY,OAAP,CAAL;;AACA,iBAAKD,SAAL,CAAe3R,IAAf,CAAoB6R,EAApB;AACH;AACJ;;AAEDC,QAAAA,WAAW,GAAS;AAAE,iBAAO,KAAKH,SAAZ;AAAuB;;AAE7CI,QAAAA,GAAG,CAACb,KAAD,EAAgC;AAAE,iBAAO,KAAKS,SAAL,CAAeT,KAAf,CAAP;AAA8B;;AAEnEnS,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKgT,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAACjT,OAAL,CAAaC,MAAb;AACH;AACJ;;AArBa,O;;yBA4BLvD,O,GAAN,MAAMA,OAAN,CAAc;AAGjBgD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBuT,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBlT,MAAnB,EAA2B;AACvB,gBAAImT,EAAY,SAAhB;;AACAA,YAAAA,EAAE,GAAG,IAAIhX,QAAJ,CAAa+W,OAAb,CAAL;;AACA,iBAAKD,SAAL,CAAe3R,IAAf,CAAoB6R,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAAClS,EAArB,EAAyBkS,EAAzB;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAA0B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC7DH,QAAAA,WAAW,GAAe;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEpDI,QAAAA,GAAG,CAACM,GAAD,EAAoC;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAEzEtT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKgT,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAACjT,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBgB,O;;2BA+BRtD,S,GAAN,MAAMA,SAAN,CAAgB;AAGnB+C,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBuT,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBlT,MAAnB,EAA2B;AACvB,gBAAImT,EAAW,SAAf;;AACAA,YAAAA,EAAE,GAAG,IAAI3W,OAAJ,CAAY0W,OAAZ,CAAL;;AACA,iBAAKD,SAAL,CAAe3R,IAAf,CAAoB6R,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAAC9P,MAArB,EAA6B8P,EAA7B;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAAyB;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC5DH,QAAAA,WAAW,GAAc;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEnDI,QAAAA,GAAG,CAACM,GAAD,EAAmC;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAExEtT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKgT,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAACjT,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBkB,O;;kCA+BVrD,gB,GAAN,MAAMA,gBAAN,CAAuB;AAG1B8C,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBuT,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBlT,MAAnB,EAA2B;AACvB,gBAAImT,EAAkB,SAAtB;;AACAA,YAAAA,EAAE,GAAG,IAAIlY,cAAJ,CAAmBiY,OAAnB,CAAL;;AACA,iBAAKD,SAAL,CAAe3R,IAAf,CAAoB6R,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAAC9P,MAArB,EAA6B8P,EAA7B;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAAgC;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AACnEH,QAAAA,WAAW,GAAqB;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAE1DI,QAAAA,GAAG,CAACM,GAAD,EAA0C;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAE/EtT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKgT,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAACjT,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxByB,O;;+BA+BjBpD,a,GAAN,MAAMA,aAAN,CAAoB;AAGvB6C,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBuT,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBlT,MAAnB,EAA2B;AACvB,gBAAImT,EAAe,SAAnB;;AACAA,YAAAA,EAAE,GAAG,IAAIjY,WAAJ,CAAgBgY,OAAhB,CAAL;;AACA,iBAAKD,SAAL,CAAe3R,IAAf,CAAoB6R,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAAC9P,MAArB,EAA6B8P,EAA7B;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAA6B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAChEH,QAAAA,WAAW,GAAkB;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEvDI,QAAAA,GAAG,CAACM,GAAD,EAAuC;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAE5EtT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKgT,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAACjT,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBsB,O;;6BA+BdnD,W,GAAN,MAAMA,WAAN,CAAkB;AAGrB4C,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBuT,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBlT,MAAnB,EAA2B;AACvB,gBAAImT,EAAa,SAAjB;;AACAA,YAAAA,EAAE,GAAG,IAAIhY,SAAJ,CAAc+X,OAAd,CAAL;;AACA,iBAAKD,SAAL,CAAe3R,IAAf,CAAoB6R,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAAClS,EAArB,EAAyBkS,EAAzB;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAA2B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC9DH,QAAAA,WAAW,GAAgB;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAErDI,QAAAA,GAAG,CAACM,GAAD,EAAqC;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAE1EtT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKgT,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAACjT,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBoB,O;;8BA+BZlD,Y,GAAN,MAAMA,YAAN,CAAmB;AAGtB2C,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBuT,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBlT,MAAnB,EAA2B;AACvB,gBAAImT,EAAc,SAAlB;;AACAA,YAAAA,EAAE,GAAG,IAAI5X,UAAJ,CAAe2X,OAAf,CAAL;;AACA,iBAAKD,SAAL,CAAe3R,IAAf,CAAoB6R,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAAClS,EAArB,EAAyBkS,EAAzB;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAA4B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC/DH,QAAAA,WAAW,GAAiB;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEtDI,QAAAA,GAAG,CAACM,GAAD,EAAsC;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAE3EtT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKgT,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAACjT,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBqB,O;;8BA+BbjD,Y,GAAN,MAAMA,YAAN,CAAmB;AAGtB0C,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBuT,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBlT,MAAnB,EAA2B;AACvB,gBAAImT,EAAc,SAAlB;;AACAA,YAAAA,EAAE,GAAG,IAAI/X,UAAJ,CAAe8X,OAAf,CAAL;;AACA,iBAAKD,SAAL,CAAe3R,IAAf,CAAoB6R,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAAClS,EAArB,EAAyBkS,EAAzB;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAA4B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC/DH,QAAAA,WAAW,GAAiB;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEtDI,QAAAA,GAAG,CAACM,GAAD,EAAsC;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAE3EtT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKgT,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAACjT,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBqB,O;;6BA+BbhD,W,GAAN,MAAMA,WAAN,CAAkB;AAGrByC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBuT,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBlT,MAAnB,EAA2B;AACvB,gBAAImT,EAAa,SAAjB;;AACAA,YAAAA,EAAE,GAAG,IAAI7X,SAAJ,CAAc4X,OAAd,CAAL;;AACA,iBAAKD,SAAL,CAAe3R,IAAf,CAAoB6R,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAAClS,EAArB,EAAyBkS,EAAzB;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAA2B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC9DH,QAAAA,WAAW,GAAgB;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAErDI,QAAAA,GAAG,CAACM,GAAD,EAAqC;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAE1EtT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKgT,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAACjT,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBoB,O;;4BA+BZ/C,U,GAAN,MAAMA,UAAN,CAAiB;AAGpBwC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBuT,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBlT,MAAnB,EAA2B;AACvB,gBAAImT,EAAY,SAAhB;;AACAA,YAAAA,EAAE,GAAG,IAAI3X,QAAJ,CAAa0X,OAAb,CAAL;;AACA,iBAAKD,SAAL,CAAe3R,IAAf,CAAoB6R,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAAClS,EAArB,EAAyBkS,EAAzB;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAA0B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC7DH,QAAAA,WAAW,GAAe;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEpDI,QAAAA,GAAG,CAACM,GAAD,EAAoC;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAEzEtT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKgT,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAACjT,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBmB,O;;4BA+BX9C,U,GAAN,MAAMA,UAAN,CAAiB;AAGpBuC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBuT,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBlT,MAAnB,EAA2B;AACvB,gBAAImT,EAAY,SAAhB;;AACAA,YAAAA,EAAE,GAAG,IAAI/W,QAAJ,CAAa8W,OAAb,CAAL;;AACA,iBAAKD,SAAL,CAAe3R,IAAf,CAAoB6R,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAAClS,EAArB,EAAyBkS,EAAzB;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAA0B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC7DH,QAAAA,WAAW,GAAe;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEpDI,QAAAA,GAAG,CAACM,GAAD,EAAoC;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAEzEtT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKgT,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAACjT,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBmB,O;;4BA+BX7C,U,GAAN,MAAMA,UAAN,CAAiB;AAGpBsC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBuT,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBlT,MAAnB,EAA2B;AACvB,gBAAImT,EAAY,SAAhB;;AACAA,YAAAA,EAAE,GAAG,IAAIrX,QAAJ,CAAaoX,OAAb,CAAL;;AACA,iBAAKD,SAAL,CAAe3R,IAAf,CAAoB6R,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAAClS,EAArB,EAAyBkS,EAAzB;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAA0B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC7DH,QAAAA,WAAW,GAAe;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEpDI,QAAAA,GAAG,CAACM,GAAD,EAAoC;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAEzEtT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKgT,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAACjT,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBmB,O;;2BA+BX5C,S,GAAN,MAAMA,SAAN,CAAgB;AAGnBqC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBuT,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBlT,MAAnB,EAA2B;AACvB,gBAAImT,EAAW,SAAf;;AACAA,YAAAA,EAAE,GAAG,IAAInX,OAAJ,CAAYkX,OAAZ,CAAL;;AACA,iBAAKD,SAAL,CAAe3R,IAAf,CAAoB6R,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAACnE,MAArB,EAA6BmE,EAA7B;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAAyB;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC5DH,QAAAA,WAAW,GAAc;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEnDI,QAAAA,GAAG,CAACM,GAAD,EAAmC;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAExEtT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKgT,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAACjT,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBkB,O;;4BA+BV3C,U,GAAN,MAAMA,UAAN,CAAiB;AAGpBoC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBuT,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBlT,MAAnB,EAA2B;AACvB,gBAAImT,EAAY,SAAhB;;AACAA,YAAAA,EAAE,GAAG,IAAI1X,QAAJ,CAAayX,OAAb,CAAL;;AACA,iBAAKD,SAAL,CAAe3R,IAAf,CAAoB6R,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAAClS,EAArB,EAAyBkS,EAAzB;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAA0B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC7DH,QAAAA,WAAW,GAAe;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEpDI,QAAAA,GAAG,CAACM,GAAD,EAAoC;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAEzEtT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKgT,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAACjT,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBmB,O;;2BA+BX1C,S,GAAN,MAAMA,SAAN,CAAgB;AAGnBmC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBuT,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBlT,MAAnB,EAA2B;AACvB,gBAAImT,EAAW,SAAf;;AACAA,YAAAA,EAAE,GAAG,IAAItX,OAAJ,CAAYqX,OAAZ,CAAL;;AACA,iBAAKD,SAAL,CAAe3R,IAAf,CAAoB6R,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAAClS,EAArB,EAAyBkS,EAAzB;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAAyB;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC5DH,QAAAA,WAAW,GAAc;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEnDI,QAAAA,GAAG,CAACM,GAAD,EAAmC;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAExEtT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKgT,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAACjT,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBkB,O;;+BA+BVzC,a,GAAN,MAAMA,aAAN,CAAoB;AAGvBkC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBuT,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBlT,MAAnB,EAA2B;AACvB,gBAAImT,EAAe,SAAnB;;AACAA,YAAAA,EAAE,GAAG,IAAIxX,WAAJ,CAAgBuX,OAAhB,CAAL;;AACA,iBAAKD,SAAL,CAAe3R,IAAf,CAAoB6R,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAACpH,MAArB,EAA6BoH,EAA7B;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAA6B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAChEH,QAAAA,WAAW,GAAkB;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEvDI,QAAAA,GAAG,CAACM,GAAD,EAAuC;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAE5EtT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKgT,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAACjT,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBsB,O;;iCA+BdxC,e,GAAN,MAAMA,eAAN,CAAsB;AAGzBiC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBuT,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBlT,MAAnB,EAA2B;AACvB,gBAAImT,EAAiB,SAArB;;AACAA,YAAAA,EAAE,GAAG,IAAIpX,aAAJ,CAAkBmX,OAAlB,CAAL;;AACA,iBAAKD,SAAL,CAAe3R,IAAf,CAAoB6R,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAAClS,EAArB,EAAyBkS,EAAzB;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAA+B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAClEH,QAAAA,WAAW,GAAoB;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEzDI,QAAAA,GAAG,CAACM,GAAD,EAAyC;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAE9EtT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKgT,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAACjT,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBwB,O;;iCA+BhBvC,e,GAAN,MAAMA,eAAN,CAAsB;AAGzBgC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBuT,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBlT,MAAnB,EAA2B;AACvB,gBAAImT,EAAiB,SAArB;;AACAA,YAAAA,EAAE,GAAG,IAAIjX,aAAJ,CAAkBgX,OAAlB,CAAL;;AACA,iBAAKD,SAAL,CAAe3R,IAAf,CAAoB6R,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAACpH,MAArB,EAA6BoH,EAA7B;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAA+B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAClEH,QAAAA,WAAW,GAAoB;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEzDI,QAAAA,GAAG,CAACM,GAAD,EAAyC;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAE9EtT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKgT,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAACjT,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBwB,O;;8BA+BhBtC,Y,GAAN,MAAMA,YAAN,CAAmB;AAGtB+B,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBuT,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBlT,MAAnB,EAA2B;AACvB,gBAAImT,EAAc,SAAlB;;AACAA,YAAAA,EAAE,GAAG,IAAIxW,UAAJ,CAAeuW,OAAf,CAAL;;AACA,iBAAKD,SAAL,CAAe3R,IAAf,CAAoB6R,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAACV,SAArB,EAAgCU,EAAhC;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAA4B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC/DH,QAAAA,WAAW,GAAiB;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEtDI,QAAAA,GAAG,CAACM,GAAD,EAAsC;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAE3EtT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKgT,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAACjT,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBqB,O;;gCA+BbrC,c,GAAN,MAAMA,cAAN,CAAqB;AAGxB8B,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBuT,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBlT,MAAnB,EAA2B;AACvB,gBAAImT,EAAgB,SAApB;;AACAA,YAAAA,EAAE,GAAG,IAAIzW,YAAJ,CAAiBwW,OAAjB,CAAL;;AACA,iBAAKD,SAAL,CAAe3R,IAAf,CAAoB6R,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAAC9P,MAArB,EAA6B8P,EAA7B;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAA8B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AACjEH,QAAAA,WAAW,GAAmB;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAExDI,QAAAA,GAAG,CAACM,GAAD,EAAwC;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAE7EtT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKgT,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAACjT,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBuB,O;;4BA+BfpC,U,GAAN,MAAMA,UAAN,CAAiB;AAGpB6B,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBuT,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBlT,MAAnB,EAA2B;AACvB,gBAAImT,EAAY,SAAhB;;AACAA,YAAAA,EAAE,GAAG,IAAI5W,QAAJ,CAAa2W,OAAb,CAAL;;AACA,iBAAKD,SAAL,CAAe3R,IAAf,CAAoB6R,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAACjQ,EAArB,EAAyBiQ,EAAzB;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAA0B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC7DH,QAAAA,WAAW,GAAe;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEpDI,QAAAA,GAAG,CAACM,GAAD,EAAoC;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAEzEtT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKgT,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAACjT,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBmB,O;;qCA+BXnC,mB,GAAN,MAAMA,mBAAN,CAA0B;AAG7B4B,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBuT,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBlT,MAAnB,EAA2B;AACvB,gBAAImT,EAAqB,SAAzB;;AACAA,YAAAA,EAAE,GAAG,IAAI9W,iBAAJ,CAAsB6W,OAAtB,CAAL;;AACA,iBAAKD,SAAL,CAAe3R,IAAf,CAAoB6R,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAAClS,EAArB,EAAyBkS,EAAzB;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAAmC;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AACtEH,QAAAA,WAAW,GAAwB;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAE7DI,QAAAA,GAAG,CAACM,GAAD,EAA6C;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAElFtT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKgT,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAACjT,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxB4B,O;;wBAiCpBlC,M,GAAN,MAAMA,MAAN,CAAa;AAEA,YAAZxB,YAAY,GAAkB;AAAE,iBAAO,KAAKgX,aAAZ;AAA2B;;AAE7C,YAAd/W,cAAc,GAAoB;AAAE,iBAAO,KAAKgX,eAAZ;AAA6B;;AAE7D,YAAJ/W,IAAI,GAAU;AAAE,iBAAO,KAAKgX,KAAZ;AAAmB;;AAE5B,YAAP/W,OAAO,GAAa;AAAE,iBAAO,KAAKgX,QAAZ;AAAsB;;AAEnC,YAAT/W,SAAS,GAAe;AAAE,iBAAO,KAAKgX,UAAZ;AAAwB;;AAElC,YAAhB/W,gBAAgB,GAAsB;AAAE,iBAAO,KAAKgX,iBAAZ;AAA+B;;AAE1D,YAAb/W,aAAa,GAAmB;AAAE,iBAAO,KAAKgX,cAAZ;AAA4B;;AAEnD,YAAX/W,WAAW,GAAiB;AAAE,iBAAO,KAAKgX,YAAZ;AAA0B;;AAE5C,YAAZ/W,YAAY,GAAkB;AAAE,iBAAO,KAAKgX,aAAZ;AAA2B;;AAE/C,YAAZ/W,YAAY,GAAkB;AAAE,iBAAO,KAAKgX,aAAZ;AAA2B;;AAEhD,YAAX/W,WAAW,GAAiB;AAAE,iBAAO,KAAKgX,YAAZ;AAA0B;;AAE9C,YAAV/W,UAAU,GAAgB;AAAE,iBAAO,KAAKgX,WAAZ;AAAyB;;AAE3C,YAAV/W,UAAU,GAAgB;AAAE,iBAAO,KAAKgX,WAAZ;AAAyB;;AAE3C,YAAV/W,UAAU,GAAgB;AAAE,iBAAO,KAAKgX,WAAZ;AAAyB;;AAE5C,YAAT/W,SAAS,GAAe;AAAE,iBAAO,KAAKgX,UAAZ;AAAwB;;AAExC,YAAV/W,UAAU,GAAgB;AAAE,iBAAO,KAAKgX,WAAZ;AAAyB;;AAE5C,YAAT/W,SAAS,GAAe;AAAE,iBAAO,KAAKgX,UAAZ;AAAwB;;AAErC,YAAb/W,aAAa,GAAmB;AAAE,iBAAO,KAAKgX,cAAZ;AAA4B;;AAE/C,YAAf/W,eAAe,GAAqB;AAAE,iBAAO,KAAKgX,gBAAZ;AAA8B;;AAErD,YAAf/W,eAAe,GAAqB;AAAE,iBAAO,KAAKgX,gBAAZ;AAA8B;;AAExD,YAAZ/W,YAAY,GAAkB;AAAE,iBAAO,KAAKgX,aAAZ;AAA2B;;AAE7C,YAAd/W,cAAc,GAAoB;AAAE,iBAAO,KAAKgX,eAAZ;AAA6B;;AAEvD,YAAV/W,UAAU,GAAgB;AAAE,iBAAO,KAAKgX,WAAZ;AAAyB;;AAElC,YAAnB/W,mBAAmB,GAAyB;AAAE,iBAAO,KAAKgX,oBAAZ;AAAkC;;AAEpFpV,QAAAA,WAAW,CAACqV,MAAD,EAAqB;AAAA,eAjDxBxB,aAiDwB;AAAA,eA/CxBC,eA+CwB;AAAA,eA7CxBC,KA6CwB;AAAA,eA3CxBC,QA2CwB;AAAA,eAzCxBC,UAyCwB;AAAA,eAvCxBC,iBAuCwB;AAAA,eArCxBC,cAqCwB;AAAA,eAnCxBC,YAmCwB;AAAA,eAjCxBC,aAiCwB;AAAA,eA/BxBC,aA+BwB;AAAA,eA7BxBC,YA6BwB;AAAA,eA3BxBC,WA2BwB;AAAA,eAzBxBC,WAyBwB;AAAA,eAvBxBC,WAuBwB;AAAA,eArBxBC,UAqBwB;AAAA,eAnBxBC,WAmBwB;AAAA,eAjBxBC,UAiBwB;AAAA,eAfxBC,cAewB;AAAA,eAbxBC,gBAawB;AAAA,eAXxBC,gBAWwB;AAAA,eATxBC,aASwB;AAAA,eAPxBC,eAOwB;AAAA,eALxBC,WAKwB;AAAA,eAHxBC,oBAGwB;AAC5B,eAAKvB,aAAL,GAAqB,IAAIhX,YAAJ,CAAiBwY,MAAM,CAAC,cAAD,CAAvB,CAArB;AACA,eAAKvB,eAAL,GAAuB,IAAIhX,cAAJ,CAAmBuY,MAAM,CAAC,gBAAD,CAAzB,CAAvB;AACA,eAAKtB,KAAL,GAAa,IAAIhX,IAAJ,CAASsY,MAAM,CAAC,MAAD,CAAf,CAAb;AACA,eAAKrB,QAAL,GAAgB,IAAIhX,OAAJ,CAAYqY,MAAM,CAAC,SAAD,CAAlB,CAAhB;AACA,eAAKpB,UAAL,GAAkB,IAAIhX,SAAJ,CAAcoY,MAAM,CAAC,WAAD,CAApB,CAAlB;AACA,eAAKnB,iBAAL,GAAyB,IAAIhX,gBAAJ,CAAqBmY,MAAM,CAAC,kBAAD,CAA3B,CAAzB;AACA,eAAKlB,cAAL,GAAsB,IAAIhX,aAAJ,CAAkBkY,MAAM,CAAC,eAAD,CAAxB,CAAtB;AACA,eAAKjB,YAAL,GAAoB,IAAIhX,WAAJ,CAAgBiY,MAAM,CAAC,aAAD,CAAtB,CAApB;AACA,eAAKhB,aAAL,GAAqB,IAAIhX,YAAJ,CAAiBgY,MAAM,CAAC,cAAD,CAAvB,CAArB;AACA,eAAKf,aAAL,GAAqB,IAAIhX,YAAJ,CAAiB+X,MAAM,CAAC,cAAD,CAAvB,CAArB;AACA,eAAKd,YAAL,GAAoB,IAAIhX,WAAJ,CAAgB8X,MAAM,CAAC,aAAD,CAAtB,CAApB;AACA,eAAKb,WAAL,GAAmB,IAAIhX,UAAJ,CAAe6X,MAAM,CAAC,YAAD,CAArB,CAAnB;AACA,eAAKZ,WAAL,GAAmB,IAAIhX,UAAJ,CAAe4X,MAAM,CAAC,YAAD,CAArB,CAAnB;AACA,eAAKX,WAAL,GAAmB,IAAIhX,UAAJ,CAAe2X,MAAM,CAAC,YAAD,CAArB,CAAnB;AACA,eAAKV,UAAL,GAAkB,IAAIhX,SAAJ,CAAc0X,MAAM,CAAC,WAAD,CAApB,CAAlB;AACA,eAAKT,WAAL,GAAmB,IAAIhX,UAAJ,CAAeyX,MAAM,CAAC,YAAD,CAArB,CAAnB;AACA,eAAKR,UAAL,GAAkB,IAAIhX,SAAJ,CAAcwX,MAAM,CAAC,WAAD,CAApB,CAAlB;AACA,eAAKP,cAAL,GAAsB,IAAIhX,aAAJ,CAAkBuX,MAAM,CAAC,eAAD,CAAxB,CAAtB;AACA,eAAKN,gBAAL,GAAwB,IAAIhX,eAAJ,CAAoBsX,MAAM,CAAC,iBAAD,CAA1B,CAAxB;AACA,eAAKL,gBAAL,GAAwB,IAAIhX,eAAJ,CAAoBqX,MAAM,CAAC,iBAAD,CAA1B,CAAxB;AACA,eAAKJ,aAAL,GAAqB,IAAIhX,YAAJ,CAAiBoX,MAAM,CAAC,cAAD,CAAvB,CAArB;AACA,eAAKH,eAAL,GAAuB,IAAIhX,cAAJ,CAAmBmX,MAAM,CAAC,gBAAD,CAAzB,CAAvB;AACA,eAAKF,WAAL,GAAmB,IAAIhX,UAAJ,CAAekX,MAAM,CAAC,YAAD,CAArB,CAAnB;AACA,eAAKD,oBAAL,GAA4B,IAAIhX,mBAAJ,CAAwBiX,MAAM,CAAC,qBAAD,CAA9B,CAA5B;;AAEA,eAAKxB,aAAL,CAAmBvT,OAAnB,CAA2B,IAA3B;;AACA,eAAKwT,eAAL,CAAqBxT,OAArB,CAA6B,IAA7B;;AACA,eAAKyT,KAAL,CAAWzT,OAAX,CAAmB,IAAnB;;AACA,eAAK0T,QAAL,CAAc1T,OAAd,CAAsB,IAAtB;;AACA,eAAK2T,UAAL,CAAgB3T,OAAhB,CAAwB,IAAxB;;AACA,eAAK4T,iBAAL,CAAuB5T,OAAvB,CAA+B,IAA/B;;AACA,eAAK6T,cAAL,CAAoB7T,OAApB,CAA4B,IAA5B;;AACA,eAAK8T,YAAL,CAAkB9T,OAAlB,CAA0B,IAA1B;;AACA,eAAK+T,aAAL,CAAmB/T,OAAnB,CAA2B,IAA3B;;AACA,eAAKgU,aAAL,CAAmBhU,OAAnB,CAA2B,IAA3B;;AACA,eAAKiU,YAAL,CAAkBjU,OAAlB,CAA0B,IAA1B;;AACA,eAAKkU,WAAL,CAAiBlU,OAAjB,CAAyB,IAAzB;;AACA,eAAKmU,WAAL,CAAiBnU,OAAjB,CAAyB,IAAzB;;AACA,eAAKoU,WAAL,CAAiBpU,OAAjB,CAAyB,IAAzB;;AACA,eAAKqU,UAAL,CAAgBrU,OAAhB,CAAwB,IAAxB;;AACA,eAAKsU,WAAL,CAAiBtU,OAAjB,CAAyB,IAAzB;;AACA,eAAKuU,UAAL,CAAgBvU,OAAhB,CAAwB,IAAxB;;AACA,eAAKwU,cAAL,CAAoBxU,OAApB,CAA4B,IAA5B;;AACA,eAAKyU,gBAAL,CAAsBzU,OAAtB,CAA8B,IAA9B;;AACA,eAAK0U,gBAAL,CAAsB1U,OAAtB,CAA8B,IAA9B;;AACA,eAAK2U,aAAL,CAAmB3U,OAAnB,CAA2B,IAA3B;;AACA,eAAK4U,eAAL,CAAqB5U,OAArB,CAA6B,IAA7B;;AACA,eAAK6U,WAAL,CAAiB7U,OAAjB,CAAyB,IAAzB;;AACA,eAAK8U,oBAAL,CAA0B9U,OAA1B,CAAkC,IAAlC;AACH;;AApGe,O", "sourcesContent": ["\n//------------------------------------------------------------------------------\n// <auto-generated>\n//     This code was generated by a tool.\n//     Changes to this file may cause incorrect behavior and will be lost if\n//     the code is regenerated.\n// </auto-generated>\n//------------------------------------------------------------------------------\n\n\n \n/**\n * 特效绑定点\n */\nexport enum BindSocket {\n    /**\n     * 自己\n     */\n    Self = 0,\n    /**\n     * 屏幕\n     */\n    Scene = 1,\n}\n\n \n \n/**\n * 布尔类型\n */\nexport enum BoolOpType {\n    /**\n     * 与\n     */\n    AND = 0,\n    /**\n     * 或\n     */\n    OR = 1,\n}\n\n \n \n/**\n * buff类型\n */\nexport enum BuffType {\n    /**\n     * 正面\n     */\n    Positive = 0,\n    /**\n     * 中性\n     */\n    Neutral = 1,\n    /**\n     * 负面\n     */\n    Negative = 2,\n}\n\n \n \n/**\n * 子弹来源\n */\nexport enum BulletSourceType {\n    /**\n     * 自机\n     */\n    MAINPLANE = 0,\n    /**\n     * 僚机\n     */\n    WINGPLANE = 1,\n    /**\n     * 敌机\n     */\n    ENEMYPLANE = 2,\n}\n\n \n \n/**\n * 子弹类型\n */\nexport enum BulletType {\n    /**\n     * 常规\n     */\n    NORMAL = 0,\n    /**\n     * 核弹\n     */\n    NUCLEAR = 1,\n}\n\n \n \n/**\n * 条件比较类型\n */\nexport enum CondOPType {\n    /**\n     * 大于\n     */\n    GT = 0,\n    /**\n     * 等于\n     */\n    EQ = 1,\n    /**\n     * 小于\n     */\n    LT = 2,\n    /**\n     * 大等于\n     */\n    GE = 3,\n    /**\n     * 小等于\n     */\n    LE = 4,\n    /**\n     * 不等于\n     */\n    NE = 5,\n}\n\n \n \n/**\n * 伤害类型\n */\nexport enum DamageType {\n    /**\n     * 所有\n     */\n    ALL = 0,\n    /**\n     * 爆炸\n     */\n    EXPLOSIVE = 1,\n    /**\n     * 普通\n     */\n    NORMAL = 2,\n    /**\n     * 能量\n     */\n    ENERGETIC = 3,\n    /**\n     * 物理\n     */\n    PHYSICAL = 4,\n}\n\n \n \n/**\n * 效果类型\n */\nexport enum EffectType {\n    /**\n     * 最大生命值%\n     */\n    AttrMaxHPPer = 1,\n    /**\n     * 最大生命值+\n     */\n    AttrMaxHPAdd = 2,\n    /**\n     * 生命恢复速度%\n     */\n    AttrHPRecoveryPer = 3,\n    /**\n     * 生命恢复速度+\n     */\n    AttrHPRecoveryAdd = 4,\n    /**\n     * 基于最大生命值的恢复速度%\n     */\n    AttrHPRecoveryMaxHPPerAdd = 5,\n    /**\n     * 回复最大生命值%\n     */\n    HealMaxHPPer = 6,\n    /**\n     * 回复已损失生命%\n     */\n    HealLoseHPPer = 7,\n    /**\n     * 回复指定生命值+\n     */\n    HealHP = 8,\n    /**\n     * 攻击力%\n     */\n    AttrAttackPer = 9,\n    /**\n     * 攻击力+\n     */\n    AttrAttackAdd = 10,\n    /**\n     * 对Boss伤害%\n     */\n    AttrAttackBossPer = 11,\n    /**\n     * 对普通怪物伤害%\n     */\n    AttrAttackNormalPer = 12,\n    /**\n     * 幸运值%\n     */\n    AttrFortunatePer = 13,\n    /**\n     * 幸运值+\n     */\n    AttrFortunateAdd = 14,\n    /**\n     * 闪避率+\n     */\n    AttrMissAdd = 15,\n    /**\n     * 子弹伤害抗性%\n     */\n    AttrBulletHurtResistancePer = 16,\n    /**\n     * 子弹伤害抗性+\n     */\n    AttrBulletHurtResistanceAdd = 17,\n    /**\n     * 撞击伤害减免%\n     */\n    AttrCollisionHurtDerateAdd = 18,\n    /**\n     * 撞击伤害抗性%\n     */\n    AttrCollisionHurtResistancePer = 19,\n    /**\n     * 撞击伤害抗性+\n     */\n    AttrCollisionHurtResistanceAdd = 20,\n    /**\n     * 子弹伤害减免%\n     */\n    AttrBulletHurtDerateAdd = 21,\n    /**\n     * 结算得分%\n     */\n    AttrFinalScoreAdd = 22,\n    /**\n     * 击杀得分%\n     */\n    AttrKillScoreAdd = 23,\n    /**\n     * 能量回复%\n     */\n    AttrEnergyRecoveryPerAdd = 24,\n    /**\n     * 能量回复+\n     */\n    AttrEnergyRecoveryAdd = 25,\n    /**\n     * 拾取范围%\n     */\n    AttrPickRadiusPer = 26,\n    /**\n     * 拾取范围+\n     */\n    AttrPickRadiusAdd = 27,\n    /**\n     * 添加Buff\n     */\n    ApplyBuff = 28,\n    /**\n     * 免疫子弹伤害\n     */\n    ImmuneBulletHurt = 29,\n    /**\n     * 免疫撞击伤害\n     */\n    ImmuneCollisionHurt = 30,\n    /**\n     * 无视子弹\n     */\n    IgnoreBullet = 31,\n    /**\n     * 无视撞击\n     */\n    IgnoreCollision = 32,\n    /**\n     * 免疫核弹伤害\n     */\n    ImmuneNuclearHurt = 33,\n    /**\n     * 免疫主动技能\n     */\n    ImmuneActiveSkillHurt = 34,\n    /**\n     * 无敌\n     */\n    Invincible = 35,\n    /**\n     * 核弹携带数+\n     */\n    AttrNuclearMax = 36,\n    /**\n     * 子弹攻击+\n     */\n    AttrBulletAttackAdd = 37,\n    /**\n     * 子弹攻击%\n     */\n    AttrBulletAttackPer = 38,\n    /**\n     * 子弹伤害%\n     */\n    AttrBulletHurtFix = 39,\n    /**\n     * 基于最大生命值系数的伤害%\n     */\n    HurtMaxHPPer = 40,\n    /**\n     * 基于当前生命值系数的伤害%\n     */\n    HurtCurHPPer = 41,\n    /**\n     * 核弹攻击+\n     */\n    AttrNuclearAttackAdd = 42,\n    /**\n     * 核弹攻击%\n     */\n    AttrNuclearAttackPer = 43,\n    /**\n     * 核弹伤害%\n     */\n    AttrNuclearHurtFix = 44,\n    /**\n     * 发射子弹\n     */\n    FireBullet = 45,\n    /**\n     * 添加核弹\n     */\n    AddNuclear = 46,\n}\n\n \n \n/**\n * 装备部位\n */\nexport enum EquipClass {\n    /**\n     * 主武器仓\n     */\n    WEAPON = 1,\n    /**\n     * 战术模块\n     */\n    TACTICAL = 2,\n    /**\n     * 能源核心\n     */\n    POWER = 3,\n    /**\n     * 机身装甲\n     */\n    ARMOR = 4,\n    /**\n     * 动力引擎\n     */\n    ENGINE = 5,\n    /**\n     * 探测系统\n     */\n    DETECTION = 6,\n}\n\n \n \n/**\n * 功能模块\n */\nexport enum FunctionID {\n    /**\n     * 无\n     */\n    None = 0,\n    /**\n     * 商城\n     */\n    Store = 1,\n    /**\n     * 装备\n     */\n    Equip = 2,\n    /**\n     * 飞行执照\n     */\n    FlyID = 3,\n    /**\n     * PVP\n     */\n    Pvp = 4,\n}\n\n \n \n/**\n * GM命令页签\n */\nexport enum GMTabID {\n    /**\n     * 通用\n     */\n    COMMON = 0,\n    /**\n     * 战斗\n     */\n    BATTLE = 1,\n}\n\n \n \n/**\n * 商品分类\n */\nexport enum GoodsClass {\n    /**\n     * 普通商品\n     */\n    COMMON = 1,\n    /**\n     * 特惠商品\n     */\n    SPECIAE_OFFER = 2,\n    /**\n     * 宝箱\n     */\n    BOXES = 3,\n    /**\n     * 材料\n     */\n    MATERIALS = 4,\n    /**\n     * 月卡\n     */\n    CARDS = 5,\n    /**\n     * 不显示在商品列表，只在特定界面购买\n     */\n    SPECIAL = 100,\n}\n\n \n \n/**\n * 道具的使用效果\n */\nexport enum ItemEffectType {\n    /**\n     * 无效果\n     */\n    NONE = 0,\n    /**\n     * 使用后消耗道具，并掉落相应的ID的随机道具，参数1为掉落ID\n     */\n    GEN_LOOT = 1,\n    /**\n     * 使用后消耗道具，并掉落相应数量的金币，参数1为每个1道具对应多少个金币\n     */\n    GEN_GOLD = 2,\n    /**\n     * 使用后消耗道具，并掉落相应数量的钻石，参数1为每个1道具对应多少个钻石\n     */\n    GEN_DIAMOND = 3,\n    /**\n     * 使用后消耗道具，并掉落相应数量的经验，参数1为每个1道具对应多少经验值\n     */\n    GEN_XP = 4,\n    /**\n     * 使用后消耗道具，并掉落相应数量的体力，参数1为每个1道具对应多少体力值\n     */\n    GEN_ENERGY = 5,\n    /**\n     * 使用后消耗道具，并掉落相应数量的另一个道具，参数1为新道具ID，参数2为每个1道具对应多少个新道具\n     */\n    GEN_ITEM = 6,\n    /**\n     * 使用后消耗道具,并添加VIPCard到Role。VIPCard只能拥有一个，参数1为CardID\n     */\n    GEN_VIPCARD = 7,\n    /**\n     * 使用后消耗道具,解锁对应的战机。如果战机已经解锁，则不能重复解锁\n     */\n    UNLOCK_PLANE = 8,\n    /**\n     * 使用后消耗道具,并增加指定任务轨道值,参数1为轨道集ID,参数2为增加的值\n     */\n    GEN_TASK_VALUE = 9,\n    /**\n     * 使用后立刻完成指定的任务,如果指定的任务未解锁或者已经完成,则无效,同时也不消耗此道具.参数1为任务ID\n     */\n    COMPLETE_TASK = 10,\n    /**\n     * 使用后解锁任务特定的轨道,参数1为轨道集ID\n     */\n    UNLOCK_TASK_ORBIT = 11,\n}\n\n \n \n/**\n * 道具的使用类型\n */\nexport enum ItemUseType {\n    /**\n     * 不可直接从背包来使用的道具\n     */\n    NONE = 0,\n    /**\n     * 先放入背包内，然后由玩家从背包手动选择后使用\n     */\n    MANUAL = 1,\n    /**\n     * 当进入背包时立刻触发使用，并产生效果，一般不直接占用背包格子\n     */\n    AUTO = 2,\n}\n\n \n \n/**\n * 模式类型\n */\nexport enum ModeType {\n    /**\n     * 不要填这个\n     */\n    INVALID = 0,\n    /**\n     * 无尽\n     */\n    ENDLESS = 1,\n    /**\n     * 剧情\n     */\n    STORY = 2,\n    /**\n     * 远征\n     */\n    EXPEDITION = 3,\n    /**\n     * 无尽PK\n     */\n    ENDLESSPK = 4,\n    /**\n     * 好友PK\n     */\n    FRIENDPK = 5,\n}\n\n \n \n/**\n * 货币类型\n */\nexport enum MoneyType {\n    /**\n     * 一般不要填这个~\n     */\n    NONE = 0,\n    /**\n     * 金币\n     */\n    GOLD = 1,\n    /**\n     * 钻石\n     */\n    DIAMOND = 2,\n    /**\n     * ID为道具ID\n     */\n    ITEM = 3,\n    /**\n     * 直接支付人民币购买 - 需要接入支付系统后支持\n     */\n    DIRECT_BUY = 100,\n    /**\n     * 对于商城的商品，如果是免费，需要填充这个类型,其它类型不能填数量0\n     */\n    FREE_BUY = 101,\n}\n\n \n \n/**\n * 朝向类型\n */\nexport enum OrientationType {\n    /**\n     * 面朝移动方向\n     */\n    Path = 0,\n    /**\n     * 玩家\n     */\n    Player = 1,\n    /**\n     * 固定\n     */\n    Fixed = 2,\n}\n\n \n \n/**\n * 装备属性名称\n */\nexport enum PlanePropType {\n    /**\n     * 无\n     */\n    NONE = 0,\n    /**\n     * 最大生命+\n     */\n    MaxHP = 2,\n    /**\n     * 生命恢复速度+\n     */\n    HPRecovery = 3,\n    /**\n     * 攻击力+\n     */\n    Attack = 4,\n    /**\n     * 幸运值+\n     */\n    Fortunate = 5,\n    /**\n     * 闪避率+\n     */\n    Miss = 6,\n    /**\n     * 子弹伤害抗性+\n     */\n    BulletHurtResistance = 7,\n    /**\n     * 撞击伤害抗性+\n     */\n    CollisionHurtResistance = 8,\n    /**\n     * 拾取范围\n     */\n    PickRadius = 9,\n    /**\n     * 结算得分%\n     */\n    FinalScore = 10,\n    /**\n     * 核弹携带数+\n     */\n    NuclearMax = 11,\n    /**\n     * 最大能量+\n     */\n    MaxEnergy = 12,\n    /**\n     * 能量恢复速度+\n     */\n    EnergyRecovery = 13,\n    /**\n     * 所有子弹攻击力+\n     */\n    AllBulletAttack = 14,\n    /**\n     * 爆炸子弹攻击力+\n     */\n    ExplosiveBulletAttack = 15,\n    /**\n     * 普通子弹攻击力+\n     */\n    NormalBulletAttack = 16,\n    /**\n     * 能量子弹攻击力+\n     */\n    EnergeticBulletAttack = 17,\n    /**\n     * 物理子弹攻击力+\n     */\n    PhysicalBulletAttack = 18,\n}\n\n \n \n/**\n * 普通(Common)、精良(Uncommon)、稀有(Rare)、史诗(Epic)、传说(Legendary)、神话(Mythic)\n */\nexport enum QualityType {\n    /**\n     * 普通\n     */\n    COMMON = 1,\n    /**\n     * 精良\n     */\n    UNCOMMON = 2,\n    /**\n     * 稀有\n     */\n    RACE = 3,\n    /**\n     * 史诗\n     */\n    EPIC = 4,\n    /**\n     * 传说\n     */\n    LEGENDARY = 5,\n    /**\n     * 神话\n     */\n    MYTHIC = 6,\n}\n\n \n \n/**\n * 条件类型\n */\nexport enum ResCondType {\n    /**\n     * 可以不填，默认无条件限制\n     */\n    NONE = 0,\n    /**\n     * 等级达到指定值\n     */\n    LEVEL = 1,\n    /**\n     * 通过模式\n     */\n    MODE = 2,\n    /**\n     * 星级\n     */\n    STAR = 3,\n    /**\n     * 战力\n     */\n    FORCE = 4,\n    /**\n     * 指定 ID 的活动在开放时间段内\n     */\n    ACTIVITY = 5,\n    /**\n     * Role所在的区服的开服相对时间在指定段内,参数1~参数2\n     */\n    OPENAREA = 6,\n    /**\n     * Role创建时间（第一次登录）参数1~参数2\n     */\n    CREATE = 7,\n    /**\n     * 时间段\n     */\n    TIMESCOPE = 8,\n}\n\n \n \n/**\n * 目标类型\n */\nexport enum ResGoalType {\n    /**\n     * 空目标，忽略\n     */\n    NONE = 0,\n    /**\n     * 通过某模型大类达到指定次数/模式ID/次数\n     */\n    MODE_PASS_TIMES = 1,\n    /**\n     * 通过某指定关卡达到指定次数/关卡ID/次数\n     */\n    STAGE_PASS_TIMES = 2,\n    /**\n     * 消灭指定类型的怪物数量达到目标/怪物类型/数量\n     */\n    KILL_MONSTER_CLASS_COUNT = 3,\n    /**\n     * 商城指定抽奖操作达到指定次数/抽奖ID/次数\n     */\n    LOTTERY_TIMES = 4,\n    /**\n     * 领取挂机奖励达到指定次数/次数\n     */\n    AFK_TIMES = 5,\n    /**\n     * 充值达到指定金额/金额\n     */\n    CHARGE_AMOUNT = 6,\n    /**\n     * 购买指定商品（包括体力等）达到指定数量/商品ID/数量\n     */\n    BUY_ITEM_COUNT = 7,\n    /**\n     * 看广告达到指定次数/次数\n     */\n    WATCH_AD_TIMES = 8,\n    /**\n     * 累计登录达到指定天数/天数\n     */\n    LOGIN_DAYS = 9,\n    /**\n     * 玩家达到指定等级\n     */\n    ROLE_LEVEL = 10,\n    /**\n     * 消耗金币达到指定数额\n     */\n    CONSUME_GOLD = 11,\n    /**\n     * 消耗钻石达到指定数额\n     */\n    CONSUME_DIAMOND = 12,\n    /**\n     * 消耗体力达到指定数额\n     */\n    CONSUME_ENERGY = 13,\n    /**\n     * 消耗道具达到指定数额\n     */\n    CONSUME_ITEM = 14,\n    /**\n     * 拥有指定品质的装备达到指定数量\n     */\n    EQUIP_QUALITY = 15,\n    /**\n     * 拥有指定等级的装备达到指定数量\n     */\n    EQUIP_LEVEL = 16,\n    /**\n     * 执行装备合成达到指定次数\n     */\n    EQUIP_COMB_TIMES = 17,\n    /**\n     * 执行装备升级达到指定次数\n     */\n    EQUIP_UPGRADE_TIMES = 18,\n    /**\n     * 公会捐献达到指定次数\n     */\n    GUILD_DONATE_TIMES = 19,\n    /**\n     * 拥有战机（已解锁）达到指定数量\n     */\n    FIGHTER_UNLOCK_COUNT = 20,\n    /**\n     * 所有已解锁战机星级累计到指定数量\n     */\n    FIGHTER_STAR_TOTAL = 21,\n    /**\n     * 角色战力达到指定数值\n     */\n    ROLE_FORCE = 22,\n    /**\n     * 消耗指定金额的货币获得奖励\n     */\n    SPECIFY_BUG_CONSUME = 23,\n    /**\n     * 使用特定道具达到指定次数\n     */\n    USE_ITEM_TIMES = 24,\n    /**\n     * 执行签到操作，配置时参数1配置为签到的第几天(1-7，以接受任务的时间算起，当天第1天)\n     */\n    DAILY_SIGN_IN = 25,\n    /**\n     * 参与某模型大类达到指定次数/模式ID/次数\n     */\n    MODE_ENTER_TIMES = 26,\n    /**\n     * 参与某指定关卡达到指定次数/关卡ID/次数\n     */\n    STAGE_ENTER_TIMES = 27,\n    /**\n     * 任务达成后的累计轨道值达到指定数值/轨道ID/数值\n     */\n    ORBIT_VALUE = 28,\n}\n\n \n \n/**\n * 掉落类型\n */\nexport enum ResLootType {\n    /**\n     * 单个\n     */\n    SINGLE = 1,\n    /**\n     * 多个\n     */\n    MULTI = 2,\n}\n\n \n \n/**\n * 周期类型\n */\nexport enum ResPeriodType {\n    /**\n     * 不重置， 默认，可不填\n     */\n    PERMANENT = 0,\n    /**\n     * 每日 0时重置\n     */\n    DAILY = 1,\n    /**\n     * 每周 周一0时重置\n     */\n    WEEKLY = 2,\n    /**\n     * 每月 1日0时重置\n     */\n    MONTHLY = 3,\n}\n\n \n \n/**\n * 任务分类\n */\nexport enum ResTaskClass {\n    /**\n     * 默认，通过 ID 来识别\n     */\n    NONE = 0,\n    /**\n     * 日常任务\n     */\n    DAILY_TASK = 1,\n    /**\n     * 周常任务\n     */\n    WEEKLY_TASK = 2,\n    /**\n     * 月常任务\n     */\n    MONTHLY_TASK = 3,\n    /**\n     * 挑战任务\n     */\n    CHALLENGE = 4,\n    /**\n     * 活动\n     */\n    ACTIVITY = 5,\n    /**\n     * 任务轨道\n     */\n    TASK_ORBIT = 8,\n    /**\n     * 成就\n     */\n    ACHIEVEMENT = 9,\n}\n\n \n \n/**\n * 技能/buff条件\n */\nexport enum SkillConditionType {\n    /**\n     * 无条件\n     */\n    NONE = 0,\n    /**\n     * 比较Buff计数\n     */\n    BuffStack = 1,\n    /**\n     * 比较当前生命系数\n     */\n    CurHPPer = 2,\n    /**\n     * 比较当前生命值\n     */\n    CurHP = 3,\n    /**\n     * 比较最大生命值\n     */\n    MaxHP = 4,\n    /**\n     * 攻击承受时间\n     */\n    BeAttackTime = 5,\n    /**\n     * 是否受到致死伤害\n     */\n    FatalInjuryHurt = 6,\n    /**\n     * 拾取宝石数量\n     */\n    PickDiamond = 7,\n    /**\n     * 击杀数\n     */\n    KillEnemyNum = 8,\n    /**\n     * 剩余核弹数量\n     */\n    RemainNuclearNum = 9,\n    /**\n     * 已使用核弹数量\n     */\n    UsedNuclearNum = 10,\n    /**\n     * 关卡开始\n     */\n    LevelStart = 11,\n    /**\n     * 关卡Boss被击杀\n     */\n    BossBeKilled = 12,\n    /**\n     * 关卡结束\n     */\n    LevelEnd = 13,\n    /**\n     * 使用核弹\n     */\n    UseNuclear = 14,\n    /**\n     * 使用大招\n     */\n    UserSuper = 15,\n    /**\n     * 持续时间\n     */\n    GameTime = 16,\n    /**\n     * 范围内敌人数量\n     */\n    EnemyCount = 17,\n    /**\n     * 怪物波次进度\n     */\n    WaveNo = 18,\n    /**\n     * 与战机距离\n     */\n    Distance = 19,\n    /**\n     * 击杀敌人\n     */\n    KillEnemy = 20,\n}\n\n \n \n/**\n * 目标类型\n */\nexport enum TargetType {\n    /**\n     * 自己\n     */\n    Self = 0,\n    /**\n     * 自机\n     */\n    Main = 1,\n    /**\n     * 全部敌方\n     */\n    Enemy = 2,\n    /**\n     * Boss敌方\n     */\n    BossEnemy = 3,\n    /**\n     * 普通敌方\n     */\n    NormalEnemy = 4,\n    /**\n     * 自机友方\n     */\n    MainFriendly = 5,\n}\n\n \n\n\n\n\n\nexport class ApplyBuff {\n\n    constructor(_json_: any) {\n        if (_json_.target === undefined) { throw new Error() }\n        this.target = _json_.target\n        if (_json_.buffID === undefined) { throw new Error() }\n        this.buffID = _json_.buffID\n    }\n\n    readonly target: TargetType\n    readonly buffID: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\nexport namespace builtin {\nexport class vector2 {\n\n    constructor(_json_: any) {\n        if (_json_.x === undefined) { throw new Error() }\n        this.x = _json_.x\n        if (_json_.y === undefined) { throw new Error() }\n        this.y = _json_.y\n    }\n\n    readonly x: number\n    readonly y: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n}\n\n\nexport namespace builtin {\nexport class vector3 {\n\n    constructor(_json_: any) {\n        if (_json_.x === undefined) { throw new Error() }\n        this.x = _json_.x\n        if (_json_.y === undefined) { throw new Error() }\n        this.y = _json_.y\n        if (_json_.z === undefined) { throw new Error() }\n        this.z = _json_.z\n    }\n\n    readonly x: number\n    readonly y: number\n    readonly z: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n    }\n}\n\n}\n\n\nexport namespace builtin {\nexport class vector4 {\n\n    constructor(_json_: any) {\n        if (_json_.x === undefined) { throw new Error() }\n        this.x = _json_.x\n        if (_json_.y === undefined) { throw new Error() }\n        this.y = _json_.y\n        if (_json_.z === undefined) { throw new Error() }\n        this.z = _json_.z\n        if (_json_.w === undefined) { throw new Error() }\n        this.w = _json_.w\n    }\n\n    readonly x: number\n    readonly y: number\n    readonly z: number\n    readonly w: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n    }\n}\n\n}\n\n\n\nexport class ConParam {\n\n    constructor(_json_: any) {\n        if (_json_.con === undefined) { throw new Error() }\n        this.con = _json_.con\n        if (_json_.param === undefined) { throw new Error() }\n        this.param = _json_.param\n    }\n\n    readonly con: number\n    readonly param: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\n/**\n * 消耗的材料\n */\nexport class ConsumeItem {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.num === undefined) { throw new Error() }\n        this.num = _json_.num\n    }\n\n    readonly id: number\n    readonly num: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\nexport class ConsumeMoney {\n\n    constructor(_json_: any) {\n        if (_json_.type === undefined) { throw new Error() }\n        this.type = _json_.type\n        if (_json_.num === undefined) { throw new Error() }\n        this.num = _json_.num\n    }\n\n    /**\n     * 货币类型\n     */\n    readonly type: MoneyType\n    /**\n     * 货币数量\n     */\n    readonly num: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\nexport class EffectParam {\n\n    constructor(_json_: any) {\n        if (_json_.type === undefined) { throw new Error() }\n        this.type = _json_.type\n        if (_json_.target === undefined) { throw new Error() }\n        this.target = _json_.target\n        if (_json_.param === undefined) { throw new Error() }\n        { this.param = []; for(let _ele0 of _json_.param) { let _e0; _e0 = _ele0; this.param.push(_e0);}}\n    }\n\n    readonly type: EffectType\n    readonly target: TargetType\n    readonly param: number[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class GM {\n\n    constructor(_json_: any) {\n        if (_json_.tabID === undefined) { throw new Error() }\n        this.tabID = _json_.tabID\n        if (_json_.tabName === undefined) { throw new Error() }\n        this.tabName = _json_.tabName\n        if (_json_.name === undefined) { throw new Error() }\n        this.name = _json_.name\n        if (_json_.cmd === undefined) { throw new Error() }\n        this.cmd = _json_.cmd\n        if (_json_.desc === undefined) { throw new Error() }\n        this.desc = _json_.desc\n    }\n\n    /**\n     * 页签ID\n     */\n    readonly tabID: GMTabID\n    /**\n     * 页签名称\n     */\n    readonly tabName: string\n    /**\n     * 按钮名称\n     */\n    readonly name: string\n    /**\n     * 命令\n     */\n    readonly cmd: string\n    /**\n     * 描述\n     */\n    readonly desc: string\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\n/**\n * 战机效果\n */\nexport class PlaneEffect {\n\n    constructor(_json_: any) {\n        if (_json_.effect_id === undefined) { throw new Error() }\n        this.effectId = _json_.effect_id\n    }\n\n    readonly effectId: number\n\n    resolve(tables:Tables) {\n        \n    }\n}\n\n\n\n\n\n/**\n * 升星材料\n */\nexport class PlaneMaterial {\n\n    constructor(_json_: any) {\n        if (_json_.material_id === undefined) { throw new Error() }\n        this.materialId = _json_.material_id\n        if (_json_.material_count === undefined) { throw new Error() }\n        this.materialCount = _json_.material_count\n    }\n\n    readonly materialId: number\n    readonly materialCount: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\n/**\n * 战机属性\n */\nexport class PlaneProperty {\n\n    constructor(_json_: any) {\n        if (_json_.MaxHP === undefined) { throw new Error() }\n        this.MaxHP = _json_.MaxHP\n        if (_json_.HPRecovery === undefined) { throw new Error() }\n        this.HPRecovery = _json_.HPRecovery\n        if (_json_.Attack === undefined) { throw new Error() }\n        this.Attack = _json_.Attack\n        if (_json_.Fortunate === undefined) { throw new Error() }\n        this.Fortunate = _json_.Fortunate\n        if (_json_.Miss === undefined) { throw new Error() }\n        this.Miss = _json_.Miss\n        if (_json_.BulletHurtResistance === undefined) { throw new Error() }\n        this.BulletHurtResistance = _json_.BulletHurtResistance\n        if (_json_.CollisionHurtResistance === undefined) { throw new Error() }\n        this.CollisionHurtResistance = _json_.CollisionHurtResistance\n        if (_json_.PickRadius === undefined) { throw new Error() }\n        this.PickRadius = _json_.PickRadius\n        if (_json_.FinalScore === undefined) { throw new Error() }\n        this.FinalScore = _json_.FinalScore\n        if (_json_.NuclearMax === undefined) { throw new Error() }\n        this.NuclearMax = _json_.NuclearMax\n        if (_json_.MaxEnergy === undefined) { throw new Error() }\n        this.MaxEnergy = _json_.MaxEnergy\n        if (_json_.EnergyRecovery === undefined) { throw new Error() }\n        this.EnergyRecovery = _json_.EnergyRecovery\n    }\n\n    readonly MaxHP: number\n    readonly HPRecovery: number\n    readonly Attack: number\n    readonly Fortunate: number\n    readonly Miss: number\n    readonly BulletHurtResistance: number\n    readonly CollisionHurtResistance: number\n    readonly PickRadius: number\n    readonly FinalScore: number\n    readonly NuclearMax: number\n    readonly MaxEnergy: number\n    readonly EnergyRecovery: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\n/**\n * 战机属性\n */\nexport class PlanePropertyElem {\n\n    constructor(_json_: any) {\n        if (_json_.prop_type === undefined) { throw new Error() }\n        this.propType = _json_.prop_type\n        if (_json_.prop_param === undefined) { throw new Error() }\n        this.propParam = _json_.prop_param\n    }\n\n    readonly propType: PlanePropType\n    readonly propParam: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\n/**\n * 随机策略\n */\nexport class randStrategy {\n\n    constructor(_json_: any) {\n        if (_json_.ID === undefined) { throw new Error() }\n        this.ID = _json_.ID\n        if (_json_.Weight === undefined) { throw new Error() }\n        this.Weight = _json_.Weight\n    }\n\n    /**\n     * 随机策略ID\n     */\n    readonly ID: number\n    /**\n     * ID的权重\n     */\n    readonly Weight: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\nexport class RatingParam {\n\n    constructor(_json_: any) {\n        if (_json_.rating === undefined) { throw new Error() }\n        this.rating = _json_.rating\n        if (_json_.param === undefined) { throw new Error() }\n        this.param = _json_.param\n    }\n\n    readonly rating: number\n    readonly param: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\nexport class ResAchievement {\n\n    constructor(_json_: any) {\n        if (_json_.task_id === undefined) { throw new Error() }\n        this.taskId = _json_.task_id\n        if (_json_.group_id === undefined) { throw new Error() }\n        this.groupId = _json_.group_id\n        if (_json_.prev_id === undefined) { throw new Error() }\n        this.prevId = _json_.prev_id\n        if (_json_.period_type === undefined) { throw new Error() }\n        this.periodType = _json_.period_type\n        if (_json_.task_cond === undefined) { throw new Error() }\n        { this.taskCond = []; for(let _ele0 of _json_.task_cond) { let _e0; _e0 = new ResCondition(_ele0); this.taskCond.push(_e0);}}\n        if (_json_.task_goal === undefined) { throw new Error() }\n        this.taskGoal = new ResTaskGoal(_json_.task_goal)\n        if (_json_.accumulate === undefined) { throw new Error() }\n        this.accumulate = _json_.accumulate\n        if (_json_.reward_id === undefined) { throw new Error() }\n        this.rewardId = _json_.reward_id\n        if (_json_.link_to === undefined) { throw new Error() }\n        this.linkTo = _json_.link_to\n    }\n\n    /**\n     * 成就 ID\n     */\n    readonly taskId: number\n    /**\n     * 成就集 ID\n     */\n    readonly groupId: number\n    /**\n     * 前置ID\n     */\n    readonly prevId: number\n    /**\n     * 重置周期\n     */\n    readonly periodType: ResPeriodType\n    readonly taskCond: ResCondition[]\n    readonly taskGoal: ResTaskGoal\n    /**\n     * 是否累积\n     */\n    readonly accumulate: boolean\n    /**\n     * 奖励ID\n     */\n    readonly rewardId: number\n    /**\n     * 追踪\n     */\n    readonly linkTo: string\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        for (let _e of this.taskCond) { _e?.resolve(tables); }\n        this.taskGoal?.resolve(tables);\n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResActivity {\n\n    constructor(_json_: any) {\n        if (_json_.task_id === undefined) { throw new Error() }\n        this.taskId = _json_.task_id\n        if (_json_.group_id === undefined) { throw new Error() }\n        this.groupId = _json_.group_id\n        if (_json_.desc === undefined) { throw new Error() }\n        this.desc = _json_.desc\n        if (_json_.ui_res === undefined) { throw new Error() }\n        this.uiRes = _json_.ui_res\n        if (_json_.precv_id === undefined) { throw new Error() }\n        this.precvId = _json_.precv_id\n        if (_json_.period_type === undefined) { throw new Error() }\n        this.periodType = _json_.period_type\n        if (_json_.task_cond === undefined) { throw new Error() }\n        { this.taskCond = []; for(let _ele0 of _json_.task_cond) { let _e0; _e0 = new ResCondition(_ele0); this.taskCond.push(_e0);}}\n        if (_json_.task_goal === undefined) { throw new Error() }\n        this.taskGoal = new ResTaskGoal(_json_.task_goal)\n        if (_json_.accumlate === undefined) { throw new Error() }\n        this.accumlate = _json_.accumlate\n        if (_json_.reward_id === undefined) { throw new Error() }\n        this.rewardId = _json_.reward_id\n        if (_json_.open_date === undefined) { throw new Error() }\n        this.openDate = _json_.open_date\n        if (_json_.open_time === undefined) { throw new Error() }\n        this.openTime = _json_.open_time\n        if (_json_.close_date === undefined) { throw new Error() }\n        this.closeDate = _json_.close_date\n        if (_json_.close_time === undefined) { throw new Error() }\n        this.closeTime = _json_.close_time\n        if (_json_.link_to === undefined) { throw new Error() }\n        this.linkTo = _json_.link_to\n    }\n\n    /**\n     * 活动ID\n     */\n    readonly taskId: number\n    /**\n     * 活动集ID\n     */\n    readonly groupId: number\n    /**\n     * 说明\n     */\n    readonly desc: string\n    /**\n     * UI资源配置\n     */\n    readonly uiRes: string\n    /**\n     * 前置ID\n     */\n    readonly precvId: number\n    /**\n     * 重置周期\n     */\n    readonly periodType: ResPeriodType\n    readonly taskCond: ResCondition[]\n    readonly taskGoal: ResTaskGoal\n    /**\n     * 目标是否累积\n     */\n    readonly accumlate: boolean\n    /**\n     * 奖励ID\n     */\n    readonly rewardId: number\n    /**\n     * 开放日期\n     */\n    readonly openDate: string\n    /**\n     * 开放时间\n     */\n    readonly openTime: string\n    /**\n     * 结束日期\n     */\n    readonly closeDate: string\n    /**\n     * 结束时间\n     */\n    readonly closeTime: string\n    /**\n     * 活动追踪\n     */\n    readonly linkTo: string\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        for (let _e of this.taskCond) { _e?.resolve(tables); }\n        this.taskGoal?.resolve(tables);\n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResBuffer {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.buffType === undefined) { throw new Error() }\n        this.buffType = _json_.buffType\n        if (_json_.triggerCondition === undefined) { throw new Error() }\n        this.triggerCondition = _json_.triggerCondition\n        if (_json_.forbinCondition === undefined) { throw new Error() }\n        this.forbinCondition = _json_.forbinCondition\n        if (_json_.removeCondition === undefined) { throw new Error() }\n        this.removeCondition = _json_.removeCondition\n        if (_json_.duration === undefined) { throw new Error() }\n        this.duration = _json_.duration\n        if (_json_.durationBonus === undefined) { throw new Error() }\n        this.durationBonus = _json_.durationBonus\n        if (_json_.maxStack === undefined) { throw new Error() }\n        this.maxStack = _json_.maxStack\n        if (_json_.refreshType === undefined) { throw new Error() }\n        this.refreshType = _json_.refreshType\n        if (_json_.cycle === undefined) { throw new Error() }\n        this.cycle = _json_.cycle\n        if (_json_.cycleTimes === undefined) { throw new Error() }\n        this.cycleTimes = _json_.cycleTimes\n        if (_json_.EffectPath === undefined) { throw new Error() }\n        this.EffectPath = _json_.EffectPath\n        if (_json_.EffectSocket === undefined) { throw new Error() }\n        this.EffectSocket = _json_.EffectSocket\n        if (_json_.EffectPos === undefined) { throw new Error() }\n        this.EffectPos = new builtin.vector2(_json_.EffectPos)\n        if (_json_.effects === undefined) { throw new Error() }\n        { this.effects = []; for(let _ele0 of _json_.effects) { let _e0; _e0 = new EffectParam(_ele0); this.effects.push(_e0);}}\n    }\n\n    /**\n     * ID\n     */\n    readonly id: number\n    /**\n     * 类别\n     */\n    readonly buffType: BuffType\n    /**\n     * Buff触发条件\n     */\n    readonly triggerCondition: number\n    /**\n     * 禁用条件\n     */\n    readonly forbinCondition: number\n    /**\n     * 移除条件\n     */\n    readonly removeCondition: number\n    /**\n     * 持续时间\n     */\n    readonly duration: number\n    /**\n     * 持续时间加成\n     */\n    readonly durationBonus: number\n    /**\n     * 最大叠加次数\n     */\n    readonly maxStack: number\n    /**\n     * 叠加刷新策略\n     */\n    readonly refreshType: boolean\n    /**\n     * 周期\n     */\n    readonly cycle: number\n    /**\n     * 周期计数\n     */\n    readonly cycleTimes: number\n    /**\n     * 特效Prefab\n     */\n    readonly EffectPath: string\n    /**\n     * 特效挂点\n     */\n    readonly EffectSocket: BindSocket\n    /**\n     * 特效偏移\n     */\n    readonly EffectPos: builtin.vector2\n    readonly effects: EffectParam[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResChapter {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.levelCount === undefined) { throw new Error() }\n        this.levelCount = _json_.levelCount\n        if (_json_.levelGroupCount === undefined) { throw new Error() }\n        this.levelGroupCount = _json_.levelGroupCount\n        if (_json_.strategy === undefined) { throw new Error() }\n        this.strategy = _json_.strategy\n        if (_json_.chapterTitle === undefined) { throw new Error() }\n        this.chapterTitle = _json_.chapterTitle\n        if (_json_.bossName === undefined) { throw new Error() }\n        this.bossName = _json_.bossName\n        if (_json_.enemyFaction === undefined) { throw new Error() }\n        this.enemyFaction = _json_.enemyFaction\n        if (_json_.levelDesc === undefined) { throw new Error() }\n        this.levelDesc = _json_.levelDesc\n        if (_json_.combatRequire === undefined) { throw new Error() }\n        this.combatRequire = _json_.combatRequire\n        if (_json_.strategyList === undefined) { throw new Error() }\n        { this.strategyList = []; for(let _ele0 of _json_.strategyList) { let _e0; _e0 = new randStrategy(_ele0); this.strategyList.push(_e0);}}\n    }\n\n    /**\n     * 章节ID\n     */\n    readonly id: number\n    /**\n     * 章节关卡数量\n     */\n    readonly levelCount: number\n    /**\n     * 章节关卡组数量\n     */\n    readonly levelGroupCount: number\n    /**\n     * 1=随机<br/>2=随机不重复<br/>3=顺序重复\n     */\n    readonly strategy: number\n    /**\n     * 过场章节名\n     */\n    readonly chapterTitle: string\n    /**\n     * 过场Boss名称\n     */\n    readonly bossName: string\n    /**\n     * 过场反派派别\n     */\n    readonly enemyFaction: string\n    /**\n     * 过场关卡介绍\n     */\n    readonly levelDesc: string\n    /**\n     * 战斗力考核要求\n     */\n    readonly combatRequire: number\n    readonly strategyList: randStrategy[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        for (let _e of this.strategyList) { _e?.resolve(tables); }\n    }\n}\n\n\n\n\n\n/**\n * 条件项\n */\nexport class ResCondition {\n\n    constructor(_json_: any) {\n        if (_json_.cond_type === undefined) { throw new Error() }\n        this.condType = _json_.cond_type\n        if (_json_.params === undefined) { throw new Error() }\n        { this.params = []; for(let _ele0 of _json_.params) { let _e0; _e0 = _ele0; this.params.push(_e0);}}\n    }\n\n    readonly condType: ResCondType\n    readonly params: number[]\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\nexport class ResEffect {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.name === undefined) { throw new Error() }\n        this.name = _json_.name\n        if (_json_.description === undefined) { throw new Error() }\n        this.description = _json_.description\n        if (_json_.icon === undefined) { throw new Error() }\n        this.icon = _json_.icon\n        if (_json_.effect_type === undefined) { throw new Error() }\n        this.effectType = _json_.effect_type\n        if (_json_.effect_value === undefined) { throw new Error() }\n        this.effectValue = _json_.effect_value\n        if (_json_.effect_params === undefined) { throw new Error() }\n        this.effectParams = _json_.effect_params\n    }\n\n    /**\n     * 效果ID\n     */\n    readonly id: number\n    /**\n     * 效果名称\n     */\n    readonly name: string\n    /**\n     * 效果描述\n     */\n    readonly description: string\n    /**\n     * 效果图标\n     */\n    readonly icon: string\n    /**\n     * 效果类型\n     */\n    readonly effectType: number\n    /**\n     * 效果数值\n     */\n    readonly effectValue: number\n    /**\n     * 效果参数\n     */\n    readonly effectParams: string\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResEmitter {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.name === undefined) { throw new Error() }\n        this.name = _json_.name\n        if (_json_.source === undefined) { throw new Error() }\n        this.source = _json_.source\n        if (_json_.type === undefined) { throw new Error() }\n        this.type = _json_.type\n        if (_json_.damage_type === undefined) { throw new Error() }\n        this.damageType = _json_.damage_type\n        if (_json_.prefab === undefined) { throw new Error() }\n        this.prefab = _json_.prefab\n        if (_json_.attack_coefficient === undefined) { throw new Error() }\n        this.attackCoefficient = _json_.attack_coefficient\n        if (_json_.penetration_count === undefined) { throw new Error() }\n        this.penetrationCount = _json_.penetration_count\n        if (_json_.penetration_cooldown === undefined) { throw new Error() }\n        this.penetrationCooldown = _json_.penetration_cooldown\n        if (_json_.hit_bounce_count === undefined) { throw new Error() }\n        this.hitBounceCount = _json_.hit_bounce_count\n        if (_json_.boundary_bounce_count === undefined) { throw new Error() }\n        this.boundaryBounceCount = _json_.boundary_bounce_count\n    }\n\n    /**\n     * ID\n     */\n    readonly id: number\n    /**\n     * 名称\n     */\n    readonly name: string\n    /**\n     * 子弹来源\n     */\n    readonly source: BulletSourceType\n    /**\n     * 子弹类型\n     */\n    readonly type: BulletType\n    /**\n     * 子弹伤害类型\n     */\n    readonly damageType: DamageType\n    /**\n     * 发射器Prefab\n     */\n    readonly prefab: string\n    /**\n     * 攻击转换系数%\n     */\n    readonly attackCoefficient: number\n    /**\n     * 穿透次数\n     */\n    readonly penetrationCount: number\n    /**\n     * 穿透伤害冷却\n     */\n    readonly penetrationCooldown: number\n    /**\n     * 命中反弹次数\n     */\n    readonly hitBounceCount: number\n    /**\n     * 屏幕反弹次数\n     */\n    readonly boundaryBounceCount: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResEnemy {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.name === undefined) { throw new Error() }\n        this.name = _json_.name\n        if (_json_.comment === undefined) { throw new Error() }\n        this.comment = _json_.comment\n        if (_json_.prefab === undefined) { throw new Error() }\n        this.prefab = _json_.prefab\n        if (_json_.rank === undefined) { throw new Error() }\n        this.rank = _json_.rank\n        if (_json_.purpose === undefined) { throw new Error() }\n        this.purpose = _json_.purpose\n        if (_json_.base_hp === undefined) { throw new Error() }\n        this.baseHp = _json_.base_hp\n        if (_json_.base_atk === undefined) { throw new Error() }\n        this.baseAtk = _json_.base_atk\n        if (_json_.move_speed === undefined) { throw new Error() }\n        this.moveSpeed = _json_.move_speed\n        if (_json_.turn_speed === undefined) { throw new Error() }\n        this.turnSpeed = _json_.turn_speed\n        if (_json_.orientation === undefined) { throw new Error() }\n        this.orientation = _json_.orientation\n        if (_json_.kill_score === undefined) { throw new Error() }\n        this.killScore = _json_.kill_score\n        if (_json_.show_hp_bar === undefined) { throw new Error() }\n        this.showHpBar = _json_.show_hp_bar\n        if (_json_.use_hit_count === undefined) { throw new Error() }\n        this.useHitCount = _json_.use_hit_count\n        if (_json_.hit_count_to_kill === undefined) { throw new Error() }\n        this.hitCountToKill = _json_.hit_count_to_kill\n        if (_json_.hit_count_interval === undefined) { throw new Error() }\n        this.hitCountInterval = _json_.hit_count_interval\n        if (_json_.target_priority === undefined) { throw new Error() }\n        this.targetPriority = _json_.target_priority\n        if (_json_.immune_bullet_damage === undefined) { throw new Error() }\n        this.immuneBulletDamage = _json_.immune_bullet_damage\n        if (_json_.immune_collide_damage === undefined) { throw new Error() }\n        this.immuneCollideDamage = _json_.immune_collide_damage\n        if (_json_.ignore_bullet === undefined) { throw new Error() }\n        this.ignoreBullet = _json_.ignore_bullet\n        if (_json_.ignore_collide === undefined) { throw new Error() }\n        this.ignoreCollide = _json_.ignore_collide\n        if (_json_.immune_nuke === undefined) { throw new Error() }\n        this.immuneNuke = _json_.immune_nuke\n        if (_json_.immune_active_skill === undefined) { throw new Error() }\n        this.immuneActiveSkill = _json_.immune_active_skill\n        if (_json_.invincible === undefined) { throw new Error() }\n        this.invincible = _json_.invincible\n        if (_json_.collide_level === undefined) { throw new Error() }\n        this.collideLevel = _json_.collide_level\n        if (_json_.collide_damage === undefined) { throw new Error() }\n        this.collideDamage = _json_.collide_damage\n        if (_json_.drop_radius === undefined) { throw new Error() }\n        this.dropRadius = _json_.drop_radius\n    }\n\n    /**\n     * 单位ID\n     */\n    readonly id: number\n    /**\n     * 单位名称\n     */\n    readonly name: string\n    /**\n     * 备注\n     */\n    readonly comment: string\n    /**\n     * 单位Prefab\n     */\n    readonly prefab: string\n    /**\n     * 单位级别\n     */\n    readonly rank: number\n    /**\n     * 单位用途\n     */\n    readonly purpose: string\n    /**\n     * 基础生命值\n     */\n    readonly baseHp: number\n    /**\n     * 基础攻击力\n     */\n    readonly baseAtk: number\n    /**\n     * 移动速度\n     */\n    readonly moveSpeed: number\n    /**\n     * 转向速度\n     */\n    readonly turnSpeed: number\n    /**\n     * 朝向类型\n     */\n    readonly orientation: OrientationType\n    /**\n     * 击杀得分\n     */\n    readonly killScore: number\n    /**\n     * 是否显示血条\n     */\n    readonly showHpBar: boolean\n    /**\n     * 是否开启受击次数\n     */\n    readonly useHitCount: boolean\n    /**\n     * 受击次数\n     */\n    readonly hitCountToKill: number\n    /**\n     * 受击间隔\n     */\n    readonly hitCountInterval: number\n    /**\n     * 选中级别\n     */\n    readonly targetPriority: number\n    /**\n     * 免疫子弹伤害\n     */\n    readonly immuneBulletDamage: boolean\n    /**\n     * 免疫撞击伤害\n     */\n    readonly immuneCollideDamage: boolean\n    /**\n     * 无视子弹\n     */\n    readonly ignoreBullet: boolean\n    /**\n     * 无视撞击\n     */\n    readonly ignoreCollide: boolean\n    /**\n     * 是否免疫核弹\n     */\n    readonly immuneNuke: boolean\n    /**\n     * 是否免疫主动技能\n     */\n    readonly immuneActiveSkill: boolean\n    /**\n     * 是否无敌\n     */\n    readonly invincible: boolean\n    /**\n     * 相撞等级\n     */\n    readonly collideLevel: number\n    /**\n     * 相撞伤害\n     */\n    readonly collideDamage: number\n    /**\n     * 掉落半径\n     */\n    readonly dropRadius: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResEquip {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.name === undefined) { throw new Error() }\n        this.name = _json_.name\n        if (_json_.icon === undefined) { throw new Error() }\n        this.icon = _json_.icon\n        if (_json_.quality === undefined) { throw new Error() }\n        this.quality = _json_.quality\n        if (_json_.quality_sub === undefined) { throw new Error() }\n        this.qualitySub = _json_.quality_sub\n        if (_json_.equip_class === undefined) { throw new Error() }\n        this.equipClass = _json_.equip_class\n        if (_json_.props === undefined) { throw new Error() }\n        { this.props = []; for(let _ele0 of _json_.props) { let _e0; _e0 = new PlanePropertyElem(_ele0); this.props.push(_e0);}}\n        if (_json_.consume_items === undefined) { throw new Error() }\n        { this.consumeItems = []; for(let _ele0 of _json_.consume_items) { let _e0; _e0 = new ConsumeItem(_ele0); this.consumeItems.push(_e0);}}\n    }\n\n    /**\n     * ID\n     */\n    readonly id: number\n    /**\n     * 名称\n     */\n    readonly name: string\n    /**\n     * 图标\n     */\n    readonly icon: string\n    /**\n     * 品质\n     */\n    readonly quality: QualityType\n    /**\n     * 品质子等级\n     */\n    readonly qualitySub: number\n    /**\n     * 装备部位\n     */\n    readonly equipClass: EquipClass\n    /**\n     * 属性\n     */\n    readonly props: PlanePropertyElem[]\n    /**\n     * 消耗材料\n     */\n    readonly consumeItems: ConsumeItem[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        for (let _e of this.props) { _e?.resolve(tables); }\n        for (let _e of this.consumeItems) { _e?.resolve(tables); }\n    }\n}\n\n\n\n\n\nexport class ResEquipUpgrade {\n\n    constructor(_json_: any) {\n        if (_json_.equip_class === undefined) { throw new Error() }\n        this.equipClass = _json_.equip_class\n        if (_json_.level_from === undefined) { throw new Error() }\n        this.levelFrom = _json_.level_from\n        if (_json_.level_to === undefined) { throw new Error() }\n        this.levelTo = _json_.level_to\n        if (_json_.prop_inc === undefined) { throw new Error() }\n        { this.propInc = []; for(let _ele0 of _json_.prop_inc) { let _e0; _e0 = new PlanePropertyElem(_ele0); this.propInc.push(_e0);}}\n        if (_json_.consume_money === undefined) { throw new Error() }\n        this.consumeMoney = new ConsumeMoney(_json_.consume_money)\n        if (_json_.consume_items === undefined) { throw new Error() }\n        { this.consumeItems = []; for(let _ele0 of _json_.consume_items) { let _e0; _e0 = new ConsumeItem(_ele0); this.consumeItems.push(_e0);}}\n    }\n\n    /**\n     * 部位\n     */\n    readonly equipClass: EquipClass\n    /**\n     * 等级下限\n     */\n    readonly levelFrom: number\n    /**\n     * 等级上限\n     */\n    readonly levelTo: number\n    /**\n     * 属性增幅\n     */\n    readonly propInc: PlanePropertyElem[]\n    /**\n     * 消耗货币\n     */\n    readonly consumeMoney: ConsumeMoney\n    /**\n     * 消耗材料\n     */\n    readonly consumeItems: ConsumeItem[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        for (let _e of this.propInc) { _e?.resolve(tables); }\n        this.consumeMoney?.resolve(tables);\n        for (let _e of this.consumeItems) { _e?.resolve(tables); }\n    }\n}\n\n\n\n\n\nexport class ResGameMode {\n\n    constructor(_json_: any) {\n        if (_json_.modeID === undefined) { throw new Error() }\n        this.modeID = _json_.modeID\n        if (_json_.modeType === undefined) { throw new Error() }\n        this.modeType = _json_.modeType\n        if (_json_.chapterID === undefined) { throw new Error() }\n        this.chapterID = _json_.chapterID\n        if (_json_.order === undefined) { throw new Error() }\n        this.order = _json_.order\n        if (_json_.resourceID === undefined) { throw new Error() }\n        this.resourceID = _json_.resourceID\n        if (_json_.description === undefined) { throw new Error() }\n        this.description = _json_.description\n        if (_json_.conList === undefined) { throw new Error() }\n        { this.conList = []; for(let _ele0 of _json_.conList) { let _e0; _e0 = new ResCondition(_ele0); this.conList.push(_e0);}}\n        if (_json_.periodType === undefined) { throw new Error() }\n        this.periodType = _json_.periodType\n        if (_json_.times === undefined) { throw new Error() }\n        this.times = _json_.times\n        if (_json_.costEnergy === undefined) { throw new Error() }\n        this.costEnergy = _json_.costEnergy\n        if (_json_.monType === undefined) { throw new Error() }\n        this.monType = _json_.monType\n        if (_json_.costParam1 === undefined) { throw new Error() }\n        this.costParam1 = _json_.costParam1\n        if (_json_.costParam2 === undefined) { throw new Error() }\n        this.costParam2 = _json_.costParam2\n        if (_json_.rebirthTimes === undefined) { throw new Error() }\n        this.rebirthTimes = _json_.rebirthTimes\n        if (_json_.rebirthCost === undefined) { throw new Error() }\n        this.rebirthCost = _json_.rebirthCost\n        if (_json_.power === undefined) { throw new Error() }\n        this.power = _json_.power\n        if (_json_.rogueID === undefined) { throw new Error() }\n        this.rogueID = _json_.rogueID\n        if (_json_.LevelLimit === undefined) { throw new Error() }\n        this.LevelLimit = _json_.LevelLimit\n        if (_json_.rogueFirst === undefined) { throw new Error() }\n        this.rogueFirst = _json_.rogueFirst\n        if (_json_.sweepLimit === undefined) { throw new Error() }\n        this.sweepLimit = _json_.sweepLimit\n        if (_json_.rewardID === undefined) { throw new Error() }\n        this.rewardID = _json_.rewardID\n        if (_json_.ratingList === undefined) { throw new Error() }\n        { this.ratingList = []; for(let _ele0 of _json_.ratingList) { let _e0; _e0 = new RatingParam(_ele0); this.ratingList.push(_e0);}}\n    }\n\n    /**\n     * ID\n     */\n    readonly modeID: number\n    /**\n     * 模式类型\n     */\n    readonly modeType: ModeType\n    /**\n     * 章节ID\n     */\n    readonly chapterID: number\n    /**\n     * 排序\n     */\n    readonly order: number\n    /**\n     * 入口资源\n     */\n    readonly resourceID: number\n    /**\n     * 文本介绍\n     */\n    readonly description: string\n    readonly conList: ResCondition[]\n    /**\n     * 进入周期\n     */\n    readonly periodType: ResPeriodType\n    /**\n     * 进入次数\n     */\n    readonly times: number\n    /**\n     * 消耗体力\n     */\n    readonly costEnergy: number\n    /**\n     * 消耗类型\n     */\n    readonly monType: MoneyType\n    /**\n     * 消耗参数1\n     */\n    readonly costParam1: number\n    /**\n     * 消耗参数2\n     */\n    readonly costParam2: number\n    /**\n     * 复活次数\n     */\n    readonly rebirthTimes: number\n    /**\n     * 复活消耗\n     */\n    readonly rebirthCost: number\n    /**\n     * 战力评估\n     */\n    readonly power: number\n    /**\n     * 肉鸽组\n     */\n    readonly rogueID: number\n    /**\n     * 局内等级上限\n     */\n    readonly LevelLimit: number\n    /**\n     * 初始肉鸽选择\n     */\n    readonly rogueFirst: number\n    /**\n     * 扫荡次数\n     */\n    readonly sweepLimit: number\n    /**\n     * 奖励ID\n     */\n    readonly rewardID: number\n    readonly ratingList: RatingParam[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        for (let _e of this.conList) { _e?.resolve(tables); }\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResGlobalAttr {\n\n    constructor(_json_: any) {\n        if (_json_.GoldProducion === undefined) { throw new Error() }\n        this.GoldProducion = _json_.GoldProducion\n        if (_json_.MaxEnergy === undefined) { throw new Error() }\n        this.MaxEnergy = _json_.MaxEnergy\n        if (_json_.EnergyRecoverInterval === undefined) { throw new Error() }\n        this.EnergyRecoverInterval = _json_.EnergyRecoverInterval\n        if (_json_.EnergyRecoverValue === undefined) { throw new Error() }\n        this.EnergyRecoverValue = _json_.EnergyRecoverValue\n        if (_json_.ItemPickUpRadius === undefined) { throw new Error() }\n        this.ItemPickUpRadius = _json_.ItemPickUpRadius\n        if (_json_.PostHitProtection === undefined) { throw new Error() }\n        this.PostHitProtection = _json_.PostHitProtection\n        if (_json_.CameraTranslationMaxMoveSpeed === undefined) { throw new Error() }\n        this.CameraTranslationMaxMoveSpeed = _json_.CameraTranslationMaxMoveSpeed\n        if (_json_.CameraTranslationMoveDelay === undefined) { throw new Error() }\n        this.CameraTranslationMoveDelay = _json_.CameraTranslationMoveDelay\n        if (_json_.MaxEndlessDoubleRewardTimes === undefined) { throw new Error() }\n        this.MaxEndlessDoubleRewardTimes = _json_.MaxEndlessDoubleRewardTimes\n        if (_json_.MaxStoryDoubleRewardTimes === undefined) { throw new Error() }\n        this.MaxStoryDoubleRewardTimes = _json_.MaxStoryDoubleRewardTimes\n        if (_json_.MaxMailKeepDays === undefined) { throw new Error() }\n        this.MaxMailKeepDays = _json_.MaxMailKeepDays\n    }\n\n    /**\n     * 每回合发放的金币\n     */\n    readonly GoldProducion: number\n    /**\n     * 体力上限值\n     */\n    readonly MaxEnergy: number\n    /**\n     * 体力恢复的间隔时间\n     */\n    readonly EnergyRecoverInterval: number\n    /**\n     * 体力恢复的值\n     */\n    readonly EnergyRecoverValue: number\n    /**\n     * 局内道具拾取距离\n     */\n    readonly ItemPickUpRadius: number\n    /**\n     * 受击保护\n     */\n    readonly PostHitProtection: number\n    /**\n     * 镜头平移最大跟随速度\n     */\n    readonly CameraTranslationMaxMoveSpeed: number\n    /**\n     * 镜头平移启动延迟\n     */\n    readonly CameraTranslationMoveDelay: number\n    /**\n     * 无尽模式的最大双倍奖励每日领取次数\n     */\n    readonly MaxEndlessDoubleRewardTimes: number\n    /**\n     * 剧情模式的最大双倍奖励每日领取次数\n     */\n    readonly MaxStoryDoubleRewardTimes: number\n    /**\n     * 邮件最大保留时长，超过此时间的邮件自动清理\n     */\n    readonly MaxMailKeepDays: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResItem {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.name === undefined) { throw new Error() }\n        this.name = _json_.name\n        if (_json_.icon === undefined) { throw new Error() }\n        this.icon = _json_.icon\n        if (_json_.quality === undefined) { throw new Error() }\n        this.quality = _json_.quality\n        if (_json_.quality_sub === undefined) { throw new Error() }\n        this.qualitySub = _json_.quality_sub\n        if (_json_.use_type === undefined) { throw new Error() }\n        this.useType = _json_.use_type\n        if (_json_.effect_id === undefined) { throw new Error() }\n        this.effectId = _json_.effect_id\n        if (_json_.effect_param1 === undefined) { throw new Error() }\n        this.effectParam1 = _json_.effect_param1\n        if (_json_.effect_param2 === undefined) { throw new Error() }\n        this.effectParam2 = _json_.effect_param2\n        if (_json_.max_stack_num === undefined) { throw new Error() }\n        this.maxStackNum = _json_.max_stack_num\n    }\n\n    /**\n     * ID\n     */\n    readonly id: number\n    /**\n     * 名称\n     */\n    readonly name: string\n    /**\n     * 图标\n     */\n    readonly icon: string\n    /**\n     * 品质\n     */\n    readonly quality: QualityType\n    /**\n     * 品质子等级\n     */\n    readonly qualitySub: number\n    /**\n     * 使用类型\n     */\n    readonly useType: ItemUseType\n    /**\n     * 效果类型\n     */\n    readonly effectId: ItemEffectType\n    /**\n     * 效果参数1\n     */\n    readonly effectParam1: number\n    /**\n     * 效果参数2\n     */\n    readonly effectParam2: number\n    /**\n     * 最大叠放数量\n     */\n    readonly maxStackNum: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResLevel {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.prefab === undefined) { throw new Error() }\n        this.prefab = _json_.prefab\n        if (_json_.forbidFire === undefined) { throw new Error() }\n        this.forbidFire = _json_.forbidFire\n        if (_json_.forbidNBomb === undefined) { throw new Error() }\n        this.forbidNBomb = _json_.forbidNBomb\n        if (_json_.forbidActSkill === undefined) { throw new Error() }\n        this.forbidActSkill = _json_.forbidActSkill\n        if (_json_.planeCollisionScaling === undefined) { throw new Error() }\n        this.planeCollisionScaling = _json_.planeCollisionScaling\n        if (_json_.levelType === undefined) { throw new Error() }\n        this.levelType = _json_.levelType\n    }\n\n    /**\n     * 关卡ID\n     */\n    readonly id: number\n    /**\n     * 关卡Prefab\n     */\n    readonly prefab: string\n    /**\n     * 禁止发射子弹\n     */\n    readonly forbidFire: boolean\n    /**\n     * 战机碰撞缩放比例\n     */\n    readonly forbidNBomb: boolean\n    /**\n     * 禁用核弹\n     */\n    readonly forbidActSkill: boolean\n    /**\n     * 禁用主动技能\n     */\n    readonly planeCollisionScaling: boolean\n    /**\n     * 关卡类型\n     */\n    readonly levelType: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResLevelGroup {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.normLevelCount === undefined) { throw new Error() }\n        this.normLevelCount = _json_.normLevelCount\n        if (_json_.normLevelST === undefined) { throw new Error() }\n        this.normLevelST = _json_.normLevelST\n        if (_json_.normSTList === undefined) { throw new Error() }\n        { this.normSTList = []; for(let _ele0 of _json_.normSTList) { let _e0; _e0 = new randStrategy(_ele0); this.normSTList.push(_e0);}}\n        if (_json_.bossLevelCount === undefined) { throw new Error() }\n        this.bossLevelCount = _json_.bossLevelCount\n        if (_json_.bossLevelST === undefined) { throw new Error() }\n        this.bossLevelST = _json_.bossLevelST\n        if (_json_.bossSTList === undefined) { throw new Error() }\n        { this.bossSTList = []; for(let _ele0 of _json_.bossSTList) { let _e0; _e0 = new randStrategy(_ele0); this.bossSTList.push(_e0);}}\n    }\n\n    /**\n     * 关卡组ID\n     */\n    readonly id: number\n    /**\n     * 常规关卡数量\n     */\n    readonly normLevelCount: number\n    /**\n     * 1=随机<br/>2=随机不重复<br/>3=顺序重复\n     */\n    readonly normLevelST: number\n    readonly normSTList: randStrategy[]\n    /**\n     * BOSS关卡数量\n     */\n    readonly bossLevelCount: number\n    /**\n     * 1=随机<br/>2=随机不重复<br/>3=顺序重复\n     */\n    readonly bossLevelST: number\n    readonly bossSTList: randStrategy[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        for (let _e of this.normSTList) { _e?.resolve(tables); }\n        \n        \n        for (let _e of this.bossSTList) { _e?.resolve(tables); }\n    }\n}\n\n\n\n\n\nexport class ResLoot {\n\n    constructor(_json_: any) {\n        if (_json_.loot_id === undefined) { throw new Error() }\n        this.lootId = _json_.loot_id\n        if (_json_.loot_group === undefined) { throw new Error() }\n        this.lootGroup = _json_.loot_group\n        if (_json_.loot_type === undefined) { throw new Error() }\n        this.lootType = _json_.loot_type\n        if (_json_.item_list === undefined) { throw new Error() }\n        { this.itemList = []; for(let _ele0 of _json_.item_list) { let _e0; _e0 = new ResLootItem(_ele0); this.itemList.push(_e0);}}\n    }\n\n    /**\n     * 掉落ID\n     */\n    readonly lootId: number\n    /**\n     * 掉落组\n     */\n    readonly lootGroup: number\n    /**\n     * 掉落方式\n     */\n    readonly lootType: ResLootType\n    readonly itemList: ResLootItem[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        for (let _e of this.itemList) { _e?.resolve(tables); }\n    }\n}\n\n\n\n\n\n/**\n * 掉落资源项配置\n */\nexport class ResLootItem {\n\n    constructor(_json_: any) {\n        if (_json_.item_id === undefined) { throw new Error() }\n        this.itemId = _json_.item_id\n        if (_json_.count === undefined) { throw new Error() }\n        this.count = _json_.count\n        if (_json_.rate === undefined) { throw new Error() }\n        this.rate = _json_.rate\n        if (_json_.protect_times === undefined) { throw new Error() }\n        this.protectTimes = _json_.protect_times\n    }\n\n    /**\n     * item_id 可以是道具，装备，飞机，或者另一个掉落ID\n     */\n    readonly itemId: number\n    readonly count: number\n    readonly rate: number\n    readonly protectTimes: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResModeReward {\n\n    constructor(_json_: any) {\n        if (_json_.modeID === undefined) { throw new Error() }\n        this.modeID = _json_.modeID\n        if (_json_.levelReward === undefined) { throw new Error() }\n        { this.levelReward = []; for(let _ele0 of _json_.levelReward) { let _e0; _e0 = _ele0; this.levelReward.push(_e0);}}\n        if (_json_.passStar1Reward === undefined) { throw new Error() }\n        this.passStar1Reward = _json_.passStar1Reward\n        if (_json_.passStar2Reward === undefined) { throw new Error() }\n        this.passStar2Reward = _json_.passStar2Reward\n        if (_json_.passStar3Reward === undefined) { throw new Error() }\n        this.passStar3Reward = _json_.passStar3Reward\n    }\n\n    /**\n     * 模式ID\n     */\n    readonly modeID: number\n    /**\n     * 关卡1奖励ID\n     */\n    readonly levelReward: number[]\n    /**\n     * 通关1星奖励ID\n     */\n    readonly passStar1Reward: number\n    /**\n     * 通关2星奖励ID\n     */\n    readonly passStar2Reward: number\n    /**\n     * 通关3星奖励ID\n     */\n    readonly passStar3Reward: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResPlane {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.star_level === undefined) { throw new Error() }\n        this.starLevel = _json_.star_level\n        if (_json_.name === undefined) { throw new Error() }\n        this.name = _json_.name\n        if (_json_.icon === undefined) { throw new Error() }\n        this.icon = _json_.icon\n        if (_json_.portrait === undefined) { throw new Error() }\n        this.portrait = _json_.portrait\n        if (_json_.prefab === undefined) { throw new Error() }\n        this.prefab = _json_.prefab\n        if (_json_.description === undefined) { throw new Error() }\n        this.description = _json_.description\n        if (_json_.quality === undefined) { throw new Error() }\n        this.quality = _json_.quality\n        if (_json_.collide_level === undefined) { throw new Error() }\n        this.collideLevel = _json_.collide_level\n        if (_json_.collide_damage === undefined) { throw new Error() }\n        this.collideDamage = _json_.collide_damage\n        if (_json_.property === undefined) { throw new Error() }\n        this.property = new PlaneProperty(_json_.property)\n        if (_json_.effects === undefined) { throw new Error() }\n        { this.effects = []; for(let _ele0 of _json_.effects) { let _e0; _e0 = new PlaneEffect(_ele0); this.effects.push(_e0);}}\n        if (_json_.materials === undefined) { throw new Error() }\n        { this.materials = []; for(let _ele0 of _json_.materials) { let _e0; _e0 = new PlaneMaterial(_ele0); this.materials.push(_e0);}}\n    }\n\n    /**\n     * ID\n     */\n    readonly id: number\n    /**\n     * 战机星级\n     */\n    readonly starLevel: number\n    /**\n     * 名称\n     */\n    readonly name: string\n    /**\n     * 图标\n     */\n    readonly icon: string\n    /**\n     * 立绘\n     */\n    readonly portrait: string\n    /**\n     * prefab\n     */\n    readonly prefab: string\n    /**\n     * 描述\n     */\n    readonly description: string\n    /**\n     * 品质\n     */\n    readonly quality: QualityType\n    /**\n     * 相撞等级\n     */\n    readonly collideLevel: number\n    /**\n     * 相撞伤害\n     */\n    readonly collideDamage: number\n    readonly property: PlaneProperty\n    /**\n     * 效果列表\n     */\n    readonly effects: PlaneEffect[]\n    /**\n     * 升星材料\n     */\n    readonly materials: PlaneMaterial[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        this.property?.resolve(tables);\n        for (let _e of this.effects) { _e?.resolve(tables); }\n        for (let _e of this.materials) { _e?.resolve(tables); }\n    }\n}\n\n\n\n\n\nexport class ResSkill {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.name === undefined) { throw new Error() }\n        this.name = _json_.name\n        if (_json_.desc === undefined) { throw new Error() }\n        this.desc = _json_.desc\n        if (_json_.icon === undefined) { throw new Error() }\n        this.icon = _json_.icon\n        if (_json_.cd === undefined) { throw new Error() }\n        this.cd = _json_.cd\n        if (_json_.CostID === undefined) { throw new Error() }\n        this.CostID = _json_.CostID\n        if (_json_.CostNum === undefined) { throw new Error() }\n        this.CostNum = _json_.CostNum\n        if (_json_.conditionID === undefined) { throw new Error() }\n        this.conditionID = _json_.conditionID\n        if (_json_.ApplyBuffs === undefined) { throw new Error() }\n        { this.ApplyBuffs = []; for(let _ele0 of _json_.ApplyBuffs) { let _e0; _e0 = new ApplyBuff(_ele0); this.ApplyBuffs.push(_e0);}}\n        if (_json_.effects === undefined) { throw new Error() }\n        { this.effects = []; for(let _ele0 of _json_.effects) { let _e0; _e0 = new EffectParam(_ele0); this.effects.push(_e0);}}\n    }\n\n    /**\n     * ID\n     */\n    readonly id: number\n    /**\n     * 技能名称\n     */\n    readonly name: string\n    /**\n     * 描述\n     */\n    readonly desc: string\n    /**\n     * 技能图标prefab\n     */\n    readonly icon: string\n    /**\n     * 冷却时间\n     */\n    readonly cd: number\n    /**\n     * 费用ID\n     */\n    readonly CostID: number\n    /**\n     * 费用消耗值\n     */\n    readonly CostNum: number\n    /**\n     * 条件\n     */\n    readonly conditionID: number\n    readonly ApplyBuffs: ApplyBuff[]\n    readonly effects: EffectParam[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResSkillCondition {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.target === undefined) { throw new Error() }\n        this.target = _json_.target\n        if (_json_.boolType === undefined) { throw new Error() }\n        this.boolType = _json_.boolType\n        if (_json_.conditions === undefined) { throw new Error() }\n        { this.conditions = []; for(let _ele0 of _json_.conditions) { let _e0; _e0 = new ResSkillConditionElem(_ele0); this.conditions.push(_e0);}}\n    }\n\n    /**\n     * 条件ID\n     */\n    readonly id: number\n    /**\n     * 条件目标\n     */\n    readonly target: TargetType\n    /**\n     * 条件布尔类型\n     */\n    readonly boolType: BoolOpType\n    readonly conditions: ResSkillConditionElem[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        for (let _e of this.conditions) { _e?.resolve(tables); }\n    }\n}\n\n\n\n\n\nexport class ResSkillConditionElem {\n\n    constructor(_json_: any) {\n        if (_json_.type === undefined) { throw new Error() }\n        this.type = _json_.type\n        if (_json_.params === undefined) { throw new Error() }\n        { this.params = []; for(let _ele0 of _json_.params) { let _e0; _e0 = _ele0; this.params.push(_e0);}}\n        if (_json_.op === undefined) { throw new Error() }\n        this.op = _json_.op\n        if (_json_.value === undefined) { throw new Error() }\n        this.value = _json_.value\n    }\n\n    readonly type: SkillConditionType\n    readonly params: number[]\n    readonly op: CondOPType\n    readonly value: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResStore {\n\n    constructor(_json_: any) {\n        if (_json_.ID === undefined) { throw new Error() }\n        this.ID = _json_.ID\n        if (_json_.name === undefined) { throw new Error() }\n        this.name = _json_.name\n        if (_json_.icon === undefined) { throw new Error() }\n        this.icon = _json_.icon\n        if (_json_.goodsClass === undefined) { throw new Error() }\n        this.goodsClass = _json_.goodsClass\n        if (_json_.price === undefined) { throw new Error() }\n        this.price = _json_.price\n        if (_json_.moneyType === undefined) { throw new Error() }\n        this.moneyType = _json_.moneyType\n        if (_json_.moneyItemID === undefined) { throw new Error() }\n        this.moneyItemID = _json_.moneyItemID\n        if (_json_.discount === undefined) { throw new Error() }\n        this.discount = _json_.discount\n        if (_json_.limitPeriod === undefined) { throw new Error() }\n        this.limitPeriod = _json_.limitPeriod\n        if (_json_.limitCount === undefined) { throw new Error() }\n        this.limitCount = _json_.limitCount\n        if (_json_.itemID === undefined) { throw new Error() }\n        this.itemID = _json_.itemID\n        if (_json_.count === undefined) { throw new Error() }\n        this.count = _json_.count\n        if (_json_.listing === undefined) { throw new Error() }\n        this.listing = _json_.listing\n        if (_json_.delisting === undefined) { throw new Error() }\n        this.delisting = _json_.delisting\n        if (_json_.newFlag === undefined) { throw new Error() }\n        this.newFlag = _json_.newFlag\n        if (_json_.freeFlag === undefined) { throw new Error() }\n        this.freeFlag = _json_.freeFlag\n        if (_json_.hotFlag === undefined) { throw new Error() }\n        this.hotFlag = _json_.hotFlag\n        if (_json_.adPos === undefined) { throw new Error() }\n        this.adPos = _json_.adPos\n    }\n\n    /**\n     * 商品ID\n     */\n    readonly ID: number\n    /**\n     * 商品名\n     */\n    readonly name: string\n    /**\n     * 图标\n     */\n    readonly icon: string\n    /**\n     * 商品分类\n     */\n    readonly goodsClass: GoodsClass\n    /**\n     * 价格\n     */\n    readonly price: number\n    /**\n     * 货币类型\n     */\n    readonly moneyType: MoneyType\n    /**\n     * 货币道具ID\n     */\n    readonly moneyItemID: number\n    /**\n     * 折扣\n     */\n    readonly discount: number\n    /**\n     * 限购周期\n     */\n    readonly limitPeriod: ResPeriodType\n    /**\n     * 限购次数\n     */\n    readonly limitCount: number\n    /**\n     * 道具ID\n     */\n    readonly itemID: number\n    /**\n     * 道具数量\n     */\n    readonly count: number\n    /**\n     * 上架时间\n     */\n    readonly listing: string\n    /**\n     * 下架时间\n     */\n    readonly delisting: string\n    /**\n     * 上新标识\n     */\n    readonly newFlag: number\n    /**\n     * 限免标识\n     */\n    readonly freeFlag: number\n    /**\n     * 热销标识\n     */\n    readonly hotFlag: number\n    /**\n     * 广告位\n     */\n    readonly adPos: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResTask {\n\n    constructor(_json_: any) {\n        if (_json_.task_id === undefined) { throw new Error() }\n        this.taskId = _json_.task_id\n        if (_json_.group_id === undefined) { throw new Error() }\n        this.groupId = _json_.group_id\n        if (_json_.task_class === undefined) { throw new Error() }\n        this.taskClass = _json_.task_class\n        if (_json_.prev_id === undefined) { throw new Error() }\n        this.prevId = _json_.prev_id\n        if (_json_.period_type === undefined) { throw new Error() }\n        this.periodType = _json_.period_type\n        if (_json_.need_pay === undefined) { throw new Error() }\n        this.needPay = _json_.need_pay\n        if (_json_.task_cond === undefined) { throw new Error() }\n        { this.taskCond = []; for(let _ele0 of _json_.task_cond) { let _e0; _e0 = new ResCondition(_ele0); this.taskCond.push(_e0);}}\n        if (_json_.task_goal === undefined) { throw new Error() }\n        this.taskGoal = new ResTaskGoal(_json_.task_goal)\n        if (_json_.accumulate === undefined) { throw new Error() }\n        this.accumulate = _json_.accumulate\n        if (_json_.reward_id === undefined) { throw new Error() }\n        this.rewardId = _json_.reward_id\n        if (_json_.orbit_group_id === undefined) { throw new Error() }\n        this.orbitGroupId = _json_.orbit_group_id\n        if (_json_.orbit_value === undefined) { throw new Error() }\n        this.orbitValue = _json_.orbit_value\n        if (_json_.open_date === undefined) { throw new Error() }\n        this.openDate = _json_.open_date\n        if (_json_.open_time === undefined) { throw new Error() }\n        this.openTime = _json_.open_time\n        if (_json_.close_date === undefined) { throw new Error() }\n        this.closeDate = _json_.close_date\n        if (_json_.close_time === undefined) { throw new Error() }\n        this.closeTime = _json_.close_time\n        if (_json_.link_to === undefined) { throw new Error() }\n        this.linkTo = _json_.link_to\n    }\n\n    /**\n     * 任务 ID\n     */\n    readonly taskId: number\n    /**\n     * 任务集 ID\n     */\n    readonly groupId: number\n    /**\n     * 任务类型\n     */\n    readonly taskClass: ResTaskClass\n    /**\n     * 前置任务 ID\n     */\n    readonly prevId: number\n    /**\n     * 重置周期\n     */\n    readonly periodType: ResPeriodType\n    /**\n     * 是否需要付费解锁\n     */\n    readonly needPay: boolean\n    /**\n     * 任务接取条件\n     */\n    readonly taskCond: ResCondition[]\n    readonly taskGoal: ResTaskGoal\n    /**\n     * 目标值累积\n     */\n    readonly accumulate: boolean\n    /**\n     * 奖励ID\n     */\n    readonly rewardId: number\n    /**\n     * 奖励轨道集ID\n     */\n    readonly orbitGroupId: number\n    /**\n     * 完成奖励值\n     */\n    readonly orbitValue: number\n    /**\n     * 开放日期(yyyymmdd)\n     */\n    readonly openDate: string\n    /**\n     * 开放时间(HHMMSS)\n     */\n    readonly openTime: string\n    /**\n     * 结束日期(yyyymmdd)\n     */\n    readonly closeDate: string\n    /**\n     * 结束时间(HHMMSS)\n     */\n    readonly closeTime: string\n    /**\n     * 任务追踪\n     */\n    readonly linkTo: string\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        for (let _e of this.taskCond) { _e?.resolve(tables); }\n        this.taskGoal?.resolve(tables);\n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\n/**\n * 目标项\n */\nexport class ResTaskGoal {\n\n    constructor(_json_: any) {\n        if (_json_.goal_type === undefined) { throw new Error() }\n        this.goalType = _json_.goal_type\n        if (_json_.params === undefined) { throw new Error() }\n        { this.params = []; for(let _ele0 of _json_.params) { let _e0; _e0 = _ele0; this.params.push(_e0);}}\n        if (_json_.desc === undefined) { throw new Error() }\n        this.desc = _json_.desc\n    }\n\n    readonly goalType: ResGoalType\n    readonly params: number[]\n    readonly desc: string\n\n    resolve(tables:Tables) {\n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResTaskOrbit {\n\n    constructor(_json_: any) {\n        if (_json_.task_id === undefined) { throw new Error() }\n        this.taskId = _json_.task_id\n        if (_json_.group_id === undefined) { throw new Error() }\n        this.groupId = _json_.group_id\n        if (_json_.index === undefined) { throw new Error() }\n        this.index = _json_.index\n        if (_json_.period_type === undefined) { throw new Error() }\n        this.periodType = _json_.period_type\n        if (_json_.need_pay === undefined) { throw new Error() }\n        this.needPay = _json_.need_pay\n        if (_json_.task_cond === undefined) { throw new Error() }\n        { this.taskCond = []; for(let _ele0 of _json_.task_cond) { let _e0; _e0 = new ResCondition(_ele0); this.taskCond.push(_e0);}}\n        if (_json_.task_goal === undefined) { throw new Error() }\n        this.taskGoal = new ResTaskGoal(_json_.task_goal)\n        if (_json_.accumulate === undefined) { throw new Error() }\n        this.accumulate = _json_.accumulate\n        if (_json_.reward_id === undefined) { throw new Error() }\n        this.rewardId = _json_.reward_id\n    }\n\n    /**\n     * 任务轨道ID\n     */\n    readonly taskId: number\n    /**\n     * 轨道任务集\n     */\n    readonly groupId: number\n    /**\n     * 序号\n     */\n    readonly index: number\n    /**\n     * 循环类型\n     */\n    readonly periodType: ResPeriodType\n    /**\n     * 是否付费\n     */\n    readonly needPay: boolean\n    readonly taskCond: ResCondition[]\n    readonly taskGoal: ResTaskGoal\n    /**\n     * 是否累积\n     */\n    readonly accumulate: boolean\n    /**\n     * 奖励ID，<br/>对应掉落表的配置\n     */\n    readonly rewardId: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        for (let _e of this.taskCond) { _e?.resolve(tables); }\n        this.taskGoal?.resolve(tables);\n        \n        \n    }\n}\n\n\n\n\n\nexport class ResUpgrade {\n\n    constructor(_json_: any) {\n        if (_json_.role_level === undefined) { throw new Error() }\n        this.roleLevel = _json_.role_level\n        if (_json_.xp === undefined) { throw new Error() }\n        this.xp = _json_.xp\n        if (_json_.reward_id === undefined) { throw new Error() }\n        this.rewardId = _json_.reward_id\n        if (_json_.function_unlock === undefined) { throw new Error() }\n        { this.functionUnlock = []; for(let _ele0 of _json_.function_unlock) { let _e0; _e0 = _ele0; this.functionUnlock.push(_e0);}}\n    }\n\n    /**\n     * 玩家等级\n     */\n    readonly roleLevel: number\n    /**\n     * 升级所需的经验\n     */\n    readonly xp: number\n    /**\n     * 升级奖励\n     */\n    readonly rewardId: number\n    /**\n     * 解锁功能\n     */\n    readonly functionUnlock: FunctionID[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n    }\n}\n\n\n\n\n\n\nexport class TbGlobalAttr {\n\n    private _data: ResGlobalAttr\n    constructor(_json_: any) {\n        if (_json_.length != 1) throw new Error('table mode=one, but size != 1')\n        this._data = new ResGlobalAttr(_json_[0])\n    }\n\n    getData(): ResGlobalAttr { return this._data; }\n\n    /**\n     * 每回合发放的金币\n     */\n    get  GoldProducion(): number { return this._data.GoldProducion; }\n    /**\n     * 体力上限值\n     */\n    get  MaxEnergy(): number { return this._data.MaxEnergy; }\n    /**\n     * 体力恢复的间隔时间\n     */\n    get  EnergyRecoverInterval(): number { return this._data.EnergyRecoverInterval; }\n    /**\n     * 体力恢复的值\n     */\n    get  EnergyRecoverValue(): number { return this._data.EnergyRecoverValue; }\n    /**\n     * 局内道具拾取距离\n     */\n    get  ItemPickUpRadius(): number { return this._data.ItemPickUpRadius; }\n    /**\n     * 受击保护\n     */\n    get  PostHitProtection(): number { return this._data.PostHitProtection; }\n    /**\n     * 镜头平移最大跟随速度\n     */\n    get  CameraTranslationMaxMoveSpeed(): number { return this._data.CameraTranslationMaxMoveSpeed; }\n    /**\n     * 镜头平移启动延迟\n     */\n    get  CameraTranslationMoveDelay(): number { return this._data.CameraTranslationMoveDelay; }\n    /**\n     * 无尽模式的最大双倍奖励每日领取次数\n     */\n    get  MaxEndlessDoubleRewardTimes(): number { return this._data.MaxEndlessDoubleRewardTimes; }\n    /**\n     * 剧情模式的最大双倍奖励每日领取次数\n     */\n    get  MaxStoryDoubleRewardTimes(): number { return this._data.MaxStoryDoubleRewardTimes; }\n    /**\n     * 邮件最大保留时长，超过此时间的邮件自动清理\n     */\n    get  MaxMailKeepDays(): number { return this._data.MaxMailKeepDays; }\n\n    resolve(tables:Tables)\n    {\n        this._data.resolve(tables)\n    }\n    \n}\n\n\n\n\nexport class TbEquipUpgrade {\n    private _dataList: ResEquipUpgrade[]\n    \n    constructor(_json_: any) {\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResEquipUpgrade\n            _v = new ResEquipUpgrade(_json2_)\n            this._dataList.push(_v)\n        }\n    }\n\n    getDataList(): ResEquipUpgrade[] { return this._dataList }\n\n    get(index: number): ResEquipUpgrade | undefined { return this._dataList[index] }\n    \n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbGM {\n    private _dataList: GM[]\n    \n    constructor(_json_: any) {\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: GM\n            _v = new GM(_json2_)\n            this._dataList.push(_v)\n        }\n    }\n\n    getDataList(): GM[] { return this._dataList }\n\n    get(index: number): GM | undefined { return this._dataList[index] }\n    \n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbPlane {\n    private _dataMap: Map<number, ResPlane>\n    private _dataList: ResPlane[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResPlane>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResPlane\n            _v = new ResPlane(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResPlane> { return this._dataMap; }\n    getDataList(): ResPlane[] { return this._dataList; }\n\n    get(key: number): ResPlane | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResTask {\n    private _dataMap: Map<number, ResTask>\n    private _dataList: ResTask[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResTask>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResTask\n            _v = new ResTask(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.taskId, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResTask> { return this._dataMap; }\n    getDataList(): ResTask[] { return this._dataList; }\n\n    get(key: number): ResTask | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResAchievement {\n    private _dataMap: Map<number, ResAchievement>\n    private _dataList: ResAchievement[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResAchievement>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResAchievement\n            _v = new ResAchievement(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.taskId, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResAchievement> { return this._dataMap; }\n    getDataList(): ResAchievement[] { return this._dataList; }\n\n    get(key: number): ResAchievement | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResActivity {\n    private _dataMap: Map<number, ResActivity>\n    private _dataList: ResActivity[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResActivity>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResActivity\n            _v = new ResActivity(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.taskId, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResActivity> { return this._dataMap; }\n    getDataList(): ResActivity[] { return this._dataList; }\n\n    get(key: number): ResActivity | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResBuffer {\n    private _dataMap: Map<number, ResBuffer>\n    private _dataList: ResBuffer[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResBuffer>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResBuffer\n            _v = new ResBuffer(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResBuffer> { return this._dataMap; }\n    getDataList(): ResBuffer[] { return this._dataList; }\n\n    get(key: number): ResBuffer | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResEmitter {\n    private _dataMap: Map<number, ResEmitter>\n    private _dataList: ResEmitter[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResEmitter>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResEmitter\n            _v = new ResEmitter(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResEmitter> { return this._dataMap; }\n    getDataList(): ResEmitter[] { return this._dataList; }\n\n    get(key: number): ResEmitter | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResChapter {\n    private _dataMap: Map<number, ResChapter>\n    private _dataList: ResChapter[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResChapter>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResChapter\n            _v = new ResChapter(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResChapter> { return this._dataMap; }\n    getDataList(): ResChapter[] { return this._dataList; }\n\n    get(key: number): ResChapter | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResEffect {\n    private _dataMap: Map<number, ResEffect>\n    private _dataList: ResEffect[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResEffect>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResEffect\n            _v = new ResEffect(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResEffect> { return this._dataMap; }\n    getDataList(): ResEffect[] { return this._dataList; }\n\n    get(key: number): ResEffect | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResEnemy {\n    private _dataMap: Map<number, ResEnemy>\n    private _dataList: ResEnemy[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResEnemy>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResEnemy\n            _v = new ResEnemy(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResEnemy> { return this._dataMap; }\n    getDataList(): ResEnemy[] { return this._dataList; }\n\n    get(key: number): ResEnemy | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResSkill {\n    private _dataMap: Map<number, ResSkill>\n    private _dataList: ResSkill[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResSkill>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResSkill\n            _v = new ResSkill(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResSkill> { return this._dataMap; }\n    getDataList(): ResSkill[] { return this._dataList; }\n\n    get(key: number): ResSkill | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResLevel {\n    private _dataMap: Map<number, ResLevel>\n    private _dataList: ResLevel[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResLevel>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResLevel\n            _v = new ResLevel(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResLevel> { return this._dataMap; }\n    getDataList(): ResLevel[] { return this._dataList; }\n\n    get(key: number): ResLevel | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResLoot {\n    private _dataMap: Map<number, ResLoot>\n    private _dataList: ResLoot[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResLoot>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResLoot\n            _v = new ResLoot(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.lootId, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResLoot> { return this._dataMap; }\n    getDataList(): ResLoot[] { return this._dataList; }\n\n    get(key: number): ResLoot | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResEquip {\n    private _dataMap: Map<number, ResEquip>\n    private _dataList: ResEquip[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResEquip>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResEquip\n            _v = new ResEquip(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResEquip> { return this._dataMap; }\n    getDataList(): ResEquip[] { return this._dataList; }\n\n    get(key: number): ResEquip | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResItem {\n    private _dataMap: Map<number, ResItem>\n    private _dataList: ResItem[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResItem>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResItem\n            _v = new ResItem(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResItem> { return this._dataMap; }\n    getDataList(): ResItem[] { return this._dataList; }\n\n    get(key: number): ResItem | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResGameMode {\n    private _dataMap: Map<number, ResGameMode>\n    private _dataList: ResGameMode[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResGameMode>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResGameMode\n            _v = new ResGameMode(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.modeID, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResGameMode> { return this._dataMap; }\n    getDataList(): ResGameMode[] { return this._dataList; }\n\n    get(key: number): ResGameMode | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResLevelGroup {\n    private _dataMap: Map<number, ResLevelGroup>\n    private _dataList: ResLevelGroup[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResLevelGroup>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResLevelGroup\n            _v = new ResLevelGroup(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResLevelGroup> { return this._dataMap; }\n    getDataList(): ResLevelGroup[] { return this._dataList; }\n\n    get(key: number): ResLevelGroup | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResModeReward {\n    private _dataMap: Map<number, ResModeReward>\n    private _dataList: ResModeReward[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResModeReward>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResModeReward\n            _v = new ResModeReward(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.modeID, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResModeReward> { return this._dataMap; }\n    getDataList(): ResModeReward[] { return this._dataList; }\n\n    get(key: number): ResModeReward | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResUpgrade {\n    private _dataMap: Map<number, ResUpgrade>\n    private _dataList: ResUpgrade[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResUpgrade>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResUpgrade\n            _v = new ResUpgrade(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.roleLevel, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResUpgrade> { return this._dataMap; }\n    getDataList(): ResUpgrade[] { return this._dataList; }\n\n    get(key: number): ResUpgrade | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResTaskOrbit {\n    private _dataMap: Map<number, ResTaskOrbit>\n    private _dataList: ResTaskOrbit[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResTaskOrbit>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResTaskOrbit\n            _v = new ResTaskOrbit(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.taskId, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResTaskOrbit> { return this._dataMap; }\n    getDataList(): ResTaskOrbit[] { return this._dataList; }\n\n    get(key: number): ResTaskOrbit | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResStore {\n    private _dataMap: Map<number, ResStore>\n    private _dataList: ResStore[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResStore>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResStore\n            _v = new ResStore(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.ID, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResStore> { return this._dataMap; }\n    getDataList(): ResStore[] { return this._dataList; }\n\n    get(key: number): ResStore | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResSkillCondition {\n    private _dataMap: Map<number, ResSkillCondition>\n    private _dataList: ResSkillCondition[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResSkillCondition>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResSkillCondition\n            _v = new ResSkillCondition(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResSkillCondition> { return this._dataMap; }\n    getDataList(): ResSkillCondition[] { return this._dataList; }\n\n    get(key: number): ResSkillCondition | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\ntype JsonLoader = (file: string) => any\n\nexport class Tables {\n    private _TbGlobalAttr: TbGlobalAttr\n    get TbGlobalAttr(): TbGlobalAttr  { return this._TbGlobalAttr;}\n    private _TbEquipUpgrade: TbEquipUpgrade\n    get TbEquipUpgrade(): TbEquipUpgrade  { return this._TbEquipUpgrade;}\n    private _TbGM: TbGM\n    get TbGM(): TbGM  { return this._TbGM;}\n    private _TbPlane: TbPlane\n    get TbPlane(): TbPlane  { return this._TbPlane;}\n    private _TbResTask: TbResTask\n    get TbResTask(): TbResTask  { return this._TbResTask;}\n    private _TbResAchievement: TbResAchievement\n    get TbResAchievement(): TbResAchievement  { return this._TbResAchievement;}\n    private _TbResActivity: TbResActivity\n    get TbResActivity(): TbResActivity  { return this._TbResActivity;}\n    private _TbResBuffer: TbResBuffer\n    get TbResBuffer(): TbResBuffer  { return this._TbResBuffer;}\n    private _TbResEmitter: TbResEmitter\n    get TbResEmitter(): TbResEmitter  { return this._TbResEmitter;}\n    private _TbResChapter: TbResChapter\n    get TbResChapter(): TbResChapter  { return this._TbResChapter;}\n    private _TbResEffect: TbResEffect\n    get TbResEffect(): TbResEffect  { return this._TbResEffect;}\n    private _TbResEnemy: TbResEnemy\n    get TbResEnemy(): TbResEnemy  { return this._TbResEnemy;}\n    private _TbResSkill: TbResSkill\n    get TbResSkill(): TbResSkill  { return this._TbResSkill;}\n    private _TbResLevel: TbResLevel\n    get TbResLevel(): TbResLevel  { return this._TbResLevel;}\n    private _TbResLoot: TbResLoot\n    get TbResLoot(): TbResLoot  { return this._TbResLoot;}\n    private _TbResEquip: TbResEquip\n    get TbResEquip(): TbResEquip  { return this._TbResEquip;}\n    private _TbResItem: TbResItem\n    get TbResItem(): TbResItem  { return this._TbResItem;}\n    private _TbResGameMode: TbResGameMode\n    get TbResGameMode(): TbResGameMode  { return this._TbResGameMode;}\n    private _TbResLevelGroup: TbResLevelGroup\n    get TbResLevelGroup(): TbResLevelGroup  { return this._TbResLevelGroup;}\n    private _TbResModeReward: TbResModeReward\n    get TbResModeReward(): TbResModeReward  { return this._TbResModeReward;}\n    private _TbResUpgrade: TbResUpgrade\n    get TbResUpgrade(): TbResUpgrade  { return this._TbResUpgrade;}\n    private _TbResTaskOrbit: TbResTaskOrbit\n    get TbResTaskOrbit(): TbResTaskOrbit  { return this._TbResTaskOrbit;}\n    private _TbResStore: TbResStore\n    get TbResStore(): TbResStore  { return this._TbResStore;}\n    private _TbResSkillCondition: TbResSkillCondition\n    get TbResSkillCondition(): TbResSkillCondition  { return this._TbResSkillCondition;}\n\n    constructor(loader: JsonLoader) {\n        this._TbGlobalAttr = new TbGlobalAttr(loader('tbglobalattr'))\n        this._TbEquipUpgrade = new TbEquipUpgrade(loader('tbequipupgrade'))\n        this._TbGM = new TbGM(loader('tbgm'))\n        this._TbPlane = new TbPlane(loader('tbplane'))\n        this._TbResTask = new TbResTask(loader('tbrestask'))\n        this._TbResAchievement = new TbResAchievement(loader('tbresachievement'))\n        this._TbResActivity = new TbResActivity(loader('tbresactivity'))\n        this._TbResBuffer = new TbResBuffer(loader('tbresbuffer'))\n        this._TbResEmitter = new TbResEmitter(loader('tbresemitter'))\n        this._TbResChapter = new TbResChapter(loader('tbreschapter'))\n        this._TbResEffect = new TbResEffect(loader('tbreseffect'))\n        this._TbResEnemy = new TbResEnemy(loader('tbresenemy'))\n        this._TbResSkill = new TbResSkill(loader('tbresskill'))\n        this._TbResLevel = new TbResLevel(loader('tbreslevel'))\n        this._TbResLoot = new TbResLoot(loader('tbresloot'))\n        this._TbResEquip = new TbResEquip(loader('tbresequip'))\n        this._TbResItem = new TbResItem(loader('tbresitem'))\n        this._TbResGameMode = new TbResGameMode(loader('tbresgamemode'))\n        this._TbResLevelGroup = new TbResLevelGroup(loader('tbreslevelgroup'))\n        this._TbResModeReward = new TbResModeReward(loader('tbresmodereward'))\n        this._TbResUpgrade = new TbResUpgrade(loader('tbresupgrade'))\n        this._TbResTaskOrbit = new TbResTaskOrbit(loader('tbrestaskorbit'))\n        this._TbResStore = new TbResStore(loader('tbresstore'))\n        this._TbResSkillCondition = new TbResSkillCondition(loader('tbresskillcondition'))\n\n        this._TbGlobalAttr.resolve(this)\n        this._TbEquipUpgrade.resolve(this)\n        this._TbGM.resolve(this)\n        this._TbPlane.resolve(this)\n        this._TbResTask.resolve(this)\n        this._TbResAchievement.resolve(this)\n        this._TbResActivity.resolve(this)\n        this._TbResBuffer.resolve(this)\n        this._TbResEmitter.resolve(this)\n        this._TbResChapter.resolve(this)\n        this._TbResEffect.resolve(this)\n        this._TbResEnemy.resolve(this)\n        this._TbResSkill.resolve(this)\n        this._TbResLevel.resolve(this)\n        this._TbResLoot.resolve(this)\n        this._TbResEquip.resolve(this)\n        this._TbResItem.resolve(this)\n        this._TbResGameMode.resolve(this)\n        this._TbResLevelGroup.resolve(this)\n        this._TbResModeReward.resolve(this)\n        this._TbResUpgrade.resolve(this)\n        this._TbResTaskOrbit.resolve(this)\n        this._TbResStore.resolve(this)\n        this._TbResSkillCondition.resolve(this)\n    }\n}\n\n"]}