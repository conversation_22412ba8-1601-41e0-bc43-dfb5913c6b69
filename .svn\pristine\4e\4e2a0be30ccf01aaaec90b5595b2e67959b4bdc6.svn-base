import { _decorator, CCBoolean, CCFloat, CCInteger, Component } from "cc";
import { MainPlane } from "./MainPlane";
import PlaneBaseDebug from "../PlaneBaseDebug";
import { AttributeConst } from "../../../../const/AttributeConst";
const { ccclass, property } = _decorator;

@ccclass("MainPlaneDebug")
export default class MainPlaneDebug extends PlaneBaseDebug {
    protected mainPlane: MainPlane | null = null;
    start(): void {
        super.start();
        this.mainPlane = this.node.getComponent(MainPlane);
    }
    @property(CCInteger)
    get maxNuclear():number {
        return Math.floor(this.mainPlane?.attribute.getFinalAttributeByKey(AttributeConst.NuclearMax) || 0);
    }
    @property(CCInteger)
    get curNuclear():number {
        return this.mainPlane?.nuclearNum || 0;
    }
}