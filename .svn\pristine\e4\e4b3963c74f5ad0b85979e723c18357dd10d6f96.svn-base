import { _decorator, Component, EPhysics2DDrawFlags, find, Node, PhysicsSystem2D } from 'cc';
import { GameConst } from 'db://assets/scripts/core/base/GameConst';
import { UIMgr } from 'db://assets/scripts/core/base/UIMgr';
import { MyApp } from '../../app/MyApp';
import EventManager from '../../event/EventManager';
import { MBoomUI } from '../../ui/gameui/game/MBoomUI';
import { GameIns } from '../GameIns';
import { BulletSystem } from '../bullet/BulletSystem';
import FCollider from '../collider-system/FCollider';
import { GameEvent } from '../event/GameEvent';
import BattleLayer from '../ui/layer/BattleLayer';
import { GameFightUI } from '../ui/layer/GameFightUI';

const { ccclass, property } = _decorator;

@ccclass('GameMain')
export class GameMain extends Component {


    @property(BattleLayer)
    BattleLayer: BattleLayer | null = null;

    @property(Node)
    gameEnd: Node | null = null;
    @property(Node)
    CoverBg: Node | null = null;
    @property(GameFightUI)
    GameFightUI: GameFightUI | null = null;


    static instance: GameMain;
    protected onLoad(): void {
        GameMain.instance = this;
        this.CoverBg!.active = true;
        BulletSystem.bulletParent = this.BattleLayer!.selfBulletLayer!;//设置子弹父节点

        MyApp.globalMgr.setBattleResolution();

        GameIns.fColliderManager.enable = true;
        GameIns.fColliderManager.setGlobalColliderEnterCall((colliderA: FCollider, colliderB: FCollider) => {
            colliderA.entity?.onCollide?.(colliderB);
            colliderB.entity?.onCollide?.(colliderA);
        });

        if (GameConst.ColliderDraw) {
            PhysicsSystem2D.instance.enable = true;
            PhysicsSystem2D.instance.debugDrawFlags = EPhysics2DDrawFlags.Aabb;
        }
    }

    protected onEnable(): void {
        EventManager.Instance.on(GameEvent.GameLoadEnd, this.onEventGameLoadEnd, this);
    }

    protected onDisable(): void {
        EventManager.Instance.off(GameEvent.GameLoadEnd, this.onEventGameLoadEnd, this);
    }

    onEventGameLoadEnd() {
        //由于gameui,使用了独立的摄影机，层级太高，会在loading界面之上，所以先隐藏，加载完，再显示
        this.GameFightUI!.node.active = true;
    }

    start() {
        GameIns.battleManager.startLoading();
        UIMgr.openUI(MBoomUI)
    }

    showGameResult(isSuccess: boolean) {
        this.gameEnd!.active = true;

        let LabelWin = find("LabelWin", this.gameEnd!);
        let LabelFail = find("LabelFail", this.gameEnd!);
        LabelWin!.active = isSuccess;
        LabelFail!.active = !isSuccess;
    }

    async onBtnAgainClicked() {
        this.gameEnd!.active = false;
        GameIns.battleManager.quitBattle();
    }

    /**
     * 每帧更新逻辑
     * @param deltaTime 时间增量
     */
    update(deltaTime: number): void {
        GameIns.battleManager.updateGameLogic(deltaTime);
    }
}


