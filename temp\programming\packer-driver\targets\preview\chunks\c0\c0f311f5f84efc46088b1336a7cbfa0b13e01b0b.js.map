{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/skill/Buff.ts"], "names": ["Buff", "Vec2", "BoolOpType", "CondOPType", "SkillConditionType", "TargetType", "MyApp", "PlaneEventType", "forEachEntityByTargetType", "ExCondition", "ExConditionEvent", "ExConditionNum", "GameIns", "constructor", "isOutside", "data", "self", "id", "res", "removeConditionRes", "undefined", "removeConditionElems", "forbinConditionRes", "forbinConditionElems", "triggerConditionRes", "triggerConditionElems", "time", "cycleTimes", "forbin", "stack", "incID", "removeCondition", "lubanTables", "TbResSkillCondition", "get", "conditionRes2Elems", "forbinCondition", "triggerCondition", "target", "entity", "valid<PERSON>arget", "elems", "Array", "from", "conditions", "i", "type", "FatalInjuryHurt", "elem", "has", "update", "PlaneEventRegister", "cb", "<PERSON><PERSON><PERSON><PERSON>", "pickDiamond<PERSON>um", "KillEnemyNum", "killEnemyNum", "UseNuclear", "usedNuclearNum", "UserSuper", "usedSuperNum", "length", "checkCondition", "ret", "boolType", "AND", "for<PERSON>ach", "condition", "value", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "params", "buff<PERSON><PERSON>p", "GetBuff", "CurHP<PERSON>er", "curHp", "maxHp", "CurHP", "MaxHP", "BeAttackTime", "gameDataManager", "gameTime", "hurtTime", "num", "KillEnemy", "RemainNuclearNum", "nuclearNum", "UsedNuclearNum", "LevelStart", "BossBeKilled", "GameTime", "EnemyCount", "radiusSqr", "enemy", "squaredDistance", "node", "position", "mainPlaneManager", "mainPlane", "Enemy", "isDead", "WaveNo", "Distance", "distance", "ret2", "op", "EQ", "GE", "GT", "LE", "LT", "NE", "checkRemoveCondition", "duration", "resetTriggerConditionNum", "reset", "resetConditionElems", "PlaneEventUnRegister", "onRemove", "effects", "applyEffect", "RemoveBuffEffect", "dt", "removeBuff", "cycle", "ApplyBuffEffect"], "mappings": ";;;uPAWaA,I;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAXJC,MAAAA,I,OAAAA,I;;AACAC,MAAAA,U,iBAAAA,U;AAAYC,MAAAA,U,iBAAAA,U;AAAiEC,MAAAA,kB,iBAAAA,kB;AAAoBC,MAAAA,U,iBAAAA,U;;AAEjGC,MAAAA,K,iBAAAA,K;;AAEAC,MAAAA,c,iBAAAA,c;;AACFC,MAAAA,yB;;AAEEC,MAAAA,W,iBAAAA,W;AAAaC,MAAAA,gB,iBAAAA,gB;AAAkBC,MAAAA,c,iBAAAA,c;;AAC/BC,MAAAA,O,iBAAAA,O;;;;;;;;;sBAEIZ,I,GAAN,MAAMA,IAAN,CAAW;AAgBda,QAAAA,WAAW,CAACC,SAAD,EAAqBC,IAArB,EAAsCC,IAAtC,EAAsD;AAAA,eAfjEC,EAeiE;AAAA,eAdjEC,GAciE;AAAA,eAbjEC,kBAaiE,GAbfC,SAae;AAAA,eAZjEC,oBAYiE,GAZZ,IAYY;AAAA,eAXjEC,kBAWiE,GAXfF,SAWe;AAAA,eAVjEG,oBAUiE,GAVZ,IAUY;AAAA,eATjEC,mBASiE,GATdJ,SASc;AAAA,eARjEK,qBAQiE,GARX,IAQW;AAAA,eAPjEC,IAOiE,GAP1D,CAO0D;AAAA,eANjEC,UAMiE,GANpD,CAMoD;AAAA,eALjEb,SAKiE;AAAA,eAJjEc,MAIiE,GAJhD,KAIgD;AAAA,eAHjEC,KAGiE,GAHlD,CAGkD;AAAA,eAFjEb,IAEiE;AAC7D,eAAKC,EAAL,GAAUjB,IAAI,CAAC8B,KAAL,EAAV;AACA,eAAKZ,GAAL,GAAWH,IAAX;AACA,eAAKD,SAAL,GAAiBA,SAAjB;AACA,eAAKE,IAAL,GAAYA,IAAZ;;AACA,cAAI,KAAKE,GAAL,CAASa,eAAb,EAA8B;AAC1B,iBAAKZ,kBAAL,GAA0B;AAAA;AAAA,gCAAMa,WAAN,CAAkBC,mBAAlB,CAAsCC,GAAtC,CAA0C,KAAKhB,GAAL,CAASa,eAAnD,CAA1B;AACA,iBAAKV,oBAAL,GAA4B,KAAKc,kBAAL,CAAwB,KAAKhB,kBAA7B,CAA5B;AACH;;AACD,cAAI,KAAKD,GAAL,CAASkB,eAAb,EAA8B;AAC1B,iBAAKd,kBAAL,GAA0B;AAAA;AAAA,gCAAMU,WAAN,CAAkBC,mBAAlB,CAAsCC,GAAtC,CAA0C,KAAKhB,GAAL,CAASkB,eAAnD,CAA1B;AACA,iBAAKb,oBAAL,GAA4B,KAAKY,kBAAL,CAAwB,KAAKb,kBAA7B,CAA5B;AACH;;AACD,cAAI,KAAKJ,GAAL,CAASmB,gBAAb,EAA+B;AAC3B,iBAAKb,mBAAL,GAA2B;AAAA;AAAA,gCAAMQ,WAAN,CAAkBC,mBAAlB,CAAsCC,GAAtC,CAA0C,KAAKhB,GAAL,CAASmB,gBAAnD,CAA3B;AACA,iBAAKZ,qBAAL,GAA6B,KAAKU,kBAAL,CAAwB,KAAKX,mBAA7B,CAA7B;AACH;AACJ;;AAEDW,QAAAA,kBAAkB,CAACjB,GAAD,EAAgE;AAAA;;AAC9E,cAAI,CAACA,GAAL,EAAU;AACN,mBAAO,IAAP;AACH;;AACD,cAAIoB,MAAwB,GAAG,IAA/B;AACA;AAAA;AAAA,sEAA0B,KAAKtB,IAA/B,EAAqCE,GAAG,CAACoB,MAAzC,EAAkDC,MAAD,IAAsB;AACnED,YAAAA,MAAM,GAAGC,MAAT;AACH,WAFD;;AAGA,cAAI,CAACD,MAAL,EAAa;AACT,mBAAO,IAAP;AACH;;AACD,cAAME,WAAsB,GAAGF,MAA/B;AAEA,cAAMG,KAAK,GAAGC,KAAK,CAACC,IAAN,CAAWzB,GAAG,CAAC0B,UAAf,CAAd;;AAb8E,uCAcvC;AACnC,oBAAQH,KAAK,CAACI,CAAD,CAAL,CAASC,IAAjB;AACI,mBAAK;AAAA;AAAA,4DAAmBC,eAAxB;AACI;AACI,sBAAIC,IAAI,GAAG;AAAA;AAAA,4DAAqBP,KAAK,CAACI,CAAD,CAA1B,EAA+B,MAAM;AAC5CG,oBAAAA,IAAI,CAACC,GAAL,GAAW,IAAX;;AACA,oBAAA,KAAI,CAACC,MAAL,CAAY,CAAZ;AACH,mBAHU,CAAX;AAIAT,kBAAAA,KAAK,CAACI,CAAD,CAAL,GAAWG,IAAX,CALJ,CAMI;;AACA,kBAAA,KAAI,CAAChC,IAAL,CAAUmC,kBAAV,CAA6B;AAAA;AAAA,wDAAeJ,eAA5C,EAA6DC,IAAI,CAACI,EAAlE;AACH;AACD;;AACJ,mBAAK;AAAA;AAAA,4DAAmBC,WAAxB;AACIZ,gBAAAA,KAAK,CAACI,CAAD,CAAL,GAAW;AAAA;AAAA,sDAAmBJ,KAAK,CAACI,CAAD,CAAxB,EAA6BL,WAAW,CAACc,cAAzC,CAAX;AACA;;AACJ,mBAAK;AAAA;AAAA,4DAAmBC,YAAxB;AACId,gBAAAA,KAAK,CAACI,CAAD,CAAL,GAAW;AAAA;AAAA,sDAAmBJ,KAAK,CAACI,CAAD,CAAxB,EAA6BL,WAAW,CAACgB,YAAzC,CAAX;AACA;;AACJ,mBAAK;AAAA;AAAA,4DAAmBC,UAAxB;AACIhB,gBAAAA,KAAK,CAACI,CAAD,CAAL,GAAW;AAAA;AAAA,sDAAmBJ,KAAK,CAACI,CAAD,CAAxB,EAA6BL,WAAW,CAACkB,cAAzC,CAAX;AACA;;AACJ,mBAAK;AAAA;AAAA,4DAAmBC,SAAxB;AACIlB,gBAAAA,KAAK,CAACI,CAAD,CAAL,GAAW;AAAA;AAAA,sDAAmBJ,KAAK,CAACI,CAAD,CAAxB,EAA6BL,WAAW,CAACoB,YAAzC,CAAX;AACA;AAvBR;AAyBH,WAxC6E;;AAc9E,eAAK,IAAIf,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGJ,KAAK,CAACoB,MAA1B,EAAkChB,CAAC,EAAnC;AAAA;AAAA;;AA2BA,iBAAOJ,KAAP;AACH;;AAEoB,eAAdqB,cAAc,CAAC9C,IAAD,EAAkBE,GAAlB,EAA0CuB,KAA1C,EAA+E;AAChG,cAAIH,MAAwB,GAAG,IAA/B;AACA;AAAA;AAAA,sEAA0BtB,IAA1B,EAAgCE,GAAG,CAACoB,MAApC,EAA6CC,MAAD,IAAY;AACpDD,YAAAA,MAAM,GAAGC,MAAT;AACH,WAFD;;AAGA,cAAI,CAACD,MAAL,EAAa;AACT,mBAAO,KAAP;AACH;;AACD,cAAIpB,GAAG,CAAC0B,UAAJ,CAAeiB,MAAf,IAAyB,CAA7B,EAAgC;AAC5B,mBAAO,IAAP;AACH;;AACD,cAAIE,GAAG,GAAG7C,GAAG,CAAC8C,QAAJ,IAAgB;AAAA;AAAA,wCAAWC,GAA3B,GAAiC,IAAjC,GAAwC,KAAlD;AACAxB,UAAAA,KAAK,QAAL,IAAAA,KAAK,CAAEyB,OAAP,CAAgBC,SAAD,IAAe;AAC1B,gBAAIC,KAAK,GAAG,CAAZ;;AACA,oBAAOD,SAAS,CAACrB,IAAjB;AACI,mBAAK;AAAA;AAAA,4DAAmBuB,SAAxB;AACI;AAAA;;AACI,sBAAIF,SAAS,CAACG,MAAV,CAAiBT,MAAjB,GAA0B,CAA9B,EAAiC;AAC7B;AACH;;AACDO,kBAAAA,KAAK,GAAG,aAAA9B,MAAM,CAAEiC,QAAR,CAAkBC,OAAlB,CAA0BL,SAAS,CAACG,MAAV,CAAiB,CAAjB,CAA1B,+BAAgDzC,KAAhD,KAAyD,CAAjE;AACH;AACD;;AACJ,mBAAK;AAAA;AAAA,4DAAmB4C,QAAxB;AACIL,gBAAAA,KAAK,GAAG9B,MAAM,CAAEoC,KAAR,GAAgBpC,MAAM,CAAEqC,KAAxB,GAAgC,KAAxC;AACA;;AACJ,mBAAK;AAAA;AAAA,4DAAmBC,KAAxB;AACIR,gBAAAA,KAAK,GAAG9B,MAAM,CAAEoC,KAAhB;AACA;;AACJ,mBAAK;AAAA;AAAA,4DAAmBG,KAAxB;AACIT,gBAAAA,KAAK,GAAG9B,MAAM,CAAEqC,KAAhB;AACA;;AACJ,mBAAK;AAAA;AAAA,4DAAmBG,YAAxB;AACIV,gBAAAA,KAAK,GAAG;AAAA;AAAA,wCAAQW,eAAR,CAAwBC,QAAxB,GAAmC1C,MAAM,CAAE2C,QAAnD;AACA;;AACJ,mBAAK;AAAA;AAAA,4DAAmBlC,eAAxB;AACI,oBAAIoB,SAAS;AAAA;AAAA,yDAAb,EAA2C;AACvCC,kBAAAA,KAAK,GAAGD,SAAS,CAAClB,GAAV,GAAc,CAAd,GAAgB,CAAxB;AACH;;AACD;;AACJ,mBAAK;AAAA;AAAA,4DAAmBI,WAAxB;AACI,oBAAIc,SAAS;AAAA;AAAA,qDAAb,EAAyC;AACrCC,kBAAAA,KAAK,GAAG9B,MAAM,CAAEgB,cAAR,GAAyBa,SAAS,CAACe,GAA3C;AACH;;AACL,mBAAK;AAAA;AAAA,4DAAmB3B,YAAxB;AACA,mBAAK;AAAA;AAAA,4DAAmB4B,SAAxB;AACI,oBAAIhB,SAAS;AAAA;AAAA,qDAAb,EAAyC;AACrCC,kBAAAA,KAAK,GAAG9B,MAAM,CAAEkB,YAAR,GAAuBW,SAAS,CAACe,GAAzC;AACH;;AACD;;AACJ,mBAAK;AAAA;AAAA,4DAAmBE,gBAAxB;AACIhB,gBAAAA,KAAK,GAAG9B,MAAM,CAAE+C,UAAhB;AACA;;AACJ,mBAAK;AAAA;AAAA,4DAAmBC,cAAxB;AACIlB,gBAAAA,KAAK,GAAG9B,MAAM,CAAEoB,cAAhB;AACA;;AACJ,mBAAK;AAAA;AAAA,4DAAmB6B,UAAxB;AACInB,gBAAAA,KAAK,GAAG;AAAA;AAAA,wCAAQW,eAAR,CAAwBC,QAAxB,IAAoC,CAApC,GAAwC,CAAxC,GAA4C,CAApD;AACA;;AACJ,mBAAK;AAAA;AAAA,4DAAmBQ,YAAxB;AACI;AACA;;AACJ,mBAAK;AAAA;AAAA,4DAAmB/B,UAAxB;AACI,oBAAIU,SAAS;AAAA;AAAA,qDAAb,EAAyC;AACrCC,kBAAAA,KAAK,GAAG9B,MAAM,CAAEoB,cAAR,GAAyBS,SAAS,CAACe,GAA3C;AACH;;AACL,mBAAK;AAAA;AAAA,4DAAmBvB,SAAxB;AACI,oBAAIQ,SAAS;AAAA;AAAA,qDAAb,EAAyC;AACrCC,kBAAAA,KAAK,GAAG9B,MAAM,CAAEsB,YAAR,GAAuBO,SAAS,CAACe,GAAzC;AACH;;AACD;;AACJ,mBAAK;AAAA;AAAA,4DAAmBO,QAAxB;AACIrB,gBAAAA,KAAK,GAAG;AAAA;AAAA,wCAAQW,eAAR,CAAwBC,QAAhC;AACA;;AACJ,mBAAK;AAAA;AAAA,4DAAmBU,UAAxB;AACI,oBAAIvB,SAAS,CAACG,MAAV,CAAiBT,MAAjB,IAA2B,CAA/B,EAAkC;AAAA;;AAC9B,sBAAM8B,SAAS,GAAGxB,SAAS,CAACG,MAAV,CAAiB,CAAjB,IAAoBH,SAAS,CAACG,MAAV,CAAiB,CAAjB,CAAtC;;AACA,iCAAIhC,MAAJ,aAAI,QAAQsD,KAAZ,EAAmB;AACf,wBAAI3F,IAAI,CAAC4F,eAAL,CAAqBvD,MAAM,CAAEwD,IAAR,CAAaC,QAAlC,EAA4C;AAAA;AAAA,4CAAQC,gBAAR,CAAyBC,SAAzB,CAAoCH,IAApC,CAAyCC,QAArF,KAAkGJ,SAAtG,EAAiH;AAC7GvB,sBAAAA,KAAK;AACR;AACJ,mBAJD,MAIO;AACH;AAAA;AAAA,gFAA0B9B,MAA1B,EAAmC;AAAA;AAAA,kDAAW4D,KAA9C,EAAsD3D,MAAD,IAAY;AAC7D,0BAAIA,MAAM,CAAC4D,MAAP,IAAiBlG,IAAI,CAAC4F,eAAL,CAAqBtD,MAAM,CAACuD,IAAP,CAAYC,QAAjC,EAA2CzD,MAAM,CAAEwD,IAAR,CAAaC,QAAxD,KAAqEJ,SAA1F,EAAqG;AACjGvB,wBAAAA,KAAK;AACR;AACJ,qBAJD;AAKH;AACJ;;AACD;;AACJ,mBAAK;AAAA;AAAA,4DAAmBgC,MAAxB;AACI;AACA;AACA;;AACJ,mBAAK;AAAA;AAAA,4DAAmBC,QAAxB;AACIjC,gBAAAA,KAAK,GAAGnE,IAAI,CAACqG,QAAL,CAAchE,MAAM,CAAEwD,IAAR,CAAaC,QAA3B,EAAqC;AAAA;AAAA,wCAAQC,gBAAR,CAAyBC,SAAzB,CAAoCH,IAApC,CAAyCC,QAA9E,CAAR;AACA;;AACJ,mBAAK;AAAA;AAAA,4DAAmBZ,SAAxB;AACI,oBAAIhB,SAAS;AAAA;AAAA,qDAAb,EAAyC;AACrCC,kBAAAA,KAAK,GAAG9B,MAAM,CAAEkB,YAAR,GAAuBW,SAAS,CAACe,GAAzC;AACH;;AACD;AAvFR;;AA0FA,gBAAIqB,IAAI,GAAG,KAAX;;AACA,oBAAOpC,SAAS,CAACqC,EAAjB;AACI,mBAAK;AAAA;AAAA,4CAAWC,EAAhB;AACIF,gBAAAA,IAAI,GAAGnC,KAAK,IAAID,SAAS,CAACC,KAA1B;AACA;;AACJ,mBAAK;AAAA;AAAA,4CAAWsC,EAAhB;AACIH,gBAAAA,IAAI,GAAGnC,KAAK,IAAID,SAAS,CAACC,KAA1B;AACA;;AACJ,mBAAK;AAAA;AAAA,4CAAWuC,EAAhB;AACIJ,gBAAAA,IAAI,GAAGnC,KAAK,GAAGD,SAAS,CAACC,KAAzB;AACA;;AACJ,mBAAK;AAAA;AAAA,4CAAWwC,EAAhB;AACIL,gBAAAA,IAAI,GAAGnC,KAAK,IAAID,SAAS,CAACC,KAA1B;AACA;;AACJ,mBAAK;AAAA;AAAA,4CAAWyC,EAAhB;AACIN,gBAAAA,IAAI,GAAGnC,KAAK,GAAGD,SAAS,CAACC,KAAzB;AACA;;AACJ,mBAAK;AAAA;AAAA,4CAAW0C,EAAhB;AACIP,gBAAAA,IAAI,GAAGnC,KAAK,IAAID,SAAS,CAACC,KAA1B;AACA;AAlBR;;AAoBA,gBAAIlD,GAAG,CAAC8C,QAAJ,IAAgB;AAAA;AAAA,0CAAWC,GAA/B,EAAoC;AAChCF,cAAAA,GAAG,GAAGA,GAAG,IAAIwC,IAAb;AACH,aAFD,MAEO;AACHxC,cAAAA,GAAG,GAAGA,GAAG,IAAIwC,IAAb;AACH;AACJ,WAtHD;AAwHA,iBAAOxC,GAAP;AACH;;AAEDgD,QAAAA,oBAAoB,GAAG;AACnB,cAAI,KAAK7F,GAAL,CAAS8F,QAAT,IAAqB,CAAC,CAAtB,IAA2B,KAAKtF,IAAL,IAAa,KAAKR,GAAL,CAAS8F,QAArD,EAA+D;AAC3D,mBAAO,IAAP;AACH;;AACD,cAAI,CAAC,KAAK7F,kBAAV,EAA8B;AAC1B,mBAAO,KAAP;AACH;;AACD,iBAAOnB,IAAI,CAAC8D,cAAL,CAAoB,KAAK9C,IAAzB,EAA+B,KAAKG,kBAApC,EAAwD,KAAKE,oBAA7D,CAAP;AACH;;AACD4F,QAAAA,wBAAwB,GAAG;AAAA;;AACvB,wCAAKxF,qBAAL,mCAA4ByC,OAA5B,CAAqCC,SAAD,IAAe;AAC/C,gBAAIA,SAAS;AAAA;AAAA,2CAAb,EAAsC;AAClCA,cAAAA,SAAS,CAAC+C,KAAV;AACH;AACJ,WAJD;AAKH;;AACDC,QAAAA,mBAAmB,CAAC1E,KAAD,EAAsC;AACrDA,UAAAA,KAAK,QAAL,IAAAA,KAAK,CAAEyB,OAAP,CAAgBC,SAAD,IAAe;AAC1B,oBAAOA,SAAS,CAACrB,IAAjB;AACI,mBAAK;AAAA;AAAA,4DAAmBC,eAAxB;AACI,oBAAIoB,SAAS;AAAA;AAAA,yDAAb,EAA2C;AACvC,uBAAKnD,IAAL,CAAUoG,oBAAV,CAA+B;AAAA;AAAA,wDAAerE,eAA9C,EAA+DoB,SAAS,CAACf,EAAzE;AACH;;AACD;AALR;AAOH,WARD;AASH;;AACDiE,QAAAA,QAAQ,GAAG;AACP,eAAKnG,GAAL,CAASoG,OAAT,CAAiBpD,OAAjB,CAA0BqD,WAAD,IAAiB;AACtC;AAAA;AAAA,wEAA0B,KAAKvG,IAA/B,EAAqCuG,WAAW,CAACjF,MAAjD,EAA0DC,MAAD,IAAY;AACjEA,cAAAA,MAAM,CAACiF,gBAAP,CAAwB,IAAxB,EAA8BD,WAA9B;AACH,aAFD;AAGH,WAJD;AAKA,eAAKJ,mBAAL,CAAyB,KAAK9F,oBAA9B;AACH;;AACD6B,QAAAA,MAAM,CAACuE,EAAD,EAAiB;AACnB,eAAK/F,IAAL,IAAa+F,EAAE,GAAG,IAAlB;;AACA,cAAI,KAAKV,oBAAL,EAAJ,EAAiC;AAC7B,iBAAK/F,IAAL,CAAUuD,QAAV,CAAmBmD,UAAnB,CAA8B,IAA9B,EAAoC,KAAKxG,GAAL,CAASD,EAA7C;AACA;AACH;;AACD,cAAI,KAAKK,kBAAL,IAA2BtB,IAAI,CAAC8D,cAAL,CAAoB,KAAK9C,IAAzB,EAA+B,KAAKM,kBAApC,EAAwD,KAAKC,oBAA7D,CAA/B,EAAmH;AAC/G,gBAAI,CAAC,KAAKK,MAAV,EAAkB;AACd,mBAAKV,GAAL,CAASoG,OAAT,CAAiBpD,OAAjB,CAA0BqD,WAAD,IAAiB;AACtC;AAAA;AAAA,4EAA0B,KAAKvG,IAA/B,EAAqCuG,WAAW,CAACjF,MAAjD,EAA0DC,MAAD,IAAY;AACjEA,kBAAAA,MAAM,CAACiF,gBAAP,CAAwB,IAAxB,EAA8BD,WAA9B;AACH,iBAFD;AAGH,eAJD;AAKH;;AACD,iBAAK3F,MAAL,GAAc,IAAd;AACH,WATD,MASO;AACH,gBAAI,KAAKA,MAAT,EAAiB;AACb,mBAAK2F,WAAL;AACH;;AACD,iBAAK3F,MAAL,GAAc,KAAd;AACH;;AAED,cAAK,KAAKV,GAAL,CAASyG,KAAT,GAAiB,CAAjB,IACG,KAAKjG,IAAL,IAAa,CAAC,KAAKC,UAAL,GAAkB,CAAnB,IAAwB,KAAKT,GAAL,CAASyG,KADjD,KAEI,KAAKzG,GAAL,CAASS,UAAT,IAAuB,CAAvB,IAA4B,KAAKA,UAAL,GAAkB,KAAKT,GAAL,CAASS,UAF3D,CAAD,IAGI,KAAKF,qBAAL,IAA8BzB,IAAI,CAAC8D,cAAL,CAAoB,KAAK9C,IAAzB,EAA+B,KAAKQ,mBAApC,EAA0D,KAAKC,qBAA/D,CAHtC,EAKE;AACE,iBAAKE,UAAL;AACA,iBAAKsF,wBAAL;AACA,iBAAKM,WAAL;AACH;AACJ;;AACOA,QAAAA,WAAW,GAAG;AAClB,eAAKrG,GAAL,CAASoG,OAAT,CAAiBpD,OAAjB,CAA0BqD,WAAD,IAAiB;AACtC;AAAA;AAAA,wEAA0B,KAAKvG,IAA/B,EAAqCuG,WAAW,CAACjF,MAAjD,EAA0DC,MAAD,IAAY;AACjEA,cAAAA,MAAM,CAACqF,eAAP,CAAuB,IAAvB,EAA6BL,WAA7B;AACH,aAFD;AAGH,WAJD;AAKH;;AAhSa,O;;AAALvH,MAAAA,I,CAeF8B,K,GAAQ,C", "sourcesContent": ["import { Vec2 } from \"cc\";\r\nimport { BoolOpType, CondOPType, Res<PERSON><PERSON><PERSON>, ResSkillCondition, ResSkillConditionElem, SkillConditionType, TargetType } from \"../../../../autogen/luban/schema\";\r\n\r\nimport { MyApp } from \"../../../../app/MyApp\";\r\n\r\nimport { PlaneEventType } from \"../event/PlaneEventType\";\r\nimport forEachEntityByTargetType from \"./SearchTarget\";\r\nimport type PlaneBase from \"../PlaneBase\";\r\nimport { ExCondition, ExConditionEvent, ExConditionNum } from \"./ExCondition\";\r\nimport { GameIns } from \"../../../GameIns\";\r\n\r\nexport class Buff {\r\n    id: number;\r\n    res: ResBuffer\r\n    removeConditionRes: ResSkillCondition|undefined = undefined\r\n    removeConditionElems: ResSkillConditionElem[]|null = null\r\n    forbinConditionRes: ResSkillCondition|undefined = undefined\r\n    forbinConditionElems: ResSkillConditionElem[]|null = null\r\n    triggerConditionRes: ResSkillCondition|undefined = undefined\r\n    triggerConditionElems: ResSkillConditionElem[]|null = null\r\n    time = 0;\r\n    cycleTimes = 0;\r\n    isOutside: boolean;\r\n    forbin:boolean = false;\r\n    stack:number = 0;\r\n    self:PlaneBase;\r\n    static incID = 1;\r\n    constructor(isOutside: boolean, data: ResBuffer, self:PlaneBase) {\r\n        this.id = Buff.incID++;\r\n        this.res = data;\r\n        this.isOutside = isOutside;\r\n        this.self = self\r\n        if (this.res.removeCondition) {\r\n            this.removeConditionRes = MyApp.lubanTables.TbResSkillCondition.get(this.res.removeCondition);\r\n            this.removeConditionElems = this.conditionRes2Elems(this.removeConditionRes);\r\n        }\r\n        if (this.res.forbinCondition) {\r\n            this.forbinConditionRes = MyApp.lubanTables.TbResSkillCondition.get(this.res.forbinCondition);\r\n            this.forbinConditionElems = this.conditionRes2Elems(this.forbinConditionRes);\r\n        }\r\n        if (this.res.triggerCondition) {\r\n            this.triggerConditionRes = MyApp.lubanTables.TbResSkillCondition.get(this.res.triggerCondition);\r\n            this.triggerConditionElems = this.conditionRes2Elems(this.triggerConditionRes);\r\n        }\r\n    }\r\n\r\n    conditionRes2Elems(res: ResSkillCondition|undefined):ResSkillConditionElem[]|null {\r\n        if (!res) {\r\n            return null;\r\n        }\r\n        let target: PlaneBase | null = null;\r\n        forEachEntityByTargetType(this.self, res.target, (entity:PlaneBase) => {\r\n            target = entity;\r\n        });\r\n        if (!target) {\r\n            return null;\r\n        }\r\n        const validTarget: PlaneBase = target;\r\n\r\n        const elems = Array.from(res.conditions);\r\n        for (let i = 0; i < elems.length; i++) {\r\n            switch (elems[i].type) {\r\n                case SkillConditionType.FatalInjuryHurt:\r\n                    {\r\n                        let elem = new ExConditionEvent(elems[i], () => {\r\n                            elem.has = true;\r\n                            this.update(0)\r\n                        });\r\n                        elems[i] = elem\r\n                        // NOTE FatalInjuryHurt 只支持self， 要不Unregister不好删除\r\n                        this.self.PlaneEventRegister(PlaneEventType.FatalInjuryHurt, elem.cb)\r\n                    } \r\n                    break;\r\n                case SkillConditionType.PickDiamond:\r\n                    elems[i] = new ExConditionNum(elems[i], validTarget.pickDiamondNum);\r\n                    break;\r\n                case SkillConditionType.KillEnemyNum:\r\n                    elems[i] = new ExConditionNum(elems[i], validTarget.killEnemyNum);\r\n                    break;\r\n                case SkillConditionType.UseNuclear:\r\n                    elems[i] = new ExConditionNum(elems[i], validTarget.usedNuclearNum);\r\n                    break;\r\n                case SkillConditionType.UserSuper:\r\n                    elems[i] = new ExConditionNum(elems[i], validTarget.usedSuperNum);\r\n                    break;\r\n            }\r\n        }\r\n        return elems;\r\n    }\r\n\r\n    static checkCondition(self: PlaneBase, res: ResSkillCondition, elems: ResSkillConditionElem[]|null) {\r\n        let target: PlaneBase | null = null;\r\n        forEachEntityByTargetType(self, res.target, (entity) => {\r\n            target = entity;\r\n        });\r\n        if (!target) {\r\n            return false\r\n        }\r\n        if (res.conditions.length == 0) {\r\n            return true;\r\n        }\r\n        let ret = res.boolType == BoolOpType.AND ? true : false;\r\n        elems?.forEach((condition) => {\r\n            let value = 0;\r\n            switch(condition.type) {\r\n                case SkillConditionType.BuffStack:\r\n                    {\r\n                        if (condition.params.length < 1) {\r\n                            break\r\n                        }\r\n                        value = target!.buffComp!.GetBuff(condition.params[0])?.stack || 0;\r\n                    }\r\n                    break\r\n                case SkillConditionType.CurHPPer:\r\n                    value = target!.curHp / target!.maxHp * 10000;\r\n                    break;\r\n                case SkillConditionType.CurHP:\r\n                    value = target!.curHp;\r\n                    break;\r\n                case SkillConditionType.MaxHP:\r\n                    value = target!.maxHp;\r\n                    break;\r\n                case SkillConditionType.BeAttackTime:\r\n                    value = GameIns.gameDataManager.gameTime - target!.hurtTime;\r\n                    break;\r\n                case SkillConditionType.FatalInjuryHurt:\r\n                    if (condition instanceof ExConditionEvent) {\r\n                        value = condition.has?1:0;\r\n                    }\r\n                    break;\r\n                case SkillConditionType.PickDiamond:\r\n                    if (condition instanceof ExConditionNum) {\r\n                        value = target!.pickDiamondNum - condition.num;\r\n                    }\r\n                case SkillConditionType.KillEnemyNum:\r\n                case SkillConditionType.KillEnemy:\r\n                    if (condition instanceof ExConditionNum) {\r\n                        value = target!.killEnemyNum - condition.num;\r\n                    }\r\n                    break;\r\n                case SkillConditionType.RemainNuclearNum:\r\n                    value = target!.nuclearNum;\r\n                    break;\r\n                case SkillConditionType.UsedNuclearNum:\r\n                    value = target!.usedNuclearNum;\r\n                    break;\r\n                case SkillConditionType.LevelStart:\r\n                    value = GameIns.gameDataManager.gameTime == 0 ? 1 : 0;\r\n                    break;\r\n                case SkillConditionType.BossBeKilled:\r\n                    // TODO ybgg\r\n                    break;\r\n                case SkillConditionType.UseNuclear:\r\n                    if (condition instanceof ExConditionNum) {\r\n                        value = target!.usedNuclearNum - condition.num;\r\n                    }\r\n                case SkillConditionType.UserSuper:\r\n                    if (condition instanceof ExConditionNum) {\r\n                        value = target!.usedSuperNum - condition.num;\r\n                    }\r\n                    break;\r\n                case SkillConditionType.GameTime:\r\n                    value = GameIns.gameDataManager.gameTime;\r\n                    break;\r\n                case SkillConditionType.EnemyCount:\r\n                    if (condition.params.length >= 2) {\r\n                        const radiusSqr = condition.params[1]*condition.params[1];\r\n                        if (target?.enemy) {\r\n                            if (Vec2.squaredDistance(target!.node.position, GameIns.mainPlaneManager.mainPlane!.node.position) <= radiusSqr) {\r\n                                value++;\r\n                            }\r\n                        } else {\r\n                            forEachEntityByTargetType(target!, TargetType.Enemy, (entity) => {\r\n                                if (entity.isDead && Vec2.squaredDistance(entity.node.position, target!.node.position) <= radiusSqr) {\r\n                                    value++;\r\n                                }\r\n                            });\r\n                        }\r\n                    }\r\n                    break;\r\n                case SkillConditionType.WaveNo:\r\n                    // TODO ybgg\r\n                    // value = GameIns.battleManager.waveNo;\r\n                    break;\r\n                case SkillConditionType.Distance:\r\n                    value = Vec2.distance(target!.node.position, GameIns.mainPlaneManager.mainPlane!.node.position);\r\n                    break;\r\n                case SkillConditionType.KillEnemy:\r\n                    if (condition instanceof ExConditionNum) {\r\n                        value = target!.killEnemyNum - condition.num;\r\n                    }\r\n                    break;\r\n\r\n            }\r\n            let ret2 = false;\r\n            switch(condition.op) {\r\n                case CondOPType.EQ:\r\n                    ret2 = value == condition.value;\r\n                    break;\r\n                case CondOPType.GE:\r\n                    ret2 = value >= condition.value;\r\n                    break;\r\n                case CondOPType.GT:\r\n                    ret2 = value > condition.value;\r\n                    break;\r\n                case CondOPType.LE:\r\n                    ret2 = value <= condition.value;\r\n                    break;\r\n                case CondOPType.LT:\r\n                    ret2 = value < condition.value;\r\n                    break;\r\n                case CondOPType.NE:\r\n                    ret2 = value != condition.value;\r\n                    break;\r\n            }\r\n            if (res.boolType == BoolOpType.AND) {\r\n                ret = ret && ret2;\r\n            } else {\r\n                ret = ret || ret2;\r\n            }\r\n        })\r\n        \r\n        return ret;\r\n    }\r\n\r\n    checkRemoveCondition() {\r\n        if (this.res.duration != -1 && this.time >= this.res.duration) {\r\n            return true;\r\n        }\r\n        if (!this.removeConditionRes) {\r\n            return false;\r\n        }\r\n        return Buff.checkCondition(this.self, this.removeConditionRes, this.removeConditionElems);\r\n    }\r\n    resetTriggerConditionNum() {\r\n        this.triggerConditionElems?.forEach((condition) => {\r\n            if (condition instanceof ExCondition) {\r\n                condition.reset()\r\n            }\r\n        })\r\n    }\r\n    resetConditionElems(elems: ResSkillConditionElem[]|null) {\r\n        elems?.forEach((condition) => {\r\n            switch(condition.type) {\r\n                case SkillConditionType.FatalInjuryHurt:\r\n                    if (condition instanceof ExConditionEvent) {\r\n                        this.self.PlaneEventUnRegister(PlaneEventType.FatalInjuryHurt, condition.cb)\r\n                    }\r\n                    break;\r\n            }\r\n        })\r\n    }\r\n    onRemove() {\r\n        this.res.effects.forEach((applyEffect) => {\r\n            forEachEntityByTargetType(this.self, applyEffect.target, (entity) => {\r\n                entity.RemoveBuffEffect(this, applyEffect);\r\n            })\r\n        })\r\n        this.resetConditionElems(this.removeConditionElems)\r\n    }\r\n    update(dt:number):void {\r\n        this.time += dt * 1000;\r\n        if (this.checkRemoveCondition()) {\r\n            this.self.buffComp.removeBuff(this, this.res.id);\r\n            return\r\n        }\r\n        if (this.forbinConditionRes && Buff.checkCondition(this.self, this.forbinConditionRes, this.forbinConditionElems)) {\r\n            if (!this.forbin) {\r\n                this.res.effects.forEach((applyEffect) => {\r\n                    forEachEntityByTargetType(this.self, applyEffect.target, (entity) => {\r\n                        entity.RemoveBuffEffect(this, applyEffect);\r\n                    })\r\n                })\r\n            }\r\n            this.forbin = true;\r\n        } else {\r\n            if (this.forbin) {\r\n                this.applyEffect()\r\n            }\r\n            this.forbin = false;\r\n        }\r\n        \r\n        if ((this.res.cycle > 0 &&\r\n                this.time >= (this.cycleTimes + 1) * this.res.cycle &&\r\n                (this.res.cycleTimes == 0 || this.cycleTimes < this.res.cycleTimes))\r\n            || (this.triggerConditionElems && Buff.checkCondition(this.self, this.triggerConditionRes!, this.triggerConditionElems)\r\n            )\r\n        ) {\r\n            this.cycleTimes++;\r\n            this.resetTriggerConditionNum();\r\n            this.applyEffect()\r\n        }\r\n    }\r\n    private applyEffect() {\r\n        this.res.effects.forEach((applyEffect) => {\r\n            forEachEntityByTargetType(this.self, applyEffect.target, (entity) => {\r\n                entity.ApplyBuffEffect(this, applyEffect);\r\n            })\r\n        })\r\n    }\r\n}"]}