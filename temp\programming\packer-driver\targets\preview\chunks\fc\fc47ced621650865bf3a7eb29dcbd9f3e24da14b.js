System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, NodeEventType, Vec3, GameConst, GameIns, GameEnum, _dec, _class, _crd, ccclass, property, Controller;

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "db://assets/scripts/core/base/GameConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEnum(extras) {
    _reporterNs.report("GameEnum", "../../const/GameEnum", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMainPlane(extras) {
    _reporterNs.report("MainPlane", "../plane/mainPlane/MainPlane", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      NodeEventType = _cc.NodeEventType;
      Vec3 = _cc.Vec3;
    }, function (_unresolved_2) {
      GameConst = _unresolved_2.GameConst;
    }, function (_unresolved_3) {
      GameIns = _unresolved_3.GameIns;
    }, function (_unresolved_4) {
      GameEnum = _unresolved_4.GameEnum;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "572fcBk7RlLfpdl/RebWJUY", "Controller", undefined);

      __checkObsolete__(['_decorator', 'Component', 'EventTouch', 'NodeEventType', 'Vec3']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("Controller", Controller = (_dec = ccclass('Controller'), _dec(_class = class Controller extends Component {
        constructor() {
          super(...arguments);
          this.target = null;
          // 目标对象（主飞机）
          this._targetStartPos = new Vec3(0, 0);
        }

        // 目标起始位置

        /**
         * 加载时初始化
         */
        onLoad() {}
        /**
         * 开始时绑定触摸事件
         */


        start() {
          this.node.on(NodeEventType.TOUCH_START, this.onTouchStart, this);
          this.node.on(NodeEventType.TOUCH_END, this.onTouchEnd, this);
          this.node.on(NodeEventType.TOUCH_CANCEL, this.onTouchEnd, this);
          this.node.on(NodeEventType.TOUCH_MOVE, this.onTouchMove, this);
        }
        /**
         * 触摸开始事件
         * @param {Event.EventTouch} event 触摸事件
         */


        onTouchStart(event) {
          var target = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainPlane; // 获取主飞机

          if (!target) {
            return; // 如果主飞机不存在，则不处理
          }

          if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameStateManager.gameState == (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameState.WillOver || (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameStateManager.gameState == (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameState.Over) {
            return;
          }

          this._targetStartPos = target.node.getPosition(); // 记录主飞机的起始位置

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.setTouchState(true);
        }
        /**
         * 触摸移动事件
         * @param {Event.EventTouch} event 触摸事件
         */


        onTouchMove(event) {
          if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameStateManager.gameState != (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameState.Battle) {
            return; // 游戏未进入战斗状态时不处理
          }

          var target = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainPlane; // 获取主飞机

          if (!target) {
            return; // 如果主飞机不存在，则不处理
          }

          var startPos = event.getUIStartLocation(); // UI坐标系（左下角原点）

          var location = event.getUILocation(); // UI坐标系（左下角原点）
          // 直接计算偏移量（UI坐标系）

          var deltaX = location.x - startPos.x;
          var deltaY = location.y - startPos.y; // 计算UI坐标系到战斗坐标系的转换比例

          var uiWidth = (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).designWidth; // UI坐标系宽度 (750)

          var battleWidth = (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).ViewBattleWidth; // 战斗坐标系宽度 (950)

          var scaleFactor = battleWidth / uiWidth; // 将UI偏移量转换为战斗坐标系偏移量

          var battleDeltaX = deltaX * scaleFactor;
          var battleDeltaY = deltaY * scaleFactor; // 应用偏移量到飞机初始位置（战斗坐标系）

          var newX = this._targetStartPos.x + battleDeltaX;
          var newY = this._targetStartPos.y + battleDeltaY;
          target.onControl(newX, newY); // 调试日志
          //logInfo("Controller", `UI坐标: 开始(${startPos.x},${startPos.y}) 当前(${location.x},${location.y})`);
          //logInfo("Controller", `UI偏移量: (${deltaX},${deltaY}), 战斗偏移量: (${battleDeltaX},${battleDeltaY})`);
        }
        /**
         * 触摸结束事件
         * @param {Event.EventTouch} event 触摸事件
         */


        onTouchEnd(event) {
          if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameStateManager.gameState == (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameState.WillOver || (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameStateManager.gameState == (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameState.Over) {
            return;
          }

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.setTouchState(false);
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=fc47ced621650865bf3a7eb29dcbd9f3e24da14b.js.map