import { Label, RichText } from "cc";

class UIToolMgr {
    /**
     * 将秒数格式化为 "时:分:秒" 的字符串
     * @param totalSeconds 总秒数（非负数）
     * @returns 格式化后的时间字符串，如 "00:00:00"
     */
    formatTime(totalSeconds: number): string {
        if (typeof totalSeconds !== 'number' || totalSeconds < 0) {
            return '00:00';
        }
        const hours = Math.floor(totalSeconds / 3600);
        const minutes = Math.floor((totalSeconds % 3600) / 60);
        const seconds = totalSeconds % 60;
        const pad = (num: number) => num.toString().padStart(2, '0');
        if (hours === 0) {
            return `${pad(minutes)}:${pad(seconds)}`;
        }
        return `${pad(hours)}:${pad(minutes)}:${pad(seconds)}`;
    }
    /**
     * 修改富文本原字符串中指定 <color> 标签内的数值
     * @param passText RichText
     * @param index 要修改的 <color> 标签的索引（从 0 开始）
     * @param newValue 新的数值
     */
    modifyColorTag(passText: RichText, index: number, options: { value?: string; color?: string }) {
        if (passText == null || passText.string == null) return;
        const originalString = passText.string;
        const parts = originalString.split(/(<color=[^>]+>)([^<]+)(<\/color>)/g);
        const filteredParts = parts.filter(part => part !== '');
        if (index < 0 || index >= filteredParts.length / 3) {
            throw new Error(`Invalid index: ${index}`);
        }
        const tagIndex = 3 * index;
        const valueIndex = tagIndex + 1;
        if (options.color) {
            filteredParts[tagIndex] = `<color=${options.color}>`;
        }
        if (options.value) {
            filteredParts[valueIndex] = options.value;
        }
        passText.string = filteredParts.join('');
    }
    /**
     * 修改 Label 文本中的数字部分为指定值
     * @param txt 目标 Label 组件
     * @param num 替换的数字（支持 number 或 string 类型）
     */
    modifyNumber(txt: Label | null, num: number | string | null): void {
        if (!txt || num === null || num === undefined) return;
        const replacement = num.toString();
        if (txt.string.match(/\d+/g)) {
            txt.string = txt.string.replace(/\d+/g, replacement);
        }
    }
}
export const UITools = new UIToolMgr();