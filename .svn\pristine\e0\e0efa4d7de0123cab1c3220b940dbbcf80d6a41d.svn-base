import { _decorator, CCFloat, Component, Vec3, view } from "cc";
import { logInfo } from "db://assets/scripts/utils/Logger";
import { GameConst } from "../../../../../scripts/core/base/GameConst";
import { GameIns } from "../GameIns";
import { GameEnum } from "../const/GameEnum";

const { ccclass, property } = _decorator;

@ccclass('CameraMove')
export class CameraMove extends Component {

    /* 参数组合效果示例
     * 场景	    followFactor  maxMoveSpeed  摄像机行为
     * 平滑跟随	0.3	          100	        平滑移动，不会突然加速
     * 快速响应	0.7	          200	        快速接近目标，但不会跳跃
     * 精确控制	0.5	          80	        平衡响应速度和平滑度
     * 电影效果	0.2	          50	        非常平滑的移动，适合过场动画
    */

    @property(CCFloat)
    public followFactor = 0.7; // 跟随比例因子 (0.0-1.0)

    @property(CCFloat)
    public maxMoveSpeed = 200; // 摄像机最大移动速度（单位：世界单位/秒）

    private _screenRatio: number = 1;
    private _battleWidth: number = 0;

    private _currentPosition: Vec3 = new Vec3(); // 当前位置
    private _targetPosition: Vec3 = new Vec3(); // 目标位置

    start() {
        // 初始化位置
        this.node.getPosition(this._currentPosition);
        this._targetPosition.set(this._currentPosition);
        this._calculateScreenRatio();
    }

    update(deltaTime: number) {
        if (GameIns.gameStateManager.gameState != GameEnum.GameState.Battle) {
            return; // 游戏未进入战斗状态时不处理
        }

        let mainPlane = GameIns.mainPlaneManager.mainPlane;
        if (!mainPlane) return;

        // 获取飞机位置
        const planePos = mainPlane.node.position;

        // 计算摄像机应该跟随的目标位置
        const targetX = this.calculateTargetPosition(planePos.x);
        this._targetPosition.set(this._currentPosition);
        this._targetPosition.x = targetX;

        // 平滑移动摄像机（考虑最大移动速度）
        this.smoothMoveCamera(deltaTime);
    }

    /**
     * 平滑移动摄像机（考虑最大移动速度）
     * @param deltaTime 帧时间（秒）
     */
    private smoothMoveCamera(deltaTime: number) {
        // 获取当前位置
        this.node.getPosition(this._currentPosition);

        // 计算当前位置到目标位置的距离
        const distanceToTarget = this._targetPosition.x - this._currentPosition.x;

        // 计算期望移动距离（基于平滑因子）
        const desiredMove = distanceToTarget * this.followFactor;

        // 计算最大允许移动距离（基于最大速度）
        const maxMove = this.maxMoveSpeed * deltaTime;

        // 应用移动速度限制
        let actualMove = desiredMove;
        if (Math.abs(desiredMove) > maxMove) {
            actualMove = Math.sign(desiredMove) * maxMove;
        }

        // 计算新位置
        const newX = this._currentPosition.x + actualMove;

        // 设置新位置（只修改X轴）
        this._currentPosition.x = newX;
        this.node.setPosition(this._currentPosition);
    }

    /**
     * 计算摄像机目标位置
     * @param planeX 飞机X坐标
     * @returns 摄像机目标X坐标
     */
    private calculateTargetPosition(planeX: number): number {
        // 计算飞机在战斗区域中的位置比例
        const relativeX = planeX - (-this._battleWidth / 2); // 左边界为 -battleWidth/2

        // 计算摄像机偏移比例 (0.0 到 1.0)
        let cameraRatio = relativeX / this._battleWidth;

        // 限制在 0.0 到 1.0 范围内
        cameraRatio = Math.max(0.0, Math.min(1.0, cameraRatio));

        // 返回摄像机目标位置（-100% 到 100%）
        return (cameraRatio * 200) - 100;
    }

    /**
     * 设置摄像机位置
     * @param targetX 目标X坐标
     * @param planeX 飞机X坐标（用于日志记录）
     */
    private setCameraPosition(targetX: number, planeX: number) {
        // 获取当前位置
        this.node.getPosition(this._currentPosition);

        // 计算新位置（按比例平滑移动）
        const newX = this._currentPosition.x * (1 - this.followFactor) +
            targetX * this.followFactor;

        // 设置新位置（只修改X轴）
        this._currentPosition.x = newX;
        this.node.setPosition(this._currentPosition);
        // logInfo('CameraMove', `摄像机当前位置: ${this._currentPosition.x} name: ${this.node.name}`);

        // // 调试日志（现在可以正确使用 planeX）
        // logInfo('CameraMove', ` 飞机位置:${planeX}, 目标位置:${targetX}, 新位置:${newX}`);
    }
    /**
     * 强制移动摄像机到目标位置
     */
    public forceMoveToTarget() {
        this.node.setPosition(this._targetPosition);
        this._currentPosition.set(this._targetPosition);
    }

    /**
     * 重置摄像机状态
     */
    public resetCamera() {
        // 重置位置到初始状态
        this.node.setPosition(0, 0, 0);
        this._currentPosition.set(0, 0, 0);
        this._targetPosition.set(0, 0, 0);
    }

    /**
     * 计算屏幕适配比例
     */
    private _calculateScreenRatio() {
        const visibleSize = view.getVisibleSize();
        this._screenRatio = visibleSize.width / GameConst.designWidth;

        // 计算实际战斗宽度（考虑宽屏适配）
        this._battleWidth = GameConst.ViewBattleWidth * this._screenRatio;
        logInfo('CameraMove', `屏幕比例: ${this._screenRatio}, 战斗宽度: ${this._battleWidth}`);
    }
}