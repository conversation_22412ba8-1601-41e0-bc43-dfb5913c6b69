{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts"], "names": ["_decorator", "MyApp", "BundleName", "IBundleEntry", "ResManager", "UIMgr", "logDebug", "EventMgr", "HomeUIEvent", "FriendUI", "DevLoginUI", "BottomTab", "BottomUI", "HomeUI", "TopUI", "MailUI", "PKUI", "PlaneUI", "ShopUI", "SkyIslandUI", "StoryUI", "TalentUI", "TTFUtils", "ccclass", "CommonEntry", "initEntry", "args", "GetInstance", "init", "getInstance", "CUSTOM_TYPE", "TEXT", "instance", "loadBundle", "<PERSON><PERSON>", "lubanMgr", "load", "loadUI", "resMgr", "Home", "openUI", "on", "Leave", "closeUI", "emit", "BottomTabRegister", "get", "preloadHomeSubBundles", "HomePlane", "then", "Plane", "HomeTalent", "Talent", "HomeShop", "Shop", "HomeSkyIsland", "SkyIsLand", "HomeFriend", "HomeMail", "HomePK", "HomeStory"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,Q,kBAAAA,Q;;AACAC,MAAAA,U,kBAAAA,U;;AACAC,MAAAA,S,kBAAAA,S;;AACAC,MAAAA,Q,kBAAAA,Q;;AACAC,MAAAA,M,kBAAAA,M;;AACAC,MAAAA,K,kBAAAA,K;;AACAC,MAAAA,M,kBAAAA,M;;AACAC,MAAAA,I,kBAAAA,I;;AACAC,MAAAA,O,kBAAAA,O;;AACAC,MAAAA,M,kBAAAA,M;;AACAC,MAAAA,W,kBAAAA,W;;AACAC,MAAAA,O,kBAAAA,O;;AACAC,MAAAA,Q,kBAAAA,Q;;AACAC,MAAAA,Q,kBAAAA,Q;;;;;;;;;OACH;AAAEC,QAAAA;AAAF,O,GAAcvB,U;;6BAEPwB,W,WADZD,OAAO,CAAC,aAAD,C,gBAAR,MACaC,WADb;AAAA;AAAA,wCAC8C;AACpB,cAATC,SAAS,CAAC,GAAGC,IAAJ,EAAgC;AAClD;AAAA;AAAA,oCAAS,aAAT,EAAwB,WAAxB;AACA;AAAA;AAAA,8BAAMC,WAAN,GAAoBC,IAApB;AACA;AAAA;AAAA,oCAASC,WAAT,GAAuBD,IAAvB,CAA4B;AAAA;AAAA,oCAASE,WAAT,CAAqBC,IAAjD,EAAsD,eAAtD;AACA,gBAAM;AAAA;AAAA,wCAAWC,QAAX,CAAoBC,UAApB,CAA+B;AAAA;AAAA,wCAAWC,KAA1C,CAAN,CAJkD,CAIK;;AACvD,gBAAM;AAAA;AAAA,8BAAMC,QAAN,CAAeC,IAAf,EAAN;AACA,gBAAM;AAAA;AAAA,8BAAMC,MAAN;AAAA;AAAA,uCAAN;AACA,gBAAM;AAAA;AAAA,8BAAMC,MAAN,CAAaL,UAAb,CAAwB;AAAA;AAAA,wCAAWM,IAAnC,CAAN;AACA,gBAAM;AAAA;AAAA,8BAAMC,MAAN;AAAA;AAAA,+BAAN;AACA,gBAAM;AAAA;AAAA,8BAAMA,MAAN;AAAA;AAAA,mCAAN;AACA,gBAAM;AAAA;AAAA,8BAAMA,MAAN;AAAA;AAAA,6BAAN,CAVkD,CAWlD;;AACA;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,0CAAYC,KAAxB,EAA+B,MAAM;AACjC;AAAA;AAAA,gCAAMC,OAAN;AAAA;AAAA;AACA;AAAA;AAAA,gCAAMA,OAAN;AAAA;AAAA,sCAFiC,CAET;;AACxB;AAAA;AAAA,gCAAMA,OAAN;AAAA;AAAA;AACH,WAJD;AAKA;AAAA;AAAA,oCAASC,IAAT,CAAc;AAAA;AAAA,0CAAYC,iBAA1B,EAA6C;AAAA;AAAA,sCAAUN,IAAvD,EAA6D;AAAA;AAAA,8BAAMO,GAAN;AAAA;AAAA,+BAA7D;AACA,eAAKC,qBAAL;AACH;;AAEOA,QAAAA,qBAAqB,GAAG;AAC5B;AACA;AAAA;AAAA,8BAAMT,MAAN,CAAaL,UAAb,CAAwB;AAAA;AAAA,wCAAWe,SAAnC,EAA8CC,IAA9C,CAAmD,YAAY;AAC3D,kBAAM;AAAA;AAAA,gCAAMZ,MAAN;AAAA;AAAA,mCAAN;AACA;AAAA;AAAA,sCAASO,IAAT,CAAc;AAAA;AAAA,4CAAYC,iBAA1B,EAA6C;AAAA;AAAA,wCAAUK,KAAvD,EAA8D;AAAA;AAAA,gCAAMJ,GAAN;AAAA;AAAA,mCAA9D;AACH,WAHD;AAIA;AAAA;AAAA,8BAAMR,MAAN,CAAaL,UAAb,CAAwB;AAAA;AAAA,wCAAWkB,UAAnC,EAA+CF,IAA/C,CAAoD,YAAY;AAC5D,kBAAM;AAAA;AAAA,gCAAMZ,MAAN;AAAA;AAAA,qCAAN;AACA;AAAA;AAAA,sCAASO,IAAT,CAAc;AAAA;AAAA,4CAAYC,iBAA1B,EAA6C;AAAA;AAAA,wCAAUO,MAAvD,EAA+D;AAAA;AAAA,gCAAMN,GAAN;AAAA;AAAA,qCAA/D;AACH,WAHD;AAIA;AAAA;AAAA,8BAAMR,MAAN,CAAaL,UAAb,CAAwB;AAAA;AAAA,wCAAWoB,QAAnC,EAA6CJ,IAA7C,CAAkD,YAAY;AAC1D,kBAAM;AAAA;AAAA,gCAAMZ,MAAN;AAAA;AAAA,iCAAN;AACA;AAAA;AAAA,sCAASO,IAAT,CAAc;AAAA;AAAA,4CAAYC,iBAA1B,EAA6C;AAAA;AAAA,wCAAUS,IAAvD,EAA6D;AAAA;AAAA,gCAAMR,GAAN;AAAA;AAAA,iCAA7D;AACH,WAHD;AAIA;AAAA;AAAA,8BAAMR,MAAN,CAAaL,UAAb,CAAwB;AAAA;AAAA,wCAAWsB,aAAnC,EAAkDN,IAAlD,CAAuD,YAAY;AAC/D,kBAAM;AAAA;AAAA,gCAAMZ,MAAN;AAAA;AAAA,2CAAN;AACA;AAAA;AAAA,sCAASO,IAAT,CAAc;AAAA;AAAA,4CAAYC,iBAA1B,EAA6C;AAAA;AAAA,wCAAUW,SAAvD,EAAkE;AAAA;AAAA,gCAAMV,GAAN;AAAA;AAAA,2CAAlE;AACH,WAHD;AAIA;AAAA;AAAA,8BAAMR,MAAN,CAAaL,UAAb,CAAwB;AAAA;AAAA,wCAAWwB,UAAnC,EAA+CR,IAA/C,CAAoD,YAAY;AAC5D,kBAAM;AAAA;AAAA,gCAAMZ,MAAN;AAAA;AAAA,qCAAN;AACH,WAFD;AAGA;AAAA;AAAA,8BAAMC,MAAN,CAAaL,UAAb,CAAwB;AAAA;AAAA,wCAAWyB,QAAnC,EAA6CT,IAA7C,CAAkD,YAAY;AAC1D,kBAAM;AAAA;AAAA,gCAAMZ,MAAN;AAAA;AAAA,iCAAN;AACH,WAFD;AAGA;AAAA;AAAA,8BAAMC,MAAN,CAAaL,UAAb,CAAwB;AAAA;AAAA,wCAAW0B,MAAnC,EAA2CV,IAA3C,CAAgD,YAAY;AACxD,kBAAM;AAAA;AAAA,gCAAMZ,MAAN;AAAA;AAAA,6BAAN;AACH,WAFD;AAGA;AAAA;AAAA,8BAAMC,MAAN,CAAaL,UAAb,CAAwB;AAAA;AAAA,wCAAW2B,SAAnC,EAA8CX,IAA9C,CAAmD,YAAY;AAC3D,kBAAM;AAAA;AAAA,gCAAMZ,MAAN;AAAA;AAAA,mCAAN;AACH,WAFD;AAGH;;AApDyC,O", "sourcesContent": ["import { _decorator } from \"cc\";\nimport { MyApp } from \"db://assets/bundles/common/script/app/MyApp\";\nimport { BundleName } from \"db://assets/bundles/common/script/const/BundleConst\";\nimport { IBundleEntry } from \"db://assets/scripts/core/base/Bundle\";\nimport { ResManager } from \"db://assets/scripts/core/base/ResManager\";\nimport { UIMgr } from \"db://assets/scripts/core/base/UIMgr\";\nimport { logDebug } from \"db://assets/scripts/utils/Logger\";\nimport { EventMgr } from \"./event/EventManager\";\nimport { HomeUIEvent } from \"./event/HomeUIEvent\";\nimport { FriendUI } from \"./ui/friend/FriendUI\";\nimport { DevLoginUI } from \"./ui/gameui/DevLoginUI\";\nimport { BottomTab } from \"./ui/home/<USER>\";\nimport { BottomUI } from \"./ui/home/<USER>\";\nimport { HomeUI } from \"./ui/home/<USER>\";\nimport { TopUI } from \"./ui/home/<USER>\";\nimport { MailUI } from \"./ui/mail/MailUI\";\nimport { PKUI } from \"./ui/pk/PKUI\";\nimport { PlaneUI } from \"./ui/plane/PlaneUI\";\nimport { ShopUI } from \"./ui/shop/ShopUI\";\nimport { SkyIslandUI } from \"./ui/skyisland/SkyIslandUI\";\nimport { StoryUI } from \"./ui/story/StoryUI\";\nimport { TalentUI } from \"./ui/talent/TalentUI\";\nimport { TTFUtils } from \"./utils/TTFUtils\";\nconst { ccclass } = _decorator;\n@ccclass('CommonEntry')\nexport class CommonEntry extends IBundleEntry {\n    public async initEntry(...args: any[]): Promise<void> {\n        logDebug(\"CommonEntry\", \"initEntry\")\n        MyApp.GetInstance().init();\n        TTFUtils.getInstance().init(TTFUtils.CUSTOM_TYPE.TEXT,\"font/gamefont\")\n        await ResManager.instance.loadBundle(BundleName.Luban) //优先加载完配置\n        await MyApp.lubanMgr.load();\n        await UIMgr.loadUI(DevLoginUI)\n        await MyApp.resMgr.loadBundle(BundleName.Home)\n        await UIMgr.openUI(HomeUI)\n        await UIMgr.openUI(BottomUI)\n        await UIMgr.openUI(TopUI)\n        //暂时这样 后面再优化\n        EventMgr.on(HomeUIEvent.Leave, () => {\n            UIMgr.closeUI(HomeUI)\n            UIMgr.closeUI(BottomUI) //这里会把下面的主界面都关闭  \n            UIMgr.closeUI(TopUI)\n        })\n        EventMgr.emit(HomeUIEvent.BottomTabRegister, BottomTab.Home, UIMgr.get(HomeUI))\n        this.preloadHomeSubBundles();\n    }\n\n    private preloadHomeSubBundles() {\n        //异步加载其他bundle\n        MyApp.resMgr.loadBundle(BundleName.HomePlane).then(async () => {\n            await UIMgr.loadUI(PlaneUI)\n            EventMgr.emit(HomeUIEvent.BottomTabRegister, BottomTab.Plane, UIMgr.get(PlaneUI))\n        })\n        MyApp.resMgr.loadBundle(BundleName.HomeTalent).then(async () => {\n            await UIMgr.loadUI(TalentUI)\n            EventMgr.emit(HomeUIEvent.BottomTabRegister, BottomTab.Talent, UIMgr.get(TalentUI))\n        })\n        MyApp.resMgr.loadBundle(BundleName.HomeShop).then(async () => {\n            await UIMgr.loadUI(ShopUI)\n            EventMgr.emit(HomeUIEvent.BottomTabRegister, BottomTab.Shop, UIMgr.get(ShopUI))\n        })\n        MyApp.resMgr.loadBundle(BundleName.HomeSkyIsland).then(async () => {\n            await UIMgr.loadUI(SkyIslandUI)\n            EventMgr.emit(HomeUIEvent.BottomTabRegister, BottomTab.SkyIsLand, UIMgr.get(SkyIslandUI))\n        })\n        MyApp.resMgr.loadBundle(BundleName.HomeFriend).then(async () => {\n            await UIMgr.loadUI(FriendUI)\n        })\n        MyApp.resMgr.loadBundle(BundleName.HomeMail).then(async () => {\n            await UIMgr.loadUI(MailUI)\n        })\n        MyApp.resMgr.loadBundle(BundleName.HomePK).then(async () => {\n            await UIMgr.loadUI(PKUI)\n        })\n        MyApp.resMgr.loadBundle(BundleName.HomeStory).then(async () => {\n            await UIMgr.loadUI(StoryUI)\n        })\n    }\n}"]}