{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/planeview/PlaneView.ts"], "names": ["_decorator", "Component", "sp", "<PERSON><PERSON>", "Graphics", "LevelEditorUtils", "ccclass", "property", "executeInEditMode", "PlaneView", "type", "SkeletonData", "displayName", "tooltip", "_skeletonData", "_animSpeed", "spine", "currentAnimIndex", "cycle", "planeNode", "skeletonData", "value", "animSpeed", "timeScale", "onLoad", "node", "<PERSON><PERSON><PERSON><PERSON><PERSON>y<PERSON><PERSON>", "initSpine", "start", "playNode", "getComponentsInChildren", "for<PERSON>ach", "button", "on", "EventType", "CLICK", "playAnimation", "name", "g", "getChildByName", "getComponent", "moveTo", "lineTo", "close", "stroke", "getOrAddComp", "Skeleton", "setCompleteListener", "trackEntry", "console", "log", "animation", "anim<PERSON><PERSON>", "warn", "setAnimation", "error", "stopAnimation", "clearTracks", "pauseResumeAnimation", "paused", "setAnimationCycle", "get<PERSON>urrent", "downAnimationSpeed", "Math", "max", "upAnimationSpeed", "min", "getAvailableAnimations", "animations", "getRuntimeData", "i", "length", "push"], "mappings": ";;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,E,OAAAA,E;AAAkCC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,Q,OAAAA,Q;;AACvEC,MAAAA,gB,iBAAAA,gB;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CR,U;;2BAIpCS,S,WAFZH,OAAO,CAAC,WAAD,C,UACPE,iBAAiB,CAAC,IAAD,C,UAIbD,QAAQ,CAAC;AACNG,QAAAA,IAAI,EAAER,EAAE,CAACS,YADH;AAENC,QAAAA,WAAW,EAAE,WAFP;AAGNC,QAAAA,OAAO,EAAE;AAHH,OAAD,C,0CALb,MAEaJ,SAFb,SAE+BR,SAF/B,CAEyC;AAAA;AAAA;AAAA,eAiBrCa,aAjBqC,GAiBG,IAjBH;AAAA,eAmB7BC,UAnB6B,GAmBR,GAnBQ;AA2BrC;AA3BqC,eA4B7BC,KA5B6B,GA4BD,IA5BC;AAAA,eA6B7BC,gBA7B6B,GA6BF,CA7BE;AAAA,eA8B7BC,KA9B6B,GA8BZ,IA9BY;AAAA,eA+B7BC,SA/B6B,GA+BJ,IA/BI;AAAA;;AAErC;AAMgB,YAAZC,YAAY,GAAG;AACf,iBAAO,KAAKN,aAAZ;AACH;;AACe,YAAZM,YAAY,CAACC,KAAD,EAAgC;AAC5C,eAAKP,aAAL,GAAqBO,KAArB;;AACA,cAAI,KAAKL,KAAT,EAAgB;AACZ,iBAAKA,KAAL,CAAWI,YAAX,GAA0BC,KAA1B;AACH;AACJ;;AAIoB,YAATC,SAAS,CAACD,KAAD,EAAgB;AACjC,eAAKN,UAAL,GAAkBM,KAAlB;;AACA,cAAI,KAAKL,KAAT,EAAgB;AACZ,iBAAKA,KAAL,CAAWO,SAAX,GAAuBF,KAAvB;AACH;AACJ;;AAQDG,QAAAA,MAAM,GAAG;AACL,eAAKL,SAAL,GAAiB,KAAKM,IAAL,CAAUC,cAAV,CAAyB,OAAzB,CAAjB;AACA,eAAKC,SAAL;AACH;;AAEDC,QAAAA,KAAK,GAAG;AACJ,cAAIC,QAAQ,GAAG,KAAKJ,IAAL,CAAUC,cAAV,CAAyB,SAAzB,CAAf;AACAG,UAAAA,QAAQ,CAAEC,uBAAV,CAAkC3B,MAAlC,EAA0C4B,OAA1C,CAAkDC,MAAM,IAAI;AACxDA,YAAAA,MAAM,CAACP,IAAP,CAAYQ,EAAZ,CAAe9B,MAAM,CAAC+B,SAAP,CAAiBC,KAAhC,EAAuC,MAAI;AACvC,mBAAKC,aAAL,CAAmBJ,MAAM,CAACP,IAAP,CAAYY,IAA/B;AACH,aAFD;AAGH,WAJD;AAKA,cAAMC,CAAC,GAAG,KAAKnB,SAAL,CAAgBoB,cAAhB,CAA+B,GAA/B,EAAqCC,YAArC,CAAkDpC,QAAlD,CAAV;AACAkC,UAAAA,CAAC,CAACG,MAAF,CAAS,CAAC,EAAV,EAAc,CAAd;AACAH,UAAAA,CAAC,CAACI,MAAF,CAAS,EAAT,EAAa,CAAb;AACAJ,UAAAA,CAAC,CAACI,MAAF,CAAS,EAAT,EAAa,GAAb;AACAJ,UAAAA,CAAC,CAACI,MAAF,CAAS,CAAC,EAAV,EAAc,GAAd;AACAJ,UAAAA,CAAC,CAACK,KAAF;AACAL,UAAAA,CAAC,CAACM,MAAF;AACH;AAED;AACJ;AACA;;;AACYjB,QAAAA,SAAS,GAAG;AAChB;AACA,eAAKX,KAAL,GAAa;AAAA;AAAA,oDAAiB6B,YAAjB,CAA8B,KAAK1B,SAAnC,EAA+CjB,EAAE,CAAC4C,QAAlD,CAAb,CAFgB,CAIhB;;AACA,cAAI,KAAK1B,YAAT,EAAuB;AACnB,iBAAKJ,KAAL,CAAWI,YAAX,GAA0B,KAAKA,YAA/B;AACH,WAPe,CAShB;;;AACA,eAAKJ,KAAL,CAAWO,SAAX,GAAuB,KAAKR,UAA5B,CAVgB,CAYhB;;AACA,eAAKC,KAAL,CAAW+B,mBAAX,CAAgCC,UAAD,IAAgB;AAAA;;AAC3CC,YAAAA,OAAO,CAACC,GAAR,sEAAuBF,UAAU,CAACG,SAAlC,qBAAuB,sBAAsBd,IAA7C;AACH,WAFD;AAGH;AAED;AACJ;AACA;AACA;AACA;;;AACID,QAAAA,aAAa,CAACgB,QAAD,EAAmB;AAC5B,cAAI,CAAC,KAAKpC,KAAV,EAAiB;AACbiC,YAAAA,OAAO,CAACI,IAAR,CAAa,aAAb;AACA;AACH;;AAED,cAAI,CAACD,QAAL,EAAe;AACXH,YAAAA,OAAO,CAACI,IAAR,CAAa,QAAb;AACA;AACH;;AAED,eAAKtC,UAAL,GAAkB,GAAlB;;AACA,cAAI;AACA,iBAAKC,KAAL,CAAWsC,YAAX,CAAwB,CAAxB,EAA2BF,QAA3B,EAAqC,KAAKlC,KAA1C;AACA+B,YAAAA,OAAO,CAACC,GAAR,gCAAqBE,QAArB,wBAAsC,KAAKlC,KAA3C;AACH,WAHD,CAGE,OAAOqC,KAAP,EAAc;AACZN,YAAAA,OAAO,CAACM,KAAR,4CAAyBH,QAAzB,EAAqCG,KAArC;AACH;AACJ;AAED;AACJ;AACA;;;AACIC,QAAAA,aAAa,GAAG;AACZ,cAAI,KAAKxC,KAAT,EAAgB;AACZ,iBAAKA,KAAL,CAAWyC,WAAX;AACAR,YAAAA,OAAO,CAACC,GAAR,CAAY,MAAZ;AACH;AACJ;AAED;AACJ;AACA;;;AACIQ,QAAAA,oBAAoB,GAAG;AACnB,cAAI,KAAK1C,KAAT,EAAgB;AACZ,iBAAKA,KAAL,CAAW2C,MAAX,GAAoB,CAAC,KAAK3C,KAAL,CAAW2C,MAAhC;AACAV,YAAAA,OAAO,CAACC,GAAR,CAAY,KAAKlC,KAAL,CAAW2C,MAAX,GAAoB,MAApB,GAA6B,MAAzC;AACH;AACJ;;AAEDC,QAAAA,iBAAiB,GAAG;AAChB,eAAK1C,KAAL,GAAa,CAAC,KAAKA,KAAnB;;AACA,cAAI,KAAKF,KAAT,EAAgB;AAAA;;AACZ,iBAAKA,KAAL,CAAWsC,YAAX,CAAwB,CAAxB,EAA2B,+BAAKtC,KAAL,CAAW6C,UAAX,CAAsB,CAAtB,6DAA0BV,SAA1B,2CAAqCd,IAArC,KAA6C,EAAxE,EAA4E,KAAKnB,KAAjF;AACH;AACJ;;AAED4C,QAAAA,kBAAkB,GAAG;AACjB,eAAKxC,SAAL,GAAiByC,IAAI,CAACC,GAAL,CAAS,KAAKjD,UAAL,GAAkB,CAA3B,EAA8B,KAA9B,CAAjB;AACH;;AACDkD,QAAAA,gBAAgB,GAAG;AACf,eAAK3C,SAAL,GAAiByC,IAAI,CAACG,GAAL,CAAS,KAAKnD,UAAL,GAAkB,CAA3B,EAA8B,CAA9B,CAAjB;AACH;AAED;AACJ;AACA;;;AACIoD,QAAAA,sBAAsB,GAAa;AAC/B,cAAI,CAAC,KAAKnD,KAAN,IAAe,CAAC,KAAKA,KAAL,CAAWI,YAA/B,EAA6C;AACzC,mBAAO,EAAP;AACH;;AAED,cAAMgD,UAAoB,GAAG,EAA7B;AACA,cAAMhD,YAAY,GAAG,KAAKJ,KAAL,CAAWI,YAAX,CAAwBiD,cAAxB,EAArB;;AACA,cAAIjD,YAAY,IAAIA,YAAY,CAACgD,UAAjC,EAA6C;AACzC,iBAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGlD,YAAY,CAACgD,UAAb,CAAwBG,MAA5C,EAAoDD,CAAC,EAArD,EAAyD;AACrDF,cAAAA,UAAU,CAACI,IAAX,CAAgBpD,YAAY,CAACgD,UAAb,CAAwBE,CAAxB,EAA2BjC,IAA3C;AACH;AACJ;;AACD,iBAAO+B,UAAP;AACH;;AAtJoC,O", "sourcesContent": ["import { _decorator, Component, Node, sp, CCString, CCFloat, CCBoolean, Button, Graphics } from 'cc';\r\nimport { LevelEditorUtils } from '../level/utils';\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\n\r\n@ccclass('PlaneView')\r\n@executeInEditMode(true)\r\nexport class PlaneView extends Component {\r\n\r\n    // ========== Spine相关属性 ==========\r\n    @property({\r\n        type: sp.SkeletonData,\r\n        displayName: \"Spine骨骼数据\",\r\n        tooltip: \"拖拽Spine骨骼数据到这里\"\r\n    })\r\n    get skeletonData() {\r\n        return this._skeletonData;\r\n    }\r\n    set skeletonData(value: sp.SkeletonData | null) {\r\n        this._skeletonData = value;\r\n        if (this.spine) {\r\n            this.spine.skeletonData = value;\r\n        }\r\n    }\r\n    _skeletonData: sp.SkeletonData | null = null;\r\n\r\n    private _animSpeed: number = 1.0;\r\n    private set animSpeed(value: number) {\r\n        this._animSpeed = value;\r\n        if (this.spine) {\r\n            this.spine.timeScale = value;\r\n        }\r\n    }\r\n\r\n    // ========== 私有属性 ==========\r\n    private spine: sp.Skeleton | null = null;\r\n    private currentAnimIndex: number = 0;\r\n    private cycle: boolean = true;\r\n    private planeNode: Node | null = null;\r\n\r\n    onLoad() {\r\n        this.planeNode = this.node.getChildByPath(\"plane\");\r\n        this.initSpine();\r\n    }\r\n\r\n    start() {\r\n        let playNode = this.node.getChildByPath(\"ui/play\")\r\n        playNode!.getComponentsInChildren(Button).forEach(button => {\r\n            button.node.on(Button.EventType.CLICK, ()=>{\r\n                this.playAnimation(button.node.name)\r\n            });\r\n        });\r\n        const g = this.planeNode!.getChildByName(\"g\")!.getComponent(Graphics)!;\r\n        g.moveTo(-64, 0);\r\n        g.lineTo(64, 0);\r\n        g.lineTo(64, 128);\r\n        g.lineTo(-64, 128);\r\n        g.close();\r\n        g.stroke();\r\n    }\r\n\r\n    /**\r\n     * 初始化Spine组件\r\n     */\r\n    private initSpine() {\r\n        // 获取或创建Spine组件\r\n        this.spine = LevelEditorUtils.getOrAddComp(this.planeNode!, sp.Skeleton);\r\n\r\n        // 设置骨骼数据\r\n        if (this.skeletonData) {\r\n            this.spine.skeletonData = this.skeletonData;\r\n        }\r\n\r\n        // 设置播放速度\r\n        this.spine.timeScale = this._animSpeed;\r\n\r\n        // 设置动画完成监听\r\n        this.spine.setCompleteListener((trackEntry) => {\r\n            console.log(`动画播放完成: ${trackEntry.animation?.name}`);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * 播放指定动画\r\n     * @param animName 动画名称\r\n     * @param loop 是否循环\r\n     */\r\n    playAnimation(animName: string) {\r\n        if (!this.spine) {\r\n            console.warn(\"Spine组件未初始化\");\r\n            return;\r\n        }\r\n\r\n        if (!animName) {\r\n            console.warn(\"动画名称为空\");\r\n            return;\r\n        }\r\n\r\n        this._animSpeed = 1.0;\r\n        try {\r\n            this.spine.setAnimation(0, animName, this.cycle);\r\n            console.log(`播放动画: ${animName}, 循环: ${this.cycle}`);\r\n        } catch (error) {\r\n            console.error(`播放动画失败: ${animName}`, error);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 停止当前动画\r\n     */\r\n    stopAnimation() {\r\n        if (this.spine) {\r\n            this.spine.clearTracks();\r\n            console.log(\"停止动画\");\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 暂停/恢复动画\r\n     */\r\n    pauseResumeAnimation() {\r\n        if (this.spine) {\r\n            this.spine.paused = !this.spine.paused;\r\n            console.log(this.spine.paused ? \"暂停动画\" : \"恢复动画\");\r\n        }\r\n    }\r\n\r\n    setAnimationCycle() {\r\n        this.cycle = !this.cycle;\r\n        if (this.spine) {\r\n            this.spine.setAnimation(0, this.spine.getCurrent(0)?.animation?.name || \"\", this.cycle);\r\n        }\r\n    }\r\n\r\n    downAnimationSpeed() {\r\n        this.animSpeed = Math.max(this._animSpeed / 2, 0.125)\r\n    }\r\n    upAnimationSpeed() {\r\n        this.animSpeed = Math.min(this._animSpeed * 2, 8)\r\n    }\r\n    \r\n    /**\r\n     * 获取所有可用的动画名称\r\n     */\r\n    getAvailableAnimations(): string[] {\r\n        if (!this.spine || !this.spine.skeletonData) {\r\n            return [];\r\n        }\r\n\r\n        const animations: string[] = [];\r\n        const skeletonData = this.spine.skeletonData.getRuntimeData();\r\n        if (skeletonData && skeletonData.animations) {\r\n            for (let i = 0; i < skeletonData.animations.length; i++) {\r\n                animations.push(skeletonData.animations[i].name);\r\n            }\r\n        }\r\n        return animations;\r\n    }\r\n}\r\n\r\n\r\n"]}