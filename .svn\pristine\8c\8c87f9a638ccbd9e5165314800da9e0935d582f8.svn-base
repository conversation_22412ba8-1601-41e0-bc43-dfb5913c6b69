// // @ts-ignore
// import packageJSON from '../package.json';
import { MenuAssetInfo, IAssetInfo } from '@cocos/creator-types/editor/packages/package-asset/@types/public';

export function onCreateMenu(assetInfo: MenuAssetInfo) {
  return [
    {
      label: '飞机游戏',
      submenu: [
        {
          label: '创建波次',
          click() {
            console.log('wave');
            console.log(assetInfo);
          },
        },
        {
          label: '创建阵型',
          click() {
            console.log('formation');
            console.log(assetInfo);
          },
        },
      ],
    },
  ];
};

export function onAssetMenu(assetInfo: MenuAssetInfo) {
  const submenu = [
    {
      label: '创建关卡Prefab',
      //enabled: assetInfo.isDirectory,
      click() {
        createLevelPrefab(assetInfo);
      },
    },
    {
      label: '创建子弹prefab',
      //enabled: !assetInfo.isDirectory,
      click() {
        createBulletPrefabs(assetInfo);
      },
    },
  ];

  // 检查是否是阵型JSON文件
  if (!assetInfo.isDirectory &&
      assetInfo.url.includes('assets/resources/game/level/wave/formation') &&
      assetInfo.url.endsWith('.json')) {
    submenu.push({
      label: '编辑阵型',
      click() {
        editFormation(assetInfo);
      },
    });
  }

  // 检查是否是路径JSON文件
  if (!assetInfo.isDirectory &&
      assetInfo.url.includes('assets/resources/game/level/wave/path') &&
      assetInfo.url.endsWith('.json')) {
    submenu.push({
      label: '编辑路径',
      click() {
        editPath(assetInfo);
      },
    });
  }
  if (!assetInfo.isDirectory &&
      assetInfo.url.includes('assets/resources/game/spine/plane/mainplane/') &&
      assetInfo.url.endsWith('.json')) {
    submenu.push({
      label: '创建自机prefab',
      click() {
        createMainPlanePrefab(assetInfo);
      },
    });
  }

  return [
    {
      label: '飞机游戏',
      submenu: submenu,
    },
  ];
};

function getAssetUuidByPath(path: string): Promise<string> {
  return new Promise((resolve, reject) => {
    // @ts-ignore
    Editor.Message.request('asset-db', 'query-uuid', path).then((res: any) => {
      resolve(res);
    }).catch((err: any) => {
      console.error('Failed to query uuid:', err);
      reject(err);
    });
  });
}

function getAssetUuidsByPath(path: string): Promise<string[]> {
    return new Promise((resolve, reject) => {
        // @ts-ignore
        Editor.Message.request('asset-db', 'query-assets', { pattern: path + '/**' }).then((res: any) => {
            const arr: any[] = Array.isArray(res) ? res : (Array.isArray(res?.[0]) ? res[0] : []);
            const assets = arr
                .filter((a: any) => a && !a.isDirectory)
                .map((a: any) => ({
                    name: String(a.name || ''),
                    path: a.path || '',
                    uuid: a.uuid || ''
                }))
                .filter(p => p.name)
                .sort((a, b) => a.name.localeCompare(b.name));
            resolve(assets.map(a => a.uuid)); // 只需要uuid即可，不需要其他信息了。
        }).catch((err: any) => {
            console.error('Failed to query assets:', err);
            reject(err);
        });
    });
}

function createLevelPrefab(assetInfo: MenuAssetInfo) {
  console.log(assetInfo);

  if (assetInfo.isDirectory) {
    getAssetUuidsByPath(assetInfo.url).then((uuids: string[]) => {
      uuids.forEach((uuid: string) => {
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
          name: 'level-editor',
          method: 'createLevelPrefab',
          args: [uuid]
        });
      });
    });
  } else {
    // @ts-ignore
    Editor.Message.request('scene', 'execute-scene-script', {
      name: 'level-editor',
      method: 'createLevelPrefab',
      args: [assetInfo.uuid]
    });
  }
}

function createBulletPrefabs(assetInfo: MenuAssetInfo) {
    if (assetInfo.isDirectory) {
        getAssetUuidsByPath(assetInfo.url).then((uuids: string[]) => {
            uuids.forEach((uuid: string) => {
                 // @ts-ignore
                Editor.Message.request('scene', 'execute-scene-script', {
                    name: 'level-editor',
                    method: 'createBulletPrefab',
                    args: [uuid]
                });
            });
        });
    } else {
         // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'level-editor',
            method: 'createBulletPrefab',
            args: [assetInfo.uuid]
        });
    }
}

function createMainPlanePrefab(assetInfo: MenuAssetInfo) {
  console.log("create mainplane from spine assetInfo", assetInfo);
  
  // @ts-ignore
  Editor.Message.request('scene', 'execute-scene-script', {
    name: 'level-editor',
    method: 'createMainPlanePrefab',
    args: [assetInfo.uuid]
  });
}

function editFormation(assetInfo: MenuAssetInfo) {
  console.log('编辑阵型:', assetInfo);
  // 打开FormationEditor场景 Uuid = aa842f3a-8aea-42c2-a480-968aade3dfef
  getAssetUuidByPath('db://assets/scenes/FormationEditor.scene').then((uuid: string) => {
    // @ts-ignore
    Editor.Message.request('scene', 'open-scene', uuid).then(() => {
      // 场景打开后，加载选中的阵型JSON文件到FormationEditor组件
      // @ts-ignore
      Editor.Message.request('scene', 'execute-scene-script', {
          name: 'level-editor',
          method: 'loadFormationData',
          args: [assetInfo.uuid]
      });
    }).catch((err: any) => {
        console.error('Failed to open FormationEditor scene:', err);
    });
  });
}

function editPath(assetInfo: MenuAssetInfo) {
  console.log('编辑路径:', assetInfo);
  // 打开PathEditor场景
  getAssetUuidByPath('db://assets/scenes/PathEditor.scene').then((uuid: string) => {
    // @ts-ignore
    Editor.Message.request('scene', 'open-scene', uuid).then(() => {
      // 场景打开后，加载选中的路径JSON文件到PathEditor组件
      // @ts-ignore
      Editor.Message.request('scene', 'execute-scene-script', {
          name: 'level-editor',
          method: 'loadPathData',
          args: [assetInfo.uuid]
      });
    }).catch((err: any) => {
        console.error('Failed to open PathEditor scene:', err);
    });
  });
}
