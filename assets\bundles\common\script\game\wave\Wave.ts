import { _decorator, CCBoolean, CCFloat, CCInteger, CCString, Component, Vec2 } from 'cc';
import { WaveData, FormationGroup, FormationPoint, eSpawnOrder, eWaveCompletion } from '../data/WaveData';
import { GameIns } from 'db://assets/bundles/common/script/game/GameIns';
import { WaveEventGroup, WaveEventGroupContext } from './WaveEventGroup';
import EnemyPlane from 'db://assets/bundles/common/script/game/ui/plane/enemy/EnemyPlane';
import PlaneBase from 'db://assets/bundles/common/script/game/ui/plane/PlaneBase';
import { PlaneEventType } from 'db://assets/bundles/common/script/game/ui/plane/event/PlaneEventType';
const { ccclass, property, executeInEditMode, menu } = _decorator;

@ccclass('Wave')
@menu("怪物/波次")
@executeInEditMode()
export class Wave extends Component {
    @property({displayName: '名称', editorOnly: true})
    waveName: string = '';                // 备注(策划用)

    @property({type:WaveData})
    readonly waveData: WaveData = new WaveData();

    /*
     * 以下是新的wave逻辑, 旧的逻辑主要在WaveManager与EnemyWave
     */
    private _isSpawnCompleted: boolean = false;
    public get isSpawnCompleted() { return this._isSpawnCompleted; }
    private _isAllEnemyDead: boolean = false;
    public get isAllEnemyDead() { return this._isAllEnemyDead; }
    
    private _waveElapsedTime: number = 0;
    private _nextSpawnTime: number = 0;
    private _totalWeight: number = 0;
    // 这个参数可能是时间，也可能是数量，取决于waveData.waveCompletion
    private _waveCompleteParam: number = 0;
    private _spawnIndex: number = 0;

    // 用在waveCompletion == SpawnCount时的队列
    private _spawnQueue: number[] = [];
    // 当前wave的偏移位置
    private _offsetX: number = 0;
    private _offsetY: number = 0;
    private _formationGroup: FormationGroup|null = null;
    // 事件组
    private _eventGroups: WaveEventGroup[] = [];
    private _eventGroupContext: WaveEventGroupContext|null = null;
    // 怪物
    private _enemyCreated: EnemyPlane[] = [];

    onLoad() {
        if (this.waveData) {
            if (this.waveData.spawnOrder === eSpawnOrder.Random) {
                this._totalWeight = 0;
                // add up _totalWeight if is random
                this.waveData.spawnGroups.forEach((group) => {
                    this._totalWeight += group.weight;
                    group.selfWeight = this._totalWeight;
                });
            }
            
            if (this.waveData.eventGroupData) {
                if (!this._eventGroupContext) {
                    this._eventGroupContext = new WaveEventGroupContext();
                }
                this.waveData.eventGroupData.forEach((groupData) => {
                    const group = new WaveEventGroup(this._eventGroupContext!, groupData);
                    this._eventGroups.push(group);
                });
            }

            if (this.waveData.formationAsset) {
                this._formationGroup = new FormationGroup();
                Object.assign(this._formationGroup, this.waveData.formationAsset.json);
            }
        }
    }

    private reset() {
        this._isSpawnCompleted = false;
        this._isAllEnemyDead = false;
        this._waveElapsedTime = 0;
        this._nextSpawnTime = 0;
        this._spawnIndex = 0;
        this._spawnQueue.length = 0;
        this._eventGroupContext && (this._eventGroupContext!.reset());
        
        this._eventGroups.forEach((group) => {
            group.reset();
        });

        if (this._enemyCreated.length > 0) {
            this._enemyCreated.length = 0;
        }
    }

    trigger(x: number, y: number) {
        this.reset();
        this._offsetX = x;
        this._offsetY = y;

        // 对于固定数量的波次，可以预先生成队列，每次从里面取即可
        if (this.waveData) {
            this._waveCompleteParam = this.waveData.waveCompletionParam.eval();
            if (this.waveData.waveCompletion === eWaveCompletion.SpawnCount) {
                if (this.waveData.spawnOrder === eSpawnOrder.Random) {
                    for (let i = 0; i < this._waveCompleteParam; i++) {
                        const randomWeight = GameIns.battleManager.random() * this._totalWeight;
                        for (const group of this.waveData.spawnGroups) {
                            if (randomWeight <= group.selfWeight) {
                                this._spawnQueue.push(group.planeID);
                                break;
                            }
                        }
                    }
                } else {
                    for (let i = 0; i < this._waveCompleteParam; i++) {
                        // 通过取余实现循环
                        this._spawnQueue.push(this.waveData.spawnGroups[i % this.waveData.spawnGroups.length].planeID);
                    }
                }
            }
        }

        this._eventGroupContext && (this._eventGroupContext!.wave = this);
        this._eventGroups.forEach((group) => {
            group.tryStart();
        });
    }

    // tick wave
    tick(dtInMiliseconds: number) {
        if (!this._isSpawnCompleted) {
            this.tickSpawn(dtInMiliseconds);
        }
    }
    
    private tickSpawn(dtInMiliseconds: number) {
        this._waveElapsedTime += dtInMiliseconds;
        if (this.waveData.waveCompletion === eWaveCompletion.SpawnCount) {
            // 产出固定数量的波次
            if (this._spawnIndex >= this._waveCompleteParam) {
                this._isSpawnCompleted = true;
            } else {
                if (this._waveElapsedTime >= this._nextSpawnTime) {
                    this.spawnFromQueue();
                }
            }
        } else {
            // 完全根据时间的波次
            if (this._waveElapsedTime >= this._waveCompleteParam) {
                this._isSpawnCompleted = true;
            } else {
                if (this._waveElapsedTime >= this._nextSpawnTime) {
                    this.spawnFromGroup();
                }
            }
        }

        if (this._eventGroups.length > 0) {
            for (let i = 0; i < this._eventGroups.length; i++) {
                this._eventGroups[i].tick(dtInMiliseconds);
            }
        }
    }

    private spawnFromQueue(): void {        
        this.spawnSingleFromQueue(this._spawnIndex % this._spawnQueue.length);
        this._nextSpawnTime = this._waveElapsedTime + this.waveData.spawnInterval.eval();
    }

    private spawnSingleFromQueue(index: number): void {
        if (index >= this._spawnQueue.length) {
            return;
        }

        let spawnPos = this.waveData.spawnPos;
        let spawnAngle = this.waveData.spawnAngle.eval();
        // let spawnSpeed = this.waveData.spawnSpeed.eval();

        this.createPlane(this._spawnQueue[index], spawnPos, spawnAngle);
    }

    private spawnFromGroup(): void {
        this._nextSpawnTime = this._waveElapsedTime + this.waveData.spawnInterval.eval();

        let spawnPos = this.waveData.spawnPos;
        let spawnAngle = this.waveData.spawnAngle.eval();

        if (this.waveData.spawnOrder === eSpawnOrder.Random) {
            const randomWeight = GameIns.battleManager.random() * this._totalWeight;
            for (const group of this.waveData.spawnGroups) {
                if (randomWeight <= group.selfWeight) {
                    this.createPlane(group.planeID, spawnPos, spawnAngle);
                    break;
                }
            }
        } else {
            this.createPlane(this.waveData.spawnGroups[this._spawnIndex % this.waveData.spawnGroups.length].planeID, spawnPos, spawnAngle);
        }
    }

    private createPlane(planeId: number, pos: Vec2, angle: number) {
        pos.x += this._offsetX;
        pos.y += this._offsetY;

        if (this._createPlaneDelegate) {
            this._createPlaneDelegate(planeId, pos, angle);
            this._spawnIndex++;
            return;
        }

        let enemy = GameIns.enemyManager.addPlane(planeId);
        if (enemy) {
            // 获取阵型数据
            if (this._formationGroup) {
                const point = this._formationGroup.points[this._spawnIndex % this._formationGroup.points.length];
                pos.x += point.x;
                pos.y += point.y;
            }

            // TODO: 路径数据

            enemy.setPos(pos.x, pos.y);
            enemy.initMove(angle);
            enemy.PlaneEventRegister(PlaneEventType.Die, this.onEnemyDie.bind(this));
            this._enemyCreated.push(enemy); // 记录下来，方便后面判断是否都死了
        }
        this._spawnIndex++;
    }

    private onEnemyDie(plane: EnemyPlane) {
        plane.PlaneEventUnRegister(PlaneEventType.Die, this.onEnemyDie.bind(this));
        const index = this._enemyCreated.indexOf(plane);
        if (index !== -1) {
            this._enemyCreated.splice(index, 1);

            if (this._enemyCreated.length === 0 && this._isSpawnCompleted) {
                this._isAllEnemyDead = true;
            }
        }
    }

    // 以下几个函数是为了给编辑器预览用
    private _createPlaneDelegate: ((planeId: number, pos: Vec2, angle: number) => void)|null = null;
    public setCreatePlaneDelegate(func: (planeId: number, pos: Vec2, angle: number) => void) {
        this._createPlaneDelegate = func;
    }
}