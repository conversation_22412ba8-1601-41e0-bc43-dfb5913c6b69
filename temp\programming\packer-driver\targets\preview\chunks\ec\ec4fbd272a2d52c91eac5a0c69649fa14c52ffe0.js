System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, BoxCollider2D, Size, size, v2, GameConst, FCollider, ColliderType, _dec, _dec2, _dec3, _class, _class2, _descriptor, _crd, ccclass, property, menu, FBoxCollider;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "db://assets/scripts/core/base/GameConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEntity(extras) {
    _reporterNs.report("Entity", "../ui/base/Entity", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFCollider(extras) {
    _reporterNs.report("FCollider", "./FCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfColliderType(extras) {
    _reporterNs.report("ColliderType", "./FCollider", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      BoxCollider2D = _cc.BoxCollider2D;
      Size = _cc.Size;
      size = _cc.size;
      v2 = _cc.v2;
    }, function (_unresolved_2) {
      GameConst = _unresolved_2.GameConst;
    }, function (_unresolved_3) {
      FCollider = _unresolved_3.default;
      ColliderType = _unresolved_3.ColliderType;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "c976eGmvO5AnJ3rMG4DYuIx", "FBoxCollider", undefined);

      __checkObsolete__(['_decorator', 'BoxCollider2D', 'Size', 'size', 'v2', 'Vec2']);

      ({
        ccclass,
        property,
        menu
      } = _decorator);

      _export("default", FBoxCollider = (_dec = ccclass('FBoxCollider'), _dec2 = menu("碰撞组件Ex/FBoxCollider"), _dec3 = property(Size), _dec(_class = _dec2(_class = (_class2 = class FBoxCollider extends (_crd && FCollider === void 0 ? (_reportPossibleCrUseOfFCollider({
        error: Error()
      }), FCollider) : FCollider) {
        constructor() {
          super(...arguments);
          this.worldPoints = [v2(), v2(), v2(), v2()];
          this.worldEdge = [];
          this.isConvex = true;

          _initializerDefineProperty(this, "_size", _descriptor, this);
        }

        get type() {
          return (_crd && ColliderType === void 0 ? (_reportPossibleCrUseOfColliderType({
            error: Error()
          }), ColliderType) : ColliderType).Box;
        }

        //默认100x100
        get size() {
          return this._size;
        }

        set size(value) {
          this._size = new Size(value.width < 0 ? 0 : value.width, value.height < 0 ? 0 : value.height);
        }

        onLoad() {
          var collider = this.node.getComponent(BoxCollider2D);

          if (collider) {
            this.size = collider.size;
            this.offset = v2(collider.offset.x, collider.offset.y);
          }
        }

        init(entity, size, offset) {
          if (size === void 0) {
            size = null;
          }

          if (offset === void 0) {
            offset = v2(0, 0);
          }

          this.initBaseData(entity, offset);

          if (size) {
            this.size = size;
          }
        }

        draw() {
          if (!(_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).ColliderDraw) {
            return;
          }

          var collider = this.node.getComponent(BoxCollider2D);

          if (!collider) {
            collider = this.node.addComponent(BoxCollider2D);
            collider.size = this.size;
            collider.offset.x = this.offset.x;
            collider.offset.y = this.offset.y;
          }
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "_size", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return size(100, 100);
        }
      }), _applyDecoratedDescriptor(_class2.prototype, "size", [property], Object.getOwnPropertyDescriptor(_class2.prototype, "size"), _class2.prototype)), _class2)) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=ec4fbd272a2d52c91eac5a0c69649fa14c52ffe0.js.map