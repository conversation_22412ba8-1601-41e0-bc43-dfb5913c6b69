/**
 * 路径编辑器
 */
'use strict';

const { updatePropByDump, disconnectGroup } = require('./../../prop');

type Selector<$> = { $: Record<keyof $, any | null> }

export const template = `
<div class="component-container"></div>
<ui-prop>
    <ui-button class="btn-add">添加路径点</ui-button>
    <ui-button class="btn-save">保存</ui-button>
    <ui-button class="btn-clear">清空路径</ui-button>
</ui-prop>
`;

export const $ = {
    componentContainer: '.component-container',
    btnAdd: '.btn-add',
    btnSave: '.btn-save',
    btnClear: '.btn-clear',
};

type PanelThis = Selector<typeof $> & { dump: any };

export function update(this: PanelThis, dump: any) {
    updatePropByDump(this, dump);
    this.dump = dump;
}

export async function ready(this: PanelThis) {
    disconnectGroup(this);
    
    this.$.btnAdd.addEventListener('confirm', async () => {
        // console.log('add path point', this.dump);
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'level-editor',
            method: 'addPathPoint',
            args: [this.dump?.value.uuid.value]
        });
    });

    this.$.btnSave.addEventListener('confirm', async () => {
        // console.log('save path', this.dump);
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'level-editor',
            method: 'savePath',
            args: [this.dump?.value.uuid.value]
        });
    });

    this.$.btnClear.addEventListener('confirm', async () => {
        // console.log('clear path', this.dump);
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'level-editor',
            method: 'clearPath',
            args: [this.dump?.value.uuid.value]
        });
    });
}
