import { Font, instantiate, Label, Node, Prefab, Tween, v3, Vec3, warn } from "cc";
import { MyApp } from 'db://assets/bundles/common/script/app/MyApp';
import { SingletonBase } from "../../../../../scripts/core/base/SingletonBase";
import { GameIns } from "../GameIns";
import GameResourceList from "../const/GameResourceList";
import UIAnimMethods from "../ui/base/UIAnimMethods";
import EnemyEffectLayer from "../ui/layer/EnemyEffectLayer";
import { Tools } from "../utils/Tools";


export class HurtEffectManager extends SingletonBase<HurtEffectManager> {
    ratio = 0.8;
    hurtNum: Prefab | null = null;
    m_hurtNums = new Map();
    m_hurtFont = new Map();

    /**
     * 预加载资源
     */
    preLoad() {
        if (!this.hurtNum) {
            GameIns.battleManager.addLoadCount(1);
            MyApp.resMgr.load(GameResourceList.HurtNum, Prefab, (error: any, prefab: Prefab) => {
                this.hurtNum = prefab;
                GameIns.battleManager.checkLoadFinish();
            });
        }

        if (!this.m_hurtFont) {
            this.m_hurtFont = new Map();
        }

        GameIns.battleManager.addLoadCount(1);
        MyApp.resMgr.loadDir(GameResourceList.font_hurtNum, Font, (error: any, fonts: Font[]) => {
            fonts.forEach((font) => {
                if (font) {
                    this.m_hurtFont.set(font.name, font);
                }
            });

            this.m_hurtNums.clear();
            this.m_hurtFont.forEach((font, name) => {
                const instances = [];
                for (let i = 0; i < 3; i++) {
                    if (!this.hurtNum) continue;
                    const labelNode = instantiate(this.hurtNum) as Node;
                    const label = labelNode!.getComponent(Label);
                    label!.string = "";
                    label!.font = font;
                    instances.push(label);
                }
                this.m_hurtNums.set(name, instances);
            });

            GameIns.battleManager.checkLoadFinish();
        });
    }


    /**
     * 清理资源
     */
    clear() {
        Tools.clearMapForCompArr(this.m_hurtNums);
    }

    createHurtNumByType(position: Vec3, damage: number, isCirt: boolean = false) {
        if (damage <= 0) return;

        const fontType = isCirt ? "yellowHurtNum" : "whiteHurtNum";
        const lab: Label = this.GetHurtNumsByCount(fontType, damage);

        if (lab && EnemyEffectLayer.instance.hurtNumLayer) {
            lab.node.parent = EnemyEffectLayer.instance.hurtNumLayer;
            lab.node.setPosition(position.x, position.y - 30);

            this.startHurtAni(lab, fontType);
        }
    }

    GetHurtNumsByCount(fontType: string, damage: number) {
        let hurtNum = null;
        const pool = this.m_hurtNums.get(fontType);

        if (pool) {
            if (pool.length > 0) {
                hurtNum = pool.pop();
            } else {
                hurtNum = instantiate(this.hurtNum!).getComponent(Label)!;
                hurtNum.font = this.m_hurtFont.get(fontType);
            }
        }

        hurtNum.node.opacity = 255;
        hurtNum.node.active = true;
        hurtNum.string = Math.ceil(damage).toString();

        return hurtNum;
    }

    startHurtAni(hurtNum: Label, fontType: string) {
        const ratio = this.ratio;
        Tween.stopAllByTarget(hurtNum.node);

        let tween: Tween;
        switch (fontType) {
            case "whiteHurtNum":
                hurtNum.node.setScale(0.15, 0.15);
                tween = new Tween(hurtNum.node)
                    .to(UIAnimMethods.fromTo(1, 6), { scale: v3(1.53 * ratio, 1.53 * ratio) })
                    .to(UIAnimMethods.fromTo(6, 11), { scale: v3(0.47 * ratio, 0.47 * ratio) })
                    .to(UIAnimMethods.fromTo(11, 32), { position: v3(hurtNum.node.x, hurtNum.node.y + 13), scale: v3(0.47 * ratio, 0.47 * ratio) });
                break;

            case "yellowHurtNum":
                hurtNum.node.setScale(0.16, 0.16);
                tween = new Tween(hurtNum.node)
                    .to(UIAnimMethods.fromTo(1, 6), { scale: v3(1.75 * ratio, 1.75 * ratio) })
                    .to(UIAnimMethods.fromTo(6, 9), { scale: v3(0.44 * ratio, 0.44 * ratio) })
                    .to(UIAnimMethods.fromTo(9, 12), { scale: v3(0.52 * ratio, 0.52 * ratio) })
                    .to(UIAnimMethods.fromTo(12, 31), { position: v3(hurtNum.node.x, hurtNum.node.y + 21), scale: v3(0.52 * ratio, 0.52 * ratio) });
                break;

            default:
                warn("Unknown font type in createHurtNumInTarget");
        }

        tween!.call(() => {
            this.pushHurtNums(fontType, hurtNum);
        }).start();
    }

    pushHurtNums(fontType: string, hurtNum: Label) {
        if (hurtNum && hurtNum.node) {
            hurtNum.string = "";
            hurtNum.node.active = false;

            const pool = this.m_hurtNums.get(fontType);
            if (pool) {
                pool.push(hurtNum);
            } else {
                hurtNum.node.destroy();
            }
        }
    }
}