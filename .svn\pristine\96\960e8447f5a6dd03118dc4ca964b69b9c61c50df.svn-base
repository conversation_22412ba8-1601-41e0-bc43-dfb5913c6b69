import { _decorator, ResolutionPolicy, view } from "cc";
import { GameConst } from "db://assets/scripts/core/base/GameConst";
import { IMgr } from "../../../../../scripts/core/base/IMgr";
const { ccclass, property } = _decorator;
@ccclass("GlobalDataManager")
export class GlobalDataManager extends IMgr {

    private _chapterID: number = 0;

    get chapterID(): number {
        return this._chapterID;
    }

    set chapterID(value: number) {
        this._chapterID = value;
    }

    init(): void {
        this.setUIResolution();
    }

    setUIResolution() {
        let fitType = ResolutionPolicy.SHOW_ALL
        if (view.getVisibleSize().height / view.getVisibleSize().width * GameConst.designWidth >= GameConst.designHeight) {//高度大于 16:9
            fitType = ResolutionPolicy.FIXED_WIDTH//宽度全部显示，高度做适配拉长
        } else {//宽屏,比如平板或者电脑
            fitType = ResolutionPolicy.SHOW_ALL//两边黑边       //FIXED_HEIGHT// 全部铺满
        }
        view.setResolutionPolicy(fitType);
        view.resizeWithBrowserSize(true);
    }

    setBattleResolution() {
        // 设置分辨率适配策略
        let fitType = ResolutionPolicy.SHOW_ALL
        // if (view.getVisibleSize().height / view.getVisibleSize().width * GameConst.designWidth >= GameConst.designHeight){//高度大于 16:9
        //     fitType = ResolutionPolicy.SHOW_ALL
        // }else{//宽屏,比如平板或者电脑
        //     fitType = ResolutionPolicy.FIXED_HEIGHT
        // }
        view.setResolutionPolicy(fitType);
        view.resizeWithBrowserSize(true);
    }

}