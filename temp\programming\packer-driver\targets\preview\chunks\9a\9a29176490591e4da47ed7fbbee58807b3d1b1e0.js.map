{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/GameDataManager.ts"], "names": ["GameDataManager", "SingletonBase", "csproto", "GameIns", "gameTime", "isWin", "totalScore", "starLevel", "reset", "setGameEnd", "getGameResultData", "data", "comm", "GameResult", "total_time_cost", "max_levels", "battleManager", "curLevel", "is_pass", "total_score", "star_level", "gain_items", "getAllItemData", "rogues", "getRogueItemData", "list", "getGameLevelResultData"], "mappings": ";;;+DAQaA,e;;;;;;;;;;;;;;;;;;;;;;AAJJC,MAAAA,a,iBAAAA,a;;AACFC,MAAAA,O;;AACEC,MAAAA,O,iBAAAA,O;;;;;;AANT;AACA;AACA;;;iCAMaH,e,GAAN,MAAMA,eAAN;AAAA;AAAA,0CAA6D;AAAA;AAAA;AAAA,eAEhEI,QAFgE,GAE9C,CAF8C;AAAA,eAGhEC,KAHgE,GAGhD,KAHgD;AAAA,eAIhEC,UAJgE,GAI5C,CAJ4C;AAAA,eAKhEC,SALgE,GAK7C,CAL6C;AAAA;;AAOhEC,QAAAA,KAAK,GAAE;AACH,eAAKJ,QAAL,GAAgB,CAAhB;AACA,eAAKE,UAAL,GAAkB,CAAlB;AACA,eAAKC,SAAL,GAAiB,CAAjB;AACA,eAAKF,KAAL,GAAa,KAAb;AACH;;AAEDI,QAAAA,UAAU,CAACJ,KAAD,EAAe;AACrB,eAAKA,KAAL,GAAaA,KAAb;AACH;;AAEDK,QAAAA,iBAAiB,GAAiC;AAC9C,cAAIC,IAAI,GAAG,IAAI;AAAA;AAAA,kCAAQC,IAAR,CAAaC,UAAjB,EAAX;AACAF,UAAAA,IAAI,CAACG,eAAL,GAAuB,KAAKV,QAA5B;AACAO,UAAAA,IAAI,CAACI,UAAL,GAAkB;AAAA;AAAA,kCAAQC,aAAR,CAAsBC,QAAxC;AACAN,UAAAA,IAAI,CAACO,OAAL,GAAe,KAAKb,KAAL,GAAa,CAAb,GAAiB,CAAhC;AACAM,UAAAA,IAAI,CAACQ,WAAL,GAAmB,KAAKb,UAAxB;AACAK,UAAAA,IAAI,CAACS,UAAL,GAAkB,KAAKb,SAAvB;AACAI,UAAAA,IAAI,CAACU,UAAL,GAAkB,KAAKC,cAAL,EAAlB;AACAX,UAAAA,IAAI,CAACY,MAAL,GAAc,KAAKC,gBAAL,EAAd;AACA,iBAAOb,IAAP,CAT8C,CAW9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACH;;AAEDW,QAAAA,cAAc,GAAE;AACZ,cAAIG,IAA4B,GAAG,EAAnC;AACA,iBAAOA,IAAP;AACH;;AACDD,QAAAA,gBAAgB,GAAE;AACd,cAAIC,IAA6B,GAAG,EAApC;AACA,iBAAOA,IAAP;AACH;;AAEDC,QAAAA,sBAAsB,GAAuC;AAEzD;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA,iBAAO,IAAP;AACH;;AAjE+D,O", "sourcesContent": ["/**\r\n * 战斗数据管理\r\n */\r\n\r\nimport { SingletonBase } from \"db://assets/scripts/core/base/SingletonBase\";\r\nimport csproto from 'db://assets/bundles/common/script/autogen/pb/cs_proto.js';\r\nimport { GameIns } from \"../GameIns\";\r\n\r\nexport class GameDataManager extends SingletonBase<GameDataManager> {\r\n\r\n    gameTime:number = 0;\r\n    isWin:boolean = false;\r\n    totalScore:number = 0;\r\n    starLevel:number = 0;\r\n\r\n    reset(){\r\n        this.gameTime = 0;\r\n        this.totalScore = 0;\r\n        this.starLevel = 0;\r\n        this.isWin = false;\r\n    }\r\n\r\n    setGameEnd(isWin:boolean){\r\n        this.isWin = isWin;\r\n    }\r\n    \r\n    getGameResultData():csproto.comm.IGameResult|null {\r\n        let data = new csproto.comm.GameResult();\r\n        data.total_time_cost = this.gameTime;\r\n        data.max_levels = GameIns.battleManager.curLevel;\r\n        data.is_pass = this.isWin ? 1 : 0;\r\n        data.total_score = this.totalScore;\r\n        data.star_level = this.starLevel;\r\n        data.gain_items = this.getAllItemData();\r\n        data.rogues = this.getRogueItemData();\r\n        return data;\r\n\r\n        // fixed64  game_id = 1;           // 本局的唯一ID\r\n        // int32  mode_id = 2;           // 入口模式ID，见模式表\r\n        // int32  total_time_cost = 3;   // 总耗时\r\n        // int32  max_levels = 4;        // 最大关卡数\r\n        // int32  is_pass = 5;           // 是否过关（对于剧情或者闯关模式。如果是无尽模式 - 最后一关总是失败才结算的，则忽略）\r\n        // int32  total_score = 6;       // 总得分\r\n        // int32  star_level = 7;        // 星级\r\n        // repeated  comm.GainItem gain_items = 8;    // 局内获得的道具\r\n        // repeated  RogueItem rogues = 9;  // 本局选择的Rogue\r\n        // repeated GameStatItem  game_stats = 10;  // 局内的一些零散的统计值，比如击杀的怪物数，击杀的怪物类型数，等等\r\n        // int32  is_week_bset = 11;      // 是否本周最佳得分，服务器下发用\r\n        // int32  history_bese_score = 12; // 历史最高分，服务器下发用\r\n        // repeated  comm.GainItem  reward_items = 13; // 服务器计算的奖励列表\r\n    }\r\n\r\n    getAllItemData(){\r\n        let list:csproto.comm.GainItem[] = [];\r\n        return list;\r\n    }\r\n    getRogueItemData(){\r\n        let list:csproto.comm.RogueItem[] = [];\r\n        return list;\r\n    }\r\n\r\n    getGameLevelResultData():csproto.comm.IGameLevelResult[]|null{\r\n\r\n        // int32  level_index = 1;\r\n        // int32  level_id = 2;\r\n        // int32  score = 3;\r\n        // int32  star = 4;\r\n        // int32  is_pass = 5;\r\n        // int32  time_cost = 6;\r\n        // repeated GameStatItem level_stats = 7;    // 关卡内的各项信息统计（取决于需要）\r\n\r\n        // let data = new csproto.comm.GameLevelResult();\r\n        return null;\r\n    }\r\n}"]}