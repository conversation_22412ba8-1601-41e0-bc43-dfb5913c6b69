{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/data/base/AttributeData.ts"], "names": ["AttributeData", "AttributeConst", "DamageType", "constructor", "_baseAttributes", "Map", "_Attributes", "_AttributeModifies", "_sources", "bulletHurtRate", "boomHurtRate", "addBaseAttribute", "key", "value", "set", "get", "recalculateAttributes", "addModify", "id", "modify", "source", "push", "removeModify", "for<PERSON>ach", "delete", "finalValue", "getFinalAttributeByKey", "getFinialAttributeByOutInKey", "out<PERSON><PERSON><PERSON><PERSON>", "out<PERSON><PERSON><PERSON><PERSON>", "inAdd<PERSON>ey", "in<PERSON>er<PERSON>ey", "CalcBulletDamage", "attackerAttr", "<PERSON><PERSON><PERSON><PERSON>", "hurtRate", "isBullet", "damageType", "isBoss", "attackPowerFix", "attack", "getAttack", "damageAttackOutAddKey", "damageAttackOutPerKey", "damageAttackInAddKey", "damageAttackInPerKey", "EXPLOSIVE", "ExplosiveBulletAttackInAdd", "ExplosiveBulletAttackInPer", "ExplosiveBulletAttackOutAdd", "ExplosiveBulletAttackOutPer", "NORMAL", "NormalBulletAttackInAdd", "NormalBulletAttackInPer", "NormalBulletAttackOutAdd", "NormalBulletAttackOutPer", "ENERGETIC", "EnergeticBulletAttackInAdd", "EnergeticBulletAttackInPer", "EnergeticBulletAttackOutAdd", "EnergeticBulletAttackOutPer", "PHYSICAL", "PhysicsBulletAttackInAdd", "PhysicsBulletAttackInPer", "PhysicsBulletAttackOutAdd", "PhysicsBulletAttackOutPer", "ALL", "BulletAttackOutAdd", "BulletAttackOutPer", "BulletAttackInAdd", "BulletAttackInPer", "NuclearAttackOutAdd", "NuclearAttackOutPer", "NuclearAttackInAdd", "NuclearAttackInPer", "hurtResistanceOutAddKey", "BulletHurtResistanceOutAdd", "NuclearHurtResistanceOutAdd", "hurtResistanceOutPerKey", "BulletHurtResistanceOutPer", "NuclearHurtResistanceOutPer", "hurtResistanceInAddKey", "BulletHurtResistanceInAdd", "NuclearHurtResistanceInAdd", "hurtResistanceInPerKey", "BulletHurtResistanceInPer", "NuclearHurtResistanceInPer", "hurtResistance", "hurtDerate", "BulletHurtDerateOut", "BulletHurtDerateIn", "hurtBonus", "BossHurtBonusOut", "BossHurtBonusIn", "NormalHurtBonusOut", "NormalHurtBonusIn", "hurtFix", "damageHurtFixOutKey", "damageHurtFixInKey", "ExplosiveBulletHurtFixOut", "ExplosiveBulletHurtFixIn", "NormalBulletHurtFixOut", "NormalBulletHurtFixIn", "EnergeticBulletHurtFixOut", "EnergeticBulletHurtFixIn", "PhysicsBulletHurtFixOut", "PhysicsBulletHurtFixIn", "BulletHurtFixOut", "BulletHurtFixIn", "NuclearHurtFixOut", "NuclearHurtFixIn", "damage", "Math", "ceil", "getMaxHP", "floor", "MaxHPOutAdd", "MaxHPOutPer", "MaxHPInAdd", "MaxHPInPer", "AttackOutAdd", "AttackOutPer", "AttackInAdd", "AttackInPer", "getHPRecovery", "HPRecoveryOutAdd", "HPRecoveryOutPer", "HPRecoveryInAdd", "HPRecoveryInPer", "MaxHPRecoveryRateOut", "MaxHPRecoveryRateIn"], "mappings": ";;;0DAGaA,a;;;;;;;;;;;;;;;;;;AAHJC,MAAAA,c,iBAAAA,c;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;+BAEIF,a,GAAN,MAAMA,aAAN,CAAoB;AAWvBG,QAAAA,WAAW,GAAG;AAAA,eAVNC,eAUM,GAVyC,IAAIC,GAAJ,EAUzC;AAVoD;AAUpD,eATNC,WASM,GATqC,IAAID,GAAJ,EASrC;AATgD;AAShD,eARNE,kBAQM,GARyD,IAAIF,GAAJ,EAQzD;AAAA,eAPNG,QAOM,GAPqC,IAAIH,GAAJ,EAOrC;AAP8C;AAG5D;AAIc,eAHPI,cAGO,GAHU,CAGV;AAAA,eAFPC,YAEO,GAFQ,CAER;AACb;;AAGDC,QAAAA,gBAAgB,CAACC,GAAD,EAAsBC,KAAtB,EAAqC;AACjD,eAAKT,eAAL,CAAqBU,GAArB,CAAyBF,GAAzB,EAA8B,KAAKR,eAAL,CAAqBW,GAArB,CAAyBH,GAAzB,KAA+B,IAAIC,KAAjE;;AACA,eAAKG,qBAAL,CAA2BJ,GAA3B;AACH,SAlBsB,CAoBvB;;;AACAK,QAAAA,SAAS,CAACC,EAAD,EAAaN,GAAb,EAAiCC,KAAjC,EAAgD;AACrD,cAAIM,MAAM,GAAG,KAAKZ,kBAAL,CAAwBQ,GAAxB,CAA4BH,GAA5B,CAAb;;AACA,cAAI,CAACO,MAAL,EAAa;AACTA,YAAAA,MAAM,GAAG,IAAId,GAAJ,EAAT;;AACA,iBAAKE,kBAAL,CAAwBO,GAAxB,CAA4BI,EAA5B,EAAgCC,MAAhC;AACH;;AACDA,UAAAA,MAAM,CAACL,GAAP,CAAWI,EAAX,EAAeL,KAAf;;AAEA,cAAIO,MAAM,GAAG,KAAKZ,QAAL,CAAcO,GAAd,CAAkBH,GAAlB,CAAb;;AACA,cAAI,CAACQ,MAAL,EAAa;AACTA,YAAAA,MAAM,GAAG,EAAT;;AACA,iBAAKZ,QAAL,CAAcM,GAAd,CAAkBF,GAAlB,EAAuBQ,MAAvB;AACH;;AACDA,UAAAA,MAAM,CAACC,IAAP,CAAYH,EAAZ;AAEA,eAAKF,qBAAL,CAA2BJ,GAA3B;AACH,SArCsB,CAuCvB;;;AACAU,QAAAA,YAAY,CAACJ,EAAD,EAAa;AACrB,cAAIE,MAAM,GAAG,KAAKZ,QAAL,CAAcO,GAAd,CAAkBG,EAAlB,CAAb;;AACAE,UAAAA,MAAM,QAAN,IAAAA,MAAM,CAAEG,OAAR,CAAiBX,GAAD,IAAS;AACrB,gBAAIO,MAAM,GAAG,KAAKZ,kBAAL,CAAwBQ,GAAxB,CAA4BH,GAA5B,CAAb;;AACA,gBAAIO,MAAJ,EAAY;AACRA,cAAAA,MAAM,CAACK,MAAP,CAAcN,EAAd;AACH;;AACD,iBAAKF,qBAAL,CAA2BJ,GAA3B;AACH,WAND;;AAOA,eAAKJ,QAAL,CAAcgB,MAAd,CAAqBN,EAArB;AACH,SAlDsB,CAoDvB;;;AACQF,QAAAA,qBAAqB,CAACJ,GAAD,EAAqB;AAC9C,cAAIa,UAAU,GAAG,KAAKrB,eAAL,CAAqBW,GAArB,CAAyBH,GAAzB,KAAiC,CAAlD,CAD8C,CAG9C;;AACA,eAAKL,kBAAL,CAAwBgB,OAAxB,CAAgCH,MAAM,IAAI;AACtCA,YAAAA,MAAM,CAACG,OAAP,CAAeV,KAAK,IAAI;AACpBY,cAAAA,UAAU,IAAIZ,KAAd;AACH,aAFD;AAGH,WAJD,EAJ8C,CAU9C;;;AACA,eAAKP,WAAL,CAAiBQ,GAAjB,CAAqBF,GAArB,EAA0Ba,UAA1B;AACH;;AAEDC,QAAAA,sBAAsB,CAACd,GAAD,EAA4B;AAC9C,iBAAO,KAAKN,WAAL,CAAiBS,GAAjB,CAAqBH,GAArB,KAA6B,CAApC;AACH;;AAEDe,QAAAA,4BAA4B,CAACC,SAAD,EAA2BC,SAA3B,EACxBC,QADwB,EACCC,QADD,EACkC;AAE1D,iBAAO,CAAC,KAAKL,sBAAL,CAA4BE,SAA5B,KAA0C,IAAI,KAAKF,sBAAL,CAA4BG,SAA5B,IAAyC,KAAvF,IACF,KAAKH,sBAAL,CAA4BI,QAA5B,CADC,KACyC,IAAI,KAAKJ,sBAAL,CAA4BK,QAA5B,IAAwC,KADrF,CAAP;AAEH;;AAEsB,eAAhBC,gBAAgB,CAACC,YAAD,EAA8BC,YAA9B,EACfC,QADe,EACGC,QADH,EACsBC,UADtB,EAC8CC,MAD9C,EAC+DC,cAD/D,EAC8F;AAEjH;AACA;AACA,cAAIC,MAAM,GAAGP,YAAY,CAACQ,SAAb,EAAb,CAJiH,CAMjH;;AAEA,cAAIL,QAAJ,EAAc;AACV,gBAAIM,qBAAqB,GAAG,CAA5B;AAA8B;AAC9B,gBAAIC,qBAAqB,GAAG,CAA5B;AAA8B;AAC9B,gBAAIC,oBAAoB,GAAI,CAA5B;AAA8B;AAC9B,gBAAIC,oBAAoB,GAAI,CAA5B;AAA8B;;AAC9B,oBAAOR,UAAP;AACI,mBAAK;AAAA;AAAA,4CAAWS,SAAhB;AACIJ,gBAAAA,qBAAqB,GAAG;AAAA;AAAA,sDAAeK,0BAAvC;AACAJ,gBAAAA,qBAAqB,GAAG;AAAA;AAAA,sDAAeK,0BAAvC;AACAJ,gBAAAA,oBAAoB,GAAI;AAAA;AAAA,sDAAeK,2BAAvC;AACAJ,gBAAAA,oBAAoB,GAAI;AAAA;AAAA,sDAAeK,2BAAvC;AACA;;AACJ,mBAAK;AAAA;AAAA,4CAAWC,MAAhB;AACIT,gBAAAA,qBAAqB,GAAG;AAAA;AAAA,sDAAeU,uBAAvC;AACAT,gBAAAA,qBAAqB,GAAG;AAAA;AAAA,sDAAeU,uBAAvC;AACAT,gBAAAA,oBAAoB,GAAI;AAAA;AAAA,sDAAeU,wBAAvC;AACAT,gBAAAA,oBAAoB,GAAI;AAAA;AAAA,sDAAeU,wBAAvC;AACA;;AACJ,mBAAK;AAAA;AAAA,4CAAWC,SAAhB;AACId,gBAAAA,qBAAqB,GAAG;AAAA;AAAA,sDAAee,0BAAvC;AACAd,gBAAAA,qBAAqB,GAAG;AAAA;AAAA,sDAAee,0BAAvC;AACAd,gBAAAA,oBAAoB,GAAI;AAAA;AAAA,sDAAee,2BAAvC;AACAd,gBAAAA,oBAAoB,GAAI;AAAA;AAAA,sDAAee,2BAAvC;AACA;;AACJ,mBAAK;AAAA;AAAA,4CAAWC,QAAhB;AACInB,gBAAAA,qBAAqB,GAAG;AAAA;AAAA,sDAAeoB,wBAAvC;AACAnB,gBAAAA,qBAAqB,GAAG;AAAA;AAAA,sDAAeoB,wBAAvC;AACAnB,gBAAAA,oBAAoB,GAAI;AAAA;AAAA,sDAAeoB,yBAAvC;AACAnB,gBAAAA,oBAAoB,GAAI;AAAA;AAAA,sDAAeoB,yBAAvC;AACA;AAxBR;;AA0BA,gBAAI5B,UAAU,IAAI;AAAA;AAAA,0CAAW6B,GAA7B,EAAkC;AAC9B1B,cAAAA,MAAM,GAAG,CAAE,CAACA,MAAM,GAAGL,QAAT,GAAoBF,YAAY,CAACP,sBAAb,CAAoC;AAAA;AAAA,oDAAeyC,kBAAnD,CAApB,GACElC,YAAY,CAACP,sBAAb,CAAoCgB,qBAApC,CADH,KAEA,IAAI,CAACT,YAAY,CAACP,sBAAb,CAAoC;AAAA;AAAA,oDAAe0C,kBAAnD,IACFnC,YAAY,CAACP,sBAAb,CAAoCiB,qBAApC,CADC,IAC6D,KAHjE,CAAD,GAIJV,YAAY,CAACP,sBAAb,CAAoC;AAAA;AAAA,oDAAe2C,iBAAnD,CAJI,GAKJpC,YAAY,CAACP,sBAAb,CAAoCkB,oBAApC,CALG,KAMF,IAAI,CAACX,YAAY,CAACP,sBAAb,CAAoC;AAAA;AAAA,oDAAe4C,iBAAnD,IACFrC,YAAY,CAACP,sBAAb,CAAoCmB,oBAApC,CADC,IAC4D,KAP9D,CAAT;AAQH,aATD,MASO;AACHL,cAAAA,MAAM,GAAG,CAAE,CAACA,MAAM,GAAGL,QAAT,GAAoBF,YAAY,CAACP,sBAAb,CAAoC;AAAA;AAAA,oDAAeyC,kBAAnD,CAArB,KACA,IAAIlC,YAAY,CAACP,sBAAb,CAAoC;AAAA;AAAA,oDAAe0C,kBAAnD,IAAyE,KAD7E,CAAD,GAEJnC,YAAY,CAACP,sBAAb,CAAoC;AAAA;AAAA,oDAAe2C,iBAAnD,CAFG,KAGE,IAAIpC,YAAY,CAACP,sBAAb,CAAoC;AAAA;AAAA,oDAAe4C,iBAAnD,IAAwE,KAH9E,CAAT;AAIH;AACJ,WA9CD,MA8CO;AACH9B,YAAAA,MAAM,GAAG,CAAE,CAACA,MAAM,GAAGL,QAAT,GAAoBF,YAAY,CAACP,sBAAb,CAAoC;AAAA;AAAA,kDAAe6C,mBAAnD,CAArB,KACA,IAAItC,YAAY,CAACP,sBAAb,CAAoC;AAAA;AAAA,kDAAe8C,mBAAnD,IAA0E,KAD9E,CAAD,GAEJvC,YAAY,CAACP,sBAAb,CAAoC;AAAA;AAAA,kDAAe+C,kBAAnD,CAFG,KAGE,IAAIxC,YAAY,CAACP,sBAAb,CAAoC;AAAA;AAAA,kDAAegD,kBAAnD,IAAyE,KAH/E,CAAT;AAIH,WA3DgH,CA6DjH;;;AACA,cAAIC,uBAAuB,GAAGvC,QAAQ,GAAG;AAAA;AAAA,gDAAewC,0BAAlB,GAA8C;AAAA;AAAA,gDAAeC,2BAAnG;AACA,cAAIC,uBAAuB,GAAG1C,QAAQ,GAAG;AAAA;AAAA,gDAAe2C,0BAAlB,GAA8C;AAAA;AAAA,gDAAeC,2BAAnG;AACA,cAAIC,sBAAsB,GAAG7C,QAAQ,GAAG;AAAA;AAAA,gDAAe8C,yBAAlB,GAA6C;AAAA;AAAA,gDAAeC,0BAAjG;AACA,cAAIC,sBAAsB,GAAGhD,QAAQ,GAAG;AAAA;AAAA,gDAAeiD,yBAAlB,GAA6C;AAAA;AAAA,gDAAeC,0BAAjG;AAEA,cAAIC,cAAc,GAAGrD,YAAY,CAACP,4BAAb,CACjBgD,uBADiB,EACQG,uBADR,EAEjBG,sBAFiB,EAEOG,sBAFP,CAArB,CAnEiH,CAuEjH;;AACA,cAAII,UAAU,GAAG,CAAC,IAAItD,YAAY,CAACR,sBAAb,CAAoC;AAAA;AAAA,gDAAe+D,mBAAnD,CAAL,KACV,IAAIvD,YAAY,CAACR,sBAAb,CAAoC;AAAA;AAAA,gDAAegE,kBAAnD,CADM,CAAjB,CAxEiH,CA2EjH;;AACA,cAAIC,SAAJ;;AACA,cAAIrD,MAAJ,EAAY;AACRqD,YAAAA,SAAS,GAAG,CAAC,IAAI1D,YAAY,CAACP,sBAAb,CAAoC;AAAA;AAAA,kDAAekE,gBAAnD,CAAL,KACL,IAAI3D,YAAY,CAACP,sBAAb,CAAoC;AAAA;AAAA,kDAAemE,eAAnD,CADC,CAAZ;AAEH,WAHD,MAGO;AACHF,YAAAA,SAAS,GAAG,CAAC,IAAI1D,YAAY,CAACP,sBAAb,CAAoC;AAAA;AAAA,kDAAeoE,kBAAnD,CAAL,KACL,IAAI7D,YAAY,CAACP,sBAAb,CAAoC;AAAA;AAAA,kDAAeqE,iBAAnD,CADC,CAAZ;AAEH,WAnFgH,CAqFjH;;;AACA,cAAIC,OAAJ;;AACA,cAAI5D,QAAJ,EAAc;AACV,gBAAI6D,mBAAmB,GAAG,CAA1B;AACA,gBAAIC,kBAAkB,GAAI,CAA1B;;AACA,oBAAO7D,UAAP;AACI,mBAAK;AAAA;AAAA,4CAAWS,SAAhB;AACImD,gBAAAA,mBAAmB,GAAG;AAAA;AAAA,sDAAeE,yBAArC;AACAD,gBAAAA,kBAAkB,GAAI;AAAA;AAAA,sDAAeE,wBAArC;AACA;;AACJ,mBAAK;AAAA;AAAA,4CAAWjD,MAAhB;AACI8C,gBAAAA,mBAAmB,GAAG;AAAA;AAAA,sDAAeI,sBAArC;AACAH,gBAAAA,kBAAkB,GAAI;AAAA;AAAA,sDAAeI,qBAArC;AACA;;AACJ,mBAAK;AAAA;AAAA,4CAAW9C,SAAhB;AACIyC,gBAAAA,mBAAmB,GAAG;AAAA;AAAA,sDAAeM,yBAArC;AACAL,gBAAAA,kBAAkB,GAAI;AAAA;AAAA,sDAAeM,wBAArC;AACA;;AACJ,mBAAK;AAAA;AAAA,4CAAW3C,QAAhB;AACIoC,gBAAAA,mBAAmB,GAAG;AAAA;AAAA,sDAAeQ,uBAArC;AACAP,gBAAAA,kBAAkB,GAAI;AAAA;AAAA,sDAAeQ,sBAArC;AACA;AAhBR;;AAkBA,gBAAIrE,UAAU,IAAI;AAAA;AAAA,0CAAW6B,GAA7B,EAAkC;AAC9B8B,cAAAA,OAAO,GAAG,CAAC,IAAI/D,YAAY,CAACP,sBAAb,CAAoC;AAAA;AAAA,oDAAeiF,gBAAnD,CAAJ,GAA2E1E,YAAY,CAACP,sBAAb,CAAoCuE,mBAApC,CAA5E,KACH,IAAIhE,YAAY,CAACP,sBAAb,CAAoC;AAAA;AAAA,oDAAekF,eAAnD,CAAJ,GAA2E3E,YAAY,CAACP,sBAAb,CAAoCwE,kBAApC,CADxE,CAAV;AAEH,aAHD,MAGO;AACHF,cAAAA,OAAO,GAAG,CAAC,IAAI/D,YAAY,CAACP,sBAAb,CAAoC;AAAA;AAAA,oDAAeiF,gBAAnD,CAAL,KACH,IAAI1E,YAAY,CAACP,sBAAb,CAAoC;AAAA;AAAA,oDAAekF,eAAnD,CADD,CAAV;AAEH;AACJ,WA5BD,MA4BO;AACHZ,YAAAA,OAAO,GAAG,CAAC,IAAI/D,YAAY,CAACP,sBAAb,CAAoC;AAAA;AAAA,kDAAemF,iBAAnD,CAAL,KACH,IAAI5E,YAAY,CAACP,sBAAb,CAAoC;AAAA;AAAA,kDAAeoF,gBAAnD,CADD,CAAV;AAEH,WAtHgH,CAwHjH;;;AACA,cAAIC,MAAM,GAAG,CAAEvE,MAAM,GAAGwD,OAAT,GAAmBzD,cAApB,GAAsCgD,cAAvC,IAAyDC,UAAzD,GAAsEG,SAAnF;AACA,iBAAOqB,IAAI,CAACC,IAAL,CAAUF,MAAV,CAAP;AACH;;AAEDG,QAAAA,QAAQ,GAAU;AACd,iBAAOF,IAAI,CAACG,KAAL,CAAW,KAAKxF,4BAAL,CAAkC;AAAA;AAAA,gDAAeyF,WAAjD,EAA8D;AAAA;AAAA,gDAAeC,WAA7E,EACd;AAAA;AAAA,gDAAeC,UADD,EACa;AAAA;AAAA,gDAAeC,UAD5B,CAAX,CAAP;AAEH;;AAED9E,QAAAA,SAAS,GAAU;AACf,iBAAOuE,IAAI,CAACG,KAAL,CAAW,KAAKxF,4BAAL,CACd;AAAA;AAAA,gDAAe6F,YADD,EACe;AAAA;AAAA,gDAAeC,YAD9B,EAEd;AAAA;AAAA,gDAAeC,WAFD,EAEc;AAAA;AAAA,gDAAeC,WAF7B,CAAX,CAAP;AAGH;;AAEDC,QAAAA,aAAa,GAAU;AACnB,iBAAOZ,IAAI,CAACG,KAAL,CAAW,KAAKxF,4BAAL,CACV;AAAA;AAAA,gDAAekG,gBADL,EACuB;AAAA;AAAA,gDAAeC,gBADtC,EAEV;AAAA;AAAA,gDAAeC,eAFL,EAEsB;AAAA;AAAA,gDAAeC,eAFrC,IAGZ,KAAKd,QAAL,MACK,KAAKxF,sBAAL,CAA4B;AAAA;AAAA,gDAAeuG,oBAA3C,IACI,KAAKvG,sBAAL,CAA4B;AAAA;AAAA,gDAAewG,mBAA3C,CAFT,IAE2E,KAL1E,CAAP;AAMH;;AA9NsB,O", "sourcesContent": ["import { AttributeConst } from \"db://assets/bundles/common/script/const/AttributeConst\";\r\nimport { DamageType } from \"../../autogen/luban/schema\";\r\n\r\nexport class AttributeData {\r\n    private _baseAttributes: Map<AttributeConst, number> = new Map(); // 基础属性\r\n    private _Attributes: Map<AttributeConst, number> = new Map(); // 缓存最终属性值\r\n    private _AttributeModifies: Map<AttributeConst, Map<number, number>> = new Map();\r\n    private _sources: Map<number,  AttributeConst[]> = new Map()// 属性来源\r\n\r\n\r\n    // 静态属性\r\n    public bulletHurtRate = 0;\r\n    public boomHurtRate = 0;\r\n\r\n    constructor() {\r\n    }\r\n\r\n\r\n    addBaseAttribute(key: AttributeConst, value: number) {\r\n        this._baseAttributes.set(key, this._baseAttributes.get(key)||0 + value);\r\n        this.recalculateAttributes(key)\r\n    }\r\n\r\n    // 添加属性来源\r\n    addModify(id: number, key:AttributeConst, value: number) {\r\n        let modify = this._AttributeModifies.get(key);\r\n        if (!modify) {\r\n            modify = new Map()\r\n            this._AttributeModifies.set(id, modify)\r\n        }\r\n        modify.set(id, value)\r\n\r\n        let source = this._sources.get(key);\r\n        if (!source) {\r\n            source = []\r\n            this._sources.set(key, source);\r\n        }\r\n        source.push(id);\r\n\r\n        this.recalculateAttributes(key);\r\n    }\r\n\r\n    // 移除属性来源\r\n    removeModify(id: number) {\r\n        let source = this._sources.get(id)\r\n        source?.forEach((key) => {\r\n            let modify = this._AttributeModifies.get(key);\r\n            if (modify) {\r\n                modify.delete(id);\r\n            }\r\n            this.recalculateAttributes(key);\r\n        });\r\n        this._sources.delete(id);\r\n    }\r\n\r\n    // 重新计算属性\r\n    private recalculateAttributes(key:AttributeConst) {\r\n        let finalValue = this._baseAttributes.get(key) || 0;\r\n\r\n        // 遍历所有属性来源\r\n        this._AttributeModifies.forEach(source => {\r\n            source.forEach(value => {\r\n                finalValue += value;\r\n            });\r\n        });\r\n\r\n        // 计算最终属性值: (基础值 + 加法值) * (1 + 百分比值/10000)\r\n        this._Attributes.set(key, finalValue);\r\n    }\r\n\r\n    getFinalAttributeByKey(key:AttributeConst):number {\r\n        return this._Attributes.get(key) || 0\r\n    }\r\n\r\n    getFinialAttributeByOutInKey(outAddKey:AttributeConst, outPreKey:AttributeConst, \r\n        inAddKey:AttributeConst, inPerKey:AttributeConst): number {\r\n\r\n        return (this.getFinalAttributeByKey(outAddKey) * (1 + this.getFinalAttributeByKey(outPreKey) / 10000) \r\n            + this.getFinalAttributeByKey(inAddKey)) * (1 + this.getFinalAttributeByKey(inPerKey) / 10000)\r\n    }\r\n\r\n    static CalcBulletDamage(attackerAttr: AttributeData, defenderAttr: AttributeData, \r\n            hurtRate: number, isBullet: boolean, damageType: DamageType, isBoss: boolean, attackPowerFix:number): number {\r\n\r\n        // ①局外属性面板上显示的攻击力值=(攻击力局外绝对值1+攻击力局外绝对值2+…)×(1+攻击力局外百分比1+攻击力局外百分比2+…)\r\n        // ②攻击力局内总值=(①局外属性面板上显示的攻击力值+攻击力局内绝对值1+攻击力局内绝对值2+⋯)×(1+攻击力局内百分比1+攻击力局内百分比2+⋯)\r\n        let attack = attackerAttr.getAttack();\r\n\r\n        // ③子弹or核弹的局内攻击力=[(②攻击力局内总值×攻击转换系数+子弹攻击局外绝对值1+子弹攻击局外绝对值2+⋯)×(1+子弹攻击局外百分比1+子弹攻击局外百分比2+⋯)+子弹攻击局内绝对值1+子弹攻击局内绝对值2+⋯]×(1+子弹攻击局内百分比1+子弹攻击局内百分比2+⋯)\r\n\r\n        if (isBullet) {\r\n            let damageAttackOutAddKey = 0;;\r\n            let damageAttackOutPerKey = 0;;\r\n            let damageAttackInAddKey  = 0;;\r\n            let damageAttackInPerKey  = 0;;\r\n            switch(damageType) {\r\n                case DamageType.EXPLOSIVE:\r\n                    damageAttackOutAddKey = AttributeConst.ExplosiveBulletAttackInAdd\r\n                    damageAttackOutPerKey = AttributeConst.ExplosiveBulletAttackInPer\r\n                    damageAttackInAddKey  = AttributeConst.ExplosiveBulletAttackOutAdd\r\n                    damageAttackInPerKey  = AttributeConst.ExplosiveBulletAttackOutPer\r\n                    break;\r\n                case DamageType.NORMAL:\r\n                    damageAttackOutAddKey = AttributeConst.NormalBulletAttackInAdd\r\n                    damageAttackOutPerKey = AttributeConst.NormalBulletAttackInPer\r\n                    damageAttackInAddKey  = AttributeConst.NormalBulletAttackOutAdd\r\n                    damageAttackInPerKey  = AttributeConst.NormalBulletAttackOutPer\r\n                    break;\r\n                case DamageType.ENERGETIC:\r\n                    damageAttackOutAddKey = AttributeConst.EnergeticBulletAttackInAdd\r\n                    damageAttackOutPerKey = AttributeConst.EnergeticBulletAttackInPer\r\n                    damageAttackInAddKey  = AttributeConst.EnergeticBulletAttackOutAdd\r\n                    damageAttackInPerKey  = AttributeConst.EnergeticBulletAttackOutPer\r\n                    break;\r\n                case DamageType.PHYSICAL:\r\n                    damageAttackOutAddKey = AttributeConst.PhysicsBulletAttackInAdd\r\n                    damageAttackOutPerKey = AttributeConst.PhysicsBulletAttackInPer\r\n                    damageAttackInAddKey  = AttributeConst.PhysicsBulletAttackOutAdd\r\n                    damageAttackInPerKey  = AttributeConst.PhysicsBulletAttackOutPer\r\n                    break;\r\n            }\r\n            if (damageType != DamageType.ALL) {\r\n                attack = (((attack * hurtRate + attackerAttr.getFinalAttributeByKey(AttributeConst.BulletAttackOutAdd)\r\n                            + attackerAttr.getFinalAttributeByKey(damageAttackOutAddKey))\r\n                        * (1 + (attackerAttr.getFinalAttributeByKey(AttributeConst.BulletAttackOutPer)\r\n                            + attackerAttr.getFinalAttributeByKey(damageAttackOutPerKey)) / 10000))\r\n                    + attackerAttr.getFinalAttributeByKey(AttributeConst.BulletAttackInAdd)\r\n                    + attackerAttr.getFinalAttributeByKey(damageAttackInAddKey)) \r\n                    * (1 + (attackerAttr.getFinalAttributeByKey(AttributeConst.BulletAttackInPer) \r\n                        + attackerAttr.getFinalAttributeByKey(damageAttackInPerKey)) / 10000);\r\n            } else {\r\n                attack = (((attack * hurtRate + attackerAttr.getFinalAttributeByKey(AttributeConst.BulletAttackOutAdd))\r\n                        * (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.BulletAttackOutPer) / 10000))\r\n                    + attackerAttr.getFinalAttributeByKey(AttributeConst.BulletAttackInAdd)) \r\n                        * (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.BulletAttackInPer) / 10000);\r\n            }\r\n        } else {\r\n            attack = (((attack * hurtRate + attackerAttr.getFinalAttributeByKey(AttributeConst.NuclearAttackOutAdd))\r\n                    * (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.NuclearAttackOutPer) / 10000))\r\n                + attackerAttr.getFinalAttributeByKey(AttributeConst.NuclearAttackInAdd)) \r\n                    * (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.NuclearAttackInPer) / 10000);\r\n        }\r\n\r\n        // ④子弹or核弹伤害抗性局内总值=[(子弹伤害抗性局外绝对值1+子弹伤害抗性局外绝对值2+⋯)×(1+子弹伤害抗性局外百分比1+子弹伤害抗性局外百分比2+⋯)+子弹伤害抗性局内绝对值1+子弹伤害抗性局内绝对值2+⋯]×(1+子弹伤害抗性局内百分比1+子弹伤害抗性局内百分比2+⋯)\r\n        let hurtResistanceOutAddKey = isBullet ? AttributeConst.BulletHurtResistanceOutAdd: AttributeConst.NuclearHurtResistanceOutAdd;\r\n        let hurtResistanceOutPerKey = isBullet ? AttributeConst.BulletHurtResistanceOutPer: AttributeConst.NuclearHurtResistanceOutPer;\r\n        let hurtResistanceInAddKey = isBullet ? AttributeConst.BulletHurtResistanceInAdd: AttributeConst.NuclearHurtResistanceInAdd;\r\n        let hurtResistanceInPerKey = isBullet ? AttributeConst.BulletHurtResistanceInPer: AttributeConst.NuclearHurtResistanceInPer;\r\n\r\n        let hurtResistance = defenderAttr.getFinialAttributeByOutInKey(\r\n            hurtResistanceOutAddKey, hurtResistanceOutPerKey,\r\n            hurtResistanceInAddKey, hurtResistanceInPerKey);\r\n\r\n        // ⑤承受子弹伤害%局内总值=(1-子弹伤害减免局外百分比1-子弹伤害减免局外百分比2-…)×(1-子弹伤害减免局内百分比1-子弹伤害减免局内百分比2-…)\r\n        let hurtDerate = (1 - defenderAttr.getFinalAttributeByKey(AttributeConst.BulletHurtDerateOut)) \r\n            * (1 - defenderAttr.getFinalAttributeByKey(AttributeConst.BulletHurtDerateIn));\r\n\r\n        // ⑥对boss or 普通怪物伤害%局内总值=(1+对boss or 普通怪物伤害局外百分比1+对boss or 普通怪物伤害局外百分比2+⋯)×(1+对boss or 普通怪物伤害局内百分比1+对boss or 普通怪物伤害局内百分比2+⋯)\r\n        let hurtBonus: number\r\n        if (isBoss) {\r\n            hurtBonus = (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.BossHurtBonusOut)) \r\n                * (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.BossHurtBonusIn));\r\n        } else {\r\n            hurtBonus = (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.NormalHurtBonusOut)) \r\n                * (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.NormalHurtBonusIn));\r\n        }\r\n\r\n        //⑦子弹（分类型）or核弹的局内伤害修正=(1+子弹伤害局外百分比1+子弹伤害局外百分比2+⋯)×(1+子弹伤害局内百分比1+子弹伤害局内百分比2+⋯)\r\n        let hurtFix:number\r\n        if (isBullet) {\r\n            let damageHurtFixOutKey = 0;\r\n            let damageHurtFixInKey  = 0;\r\n            switch(damageType) {\r\n                case DamageType.EXPLOSIVE:\r\n                    damageHurtFixOutKey = AttributeConst.ExplosiveBulletHurtFixOut\r\n                    damageHurtFixInKey  = AttributeConst.ExplosiveBulletHurtFixIn\r\n                    break;\r\n                case DamageType.NORMAL:\r\n                    damageHurtFixOutKey = AttributeConst.NormalBulletHurtFixOut\r\n                    damageHurtFixInKey  = AttributeConst.NormalBulletHurtFixIn\r\n                    break;\r\n                case DamageType.ENERGETIC:\r\n                    damageHurtFixOutKey = AttributeConst.EnergeticBulletHurtFixOut\r\n                    damageHurtFixInKey  = AttributeConst.EnergeticBulletHurtFixIn\r\n                    break;\r\n                case DamageType.PHYSICAL:\r\n                    damageHurtFixOutKey = AttributeConst.PhysicsBulletHurtFixOut\r\n                    damageHurtFixInKey  = AttributeConst.PhysicsBulletHurtFixIn\r\n                    break;\r\n            }\r\n            if (damageType != DamageType.ALL) {\r\n                hurtFix = (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.BulletHurtFixOut) + attackerAttr.getFinalAttributeByKey(damageHurtFixOutKey)) \r\n                    * (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.BulletHurtFixIn) +  attackerAttr.getFinalAttributeByKey(damageHurtFixInKey));\r\n            } else {\r\n                hurtFix = (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.BulletHurtFixOut)) \r\n                    * (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.BulletHurtFixIn));\r\n            }\r\n        } else {\r\n            hurtFix = (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.NuclearHurtFixOut)) \r\n                * (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.NuclearHurtFixIn));\r\n        }\r\n\r\n        // ⑧子弹or核弹最终造成的伤害=[(③子弹or核弹的局内攻击力×⑦子弹or核弹的局内伤害修正×战斗力差伤害修正百分比)-④受击方子弹伤害抗性局内总值]×⑤受击方承受子弹伤害%局内总值×(1+⑥攻击方对boss or 普通怪物伤害%局内总值)\r\n        let damage = ((attack * hurtFix * attackPowerFix) - hurtResistance) * hurtDerate * hurtBonus;\r\n        return Math.ceil(damage);\r\n    }\r\n\r\n    getMaxHP():number {\r\n        return Math.floor(this.getFinialAttributeByOutInKey(AttributeConst.MaxHPOutAdd, AttributeConst.MaxHPOutPer, \r\n            AttributeConst.MaxHPInAdd, AttributeConst.MaxHPInPer));\r\n    }\r\n\r\n    getAttack():number {\r\n        return Math.floor(this.getFinialAttributeByOutInKey(\r\n            AttributeConst.AttackOutAdd, AttributeConst.AttackOutPer, \r\n            AttributeConst.AttackInAdd, AttributeConst.AttackInPer));\r\n    }\r\n\r\n    getHPRecovery():number {\r\n        return Math.floor(this.getFinialAttributeByOutInKey(\r\n                AttributeConst.HPRecoveryOutAdd, AttributeConst.HPRecoveryOutPer, \r\n                AttributeConst.HPRecoveryInAdd, AttributeConst.HPRecoveryInPer)\r\n            + this.getMaxHP() \r\n                * (this.getFinalAttributeByKey(AttributeConst.MaxHPRecoveryRateOut) \r\n                    + (this.getFinalAttributeByKey(AttributeConst.MaxHPRecoveryRateIn)))/10000);\r\n    }\r\n    \r\n}"]}