{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/LevelPrefabParse.ts"], "names": ["_decorator", "Component", "Vec2", "ccclass", "property", "executeInEditMode", "LevelPrefabParse", "onLoad", "console", "log", "onDisable", "data", "node", "children", "for<PERSON>ach", "push", "uuid", "_prefab", "asset", "_uuid", "position", "x", "y", "scale", "rotation", "z", "_exportDataAsJson", "jsonData", "JSON", "stringify", "fileName", "name", "assetPath", "sourceAssetInfo", "Editor", "Message", "request", "error", "_createAsset", "_saveAsset", "createRsp", "rsp", "send", "_refreshAssetDb"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;;;;;;;;;OAE1B;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CL,U;;kCAIpCM,gB,WAFZH,OAAO,CAAC,kBAAD,C,UACPE,iBAAiB,E,+BADlB,MAEaC,gBAFb,SAEsCL,SAFtC,CAEgD;AAElCM,QAAAA,MAAM,GAAS;AACrBC,UAAAA,OAAO,CAACC,GAAR,CAAY,yBAAZ;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxBF,UAAAA,OAAO,CAACC,GAAR,CAAY,4BAAZ;AACA,cAAIE,IAAwB,GAAG,EAA/B;AACA,eAAKC,IAAL,CAAUC,QAAV,CAAmBC,OAAnB,CAA4BF,IAAD,IAAU;AACjCD,YAAAA,IAAI,CAACI,IAAL,CAAU;AACN;AACAC,cAAAA,IAAI,EAAEJ,IAAI,CAACK,OAAL,CAAaC,KAAb,CAAmBC,KAFnB;AAGNC,cAAAA,QAAQ,EAAE,IAAIlB,IAAJ,CAASU,IAAI,CAACQ,QAAL,CAAcC,CAAvB,EAA0BT,IAAI,CAACQ,QAAL,CAAcE,CAAxC,CAHJ;AAINC,cAAAA,KAAK,EAAE,IAAIrB,IAAJ,CAASU,IAAI,CAACW,KAAL,CAAWF,CAApB,EAAuBT,IAAI,CAACW,KAAL,CAAWD,CAAlC,CAJD;AAKNE,cAAAA,QAAQ,EAAEZ,IAAI,CAACY,QAAL,CAAcC;AALlB,aAAV;AAOH,WARD;;AAUA,eAAKC,iBAAL,CAAuBf,IAAvB;AACH;AAED;AACJ;AACA;AACA;;;AACmC,cAAjBe,iBAAiB,CAACf,IAAD,EAA2B;AACtD,gBAAMgB,QAAQ,GAAGC,IAAI,CAACC,SAAL,CAAelB,IAAf,EAAqB,IAArB,EAA2B,CAA3B,CAAjB;AAEA,gBAAMmB,QAAQ,GAAI,GAAE,KAAKlB,IAAL,CAAUmB,IAAK,OAAnC;AACA,gBAAMC,SAAS,GAAI,6DAA4DF,QAAS,EAAxF;AAEAtB,UAAAA,OAAO,CAACC,GAAR,CAAY,oCAAZ,EAAkDuB,SAAlD,EANsD,CAOtD;;AACA,gBAAMC,eAAe,GAAG,MAAMC,MAAM,CAACC,OAAP,CAAeC,OAAf,CAAuB,UAAvB,EAAmC,kBAAnC,EAAuDJ,SAAvD,CAA9B;;AACA,cAAIC,eAAe,KAAK,IAAxB,EAA8B;AAC1BzB,YAAAA,OAAO,CAAC6B,KAAR,CAAc,WAAd;;AACA,iBAAKC,YAAL,CAAkBN,SAAlB,EAA6BL,QAA7B;AACH,WAHD,MAGO;AACHnB,YAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ,EAA0BwB,eAA1B;;AACA,iBAAKM,UAAL,CAAgBN,eAAe,CAAEjB,IAAjC,EAAuCW,QAAvC;AACH;AACJ;AAED;AACJ;AACA;;;AAC8B,cAAZW,YAAY,CAACN,SAAD,EAAoBL,QAApB,EAAsC;AAC5D;AACA,cAAIa,SAAS,GAAG,MAAMN,MAAM,CAACC,OAAP,CAAeC,OAAf,CAAuB,UAAvB,EAAmC,cAAnC,EAAmDJ,SAAnD,EAA8DL,QAA9D,CAAtB;AACH;AAED;AACJ;AACA;;;AAC4B,cAAVY,UAAU,CAACvB,IAAD,EAAeW,QAAf,EAAiC;AACrD;AACA,gBAAMc,GAAG,GAAG,MAAMP,MAAM,CAACC,OAAP,CAAeO,IAAf,CAAoB,UAApB,EAAgC,YAAhC,EAA8C1B,IAA9C,EAAoDW,QAApD,CAAlB;;AAEA,eAAKgB,eAAL;AACH;AAED;AACJ;AACA;;;AACiC,cAAfA,eAAe,GAAG;AAC5B;AACAT,UAAAA,MAAM,CAACC,OAAP,CAAeO,IAAf,CAAoB,UAApB,EAAgC,eAAhC,EAAkD,2DAAlD;AACH;;AApE2C,O", "sourcesContent": ["import { _decorator, Component, Vec2 } from 'cc';\r\nimport { LevelDataTerrain } from \"db://assets/bundles/common/script/leveldata/leveldata\";\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\n\r\n@ccclass('LevelPrefabParse')\r\n@executeInEditMode()\r\nexport class LevelPrefabParse extends Component {\r\n\r\n    protected onLoad(): void {\r\n        console.log(\"LevelPrefabParse onLoad\");\r\n    }\r\n\r\n    protected onDisable(): void {\r\n        console.log(\"LevelPrefabParse onDisable\");\r\n        let data: LevelDataTerrain[] = [];\r\n        this.node.children.forEach((node) => {\r\n            data.push({\r\n                // @ts-ignore\r\n                uuid: node._prefab.asset._uuid,\r\n                position: new Vec2(node.position.x, node.position.y),\r\n                scale: new Vec2(node.scale.x, node.scale.y),  \r\n                rotation: node.rotation.z,\r\n            });\r\n        });\r\n\r\n        this._exportDataAsJson(data);\r\n    }\r\n\r\n    /**\r\n     * 将数据导出为JSON文件\r\n     * @param data 要导出的数据\r\n     */\r\n    private async _exportDataAsJson(data: LevelDataTerrain[]) {\r\n        const jsonData = JSON.stringify(data, null, 2);\r\n        \r\n        const fileName = `${this.node.name}.json`;\r\n        const assetPath = `db://assets/resources/game/level/background/Prefab/Config/${fileName}`;\r\n        \r\n        console.log(\"LevelPrefabParse _exportDataAsJson\", assetPath);\r\n        // @ts-ignore\r\n        const sourceAssetInfo = await Editor.Message.request('asset-db', 'query-asset-info', assetPath);\r\n        if (sourceAssetInfo === null) {\r\n            console.error('查询资源信息失败:');\r\n            this._createAsset(assetPath, jsonData);\r\n        } else {\r\n            console.log('导出预制体配置信息:', sourceAssetInfo);\r\n            this._saveAsset(sourceAssetInfo!.uuid, jsonData);\r\n        }\r\n    } \r\n\r\n    /**\r\n     * 创建新资源\r\n     */\r\n    private async _createAsset(assetPath: string, jsonData: string) {\r\n        // @ts-ignore\r\n        var createRsp = await Editor.Message.request('asset-db', 'create-asset', assetPath, jsonData);\r\n    }\r\n    \r\n    /**\r\n     * 更新现有资源\r\n     */\r\n    private async _saveAsset(uuid: string, jsonData: string) {\r\n        // @ts-ignore\r\n        const rsp = await Editor.Message.send('asset-db', 'save-asset', uuid, jsonData);\r\n        \r\n        this._refreshAssetDb();\r\n    }\r\n    \r\n    /**\r\n     * 刷新资源数据库\r\n     */\r\n    private async _refreshAssetDb() {\r\n        // @ts-ignore\r\n        Editor.Message.send('asset-db', 'refresh-asset', `db://assets/resources/game/level/background/Prefab/Config`);\r\n    }\r\n}\r\n\r\n"]}