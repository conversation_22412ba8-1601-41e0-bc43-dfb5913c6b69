import { _decorator, Component, Node } from 'cc';
import { GameIns } from '../../GameIns';

const { ccclass, property } = _decorator;

@ccclass('BattleLayer')
export default class BattleLayer extends Component {

    @property(Node)
    enemyPlaneLayer: Node | null = null;

    @property(Node)
    enemyBulletLayer: Node | null = null;

    @property(Node)
    enemyEffectLayer: Node | null = null;

    @property(Node)
    selfPlaneLayer: Node | null = null;

    @property(Node)
    friendPlaneLayer: Node | null = null;

    @property(Node)
    selfBulletLayer: Node | null = null;

    static instance: BattleLayer;

    onLoad() {
        BattleLayer.instance = this;
    }

    addBullet(bullet: { enemy: boolean; node: Node }) {
        if (bullet.enemy) {
            bullet.node.parent = this.enemyBulletLayer;
        } else {
            bullet.node.parent = this.selfBulletLayer;
        }
    }

    addMainPlane() {
        GameIns.mainPlaneManager.mainPlane!.node.parent = this.selfPlaneLayer;
    }

    addFriendPlane(plane: { node: Node }) {
        plane.node.parent = this.friendPlaneLayer;
    }

    addEnemy(node: Node, zIndex: number = 0) {
        node.parent = this.enemyPlaneLayer;
        node.setSiblingIndex(zIndex);
    }

    addMissile(node: Node) {
        node.parent = this.enemyBulletLayer;
    }

    addMissileWarn(node: Node) {
        node.parent = this.enemyPlaneLayer;
    }
}