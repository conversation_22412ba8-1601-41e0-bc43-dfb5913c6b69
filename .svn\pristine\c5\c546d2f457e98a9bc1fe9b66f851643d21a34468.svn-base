import { _decorator, Component, instantiate, Node, Prefab, view } from "cc";
import { MyApp } from 'db://assets/bundles/common/script/app/MyApp';
import { LevelDataEvent, LevelDataLayer } from "../../../leveldata/leveldata";
import { LevelDataEventTriggerType } from "../../../leveldata/trigger/LevelDataEventTrigger";
import { LevelDataEventTriggerLog } from "../../../leveldata/trigger/LevelDataEventTriggerLog";
import { LevelDataEventTriggerWave, LevelDataEventWaveGroup } from "../../../leveldata/trigger/LevelDataEventTriggerWave";
import { LevelDataEventTriggerSpecialEvent } from "../../../leveldata/trigger/LevelDataEventTriggerSpecialEvent";
import { LevelDataEventCondtionDelayDistance } from "../../../leveldata/condition/LevelDataEventCondtionDelayDistance";
import { LevelDataEventCondtionDelayTime } from "../../../leveldata/condition/LevelDataEventCondtionDelayTime";
import { LevelDataEventCondtionWave, LevelDataEventConditionWaveType } from "../../../leveldata/condition/LevelDataEventCondtionWave";
import { LevelDataEventCondtion, LevelDataEventCondtionComb, LevelDataEventCondtionType } from "../../../leveldata/condition/LevelDataEventCondtion";
import GameMapRun from "./GameMapRun";
import { LevelLayerUI } from "./LevelLayerUI";
import EventManager from "db://assets/bundles/common/script/event/EventManager";
import { GameEvent } from "db://assets/bundles/common/script/game/event/GameEvent";
const { ccclass } = _decorator;

enum LevelEventStatus {
    Ready,     // 等待中(关卡位置未达到)
    Waiting,   // 等待条件完成
    Triggered, // 条件已触发
}

/// 有状态的关卡事件执行逻辑
export class LevelEventRun {
    private _status: LevelEventStatus = LevelEventStatus.Ready;
    private _data: LevelDataEvent = null!;
    private _layer: LevelLayerUI = null!;
    
    constructor(dataEvent: LevelDataEvent, layer: LevelLayerUI) {
        this._data = dataEvent;
        this._layer = layer;
        this._data.triggers?.forEach((trigger) => {
            trigger.onInit();
        });
    }

    public get isTriggered() {
        return this._status === LevelEventStatus.Triggered;
    }

    public get data(): LevelDataEvent {
        return this._data;
    }

    start() {
        this._status = LevelEventStatus.Waiting;
    }

    tick(currentPosY: number) {
        switch (this._status) {
            case LevelEventStatus.Ready:
                // console.log('checking levelevent : ', currentPosY);
                if (this._data.position.y < currentPosY) {
                    // console.log(`LevelEventRun tick ${this._data.elemID}, currentPosY: ${currentPosY}, eventPosY: ${this._data.position.y}`);
                    this._status = LevelEventStatus.Waiting;
                }
                break;
            case LevelEventStatus.Waiting:
                let condResult = this.evalConditions(this._data.conditions);
                if (condResult) {
                    this.execActions(this._data);
                    this._status = LevelEventStatus.Triggered;
                }
                break;
            default: break;
        }
    }

    private evalConditions(conditions: LevelDataEventCondtion[]): boolean {
        let result = true;
        const currentDistance = GameMapRun.instance?.levelDistance ?? 0;
        const currentTime = GameMapRun.instance?.levelDuration ?? 0;
        for (let cond of conditions) {
            let condEvalRet: boolean = true;
            switch (cond._type) {
                case LevelDataEventCondtionType.DelayTime:
                    condEvalRet = currentTime >= (cond as LevelDataEventCondtionDelayTime).time;
                    break;
                case LevelDataEventCondtionType.DelayDistance:
                    condEvalRet = currentDistance >= (cond as LevelDataEventCondtionDelayDistance).distance;
                    break;
                case LevelDataEventCondtionType.Wave:
                    const targetEvent = this._layer.getEventByElemID((cond as LevelDataEventCondtionWave).targetElemID);
                    if (!targetEvent) {
                        condEvalRet = false;
                        break;
                    }
                    switch ((cond as LevelDataEventCondtionWave).waveCondType) {
                        case LevelDataEventConditionWaveType.WaveSpawned:
                            for (let trigger of targetEvent!.triggers) {
                                if (trigger._type == LevelDataEventTriggerType.Wave) {
                                    if (!(trigger as LevelDataEventTriggerWave).isWaveSpawned()) {
                                        condEvalRet = false;
                                        break;
                                    }
                                }
                            }
                            break;
                        case LevelDataEventConditionWaveType.WaveCleared:
                            for (let trigger of targetEvent!.triggers) {
                                if (trigger._type == LevelDataEventTriggerType.Wave) {
                                    if (!(trigger as LevelDataEventTriggerWave).isWaveCleared()) {
                                        condEvalRet = false;
                                        break;
                                    }
                                }
                            }
                            break;
                        case LevelDataEventConditionWaveType.WaveTriggered:
                            for (let trigger of targetEvent!.triggers) {
                                if (trigger._type == LevelDataEventTriggerType.Wave) {
                                    if (!(trigger as LevelDataEventTriggerWave).isWaveTriggered()) {
                                        condEvalRet = false;
                                        break;
                                    }
                                }
                            }
                            break;
                    }    
                    break;
            }
            
            if (cond.comb === LevelDataEventCondtionComb.And) {
                result = result && condEvalRet;
            } else {
                result = result || condEvalRet;
            }
        }
        return result;
    }

    private execActions(event: LevelDataEvent): void {
        for (let trigger of event.triggers) {
            switch (trigger._type) {
                case LevelDataEventTriggerType.Log:
                    console.log("LevelEventRun", "trigger log", (trigger as LevelDataEventTriggerLog).message);
                    break;
                case LevelDataEventTriggerType.Audio:
                    break;
                case LevelDataEventTriggerType.Wave:
                    console.warn("LevelEventRun", "trigger wave : ", GameMapRun.instance?.levelDuration);
                    const waveTrigger = trigger as LevelDataEventTriggerWave;
                    if (!waveTrigger.waveGroup || waveTrigger.waveGroup.length == 0) {
                        break;
                    }
                    
                    waveTrigger.onTrigger(event.position.x, Math.max(0, event.position.y - this._layer.node.position.y));
                    break;
                case LevelDataEventTriggerType.SpecialEvent:
                    EventManager.Instance.emit(GameEvent.onLevelSpecialEvent, (trigger as LevelDataEventTriggerSpecialEvent).eventType);
                    break;
                default:
                    break;
            }
        }
    }
}

