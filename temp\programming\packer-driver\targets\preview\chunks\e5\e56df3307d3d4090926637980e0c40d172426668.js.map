{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/leveldata.ts"], "names": ["LevelDataTerrain", "LayerRandomRange", "LevelDataScroll", "LevelDataRandTerrain", "LevelDataRandTerrains", "LevelDataRandTerrainsGroup", "LevelDataElem", "LevelDataWaveGroup", "LevelDataWave", "LevelDataEvent", "LevelDataLayer", "LevelDataBackgroundLayer", "LevelData", "Vec2", "newCondition", "newTrigger", "LayerType", "LayerSplicingMode", "LayerEmittierType", "LayerEmittierStrategy", "uuid", "position", "scale", "rotation", "constructor", "min", "max", "weight", "uuids", "splicingMode", "node_height", "offSetX", "offSetY", "terrains", "group", "elemID", "name", "waveUUID", "waveGroup", "fromJSON", "json", "wave", "Object", "assign", "conditions", "triggers", "event", "map", "condition", "trigger", "remark", "zIndex", "totalTime", "speed", "type", "scrolls", "dynamics", "emittiers", "waves", "events", "terrain", "scroll", "dynamic", "emittier", "layer", "backgrounds", "background<PERSON>ayer", "floorLayers", "skyLayers", "levelData"], "mappings": ";;;+GA+CaA,gB,EAOAC,gB,EAUAC,e,EAQAC,oB,EAOAC,qB,EAKAC,0B,EAIAC,a,EAMAC,kB,EAKAC,a,EAWAC,c,EAoBAC,c,EAwCAC,wB,EAaAC,S;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAvLJC,MAAAA,I,OAAAA,I;;AAGAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;AAJkB;;;2BAMfC,S,0BAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;eAAAA,S;cAOZ;;;mCACYC,iB,0BAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;eAAAA,iB;cAeZ;;;mCACYC,iB,0BAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;eAAAA,iB;cAWZ;;;uCACYC,qB,0BAAAA,qB;AAAAA,QAAAA,qB,CAAAA,qB;AAAAA,QAAAA,qB,CAAAA,qB;eAAAA,qB;;;kCAKCnB,gB,GAAN,MAAMA,gBAAN,CAAuB;AAAA;AAAA,eACnBoB,IADmB,GACJ,EADI;AACA;AADA,eAEnBC,QAFmB,GAEF,IAAIR,IAAJ,EAFE;AAAA,eAGnBS,KAHmB,GAGL,IAAIT,IAAJ,EAHK;AAAA,eAInBU,QAJmB,GAIA,CAJA;AAAA;;AAAA,O;;kCAOjBtB,gB,GAAN,MAAMA,gBAAN,CAAuB;AAI1BuB,QAAAA,WAAW,CAAEC,GAAF,EAAmBC,GAAnB,EAAoC;AAAA,cAAlCD,GAAkC;AAAlCA,YAAAA,GAAkC,GAApB,CAAoB;AAAA;;AAAA,cAAjBC,GAAiB;AAAjBA,YAAAA,GAAiB,GAAH,CAAG;AAAA;;AAAA,eAHxCD,GAGwC,GAH1B,CAG0B;AAAA,eAFxCC,GAEwC,GAF1B,CAE0B;AAC3C,eAAKD,GAAL,GAAWA,GAAX;AACA,eAAKC,GAAL,GAAWA,GAAX;AACH;;AAPyB,O;;iCAUjBxB,e,GAAN,MAAMA,eAAN,CAAsB;AAAA;AAAA,eAClByB,MADkB,GACD,GADC;AAAA,eAElBC,KAFkB,GAEA,EAFA;AAAA,eAGlBC,YAHkB,GAGgBZ,iBAAiB,CAACa,WAHlC;AAAA,eAIlBC,OAJkB,GAIU,IAAI9B,gBAAJ,EAJV;AAAA,eAKlB+B,OALkB,GAKU,IAAI/B,gBAAJ,EALV;AAAA;;AAAA,O;;sCAQhBE,oB,GAAN,MAAMA,oBAAN,CAA2B;AAAA;AAAA,eACvBwB,MADuB,GACN,CADM;AAAA,eAEvBP,IAFuB,GAER,EAFQ;AAAA,eAGvBW,OAHuB,GAGK,IAAI9B,gBAAJ,EAHL;AAAA,eAIvB+B,OAJuB,GAIK,IAAI/B,gBAAJ,EAJL;AAAA;;AAAA,O;;uCAOrBG,qB,GAAN,MAAMA,qBAAN,SAAoCJ,gBAApC,CAAqD;AAAA;AAAA;AAAA,eACjD2B,MADiD,GAChC,GADgC;AAAA,eAEjDM,QAFiD,GAEd,EAFc;AAAA;;AAAA,O;;4CAK/C5B,0B,GAAN,MAAMA,0BAAN,CAAiC;AAAA;AAAA,eAC7B6B,KAD6B,GACI,EADJ;AAAA;;AAAA,O;;+BAI3B5B,a,GAAN,MAAMA,aAAN,CAAoB;AAAA;AAAA,eAChB6B,MADgB,GACC,EADD;AAAA,eAEhBd,QAFgB,GAEC,IAAIR,IAAJ,EAFD;AAAA,eAGhBuB,IAHgB,GAGD,SAHC;AAAA;;AAAA,O;;oCAMd7B,kB,GAAN,MAAMA,kBAAN,CAAyB;AAAA;AAAA,eACrB8B,QADqB,GACA,EADA;AAAA,eAErBV,MAFqB,GAEJ,EAFI;AAAA;;AAAA,O;;+BAKnBnB,a,GAAN,MAAMA,aAAN,SAA4BF,aAA5B,CAA0C;AAAA;AAAA;AAAA,eACtCgC,SADsC,GACJ,EADI;AAAA;;AAG9B,eAARC,QAAQ,CAACC,IAAD,EAA2B;AACtC,cAAMC,IAAI,GAAG,IAAIjC,aAAJ,EAAb;AACA,cAAI,CAACgC,IAAL,EAAW,OAAOC,IAAP;AACXC,UAAAA,MAAM,CAACC,MAAP,CAAcF,IAAd,EAAoBD,IAApB;AACA,iBAAOC,IAAP;AACH;;AAR4C,O;;gCAWpChC,c,GAAN,MAAMA,cAAN,SAA6BH,aAA7B,CAA2C;AAAA;AAAA;AAAA,eACvCsC,UADuC,GACA,EADA;AAAA,eAEvCC,QAFuC,GAEH,EAFG;AAAA;;AAI/B,eAARN,QAAQ,CAACC,IAAD,EAA4B;AAAA;;AACvC,cAAMM,KAAK,GAAG,IAAIrC,cAAJ,EAAd;AACA,cAAI,CAAC+B,IAAL,EAAW,OAAOM,KAAP;AAEXJ,UAAAA,MAAM,CAACC,MAAP,CAAcG,KAAd,EAAqBN,IAArB;AACAM,UAAAA,KAAK,CAACF,UAAN,GAAmB,qBAAAJ,IAAI,CAACI,UAAL,sCAAiBG,GAAjB,CAAsBC,SAAD,IAAoB;AACxD,mBAAO;AAAA;AAAA,8CAAaA,SAAb,CAAP;AACH,WAFkB,MAEb,EAFN;AAGAF,UAAAA,KAAK,CAACD,QAAN,GAAiB,mBAAAL,IAAI,CAACK,QAAL,oCAAeE,GAAf,CAAoBE,OAAD,IAAkB;AAClD,mBAAO;AAAA;AAAA,0CAAWA,OAAX,CAAP;AACH,WAFgB,MAEX,EAFN;AAIA,iBAAOH,KAAP;AACH;;AAjB6C,O;;gCAoBrCpC,c,GAAN,MAAMA,cAAN,CAAqB;AAAA;AAAA,eACjBwC,MADiB,GACA,EADA;AAAA,eAEjBC,MAFiB,GAEA,CAFA;AAAA,eAGjBC,SAHiB,GAGG,CAHH;AAAA,eAIjBC,KAJiB,GAID,GAJC;AAAA,eAKjBC,IALiB,GAKF,CALE;AAKC;AALD,eAMjBrB,QANiB,GAMc,EANd;AAAA,eAOjBsB,OAPiB,GAOY,EAPZ;AAAA,eAQjBC,QARiB,GAQwB,EARxB;AAAA,eASjBC,SATiB,GASe,EATf;AAAA,eAUjBC,KAViB,GAUQ,EAVR;AAAA,eAWjBC,MAXiB,GAWU,EAXV;AAAA;;AAadhB,QAAAA,MAAM,CAACH,IAAD,EAAiB;AAAA;;AAC7BE,UAAAA,MAAM,CAACC,MAAP,CAAc,IAAd,EAAoBH,IAApB;AAEA,eAAKP,QAAL,GAAgB,mBAAAO,IAAI,CAACP,QAAL,oCAAec,GAAf,CAAoBa,OAAD,IAC/BlB,MAAM,CAACC,MAAP,CAAc,IAAI3C,gBAAJ,EAAd,EAAsC4D,OAAtC,CADY,MACuC,EADvD;AAEA,eAAKL,OAAL,GAAe,kBAAAf,IAAI,CAACe,OAAL,mCAAcR,GAAd,CAAmBc,MAAD,IAC7BnB,MAAM,CAACC,MAAP,CAAc,IAAIzC,eAAJ,EAAd,EAAqC2D,MAArC,CADW,MACsC,EADrD;AAEA,eAAKL,QAAL,GAAgB,mBAAAhB,IAAI,CAACgB,QAAL,oCAAeT,GAAf,CAAoBe,OAAD,IAC/BpB,MAAM,CAACC,MAAP,CAAc,IAAItC,0BAAJ,EAAd,EAAgDyD,OAAhD,CADY,MACiD,EADjE;AAEA,eAAKL,SAAL,GAAiB,oBAAAjB,IAAI,CAACiB,SAAL,qCAAgBV,GAAhB,CAAqBgB,QAAD,IACjCrB,MAAM,CAACC,MAAP,CAAc,IAAI3C,gBAAJ,EAAd,EAAsC+D,QAAtC,CADa,MACuC,EADxD;AAEA,eAAKL,KAAL,GAAa,gBAAAlB,IAAI,CAACkB,KAAL,iCAAYX,GAAZ,CAAiBN,IAAD,IACzBjC,aAAa,CAAC+B,QAAd,CAAuBE,IAAvB,CADS,MACwB,EADrC;AAEA,eAAKkB,MAAL,GAAc,iBAAAnB,IAAI,CAACmB,MAAL,kCAAaZ,GAAb,CAAkBD,KAAD,IAC3BrC,cAAc,CAAC8B,QAAf,CAAwBO,KAAxB,CADU,MACyB,EADvC;AAEH;;AAEc,eAARP,QAAQ,CAACC,IAAD,EAA4B;AACvC,cAAMwB,KAAK,GAAG,IAAItD,cAAJ,EAAd;AACA,cAAI,CAAC8B,IAAL,EAAW,OAAOwB,KAAP;AAEXA,UAAAA,KAAK,CAACrB,MAAN,CAAaH,IAAb;AAEA,iBAAOwB,KAAP;AACH;;AArCuB,O;;0CAwCfrD,wB,GAAN,MAAMA,wBAAN,SAAuCD,cAAvC,CAAsD;AAAA;AAAA;AAAA,eAClDuD,WADkD,GAC1B,EAD0B;AAAA;;AAG1C,eAAR1B,QAAQ,CAACC,IAAD,EAAsC;AACjD,cAAMwB,KAAK,GAAG,IAAIrD,wBAAJ,EAAd;AACA,cAAI,CAAC6B,IAAL,EAAW,OAAOwB,KAAP;AAEXA,UAAAA,KAAK,CAACrB,MAAN,CAAaH,IAAb;AAEA,iBAAOwB,KAAP;AACH;;AAVwD,O;;2BAahDpD,S,GAAN,MAAMA,SAAN,CAAgB;AAAA;AAAA,eACZwB,IADY,GACG,EADH;AAAA,eAEZgB,SAFY,GAEQ,CAFR;AAAA,eAGZc,eAHY,GAGgC,IAAIvD,wBAAJ,EAHhC;AAAA,eAIZwD,WAJY,GAIoB,EAJpB;AAAA,eAKZC,SALY,GAKkB,EALlB;AAAA;;AAOJ,eAAR7B,QAAQ,CAACC,IAAD,EAAuB;AAAA;;AAClC,cAAM6B,SAAS,GAAG,IAAIzD,SAAJ,EAAlB;AACA,cAAI,CAAC4B,IAAL,EAAW,OAAO6B,SAAP;AAEX3B,UAAAA,MAAM,CAACC,MAAP,CAAc0B,SAAd,EAAyB7B,IAAzB;AACA6B,UAAAA,SAAS,CAACH,eAAV,GAA4BvD,wBAAwB,CAAC4B,QAAzB,CAAkCC,IAAI,CAAC0B,eAAvC,CAA5B;AACAG,UAAAA,SAAS,CAACF,WAAV,GAAwB,sBAAA3B,IAAI,CAAC2B,WAAL,uCAAkBpB,GAAlB,CAAuBiB,KAAD,IAC1CtD,cAAc,CAAC6B,QAAf,CAAwByB,KAAxB,CADoB,MACe,EADvC;AAEAK,UAAAA,SAAS,CAACD,SAAV,GAAsB,oBAAA5B,IAAI,CAAC4B,SAAL,qCAAgBrB,GAAhB,CAAqBiB,KAAD,IACtCtD,cAAc,CAAC6B,QAAf,CAAwByB,KAAxB,CADkB,MACiB,EADvC;AAGA,iBAAOK,SAAP;AACH;;AAnBkB,O", "sourcesContent": ["import { Vec2 } from \"cc\"; // 注意：这里的分号是必须的，因为下面的代码是压缩过的\r\nimport { LevelDataEventCondtion } from \"./condition/LevelDataEventCondtion\";\r\nimport { LevelDataEventTrigger } from \"./trigger/LevelDataEventTrigger\";\r\nimport { newCondition } from \"./condition/newCondition\";\r\nimport { newTrigger } from \"./trigger/newTrigger\";\r\n\r\nexport enum LayerType {\r\n    Background = 1,\r\n    Random,\r\n    Scroll,\r\n    Emittier\r\n}\r\n\r\n// 层级拼接方式\r\nexport enum LayerSplicingMode {\r\n    /**\r\n     * @zh 节点高度拼接\r\n     */\r\n    node_height = 1,\r\n    /**\r\n     * @zh 屏幕高度拼接\r\n     */\r\n    fix_height,\r\n    /**\r\n     * @zh 随机间隔拼接\r\n     */\r\n    random_height\r\n}\r\n\r\n// 地形发射体类型\r\nexport enum LayerEmittierType {\r\n    // 无限\r\n    Infinite = 1,\r\n    // 持续时间\r\n    Duration,\r\n    // 发射次数\r\n    Count,\r\n    // 监听事件\r\n    Event\r\n}\r\n\r\n// 地形发射策略\r\nexport enum LayerEmittierStrategy {\r\n    Random, // 随机\r\n    Sequence // 顺序\r\n}\r\n\r\nexport class LevelDataTerrain {\r\n    public uuid: string = \"\"; // 如果有后缀json，需额外解析\r\n    public position: Vec2 = new Vec2();\r\n    public scale: Vec2 = new Vec2();\r\n    public rotation: number = 0;\r\n}\r\n\r\nexport class LayerRandomRange {\r\n    public min: number = 0;\r\n    public max: number = 0;\r\n\r\n    constructor (min: number = 0, max: number = 0) {\r\n        this.min = min;\r\n        this.max = max;\r\n    }\r\n}\r\n\r\nexport class LevelDataScroll {\r\n    public weight: number = 100;\r\n    public uuids: string[] = [];\r\n    public splicingMode: LayerSplicingMode = LayerSplicingMode.node_height;\r\n    public offSetX: LayerRandomRange = new LayerRandomRange();\r\n    public offSetY: LayerRandomRange = new LayerRandomRange();\r\n}\r\n\r\nexport class LevelDataRandTerrain {\r\n    public weight: number = 0;\r\n    public uuid: string = \"\";\r\n    public offSetX: LayerRandomRange = new LayerRandomRange();\r\n    public offSetY: LayerRandomRange = new LayerRandomRange();\r\n}\r\n\r\nexport class LevelDataRandTerrains extends LevelDataTerrain {\r\n    public weight: number = 100;\r\n    public terrains: LevelDataRandTerrain[] = [];\r\n}\r\n\r\nexport class LevelDataRandTerrainsGroup {\r\n    public group: LevelDataRandTerrains[] = [];\r\n}\r\n\r\nexport class LevelDataElem {\r\n    public elemID: string = \"\";\r\n    public position: Vec2 = new Vec2();\r\n    public name: string = \"default\";\r\n}\r\n\r\nexport class LevelDataWaveGroup {\r\n    public waveUUID: string[] = [];\r\n    public weight: number = 50;\r\n}\r\n\r\nexport class LevelDataWave extends LevelDataElem {\r\n    public waveGroup: LevelDataWaveGroup[] = [];\r\n\r\n    static fromJSON(json: any): LevelDataWave {\r\n        const wave = new LevelDataWave();\r\n        if (!json) return wave;\r\n        Object.assign(wave, json);\r\n        return wave;\r\n    }\r\n}\r\n\r\nexport class LevelDataEvent extends LevelDataElem {\r\n    public conditions: LevelDataEventCondtion[] = [];\r\n    public triggers: LevelDataEventTrigger[] = [];\r\n\r\n    static fromJSON(json: any): LevelDataEvent {\r\n        const event = new LevelDataEvent();\r\n        if (!json) return event;\r\n        \r\n        Object.assign(event, json);\r\n        event.conditions = json.conditions?.map((condition: any) => {\r\n            return newCondition(condition);\r\n        }) || [];\r\n        event.triggers = json.triggers?.map((trigger: any) => {\r\n            return newTrigger(trigger);\r\n        }) || [];\r\n        \r\n        return event;\r\n    }\r\n}\r\n\r\nexport class LevelDataLayer {\r\n    public remark: string = \"\";\r\n    public zIndex: number = 0;\r\n    public totalTime: number = 0;\r\n    public speed: number = 200;\r\n    public type: number = 0; // 对应LayerType\r\n    public terrains: LevelDataTerrain[] = [];\r\n    public scrolls: LevelDataScroll[] = [];\r\n    public dynamics: LevelDataRandTerrainsGroup[] = [];\r\n    public emittiers: LevelDataTerrain[] = [];\r\n    public waves: LevelDataWave[] = [];\r\n    public events: LevelDataEvent[] = [];\r\n\r\n    protected assign(json: any):void {\r\n        Object.assign(this, json);\r\n\r\n        this.terrains = json.terrains?.map((terrain: any) => \r\n            Object.assign(new LevelDataTerrain(), terrain)) || [];\r\n        this.scrolls = json.scrolls?.map((scroll: any) => \r\n            Object.assign(new LevelDataScroll(), scroll)) || [];\r\n        this.dynamics = json.dynamics?.map((dynamic: any) => \r\n            Object.assign(new LevelDataRandTerrainsGroup(), dynamic)) || [];\r\n        this.emittiers = json.emittiers?.map((emittier: any) => \r\n            Object.assign(new LevelDataTerrain(), emittier)) || [];\r\n        this.waves = json.waves?.map((wave: any) => \r\n            LevelDataWave.fromJSON(wave)) || [];\r\n        this.events = json.events?.map((event: any) => \r\n            LevelDataEvent.fromJSON(event)) || [];\r\n    }\r\n\r\n    static fromJSON(json: any): LevelDataLayer {\r\n        const layer = new LevelDataLayer();\r\n        if (!json) return layer;\r\n\r\n        layer.assign(json);\r\n        \r\n        return layer;\r\n    }\r\n}\r\n\r\nexport class LevelDataBackgroundLayer extends LevelDataLayer {\r\n    public backgrounds: string[] = [];\r\n    \r\n    static fromJSON(json: any): LevelDataBackgroundLayer {\r\n        const layer = new LevelDataBackgroundLayer();\r\n        if (!json) return layer;\r\n       \r\n        layer.assign(json);\r\n        \r\n        return layer;\r\n    }\r\n}\r\n\r\nexport class LevelData {\r\n    public name: string = \"\";\r\n    public totalTime: number = 0;\r\n    public backgroundLayer: LevelDataBackgroundLayer = new LevelDataBackgroundLayer();\r\n    public floorLayers: LevelDataLayer[] = [];\r\n    public skyLayers: LevelDataLayer[] = [];\r\n\r\n    static fromJSON(json: any): LevelData {\r\n        const levelData = new LevelData();\r\n        if (!json) return levelData;\r\n        \r\n        Object.assign(levelData, json);\r\n        levelData.backgroundLayer = LevelDataBackgroundLayer.fromJSON(json.backgroundLayer);\r\n        levelData.floorLayers = json.floorLayers?.map((layer: any) =>\r\n            LevelDataLayer.fromJSON(layer)) || [];\r\n        levelData.skyLayers = json.skyLayers?.map((layer: any) => \r\n            LevelDataLayer.fromJSON(layer)) || [];\r\n        \r\n        return levelData;\r\n    }\r\n}\r\n"]}