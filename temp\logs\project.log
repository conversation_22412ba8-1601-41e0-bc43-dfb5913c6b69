2025-9-27 14:11:36 - log: Load engine in C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine
2025-9-27 14:11:36 - log: Register native engine in C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\native
2025-9-27 14:11:37 - log: Request namespace: device-list
2025-9-27 14:11:40 - log: emitter-editor extension loaded
2025-9-27 14:11:40 - log: Available methods: [
  'movePlayerUp',
  'movePlayerDown',
  'movePlayerLeft',
  'movePlayerRight',
  'onAssetChanged',
  'createEmitterEnum',
  'createEnemyEnum'
]
2025-9-27 14:11:40 - log: EmitterEnum.ts generated successfully at: E:\M2Game\Client\assets\editor\enum-gen\EmitterEnum.ts
2025-9-27 14:11:40 - log: EnemyEnum.ts generated successfully at: E:\M2Game\Client\assets\editor\enum-gen\EnemyEnum.ts
2025-9-27 14:11:42 - info: [PreviewInEditor] 预览环境初始化完毕
2025-9-27 14:11:52 - log: [Scene] meshopt wasm decoder initialized
2025-9-27 14:11:52 - log: [Scene] [box2d]:box2d wasm lib loaded.
2025-9-27 14:11:52 - log: [Scene] [bullet]:bullet wasm lib loaded.
2025-9-27 14:11:52 - log: [Scene] [PHYSICS]: using builtin.
2025-9-27 14:11:52 - log: [Scene] Cocos Creator v3.8.6
2025-9-27 14:11:56 - warn: [Scene] The type of "PlaneBaseDebug.bufferList" must be CCFloat or CCInteger, not Number.Error: [Scene] The type of "PlaneBaseDebug.bufferList" must be CCFloat or CCInteger, not Number.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseType (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271985:11)
    at preprocessAttrs (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:272090:11)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127901:7)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/4b/4b380754ec2052c80f1e8d238cb60d7d60631247.js:42:51)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-9-27 14:11:56 - warn: [Scene] You are explicitly specifying `undefined` type to cc property "maxHP" of cc class "PlaneBaseDebug".
Is this intended? If not, this may indicate a circular reference.
For example:

 
// foo.ts
import { _decorator } from 'cc';
import { Bar } from './bar';  // Given that './bar' also reference 'foo.ts'.
                              // When importing './bar', execution of './bar' is hung on to wait execution of 'foo.ts',
                              // the `Bar` imported here is `undefined` until './bar' finish its execution.
                              // It leads to that
@_decorator.ccclass           //  ↓
export class Foo {            //  ↓
    @_decorator.type(Bar)     //  → is equivalent to `@_decorator.type(undefined)`
    public bar: Bar;          // To eliminate this error, either:
                              // - Refactor your module structure(recommended), or
                              // - specify the type as cc class name: `@_decorator.type('Bar'/* or any name you specified for `Bar` */)`
}Error: [Scene] You are explicitly specifying `undefined` type to cc property "maxHP" of cc class "PlaneBaseDebug".
Is this intended? If not, this may indicate a circular reference.
For example:

 
// foo.ts
import { _decorator } from 'cc';
import { Bar } from './bar';  // Given that './bar' also reference 'foo.ts'.
@_decorator.ccclass           //  ↓
export class Foo {            //  ↓
    @_decorator.type(Bar)     //  → is equivalent to `@_decorator.type(undefined)`
    public bar: Bar;          // To eliminate this error, either:
                              // - Refactor your module structure(recommended), or
                              // - specify the type as cc class name: `@_decorator.type('Bar'/* or any name you specified for `Bar` */)`
}
    at Logger._logHandler (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\startup\log.ccc:1:487)
    at Logger.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@base\electron-logger\lib\renderer.ccc:1:458)
    at console.<anonymous> (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@base\electron-logger\lib\renderer.ccc:1:1260)
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseAttributes (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127994:7)
    at defineGetSet (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127753:7)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127907:11)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/4b/4b380754ec2052c80f1e8d238cb60d7d60631247.js:42:51)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-9-27 14:11:56 - warn: [Scene] You are explicitly specifying `undefined` type to cc property "hpRecovery" of cc class "PlaneBaseDebug".
Is this intended? If not, this may indicate a circular reference.
For example:

 
// foo.ts
import { _decorator } from 'cc';
import { Bar } from './bar';  // Given that './bar' also reference 'foo.ts'.
                              // When importing './bar', execution of './bar' is hung on to wait execution of 'foo.ts',
                              // the `Bar` imported here is `undefined` until './bar' finish its execution.
                              // It leads to that
@_decorator.ccclass           //  ↓
export class Foo {            //  ↓
    @_decorator.type(Bar)     //  → is equivalent to `@_decorator.type(undefined)`
    public bar: Bar;          // To eliminate this error, either:
                              // - Refactor your module structure(recommended), or
                              // - specify the type as cc class name: `@_decorator.type('Bar'/* or any name you specified for `Bar` */)`
}Error: [Scene] You are explicitly specifying `undefined` type to cc property "hpRecovery" of cc class "PlaneBaseDebug".
Is this intended? If not, this may indicate a circular reference.
For example:

 
// foo.ts
import { _decorator } from 'cc';
import { Bar } from './bar';  // Given that './bar' also reference 'foo.ts'.
@_decorator.ccclass           //  ↓
export class Foo {            //  ↓
    @_decorator.type(Bar)     //  → is equivalent to `@_decorator.type(undefined)`
    public bar: Bar;          // To eliminate this error, either:
                              // - Refactor your module structure(recommended), or
                              // - specify the type as cc class name: `@_decorator.type('Bar'/* or any name you specified for `Bar` */)`
}
    at Logger._logHandler (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\startup\log.ccc:1:487)
    at Logger.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@base\electron-logger\lib\renderer.ccc:1:458)
    at console.<anonymous> (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@base\electron-logger\lib\renderer.ccc:1:1260)
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseAttributes (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127994:7)
    at defineGetSet (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127753:7)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127907:11)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/4b/4b380754ec2052c80f1e8d238cb60d7d60631247.js:42:51)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-9-27 14:11:56 - warn: [Scene] You are explicitly specifying `undefined` type to cc property "curHP" of cc class "PlaneBaseDebug".
Is this intended? If not, this may indicate a circular reference.
For example:

 
// foo.ts
import { _decorator } from 'cc';
import { Bar } from './bar';  // Given that './bar' also reference 'foo.ts'.
                              // When importing './bar', execution of './bar' is hung on to wait execution of 'foo.ts',
                              // the `Bar` imported here is `undefined` until './bar' finish its execution.
                              // It leads to that
@_decorator.ccclass           //  ↓
export class Foo {            //  ↓
    @_decorator.type(Bar)     //  → is equivalent to `@_decorator.type(undefined)`
    public bar: Bar;          // To eliminate this error, either:
                              // - Refactor your module structure(recommended), or
                              // - specify the type as cc class name: `@_decorator.type('Bar'/* or any name you specified for `Bar` */)`
}Error: [Scene] You are explicitly specifying `undefined` type to cc property "curHP" of cc class "PlaneBaseDebug".
Is this intended? If not, this may indicate a circular reference.
For example:

 
// foo.ts
import { _decorator } from 'cc';
import { Bar } from './bar';  // Given that './bar' also reference 'foo.ts'.
@_decorator.ccclass           //  ↓
export class Foo {            //  ↓
    @_decorator.type(Bar)     //  → is equivalent to `@_decorator.type(undefined)`
    public bar: Bar;          // To eliminate this error, either:
                              // - Refactor your module structure(recommended), or
                              // - specify the type as cc class name: `@_decorator.type('Bar'/* or any name you specified for `Bar` */)`
}
    at Logger._logHandler (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\startup\log.ccc:1:487)
    at Logger.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@base\electron-logger\lib\renderer.ccc:1:458)
    at console.<anonymous> (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@base\electron-logger\lib\renderer.ccc:1:1260)
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseAttributes (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127994:7)
    at defineGetSet (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127753:7)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127907:11)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/4b/4b380754ec2052c80f1e8d238cb60d7d60631247.js:42:51)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-9-27 14:11:56 - warn: [Scene] You are explicitly specifying `undefined` type to cc property "isDead" of cc class "PlaneBaseDebug".
Is this intended? If not, this may indicate a circular reference.
For example:

 
// foo.ts
import { _decorator } from 'cc';
import { Bar } from './bar';  // Given that './bar' also reference 'foo.ts'.
                              // When importing './bar', execution of './bar' is hung on to wait execution of 'foo.ts',
                              // the `Bar` imported here is `undefined` until './bar' finish its execution.
                              // It leads to that
@_decorator.ccclass           //  ↓
export class Foo {            //  ↓
    @_decorator.type(Bar)     //  → is equivalent to `@_decorator.type(undefined)`
    public bar: Bar;          // To eliminate this error, either:
                              // - Refactor your module structure(recommended), or
                              // - specify the type as cc class name: `@_decorator.type('Bar'/* or any name you specified for `Bar` */)`
}Error: [Scene] You are explicitly specifying `undefined` type to cc property "isDead" of cc class "PlaneBaseDebug".
Is this intended? If not, this may indicate a circular reference.
For example:

 
// foo.ts
import { _decorator } from 'cc';
import { Bar } from './bar';  // Given that './bar' also reference 'foo.ts'.
@_decorator.ccclass           //  ↓
export class Foo {            //  ↓
    @_decorator.type(Bar)     //  → is equivalent to `@_decorator.type(undefined)`
    public bar: Bar;          // To eliminate this error, either:
                              // - Refactor your module structure(recommended), or
                              // - specify the type as cc class name: `@_decorator.type('Bar'/* or any name you specified for `Bar` */)`
}
    at Logger._logHandler (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\startup\log.ccc:1:487)
    at Logger.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@base\electron-logger\lib\renderer.ccc:1:458)
    at console.<anonymous> (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@base\electron-logger\lib\renderer.ccc:1:1260)
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseAttributes (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127994:7)
    at defineGetSet (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127753:7)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127907:11)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/4b/4b380754ec2052c80f1e8d238cb60d7d60631247.js:42:51)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-9-27 14:11:56 - warn: [Scene] You are explicitly specifying `undefined` type to cc property "attack" of cc class "PlaneBaseDebug".
Is this intended? If not, this may indicate a circular reference.
For example:

 
// foo.ts
import { _decorator } from 'cc';
import { Bar } from './bar';  // Given that './bar' also reference 'foo.ts'.
                              // When importing './bar', execution of './bar' is hung on to wait execution of 'foo.ts',
                              // the `Bar` imported here is `undefined` until './bar' finish its execution.
                              // It leads to that
@_decorator.ccclass           //  ↓
export class Foo {            //  ↓
    @_decorator.type(Bar)     //  → is equivalent to `@_decorator.type(undefined)`
    public bar: Bar;          // To eliminate this error, either:
                              // - Refactor your module structure(recommended), or
                              // - specify the type as cc class name: `@_decorator.type('Bar'/* or any name you specified for `Bar` */)`
}Error: [Scene] You are explicitly specifying `undefined` type to cc property "attack" of cc class "PlaneBaseDebug".
Is this intended? If not, this may indicate a circular reference.
For example:

 
// foo.ts
import { _decorator } from 'cc';
import { Bar } from './bar';  // Given that './bar' also reference 'foo.ts'.
@_decorator.ccclass           //  ↓
export class Foo {            //  ↓
    @_decorator.type(Bar)     //  → is equivalent to `@_decorator.type(undefined)`
    public bar: Bar;          // To eliminate this error, either:
                              // - Refactor your module structure(recommended), or
                              // - specify the type as cc class name: `@_decorator.type('Bar'/* or any name you specified for `Bar` */)`
}
    at Logger._logHandler (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\startup\log.ccc:1:487)
    at Logger.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@base\electron-logger\lib\renderer.ccc:1:458)
    at console.<anonymous> (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@base\electron-logger\lib\renderer.ccc:1:1260)
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseAttributes (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127994:7)
    at defineGetSet (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127753:7)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127907:11)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/4b/4b380754ec2052c80f1e8d238cb60d7d60631247.js:42:51)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-9-27 14:11:56 - warn: [Scene] You are explicitly specifying `undefined` type to cc property "applyBuffID" of cc class "PlaneBaseDebug".
Is this intended? If not, this may indicate a circular reference.
For example:

 
// foo.ts
import { _decorator } from 'cc';
import { Bar } from './bar';  // Given that './bar' also reference 'foo.ts'.
                              // When importing './bar', execution of './bar' is hung on to wait execution of 'foo.ts',
                              // the `Bar` imported here is `undefined` until './bar' finish its execution.
                              // It leads to that
@_decorator.ccclass           //  ↓
export class Foo {            //  ↓
    @_decorator.type(Bar)     //  → is equivalent to `@_decorator.type(undefined)`
    public bar: Bar;          // To eliminate this error, either:
                              // - Refactor your module structure(recommended), or
                              // - specify the type as cc class name: `@_decorator.type('Bar'/* or any name you specified for `Bar` */)`
}Error: [Scene] You are explicitly specifying `undefined` type to cc property "applyBuffID" of cc class "PlaneBaseDebug".
Is this intended? If not, this may indicate a circular reference.
For example:

 
// foo.ts
import { _decorator } from 'cc';
import { Bar } from './bar';  // Given that './bar' also reference 'foo.ts'.
@_decorator.ccclass           //  ↓
export class Foo {            //  ↓
    @_decorator.type(Bar)     //  → is equivalent to `@_decorator.type(undefined)`
    public bar: Bar;          // To eliminate this error, either:
                              // - Refactor your module structure(recommended), or
                              // - specify the type as cc class name: `@_decorator.type('Bar'/* or any name you specified for `Bar` */)`
}
    at Logger._logHandler (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\startup\log.ccc:1:487)
    at Logger.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@base\electron-logger\lib\renderer.ccc:1:458)
    at console.<anonymous> (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@base\electron-logger\lib\renderer.ccc:1:1260)
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseAttributes (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127994:7)
    at defineProp (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127741:5)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127905:11)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/4b/4b380754ec2052c80f1e8d238cb60d7d60631247.js:42:51)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-9-27 14:11:56 - warn: [Scene] You are explicitly specifying `undefined` type to cc property "applyBuff" of cc class "PlaneBaseDebug".
Is this intended? If not, this may indicate a circular reference.
For example:

 
// foo.ts
import { _decorator } from 'cc';
import { Bar } from './bar';  // Given that './bar' also reference 'foo.ts'.
                              // When importing './bar', execution of './bar' is hung on to wait execution of 'foo.ts',
                              // the `Bar` imported here is `undefined` until './bar' finish its execution.
                              // It leads to that
@_decorator.ccclass           //  ↓
export class Foo {            //  ↓
    @_decorator.type(Bar)     //  → is equivalent to `@_decorator.type(undefined)`
    public bar: Bar;          // To eliminate this error, either:
                              // - Refactor your module structure(recommended), or
                              // - specify the type as cc class name: `@_decorator.type('Bar'/* or any name you specified for `Bar` */)`
}Error: [Scene] You are explicitly specifying `undefined` type to cc property "applyBuff" of cc class "PlaneBaseDebug".
Is this intended? If not, this may indicate a circular reference.
For example:

 
// foo.ts
import { _decorator } from 'cc';
import { Bar } from './bar';  // Given that './bar' also reference 'foo.ts'.
@_decorator.ccclass           //  ↓
export class Foo {            //  ↓
    @_decorator.type(Bar)     //  → is equivalent to `@_decorator.type(undefined)`
    public bar: Bar;          // To eliminate this error, either:
                              // - Refactor your module structure(recommended), or
                              // - specify the type as cc class name: `@_decorator.type('Bar'/* or any name you specified for `Bar` */)`
}
    at Logger._logHandler (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\startup\log.ccc:1:487)
    at Logger.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@base\electron-logger\lib\renderer.ccc:1:458)
    at console.<anonymous> (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@base\electron-logger\lib\renderer.ccc:1:1260)
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseAttributes (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127994:7)
    at defineGetSet (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127753:7)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127907:11)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/4b/4b380754ec2052c80f1e8d238cb60d7d60631247.js:42:51)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-9-27 14:11:56 - warn: [Scene] You are explicitly specifying `undefined` type to cc property "maxNuclear" of cc class "MainPlaneDebug".
Is this intended? If not, this may indicate a circular reference.
For example:

 
// foo.ts
import { _decorator } from 'cc';
import { Bar } from './bar';  // Given that './bar' also reference 'foo.ts'.
                              // When importing './bar', execution of './bar' is hung on to wait execution of 'foo.ts',
                              // the `Bar` imported here is `undefined` until './bar' finish its execution.
                              // It leads to that
@_decorator.ccclass           //  ↓
export class Foo {            //  ↓
    @_decorator.type(Bar)     //  → is equivalent to `@_decorator.type(undefined)`
    public bar: Bar;          // To eliminate this error, either:
                              // - Refactor your module structure(recommended), or
                              // - specify the type as cc class name: `@_decorator.type('Bar'/* or any name you specified for `Bar` */)`
}Error: [Scene] You are explicitly specifying `undefined` type to cc property "maxNuclear" of cc class "MainPlaneDebug".
Is this intended? If not, this may indicate a circular reference.
For example:

 
// foo.ts
import { _decorator } from 'cc';
import { Bar } from './bar';  // Given that './bar' also reference 'foo.ts'.
@_decorator.ccclass           //  ↓
export class Foo {            //  ↓
    @_decorator.type(Bar)     //  → is equivalent to `@_decorator.type(undefined)`
    public bar: Bar;          // To eliminate this error, either:
                              // - Refactor your module structure(recommended), or
                              // - specify the type as cc class name: `@_decorator.type('Bar'/* or any name you specified for `Bar` */)`
}
    at Logger._logHandler (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\startup\log.ccc:1:487)
    at Logger.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@base\electron-logger\lib\renderer.ccc:1:458)
    at console.<anonymous> (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@base\electron-logger\lib\renderer.ccc:1:1260)
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseAttributes (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127994:7)
    at defineGetSet (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127753:7)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127907:11)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/5d/5d454448b8b07f76d492b69a7447a1a3918910db.js:47:118)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-9-27 14:11:56 - warn: [Scene] You are explicitly specifying `undefined` type to cc property "curNuclear" of cc class "MainPlaneDebug".
Is this intended? If not, this may indicate a circular reference.
For example:

 
// foo.ts
import { _decorator } from 'cc';
import { Bar } from './bar';  // Given that './bar' also reference 'foo.ts'.
                              // When importing './bar', execution of './bar' is hung on to wait execution of 'foo.ts',
                              // the `Bar` imported here is `undefined` until './bar' finish its execution.
                              // It leads to that
@_decorator.ccclass           //  ↓
export class Foo {            //  ↓
    @_decorator.type(Bar)     //  → is equivalent to `@_decorator.type(undefined)`
    public bar: Bar;          // To eliminate this error, either:
                              // - Refactor your module structure(recommended), or
                              // - specify the type as cc class name: `@_decorator.type('Bar'/* or any name you specified for `Bar` */)`
}Error: [Scene] You are explicitly specifying `undefined` type to cc property "curNuclear" of cc class "MainPlaneDebug".
Is this intended? If not, this may indicate a circular reference.
For example:

 
// foo.ts
import { _decorator } from 'cc';
import { Bar } from './bar';  // Given that './bar' also reference 'foo.ts'.
@_decorator.ccclass           //  ↓
export class Foo {            //  ↓
    @_decorator.type(Bar)     //  → is equivalent to `@_decorator.type(undefined)`
    public bar: Bar;          // To eliminate this error, either:
                              // - Refactor your module structure(recommended), or
                              // - specify the type as cc class name: `@_decorator.type('Bar'/* or any name you specified for `Bar` */)`
}
    at Logger._logHandler (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\startup\log.ccc:1:487)
    at Logger.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@base\electron-logger\lib\renderer.ccc:1:458)
    at console.<anonymous> (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@base\electron-logger\lib\renderer.ccc:1:1260)
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseAttributes (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127994:7)
    at defineGetSet (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127753:7)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127907:11)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/5d/5d454448b8b07f76d492b69a7447a1a3918910db.js:47:118)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-9-27 14:11:56 - log: [Scene] start init_cs_proto.js
2025-9-27 14:11:56 - log: [Scene] Using custom pipeline: Builtin
2025-9-27 14:11:56 - log: [Scene] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-9-27 14:11:57 - log: [Scene] subdivided points length:  53
2025-9-27 14:11:57 - log: [Scene] subdivided points length:  53
2025-9-27 14:11:57 - log: [Scene] subdivided points length:  53
2025-9-27 14:11:57 - log: [Scene] subdivided points length:  53
2025-9-27 14:11:57 - log: [Scene] subdivided points length:  53
2025-9-27 14:11:57 - log: [Scene] subdivided points length:  53
2025-9-27 14:11:57 - log: [Scene] subdivided points length:  53
2025-9-27 14:11:57 - log: [Scene] subdivided points length:  53
2025-9-27 14:11:57 - log: [Scene] subdivided points length:  53
2025-9-27 14:11:57 - log: [Scene] subdivided points length:  53
2025-9-27 14:11:57 - log: [Scene] subdivided points length:  53
2025-9-27 14:11:57 - log: [Scene] subdivided points length:  53
2025-9-27 14:11:57 - log: [Scene] subdivided points length:  53
2025-9-27 14:11:58 - log: [Scene] subdivided points length:  53
2025-9-27 14:11:58 - log: [Scene] subdivided points length:  53
2025-9-27 14:11:58 - log: [Scene] subdivided points length:  53
2025-9-27 14:11:59 - log: [Scene] subdivided points length:  53
2025-9-27 14:12:00 - log: [Scene] subdivided points length:  53
2025-9-27 14:12:00 - log: [Scene] subdivided points length:  53
2025-9-27 14:12:00 - log: [Scene] subdivided points length:  53
2025-9-27 14:12:00 - log: [Scene] subdivided points length:  53
2025-9-27 14:12:00 - log: [Scene] subdivided points length:  53
2025-9-27 14:12:00 - log: [Scene] subdivided points length:  53
2025-9-27 14:12:00 - log: [Scene] subdivided points length:  53
2025-9-27 14:12:00 - log: [Scene] subdivided points length:  53
2025-9-27 14:12:00 - log: [Scene] subdivided points length:  53
2025-9-27 14:12:00 - log: [Scene] subdivided points length:  53
2025-9-27 14:12:00 - log: [Scene] subdivided points length:  53
2025-9-27 14:12:00 - log: [Scene] subdivided points length:  53
2025-9-27 14:12:00 - log: [Scene] subdivided points length:  53
2025-9-27 14:12:00 - log: [Scene] subdivided points length:  53
2025-9-27 14:12:00 - log: [Scene] subdivided points length:  53
2025-9-27 14:12:00 - log: [Scene] subdivided points length:  53
2025-9-27 14:12:00 - log: [Scene] subdivided points length:  53
2025-9-27 14:12:00 - log: [Scene] subdivided points length:  53
2025-9-27 14:12:00 - log: [Scene] subdivided points length:  53
2025-9-27 14:12:00 - log: [Scene] subdivided points length:  53
2025-9-27 14:12:00 - log: [Scene] subdivided points length:  53
2025-9-27 14:12:00 - log: [Scene] subdivided points length:  53
2025-9-27 14:12:00 - log: [Scene] subdivided points length:  53
2025-9-27 14:12:00 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:48 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:48 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:48 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:48 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:48 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:48 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:48 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:49 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:49 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:50 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:50 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:50 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:50 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:50 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:50 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:50 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:52 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:52 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:52 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:52 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:52 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:52 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:52 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:52 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:52 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:52 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:52 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:52 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:52 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:52 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:52 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:52 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:53 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:53 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:53 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:53 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:53 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:53 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:53 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:53 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:53 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:53 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:53 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:53 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:53 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:53 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:53 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:53 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:53 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:53 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:53 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:53 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:53 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:53 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:53 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:53 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:53 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:53 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:53 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:54 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:54 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:54 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:54 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:54 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:55 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:55 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:55 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:55 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:55 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:55 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:55 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:55 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:55 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:55 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:55 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:55 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:55 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:55 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:55 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:55 - log: [Scene] subdivided points length:  53
2025-9-27 14:32:55 - log: [Scene] subdivided points length:  55
2025-9-27 14:32:55 - log: [Scene] subdivided points length:  57
2025-9-27 14:32:55 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:55 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:55 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:56 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:57 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:57 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:58 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:58 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:58 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:58 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:58 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:58 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:58 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:58 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:58 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:58 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:58 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:58 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:58 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:58 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:58 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:58 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:58 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:58 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:58 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:58 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:32:59 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:00 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:00 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:00 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:00 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:00 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:00 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:00 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:00 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:00 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:00 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:01 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:02 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:03 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:04 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:05 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:05 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:05 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:05 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:05 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:05 - log: [Scene] subdivided points length:  61
2025-9-27 14:33:05 - log: [Scene] subdivided points length:  61
