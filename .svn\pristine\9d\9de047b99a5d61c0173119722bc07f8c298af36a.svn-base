import { _decorator, find, Node, path, Rect } from "cc";
import { EventGroupData } from "../data/bullet/EventGroupData";
import { Bullet } from "./Bullet";
import { Emitter } from "./Emitter";
import { EventGroup, EventGroupContext } from "./EventGroup";
import PlaneBase from "../ui/plane/PlaneBase";
import { ObjectPool } from "./ObjectPool";

/**
 * BulletSystem - manages all bullets in the game world
 * Handles bullet creation, movement, collision, and cleanup
 */
export class BulletSystem {
    public static readonly bulletParentPath: string = 'Canvas/bullet_root';
    public static readonly emitterEventGroupPath: string = 'Game/emitter/events/Emitter';
    public static readonly bulletEventGroupPath: string = 'Game/emitter/events/Bullet';

    /**
     * All active bullets
     */
    public static allBullets: Bullet[] = [];

    /**
     * All active emitters
     */
    public static allEmitters: Emitter[] = [];

    /**
     * All active action groups
     */
    public static allEventGroups: EventGroup[] = [];

    // public static isEmitterEnabled: boolean = true;
    // public static isBulletEnabled: boolean = true;
    public static bulletParent: Node | null = null;

    public static playerPlane: PlaneBase | null = null;

    /**
     * Bounds of the game world for bullet cleanup
     * 这个值需要在合适的地方适当调整
     */
    public static worldBounds = new Rect(0, 0, 750, 1334);

    public static init(bounds: Rect) {
        this.worldBounds = bounds;

        if (!this.bulletParent || !this.bulletParent.isValid) {
            if (this.bulletParentPath.length > 0) {
                const foundNode = find(this.bulletParentPath);
                if (foundNode) {
                    this.bulletParent = foundNode;
                }
                else {
                    console.warn('没有找到子弹父节点请检查路径:' + this.bulletParentPath);
                }
            }
        }

        ObjectPool.clearAll();
    }

    /**
     * Main update loop
     */
    public static tick(dt: number) {
        const dtInMiliseconds = dt * 1000;

        // const start_emitters = performance.now();
        this.tickEmitters(dtInMiliseconds);
        // const end_emitters = performance.now();
        // console.log('tickEmitters cost: ', `${end_emitters - start_emitters}ms`)

        this.tickBullets(dtInMiliseconds);
        // const end_bullets = performance.now();
        // console.log('tickBullets cost: ', `${end_bullets - end_emitters}ms`)

        this.tickEventGroups(dtInMiliseconds);
        // console.log('tickEventGroups cost: ', `${performance.now() - end_bullets}ms`)
    }

    public static tickEmitters(dt: number) {
        for (let i = this.allEmitters.length - 1; i >= 0; i--) {
            const emitter = this.allEmitters[i];
            emitter.tick(dt);
        }
    }

    public static tickBullets(dt: number) {
        for (let i = this.allBullets.length - 1; i >= 0; i--) {
            const bullet = this.allBullets[i];
            bullet.tick(dt);
        }
    }

    public static tickEventGroups(dt: number) {
        for (let i = this.allEventGroups.length - 1; i >= 0; i--) {
            const group = this.allEventGroups[i];
            group.tick(dt);
            // group will remove itself when stopped
            // if (group.status === eEventGroupStatus.Stopped) {
            //     this.allEventGroups.splice(i, 1);
            // }
        }
    }

    public static onCreateEmitter(emitter: Emitter) {
        for (let i = 0; i < this.allEmitters.length; i++) {
            if (this.allEmitters[i] === emitter) {
                return;
            }
        }

        this.allEmitters.push(emitter);
    }

    public static onDestroyEmitter(emitter: Emitter) {
        const index: number = this.allEmitters.indexOf(emitter, 0);
        if (index > -1) {
            this.allEmitters.splice(index, 1);
        }
    }

    public static onCreateBullet(emitter: Emitter, bullet: Bullet) {
        // 这个检查是否会比较冗余
        // for (let i = 0; i < this.allBullets.length; i++) {
        //     if (this.allBullets[i] === bullet) {
        //         return;
        //     }
        // }

        bullet.onCreate(emitter, emitter.config);
        this.allBullets.push(bullet);
    }

    public static onDestroyBullet(bullet: Bullet) {
        bullet.willDestroy();
        const index: number = this.allBullets.indexOf(bullet, 0);
        if (index > -1) {
            this.allBullets.splice(index, 1);
        }
    }

    public static destroyAllBullets() {
        for (const bullet of this.allBullets) {
            bullet.willDestroy();
        }
        this.allBullets = [];
    }

    public static createEmitterEventGroup(ctx: EventGroupContext, data: EventGroupData): void {
        ctx.emitter?.eventGroups.push(new EventGroup(ctx, data));
    }

    public static createBulletEventGroup(ctx: EventGroupContext, data: EventGroupData): void {
        // the name is the json file name
        // let finalPath = join(this.bulletEventGroupPath, name);
        // resources.load(finalPath, JsonAsset, (err, data) => {
        //     if (err) {
        //         console.error("Failed to load bullet event group:", err);
        //         return null;
        //     }
        //     const eventData = EventGroupData.fromJSON(data.json);
        //     const eventGroup = new EventGroup(ctx, eventData);
        //     eventGroup.start();
        //     ctx.bullet?.eventGroups.push(eventGroup);
        // });
        // console.log('createBulletEventGroup: ', data.name);
        const eventGroup = new EventGroup(ctx, data);
        eventGroup.tryStart();
        ctx.bullet?.eventGroups.push(eventGroup);
    }

    /**
     * Called when a new event group is created or turn active
     */
    public static onCreateEventGroup(eventGroup: EventGroup) {
        this.allEventGroups.push(eventGroup);
    }

    public static onDestroyEventGroup(eventGroup: EventGroup) {
        const index: number = this.allEventGroups.indexOf(eventGroup, 0);
        if (index > -1) {
            this.allEventGroups.splice(index, 1);
        }
    }

    public static destroy(isEditor: boolean = false,isRelease: boolean = true) {
        this.destroyAllBullets();
        if (isEditor && this.bulletParent) {
            this.bulletParent.removeAllChildren();
        }
        // 先直接这样清理掉
        this.allEmitters = [];
        this.allEventGroups = [];
        if (isRelease) {//游戏内小关卡结束，不清除全部
            this.playerPlane = null;
            this.bulletParent = null;
            ObjectPool.clearAll();
        }
    }
}