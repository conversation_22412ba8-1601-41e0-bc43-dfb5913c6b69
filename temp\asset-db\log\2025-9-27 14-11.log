2025-9-27 14:11:39-debug: start **** info
2025-9-27 14:11:39-log: Cannot access game frame or container.
2025-9-27 14:11:39-debug: asset-db:require-engine-code (417ms)
2025-9-27 14:11:39-log: meshopt wasm decoder initialized
2025-9-27 14:11:39-log: [bullet]:bullet wasm lib loaded.
2025-9-27 14:11:39-log: [box2d]:box2d wasm lib loaded.
2025-9-27 14:11:39-log: Cocos Creator v3.8.6
2025-9-27 14:11:39-log: Forward render pipeline initialized.
2025-9-27 14:11:39-debug: [Assets Memory track]: asset-db-plugin-register: programming start:81.21MB, end 84.08MB, increase: 2.86MB
2025-9-27 14:11:39-log: Using legacy pipeline
2025-9-27 14:11:40-debug: [Assets Memory track]: asset-db-plugin-register: builder start:84.12MB, end 224.82MB, increase: 140.70MB
2025-9-27 14:11:40-debug: [Assets Memory track]: asset-db-plugin-register: project start:81.07MB, end 228.46MB, increase: 147.39MB
2025-9-27 14:11:39-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:30.88MB, end 80.05MB, increase: 49.17MB
2025-9-27 14:11:40-debug: [Assets Memory track]: asset-db-plugin-register: engine-extends start:225.07MB, end 228.24MB, increase: 3.18MB
2025-9-27 14:11:40-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:80.08MB, end 228.48MB, increase: 148.41MB
2025-9-27 14:11:40-debug: run package(harmonyos-next) handler(enable) start
2025-9-27 14:11:40-debug: run package(harmonyos-next) handler(enable) success!
2025-9-27 14:11:40-debug: run package(honor-mini-game) handler(enable) success!
2025-9-27 14:11:40-debug: run package(honor-mini-game) handler(enable) start
2025-9-27 14:11:40-debug: run package(huawei-quick-game) handler(enable) start
2025-9-27 14:11:40-debug: run package(huawei-agc) handler(enable) start
2025-9-27 14:11:40-debug: run package(huawei-agc) handler(enable) success!
2025-9-27 14:11:40-debug: run package(huawei-quick-game) handler(enable) success!
2025-9-27 14:11:40-debug: run package(linux) handler(enable) start
2025-9-27 14:11:40-debug: run package(ios) handler(enable) start
2025-9-27 14:11:40-debug: run package(ios) handler(enable) success!
2025-9-27 14:11:40-debug: run package(linux) handler(enable) success!
2025-9-27 14:11:40-debug: run package(mac) handler(enable) success!
2025-9-27 14:11:40-debug: run package(migu-mini-game) handler(enable) start
2025-9-27 14:11:40-debug: run package(mac) handler(enable) start
2025-9-27 14:11:40-debug: run package(native) handler(enable) start
2025-9-27 14:11:40-debug: run package(migu-mini-game) handler(enable) success!
2025-9-27 14:11:40-debug: run package(ohos) handler(enable) success!
2025-9-27 14:11:40-debug: run package(native) handler(enable) success!
2025-9-27 14:11:40-debug: run package(oppo-mini-game) handler(enable) start
2025-9-27 14:11:40-debug: run package(ohos) handler(enable) start
2025-9-27 14:11:40-debug: run package(oppo-mini-game) handler(enable) success!
2025-9-27 14:11:40-debug: run package(runtime-dev-tools) handler(enable) success!
2025-9-27 14:11:40-debug: run package(taobao-mini-game) handler(enable) success!
2025-9-27 14:11:40-debug: run package(runtime-dev-tools) handler(enable) start
2025-9-27 14:11:40-debug: run package(vivo-mini-game) handler(enable) start
2025-9-27 14:11:40-debug: run package(taobao-mini-game) handler(enable) start
2025-9-27 14:11:40-debug: run package(vivo-mini-game) handler(enable) success!
2025-9-27 14:11:40-debug: run package(web-desktop) handler(enable) start
2025-9-27 14:11:40-debug: run package(web-desktop) handler(enable) success!
2025-9-27 14:11:40-debug: run package(web-mobile) handler(enable) success!
2025-9-27 14:11:40-debug: run package(wechatgame) handler(enable) start
2025-9-27 14:11:40-debug: run package(wechatgame) handler(enable) success!
2025-9-27 14:11:40-debug: run package(web-mobile) handler(enable) start
2025-9-27 14:11:40-debug: run package(wechatprogram) handler(enable) start
2025-9-27 14:11:40-debug: run package(windows) handler(enable) start
2025-9-27 14:11:40-debug: run package(wechatprogram) handler(enable) success!
2025-9-27 14:11:40-debug: run package(xiaomi-quick-game) handler(enable) start
2025-9-27 14:11:40-debug: run package(xiaomi-quick-game) handler(enable) success!
2025-9-27 14:11:40-debug: run package(windows) handler(enable) success!
2025-9-27 14:11:40-debug: run package(cocos-service) handler(enable) start
2025-9-27 14:11:40-debug: run package(cocos-service) handler(enable) success!
2025-9-27 14:11:40-debug: run package(im-plugin) handler(enable) start
2025-9-27 14:11:40-debug: run package(im-plugin) handler(enable) success!
2025-9-27 14:11:40-debug: run package(fix-asset-default-userdata) handler(enable) success!
2025-9-27 14:11:40-debug: run package(fix-asset-default-userdata) handler(enable) start
2025-9-27 14:11:40-debug: run package(emitter-editor) handler(enable) start
2025-9-27 14:11:40-debug: run package(emitter-editor) handler(enable) success!
2025-9-27 14:11:40-debug: asset-db:worker-init: initPlugin (1033ms)
2025-9-27 14:11:40-debug: start refresh asset from db://assets/editor/enum-gen/EmitterEnum.ts...
2025-9-27 14:11:40-debug: refresh asset db://assets/editor/enum-gen success
2025-9-27 14:11:40-debug: start refresh asset from db://assets/editor/enum-gen/EnemyEnum.ts...
2025-9-27 14:11:40-debug: refresh asset db://assets/editor/enum-gen success
2025-9-27 14:11:40-debug: [Assets Memory track]: asset-db:worker-init start:30.88MB, end 225.42MB, increase: 194.55MB
2025-9-27 14:11:40-debug: Run asset db hook programming:beforePreStart ...
2025-9-27 14:11:40-debug: Run asset db hook engine-extends:beforePreStart ...
2025-9-27 14:11:40-debug: Run asset db hook engine-extends:beforePreStart success!
2025-9-27 14:11:40-debug: Run asset db hook programming:beforePreStart success!
2025-9-27 14:11:40-debug: run package(i18n) handler(enable) start
2025-9-27 14:11:40-debug: run package(i18n) handler(enable) success!
2025-9-27 14:11:40-debug: start asset-db(i18n)...
2025-9-27 14:11:40-debug: run package(level-editor) handler(enable) start
2025-9-27 14:11:40-debug: start custom db i18n...
2025-9-27 14:11:40-debug: run package(level-editor) handler(enable) success!
2025-9-27 14:11:40-debug: [Assets Memory track]: asset-db:worker-startup-database[i18n] start:226.38MB, end 231.62MB, increase: 5.24MB
2025-9-27 14:11:40-debug: Preimport db internal success
2025-9-27 14:11:41-debug: run package(localization-editor) handler(enable) start
2025-9-27 14:11:41-debug: run package(localization-editor) handler(enable) success!
2025-9-27 14:11:41-debug: asset-db:worker-init (1714ms)
2025-9-27 14:11:41-debug: asset-db-hook-programming-beforePreStart (189ms)
2025-9-27 14:11:41-debug: asset-db-hook-engine-extends-beforePreStart (189ms)
2025-9-27 14:11:41-debug: asset-db:worker-startup-database[i18n] (68ms)
2025-9-27 14:11:41-debug: run package(placeholder) handler(enable) start
2025-9-27 14:11:41-debug: run package(placeholder) handler(enable) success!
2025-9-27 14:11:41-debug: %cImport%c: E:\M2Game\Client\assets\editor\enum-gen\EmitterEnum.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 14:11:41-debug: %cImport%c: E:\M2Game\Client\assets\editor\enum-gen\EnemyEnum.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 14:11:41-debug: Preimport db assets success
2025-9-27 14:11:41-debug: Run asset db hook programming:afterPreStart ...
2025-9-27 14:11:41-debug: starting packer-driver...
2025-9-27 14:11:50-debug: initialize scripting environment...
2025-9-27 14:11:50-debug: [[Executor]] prepare before lock
2025-9-27 14:11:50-debug: Set detail map pack:///resolution-detail-map.json: {}
2025-9-27 14:11:50-debug: [[Executor]] prepare after unlock
2025-9-27 14:11:50-debug: Run asset db hook engine-extends:afterPreStart success!
2025-9-27 14:11:50-debug: Run asset db hook programming:afterPreStart success!
2025-9-27 14:11:50-debug: Run asset db hook engine-extends:afterPreStart ...
2025-9-27 14:11:50-debug: [Assets Memory track]: asset-db:worker-init: preStart start:225.44MB, end 233.79MB, increase: 8.35MB
2025-9-27 14:11:50-debug: Start up the 'internal' database...
2025-9-27 14:11:51-debug: asset-db:worker-effect-data-processing (402ms)
2025-9-27 14:11:51-debug: asset-db-hook-programming-afterPreStart (10258ms)
2025-9-27 14:11:51-debug: asset-db-hook-engine-extends-afterPreStart (402ms)
2025-9-27 14:11:51-debug: Start up the 'assets' database...
2025-9-27 14:11:51-debug: asset-db:worker-startup-database[internal] (11018ms)
2025-9-27 14:11:51-debug: lazy register asset handler *
2025-9-27 14:11:51-debug: lazy register asset handler directory
2025-9-27 14:11:51-debug: lazy register asset handler json
2025-9-27 14:11:51-debug: lazy register asset handler spine-data
2025-9-27 14:11:51-debug: lazy register asset handler text
2025-9-27 14:11:51-debug: lazy register asset handler dragonbones
2025-9-27 14:11:51-debug: lazy register asset handler dragonbones-atlas
2025-9-27 14:11:51-debug: lazy register asset handler terrain
2025-9-27 14:11:51-debug: lazy register asset handler javascript
2025-9-27 14:11:51-debug: lazy register asset handler prefab
2025-9-27 14:11:51-debug: lazy register asset handler scene
2025-9-27 14:11:51-debug: lazy register asset handler sprite-frame
2025-9-27 14:11:51-debug: lazy register asset handler image
2025-9-27 14:11:51-debug: lazy register asset handler tiled-map
2025-9-27 14:11:51-debug: lazy register asset handler buffer
2025-9-27 14:11:51-debug: lazy register asset handler sign-image
2025-9-27 14:11:51-debug: lazy register asset handler alpha-image
2025-9-27 14:11:51-debug: lazy register asset handler texture-cube
2025-9-27 14:11:51-debug: lazy register asset handler texture
2025-9-27 14:11:51-debug: lazy register asset handler render-texture
2025-9-27 14:11:51-debug: lazy register asset handler erp-texture-cube
2025-9-27 14:11:51-debug: lazy register asset handler typescript
2025-9-27 14:11:51-debug: lazy register asset handler rt-sprite-frame
2025-9-27 14:11:51-debug: lazy register asset handler texture-cube-face
2025-9-27 14:11:51-debug: lazy register asset handler gltf-mesh
2025-9-27 14:11:51-debug: lazy register asset handler gltf
2025-9-27 14:11:51-debug: lazy register asset handler gltf-material
2025-9-27 14:11:51-debug: lazy register asset handler gltf-skeleton
2025-9-27 14:11:51-debug: lazy register asset handler gltf-animation
2025-9-27 14:11:51-debug: lazy register asset handler gltf-embeded-image
2025-9-27 14:11:51-debug: lazy register asset handler fbx
2025-9-27 14:11:51-debug: lazy register asset handler gltf-scene
2025-9-27 14:11:51-debug: lazy register asset handler physics-material
2025-9-27 14:11:51-debug: lazy register asset handler material
2025-9-27 14:11:51-debug: lazy register asset handler effect-header
2025-9-27 14:11:51-debug: lazy register asset handler effect
2025-9-27 14:11:51-debug: lazy register asset handler animation-clip
2025-9-27 14:11:51-debug: lazy register asset handler audio-clip
2025-9-27 14:11:51-debug: lazy register asset handler animation-graph-variant
2025-9-27 14:11:51-debug: lazy register asset handler animation-graph
2025-9-27 14:11:51-debug: lazy register asset handler animation-mask
2025-9-27 14:11:51-debug: lazy register asset handler ttf-font
2025-9-27 14:11:51-debug: lazy register asset handler particle
2025-9-27 14:11:51-debug: lazy register asset handler bitmap-font
2025-9-27 14:11:51-debug: lazy register asset handler sprite-atlas
2025-9-27 14:11:51-debug: lazy register asset handler label-atlas
2025-9-27 14:11:51-debug: lazy register asset handler auto-atlas
2025-9-27 14:11:51-debug: lazy register asset handler render-flow
2025-9-27 14:11:51-debug: lazy register asset handler render-stage
2025-9-27 14:11:51-debug: lazy register asset handler instantiation-material
2025-9-27 14:11:51-debug: lazy register asset handler instantiation-mesh
2025-9-27 14:11:51-debug: lazy register asset handler instantiation-animation
2025-9-27 14:11:51-debug: lazy register asset handler render-pipeline
2025-9-27 14:11:51-debug: lazy register asset handler instantiation-skeleton
2025-9-27 14:11:51-debug: lazy register asset handler video-clip
2025-9-27 14:11:51-debug: asset-db:worker-startup-database[assets] (11085ms)
2025-9-27 14:11:51-debug: asset-db:ready (14608ms)
2025-9-27 14:11:51-debug: asset-db:start-database (11265ms)
2025-9-27 14:11:51-debug: fix the bug of updateDefaultUserData
2025-9-27 14:11:51-debug: init worker message success
2025-9-27 14:11:51-debug: programming:execute-script (8ms)
2025-9-27 14:11:52-debug: [Build Memory track]: builder:worker-init start:193.53MB, end 206.17MB, increase: 12.65MB
2025-9-27 14:11:52-debug: builder:worker-init (431ms)
2025-9-27 14:11:54-debug: refresh db internal success
2025-9-27 14:11:54-debug: refresh db i18n success
2025-9-27 14:11:54-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 14:11:54-debug: refresh db assets success
2025-9-27 14:11:54-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 14:11:54-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 14:11:54-debug: asset-db:refresh-all-database (151ms)
2025-9-27 14:11:54-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 14:32:44-debug: refresh db internal success
2025-9-27 14:32:44-debug: refresh db i18n success
2025-9-27 14:32:44-debug: refresh db assets success
2025-9-27 14:32:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 14:32:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 14:32:44-debug: asset-db:refresh-all-database (190ms)
2025-9-27 14:32:44-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-27 14:32:44-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-27 14:51:54-debug: refresh db internal success
2025-9-27 14:51:54-debug: refresh db i18n success
2025-9-27 14:51:54-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 14:51:54-debug: refresh db assets success
2025-9-27 14:51:54-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 14:51:54-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 14:51:54-debug: asset-db:refresh-all-database (170ms)
2025-9-27 14:51:54-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 14:51:54-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 15:02:44-debug: refresh db internal success
2025-9-27 15:02:44-debug: refresh db i18n success
2025-9-27 15:02:44-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:02:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:02:44-debug: refresh db assets success
2025-9-27 15:02:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:02:44-debug: asset-db:refresh-all-database (122ms)
2025-9-27 15:03:50-debug: refresh db internal success
2025-9-27 15:03:50-debug: refresh db i18n success
2025-9-27 15:03:50-debug: refresh db assets success
2025-9-27 15:03:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:03:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:03:50-debug: asset-db:refresh-all-database (136ms)
2025-9-27 15:03:50-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 15:03:50-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 15:03:53-debug: refresh db internal success
2025-9-27 15:03:53-debug: refresh db i18n success
2025-9-27 15:03:53-debug: refresh db assets success
2025-9-27 15:03:53-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:03:53-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:03:53-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 15:03:53-debug: asset-db:refresh-all-database (118ms)
2025-9-27 15:03:53-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-27 15:05:14-debug: refresh db internal success
2025-9-27 15:05:14-debug: refresh db i18n success
2025-9-27 15:05:14-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:05:14-debug: refresh db assets success
2025-9-27 15:05:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:05:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:05:14-debug: asset-db:refresh-all-database (126ms)
2025-9-27 15:05:14-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 15:05:14-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 15:06:24-debug: refresh db internal success
2025-9-27 15:06:24-debug: refresh db i18n success
2025-9-27 15:06:24-debug: refresh db assets success
2025-9-27 15:06:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:06:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:06:24-debug: asset-db:refresh-all-database (125ms)
2025-9-27 15:06:24-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 15:06:24-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 15:06:39-debug: refresh db internal success
2025-9-27 15:06:39-debug: refresh db i18n success
2025-9-27 15:06:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:06:39-debug: refresh db assets success
2025-9-27 15:06:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:06:39-debug: asset-db:refresh-all-database (121ms)
2025-9-27 15:06:49-debug: refresh db internal success
2025-9-27 15:06:49-debug: refresh db i18n success
2025-9-27 15:06:49-debug: refresh db assets success
2025-9-27 15:06:49-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:06:49-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:06:49-debug: asset-db:refresh-all-database (118ms)
2025-9-27 15:10:35-debug: refresh db internal success
2025-9-27 15:10:35-debug: refresh db i18n success
2025-9-27 15:10:35-debug: refresh db assets success
2025-9-27 15:10:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:10:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:10:35-debug: asset-db:refresh-all-database (137ms)
2025-9-27 15:10:41-debug: refresh db internal success
2025-9-27 15:10:41-debug: refresh db i18n success
2025-9-27 15:10:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:10:42-debug: refresh db assets success
2025-9-27 15:10:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:10:42-debug: asset-db:refresh-all-database (136ms)
2025-9-27 15:14:32-debug: refresh db internal success
2025-9-27 15:14:32-debug: refresh db i18n success
2025-9-27 15:14:32-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:14:32-debug: refresh db assets success
2025-9-27 15:14:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:14:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:14:32-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 15:14:32-debug: asset-db:refresh-all-database (143ms)
2025-9-27 15:14:32-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 15:16:19-debug: refresh db internal success
2025-9-27 15:16:19-debug: refresh db i18n success
2025-9-27 15:16:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:16:19-debug: refresh db assets success
2025-9-27 15:16:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:16:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:16:19-debug: asset-db:refresh-all-database (149ms)
2025-9-27 15:16:31-debug: refresh db internal success
2025-9-27 15:16:31-debug: refresh db i18n success
2025-9-27 15:16:32-debug: refresh db assets success
2025-9-27 15:16:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:16:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:16:32-debug: asset-db:refresh-all-database (149ms)
2025-9-27 15:16:40-debug: refresh db internal success
2025-9-27 15:16:40-debug: refresh db i18n success
2025-9-27 15:16:40-debug: refresh db assets success
2025-9-27 15:16:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:16:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:16:40-debug: asset-db:refresh-all-database (140ms)
2025-9-27 15:16:54-debug: refresh db internal success
2025-9-27 15:16:54-debug: refresh db i18n success
2025-9-27 15:16:54-debug: refresh db assets success
2025-9-27 15:16:54-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:16:54-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:16:54-debug: asset-db:refresh-all-database (111ms)
2025-9-27 15:23:14-debug: refresh db internal success
2025-9-27 15:23:14-debug: refresh db i18n success
2025-9-27 15:23:14-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:23:14-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:23:14-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\docs\PathSystemRefactor.md
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:23:14-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\test\PathSystemTest.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:23:14-debug: refresh db assets success
2025-9-27 15:23:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:23:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:23:14-debug: asset-db:refresh-all-database (168ms)
2025-9-27 15:23:14-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 15:23:14-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-27 15:23:58-debug: refresh db internal success
2025-9-27 15:23:58-debug: refresh db i18n success
2025-9-27 15:23:58-debug: refresh db assets success
2025-9-27 15:23:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:23:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:23:58-debug: asset-db:refresh-all-database (158ms)
2025-9-27 15:23:58-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-27 15:23:58-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-27 15:28:15-debug: refresh db internal success
2025-9-27 15:28:15-debug: refresh db i18n success
2025-9-27 15:28:15-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\test\PathCurveTest.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:28:15-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\test
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:28:15-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:28:15-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\test\PathSystemTest.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:28:15-debug: refresh db assets success
2025-9-27 15:28:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:28:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:28:15-debug: asset-db:refresh-all-database (177ms)
2025-9-27 15:28:15-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 15:28:15-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 15:28:23-debug: refresh db internal success
2025-9-27 15:28:23-debug: refresh db i18n success
2025-9-27 15:28:23-debug: refresh db assets success
2025-9-27 15:28:23-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:28:23-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:28:23-debug: asset-db:refresh-all-database (125ms)
2025-9-27 15:28:23-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 15:28:23-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 15:29:07-debug: refresh db internal success
2025-9-27 15:29:07-debug: refresh db i18n success
2025-9-27 15:29:07-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:29:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:29:07-debug: refresh db assets success
2025-9-27 15:29:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:29:07-debug: asset-db:refresh-all-database (152ms)
2025-9-27 15:35:42-debug: refresh db internal success
2025-9-27 15:35:42-debug: refresh db i18n success
2025-9-27 15:35:42-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:35:42-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\docs\PathSystemRefactor.md
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:35:42-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\test\PathSystemTest.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:35:42-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\test\PathCurveTest.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:35:42-debug: refresh db assets success
2025-9-27 15:35:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:35:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:35:42-debug: asset-db:refresh-all-database (162ms)
2025-9-27 15:35:42-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 15:35:42-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 15:38:44-debug: refresh db internal success
2025-9-27 15:38:44-debug: refresh db i18n success
2025-9-27 15:38:44-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:38:44-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\test\PathSystemTest.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:38:44-debug: refresh db assets success
2025-9-27 15:38:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:38:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:38:44-debug: asset-db:refresh-all-database (152ms)
2025-9-27 15:38:44-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 15:39:05-debug: refresh db internal success
2025-9-27 15:39:05-debug: refresh db i18n success
2025-9-27 15:39:05-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:39:05-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\test\PathSystemTest.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:39:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:39:05-debug: refresh db assets success
2025-9-27 15:39:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:39:05-debug: asset-db:refresh-all-database (121ms)
2025-9-27 15:39:05-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 15:39:05-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 15:43:39-debug: refresh db internal success
2025-9-27 15:43:39-debug: refresh db i18n success
2025-9-27 15:43:39-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:43:39-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\docs\PathSystemRefactor.md
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:43:39-debug: refresh db assets success
2025-9-27 15:43:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:43:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:43:39-debug: asset-db:refresh-all-database (155ms)
2025-9-27 15:48:53-debug: refresh db internal success
2025-9-27 15:48:53-debug: refresh db i18n success
2025-9-27 15:48:53-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\test\PathMoveOptimizationTest.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:48:53-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:48:53-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\test
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:48:53-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:48:53-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\docs\PathSystemRefactor.md
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:48:53-debug: refresh db assets success
2025-9-27 15:48:53-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:48:53-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:48:53-debug: asset-db:refresh-all-database (176ms)
2025-9-27 15:48:53-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 15:49:26-debug: refresh db internal success
2025-9-27 15:49:26-debug: refresh db i18n success
2025-9-27 15:49:26-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:49:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:49:26-debug: refresh db assets success
2025-9-27 15:49:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:49:26-debug: asset-db:refresh-all-database (153ms)
2025-9-27 15:51:00-debug: refresh db internal success
2025-9-27 15:51:00-debug: refresh db i18n success
2025-9-27 15:51:00-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\docs\PathEditorSegmentVisualization.md
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:51:00-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:51:00-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\docs
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:51:00-debug: refresh db assets success
2025-9-27 15:51:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:51:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:51:00-debug: asset-db:refresh-all-database (156ms)
2025-9-27 15:51:00-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 15:54:57-debug: refresh db internal success
2025-9-27 15:54:57-debug: refresh db i18n success
2025-9-27 15:54:57-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:54:57-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\docs\PathEditorSegmentVisualization.md
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:54:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:54:57-debug: refresh db assets success
2025-9-27 15:54:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:54:57-debug: asset-db:refresh-all-database (156ms)
2025-9-27 15:54:57-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-27 15:54:57-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-27 15:56:20-debug: refresh db internal success
2025-9-27 15:56:20-debug: refresh db i18n success
2025-9-27 15:56:20-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:56:20-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:56:20-debug: refresh db assets success
2025-9-27 15:56:20-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:56:20-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:56:20-debug: asset-db:refresh-all-database (175ms)
2025-9-27 15:56:20-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 15:56:20-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 15:56:23-debug: refresh db internal success
2025-9-27 15:56:23-debug: refresh db i18n success
2025-9-27 15:56:23-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:56:23-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:56:23-debug: refresh db assets success
2025-9-27 15:56:23-debug: asset-db:refresh-all-database (134ms)
2025-9-27 15:56:27-debug: refresh db internal success
2025-9-27 15:56:27-debug: refresh db i18n success
2025-9-27 15:56:27-debug: refresh db assets success
2025-9-27 15:56:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:56:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:56:27-debug: asset-db:refresh-all-database (110ms)
2025-9-27 16:08:52-debug: refresh db internal success
2025-9-27 16:08:52-debug: refresh db i18n success
2025-9-27 16:08:52-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:08:52-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:08:52-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\IMovable.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:08:52-debug: refresh db assets success
2025-9-27 16:08:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:08:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:08:52-debug: asset-db:refresh-all-database (120ms)
2025-9-27 16:09:00-debug: refresh db internal success
2025-9-27 16:09:00-debug: refresh db i18n success
2025-9-27 16:09:00-debug: refresh db assets success
2025-9-27 16:09:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:09:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:09:00-debug: asset-db:refresh-all-database (114ms)
2025-9-27 16:09:03-debug: refresh db internal success
2025-9-27 16:09:03-debug: refresh db i18n success
2025-9-27 16:09:03-debug: refresh db assets success
2025-9-27 16:09:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:09:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:09:03-debug: asset-db:refresh-all-database (111ms)
2025-9-27 16:10:08-debug: refresh db internal success
2025-9-27 16:10:08-debug: refresh db i18n success
2025-9-27 16:10:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:10:08-debug: refresh db assets success
2025-9-27 16:10:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:10:08-debug: asset-db:refresh-all-database (139ms)
2025-9-27 16:10:08-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:11:12-debug: refresh db internal success
2025-9-27 16:11:12-debug: refresh db i18n success
2025-9-27 16:11:12-debug: refresh db assets success
2025-9-27 16:11:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:11:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:11:12-debug: asset-db:refresh-all-database (112ms)
2025-9-27 16:11:12-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:11:12-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:11:22-debug: refresh db internal success
2025-9-27 16:11:22-debug: refresh db i18n success
2025-9-27 16:11:22-debug: refresh db assets success
2025-9-27 16:11:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:11:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:11:22-debug: asset-db:refresh-all-database (112ms)
2025-9-27 16:11:22-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:11:22-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:11:38-debug: refresh db internal success
2025-9-27 16:11:38-debug: refresh db i18n success
2025-9-27 16:11:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:11:38-debug: refresh db assets success
2025-9-27 16:11:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:11:38-debug: asset-db:refresh-all-database (131ms)
2025-9-27 16:11:50-debug: refresh db internal success
2025-9-27 16:11:50-debug: refresh db i18n success
2025-9-27 16:11:50-debug: refresh db assets success
2025-9-27 16:11:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:11:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:11:50-debug: asset-db:refresh-all-database (144ms)
2025-9-27 16:11:50-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:11:50-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:13:22-debug: refresh db internal success
2025-9-27 16:13:22-debug: refresh db i18n success
2025-9-27 16:13:22-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\preview\WavePreview.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:13:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:13:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:13:22-debug: refresh db assets success
2025-9-27 16:13:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:13:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:13:22-debug: asset-db:refresh-all-database (154ms)
2025-9-27 16:13:29-debug: refresh db internal success
2025-9-27 16:13:29-debug: refresh db i18n success
2025-9-27 16:13:29-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:13:29-debug: refresh db assets success
2025-9-27 16:13:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:13:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:13:29-debug: asset-db:refresh-all-database (138ms)
2025-9-27 16:13:50-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\wave_1.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:13:50-debug: asset-db:reimport-assetd5f5e836-9495-4c08-890f-d92cc6696838 (2ms)
2025-9-27 16:14:43-debug: refresh db internal success
2025-9-27 16:14:43-debug: refresh db i18n success
2025-9-27 16:14:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:14:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:14:43-debug: refresh db assets success
2025-9-27 16:14:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:14:43-debug: asset-db:refresh-all-database (149ms)
2025-9-27 16:14:43-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:15:15-debug: refresh db internal success
2025-9-27 16:15:15-debug: refresh db i18n success
2025-9-27 16:15:15-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:15:15-debug: refresh db assets success
2025-9-27 16:15:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:15:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:15:15-debug: asset-db:refresh-all-database (146ms)
2025-9-27 16:15:15-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:15:15-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:15:19-debug: refresh db internal success
2025-9-27 16:15:19-debug: refresh db i18n success
2025-9-27 16:15:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:15:19-debug: refresh db assets success
2025-9-27 16:15:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:15:19-debug: asset-db:refresh-all-database (140ms)
2025-9-27 16:15:21-debug: refresh db internal success
2025-9-27 16:15:21-debug: refresh db i18n success
2025-9-27 16:15:21-debug: refresh db assets success
2025-9-27 16:15:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:15:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:15:21-debug: asset-db:refresh-all-database (112ms)
2025-9-27 16:15:23-debug: refresh db internal success
2025-9-27 16:15:23-debug: refresh db i18n success
2025-9-27 16:15:23-debug: refresh db assets success
2025-9-27 16:15:23-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:15:23-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:15:23-debug: asset-db:refresh-all-database (114ms)
2025-9-27 16:15:23-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:15:26-debug: refresh db internal success
2025-9-27 16:15:26-debug: refresh db i18n success
2025-9-27 16:15:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:15:26-debug: refresh db assets success
2025-9-27 16:15:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:15:26-debug: asset-db:refresh-all-database (112ms)
2025-9-27 16:15:45-debug: refresh db internal success
2025-9-27 16:15:45-debug: refresh db i18n success
2025-9-27 16:15:45-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:15:45-debug: refresh db assets success
2025-9-27 16:15:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:15:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:15:45-debug: asset-db:refresh-all-database (138ms)
2025-9-27 16:15:45-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:15:54-debug: refresh db internal success
2025-9-27 16:15:54-debug: refresh db i18n success
2025-9-27 16:15:55-debug: refresh db assets success
2025-9-27 16:15:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:15:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:15:55-debug: asset-db:refresh-all-database (118ms)
2025-9-27 16:15:57-debug: refresh db internal success
2025-9-27 16:15:57-debug: refresh db i18n success
2025-9-27 16:15:57-debug: refresh db assets success
2025-9-27 16:15:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:15:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:15:57-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:15:57-debug: asset-db:refresh-all-database (115ms)
2025-9-27 16:15:57-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:20:34-debug: refresh db internal success
2025-9-27 16:20:34-debug: refresh db i18n success
2025-9-27 16:20:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:20:34-debug: refresh db assets success
2025-9-27 16:20:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:20:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:20:34-debug: asset-db:refresh-all-database (149ms)
2025-9-27 16:20:34-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:20:54-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\wave_1.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:20:54-debug: asset-db:reimport-assetd5f5e836-9495-4c08-890f-d92cc6696838 (2ms)
2025-9-27 16:23:14-debug: refresh db internal success
2025-9-27 16:23:14-debug: refresh db i18n success
2025-9-27 16:23:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:23:14-debug: refresh db assets success
2025-9-27 16:23:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:23:14-debug: asset-db:refresh-all-database (157ms)
2025-9-27 16:23:14-debug: asset-db:worker-effect-data-processing (3ms)
2025-9-27 16:23:14-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-9-27 16:24:44-debug: refresh db internal success
2025-9-27 16:24:44-debug: refresh db i18n success
2025-9-27 16:24:44-debug: refresh db assets success
2025-9-27 16:24:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:24:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:24:44-debug: asset-db:refresh-all-database (117ms)
2025-9-27 16:24:44-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:24:44-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:32:09-debug: refresh db internal success
2025-9-27 16:32:09-debug: refresh db i18n success
2025-9-27 16:32:09-debug: refresh db assets success
2025-9-27 16:32:09-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:32:09-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:32:09-debug: asset-db:refresh-all-database (144ms)
2025-9-27 16:32:09-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:32:29-debug: refresh db internal success
2025-9-27 16:32:29-debug: refresh db i18n success
2025-9-27 16:32:29-debug: refresh db assets success
2025-9-27 16:32:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:32:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:32:29-debug: asset-db:refresh-all-database (138ms)
2025-9-27 16:32:29-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:32:29-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:32:34-debug: refresh db internal success
2025-9-27 16:32:34-debug: refresh db i18n success
2025-9-27 16:32:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:32:34-debug: refresh db assets success
2025-9-27 16:32:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:32:34-debug: asset-db:refresh-all-database (116ms)
2025-9-27 16:33:21-debug: refresh db internal success
2025-9-27 16:33:21-debug: refresh db i18n success
2025-9-27 16:33:21-debug: refresh db assets success
2025-9-27 16:33:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:33:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:33:21-debug: asset-db:refresh-all-database (135ms)
2025-9-27 16:33:38-debug: refresh db internal success
2025-9-27 16:33:38-debug: refresh db i18n success
2025-9-27 16:33:39-debug: refresh db assets success
2025-9-27 16:33:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:33:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:33:39-debug: asset-db:refresh-all-database (151ms)
2025-9-27 16:33:42-debug: refresh db internal success
2025-9-27 16:33:42-debug: refresh db i18n success
2025-9-27 16:33:42-debug: refresh db assets success
2025-9-27 16:33:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:33:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:33:42-debug: asset-db:refresh-all-database (141ms)
2025-9-27 16:33:42-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:33:42-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:33:58-debug: refresh db internal success
2025-9-27 16:33:58-debug: refresh db i18n success
2025-9-27 16:33:58-debug: refresh db assets success
2025-9-27 16:33:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:33:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:33:58-debug: asset-db:refresh-all-database (120ms)
2025-9-27 16:33:58-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:33:58-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:39:22-debug: refresh db internal success
2025-9-27 16:39:22-debug: refresh db i18n success
2025-9-27 16:39:22-debug: refresh db assets success
2025-9-27 16:39:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:39:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:39:22-debug: asset-db:refresh-all-database (143ms)
2025-9-27 16:39:28-debug: refresh db internal success
2025-9-27 16:39:28-debug: refresh db i18n success
2025-9-27 16:39:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:39:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:39:29-debug: refresh db assets success
2025-9-27 16:39:29-debug: asset-db:refresh-all-database (110ms)
2025-9-27 16:41:35-debug: refresh db internal success
2025-9-27 16:41:35-debug: refresh db i18n success
2025-9-27 16:41:35-debug: refresh db assets success
2025-9-27 16:41:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:41:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:41:35-debug: asset-db:refresh-all-database (142ms)
2025-9-27 16:41:35-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:41:35-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:41:40-debug: refresh db internal success
2025-9-27 16:41:40-debug: refresh db i18n success
2025-9-27 16:41:40-debug: refresh db assets success
2025-9-27 16:41:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:41:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:41:40-debug: asset-db:refresh-all-database (108ms)
2025-9-27 16:41:40-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:41:40-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:42:15-debug: refresh db internal success
2025-9-27 16:42:15-debug: refresh db i18n success
2025-9-27 16:42:15-debug: refresh db assets success
2025-9-27 16:42:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:42:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:42:15-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:42:15-debug: asset-db:refresh-all-database (157ms)
2025-9-27 16:42:15-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:42:18-debug: refresh db internal success
2025-9-27 16:42:18-debug: refresh db i18n success
2025-9-27 16:42:19-debug: refresh db assets success
2025-9-27 16:42:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:42:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:42:19-debug: asset-db:refresh-all-database (146ms)
2025-9-27 16:42:19-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:42:19-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:44:30-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\formation\f_01.json
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:44:30-debug: asset-db:reimport-asset30ba4125-5f81-4175-bc13-1a965f6299a3 (3ms)
2025-9-27 16:45:52-debug: refresh db internal success
2025-9-27 16:45:52-debug: refresh db i18n success
2025-9-27 16:45:52-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\FormationPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:45:52-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:45:52-debug: refresh db assets success
2025-9-27 16:45:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:45:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:45:52-debug: asset-db:refresh-all-database (155ms)
2025-9-27 16:45:52-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:45:52-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:45:57-debug: refresh db internal success
2025-9-27 16:45:57-debug: refresh db i18n success
2025-9-27 16:45:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:45:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:45:57-debug: refresh db assets success
2025-9-27 16:45:57-debug: asset-db:refresh-all-database (121ms)
2025-9-27 16:45:57-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:45:57-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:45:59-debug: refresh db internal success
2025-9-27 16:45:59-debug: refresh db i18n success
2025-9-27 16:45:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:45:59-debug: refresh db assets success
2025-9-27 16:45:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:45:59-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:45:59-debug: asset-db:refresh-all-database (112ms)
2025-9-27 16:45:59-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:46:11-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\formation\f_01.json
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:46:11-debug: asset-db:reimport-asset30ba4125-5f81-4175-bc13-1a965f6299a3 (2ms)
2025-9-27 16:46:22-debug: refresh db internal success
2025-9-27 16:46:22-debug: refresh db i18n success
2025-9-27 16:46:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:46:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:46:22-debug: refresh db assets success
2025-9-27 16:46:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:46:22-debug: asset-db:refresh-all-database (145ms)
2025-9-27 16:46:26-debug: refresh db internal success
2025-9-27 16:46:26-debug: refresh db i18n success
2025-9-27 16:46:26-debug: refresh db assets success
2025-9-27 16:46:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:46:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:46:26-debug: asset-db:refresh-all-database (145ms)
2025-9-27 16:46:30-debug: refresh db internal success
2025-9-27 16:46:30-debug: refresh db i18n success
2025-9-27 16:46:30-debug: refresh db assets success
2025-9-27 16:46:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:46:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:46:30-debug: asset-db:refresh-all-database (124ms)
2025-9-27 16:46:30-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:46:30-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\formation\f_01.json
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:46:30-debug: asset-db:reimport-asset30ba4125-5f81-4175-bc13-1a965f6299a3 (2ms)
2025-9-27 16:47:19-debug: refresh db internal success
2025-9-27 16:47:19-debug: refresh db i18n success
2025-9-27 16:47:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:47:19-debug: refresh db assets success
2025-9-27 16:47:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:47:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:47:19-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:47:19-debug: asset-db:refresh-all-database (143ms)
2025-9-27 16:47:19-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:47:32-debug: refresh db internal success
2025-9-27 16:47:32-debug: refresh db i18n success
2025-9-27 16:47:32-debug: refresh db assets success
2025-9-27 16:47:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:47:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:47:32-debug: asset-db:refresh-all-database (116ms)
2025-9-27 16:47:32-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:47:32-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:47:33-debug: refresh db internal success
2025-9-27 16:47:33-debug: refresh db i18n success
2025-9-27 16:47:33-debug: refresh db assets success
2025-9-27 16:47:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:47:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:47:33-debug: asset-db:refresh-all-database (110ms)
2025-9-27 16:47:33-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:47:33-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:47:34-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\formation\f_01.json
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:47:34-debug: asset-db:reimport-asset30ba4125-5f81-4175-bc13-1a965f6299a3 (2ms)
2025-9-27 16:47:57-debug: refresh db internal success
2025-9-27 16:47:57-debug: refresh db i18n success
2025-9-27 16:47:57-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:47:57-debug: refresh db assets success
2025-9-27 16:47:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:47:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:47:57-debug: asset-db:refresh-all-database (156ms)
2025-9-27 16:47:57-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:47:57-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:48:01-debug: refresh db internal success
2025-9-27 16:48:01-debug: refresh db i18n success
2025-9-27 16:48:02-debug: refresh db assets success
2025-9-27 16:48:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:48:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:48:02-debug: asset-db:refresh-all-database (138ms)
2025-9-27 16:48:02-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:48:02-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:48:03-debug: refresh db internal success
2025-9-27 16:48:03-debug: refresh db i18n success
2025-9-27 16:48:03-debug: refresh db assets success
2025-9-27 16:48:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:48:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:48:03-debug: asset-db:refresh-all-database (109ms)
2025-9-27 16:48:03-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:48:03-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:48:24-debug: refresh db internal success
2025-9-27 16:48:24-debug: refresh db i18n success
2025-9-27 16:48:24-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:48:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:48:24-debug: refresh db assets success
2025-9-27 16:48:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:48:24-debug: asset-db:refresh-all-database (138ms)
2025-9-27 16:48:28-debug: refresh db internal success
2025-9-27 16:48:28-debug: refresh db i18n success
2025-9-27 16:48:29-debug: refresh db assets success
2025-9-27 16:48:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:48:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:48:29-debug: asset-db:refresh-all-database (209ms)
2025-9-27 16:48:29-debug: asset-db:worker-effect-data-processing (32ms)
2025-9-27 16:48:29-debug: asset-db-hook-engine-extends-afterRefresh (33ms)
2025-9-27 16:48:29-debug: refresh db internal success
2025-9-27 16:48:29-debug: refresh db i18n success
2025-9-27 16:48:29-debug: refresh db assets success
2025-9-27 16:48:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:48:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:48:29-debug: asset-db:refresh-all-database (136ms)
2025-9-27 16:48:29-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:48:29-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-27 16:48:32-debug: refresh db internal success
2025-9-27 16:48:32-debug: refresh db i18n success
2025-9-27 16:48:32-debug: refresh db assets success
2025-9-27 16:48:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:48:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:48:32-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:48:32-debug: asset-db:refresh-all-database (110ms)
2025-9-27 16:48:32-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:48:46-debug: refresh db internal success
2025-9-27 16:48:46-debug: refresh db i18n success
2025-9-27 16:48:46-debug: refresh db assets success
2025-9-27 16:48:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:48:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:48:46-debug: asset-db:refresh-all-database (136ms)
2025-9-27 16:48:46-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:49:15-debug: refresh db internal success
2025-9-27 16:49:15-debug: refresh db i18n success
2025-9-27 16:49:15-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:49:15-debug: refresh db assets success
2025-9-27 16:49:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:49:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:49:15-debug: asset-db:refresh-all-database (143ms)
2025-9-27 16:49:15-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:49:15-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:49:20-debug: refresh db internal success
2025-9-27 16:49:20-debug: refresh db i18n success
2025-9-27 16:49:20-debug: refresh db assets success
2025-9-27 16:49:20-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:49:20-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:49:20-debug: asset-db:refresh-all-database (144ms)
2025-9-27 16:49:23-debug: refresh db internal success
2025-9-27 16:49:23-debug: refresh db i18n success
2025-9-27 16:49:23-debug: refresh db assets success
2025-9-27 16:49:23-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:49:23-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:49:23-debug: asset-db:refresh-all-database (109ms)
2025-9-27 16:49:23-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:49:23-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:50:12-debug: refresh db internal success
2025-9-27 16:50:12-debug: refresh db i18n success
2025-9-27 16:50:12-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:50:12-debug: refresh db assets success
2025-9-27 16:50:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:50:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:50:12-debug: asset-db:refresh-all-database (145ms)
2025-9-27 16:50:12-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:50:12-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:50:17-debug: refresh db internal success
2025-9-27 16:50:17-debug: refresh db i18n success
2025-9-27 16:50:17-debug: refresh db assets success
2025-9-27 16:50:17-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:50:17-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:50:17-debug: asset-db:refresh-all-database (111ms)
2025-9-27 16:50:17-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:50:17-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:50:18-debug: refresh db internal success
2025-9-27 16:50:18-debug: refresh db i18n success
2025-9-27 16:50:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:50:18-debug: refresh db assets success
2025-9-27 16:50:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:50:18-debug: asset-db:refresh-all-database (112ms)
2025-9-27 16:51:40-debug: refresh db internal success
2025-9-27 16:51:40-debug: refresh db i18n success
2025-9-27 16:51:40-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:51:40-debug: refresh db assets success
2025-9-27 16:51:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:51:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:51:40-debug: asset-db:refresh-all-database (139ms)
2025-9-27 16:51:47-debug: refresh db internal success
2025-9-27 16:51:47-debug: refresh db i18n success
2025-9-27 16:51:47-debug: refresh db assets success
2025-9-27 16:51:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:51:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:51:47-debug: asset-db:refresh-all-database (209ms)
2025-9-27 16:51:47-debug: asset-db:worker-effect-data-processing (35ms)
2025-9-27 16:51:47-debug: asset-db-hook-engine-extends-afterRefresh (35ms)
2025-9-27 16:51:47-debug: refresh db internal success
2025-9-27 16:51:47-debug: refresh db i18n success
2025-9-27 16:51:47-debug: refresh db assets success
2025-9-27 16:51:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:51:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:51:47-debug: asset-db:refresh-all-database (146ms)
2025-9-27 16:51:48-debug: refresh db internal success
2025-9-27 16:51:48-debug: refresh db i18n success
2025-9-27 16:51:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:51:48-debug: refresh db assets success
2025-9-27 16:51:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:51:48-debug: asset-db:refresh-all-database (137ms)
2025-9-27 16:51:49-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\formation\f_01.json
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:51:49-debug: asset-db:reimport-asset30ba4125-5f81-4175-bc13-1a965f6299a3 (2ms)
2025-9-27 16:52:14-debug: refresh db internal success
2025-9-27 16:52:14-debug: refresh db i18n success
2025-9-27 16:52:14-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:52:14-debug: refresh db assets success
2025-9-27 16:52:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:52:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:52:14-debug: asset-db:refresh-all-database (142ms)
2025-9-27 16:52:20-debug: refresh db internal success
2025-9-27 16:52:20-debug: refresh db i18n success
2025-9-27 16:52:20-debug: refresh db assets success
2025-9-27 16:52:20-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:52:20-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:52:20-debug: asset-db:refresh-all-database (112ms)
2025-9-27 16:52:20-debug: refresh db internal success
2025-9-27 16:52:20-debug: refresh db i18n success
2025-9-27 16:52:20-debug: refresh db assets success
2025-9-27 16:52:20-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:52:20-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:52:20-debug: asset-db:refresh-all-database (111ms)
2025-9-27 16:52:53-debug: refresh db internal success
2025-9-27 16:52:53-debug: refresh db i18n success
2025-9-27 16:52:53-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:52:53-debug: refresh db assets success
2025-9-27 16:52:53-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:52:53-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:52:53-debug: asset-db:refresh-all-database (144ms)
2025-9-27 16:52:57-debug: refresh db internal success
2025-9-27 16:52:57-debug: refresh db i18n success
2025-9-27 16:52:57-debug: refresh db assets success
2025-9-27 16:52:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:52:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:52:57-debug: asset-db:refresh-all-database (145ms)
2025-9-27 16:52:59-debug: refresh db internal success
2025-9-27 16:52:59-debug: refresh db i18n success
2025-9-27 16:52:59-debug: refresh db assets success
2025-9-27 16:52:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:52:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:52:59-debug: asset-db:refresh-all-database (111ms)
2025-9-27 16:52:59-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\formation\f_01.json
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:52:59-debug: asset-db:reimport-asset30ba4125-5f81-4175-bc13-1a965f6299a3 (2ms)
2025-9-27 16:53:06-debug: refresh db internal success
2025-9-27 16:53:06-debug: refresh db i18n success
2025-9-27 16:53:06-debug: refresh db assets success
2025-9-27 16:53:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:53:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:53:06-debug: asset-db:refresh-all-database (112ms)
2025-9-27 16:53:08-debug: refresh db internal success
2025-9-27 16:53:08-debug: refresh db i18n success
2025-9-27 16:53:08-debug: refresh db assets success
2025-9-27 16:53:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:53:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:53:08-debug: asset-db:refresh-all-database (109ms)
2025-9-27 16:53:10-debug: refresh db internal success
2025-9-27 16:53:10-debug: refresh db i18n success
2025-9-27 16:53:10-debug: refresh db assets success
2025-9-27 16:53:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:53:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:53:10-debug: asset-db:refresh-all-database (112ms)
2025-9-27 16:53:10-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:53:12-debug: refresh db internal success
2025-9-27 16:53:12-debug: refresh db i18n success
2025-9-27 16:53:12-debug: refresh db assets success
2025-9-27 16:53:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:53:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:53:12-debug: asset-db:refresh-all-database (112ms)
2025-9-27 16:53:21-debug: refresh db internal success
2025-9-27 16:53:21-debug: refresh db i18n success
2025-9-27 16:53:21-debug: refresh db assets success
2025-9-27 16:53:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:53:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:53:21-debug: asset-db:refresh-all-database (109ms)
2025-9-27 16:53:21-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:53:21-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:53:33-debug: refresh db internal success
2025-9-27 16:53:33-debug: refresh db i18n success
2025-9-27 16:53:33-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:53:33-debug: refresh db assets success
2025-9-27 16:53:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:53:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:53:33-debug: asset-db:refresh-all-database (119ms)
2025-9-27 16:53:38-debug: refresh db internal success
2025-9-27 16:53:38-debug: refresh db i18n success
2025-9-27 16:53:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:53:38-debug: refresh db assets success
2025-9-27 16:53:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:53:38-debug: asset-db:refresh-all-database (132ms)
2025-9-27 16:53:39-debug: refresh db internal success
2025-9-27 16:53:39-debug: refresh db i18n success
2025-9-27 16:53:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:53:39-debug: refresh db assets success
2025-9-27 16:53:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:53:39-debug: asset-db:refresh-all-database (114ms)
2025-9-27 16:53:39-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:53:53-debug: refresh db internal success
2025-9-27 16:53:53-debug: refresh db i18n success
2025-9-27 16:53:53-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:53:53-debug: refresh db assets success
2025-9-27 16:53:53-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:53:53-debug: asset-db:refresh-all-database (118ms)
2025-9-27 16:53:53-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:54:07-debug: refresh db internal success
2025-9-27 16:54:07-debug: refresh db i18n success
2025-9-27 16:54:07-debug: refresh db assets success
2025-9-27 16:54:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:54:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:54:07-debug: asset-db:refresh-all-database (140ms)
2025-9-27 16:54:07-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:54:07-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:54:36-debug: refresh db internal success
2025-9-27 16:54:36-debug: refresh db i18n success
2025-9-27 16:54:36-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:54:36-debug: refresh db assets success
2025-9-27 16:54:36-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:54:36-debug: asset-db:refresh-all-database (150ms)
2025-9-27 16:54:36-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:54:36-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:54:46-debug: refresh db internal success
2025-9-27 16:54:46-debug: refresh db i18n success
2025-9-27 16:54:46-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\FormationPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:54:46-debug: refresh db assets success
2025-9-27 16:54:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:54:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:54:46-debug: asset-db:refresh-all-database (124ms)
2025-9-27 16:54:46-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:54:46-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:54:54-debug: refresh db internal success
2025-9-27 16:54:54-debug: refresh db i18n success
2025-9-27 16:54:54-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\FormationPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:54:55-debug: refresh db assets success
2025-9-27 16:54:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:54:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:54:55-debug: asset-db:refresh-all-database (117ms)
2025-9-27 16:54:55-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-27 16:54:55-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-27 16:55:03-debug: refresh db internal success
2025-9-27 16:55:03-debug: refresh db i18n success
2025-9-27 16:55:03-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\FormationPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:55:03-debug: refresh db assets success
2025-9-27 16:55:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:55:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:55:03-debug: asset-db:refresh-all-database (134ms)
2025-9-27 16:55:03-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:55:03-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:55:06-debug: refresh db internal success
2025-9-27 16:55:06-debug: refresh db i18n success
2025-9-27 16:55:06-debug: refresh db assets success
2025-9-27 16:55:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:55:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:55:06-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:55:06-debug: asset-db:refresh-all-database (117ms)
2025-9-27 16:55:06-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:55:13-debug: refresh db internal success
2025-9-27 16:55:13-debug: refresh db i18n success
2025-9-27 16:55:14-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\FormationPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:55:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:55:14-debug: refresh db assets success
2025-9-27 16:55:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:55:14-debug: asset-db:refresh-all-database (114ms)
2025-9-27 16:55:14-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:55:14-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:55:32-debug: refresh db internal success
2025-9-27 16:55:32-debug: refresh db i18n success
2025-9-27 16:55:32-debug: refresh db assets success
2025-9-27 16:55:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:55:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:55:32-debug: asset-db:refresh-all-database (145ms)
2025-9-27 16:55:32-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:55:32-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-27 16:55:44-debug: refresh db internal success
2025-9-27 16:55:44-debug: refresh db i18n success
2025-9-27 16:55:44-debug: refresh db assets success
2025-9-27 16:55:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:55:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:55:44-debug: asset-db:refresh-all-database (116ms)
2025-9-27 16:55:44-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-27 16:55:45-debug: refresh db internal success
2025-9-27 16:55:45-debug: refresh db i18n success
2025-9-27 16:55:45-debug: refresh db assets success
2025-9-27 16:55:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:55:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:55:45-debug: asset-db:refresh-all-database (116ms)
2025-9-27 16:55:45-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:55:45-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-27 16:56:55-debug: refresh db internal success
2025-9-27 16:56:55-debug: refresh db i18n success
2025-9-27 16:56:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:56:55-debug: refresh db assets success
2025-9-27 16:56:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:56:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:56:55-debug: asset-db:refresh-all-database (146ms)
2025-9-27 16:56:55-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:56:55-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:56:59-debug: refresh db internal success
2025-9-27 16:56:59-debug: refresh db i18n success
2025-9-27 16:56:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:56:59-debug: refresh db assets success
2025-9-27 16:56:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:56:59-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:56:59-debug: asset-db:refresh-all-database (152ms)
2025-9-27 16:56:59-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:57:00-debug: refresh db internal success
2025-9-27 16:57:00-debug: refresh db i18n success
2025-9-27 16:57:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:57:00-debug: refresh db assets success
2025-9-27 16:57:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:57:00-debug: asset-db:refresh-all-database (129ms)
2025-9-27 16:57:00-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:57:00-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:57:17-debug: refresh db internal success
2025-9-27 16:57:17-debug: refresh db i18n success
2025-9-27 16:57:17-debug: refresh db assets success
2025-9-27 16:57:17-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:57:17-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:57:17-debug: asset-db:refresh-all-database (113ms)
2025-9-27 16:57:42-debug: refresh db internal success
2025-9-27 16:57:42-debug: refresh db i18n success
2025-9-27 16:57:42-debug: refresh db assets success
2025-9-27 16:57:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:57:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:57:42-debug: asset-db:refresh-all-database (143ms)
2025-9-27 16:57:42-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:57:42-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:58:03-debug: refresh db internal success
2025-9-27 16:58:03-debug: refresh db i18n success
2025-9-27 16:58:03-debug: refresh db assets success
2025-9-27 16:58:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:58:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:58:03-debug: asset-db:refresh-all-database (115ms)
2025-9-27 17:01:42-debug: refresh db internal success
2025-9-27 17:01:42-debug: refresh db i18n success
2025-9-27 17:01:42-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\FormationPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 17:01:42-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 17:01:42-debug: refresh db assets success
2025-9-27 17:01:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:01:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:01:42-debug: asset-db:refresh-all-database (153ms)
2025-9-27 17:01:42-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 17:01:46-debug: refresh db internal success
2025-9-27 17:01:46-debug: refresh db i18n success
2025-9-27 17:01:46-debug: refresh db assets success
2025-9-27 17:01:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:01:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:01:46-debug: asset-db:refresh-all-database (160ms)
2025-9-27 17:01:48-debug: refresh db internal success
2025-9-27 17:01:48-debug: refresh db i18n success
2025-9-27 17:01:48-debug: refresh db assets success
2025-9-27 17:01:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:01:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:01:48-debug: asset-db:refresh-all-database (112ms)
2025-9-27 17:01:48-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 17:01:48-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 17:01:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\formation\f_01.json
background: #aaff85; color: #000;
color: #000;
2025-9-27 17:01:57-debug: asset-db:reimport-asset30ba4125-5f81-4175-bc13-1a965f6299a3 (2ms)
2025-9-27 17:02:47-debug: refresh db internal success
2025-9-27 17:02:47-debug: refresh db i18n success
2025-9-27 17:02:47-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 17:02:47-debug: refresh db assets success
2025-9-27 17:02:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:02:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:02:47-debug: asset-db:refresh-all-database (150ms)
2025-9-27 17:02:47-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 17:02:47-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 17:02:53-debug: refresh db internal success
2025-9-27 17:02:53-debug: refresh db i18n success
2025-9-27 17:02:53-debug: refresh db assets success
2025-9-27 17:02:53-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:02:53-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:02:53-debug: asset-db:refresh-all-database (116ms)
2025-9-27 17:02:53-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 17:02:55-debug: refresh db internal success
2025-9-27 17:02:55-debug: refresh db i18n success
2025-9-27 17:02:55-debug: refresh db assets success
2025-9-27 17:02:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:02:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:02:55-debug: asset-db:refresh-all-database (113ms)
2025-9-27 17:02:55-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 17:02:55-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 17:03:05-debug: refresh db internal success
2025-9-27 17:03:05-debug: refresh db i18n success
2025-9-27 17:03:05-debug: refresh db assets success
2025-9-27 17:03:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:03:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:03:05-debug: asset-db:refresh-all-database (114ms)
2025-9-27 17:03:19-debug: refresh db internal success
2025-9-27 17:03:19-debug: refresh db i18n success
2025-9-27 17:03:19-debug: refresh db assets success
2025-9-27 17:03:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:03:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:03:19-debug: asset-db:refresh-all-database (121ms)
2025-9-27 17:03:19-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 17:03:19-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 17:04:03-debug: refresh db internal success
2025-9-27 17:04:03-debug: refresh db i18n success
2025-9-27 17:04:03-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\FormationPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 17:04:03-debug: refresh db assets success
2025-9-27 17:04:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:04:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:04:03-debug: asset-db:refresh-all-database (148ms)
2025-9-27 17:04:03-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 17:04:03-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 17:04:28-debug: refresh db internal success
2025-9-27 17:04:28-debug: refresh db i18n success
2025-9-27 17:04:29-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 17:04:29-debug: refresh db assets success
2025-9-27 17:04:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:04:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:04:29-debug: asset-db:refresh-all-database (125ms)
2025-9-27 17:04:29-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 17:04:29-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-27 17:05:35-debug: refresh db internal success
2025-9-27 17:05:35-debug: refresh db i18n success
2025-9-27 17:05:35-debug: refresh db assets success
2025-9-27 17:05:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:05:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:05:35-debug: asset-db:refresh-all-database (114ms)
2025-9-27 17:05:35-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-27 17:05:35-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-27 17:06:18-debug: refresh db internal success
2025-9-27 17:06:18-debug: refresh db i18n success
2025-9-27 17:06:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:06:18-debug: refresh db assets success
2025-9-27 17:06:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:06:18-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 17:06:18-debug: asset-db:refresh-all-database (141ms)
2025-9-27 17:06:18-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-27 17:06:32-debug: refresh db internal success
2025-9-27 17:06:32-debug: refresh db i18n success
2025-9-27 17:06:32-debug: refresh db assets success
2025-9-27 17:06:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:06:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:06:32-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 17:06:32-debug: asset-db:refresh-all-database (145ms)
2025-9-27 17:06:32-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 17:06:44-debug: refresh db internal success
2025-9-27 17:06:44-debug: refresh db i18n success
2025-9-27 17:06:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:06:44-debug: refresh db assets success
2025-9-27 17:06:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:06:44-debug: asset-db:refresh-all-database (143ms)
2025-9-27 17:06:44-debug: asset-db:worker-effect-data-processing (-1ms)
2025-9-27 17:06:59-debug: refresh db internal success
2025-9-27 17:06:59-debug: refresh db i18n success
2025-9-27 17:06:59-debug: refresh db assets success
2025-9-27 17:06:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:06:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:06:59-debug: asset-db:refresh-all-database (139ms)
2025-9-27 17:07:56-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\wave_1.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-27 17:07:56-debug: asset-db:reimport-assetd5f5e836-9495-4c08-890f-d92cc6696838 (2ms)
2025-9-27 17:07:56-debug: refresh db internal success
2025-9-27 17:07:56-debug: refresh db i18n success
2025-9-27 17:07:56-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:07:56-debug: refresh db assets success
2025-9-27 17:07:56-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:07:56-debug: asset-db:refresh-all-database (134ms)
2025-9-27 17:07:56-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 17:07:56-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 17:09:53-debug: refresh db internal success
2025-9-27 17:09:53-debug: refresh db i18n success
2025-9-27 17:09:53-debug: refresh db assets success
2025-9-27 17:09:53-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:09:53-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:09:53-debug: asset-db:refresh-all-database (147ms)
2025-9-27 17:10:30-debug: refresh db internal success
2025-9-27 17:10:30-debug: refresh db i18n success
2025-9-27 17:10:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:10:30-debug: refresh db assets success
2025-9-27 17:10:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:10:30-debug: asset-db:refresh-all-database (114ms)
2025-9-27 17:10:32-debug: refresh db internal success
2025-9-27 17:10:32-debug: refresh db i18n success
2025-9-27 17:10:32-debug: refresh db assets success
2025-9-27 17:10:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:10:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:10:32-debug: asset-db:refresh-all-database (114ms)
2025-9-27 17:10:32-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 17:10:43-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\wave_1.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-27 17:10:43-debug: asset-db:reimport-assetd5f5e836-9495-4c08-890f-d92cc6696838 (3ms)
2025-9-27 17:10:43-debug: refresh db internal success
2025-9-27 17:10:43-debug: refresh db i18n success
2025-9-27 17:10:43-debug: refresh db assets success
2025-9-27 17:10:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:10:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:10:43-debug: asset-db:refresh-all-database (126ms)
2025-9-27 17:10:46-debug: refresh db internal success
2025-9-27 17:10:46-debug: refresh db i18n success
2025-9-27 17:10:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:10:46-debug: refresh db assets success
2025-9-27 17:10:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:10:46-debug: asset-db:refresh-all-database (119ms)
2025-9-27 17:10:46-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 17:11:55-debug: start refresh asset from E:\M2Game\Client\assets\resources\game\level\wave\path\p_02.json...
2025-9-27 17:11:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\path\p_02.json
background: #aaff85; color: #000;
color: #000;
2025-9-27 17:11:55-debug: refresh asset E:\M2Game\Client\assets\resources\game\level\wave\path success
2025-9-27 17:11:55-debug: refresh db internal success
2025-9-27 17:11:55-debug: refresh db i18n success
2025-9-27 17:11:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\path
background: #aaff85; color: #000;
color: #000;
2025-9-27 17:11:55-debug: refresh db assets success
2025-9-27 17:11:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:11:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:11:55-debug: asset-db:refresh-all-database (123ms)
2025-9-27 17:11:55-debug: asset-db:worker-effect-data-processing (-1ms)
2025-9-27 17:15:12-debug: refresh db internal success
2025-9-27 17:15:12-debug: refresh db i18n success
2025-9-27 17:15:12-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 17:15:12-debug: refresh db assets success
2025-9-27 17:15:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:15:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:15:12-debug: asset-db:refresh-all-database (166ms)
2025-9-27 17:15:12-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-27 17:15:12-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-27 17:15:34-debug: refresh db internal success
2025-9-27 17:15:34-debug: refresh db i18n success
2025-9-27 17:15:34-debug: refresh db assets success
2025-9-27 17:15:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:15:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:15:34-debug: asset-db:refresh-all-database (122ms)
2025-9-27 17:15:40-debug: %cImport%c: E:\M2Game\Client\assets\scenes\PathEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-27 17:15:40-debug: asset-db:reimport-assetf87e7412-cf27-4eb5-a664-0071cad67cf8 (3ms)
2025-9-27 17:15:59-debug: %cImport%c: E:\M2Game\Client\assets\scenes\FormationEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-27 17:15:59-debug: asset-db:reimport-assetaa842f3a-8aea-42c2-a480-968aade3dfef (3ms)
2025-9-27 17:16:06-debug: %cImport%c: E:\M2Game\Client\assets\scenes\FormationEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-27 17:16:06-debug: asset-db:reimport-assetaa842f3a-8aea-42c2-a480-968aade3dfef (3ms)
2025-9-27 17:16:39-debug: refresh db internal success
2025-9-27 17:16:39-debug: refresh db i18n success
2025-9-27 17:16:40-debug: refresh db assets success
2025-9-27 17:16:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:16:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:16:40-debug: asset-db:refresh-all-database (149ms)
2025-9-27 17:16:40-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 17:16:40-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 17:20:13-debug: refresh db internal success
2025-9-27 17:20:13-debug: refresh db i18n success
2025-9-27 17:20:13-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\FormationPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 17:20:13-debug: refresh db assets success
2025-9-27 17:20:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:20:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:20:13-debug: asset-db:refresh-all-database (129ms)
2025-9-27 17:20:44-debug: refresh db internal success
2025-9-27 17:20:44-debug: refresh db i18n success
2025-9-27 17:20:44-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\FormationPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 17:20:44-debug: refresh db assets success
2025-9-27 17:20:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:20:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:20:44-debug: asset-db:refresh-all-database (159ms)
2025-9-27 17:26:01-debug: refresh db internal success
2025-9-27 17:26:01-debug: refresh db i18n success
2025-9-27 17:26:01-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\FormationPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 17:26:01-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 17:26:01-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 17:26:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:26:01-debug: refresh db assets success
2025-9-27 17:26:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:26:01-debug: asset-db:refresh-all-database (173ms)
2025-9-27 17:26:01-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:10:16-debug: refresh db internal success
2025-9-27 18:10:16-debug: refresh db i18n success
2025-9-27 18:10:17-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 18:10:17-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 18:10:17-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 18:10:17-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 18:10:17-debug: refresh db assets success
2025-9-27 18:10:17-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:10:17-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:10:17-debug: asset-db:refresh-all-database (167ms)
2025-9-27 18:10:38-debug: refresh db internal success
2025-9-27 18:10:38-debug: refresh db i18n success
2025-9-27 18:10:38-debug: refresh db assets success
2025-9-27 18:10:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:10:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:10:38-debug: asset-db:refresh-all-database (122ms)
2025-9-27 18:10:49-debug: refresh db internal success
2025-9-27 18:10:49-debug: refresh db i18n success
2025-9-27 18:10:49-debug: refresh db assets success
2025-9-27 18:10:49-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:10:49-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:10:49-debug: asset-db:refresh-all-database (124ms)
2025-9-27 18:10:49-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 18:10:49-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:10:52-debug: refresh db internal success
2025-9-27 18:10:52-debug: refresh db i18n success
2025-9-27 18:10:52-debug: refresh db assets success
2025-9-27 18:10:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:10:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:10:52-debug: asset-db:refresh-all-database (108ms)
2025-9-27 18:10:52-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 18:10:52-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:10:54-debug: refresh db internal success
2025-9-27 18:10:54-debug: refresh db i18n success
2025-9-27 18:10:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:10:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:10:55-debug: refresh db assets success
2025-9-27 18:10:55-debug: asset-db:refresh-all-database (235ms)
2025-9-27 18:10:55-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 18:10:55-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:10:56-debug: refresh db internal success
2025-9-27 18:10:56-debug: refresh db i18n success
2025-9-27 18:10:56-debug: refresh db assets success
2025-9-27 18:10:56-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:10:56-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:10:56-debug: asset-db:refresh-all-database (116ms)
2025-9-27 18:10:56-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-9-27 18:10:56-debug: asset-db:worker-effect-data-processing (3ms)
2025-9-27 18:11:01-debug: refresh db internal success
2025-9-27 18:11:01-debug: refresh db i18n success
2025-9-27 18:11:01-debug: refresh db assets success
2025-9-27 18:11:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:11:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:11:01-debug: asset-db:refresh-all-database (116ms)
2025-9-27 18:11:01-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 18:11:01-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:11:05-debug: refresh db internal success
2025-9-27 18:11:05-debug: refresh db i18n success
2025-9-27 18:11:05-debug: refresh db assets success
2025-9-27 18:11:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:11:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:11:05-debug: asset-db:refresh-all-database (111ms)
2025-9-27 18:11:05-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 18:11:05-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:11:12-debug: programming:execute-script (1ms)
2025-9-27 18:11:14-debug: start remove asset E:\M2Game\Client\assets\bundles\common\script\game\test\PathMoveOptimizationTest.ts...
2025-9-27 18:11:14-debug: start refresh asset from E:\M2Game\Client\assets\bundles\common\script\game\test\PathMoveOptimizationTest.ts...
2025-9-27 18:11:14-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\common\script\game\test\PathMoveOptimizationTest.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-27 18:11:14-debug: remove asset E:\M2Game\Client\assets\bundles\common\script\game\test\PathMoveOptimizationTest.ts success
2025-9-27 18:11:14-debug: refresh asset E:\M2Game\Client\assets\bundles\common\script\game\test success
2025-9-27 18:11:14-debug: refresh db internal success
2025-9-27 18:11:14-debug: refresh db i18n success
2025-9-27 18:11:14-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\test
background: #aaff85; color: #000;
color: #000;
2025-9-27 18:11:14-debug: refresh db assets success
2025-9-27 18:11:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:11:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:11:14-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-27 18:11:14-debug: asset-db:refresh-all-database (119ms)
2025-9-27 18:11:14-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-27 18:11:18-debug: refresh db internal success
2025-9-27 18:11:18-debug: refresh db i18n success
2025-9-27 18:11:18-debug: refresh db assets success
2025-9-27 18:11:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:11:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:11:18-debug: asset-db:refresh-all-database (116ms)
2025-9-27 18:11:18-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 18:11:18-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-27 18:11:22-debug: refresh db internal success
2025-9-27 18:11:22-debug: refresh db i18n success
2025-9-27 18:11:22-debug: refresh db assets success
2025-9-27 18:11:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:11:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:11:22-debug: asset-db:refresh-all-database (113ms)
2025-9-27 18:11:22-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 18:11:22-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:12:47-debug: refresh db internal success
2025-9-27 18:12:47-debug: refresh db i18n success
2025-9-27 18:12:47-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 18:12:47-debug: refresh db assets success
2025-9-27 18:12:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:12:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:12:47-debug: asset-db:refresh-all-database (156ms)
2025-9-27 18:12:49-debug: refresh db internal success
2025-9-27 18:12:49-debug: refresh db i18n success
2025-9-27 18:12:49-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:12:49-debug: refresh db assets success
2025-9-27 18:12:49-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:12:49-debug: asset-db:refresh-all-database (145ms)
2025-9-27 18:12:56-debug: refresh db internal success
2025-9-27 18:12:56-debug: refresh db i18n success
2025-9-27 18:12:56-debug: refresh db assets success
2025-9-27 18:12:56-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:12:56-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:12:56-debug: asset-db:refresh-all-database (115ms)
2025-9-27 18:13:42-debug: refresh db internal success
2025-9-27 18:13:42-debug: refresh db i18n success
2025-9-27 18:13:42-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 18:13:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:13:42-debug: refresh db assets success
2025-9-27 18:13:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:13:42-debug: asset-db:refresh-all-database (161ms)
2025-9-27 18:13:42-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 18:13:42-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:13:52-debug: refresh db internal success
2025-9-27 18:13:52-debug: refresh db i18n success
2025-9-27 18:13:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:13:52-debug: refresh db assets success
2025-9-27 18:13:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:13:52-debug: asset-db:refresh-all-database (117ms)
2025-9-27 18:13:54-debug: refresh db internal success
2025-9-27 18:13:54-debug: refresh db i18n success
2025-9-27 18:13:54-debug: refresh db assets success
2025-9-27 18:13:54-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:13:54-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:13:54-debug: asset-db:refresh-all-database (114ms)
2025-9-27 18:13:54-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:14:14-debug: refresh db internal success
2025-9-27 18:14:14-debug: refresh db i18n success
2025-9-27 18:14:15-debug: refresh db assets success
2025-9-27 18:14:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:14:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:14:15-debug: asset-db:refresh-all-database (111ms)
2025-9-27 18:14:17-debug: refresh db internal success
2025-9-27 18:14:17-debug: refresh db i18n success
2025-9-27 18:14:17-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:14:17-debug: refresh db assets success
2025-9-27 18:14:17-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:14:17-debug: asset-db:refresh-all-database (116ms)
2025-9-27 18:14:17-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 18:14:17-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:14:31-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\path\p_01.json
background: #aaff85; color: #000;
color: #000;
2025-9-27 18:14:31-debug: asset-db:reimport-asset830ed543-4e69-4b5e-94ae-ab5aac4713a4 (2ms)
2025-9-27 18:14:39-debug: refresh db internal success
2025-9-27 18:14:39-debug: refresh db i18n success
2025-9-27 18:14:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:14:39-debug: refresh db assets success
2025-9-27 18:14:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:14:39-debug: asset-db:refresh-all-database (114ms)
2025-9-27 18:14:41-debug: refresh db internal success
2025-9-27 18:14:41-debug: refresh db i18n success
2025-9-27 18:14:41-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:14:41-debug: refresh db assets success
2025-9-27 18:14:41-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:14:41-debug: asset-db:refresh-all-database (131ms)
2025-9-27 18:14:41-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 18:14:41-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:15:15-debug: refresh db internal success
2025-9-27 18:15:15-debug: refresh db i18n success
2025-9-27 18:15:15-debug: refresh db assets success
2025-9-27 18:15:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:15:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:15:15-debug: asset-db:refresh-all-database (119ms)
2025-9-27 18:15:15-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 18:15:15-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:15:42-debug: refresh db internal success
2025-9-27 18:15:42-debug: refresh db i18n success
2025-9-27 18:15:42-debug: refresh db assets success
2025-9-27 18:15:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:15:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:15:42-debug: asset-db:refresh-all-database (113ms)
2025-9-27 18:15:42-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 18:15:42-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-27 18:16:15-debug: refresh db internal success
2025-9-27 18:16:15-debug: refresh db i18n success
2025-9-27 18:16:15-debug: refresh db assets success
2025-9-27 18:16:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:16:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:16:15-debug: asset-db:refresh-all-database (137ms)
2025-9-27 18:16:15-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:16:15-debug: %cImport%c: E:\M2Game\Client\assets\scenes\PathEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-27 18:16:15-debug: asset-db:reimport-assetf87e7412-cf27-4eb5-a664-0071cad67cf8 (3ms)
2025-9-27 18:16:30-debug: refresh db internal success
2025-9-27 18:16:30-debug: refresh db i18n success
2025-9-27 18:16:31-debug: refresh db assets success
2025-9-27 18:16:31-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:16:31-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:16:31-debug: asset-db:refresh-all-database (116ms)
2025-9-27 18:16:31-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 18:16:31-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:17:10-debug: refresh db internal success
2025-9-27 18:17:10-debug: refresh db i18n success
2025-9-27 18:17:10-debug: refresh db assets success
2025-9-27 18:17:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:17:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:17:10-debug: asset-db:refresh-all-database (159ms)
2025-9-27 18:17:10-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 18:17:10-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:21:29-debug: refresh db internal success
2025-9-27 18:21:29-debug: refresh db i18n success
2025-9-27 18:21:29-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 18:21:29-debug: refresh db assets success
2025-9-27 18:21:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:21:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:21:29-debug: asset-db:refresh-all-database (156ms)
2025-9-27 18:22:28-debug: refresh db internal success
2025-9-27 18:22:28-debug: refresh db i18n success
2025-9-27 18:22:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 18:22:28-debug: refresh db assets success
2025-9-27 18:22:28-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:22:28-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:22:28-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 18:22:28-debug: asset-db:refresh-all-database (146ms)
2025-9-27 18:22:28-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:22:35-debug: refresh db internal success
2025-9-27 18:22:35-debug: refresh db i18n success
2025-9-27 18:22:36-debug: refresh db assets success
2025-9-27 18:22:36-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:22:36-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:22:36-debug: asset-db:refresh-all-database (113ms)
2025-9-27 18:22:40-debug: refresh db internal success
2025-9-27 18:22:40-debug: refresh db i18n success
2025-9-27 18:22:40-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 18:22:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:22:40-debug: refresh db assets success
2025-9-27 18:22:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:22:40-debug: asset-db:refresh-all-database (119ms)
2025-9-27 18:22:40-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 18:22:40-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:23:22-debug: refresh db internal success
2025-9-27 18:23:22-debug: refresh db i18n success
2025-9-27 18:23:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 18:23:22-debug: refresh db assets success
2025-9-27 18:23:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:23:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:23:22-debug: asset-db:refresh-all-database (122ms)
2025-9-27 18:23:22-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:23:25-debug: refresh db internal success
2025-9-27 18:23:25-debug: refresh db i18n success
2025-9-27 18:23:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:23:25-debug: refresh db assets success
2025-9-27 18:23:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:23:25-debug: asset-db:refresh-all-database (112ms)
2025-9-27 18:23:27-debug: refresh db internal success
2025-9-27 18:23:27-debug: refresh db i18n success
2025-9-27 18:23:27-debug: refresh db assets success
2025-9-27 18:23:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:23:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:23:27-debug: asset-db:refresh-all-database (114ms)
2025-9-27 18:23:27-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 18:23:27-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:24:07-debug: refresh db internal success
2025-9-27 18:24:07-debug: refresh db i18n success
2025-9-27 18:24:07-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 18:24:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:24:07-debug: refresh db assets success
2025-9-27 18:24:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:24:07-debug: asset-db:refresh-all-database (156ms)
2025-9-27 18:24:10-debug: refresh db internal success
2025-9-27 18:24:10-debug: refresh db i18n success
2025-9-27 18:24:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:24:10-debug: refresh db assets success
2025-9-27 18:24:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:24:10-debug: asset-db:refresh-all-database (143ms)
2025-9-27 18:24:12-debug: refresh db internal success
2025-9-27 18:24:12-debug: refresh db i18n success
2025-9-27 18:24:12-debug: refresh db assets success
2025-9-27 18:24:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:24:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:24:12-debug: asset-db:refresh-all-database (144ms)
2025-9-27 18:24:29-debug: refresh db internal success
2025-9-27 18:24:29-debug: refresh db i18n success
2025-9-27 18:24:29-debug: refresh db assets success
2025-9-27 18:24:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:24:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:24:29-debug: asset-db:refresh-all-database (142ms)
2025-9-27 18:24:29-debug: asset-db:worker-effect-data-processing (-1ms)
2025-9-27 18:24:37-debug: refresh db internal success
2025-9-27 18:24:37-debug: refresh db i18n success
2025-9-27 18:24:37-debug: refresh db assets success
2025-9-27 18:24:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:24:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:24:37-debug: asset-db:refresh-all-database (114ms)
2025-9-27 18:25:38-debug: refresh db internal success
2025-9-27 18:25:38-debug: refresh db i18n success
2025-9-27 18:25:38-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 18:25:38-debug: refresh db assets success
2025-9-27 18:25:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:25:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:25:38-debug: asset-db:refresh-all-database (154ms)
2025-9-27 18:25:38-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 18:25:38-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:25:42-debug: refresh db internal success
2025-9-27 18:25:42-debug: refresh db i18n success
2025-9-27 18:25:42-debug: refresh db assets success
2025-9-27 18:25:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:25:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:25:42-debug: asset-db:refresh-all-database (140ms)
2025-9-27 18:25:42-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 18:25:42-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:25:43-debug: refresh db internal success
2025-9-27 18:25:43-debug: refresh db i18n success
2025-9-27 18:25:43-debug: refresh db assets success
2025-9-27 18:25:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:25:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:25:43-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 18:25:43-debug: asset-db:refresh-all-database (125ms)
2025-9-27 18:25:43-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-27 18:26:57-debug: refresh db internal success
2025-9-27 18:26:57-debug: refresh db i18n success
2025-9-27 18:26:58-debug: refresh db assets success
2025-9-27 18:26:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:26:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:26:58-debug: asset-db:refresh-all-database (137ms)
2025-9-27 18:26:58-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:30:10-debug: refresh db internal success
2025-9-27 18:30:10-debug: refresh db i18n success
2025-9-27 18:30:10-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 18:30:10-debug: refresh db assets success
2025-9-27 18:30:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:30:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:30:10-debug: asset-db:refresh-all-database (154ms)
2025-9-27 18:30:15-debug: refresh db internal success
2025-9-27 18:30:15-debug: refresh db i18n success
2025-9-27 18:30:15-debug: refresh db assets success
2025-9-27 18:30:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:30:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:30:15-debug: asset-db:refresh-all-database (109ms)
2025-9-27 18:30:15-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 18:30:15-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:30:17-debug: refresh db internal success
2025-9-27 18:30:17-debug: refresh db i18n success
2025-9-27 18:30:17-debug: refresh db assets success
2025-9-27 18:30:17-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:30:17-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:30:17-debug: asset-db:refresh-all-database (109ms)
2025-9-27 18:30:17-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 18:30:17-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:31:42-debug: refresh db internal success
2025-9-27 18:31:42-debug: refresh db i18n success
2025-9-27 18:31:42-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 18:31:42-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 18:31:42-debug: refresh db assets success
2025-9-27 18:31:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:31:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:31:42-debug: asset-db:refresh-all-database (153ms)
2025-9-27 18:31:46-debug: refresh db internal success
2025-9-27 18:31:46-debug: refresh db i18n success
2025-9-27 18:31:46-debug: refresh db assets success
2025-9-27 18:31:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:31:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:31:46-debug: asset-db:refresh-all-database (138ms)
2025-9-27 18:31:47-debug: refresh db internal success
2025-9-27 18:31:47-debug: refresh db i18n success
2025-9-27 18:31:47-debug: refresh db assets success
2025-9-27 18:31:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:31:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:31:47-debug: asset-db:refresh-all-database (112ms)
2025-9-27 18:32:05-debug: refresh db internal success
2025-9-27 18:32:05-debug: refresh db i18n success
2025-9-27 18:32:06-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 18:32:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:32:06-debug: refresh db assets success
2025-9-27 18:32:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:32:06-debug: asset-db:refresh-all-database (146ms)
2025-9-27 18:32:11-debug: refresh db internal success
2025-9-27 18:32:11-debug: refresh db i18n success
2025-9-27 18:32:11-debug: refresh db assets success
2025-9-27 18:32:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:32:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:32:11-debug: asset-db:refresh-all-database (112ms)
2025-9-27 18:32:12-debug: refresh db internal success
2025-9-27 18:32:12-debug: refresh db i18n success
2025-9-27 18:32:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:32:12-debug: refresh db assets success
2025-9-27 18:32:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:32:12-debug: asset-db:refresh-all-database (110ms)
2025-9-27 18:33:04-debug: refresh db internal success
2025-9-27 18:33:04-debug: refresh db i18n success
2025-9-27 18:33:04-debug: refresh db assets success
2025-9-27 18:33:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:33:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:33:04-debug: asset-db:refresh-all-database (145ms)
2025-9-27 18:33:58-debug: refresh db internal success
2025-9-27 18:33:58-debug: refresh db i18n success
2025-9-27 18:33:58-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 18:33:58-debug: refresh db assets success
2025-9-27 18:33:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:33:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:33:58-debug: asset-db:refresh-all-database (148ms)
2025-9-27 18:34:15-debug: refresh db internal success
2025-9-27 18:34:15-debug: refresh db i18n success
2025-9-27 18:34:15-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 18:34:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:34:15-debug: refresh db assets success
2025-9-27 18:34:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:34:15-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 18:34:15-debug: asset-db:refresh-all-database (159ms)
2025-9-27 18:34:15-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:34:19-debug: refresh db internal success
2025-9-27 18:34:19-debug: refresh db i18n success
2025-9-27 18:34:19-debug: refresh db assets success
2025-9-27 18:34:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:34:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:34:19-debug: asset-db:refresh-all-database (144ms)
2025-9-27 18:34:19-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 18:34:19-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:34:22-debug: refresh db internal success
2025-9-27 18:34:22-debug: refresh db i18n success
2025-9-27 18:34:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:34:22-debug: refresh db assets success
2025-9-27 18:34:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:34:22-debug: asset-db:refresh-all-database (111ms)
2025-9-27 18:34:22-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 18:34:22-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:34:55-debug: refresh db internal success
2025-9-27 18:34:55-debug: refresh db i18n success
2025-9-27 18:34:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 18:34:55-debug: refresh db assets success
2025-9-27 18:34:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:34:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:34:55-debug: asset-db:refresh-all-database (144ms)
2025-9-27 18:34:55-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 18:34:55-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:34:59-debug: refresh db internal success
2025-9-27 18:34:59-debug: refresh db i18n success
2025-9-27 18:34:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:34:59-debug: refresh db assets success
2025-9-27 18:34:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:34:59-debug: asset-db:refresh-all-database (151ms)
2025-9-27 18:34:59-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 18:34:59-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:35:01-debug: refresh db internal success
2025-9-27 18:35:01-debug: refresh db i18n success
2025-9-27 18:35:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:35:01-debug: refresh db assets success
2025-9-27 18:35:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:35:01-debug: asset-db:refresh-all-database (128ms)
2025-9-27 18:35:01-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 18:35:01-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:35:21-debug: refresh db internal success
2025-9-27 18:35:21-debug: refresh db i18n success
2025-9-27 18:35:21-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 18:35:21-debug: refresh db assets success
2025-9-27 18:35:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:35:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:35:21-debug: asset-db:refresh-all-database (145ms)
2025-9-27 18:35:21-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-27 18:35:21-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-27 18:35:25-debug: refresh db internal success
2025-9-27 18:35:25-debug: refresh db i18n success
2025-9-27 18:35:26-debug: refresh db assets success
2025-9-27 18:35:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:35:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:35:26-debug: asset-db:refresh-all-database (149ms)
2025-9-27 18:35:26-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 18:35:26-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:35:28-debug: refresh db internal success
2025-9-27 18:35:28-debug: refresh db i18n success
2025-9-27 18:35:28-debug: refresh db assets success
2025-9-27 18:35:28-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:35:28-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:35:28-debug: asset-db:refresh-all-database (116ms)
2025-9-27 18:35:43-debug: refresh db internal success
2025-9-27 18:35:43-debug: refresh db i18n success
2025-9-27 18:35:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 18:35:43-debug: refresh db assets success
2025-9-27 18:35:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:35:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:35:43-debug: asset-db:refresh-all-database (144ms)
2025-9-27 18:35:43-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 18:35:43-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-27 18:35:46-debug: refresh db internal success
2025-9-27 18:35:46-debug: refresh db i18n success
2025-9-27 18:35:46-debug: refresh db assets success
2025-9-27 18:35:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:35:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:35:46-debug: asset-db:refresh-all-database (138ms)
2025-9-27 18:35:46-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 18:35:46-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:35:50-debug: refresh db internal success
2025-9-27 18:35:50-debug: refresh db i18n success
2025-9-27 18:35:50-debug: refresh db assets success
2025-9-27 18:35:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:35:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:35:50-debug: asset-db:refresh-all-database (118ms)
2025-9-27 18:35:50-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 18:35:50-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:36:12-debug: refresh db internal success
2025-9-27 18:36:12-debug: refresh db i18n success
2025-9-27 18:36:12-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 18:36:12-debug: refresh db assets success
2025-9-27 18:36:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:36:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:36:12-debug: asset-db:refresh-all-database (162ms)
2025-9-27 18:36:12-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 18:36:12-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:36:16-debug: refresh db internal success
2025-9-27 18:36:16-debug: refresh db i18n success
2025-9-27 18:36:16-debug: refresh db assets success
2025-9-27 18:36:16-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:36:16-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:36:16-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:36:16-debug: asset-db:refresh-all-database (158ms)
2025-9-27 18:36:18-debug: refresh db internal success
2025-9-27 18:36:18-debug: refresh db i18n success
2025-9-27 18:36:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:36:18-debug: refresh db assets success
2025-9-27 18:36:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:36:18-debug: asset-db:refresh-all-database (112ms)
2025-9-27 18:36:18-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 18:36:18-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:36:53-debug: refresh db internal success
2025-9-27 18:36:53-debug: refresh db i18n success
2025-9-27 18:36:53-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 18:36:53-debug: refresh db assets success
2025-9-27 18:36:53-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:36:53-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:36:53-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 18:36:53-debug: asset-db:refresh-all-database (153ms)
2025-9-27 18:36:53-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:39:12-debug: refresh db internal success
2025-9-27 18:39:12-debug: refresh db i18n success
2025-9-27 18:39:12-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 18:39:12-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 18:39:12-debug: refresh db assets success
2025-9-27 18:39:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:39:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:39:12-debug: asset-db:refresh-all-database (118ms)
2025-9-27 18:39:12-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 18:39:12-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:39:15-debug: refresh db internal success
2025-9-27 18:39:15-debug: refresh db i18n success
2025-9-27 18:39:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:39:15-debug: refresh db assets success
2025-9-27 18:39:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:39:15-debug: asset-db:refresh-all-database (115ms)
2025-9-27 18:39:15-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 18:39:15-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:39:17-debug: refresh db internal success
2025-9-27 18:39:17-debug: refresh db i18n success
2025-9-27 18:39:17-debug: refresh db assets success
2025-9-27 18:39:17-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:39:17-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:39:17-debug: asset-db:refresh-all-database (111ms)
2025-9-27 18:39:17-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 18:39:17-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:41:02-debug: refresh db internal success
2025-9-27 18:41:02-debug: refresh db i18n success
2025-9-27 18:41:02-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 18:41:02-debug: refresh db assets success
2025-9-27 18:41:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:41:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:41:02-debug: asset-db:refresh-all-database (151ms)
2025-9-27 18:41:02-debug: asset-db:worker-effect-data-processing (3ms)
2025-9-27 18:41:02-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-9-27 18:41:05-debug: refresh db internal success
2025-9-27 18:41:05-debug: refresh db i18n success
2025-9-27 18:41:06-debug: refresh db assets success
2025-9-27 18:41:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:41:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:41:06-debug: asset-db:refresh-all-database (146ms)
2025-9-27 18:41:06-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:41:07-debug: refresh db internal success
2025-9-27 18:41:07-debug: refresh db i18n success
2025-9-27 18:41:07-debug: refresh db assets success
2025-9-27 18:41:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:41:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:41:07-debug: asset-db:refresh-all-database (119ms)
2025-9-27 18:46:00-debug: refresh db internal success
2025-9-27 18:46:00-debug: refresh db i18n success
2025-9-27 18:46:00-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 18:46:00-debug: refresh db assets success
2025-9-27 18:46:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:46:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:46:00-debug: asset-db:refresh-all-database (152ms)
2025-9-27 18:46:00-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:46:13-debug: refresh db internal success
2025-9-27 18:46:13-debug: refresh db i18n success
2025-9-27 18:46:13-debug: refresh db assets success
2025-9-27 18:46:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:46:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:46:13-debug: asset-db:refresh-all-database (117ms)
2025-9-27 18:46:16-debug: refresh db internal success
2025-9-27 18:46:16-debug: refresh db i18n success
2025-9-27 18:46:16-debug: refresh db assets success
2025-9-27 18:46:16-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:46:16-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:46:16-debug: asset-db:refresh-all-database (112ms)
2025-9-27 18:48:19-debug: refresh db internal success
2025-9-27 18:48:19-debug: refresh db i18n success
2025-9-27 18:48:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:48:19-debug: refresh db assets success
2025-9-27 18:48:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:48:19-debug: asset-db:refresh-all-database (117ms)
2025-9-27 18:48:20-debug: start refresh asset from E:\M2Game\Client\assets\resources\game\level\wave\path\p_01.json...
2025-9-27 18:48:20-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\path\p_01.json
background: #aaff85; color: #000;
color: #000;
2025-9-27 18:48:20-debug: refresh asset E:\M2Game\Client\assets\resources\game\level\wave\path success
2025-9-27 18:48:21-debug: refresh db internal success
2025-9-27 18:48:21-debug: refresh db i18n success
2025-9-27 18:48:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:48:21-debug: refresh db assets success
2025-9-27 18:48:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:48:21-debug: asset-db:refresh-all-database (112ms)
2025-9-27 18:48:21-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:51:27-debug: refresh db internal success
2025-9-27 18:51:27-debug: refresh db i18n success
2025-9-27 18:51:27-debug: refresh db assets success
2025-9-27 18:51:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:51:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:51:27-debug: asset-db:refresh-all-database (147ms)
2025-9-27 18:51:30-debug: refresh db internal success
2025-9-27 18:51:30-debug: refresh db i18n success
2025-9-27 18:51:30-debug: refresh db assets success
2025-9-27 18:51:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:51:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:51:30-debug: asset-db:refresh-all-database (158ms)
2025-9-27 18:51:33-debug: refresh db internal success
2025-9-27 18:51:33-debug: refresh db i18n success
2025-9-27 18:51:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:51:33-debug: refresh db assets success
2025-9-27 18:51:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:51:33-debug: asset-db:refresh-all-database (123ms)
2025-9-27 18:51:33-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 18:51:33-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:51:37-debug: refresh db internal success
2025-9-27 18:51:37-debug: refresh db i18n success
2025-9-27 18:51:37-debug: refresh db assets success
2025-9-27 18:51:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:51:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:51:37-debug: asset-db:refresh-all-database (115ms)
2025-9-27 18:51:37-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:51:42-debug: refresh db internal success
2025-9-27 18:51:42-debug: refresh db i18n success
2025-9-27 18:51:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:51:42-debug: refresh db assets success
2025-9-27 18:51:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:51:42-debug: asset-db:refresh-all-database (111ms)
2025-9-27 18:51:44-debug: refresh db internal success
2025-9-27 18:51:44-debug: refresh db i18n success
2025-9-27 18:51:44-debug: refresh db assets success
2025-9-27 18:51:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:51:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:51:44-debug: asset-db:refresh-all-database (112ms)
2025-9-27 18:51:50-debug: refresh db internal success
2025-9-27 18:51:50-debug: refresh db i18n success
2025-9-27 18:51:50-debug: refresh db assets success
2025-9-27 18:51:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:51:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:51:50-debug: asset-db:refresh-all-database (111ms)
2025-9-27 18:51:57-debug: refresh db internal success
2025-9-27 18:51:57-debug: refresh db i18n success
2025-9-27 18:51:57-debug: refresh db assets success
2025-9-27 18:51:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:51:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:51:57-debug: asset-db:refresh-all-database (108ms)
2025-9-27 18:51:58-debug: start refresh asset from E:\M2Game\Client\assets\resources\game\level\wave\path\p_01.json...
2025-9-27 18:51:58-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\path\p_01.json
background: #aaff85; color: #000;
color: #000;
2025-9-27 18:51:58-debug: refresh asset E:\M2Game\Client\assets\resources\game\level\wave\path success
2025-9-27 18:51:58-debug: refresh db internal success
2025-9-27 18:51:58-debug: refresh db i18n success
2025-9-27 18:51:58-debug: refresh db assets success
2025-9-27 18:51:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:51:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:51:58-debug: asset-db:refresh-all-database (108ms)
2025-9-27 18:52:03-debug: refresh db internal success
2025-9-27 18:52:03-debug: refresh db i18n success
2025-9-27 18:52:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:52:04-debug: refresh db assets success
2025-9-27 18:52:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:52:04-debug: asset-db:refresh-all-database (113ms)
2025-9-27 18:52:06-debug: refresh db internal success
2025-9-27 18:52:06-debug: refresh db i18n success
2025-9-27 18:52:06-debug: refresh db assets success
2025-9-27 18:52:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:52:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:52:06-debug: asset-db:refresh-all-database (112ms)
2025-9-27 18:54:38-debug: refresh db internal success
2025-9-27 18:54:38-debug: refresh db i18n success
2025-9-27 18:54:38-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 18:54:38-debug: refresh db assets success
2025-9-27 18:54:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:54:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:54:38-debug: asset-db:refresh-all-database (144ms)
2025-9-27 18:54:38-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 18:54:38-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 18:54:49-debug: refresh db internal success
2025-9-27 18:54:49-debug: refresh db i18n success
2025-9-27 18:54:49-debug: refresh db assets success
2025-9-27 18:54:49-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 18:54:49-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 18:54:49-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 18:54:49-debug: asset-db:refresh-all-database (119ms)
2025-9-27 18:54:49-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 09:33:55-debug: refresh db internal success
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\extensions\i18n\assets\TTFUtils.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\extensions\i18n\assets\LocalizedLabel.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: refresh db i18n success
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\font
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\bottom_bar
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\spine\plane\10001001
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\top_bar
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\left
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\fight
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\right
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\middle
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\core\base\MessageBox.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\common\script\game\const\GameConst.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\font\Bld.ttf
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\common\script\ui\home\fight\RogueSelectIcon.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\layer\GameInUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\bottom_bar\airbase_btn.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\bottom_bar\battle_btn.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\bottom_bar\battle_frame.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\bottom_bar\bottom_bar_bg.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\bottom_bar\airbase_btn.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\bottom_bar\battle_btn.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\bottom_bar\battle_frame.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\bottom_bar\bottom_bar_bg.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\bottom_bar\shop_btn.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\bottom_bar\talent_btn.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\bottom_bar\shop_btn.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\bottom_bar\warehouse_btn.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\home-panel-atlas.pac
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\bottom_bar\talent_btn.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\top_bar\avtar_bg.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\bottom_bar\warehouse_btn.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\top_bar\avtar_frame.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\top_bar\avtar_bg.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\top_bar\coin_btn.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\top_bar\avtar_frame.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\top_bar\avtar_bg.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\top_bar\diamond_btn.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\top_bar\coin_btn.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\top_bar\energy_btn.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\top_bar\avtar_frame.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\top_bar\diamond_btn.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\top_bar\coin_btn.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\top_bar\energy_btn.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\top_bar\exp_progress.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\top_bar\diamond_btn.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\top_bar\energy_btn.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\top_bar\exp_progress_bg.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\top_bar\exp_progress.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\top_bar\top_bar_bg.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\top_bar\exp_progress_bg.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\top_bar\exp_progress.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\top_bar\top_item_add_btn.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\top_bar\top_bar_bg.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\top_bar\exp_progress_bg.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\top_bar\top_item_bg.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\top_bar\top_item_add_btn.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\top_bar\top_bar_bg.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\top_bar\top_item_bg.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\spine\plane\10001001\10001001.atlas
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\top_bar\top_item_add_btn.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\top_bar\top_item_bg.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\spine\plane\10001001\10001001.json
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\spine\plane\10001001\10001001.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\fight\RogueAbilityIcon.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\spine\plane\10001001\10001001.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\fight\RogueSelectIcon.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\fight\RogueUI.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\left\activity_btn.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\left\battle_pass_btn.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\left\checkin_btn.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\left\fund_btn.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\left\activity_btn.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\left\month_card_btn.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\left\battle_pass_btn.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\left\checkin_btn.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\left\fund_btn.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\left\activity_btn.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\left\battle_pass_btn.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\left\month_card_btn.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\left\checkin_btn.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\left\fund_btn.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\left\month_card_btn.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\middle\auto_idle_plane_btn.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\middle\casual_mode_btn.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\middle\chat.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\middle\endless_mode_btn.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\middle\auto_idle_plane_btn.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\middle\girl_bg.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\middle\casual_mode_btn.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\middle\endless_mode_btn.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\middle\chat.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\middle\auto_idle_plane_btn.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\middle\girl_bg.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\middle\casual_mode_btn.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\middle\endless_mode_btn.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\middle\chat.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\middle\girl_bg.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\middle\girl_chat_bubble.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\middle\girl_heart.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\middle\girl_round_bg.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\middle\map_mode_btn.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\middle\girl_chat_bubble.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\middle\star.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\middle\girl_heart.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\middle\girl_round_bg.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\middle\map_mode_btn.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\middle\girl_heart.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\middle\star.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\middle\girl_chat_bubble.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\middle\girl_round_bg.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\middle\map_mode_btn.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\middle\star.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\middle\star_bg.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\middle\star_single_bg.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\right\announcement.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\right\april_fools_day_btn.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\middle\star_bg.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\right\challenge_bg.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\middle\star_single_bg.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\right\announcement.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\right\april_fools_day_btn.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\right\challenge_bg.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\middle\star_single_bg.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\right\announcement.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\middle\star_bg.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\right\april_fools_day_btn.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\right\challenge_bg.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\right\competition_bg.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\right\friend.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\right\mail.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\right\major_activity_btn.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\right\mail.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\right\friend.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\right\competition_bg.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\right\setting.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\right\major_activity_btn.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\right\mail.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\right\friend.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\right\competition_bg.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\right\setting.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\right\major_activity_btn.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\right\setting.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\right\social.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\right\task_tip_arrow.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\right\task_tip_bg.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\right\social.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\right\task_tip_progress.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\right\task_tip_arrow.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\right\task_tip_bg.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\right\task_tip_progress_bg.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\right\social.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\right\task_tip_progress.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\right\task_tip_bg.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\right\task_tip_arrow.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\right\task_tip_progress_bg.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\right\task_tip_progress.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\texture\panel\right\task_tip_progress_bg.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\font
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\texture
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelPrefabParse.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\texture\icon
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\fight
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\rogue
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\bottom_ui
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\top_ui
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\spine\plane\enemyplane
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\spine\plane\mainplane
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\texture\icon\rogue
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\texture\icon\skill
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\prefab\ui\fight
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\scripts\core\base\GameConst.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\const\HomeUIConst.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\event\BottomUIEvent.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\middle
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\left
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\spine\plane\enemyplane\10005
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\bullet\PF_EnemyBullet
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\spine\plane\mainplane\10001001
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\spine\plane\mainplane\10100001
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\LevelDebug.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\spine\plane\mainplane\10100005
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\common\components\messagebox
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\layer\GameFightUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\common\components\messagebox\MessageBox.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\editor\planeview\beijing.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\editor\planeview\BOOS01.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\editor\planeview\xiaohuangji.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\font\Bld.ttf
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\font\gamefont.ttf
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\editor\planeview\xiaohuangji.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\editor\planeview\BOOS01.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\editor\planeview\beijing.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\editor\planeview\xiaohuangji.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\editor\planeview\BOOS01.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\editor\planeview\beijing.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\fight\fight.plist
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\fight\fight.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\rogue\rogue.plist
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\rogue\rogue.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\fight\fight.plist@ee99e
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\rogue\rogue.plist@53fb5
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\fight\fight.plist@d29b3
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\rogue\rogue.plist@5c348
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\fight\fight.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\fight\fight.plist@c7e12
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\rogue\rogue.plist@4c659
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\rogue\rogue.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\fight\fight.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\fight\fight.plist@2aa50
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\bottom_ui\airbase_btn.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\rogue\rogue.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\rogue\rogue.plist@0b5eb
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\fight\fight.plist@05839
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\rogue\rogue.plist@dc20f
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\bottom_ui\battle_btn.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\fight\fight.plist@b8a89
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\rogue\rogue.plist@f35f4
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\bottom_ui\airbase_btn.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\fight\fight.plist@411bc
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\rogue\rogue.plist@053d5
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\bottom_ui\battle_frame.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\bottom_ui\airbase_btn.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\fight\fight.plist@97ad1
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\rogue\rogue.plist@3999d
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\fight\fight.plist@3ddf9
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\bottom_ui\battle_btn.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\rogue\rogue.plist@bd93a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\bottom_ui\bottom_bar_bg.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\bottom_ui\battle_btn.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\fight\fight.plist@e9a52
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\rogue\rogue.plist@cc7c5
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\fight\fight.plist@972a4
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\bottom_ui\battle_frame.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\rogue\rogue.plist@32ca2
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\bottom_ui\battle_frame.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\fight\fight.plist@45ebf
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\rogue\rogue.plist@f99dd
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\bottom_ui\bottom_bar_bg.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\bottom_ui\shop_btn.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\fight\fight.plist@cc272
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\rogue\rogue.plist@df83d
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\bottom_ui\bottom_bar_bg.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\bottom_ui\talent_btn.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\fight\fight.plist@832be
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\rogue\rogue.plist@d179a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\fight\fight.plist@758a2
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\bottom_ui\warehouse_btn.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\bottom_ui\shop_btn.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\home-panel-atlas.pac
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\top_ui\avtar_bg.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\bottom_ui\shop_btn.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\bottom_ui\talent_btn.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\top_ui\avtar_frame.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\bottom_ui\talent_btn.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\top_ui\coin_btn.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\bottom_ui\warehouse_btn.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\bottom_ui\warehouse_btn.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\top_ui\avtar_bg.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\top_ui\diamond_btn.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\top_ui\avtar_frame.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\top_ui\avtar_bg.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\top_ui\avtar_frame.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\top_ui\energy_btn.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\top_ui\coin_btn.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\top_ui\exp_progress.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\top_ui\diamond_btn.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\top_ui\coin_btn.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\top_ui\exp_progress_bg.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\top_ui\diamond_btn.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\top_ui\energy_btn.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\top_ui\top_bar_bg.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\top_ui\exp_progress.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\top_ui\energy_btn.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\emitter\Bullet_211200121.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\top_ui\exp_progress.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\top_ui\exp_progress_bg.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\top_ui\exp_progress_bg.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\emitter\Bullet_211200122.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\emitter\Bullet_211200123.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\top_ui\top_bar_bg.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\emitter\Bullet_211200124.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\emitter\Bullet_211200125.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\top_ui\top_bar_bg.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\emitter\Bullet_211200126.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\emitter\Bullet_211200201.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\emitter\Bullet_211200202.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\emitter\Bullet_211200203.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\emitter\Bullet_211200204.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\emitter\Bullet_211200205.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\emitter\Bullet_211200206.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\emitter\Bullet_211200301.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\texture\icon\rogue\1101.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\texture\icon\rogue\1102.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\texture\icon\rogue\1103.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\texture\icon\skill\1001.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\texture\icon\skill\1002.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\texture\icon\rogue\1101.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\texture\icon\rogue\1102.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\texture\icon\rogue\1103.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\texture\icon\rogue\1101.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\texture\icon\skill\1001.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\texture\icon\rogue\1102.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\texture\icon\skill\1002.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\texture\icon\rogue\1103.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\texture\icon\skill\1001.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\texture\icon\skill\1002.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\prefab\ui\fight\RogueUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\left\activity_btn.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\left\battle_pass_btn.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\left\checkin_btn.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\left\fund_btn.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\left\month_card_btn.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\left\activity_btn.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\left\checkin_btn.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\left\fund_btn.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\left\activity_btn.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\left\battle_pass_btn.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\left\checkin_btn.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\left\month_card_btn.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\left\fund_btn.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\left\battle_pass_btn.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\left\month_card_btn.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\middle\auto_idle_plane_btn.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\middle\casual_mode_btn.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\middle\chat.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\middle\endless_mode_btn.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\middle\girl_bg.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\middle\auto_idle_plane_btn.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\middle\casual_mode_btn.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\middle\auto_idle_plane_btn.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\middle\casual_mode_btn.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\middle\chat.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\middle\girl_bg.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\middle\endless_mode_btn.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\middle\chat.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\middle\girl_bg.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\middle\endless_mode_btn.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\middle\girl_chat_bubble.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\middle\girl_heart.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\middle\girl_round_bg.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\middle\map_mode_btn.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\middle\star.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\middle\girl_chat_bubble.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\middle\girl_heart.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\middle\girl_chat_bubble.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\middle\girl_round_bg.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\middle\girl_heart.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\middle\girl_round_bg.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\middle\map_mode_btn.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\middle\star.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\middle\star_bg.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\middle\map_mode_btn.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\middle\star_single_bg.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\middle\star.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\announcement.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\april_fools_day_btn.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\challenge_bg.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\middle\star_bg.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\middle\star_single_bg.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\middle\star_bg.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\middle\star_single_bg.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\challenge_bg.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\announcement.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\april_fools_day_btn.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\announcement.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\challenge_bg.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\competition_bg.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\april_fools_day_btn.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\friend.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\mail.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\major_activity_btn.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\setting.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\competition_bg.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\friend.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\competition_bg.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\major_activity_btn.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\mail.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\friend.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\setting.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\major_activity_btn.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\mail.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\setting.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\social.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\task_tip_arrow.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\task_tip_bg.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\task_tip_progress.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\task_tip_progress_bg.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\social.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\task_tip_arrow.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\social.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\task_tip_progress.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\task_tip_bg.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\task_tip_arrow.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\task_tip_progress_bg.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\task_tip_bg.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\task_tip_progress.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\bullet\PF_EnemyBullet\PF_EnemyBullet_001.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\task_tip_progress_bg.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\spine\plane\enemyplane\10005\100005.atlas
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\spine\plane\enemyplane\10005\100005.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\spine\plane\enemyplane\10005\100005.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\spine\plane\mainplane\10001001\10001001.atlas
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\spine\plane\mainplane\10001001\10001001.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-log: UUID is initialized for E:\M2Game\Client\assets\resources\spine\plane\enemyplane\10005\100005.png.
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\spine\plane\mainplane\10001001\10001001.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\spine\plane\mainplane\10100001\10100001.atlas
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\spine\plane\mainplane\10100001\10100001.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-log: UUID is initialized for E:\M2Game\Client\assets\resources\spine\plane\mainplane\10001001\10001001.png.
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\spine\plane\enemyplane\10005\100005.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-log: UUID is initialized for E:\M2Game\Client\assets\resources\spine\plane\mainplane\10100001\10100001.png.
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\spine\plane\mainplane\10100001\10100001.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\spine\plane\mainplane\10100005\10100005.atlas
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\spine\plane\mainplane\10100005\10100005.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\spine\plane\mainplane\10100005\10100005.png
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\spine\plane\mainplane\10001001\10001001.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-log: UUID is initialized for E:\M2Game\Client\assets\resources\spine\plane\mainplane\10100005\10100005.png.
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\parse_template.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\parse_template.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\spine\plane\mainplane\10100001\10100001.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\editor
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\scenes
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\spine\plane\mainplane\10100005\10100005.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\editor\level
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\editor\planeview
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\i18n
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\scenes\Game.scene
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\scripts\resupdate
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbequipupgrade.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbplane.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbreschapter.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresenemy.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresequip.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresgamemode.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbreslevel.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresitem.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresloot.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos\GizmoManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorBaseUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorLayerUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\utils.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\editor\planeview\PlaneView.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\editor\planeview\PlaneView.scene
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\spine\plane
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\i18n\zh.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\scripts\core\base
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\scripts\resupdate\ResUpdate.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\prefab\ui
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\app
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\const
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\leveldata
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\CommonEntry.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\network
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\event
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\luban
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\platformsdk
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\gm\script\ui
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\gm\prefab\ui
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_task\prefab\ui
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\bullet
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\emitter
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\mainPlane
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\autogen\luban
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\app\MyApp.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\base
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\friend
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\game_logic
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\mail
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\plane
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\event\DataEvent.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\collider-system
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\const
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\GameIns.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\event
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\scenes
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\utils
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\leveldata\leveldata.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\network\NetMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\luban\LubanMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\platformsdk\DevLogin.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\platformsdk\IPlatformSDK.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\common
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\friend
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\platformsdk\WXLogin.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\mail
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\main
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\gameui
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\Plane.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\story
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\task
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\gm\prefab\ui\GmButtonUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\gm\script\ui\GmButtonUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\BottomUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\HomeUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\TopUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_task\prefab\ui\TaskTipUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\emitter\Bullet_211200102.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\emitter\Bullet_211200101.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\emitter\Bullet_211200103.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\emitter\Bullet_211200104.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\emitter\Bullet_211200106.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\emitter\Bullet_211200105.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\emitter\Bullet_211200107.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\mainPlane\MainPlane.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\autogen\luban\schema.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\base\Role.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\base\AttributeData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\friend\Friend.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\game_logic\GameLogic.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\mail\Mail.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\plane\PlaneCacheInfo.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\plane\PlaneData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\collider-system\FBoxCollider.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\EnemyData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\event\GameEvent.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager\BossManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager\BattleManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager\GamePlaneManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager\EnemyManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager\GameDataManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager\MainPlaneManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager\HurtEffectManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\CameraMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\scenes\GameMain.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\base
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\layer
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\map
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\utils\Tools.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\utils\UITools.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\common\components
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\common\StatisticsHurtCell.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\common\SettlementUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\common\StatisticsUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\friend\FriendUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\gameui\DevLoginUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home\BottomUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home\fight
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home\TopUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\mail\MailCellUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home\HomeUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\mail\MailUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\main\MainUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\story\StoryUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\task\TaskTipUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\layer\BattleLayer.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\base\Controller.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\layer\EnemyEffectLayer.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\layer\EffectLayer.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\map\LevelLayerUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\mainPlane
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\enemy
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\PlaneBase.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\PlaneBaseDebug.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\skill
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home\fight\RogueUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\enemy\EnemyPlaneBase.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\mainPlane\MainPlane.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\mainPlane\MainPlaneDebug.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\skill\Buff.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: refresh db assets success
2025-9-29 09:33:56-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 09:33:56-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 09:33:56-debug: asset-db:refresh-all-database (634ms)
2025-9-29 09:33:56-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 09:33:56-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\resources\spine\plane\enemyplane\10005\100005.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\resources\spine\plane\mainplane\10100001\10100001.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\resources\spine\plane\mainplane\10001001\10001001.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-log: UUID is initialized for E:\M2Game\Client\assets\resources\spine\plane\enemyplane\10005\100005.png.
2025-9-29 09:33:56-log: UUID is initialized for E:\M2Game\Client\assets\resources\spine\plane\mainplane\10001001\10001001.png.
2025-9-29 09:33:56-log: UUID is initialized for E:\M2Game\Client\assets\resources\spine\plane\mainplane\10100001\10100001.png.
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\resources\spine\plane\mainplane\10100005\10100005.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-log: UUID is initialized for E:\M2Game\Client\assets\resources\spine\plane\mainplane\10100005\10100005.png.
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\fight\fight.plist
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\rogue\rogue.plist
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cReImport%c: E:\M2Game\Client\assets\bundles\common\texture\fight\fight.plist@ee99e
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cReImport%c: E:\M2Game\Client\assets\bundles\common\texture\rogue\rogue.plist@53fb5
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cReImport%c: E:\M2Game\Client\assets\bundles\common\texture\fight\fight.plist@d29b3
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cReImport%c: E:\M2Game\Client\assets\bundles\common\texture\rogue\rogue.plist@5c348
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cReImport%c: E:\M2Game\Client\assets\bundles\common\texture\fight\fight.plist@c7e12
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cReImport%c: E:\M2Game\Client\assets\bundles\common\texture\rogue\rogue.plist@4c659
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cReImport%c: E:\M2Game\Client\assets\bundles\common\texture\fight\fight.plist@2aa50
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cReImport%c: E:\M2Game\Client\assets\bundles\common\texture\rogue\rogue.plist@0b5eb
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cReImport%c: E:\M2Game\Client\assets\bundles\common\texture\fight\fight.plist@05839
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cReImport%c: E:\M2Game\Client\assets\bundles\common\texture\rogue\rogue.plist@dc20f
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cReImport%c: E:\M2Game\Client\assets\bundles\common\texture\fight\fight.plist@b8a89
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cReImport%c: E:\M2Game\Client\assets\bundles\common\texture\rogue\rogue.plist@f35f4
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cReImport%c: E:\M2Game\Client\assets\bundles\common\texture\fight\fight.plist@411bc
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cReImport%c: E:\M2Game\Client\assets\bundles\common\texture\rogue\rogue.plist@053d5
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cReImport%c: E:\M2Game\Client\assets\bundles\common\texture\fight\fight.plist@97ad1
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cReImport%c: E:\M2Game\Client\assets\bundles\common\texture\rogue\rogue.plist@3999d
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cReImport%c: E:\M2Game\Client\assets\bundles\common\texture\fight\fight.plist@3ddf9
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cReImport%c: E:\M2Game\Client\assets\bundles\common\texture\rogue\rogue.plist@bd93a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cReImport%c: E:\M2Game\Client\assets\bundles\common\texture\fight\fight.plist@e9a52
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cReImport%c: E:\M2Game\Client\assets\bundles\common\texture\rogue\rogue.plist@cc7c5
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cReImport%c: E:\M2Game\Client\assets\bundles\common\texture\fight\fight.plist@972a4
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cReImport%c: E:\M2Game\Client\assets\bundles\common\texture\rogue\rogue.plist@32ca2
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cReImport%c: E:\M2Game\Client\assets\bundles\common\texture\fight\fight.plist@45ebf
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cReImport%c: E:\M2Game\Client\assets\bundles\common\texture\rogue\rogue.plist@f99dd
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cReImport%c: E:\M2Game\Client\assets\bundles\common\texture\fight\fight.plist@cc272
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cReImport%c: E:\M2Game\Client\assets\bundles\common\texture\rogue\rogue.plist@df83d
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cReImport%c: E:\M2Game\Client\assets\bundles\common\texture\fight\fight.plist@832be
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cReImport%c: E:\M2Game\Client\assets\bundles\common\texture\rogue\rogue.plist@d179a
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:33:56-debug: %cReImport%c: E:\M2Game\Client\assets\bundles\common\texture\fight\fight.plist@758a2
background: #aaff85; color: #000;
color: #000;
2025-9-29 09:34:00-debug: refresh db internal success
2025-9-29 09:34:00-debug: refresh db i18n success
2025-9-29 09:34:00-debug: refresh db assets success
2025-9-29 09:34:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 09:34:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 09:34:00-debug: asset-db:refresh-all-database (127ms)
2025-9-29 09:34:00-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 09:34:00-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
