2025-9-27 14:11:39-debug: start **** info
2025-9-27 14:11:39-log: Cannot access game frame or container.
2025-9-27 14:11:39-debug: asset-db:require-engine-code (417ms)
2025-9-27 14:11:39-log: meshopt wasm decoder initialized
2025-9-27 14:11:39-log: [bullet]:bullet wasm lib loaded.
2025-9-27 14:11:39-log: [box2d]:box2d wasm lib loaded.
2025-9-27 14:11:39-log: Cocos Creator v3.8.6
2025-9-27 14:11:39-log: Forward render pipeline initialized.
2025-9-27 14:11:39-debug: [Assets Memory track]: asset-db-plugin-register: programming start:81.21MB, end 84.08MB, increase: 2.86MB
2025-9-27 14:11:39-log: Using legacy pipeline
2025-9-27 14:11:40-debug: [Assets Memory track]: asset-db-plugin-register: builder start:84.12MB, end 224.82MB, increase: 140.70MB
2025-9-27 14:11:40-debug: [Assets Memory track]: asset-db-plugin-register: project start:81.07MB, end 228.46MB, increase: 147.39MB
2025-9-27 14:11:39-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:30.88MB, end 80.05MB, increase: 49.17MB
2025-9-27 14:11:40-debug: [Assets Memory track]: asset-db-plugin-register: engine-extends start:225.07MB, end 228.24MB, increase: 3.18MB
2025-9-27 14:11:40-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:80.08MB, end 228.48MB, increase: 148.41MB
2025-9-27 14:11:40-debug: run package(harmonyos-next) handler(enable) start
2025-9-27 14:11:40-debug: run package(harmonyos-next) handler(enable) success!
2025-9-27 14:11:40-debug: run package(honor-mini-game) handler(enable) success!
2025-9-27 14:11:40-debug: run package(honor-mini-game) handler(enable) start
2025-9-27 14:11:40-debug: run package(huawei-quick-game) handler(enable) start
2025-9-27 14:11:40-debug: run package(huawei-agc) handler(enable) start
2025-9-27 14:11:40-debug: run package(huawei-agc) handler(enable) success!
2025-9-27 14:11:40-debug: run package(huawei-quick-game) handler(enable) success!
2025-9-27 14:11:40-debug: run package(linux) handler(enable) start
2025-9-27 14:11:40-debug: run package(ios) handler(enable) start
2025-9-27 14:11:40-debug: run package(ios) handler(enable) success!
2025-9-27 14:11:40-debug: run package(linux) handler(enable) success!
2025-9-27 14:11:40-debug: run package(mac) handler(enable) success!
2025-9-27 14:11:40-debug: run package(migu-mini-game) handler(enable) start
2025-9-27 14:11:40-debug: run package(mac) handler(enable) start
2025-9-27 14:11:40-debug: run package(native) handler(enable) start
2025-9-27 14:11:40-debug: run package(migu-mini-game) handler(enable) success!
2025-9-27 14:11:40-debug: run package(ohos) handler(enable) success!
2025-9-27 14:11:40-debug: run package(native) handler(enable) success!
2025-9-27 14:11:40-debug: run package(oppo-mini-game) handler(enable) start
2025-9-27 14:11:40-debug: run package(ohos) handler(enable) start
2025-9-27 14:11:40-debug: run package(oppo-mini-game) handler(enable) success!
2025-9-27 14:11:40-debug: run package(runtime-dev-tools) handler(enable) success!
2025-9-27 14:11:40-debug: run package(taobao-mini-game) handler(enable) success!
2025-9-27 14:11:40-debug: run package(runtime-dev-tools) handler(enable) start
2025-9-27 14:11:40-debug: run package(vivo-mini-game) handler(enable) start
2025-9-27 14:11:40-debug: run package(taobao-mini-game) handler(enable) start
2025-9-27 14:11:40-debug: run package(vivo-mini-game) handler(enable) success!
2025-9-27 14:11:40-debug: run package(web-desktop) handler(enable) start
2025-9-27 14:11:40-debug: run package(web-desktop) handler(enable) success!
2025-9-27 14:11:40-debug: run package(web-mobile) handler(enable) success!
2025-9-27 14:11:40-debug: run package(wechatgame) handler(enable) start
2025-9-27 14:11:40-debug: run package(wechatgame) handler(enable) success!
2025-9-27 14:11:40-debug: run package(web-mobile) handler(enable) start
2025-9-27 14:11:40-debug: run package(wechatprogram) handler(enable) start
2025-9-27 14:11:40-debug: run package(windows) handler(enable) start
2025-9-27 14:11:40-debug: run package(wechatprogram) handler(enable) success!
2025-9-27 14:11:40-debug: run package(xiaomi-quick-game) handler(enable) start
2025-9-27 14:11:40-debug: run package(xiaomi-quick-game) handler(enable) success!
2025-9-27 14:11:40-debug: run package(windows) handler(enable) success!
2025-9-27 14:11:40-debug: run package(cocos-service) handler(enable) start
2025-9-27 14:11:40-debug: run package(cocos-service) handler(enable) success!
2025-9-27 14:11:40-debug: run package(im-plugin) handler(enable) start
2025-9-27 14:11:40-debug: run package(im-plugin) handler(enable) success!
2025-9-27 14:11:40-debug: run package(fix-asset-default-userdata) handler(enable) success!
2025-9-27 14:11:40-debug: run package(fix-asset-default-userdata) handler(enable) start
2025-9-27 14:11:40-debug: run package(emitter-editor) handler(enable) start
2025-9-27 14:11:40-debug: run package(emitter-editor) handler(enable) success!
2025-9-27 14:11:40-debug: asset-db:worker-init: initPlugin (1033ms)
2025-9-27 14:11:40-debug: start refresh asset from db://assets/editor/enum-gen/EmitterEnum.ts...
2025-9-27 14:11:40-debug: refresh asset db://assets/editor/enum-gen success
2025-9-27 14:11:40-debug: start refresh asset from db://assets/editor/enum-gen/EnemyEnum.ts...
2025-9-27 14:11:40-debug: refresh asset db://assets/editor/enum-gen success
2025-9-27 14:11:40-debug: [Assets Memory track]: asset-db:worker-init start:30.88MB, end 225.42MB, increase: 194.55MB
2025-9-27 14:11:40-debug: Run asset db hook programming:beforePreStart ...
2025-9-27 14:11:40-debug: Run asset db hook engine-extends:beforePreStart ...
2025-9-27 14:11:40-debug: Run asset db hook engine-extends:beforePreStart success!
2025-9-27 14:11:40-debug: Run asset db hook programming:beforePreStart success!
2025-9-27 14:11:40-debug: run package(i18n) handler(enable) start
2025-9-27 14:11:40-debug: run package(i18n) handler(enable) success!
2025-9-27 14:11:40-debug: start asset-db(i18n)...
2025-9-27 14:11:40-debug: run package(level-editor) handler(enable) start
2025-9-27 14:11:40-debug: start custom db i18n...
2025-9-27 14:11:40-debug: run package(level-editor) handler(enable) success!
2025-9-27 14:11:40-debug: [Assets Memory track]: asset-db:worker-startup-database[i18n] start:226.38MB, end 231.62MB, increase: 5.24MB
2025-9-27 14:11:40-debug: Preimport db internal success
2025-9-27 14:11:41-debug: run package(localization-editor) handler(enable) start
2025-9-27 14:11:41-debug: run package(localization-editor) handler(enable) success!
2025-9-27 14:11:41-debug: asset-db:worker-init (1714ms)
2025-9-27 14:11:41-debug: asset-db-hook-programming-beforePreStart (189ms)
2025-9-27 14:11:41-debug: asset-db-hook-engine-extends-beforePreStart (189ms)
2025-9-27 14:11:41-debug: asset-db:worker-startup-database[i18n] (68ms)
2025-9-27 14:11:41-debug: run package(placeholder) handler(enable) start
2025-9-27 14:11:41-debug: run package(placeholder) handler(enable) success!
2025-9-27 14:11:41-debug: %cImport%c: E:\M2Game\Client\assets\editor\enum-gen\EmitterEnum.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 14:11:41-debug: %cImport%c: E:\M2Game\Client\assets\editor\enum-gen\EnemyEnum.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 14:11:41-debug: Preimport db assets success
2025-9-27 14:11:41-debug: Run asset db hook programming:afterPreStart ...
2025-9-27 14:11:41-debug: starting packer-driver...
2025-9-27 14:11:50-debug: initialize scripting environment...
2025-9-27 14:11:50-debug: [[Executor]] prepare before lock
2025-9-27 14:11:50-debug: Set detail map pack:///resolution-detail-map.json: {}
2025-9-27 14:11:50-debug: [[Executor]] prepare after unlock
2025-9-27 14:11:50-debug: Run asset db hook engine-extends:afterPreStart success!
2025-9-27 14:11:50-debug: Run asset db hook programming:afterPreStart success!
2025-9-27 14:11:50-debug: Run asset db hook engine-extends:afterPreStart ...
2025-9-27 14:11:50-debug: [Assets Memory track]: asset-db:worker-init: preStart start:225.44MB, end 233.79MB, increase: 8.35MB
2025-9-27 14:11:50-debug: Start up the 'internal' database...
2025-9-27 14:11:51-debug: asset-db:worker-effect-data-processing (402ms)
2025-9-27 14:11:51-debug: asset-db-hook-programming-afterPreStart (10258ms)
2025-9-27 14:11:51-debug: asset-db-hook-engine-extends-afterPreStart (402ms)
2025-9-27 14:11:51-debug: Start up the 'assets' database...
2025-9-27 14:11:51-debug: asset-db:worker-startup-database[internal] (11018ms)
2025-9-27 14:11:51-debug: lazy register asset handler *
2025-9-27 14:11:51-debug: lazy register asset handler directory
2025-9-27 14:11:51-debug: lazy register asset handler json
2025-9-27 14:11:51-debug: lazy register asset handler spine-data
2025-9-27 14:11:51-debug: lazy register asset handler text
2025-9-27 14:11:51-debug: lazy register asset handler dragonbones
2025-9-27 14:11:51-debug: lazy register asset handler dragonbones-atlas
2025-9-27 14:11:51-debug: lazy register asset handler terrain
2025-9-27 14:11:51-debug: lazy register asset handler javascript
2025-9-27 14:11:51-debug: lazy register asset handler prefab
2025-9-27 14:11:51-debug: lazy register asset handler scene
2025-9-27 14:11:51-debug: lazy register asset handler sprite-frame
2025-9-27 14:11:51-debug: lazy register asset handler image
2025-9-27 14:11:51-debug: lazy register asset handler tiled-map
2025-9-27 14:11:51-debug: lazy register asset handler buffer
2025-9-27 14:11:51-debug: lazy register asset handler sign-image
2025-9-27 14:11:51-debug: lazy register asset handler alpha-image
2025-9-27 14:11:51-debug: lazy register asset handler texture-cube
2025-9-27 14:11:51-debug: lazy register asset handler texture
2025-9-27 14:11:51-debug: lazy register asset handler render-texture
2025-9-27 14:11:51-debug: lazy register asset handler erp-texture-cube
2025-9-27 14:11:51-debug: lazy register asset handler typescript
2025-9-27 14:11:51-debug: lazy register asset handler rt-sprite-frame
2025-9-27 14:11:51-debug: lazy register asset handler texture-cube-face
2025-9-27 14:11:51-debug: lazy register asset handler gltf-mesh
2025-9-27 14:11:51-debug: lazy register asset handler gltf
2025-9-27 14:11:51-debug: lazy register asset handler gltf-material
2025-9-27 14:11:51-debug: lazy register asset handler gltf-skeleton
2025-9-27 14:11:51-debug: lazy register asset handler gltf-animation
2025-9-27 14:11:51-debug: lazy register asset handler gltf-embeded-image
2025-9-27 14:11:51-debug: lazy register asset handler fbx
2025-9-27 14:11:51-debug: lazy register asset handler gltf-scene
2025-9-27 14:11:51-debug: lazy register asset handler physics-material
2025-9-27 14:11:51-debug: lazy register asset handler material
2025-9-27 14:11:51-debug: lazy register asset handler effect-header
2025-9-27 14:11:51-debug: lazy register asset handler effect
2025-9-27 14:11:51-debug: lazy register asset handler animation-clip
2025-9-27 14:11:51-debug: lazy register asset handler audio-clip
2025-9-27 14:11:51-debug: lazy register asset handler animation-graph-variant
2025-9-27 14:11:51-debug: lazy register asset handler animation-graph
2025-9-27 14:11:51-debug: lazy register asset handler animation-mask
2025-9-27 14:11:51-debug: lazy register asset handler ttf-font
2025-9-27 14:11:51-debug: lazy register asset handler particle
2025-9-27 14:11:51-debug: lazy register asset handler bitmap-font
2025-9-27 14:11:51-debug: lazy register asset handler sprite-atlas
2025-9-27 14:11:51-debug: lazy register asset handler label-atlas
2025-9-27 14:11:51-debug: lazy register asset handler auto-atlas
2025-9-27 14:11:51-debug: lazy register asset handler render-flow
2025-9-27 14:11:51-debug: lazy register asset handler render-stage
2025-9-27 14:11:51-debug: lazy register asset handler instantiation-material
2025-9-27 14:11:51-debug: lazy register asset handler instantiation-mesh
2025-9-27 14:11:51-debug: lazy register asset handler instantiation-animation
2025-9-27 14:11:51-debug: lazy register asset handler render-pipeline
2025-9-27 14:11:51-debug: lazy register asset handler instantiation-skeleton
2025-9-27 14:11:51-debug: lazy register asset handler video-clip
2025-9-27 14:11:51-debug: asset-db:worker-startup-database[assets] (11085ms)
2025-9-27 14:11:51-debug: asset-db:ready (14608ms)
2025-9-27 14:11:51-debug: asset-db:start-database (11265ms)
2025-9-27 14:11:51-debug: fix the bug of updateDefaultUserData
2025-9-27 14:11:51-debug: init worker message success
2025-9-27 14:11:51-debug: programming:execute-script (8ms)
2025-9-27 14:11:52-debug: [Build Memory track]: builder:worker-init start:193.53MB, end 206.17MB, increase: 12.65MB
2025-9-27 14:11:52-debug: builder:worker-init (431ms)
2025-9-27 14:11:54-debug: refresh db internal success
2025-9-27 14:11:54-debug: refresh db i18n success
2025-9-27 14:11:54-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 14:11:54-debug: refresh db assets success
2025-9-27 14:11:54-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 14:11:54-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 14:11:54-debug: asset-db:refresh-all-database (151ms)
2025-9-27 14:11:54-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 14:32:44-debug: refresh db internal success
2025-9-27 14:32:44-debug: refresh db i18n success
2025-9-27 14:32:44-debug: refresh db assets success
2025-9-27 14:32:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 14:32:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 14:32:44-debug: asset-db:refresh-all-database (190ms)
2025-9-27 14:32:44-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-27 14:32:44-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-27 14:51:54-debug: refresh db internal success
2025-9-27 14:51:54-debug: refresh db i18n success
2025-9-27 14:51:54-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 14:51:54-debug: refresh db assets success
2025-9-27 14:51:54-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 14:51:54-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 14:51:54-debug: asset-db:refresh-all-database (170ms)
2025-9-27 14:51:54-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 14:51:54-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 15:02:44-debug: refresh db internal success
2025-9-27 15:02:44-debug: refresh db i18n success
2025-9-27 15:02:44-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:02:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:02:44-debug: refresh db assets success
2025-9-27 15:02:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:02:44-debug: asset-db:refresh-all-database (122ms)
2025-9-27 15:03:50-debug: refresh db internal success
2025-9-27 15:03:50-debug: refresh db i18n success
2025-9-27 15:03:50-debug: refresh db assets success
2025-9-27 15:03:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:03:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:03:50-debug: asset-db:refresh-all-database (136ms)
2025-9-27 15:03:50-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 15:03:50-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 15:03:53-debug: refresh db internal success
2025-9-27 15:03:53-debug: refresh db i18n success
2025-9-27 15:03:53-debug: refresh db assets success
2025-9-27 15:03:53-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:03:53-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:03:53-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 15:03:53-debug: asset-db:refresh-all-database (118ms)
2025-9-27 15:03:53-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-27 15:05:14-debug: refresh db internal success
2025-9-27 15:05:14-debug: refresh db i18n success
2025-9-27 15:05:14-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:05:14-debug: refresh db assets success
2025-9-27 15:05:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:05:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:05:14-debug: asset-db:refresh-all-database (126ms)
2025-9-27 15:05:14-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 15:05:14-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 15:06:24-debug: refresh db internal success
2025-9-27 15:06:24-debug: refresh db i18n success
2025-9-27 15:06:24-debug: refresh db assets success
2025-9-27 15:06:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:06:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:06:24-debug: asset-db:refresh-all-database (125ms)
2025-9-27 15:06:24-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 15:06:24-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 15:06:39-debug: refresh db internal success
2025-9-27 15:06:39-debug: refresh db i18n success
2025-9-27 15:06:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:06:39-debug: refresh db assets success
2025-9-27 15:06:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:06:39-debug: asset-db:refresh-all-database (121ms)
2025-9-27 15:06:49-debug: refresh db internal success
2025-9-27 15:06:49-debug: refresh db i18n success
2025-9-27 15:06:49-debug: refresh db assets success
2025-9-27 15:06:49-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:06:49-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:06:49-debug: asset-db:refresh-all-database (118ms)
2025-9-27 15:10:35-debug: refresh db internal success
2025-9-27 15:10:35-debug: refresh db i18n success
2025-9-27 15:10:35-debug: refresh db assets success
2025-9-27 15:10:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:10:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:10:35-debug: asset-db:refresh-all-database (137ms)
2025-9-27 15:10:41-debug: refresh db internal success
2025-9-27 15:10:41-debug: refresh db i18n success
2025-9-27 15:10:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:10:42-debug: refresh db assets success
2025-9-27 15:10:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:10:42-debug: asset-db:refresh-all-database (136ms)
2025-9-27 15:14:32-debug: refresh db internal success
2025-9-27 15:14:32-debug: refresh db i18n success
2025-9-27 15:14:32-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:14:32-debug: refresh db assets success
2025-9-27 15:14:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:14:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:14:32-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 15:14:32-debug: asset-db:refresh-all-database (143ms)
2025-9-27 15:14:32-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 15:16:19-debug: refresh db internal success
2025-9-27 15:16:19-debug: refresh db i18n success
2025-9-27 15:16:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:16:19-debug: refresh db assets success
2025-9-27 15:16:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:16:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:16:19-debug: asset-db:refresh-all-database (149ms)
2025-9-27 15:16:31-debug: refresh db internal success
2025-9-27 15:16:31-debug: refresh db i18n success
2025-9-27 15:16:32-debug: refresh db assets success
2025-9-27 15:16:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:16:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:16:32-debug: asset-db:refresh-all-database (149ms)
2025-9-27 15:16:40-debug: refresh db internal success
2025-9-27 15:16:40-debug: refresh db i18n success
2025-9-27 15:16:40-debug: refresh db assets success
2025-9-27 15:16:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:16:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:16:40-debug: asset-db:refresh-all-database (140ms)
2025-9-27 15:16:54-debug: refresh db internal success
2025-9-27 15:16:54-debug: refresh db i18n success
2025-9-27 15:16:54-debug: refresh db assets success
2025-9-27 15:16:54-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:16:54-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:16:54-debug: asset-db:refresh-all-database (111ms)
2025-9-27 15:23:14-debug: refresh db internal success
2025-9-27 15:23:14-debug: refresh db i18n success
2025-9-27 15:23:14-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:23:14-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:23:14-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\docs\PathSystemRefactor.md
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:23:14-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\test\PathSystemTest.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:23:14-debug: refresh db assets success
2025-9-27 15:23:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:23:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:23:14-debug: asset-db:refresh-all-database (168ms)
2025-9-27 15:23:14-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 15:23:14-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-27 15:23:58-debug: refresh db internal success
2025-9-27 15:23:58-debug: refresh db i18n success
2025-9-27 15:23:58-debug: refresh db assets success
2025-9-27 15:23:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:23:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:23:58-debug: asset-db:refresh-all-database (158ms)
2025-9-27 15:23:58-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-27 15:23:58-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-27 15:28:15-debug: refresh db internal success
2025-9-27 15:28:15-debug: refresh db i18n success
2025-9-27 15:28:15-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\test\PathCurveTest.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:28:15-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\test
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:28:15-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:28:15-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\test\PathSystemTest.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:28:15-debug: refresh db assets success
2025-9-27 15:28:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:28:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:28:15-debug: asset-db:refresh-all-database (177ms)
2025-9-27 15:28:15-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 15:28:15-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 15:28:23-debug: refresh db internal success
2025-9-27 15:28:23-debug: refresh db i18n success
2025-9-27 15:28:23-debug: refresh db assets success
2025-9-27 15:28:23-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:28:23-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:28:23-debug: asset-db:refresh-all-database (125ms)
2025-9-27 15:28:23-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 15:28:23-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 15:29:07-debug: refresh db internal success
2025-9-27 15:29:07-debug: refresh db i18n success
2025-9-27 15:29:07-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:29:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:29:07-debug: refresh db assets success
2025-9-27 15:29:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:29:07-debug: asset-db:refresh-all-database (152ms)
2025-9-27 15:35:42-debug: refresh db internal success
2025-9-27 15:35:42-debug: refresh db i18n success
2025-9-27 15:35:42-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:35:42-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\docs\PathSystemRefactor.md
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:35:42-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\test\PathSystemTest.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:35:42-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\test\PathCurveTest.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:35:42-debug: refresh db assets success
2025-9-27 15:35:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:35:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:35:42-debug: asset-db:refresh-all-database (162ms)
2025-9-27 15:35:42-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 15:35:42-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 15:38:44-debug: refresh db internal success
2025-9-27 15:38:44-debug: refresh db i18n success
2025-9-27 15:38:44-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:38:44-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\test\PathSystemTest.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:38:44-debug: refresh db assets success
2025-9-27 15:38:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:38:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:38:44-debug: asset-db:refresh-all-database (152ms)
2025-9-27 15:38:44-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 15:39:05-debug: refresh db internal success
2025-9-27 15:39:05-debug: refresh db i18n success
2025-9-27 15:39:05-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:39:05-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\test\PathSystemTest.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:39:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:39:05-debug: refresh db assets success
2025-9-27 15:39:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:39:05-debug: asset-db:refresh-all-database (121ms)
2025-9-27 15:39:05-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 15:39:05-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 15:43:39-debug: refresh db internal success
2025-9-27 15:43:39-debug: refresh db i18n success
2025-9-27 15:43:39-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:43:39-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\docs\PathSystemRefactor.md
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:43:39-debug: refresh db assets success
2025-9-27 15:43:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:43:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:43:39-debug: asset-db:refresh-all-database (155ms)
2025-9-27 15:48:53-debug: refresh db internal success
2025-9-27 15:48:53-debug: refresh db i18n success
2025-9-27 15:48:53-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\test\PathMoveOptimizationTest.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:48:53-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:48:53-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\test
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:48:53-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:48:53-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\docs\PathSystemRefactor.md
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:48:53-debug: refresh db assets success
2025-9-27 15:48:53-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:48:53-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:48:53-debug: asset-db:refresh-all-database (176ms)
2025-9-27 15:48:53-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 15:49:26-debug: refresh db internal success
2025-9-27 15:49:26-debug: refresh db i18n success
2025-9-27 15:49:26-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:49:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:49:26-debug: refresh db assets success
2025-9-27 15:49:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:49:26-debug: asset-db:refresh-all-database (153ms)
2025-9-27 15:51:00-debug: refresh db internal success
2025-9-27 15:51:00-debug: refresh db i18n success
2025-9-27 15:51:00-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\docs\PathEditorSegmentVisualization.md
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:51:00-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:51:00-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\docs
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:51:00-debug: refresh db assets success
2025-9-27 15:51:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:51:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:51:00-debug: asset-db:refresh-all-database (156ms)
2025-9-27 15:51:00-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 15:54:57-debug: refresh db internal success
2025-9-27 15:54:57-debug: refresh db i18n success
2025-9-27 15:54:57-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:54:57-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\docs\PathEditorSegmentVisualization.md
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:54:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:54:57-debug: refresh db assets success
2025-9-27 15:54:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:54:57-debug: asset-db:refresh-all-database (156ms)
2025-9-27 15:54:57-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-27 15:54:57-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-27 15:56:20-debug: refresh db internal success
2025-9-27 15:56:20-debug: refresh db i18n success
2025-9-27 15:56:20-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:56:20-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 15:56:20-debug: refresh db assets success
2025-9-27 15:56:20-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:56:20-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:56:20-debug: asset-db:refresh-all-database (175ms)
2025-9-27 15:56:20-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 15:56:20-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 15:56:23-debug: refresh db internal success
2025-9-27 15:56:23-debug: refresh db i18n success
2025-9-27 15:56:23-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:56:23-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:56:23-debug: refresh db assets success
2025-9-27 15:56:23-debug: asset-db:refresh-all-database (134ms)
2025-9-27 15:56:27-debug: refresh db internal success
2025-9-27 15:56:27-debug: refresh db i18n success
2025-9-27 15:56:27-debug: refresh db assets success
2025-9-27 15:56:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 15:56:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 15:56:27-debug: asset-db:refresh-all-database (110ms)
2025-9-27 16:08:52-debug: refresh db internal success
2025-9-27 16:08:52-debug: refresh db i18n success
2025-9-27 16:08:52-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:08:52-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:08:52-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\IMovable.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:08:52-debug: refresh db assets success
2025-9-27 16:08:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:08:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:08:52-debug: asset-db:refresh-all-database (120ms)
2025-9-27 16:09:00-debug: refresh db internal success
2025-9-27 16:09:00-debug: refresh db i18n success
2025-9-27 16:09:00-debug: refresh db assets success
2025-9-27 16:09:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:09:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:09:00-debug: asset-db:refresh-all-database (114ms)
2025-9-27 16:09:03-debug: refresh db internal success
2025-9-27 16:09:03-debug: refresh db i18n success
2025-9-27 16:09:03-debug: refresh db assets success
2025-9-27 16:09:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:09:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:09:03-debug: asset-db:refresh-all-database (111ms)
2025-9-27 16:10:08-debug: refresh db internal success
2025-9-27 16:10:08-debug: refresh db i18n success
2025-9-27 16:10:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:10:08-debug: refresh db assets success
2025-9-27 16:10:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:10:08-debug: asset-db:refresh-all-database (139ms)
2025-9-27 16:10:08-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:11:12-debug: refresh db internal success
2025-9-27 16:11:12-debug: refresh db i18n success
2025-9-27 16:11:12-debug: refresh db assets success
2025-9-27 16:11:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:11:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:11:12-debug: asset-db:refresh-all-database (112ms)
2025-9-27 16:11:12-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:11:12-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:11:22-debug: refresh db internal success
2025-9-27 16:11:22-debug: refresh db i18n success
2025-9-27 16:11:22-debug: refresh db assets success
2025-9-27 16:11:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:11:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:11:22-debug: asset-db:refresh-all-database (112ms)
2025-9-27 16:11:22-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:11:22-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:11:38-debug: refresh db internal success
2025-9-27 16:11:38-debug: refresh db i18n success
2025-9-27 16:11:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:11:38-debug: refresh db assets success
2025-9-27 16:11:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:11:38-debug: asset-db:refresh-all-database (131ms)
2025-9-27 16:11:50-debug: refresh db internal success
2025-9-27 16:11:50-debug: refresh db i18n success
2025-9-27 16:11:50-debug: refresh db assets success
2025-9-27 16:11:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:11:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:11:50-debug: asset-db:refresh-all-database (144ms)
2025-9-27 16:11:50-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:11:50-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:13:22-debug: refresh db internal success
2025-9-27 16:13:22-debug: refresh db i18n success
2025-9-27 16:13:22-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\preview\WavePreview.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:13:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:13:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:13:22-debug: refresh db assets success
2025-9-27 16:13:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:13:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:13:22-debug: asset-db:refresh-all-database (154ms)
2025-9-27 16:13:29-debug: refresh db internal success
2025-9-27 16:13:29-debug: refresh db i18n success
2025-9-27 16:13:29-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:13:29-debug: refresh db assets success
2025-9-27 16:13:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:13:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:13:29-debug: asset-db:refresh-all-database (138ms)
2025-9-27 16:13:50-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\wave_1.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:13:50-debug: asset-db:reimport-assetd5f5e836-9495-4c08-890f-d92cc6696838 (2ms)
2025-9-27 16:14:43-debug: refresh db internal success
2025-9-27 16:14:43-debug: refresh db i18n success
2025-9-27 16:14:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:14:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:14:43-debug: refresh db assets success
2025-9-27 16:14:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:14:43-debug: asset-db:refresh-all-database (149ms)
2025-9-27 16:14:43-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:15:15-debug: refresh db internal success
2025-9-27 16:15:15-debug: refresh db i18n success
2025-9-27 16:15:15-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:15:15-debug: refresh db assets success
2025-9-27 16:15:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:15:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:15:15-debug: asset-db:refresh-all-database (146ms)
2025-9-27 16:15:15-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:15:15-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:15:19-debug: refresh db internal success
2025-9-27 16:15:19-debug: refresh db i18n success
2025-9-27 16:15:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:15:19-debug: refresh db assets success
2025-9-27 16:15:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:15:19-debug: asset-db:refresh-all-database (140ms)
2025-9-27 16:15:21-debug: refresh db internal success
2025-9-27 16:15:21-debug: refresh db i18n success
2025-9-27 16:15:21-debug: refresh db assets success
2025-9-27 16:15:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:15:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:15:21-debug: asset-db:refresh-all-database (112ms)
2025-9-27 16:15:23-debug: refresh db internal success
2025-9-27 16:15:23-debug: refresh db i18n success
2025-9-27 16:15:23-debug: refresh db assets success
2025-9-27 16:15:23-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:15:23-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:15:23-debug: asset-db:refresh-all-database (114ms)
2025-9-27 16:15:23-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:15:26-debug: refresh db internal success
2025-9-27 16:15:26-debug: refresh db i18n success
2025-9-27 16:15:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:15:26-debug: refresh db assets success
2025-9-27 16:15:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:15:26-debug: asset-db:refresh-all-database (112ms)
2025-9-27 16:15:45-debug: refresh db internal success
2025-9-27 16:15:45-debug: refresh db i18n success
2025-9-27 16:15:45-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:15:45-debug: refresh db assets success
2025-9-27 16:15:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:15:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:15:45-debug: asset-db:refresh-all-database (138ms)
2025-9-27 16:15:45-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:15:54-debug: refresh db internal success
2025-9-27 16:15:54-debug: refresh db i18n success
2025-9-27 16:15:55-debug: refresh db assets success
2025-9-27 16:15:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:15:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:15:55-debug: asset-db:refresh-all-database (118ms)
2025-9-27 16:15:57-debug: refresh db internal success
2025-9-27 16:15:57-debug: refresh db i18n success
2025-9-27 16:15:57-debug: refresh db assets success
2025-9-27 16:15:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:15:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:15:57-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:15:57-debug: asset-db:refresh-all-database (115ms)
2025-9-27 16:15:57-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:20:34-debug: refresh db internal success
2025-9-27 16:20:34-debug: refresh db i18n success
2025-9-27 16:20:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:20:34-debug: refresh db assets success
2025-9-27 16:20:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:20:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:20:34-debug: asset-db:refresh-all-database (149ms)
2025-9-27 16:20:34-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:20:54-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\wave_1.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:20:54-debug: asset-db:reimport-assetd5f5e836-9495-4c08-890f-d92cc6696838 (2ms)
2025-9-27 16:23:14-debug: refresh db internal success
2025-9-27 16:23:14-debug: refresh db i18n success
2025-9-27 16:23:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:23:14-debug: refresh db assets success
2025-9-27 16:23:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:23:14-debug: asset-db:refresh-all-database (157ms)
2025-9-27 16:23:14-debug: asset-db:worker-effect-data-processing (3ms)
2025-9-27 16:23:14-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-9-27 16:24:44-debug: refresh db internal success
2025-9-27 16:24:44-debug: refresh db i18n success
2025-9-27 16:24:44-debug: refresh db assets success
2025-9-27 16:24:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:24:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:24:44-debug: asset-db:refresh-all-database (117ms)
2025-9-27 16:24:44-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:24:44-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:32:09-debug: refresh db internal success
2025-9-27 16:32:09-debug: refresh db i18n success
2025-9-27 16:32:09-debug: refresh db assets success
2025-9-27 16:32:09-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:32:09-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:32:09-debug: asset-db:refresh-all-database (144ms)
2025-9-27 16:32:09-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:32:29-debug: refresh db internal success
2025-9-27 16:32:29-debug: refresh db i18n success
2025-9-27 16:32:29-debug: refresh db assets success
2025-9-27 16:32:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:32:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:32:29-debug: asset-db:refresh-all-database (138ms)
2025-9-27 16:32:29-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:32:29-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:32:34-debug: refresh db internal success
2025-9-27 16:32:34-debug: refresh db i18n success
2025-9-27 16:32:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:32:34-debug: refresh db assets success
2025-9-27 16:32:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:32:34-debug: asset-db:refresh-all-database (116ms)
2025-9-27 16:33:21-debug: refresh db internal success
2025-9-27 16:33:21-debug: refresh db i18n success
2025-9-27 16:33:21-debug: refresh db assets success
2025-9-27 16:33:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:33:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:33:21-debug: asset-db:refresh-all-database (135ms)
2025-9-27 16:33:38-debug: refresh db internal success
2025-9-27 16:33:38-debug: refresh db i18n success
2025-9-27 16:33:39-debug: refresh db assets success
2025-9-27 16:33:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:33:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:33:39-debug: asset-db:refresh-all-database (151ms)
2025-9-27 16:33:42-debug: refresh db internal success
2025-9-27 16:33:42-debug: refresh db i18n success
2025-9-27 16:33:42-debug: refresh db assets success
2025-9-27 16:33:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:33:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:33:42-debug: asset-db:refresh-all-database (141ms)
2025-9-27 16:33:42-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:33:42-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:33:58-debug: refresh db internal success
2025-9-27 16:33:58-debug: refresh db i18n success
2025-9-27 16:33:58-debug: refresh db assets success
2025-9-27 16:33:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:33:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:33:58-debug: asset-db:refresh-all-database (120ms)
2025-9-27 16:33:58-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:33:58-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:39:22-debug: refresh db internal success
2025-9-27 16:39:22-debug: refresh db i18n success
2025-9-27 16:39:22-debug: refresh db assets success
2025-9-27 16:39:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:39:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:39:22-debug: asset-db:refresh-all-database (143ms)
2025-9-27 16:39:28-debug: refresh db internal success
2025-9-27 16:39:28-debug: refresh db i18n success
2025-9-27 16:39:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:39:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:39:29-debug: refresh db assets success
2025-9-27 16:39:29-debug: asset-db:refresh-all-database (110ms)
2025-9-27 16:41:35-debug: refresh db internal success
2025-9-27 16:41:35-debug: refresh db i18n success
2025-9-27 16:41:35-debug: refresh db assets success
2025-9-27 16:41:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:41:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:41:35-debug: asset-db:refresh-all-database (142ms)
2025-9-27 16:41:35-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:41:35-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:41:40-debug: refresh db internal success
2025-9-27 16:41:40-debug: refresh db i18n success
2025-9-27 16:41:40-debug: refresh db assets success
2025-9-27 16:41:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:41:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:41:40-debug: asset-db:refresh-all-database (108ms)
2025-9-27 16:41:40-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:41:40-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:42:15-debug: refresh db internal success
2025-9-27 16:42:15-debug: refresh db i18n success
2025-9-27 16:42:15-debug: refresh db assets success
2025-9-27 16:42:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:42:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:42:15-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:42:15-debug: asset-db:refresh-all-database (157ms)
2025-9-27 16:42:15-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:42:18-debug: refresh db internal success
2025-9-27 16:42:18-debug: refresh db i18n success
2025-9-27 16:42:19-debug: refresh db assets success
2025-9-27 16:42:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:42:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:42:19-debug: asset-db:refresh-all-database (146ms)
2025-9-27 16:42:19-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:42:19-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:44:30-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\formation\f_01.json
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:44:30-debug: asset-db:reimport-asset30ba4125-5f81-4175-bc13-1a965f6299a3 (3ms)
2025-9-27 16:45:52-debug: refresh db internal success
2025-9-27 16:45:52-debug: refresh db i18n success
2025-9-27 16:45:52-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\FormationPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:45:52-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:45:52-debug: refresh db assets success
2025-9-27 16:45:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:45:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:45:52-debug: asset-db:refresh-all-database (155ms)
2025-9-27 16:45:52-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:45:52-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:45:57-debug: refresh db internal success
2025-9-27 16:45:57-debug: refresh db i18n success
2025-9-27 16:45:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:45:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:45:57-debug: refresh db assets success
2025-9-27 16:45:57-debug: asset-db:refresh-all-database (121ms)
2025-9-27 16:45:57-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:45:57-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:45:59-debug: refresh db internal success
2025-9-27 16:45:59-debug: refresh db i18n success
2025-9-27 16:45:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:45:59-debug: refresh db assets success
2025-9-27 16:45:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:45:59-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:45:59-debug: asset-db:refresh-all-database (112ms)
2025-9-27 16:45:59-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:46:11-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\formation\f_01.json
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:46:11-debug: asset-db:reimport-asset30ba4125-5f81-4175-bc13-1a965f6299a3 (2ms)
2025-9-27 16:46:22-debug: refresh db internal success
2025-9-27 16:46:22-debug: refresh db i18n success
2025-9-27 16:46:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:46:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:46:22-debug: refresh db assets success
2025-9-27 16:46:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:46:22-debug: asset-db:refresh-all-database (145ms)
2025-9-27 16:46:26-debug: refresh db internal success
2025-9-27 16:46:26-debug: refresh db i18n success
2025-9-27 16:46:26-debug: refresh db assets success
2025-9-27 16:46:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:46:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:46:26-debug: asset-db:refresh-all-database (145ms)
2025-9-27 16:46:30-debug: refresh db internal success
2025-9-27 16:46:30-debug: refresh db i18n success
2025-9-27 16:46:30-debug: refresh db assets success
2025-9-27 16:46:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:46:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:46:30-debug: asset-db:refresh-all-database (124ms)
2025-9-27 16:46:30-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:46:30-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\formation\f_01.json
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:46:30-debug: asset-db:reimport-asset30ba4125-5f81-4175-bc13-1a965f6299a3 (2ms)
2025-9-27 16:47:19-debug: refresh db internal success
2025-9-27 16:47:19-debug: refresh db i18n success
2025-9-27 16:47:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:47:19-debug: refresh db assets success
2025-9-27 16:47:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:47:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:47:19-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:47:19-debug: asset-db:refresh-all-database (143ms)
2025-9-27 16:47:19-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:47:32-debug: refresh db internal success
2025-9-27 16:47:32-debug: refresh db i18n success
2025-9-27 16:47:32-debug: refresh db assets success
2025-9-27 16:47:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:47:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:47:32-debug: asset-db:refresh-all-database (116ms)
2025-9-27 16:47:32-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:47:32-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:47:33-debug: refresh db internal success
2025-9-27 16:47:33-debug: refresh db i18n success
2025-9-27 16:47:33-debug: refresh db assets success
2025-9-27 16:47:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:47:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:47:33-debug: asset-db:refresh-all-database (110ms)
2025-9-27 16:47:33-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:47:33-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:47:34-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\formation\f_01.json
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:47:34-debug: asset-db:reimport-asset30ba4125-5f81-4175-bc13-1a965f6299a3 (2ms)
2025-9-27 16:47:57-debug: refresh db internal success
2025-9-27 16:47:57-debug: refresh db i18n success
2025-9-27 16:47:57-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:47:57-debug: refresh db assets success
2025-9-27 16:47:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:47:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:47:57-debug: asset-db:refresh-all-database (156ms)
2025-9-27 16:47:57-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:47:57-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:48:01-debug: refresh db internal success
2025-9-27 16:48:01-debug: refresh db i18n success
2025-9-27 16:48:02-debug: refresh db assets success
2025-9-27 16:48:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:48:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:48:02-debug: asset-db:refresh-all-database (138ms)
2025-9-27 16:48:02-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:48:02-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:48:03-debug: refresh db internal success
2025-9-27 16:48:03-debug: refresh db i18n success
2025-9-27 16:48:03-debug: refresh db assets success
2025-9-27 16:48:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:48:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:48:03-debug: asset-db:refresh-all-database (109ms)
2025-9-27 16:48:03-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:48:03-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:48:24-debug: refresh db internal success
2025-9-27 16:48:24-debug: refresh db i18n success
2025-9-27 16:48:24-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:48:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:48:24-debug: refresh db assets success
2025-9-27 16:48:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:48:24-debug: asset-db:refresh-all-database (138ms)
2025-9-27 16:48:28-debug: refresh db internal success
2025-9-27 16:48:28-debug: refresh db i18n success
2025-9-27 16:48:29-debug: refresh db assets success
2025-9-27 16:48:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:48:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:48:29-debug: asset-db:refresh-all-database (209ms)
2025-9-27 16:48:29-debug: asset-db:worker-effect-data-processing (32ms)
2025-9-27 16:48:29-debug: asset-db-hook-engine-extends-afterRefresh (33ms)
2025-9-27 16:48:29-debug: refresh db internal success
2025-9-27 16:48:29-debug: refresh db i18n success
2025-9-27 16:48:29-debug: refresh db assets success
2025-9-27 16:48:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:48:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:48:29-debug: asset-db:refresh-all-database (136ms)
2025-9-27 16:48:29-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:48:29-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-27 16:48:32-debug: refresh db internal success
2025-9-27 16:48:32-debug: refresh db i18n success
2025-9-27 16:48:32-debug: refresh db assets success
2025-9-27 16:48:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:48:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:48:32-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:48:32-debug: asset-db:refresh-all-database (110ms)
2025-9-27 16:48:32-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:48:46-debug: refresh db internal success
2025-9-27 16:48:46-debug: refresh db i18n success
2025-9-27 16:48:46-debug: refresh db assets success
2025-9-27 16:48:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:48:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:48:46-debug: asset-db:refresh-all-database (136ms)
2025-9-27 16:48:46-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:49:15-debug: refresh db internal success
2025-9-27 16:49:15-debug: refresh db i18n success
2025-9-27 16:49:15-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:49:15-debug: refresh db assets success
2025-9-27 16:49:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:49:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:49:15-debug: asset-db:refresh-all-database (143ms)
2025-9-27 16:49:15-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:49:15-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:49:20-debug: refresh db internal success
2025-9-27 16:49:20-debug: refresh db i18n success
2025-9-27 16:49:20-debug: refresh db assets success
2025-9-27 16:49:20-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:49:20-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:49:20-debug: asset-db:refresh-all-database (144ms)
2025-9-27 16:49:23-debug: refresh db internal success
2025-9-27 16:49:23-debug: refresh db i18n success
2025-9-27 16:49:23-debug: refresh db assets success
2025-9-27 16:49:23-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:49:23-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:49:23-debug: asset-db:refresh-all-database (109ms)
2025-9-27 16:49:23-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:49:23-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:50:12-debug: refresh db internal success
2025-9-27 16:50:12-debug: refresh db i18n success
2025-9-27 16:50:12-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:50:12-debug: refresh db assets success
2025-9-27 16:50:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:50:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:50:12-debug: asset-db:refresh-all-database (145ms)
2025-9-27 16:50:12-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:50:12-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:50:17-debug: refresh db internal success
2025-9-27 16:50:17-debug: refresh db i18n success
2025-9-27 16:50:17-debug: refresh db assets success
2025-9-27 16:50:17-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:50:17-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:50:17-debug: asset-db:refresh-all-database (111ms)
2025-9-27 16:50:17-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:50:17-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:50:18-debug: refresh db internal success
2025-9-27 16:50:18-debug: refresh db i18n success
2025-9-27 16:50:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:50:18-debug: refresh db assets success
2025-9-27 16:50:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:50:18-debug: asset-db:refresh-all-database (112ms)
2025-9-27 16:51:40-debug: refresh db internal success
2025-9-27 16:51:40-debug: refresh db i18n success
2025-9-27 16:51:40-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:51:40-debug: refresh db assets success
2025-9-27 16:51:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:51:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:51:40-debug: asset-db:refresh-all-database (139ms)
2025-9-27 16:51:47-debug: refresh db internal success
2025-9-27 16:51:47-debug: refresh db i18n success
2025-9-27 16:51:47-debug: refresh db assets success
2025-9-27 16:51:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:51:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:51:47-debug: asset-db:refresh-all-database (209ms)
2025-9-27 16:51:47-debug: asset-db:worker-effect-data-processing (35ms)
2025-9-27 16:51:47-debug: asset-db-hook-engine-extends-afterRefresh (35ms)
2025-9-27 16:51:47-debug: refresh db internal success
2025-9-27 16:51:47-debug: refresh db i18n success
2025-9-27 16:51:47-debug: refresh db assets success
2025-9-27 16:51:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:51:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:51:47-debug: asset-db:refresh-all-database (146ms)
2025-9-27 16:51:48-debug: refresh db internal success
2025-9-27 16:51:48-debug: refresh db i18n success
2025-9-27 16:51:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:51:48-debug: refresh db assets success
2025-9-27 16:51:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:51:48-debug: asset-db:refresh-all-database (137ms)
2025-9-27 16:51:49-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\formation\f_01.json
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:51:49-debug: asset-db:reimport-asset30ba4125-5f81-4175-bc13-1a965f6299a3 (2ms)
2025-9-27 16:52:14-debug: refresh db internal success
2025-9-27 16:52:14-debug: refresh db i18n success
2025-9-27 16:52:14-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:52:14-debug: refresh db assets success
2025-9-27 16:52:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:52:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:52:14-debug: asset-db:refresh-all-database (142ms)
2025-9-27 16:52:20-debug: refresh db internal success
2025-9-27 16:52:20-debug: refresh db i18n success
2025-9-27 16:52:20-debug: refresh db assets success
2025-9-27 16:52:20-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:52:20-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:52:20-debug: asset-db:refresh-all-database (112ms)
2025-9-27 16:52:20-debug: refresh db internal success
2025-9-27 16:52:20-debug: refresh db i18n success
2025-9-27 16:52:20-debug: refresh db assets success
2025-9-27 16:52:20-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:52:20-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:52:20-debug: asset-db:refresh-all-database (111ms)
2025-9-27 16:52:53-debug: refresh db internal success
2025-9-27 16:52:53-debug: refresh db i18n success
2025-9-27 16:52:53-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:52:53-debug: refresh db assets success
2025-9-27 16:52:53-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:52:53-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:52:53-debug: asset-db:refresh-all-database (144ms)
2025-9-27 16:52:57-debug: refresh db internal success
2025-9-27 16:52:57-debug: refresh db i18n success
2025-9-27 16:52:57-debug: refresh db assets success
2025-9-27 16:52:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:52:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:52:57-debug: asset-db:refresh-all-database (145ms)
2025-9-27 16:52:59-debug: refresh db internal success
2025-9-27 16:52:59-debug: refresh db i18n success
2025-9-27 16:52:59-debug: refresh db assets success
2025-9-27 16:52:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:52:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:52:59-debug: asset-db:refresh-all-database (111ms)
2025-9-27 16:52:59-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\formation\f_01.json
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:52:59-debug: asset-db:reimport-asset30ba4125-5f81-4175-bc13-1a965f6299a3 (2ms)
2025-9-27 16:53:06-debug: refresh db internal success
2025-9-27 16:53:06-debug: refresh db i18n success
2025-9-27 16:53:06-debug: refresh db assets success
2025-9-27 16:53:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:53:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:53:06-debug: asset-db:refresh-all-database (112ms)
2025-9-27 16:53:08-debug: refresh db internal success
2025-9-27 16:53:08-debug: refresh db i18n success
2025-9-27 16:53:08-debug: refresh db assets success
2025-9-27 16:53:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:53:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:53:08-debug: asset-db:refresh-all-database (109ms)
2025-9-27 16:53:10-debug: refresh db internal success
2025-9-27 16:53:10-debug: refresh db i18n success
2025-9-27 16:53:10-debug: refresh db assets success
2025-9-27 16:53:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:53:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:53:10-debug: asset-db:refresh-all-database (112ms)
2025-9-27 16:53:10-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:53:12-debug: refresh db internal success
2025-9-27 16:53:12-debug: refresh db i18n success
2025-9-27 16:53:12-debug: refresh db assets success
2025-9-27 16:53:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:53:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:53:12-debug: asset-db:refresh-all-database (112ms)
2025-9-27 16:53:21-debug: refresh db internal success
2025-9-27 16:53:21-debug: refresh db i18n success
2025-9-27 16:53:21-debug: refresh db assets success
2025-9-27 16:53:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:53:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:53:21-debug: asset-db:refresh-all-database (109ms)
2025-9-27 16:53:21-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:53:21-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:53:33-debug: refresh db internal success
2025-9-27 16:53:33-debug: refresh db i18n success
2025-9-27 16:53:33-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:53:33-debug: refresh db assets success
2025-9-27 16:53:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:53:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:53:33-debug: asset-db:refresh-all-database (119ms)
2025-9-27 16:53:38-debug: refresh db internal success
2025-9-27 16:53:38-debug: refresh db i18n success
2025-9-27 16:53:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:53:38-debug: refresh db assets success
2025-9-27 16:53:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:53:38-debug: asset-db:refresh-all-database (132ms)
2025-9-27 16:53:39-debug: refresh db internal success
2025-9-27 16:53:39-debug: refresh db i18n success
2025-9-27 16:53:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:53:39-debug: refresh db assets success
2025-9-27 16:53:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:53:39-debug: asset-db:refresh-all-database (114ms)
2025-9-27 16:53:39-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:53:53-debug: refresh db internal success
2025-9-27 16:53:53-debug: refresh db i18n success
2025-9-27 16:53:53-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:53:53-debug: refresh db assets success
2025-9-27 16:53:53-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:53:53-debug: asset-db:refresh-all-database (118ms)
2025-9-27 16:53:53-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:54:07-debug: refresh db internal success
2025-9-27 16:54:07-debug: refresh db i18n success
2025-9-27 16:54:07-debug: refresh db assets success
2025-9-27 16:54:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:54:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:54:07-debug: asset-db:refresh-all-database (140ms)
2025-9-27 16:54:07-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:54:07-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:54:36-debug: refresh db internal success
2025-9-27 16:54:36-debug: refresh db i18n success
2025-9-27 16:54:36-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:54:36-debug: refresh db assets success
2025-9-27 16:54:36-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:54:36-debug: asset-db:refresh-all-database (150ms)
2025-9-27 16:54:36-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:54:36-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:54:46-debug: refresh db internal success
2025-9-27 16:54:46-debug: refresh db i18n success
2025-9-27 16:54:46-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\FormationPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:54:46-debug: refresh db assets success
2025-9-27 16:54:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:54:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:54:46-debug: asset-db:refresh-all-database (124ms)
2025-9-27 16:54:46-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:54:46-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:54:54-debug: refresh db internal success
2025-9-27 16:54:54-debug: refresh db i18n success
2025-9-27 16:54:54-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\FormationPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:54:55-debug: refresh db assets success
2025-9-27 16:54:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:54:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:54:55-debug: asset-db:refresh-all-database (117ms)
2025-9-27 16:54:55-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-27 16:54:55-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-27 16:55:03-debug: refresh db internal success
2025-9-27 16:55:03-debug: refresh db i18n success
2025-9-27 16:55:03-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\FormationPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:55:03-debug: refresh db assets success
2025-9-27 16:55:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:55:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:55:03-debug: asset-db:refresh-all-database (134ms)
2025-9-27 16:55:03-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:55:03-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:55:06-debug: refresh db internal success
2025-9-27 16:55:06-debug: refresh db i18n success
2025-9-27 16:55:06-debug: refresh db assets success
2025-9-27 16:55:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:55:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:55:06-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:55:06-debug: asset-db:refresh-all-database (117ms)
2025-9-27 16:55:06-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:55:13-debug: refresh db internal success
2025-9-27 16:55:13-debug: refresh db i18n success
2025-9-27 16:55:14-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\FormationPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:55:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:55:14-debug: refresh db assets success
2025-9-27 16:55:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:55:14-debug: asset-db:refresh-all-database (114ms)
2025-9-27 16:55:14-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:55:14-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:55:32-debug: refresh db internal success
2025-9-27 16:55:32-debug: refresh db i18n success
2025-9-27 16:55:32-debug: refresh db assets success
2025-9-27 16:55:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:55:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:55:32-debug: asset-db:refresh-all-database (145ms)
2025-9-27 16:55:32-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:55:32-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-27 16:55:44-debug: refresh db internal success
2025-9-27 16:55:44-debug: refresh db i18n success
2025-9-27 16:55:44-debug: refresh db assets success
2025-9-27 16:55:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:55:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:55:44-debug: asset-db:refresh-all-database (116ms)
2025-9-27 16:55:44-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-27 16:55:45-debug: refresh db internal success
2025-9-27 16:55:45-debug: refresh db i18n success
2025-9-27 16:55:45-debug: refresh db assets success
2025-9-27 16:55:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:55:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:55:45-debug: asset-db:refresh-all-database (116ms)
2025-9-27 16:55:45-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:55:45-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-27 16:56:55-debug: refresh db internal success
2025-9-27 16:56:55-debug: refresh db i18n success
2025-9-27 16:56:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 16:56:55-debug: refresh db assets success
2025-9-27 16:56:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:56:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:56:55-debug: asset-db:refresh-all-database (146ms)
2025-9-27 16:56:55-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:56:55-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:56:59-debug: refresh db internal success
2025-9-27 16:56:59-debug: refresh db i18n success
2025-9-27 16:56:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:56:59-debug: refresh db assets success
2025-9-27 16:56:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:56:59-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:56:59-debug: asset-db:refresh-all-database (152ms)
2025-9-27 16:56:59-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:57:00-debug: refresh db internal success
2025-9-27 16:57:00-debug: refresh db i18n success
2025-9-27 16:57:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:57:00-debug: refresh db assets success
2025-9-27 16:57:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:57:00-debug: asset-db:refresh-all-database (129ms)
2025-9-27 16:57:00-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:57:00-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:57:17-debug: refresh db internal success
2025-9-27 16:57:17-debug: refresh db i18n success
2025-9-27 16:57:17-debug: refresh db assets success
2025-9-27 16:57:17-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:57:17-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:57:17-debug: asset-db:refresh-all-database (113ms)
2025-9-27 16:57:42-debug: refresh db internal success
2025-9-27 16:57:42-debug: refresh db i18n success
2025-9-27 16:57:42-debug: refresh db assets success
2025-9-27 16:57:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:57:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:57:42-debug: asset-db:refresh-all-database (143ms)
2025-9-27 16:57:42-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 16:57:42-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 16:58:03-debug: refresh db internal success
2025-9-27 16:58:03-debug: refresh db i18n success
2025-9-27 16:58:03-debug: refresh db assets success
2025-9-27 16:58:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 16:58:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 16:58:03-debug: asset-db:refresh-all-database (115ms)
2025-9-27 17:01:42-debug: refresh db internal success
2025-9-27 17:01:42-debug: refresh db i18n success
2025-9-27 17:01:42-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\FormationPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 17:01:42-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 17:01:42-debug: refresh db assets success
2025-9-27 17:01:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:01:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:01:42-debug: asset-db:refresh-all-database (153ms)
2025-9-27 17:01:42-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 17:01:46-debug: refresh db internal success
2025-9-27 17:01:46-debug: refresh db i18n success
2025-9-27 17:01:46-debug: refresh db assets success
2025-9-27 17:01:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:01:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:01:46-debug: asset-db:refresh-all-database (160ms)
2025-9-27 17:01:48-debug: refresh db internal success
2025-9-27 17:01:48-debug: refresh db i18n success
2025-9-27 17:01:48-debug: refresh db assets success
2025-9-27 17:01:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:01:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:01:48-debug: asset-db:refresh-all-database (112ms)
2025-9-27 17:01:48-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 17:01:48-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 17:01:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\formation\f_01.json
background: #aaff85; color: #000;
color: #000;
2025-9-27 17:01:57-debug: asset-db:reimport-asset30ba4125-5f81-4175-bc13-1a965f6299a3 (2ms)
2025-9-27 17:02:47-debug: refresh db internal success
2025-9-27 17:02:47-debug: refresh db i18n success
2025-9-27 17:02:47-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 17:02:47-debug: refresh db assets success
2025-9-27 17:02:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:02:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:02:47-debug: asset-db:refresh-all-database (150ms)
2025-9-27 17:02:47-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 17:02:47-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 17:02:53-debug: refresh db internal success
2025-9-27 17:02:53-debug: refresh db i18n success
2025-9-27 17:02:53-debug: refresh db assets success
2025-9-27 17:02:53-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:02:53-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:02:53-debug: asset-db:refresh-all-database (116ms)
2025-9-27 17:02:53-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 17:02:55-debug: refresh db internal success
2025-9-27 17:02:55-debug: refresh db i18n success
2025-9-27 17:02:55-debug: refresh db assets success
2025-9-27 17:02:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:02:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:02:55-debug: asset-db:refresh-all-database (113ms)
2025-9-27 17:02:55-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 17:02:55-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 17:03:05-debug: refresh db internal success
2025-9-27 17:03:05-debug: refresh db i18n success
2025-9-27 17:03:05-debug: refresh db assets success
2025-9-27 17:03:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:03:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:03:05-debug: asset-db:refresh-all-database (114ms)
2025-9-27 17:03:19-debug: refresh db internal success
2025-9-27 17:03:19-debug: refresh db i18n success
2025-9-27 17:03:19-debug: refresh db assets success
2025-9-27 17:03:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:03:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:03:19-debug: asset-db:refresh-all-database (121ms)
2025-9-27 17:03:19-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 17:03:19-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 17:04:03-debug: refresh db internal success
2025-9-27 17:04:03-debug: refresh db i18n success
2025-9-27 17:04:03-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\FormationPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 17:04:03-debug: refresh db assets success
2025-9-27 17:04:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:04:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:04:03-debug: asset-db:refresh-all-database (148ms)
2025-9-27 17:04:03-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 17:04:03-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 17:04:28-debug: refresh db internal success
2025-9-27 17:04:28-debug: refresh db i18n success
2025-9-27 17:04:29-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 17:04:29-debug: refresh db assets success
2025-9-27 17:04:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:04:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:04:29-debug: asset-db:refresh-all-database (125ms)
2025-9-27 17:04:29-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 17:04:29-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-27 17:05:35-debug: refresh db internal success
2025-9-27 17:05:35-debug: refresh db i18n success
2025-9-27 17:05:35-debug: refresh db assets success
2025-9-27 17:05:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:05:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:05:35-debug: asset-db:refresh-all-database (114ms)
2025-9-27 17:05:35-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-27 17:05:35-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-27 17:06:18-debug: refresh db internal success
2025-9-27 17:06:18-debug: refresh db i18n success
2025-9-27 17:06:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:06:18-debug: refresh db assets success
2025-9-27 17:06:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:06:18-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 17:06:18-debug: asset-db:refresh-all-database (141ms)
2025-9-27 17:06:18-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-27 17:06:32-debug: refresh db internal success
2025-9-27 17:06:32-debug: refresh db i18n success
2025-9-27 17:06:32-debug: refresh db assets success
2025-9-27 17:06:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:06:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:06:32-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 17:06:32-debug: asset-db:refresh-all-database (145ms)
2025-9-27 17:06:32-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 17:06:44-debug: refresh db internal success
2025-9-27 17:06:44-debug: refresh db i18n success
2025-9-27 17:06:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:06:44-debug: refresh db assets success
2025-9-27 17:06:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:06:44-debug: asset-db:refresh-all-database (143ms)
2025-9-27 17:06:44-debug: asset-db:worker-effect-data-processing (-1ms)
2025-9-27 17:06:59-debug: refresh db internal success
2025-9-27 17:06:59-debug: refresh db i18n success
2025-9-27 17:06:59-debug: refresh db assets success
2025-9-27 17:06:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:06:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:06:59-debug: asset-db:refresh-all-database (139ms)
2025-9-27 17:07:56-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\wave_1.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-27 17:07:56-debug: asset-db:reimport-assetd5f5e836-9495-4c08-890f-d92cc6696838 (2ms)
2025-9-27 17:07:56-debug: refresh db internal success
2025-9-27 17:07:56-debug: refresh db i18n success
2025-9-27 17:07:56-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:07:56-debug: refresh db assets success
2025-9-27 17:07:56-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:07:56-debug: asset-db:refresh-all-database (134ms)
2025-9-27 17:07:56-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 17:07:56-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 17:09:53-debug: refresh db internal success
2025-9-27 17:09:53-debug: refresh db i18n success
2025-9-27 17:09:53-debug: refresh db assets success
2025-9-27 17:09:53-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:09:53-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:09:53-debug: asset-db:refresh-all-database (147ms)
2025-9-27 17:10:30-debug: refresh db internal success
2025-9-27 17:10:30-debug: refresh db i18n success
2025-9-27 17:10:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:10:30-debug: refresh db assets success
2025-9-27 17:10:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:10:30-debug: asset-db:refresh-all-database (114ms)
2025-9-27 17:10:32-debug: refresh db internal success
2025-9-27 17:10:32-debug: refresh db i18n success
2025-9-27 17:10:32-debug: refresh db assets success
2025-9-27 17:10:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:10:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:10:32-debug: asset-db:refresh-all-database (114ms)
2025-9-27 17:10:32-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 17:10:43-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\wave_1.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-27 17:10:43-debug: asset-db:reimport-assetd5f5e836-9495-4c08-890f-d92cc6696838 (3ms)
2025-9-27 17:10:43-debug: refresh db internal success
2025-9-27 17:10:43-debug: refresh db i18n success
2025-9-27 17:10:43-debug: refresh db assets success
2025-9-27 17:10:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:10:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:10:43-debug: asset-db:refresh-all-database (126ms)
2025-9-27 17:10:46-debug: refresh db internal success
2025-9-27 17:10:46-debug: refresh db i18n success
2025-9-27 17:10:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:10:46-debug: refresh db assets success
2025-9-27 17:10:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:10:46-debug: asset-db:refresh-all-database (119ms)
2025-9-27 17:10:46-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 17:11:55-debug: start refresh asset from E:\M2Game\Client\assets\resources\game\level\wave\path\p_02.json...
2025-9-27 17:11:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\path\p_02.json
background: #aaff85; color: #000;
color: #000;
2025-9-27 17:11:55-debug: refresh asset E:\M2Game\Client\assets\resources\game\level\wave\path success
2025-9-27 17:11:55-debug: refresh db internal success
2025-9-27 17:11:55-debug: refresh db i18n success
2025-9-27 17:11:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\path
background: #aaff85; color: #000;
color: #000;
2025-9-27 17:11:55-debug: refresh db assets success
2025-9-27 17:11:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:11:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:11:55-debug: asset-db:refresh-all-database (123ms)
2025-9-27 17:11:55-debug: asset-db:worker-effect-data-processing (-1ms)
2025-9-27 17:15:12-debug: refresh db internal success
2025-9-27 17:15:12-debug: refresh db i18n success
2025-9-27 17:15:12-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 17:15:12-debug: refresh db assets success
2025-9-27 17:15:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:15:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:15:12-debug: asset-db:refresh-all-database (166ms)
2025-9-27 17:15:12-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-27 17:15:12-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-27 17:15:34-debug: refresh db internal success
2025-9-27 17:15:34-debug: refresh db i18n success
2025-9-27 17:15:34-debug: refresh db assets success
2025-9-27 17:15:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:15:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:15:34-debug: asset-db:refresh-all-database (122ms)
2025-9-27 17:15:40-debug: %cImport%c: E:\M2Game\Client\assets\scenes\PathEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-27 17:15:40-debug: asset-db:reimport-assetf87e7412-cf27-4eb5-a664-0071cad67cf8 (3ms)
2025-9-27 17:15:59-debug: %cImport%c: E:\M2Game\Client\assets\scenes\FormationEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-27 17:15:59-debug: asset-db:reimport-assetaa842f3a-8aea-42c2-a480-968aade3dfef (3ms)
2025-9-27 17:16:06-debug: %cImport%c: E:\M2Game\Client\assets\scenes\FormationEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-27 17:16:06-debug: asset-db:reimport-assetaa842f3a-8aea-42c2-a480-968aade3dfef (3ms)
2025-9-27 17:16:39-debug: refresh db internal success
2025-9-27 17:16:39-debug: refresh db i18n success
2025-9-27 17:16:40-debug: refresh db assets success
2025-9-27 17:16:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:16:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:16:40-debug: asset-db:refresh-all-database (149ms)
2025-9-27 17:16:40-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-27 17:16:40-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-27 17:20:13-debug: refresh db internal success
2025-9-27 17:20:13-debug: refresh db i18n success
2025-9-27 17:20:13-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\FormationPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 17:20:13-debug: refresh db assets success
2025-9-27 17:20:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:20:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:20:13-debug: asset-db:refresh-all-database (129ms)
2025-9-27 17:20:44-debug: refresh db internal success
2025-9-27 17:20:44-debug: refresh db i18n success
2025-9-27 17:20:44-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\FormationPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 17:20:44-debug: refresh db assets success
2025-9-27 17:20:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:20:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:20:44-debug: asset-db:refresh-all-database (159ms)
2025-9-27 17:26:01-debug: refresh db internal success
2025-9-27 17:26:01-debug: refresh db i18n success
2025-9-27 17:26:01-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\FormationPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 17:26:01-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathPointEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 17:26:01-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-27 17:26:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-27 17:26:01-debug: refresh db assets success
2025-9-27 17:26:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-27 17:26:01-debug: asset-db:refresh-all-database (173ms)
2025-9-27 17:26:01-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
