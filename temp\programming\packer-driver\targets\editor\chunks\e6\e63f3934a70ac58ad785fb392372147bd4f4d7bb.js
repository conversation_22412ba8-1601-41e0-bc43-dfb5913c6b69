System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, csproto, logWarn, MyApp, DataEvent, EventMgr, Mail, _crd;

  function _reportPossibleCrUseOfcsproto(extras) {
    _reporterNs.report("csproto", "db://assets/bundles/common/script/autogen/pb/cs_proto.js", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogWarn(extras) {
    _reporterNs.report("logWarn", "db://assets/scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "../../app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDataEvent(extras) {
    _reporterNs.report("DataEvent", "../../event/DataEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventMgr(extras) {
    _reporterNs.report("EventMgr", "../../event/EventManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIData(extras) {
    _reporterNs.report("IData", "../DataManager", _context.meta, extras);
  }

  _export("Mail", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      csproto = _unresolved_2.default;
    }, function (_unresolved_3) {
      logWarn = _unresolved_3.logWarn;
    }, function (_unresolved_4) {
      MyApp = _unresolved_4.MyApp;
    }, function (_unresolved_5) {
      DataEvent = _unresolved_5.DataEvent;
    }, function (_unresolved_6) {
      EventMgr = _unresolved_6.EventMgr;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "eb682MW7LxDwbLOsyEaomrs", "Mail", undefined);

      _export("Mail", Mail = class Mail {
        constructor() {
          this.mail_list = [];
        }

        init() {
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_MAIL_GET_LIST, this.onMailGetList, this);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_MAIL_NEW_MAIL_NOTIFY, this.onMailNewMailNotify, this);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_MAIL_UPDATE_READED, this.onMailUpdateReaded, this);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_MAIL_GET_ATTACHMENT, this.onMailGetAttachment, this);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_MAIL_DELETE, this.onMailDelete, this);
          this.cmdMailGetListAll();
        }

        update() {}

        getMailByIndex(index) {
          if (index >= 0 && index < this.mail_list.length) {
            return this.mail_list[index];
          }

          return undefined;
        } //#region 收协议


        onMailGetList(msg) {
          var _msg$body;

          if (msg.ret_code != (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).comm.RET_CODE.RET_CODE_NO_ERROR) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", `onMailGetList failed ${msg.ret_code}`);
            return;
          }

          var data = (_msg$body = msg.body) == null ? void 0 : _msg$body.mail_get_list;

          if (!data) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", "onMailGetList data is null");
            return;
          }

          this.mail_list = data.mail_list;
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).emit((_crd && DataEvent === void 0 ? (_reportPossibleCrUseOfDataEvent({
            error: Error()
          }), DataEvent) : DataEvent).MailRefresh); //MessageBox.show(`onMailGetList ${JSON.stringify(data)}`);
        }

        onMailNewMailNotify(msg) {
          var _msg$body2;

          if (msg.ret_code != (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).comm.RET_CODE.RET_CODE_NO_ERROR) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", `onMailNewMailNotify failed ${msg.ret_code}`);
            return;
          }

          var data = (_msg$body2 = msg.body) == null ? void 0 : _msg$body2.mail_new_mail_notify;

          if (!data) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", "onMailNewMailNotify data is null");
            return;
          }

          let guids = data.guids;
          this.cmdMailGetList(guids, 0, guids.length);
        }

        onMailUpdateReaded(msg) {
          var _msg$body3;

          if (msg.ret_code != (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).comm.RET_CODE.RET_CODE_NO_ERROR) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", `onMailUpdateReaded failed ${msg.ret_code}`);
            return;
          }

          var data = (_msg$body3 = msg.body) == null ? void 0 : _msg$body3.mail_update_readed;

          if (!data) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", "onMailUpdateReaded data is null");
            return;
          }

          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).emit((_crd && DataEvent === void 0 ? (_reportPossibleCrUseOfDataEvent({
            error: Error()
          }), DataEvent) : DataEvent).MailRefresh, data.guids);
        }

        onMailGetAttachment(msg) {
          var _msg$body4;

          if (msg.ret_code != (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).comm.RET_CODE.RET_CODE_NO_ERROR) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", `onMailGetAttachment failed ${msg.ret_code}`);
            return;
          }

          var data = (_msg$body4 = msg.body) == null ? void 0 : _msg$body4.mail_get_attachment;

          if (!data) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", "onMailGetAttachment data is null");
            return;
          }
        }

        onMailDelete(msg) {
          var _msg$body5;

          if (msg.ret_code != (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).comm.RET_CODE.RET_CODE_NO_ERROR) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", `onMailDelete failed ${msg.ret_code}`);
            return;
          }

          var data = (_msg$body5 = msg.body) == null ? void 0 : _msg$body5.mail_delete;

          if (!data) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", "onMailDelete data is null");
            return;
          }
        } //#endregion
        //#region 发协议


        cmdMailGetListAll() {
          this.cmdMailGetList([], 0, 0);
        }

        cmdMailGetList(ids, form, to) {
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.sendMessage((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_MAIL_GET_LIST, {
            mail_get_list: {
              guids: ids,
              index_from: form,
              index_to: to
            }
          });
        }

        cmdMailUpdateReaded(ids) {
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.sendMessage((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_MAIL_UPDATE_READED, {
            mail_update_readed: {
              guids: ids
            }
          });
        }

        cmdMailGetAttachment(ids) {
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.sendMessage((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_MAIL_GET_ATTACHMENT, {
            mail_get_attachment: {
              guids: ids
            }
          });
        }

        cmdMailDelete(ids) {
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.sendMessage((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_MAIL_DELETE, {
            mail_delete: {
              guids: ids
            }
          });
        } //#endregion


      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=e63f3934a70ac58ad785fb392372147bd4f4d7bb.js.map