System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Color, Component, RichText, Vec3, Graphics, VerticalTextAlignment, HorizontalTextAlignment, FormationPoint, _dec, _dec2, _dec3, _dec4, _dec5, _class, _crd, ccclass, playOnFocus, executeInEditMode, property, disallowMultiple, menu, requireComponent, FormationPointEditor;

  function _reportPossibleCrUseOfFormationPoint(extras) {
    _reporterNs.report("FormationPoint", "db://assets/bundles/common/script/game/data/WaveData", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Color = _cc.Color;
      Component = _cc.Component;
      RichText = _cc.RichText;
      Vec3 = _cc.Vec3;
      Graphics = _cc.Graphics;
      VerticalTextAlignment = _cc.VerticalTextAlignment;
      HorizontalTextAlignment = _cc.HorizontalTextAlignment;
    }, function (_unresolved_2) {
      FormationPoint = _unresolved_2.FormationPoint;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "56d8frq4PhJkqlgz2sjDxmv", "FormationPointEditor", undefined);

      __checkObsolete__(['_decorator', 'instantiate', 'Color', 'Component', 'JsonAsset', 'RichText', 'Vec3', 'Graphics', 'CCObject', 'VerticalTextAlignment', 'HorizontalTextAlignment']);

      ({
        ccclass,
        playOnFocus,
        executeInEditMode,
        property,
        disallowMultiple,
        menu,
        requireComponent
      } = _decorator);

      _export("FormationPointEditor", FormationPointEditor = (_dec = ccclass('FormationPointEditor'), _dec2 = menu("怪物/编辑器/阵型点"), _dec3 = requireComponent(Graphics), _dec4 = executeInEditMode(true), _dec5 = disallowMultiple(true), _dec(_class = _dec2(_class = _dec3(_class = _dec4(_class = _dec5(_class = class FormationPointEditor extends Component {
        constructor(...args) {
          super(...args);
          this._graphics = null;
          this._richText = null;
          this._cachedIndex = -1;
          this.selected = false;
        }

        get graphics() {
          if (!this._graphics) {
            this._graphics = this.node.getComponent(Graphics) || this.node.addComponent(Graphics);
          }

          return this._graphics;
        }

        get richText() {
          if (!this._richText) {
            this._richText = this.node.getComponent(RichText) || this.node.addComponent(RichText); // this._richText.hideFlags = CCObject.Flags.AllHideMasks;

            this._richText.fontSize = 16;
            this._richText.verticalAlign = VerticalTextAlignment.CENTER;
            this._richText.horizontalAlign = HorizontalTextAlignment.CENTER;
            this._richText.fontColor = Color.BLACK;
          }

          return this._richText;
        }

        onFocusInEditor() {
          this.selected = true;
        }

        onLostFocusInEditor() {
          this.selected = false;
        }

        get formationPoint() {
          let point = new (_crd && FormationPoint === void 0 ? (_reportPossibleCrUseOfFormationPoint({
            error: Error()
          }), FormationPoint) : FormationPoint)();
          point.x = this.node.position.x;
          point.y = this.node.position.y;
          return point;
        }

        set formationPoint(value) {
          this.node.position = new Vec3(value.x, value.y, 0);
        }

        update(dt) {
          const graphics = this.graphics;
          graphics.clear();
          const color = this.selected ? Color.YELLOW : Color.WHITE;
          graphics.fillColor = color;
          graphics.lineWidth = 2;
          graphics.circle(0, 0, 10);
          graphics.fill();
          graphics.stroke();
          const siblingIndex = this.node.getSiblingIndex();

          if (siblingIndex !== this._cachedIndex) {
            this._cachedIndex = siblingIndex;
            this.node.name = `Point_${siblingIndex}`;
            this.richText.string = `${siblingIndex}`;
          }
        }

      }) || _class) || _class) || _class) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=2008ef90ba12a1fbf9144f9c206edfd8363bfa6e.js.map