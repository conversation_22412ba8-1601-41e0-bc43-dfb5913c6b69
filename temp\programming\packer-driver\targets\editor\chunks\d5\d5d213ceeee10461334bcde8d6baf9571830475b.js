System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, error, MyApp, PlanePropType, AttributeConst, AttributeData, DataMgr, PlaneData, _crd, PATH_SPINE;

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/bundles/common/script/app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlanePropType(extras) {
    _reporterNs.report("PlanePropType", "db://assets/bundles/common/script/autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfResEquipUpgrade(extras) {
    _reporterNs.report("ResEquipUpgrade", "db://assets/bundles/common/script/autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfResPlane(extras) {
    _reporterNs.report("ResPlane", "db://assets/bundles/common/script/autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAttributeConst(extras) {
    _reporterNs.report("AttributeConst", "../../const/AttributeConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAttributeData(extras) {
    _reporterNs.report("AttributeData", "../base/AttributeData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDataMgr(extras) {
    _reporterNs.report("DataMgr", "../DataManager", _context.meta, extras);
  }

  _export("PlaneData", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      error = _cc.error;
    }, function (_unresolved_2) {
      MyApp = _unresolved_2.MyApp;
    }, function (_unresolved_3) {
      PlanePropType = _unresolved_3.PlanePropType;
    }, function (_unresolved_4) {
      AttributeConst = _unresolved_4.AttributeConst;
    }, function (_unresolved_5) {
      AttributeData = _unresolved_5.AttributeData;
    }, function (_unresolved_6) {
      DataMgr = _unresolved_6.DataMgr;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "ea969Uck/RHK7YbHpIVhq2S", "PlaneData", undefined);

      __checkObsolete__(['error']);

      PATH_SPINE = "spine/plane/mainplane/";

      _export("PlaneData", PlaneData = class PlaneData extends (_crd && AttributeData === void 0 ? (_reportPossibleCrUseOfAttributeData({
        error: Error()
      }), AttributeData) : AttributeData) {
        constructor(...args) {
          super(...args);
          this.id = 0;
          //唯一id
          this._planeId = 0;
          //飞机id
          this.config = null;
        }

        //飞机静态配置
        get planeId() {
          return this._planeId;
        }

        set planeId(value) {
          if (value != this._planeId) {
            this._planeId = value;
            this.updateConfig();
          }
        }

        get recourseSpine() {
          if (!this.config) {
            return "";
          }

          return PATH_SPINE + this.config.prefab + "/" + this.config.prefab;
        }

        updateConfig() {
          if (!this._planeId) {
            error("Plane id or level is 0, cannot update config.");
            return;
          }

          this.config = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanTables.TbPlane.get(this._planeId) || null;
          this.updateData();
        }

        updateData() {
          if (!this.config) {
            error(`Plane ${this._planeId} config is null, cannot update attributes.`);
            return;
          } //根据飞机基础配置表，获取基础属性


          this.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).MaxHPOutAdd, this.config.property.MaxHP);
          this.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).AttackOutAdd, this.config.property.Attack);
          this.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).HPRecoveryOutAdd, this.config.property.HPRecovery);
          this.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).FortunateOutAdd, this.config.property.Fortunate);
          this.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).MissRateOut, this.config.property.Miss);
          this.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).BulletHurtResistanceOutPer, this.config.property.BulletHurtResistance);
          this.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).CollisionHurtResistanceOutPer, this.config.property.CollisionHurtResistance);
          this.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).PickRadiusOutAdd, this.config.property.PickRadius);
          this.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).FinalScoreRateOut, this.config.property.FinalScore);
          this.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).NuclearMax, this.config.property.NuclearMax);
          this.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).MaxEnergyOutAdd, this.config.property.MaxEnergy);
          this.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).EnergyRecoveryOutAdd, this.config.property.EnergyRecovery); // 装备

          (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).equip.eqSlots.slots.forEach(slot => {
            if (!slot.equip_id || !slot.level || !slot.equip_class) {
              return;
            }

            const equipConfig = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).lubanTables.TbResEquip.get(slot.equip_id);

            if (!equipConfig) {
              return;
            }

            const equipUpgradeConfigs = [];
            (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).lubanTables.TbEquipUpgrade.getDataList().forEach(element => {
              if (element.equipClass == slot.equip_class && element.levelFrom <= slot.level) {
                equipUpgradeConfigs.push(element);
              }
            });
            equipUpgradeConfigs.sort((a, b) => a.levelTo - b.levelTo);
            const equipUpgradeProps = new Map();

            for (let equipUpgradeConfig of equipUpgradeConfigs) {
              for (let i = equipUpgradeConfig.levelFrom; i <= slot.level && i <= equipUpgradeConfig.levelTo; i++) {
                equipUpgradeConfig.propInc.forEach(prop => {
                  if (prop.propType == (_crd && PlanePropType === void 0 ? (_reportPossibleCrUseOfPlanePropType({
                    error: Error()
                  }), PlanePropType) : PlanePropType).NONE) {
                    return;
                  }

                  equipUpgradeProps.set(prop.propType, (equipUpgradeProps.get(prop.propType) || 1) * (10000 + prop.propParam) / 10000);
                });
              }
            }

            equipConfig.props.forEach(prop => {
              const value = prop.propParam * (equipUpgradeProps.get(prop.propType) || 1);

              switch (prop.propType) {
                case (_crd && PlanePropType === void 0 ? (_reportPossibleCrUseOfPlanePropType({
                  error: Error()
                }), PlanePropType) : PlanePropType).MaxHP:
                  this.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                    error: Error()
                  }), AttributeConst) : AttributeConst).MaxHPOutAdd, value);
                  break;

                case (_crd && PlanePropType === void 0 ? (_reportPossibleCrUseOfPlanePropType({
                  error: Error()
                }), PlanePropType) : PlanePropType).HPRecovery:
                  this.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                    error: Error()
                  }), AttributeConst) : AttributeConst).HPRecoveryOutAdd, value);
                  break;

                case (_crd && PlanePropType === void 0 ? (_reportPossibleCrUseOfPlanePropType({
                  error: Error()
                }), PlanePropType) : PlanePropType).Attack:
                  this.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                    error: Error()
                  }), AttributeConst) : AttributeConst).AttackOutAdd, value);
                  break;

                case (_crd && PlanePropType === void 0 ? (_reportPossibleCrUseOfPlanePropType({
                  error: Error()
                }), PlanePropType) : PlanePropType).Fortunate:
                  this.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                    error: Error()
                  }), AttributeConst) : AttributeConst).FortunateOutAdd, value);
                  break;

                case (_crd && PlanePropType === void 0 ? (_reportPossibleCrUseOfPlanePropType({
                  error: Error()
                }), PlanePropType) : PlanePropType).Miss:
                  this.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                    error: Error()
                  }), AttributeConst) : AttributeConst).MissRateOut, value);
                  break;

                case (_crd && PlanePropType === void 0 ? (_reportPossibleCrUseOfPlanePropType({
                  error: Error()
                }), PlanePropType) : PlanePropType).BulletHurtResistance:
                  this.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                    error: Error()
                  }), AttributeConst) : AttributeConst).BulletHurtResistanceOutPer, value);
                  break;

                case (_crd && PlanePropType === void 0 ? (_reportPossibleCrUseOfPlanePropType({
                  error: Error()
                }), PlanePropType) : PlanePropType).CollisionHurtResistance:
                  this.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                    error: Error()
                  }), AttributeConst) : AttributeConst).CollisionHurtResistanceOutPer, value);
                  break;

                case (_crd && PlanePropType === void 0 ? (_reportPossibleCrUseOfPlanePropType({
                  error: Error()
                }), PlanePropType) : PlanePropType).PickRadius:
                  this.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                    error: Error()
                  }), AttributeConst) : AttributeConst).PickRadiusOutAdd, value);
                  break;

                case (_crd && PlanePropType === void 0 ? (_reportPossibleCrUseOfPlanePropType({
                  error: Error()
                }), PlanePropType) : PlanePropType).FinalScore:
                  this.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                    error: Error()
                  }), AttributeConst) : AttributeConst).FinalScoreRateOut, value);
                  break;

                case (_crd && PlanePropType === void 0 ? (_reportPossibleCrUseOfPlanePropType({
                  error: Error()
                }), PlanePropType) : PlanePropType).NuclearMax:
                  this.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                    error: Error()
                  }), AttributeConst) : AttributeConst).NuclearMax, value);
                  break;

                case (_crd && PlanePropType === void 0 ? (_reportPossibleCrUseOfPlanePropType({
                  error: Error()
                }), PlanePropType) : PlanePropType).MaxEnergy:
                  this.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                    error: Error()
                  }), AttributeConst) : AttributeConst).MaxEnergyOutAdd, value);
                  break;

                case (_crd && PlanePropType === void 0 ? (_reportPossibleCrUseOfPlanePropType({
                  error: Error()
                }), PlanePropType) : PlanePropType).EnergyRecovery:
                  this.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                    error: Error()
                  }), AttributeConst) : AttributeConst).EnergyRecoveryOutAdd, value);
                  break;

                case (_crd && PlanePropType === void 0 ? (_reportPossibleCrUseOfPlanePropType({
                  error: Error()
                }), PlanePropType) : PlanePropType).AllBulletAttack:
                  this.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                    error: Error()
                  }), AttributeConst) : AttributeConst).BulletAttackOutAdd, value);
                  break;

                case (_crd && PlanePropType === void 0 ? (_reportPossibleCrUseOfPlanePropType({
                  error: Error()
                }), PlanePropType) : PlanePropType).ExplosiveBulletAttack:
                  this.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                    error: Error()
                  }), AttributeConst) : AttributeConst).ExplosiveBulletAttackOutAdd, value);
                  break;

                case (_crd && PlanePropType === void 0 ? (_reportPossibleCrUseOfPlanePropType({
                  error: Error()
                }), PlanePropType) : PlanePropType).NormalBulletAttack:
                  this.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                    error: Error()
                  }), AttributeConst) : AttributeConst).NormalBulletAttackOutAdd, value);
                  break;

                case (_crd && PlanePropType === void 0 ? (_reportPossibleCrUseOfPlanePropType({
                  error: Error()
                }), PlanePropType) : PlanePropType).EnergeticBulletAttack:
                  this.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                    error: Error()
                  }), AttributeConst) : AttributeConst).EnergeticBulletAttackOutAdd, value);
                  break;

                case (_crd && PlanePropType === void 0 ? (_reportPossibleCrUseOfPlanePropType({
                  error: Error()
                }), PlanePropType) : PlanePropType).PhysicalBulletAttack:
                  this.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                    error: Error()
                  }), AttributeConst) : AttributeConst).PhysicsBulletAttackOutAdd, value);
                  break;
              }
            });
          });
        }

        getAttributeList() {// 获取装备，技能，buff等属性
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=d5d213ceeee10461334bcde8d6baf9571830475b.js.map