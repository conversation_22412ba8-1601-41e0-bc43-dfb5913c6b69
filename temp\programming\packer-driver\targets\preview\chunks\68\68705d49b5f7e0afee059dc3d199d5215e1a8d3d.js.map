{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/layer/GameFightUI.ts"], "names": ["_decorator", "Component", "find", "<PERSON><PERSON><PERSON><PERSON>", "Label", "Node", "Tween", "tween", "UIOpacity", "UITransform", "EventManager", "GameEvent", "GameIns", "UIMgr", "GamePauseUI", "getI18StrByKey", "ccclass", "property", "GameFightUI", "onLoad", "instance", "reset", "node", "active", "onEnable", "Instance", "on", "GameStart", "onEventGameStart", "GameMainPlaneIn", "onEventGamePlaneIn", "onDisable", "off", "setTipActive", "setBossUIActive", "btnPause", "Mask", "updatePlayerUI", "mainPlane", "mainPlaneManager", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "playerHpBar", "getComponent", "width", "curHp", "maxHp", "labPlayerHp", "string", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "curExp", "curMaxExp", "labPlayerLv", "curLv", "updateBossUI", "boss<PERSON><PERSON>", "bossHpBar", "isActive", "NodeBoss", "labChapterLv", "String", "battleManager", "curLevel", "tipNode", "setTouchState", "is<PERSON><PERSON>ch", "showMaskAni", "onBtnPauseClicked", "gameStateManager", "gamePause", "openUI", "uIOpacity", "opacity", "stopAllByTarget", "to", "start"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,O,OAAAA,O;AAASC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,W,OAAAA,W;;AAC9EC,MAAAA,Y;;AACEC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,c,iBAAAA,c;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBjB,U;;6BAGjBkB,W,WADZF,OAAO,CAAC,aAAD,C,UAGHC,QAAQ,CAACZ,IAAD,C,UAERY,QAAQ,CAACZ,IAAD,C,UAERY,QAAQ,CAACZ,IAAD,C,UAGRY,QAAQ,CAACZ,IAAD,C,UAERY,QAAQ,CAACZ,IAAD,C,UAERY,QAAQ,CAACb,KAAD,C,UAERa,QAAQ,CAACb,KAAD,C,UAERa,QAAQ,CAACb,KAAD,C,WAGRa,QAAQ,CAACZ,IAAD,C,WAERY,QAAQ,CAACZ,IAAD,C,sCAvBb,MACaa,WADb,SACiCjB,SADjC,CAC2C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AA0B7BkB,QAAAA,MAAM,GAAS;AACrBD,UAAAA,WAAW,CAACE,QAAZ,GAAuB,IAAvB;AACA,eAAKC,KAAL;AACA,eAAKC,IAAL,CAAUC,MAAV,GAAmB,KAAnB;AACH;;AAESC,QAAAA,QAAQ,GAAS;AACvB;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,EAAtB,CAAyB;AAAA;AAAA,sCAAUC,SAAnC,EAA8C,KAAKC,gBAAnD,EAAqE,IAArE;AACA;AAAA;AAAA,4CAAaH,QAAb,CAAsBC,EAAtB,CAAyB;AAAA;AAAA,sCAAUG,eAAnC,EAAoD,KAAKC,kBAAzD,EAA6E,IAA7E;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,4CAAaN,QAAb,CAAsBO,GAAtB,CAA0B;AAAA;AAAA,sCAAUL,SAApC,EAA+C,KAAKC,gBAApD,EAAsE,IAAtE;AACA;AAAA;AAAA,4CAAaH,QAAb,CAAsBO,GAAtB,CAA0B;AAAA;AAAA,sCAAUH,eAApC,EAAqD,KAAKC,kBAA1D,EAA8E,IAA9E;AACH;;AAEDT,QAAAA,KAAK,GAAE;AACH,eAAKY,YAAL,CAAkB,KAAlB;AACA,eAAKC,eAAL,CAAqB,KAArB;AACA,eAAKC,QAAL,CAAeZ,MAAf,GAAwB,KAAxB;AACA,eAAKa,IAAL,CAAWb,MAAX,GAAoB,KAApB;AACH;;AAEDc,QAAAA,cAAc,GAAE;AACZ,cAAIC,SAAS,GAAG;AAAA;AAAA,kCAAQC,gBAAR,CAAyBD,SAAzC;;AACA,cAAI,CAACnC,OAAO,CAACmC,SAAD,CAAZ,EAAwB;AACpB;AACH;;AAED,cAAIE,WAAkB,GAAG,CAAzB,CANY,CAOZ;;AACA,cAAI,KAAKC,WAAT,EAAqB;AACjBD,YAAAA,WAAW,GAAGtC,IAAI,CAAC,QAAD,EAAU,KAAKuC,WAAf,CAAJ,CAAiCC,YAAjC,CAA8CjC,WAA9C,EAA4DkC,KAA1E;AACA,iBAAKF,WAAL,CAAkBC,YAAlB,CAA+BjC,WAA/B,EAA6CkC,KAA7C,GAAqDL,SAAS,CAACM,KAAV,GAAgBN,SAAS,CAACO,KAA1B,GAAgCL,WAArF;AACA,iBAAKM,WAAL,CAAkBC,MAAlB,GAA8BT,SAAS,CAACM,KAAxC,SAAiDN,SAAS,CAACO,KAA3D;AACH,WAZW,CAcZ;;;AACA,cAAI,KAAKG,cAAT,EAAwB;AACpBR,YAAAA,WAAW,GAAGtC,IAAI,CAAC,QAAD,EAAU,KAAK8C,cAAf,CAAJ,CAAoCN,YAApC,CAAiDjC,WAAjD,EAA+DkC,KAA7E;AACA,iBAAKK,cAAL,CAAqBN,YAArB,CAAkCjC,WAAlC,EAAgDkC,KAAhD,GAAwD;AAAA;AAAA,oCAAQJ,gBAAR,CAAyBU,MAAzB,GAAgC;AAAA;AAAA,oCAAQV,gBAAR,CAAyBW,SAAzD,GAAmEV,WAA3H;AACA,iBAAKW,WAAL,CAAkBJ,MAAlB,GAA2B;AAAA;AAAA,kDAAe,UAAf,IAA2B;AAAA;AAAA,oCAAQR,gBAAR,CAAyBa,KAA/E;AACH;AACJ;;AAEDC,QAAAA,YAAY,CAACC,SAAD,EAAqB;AAC7B;AACA,cAAI,KAAKC,SAAT,EAAmB;AACf,gBAAIf,WAAW,GAAGtC,IAAI,CAAC,QAAD,EAAU,KAAKqD,SAAf,CAAJ,CAA+Bb,YAA/B,CAA4CjC,WAA5C,EAA0DkC,KAA5E;AACA,iBAAKY,SAAL,CAAgBb,YAAhB,CAA6BjC,WAA7B,EAA2CkC,KAA3C,GAAmDW,SAAS,CAACV,KAAV,GAAgBU,SAAS,CAACT,KAA1B,GAAgCL,WAAnF;AACH;AACJ;;AAEDN,QAAAA,eAAe,CAACsB,QAAD,EAAkB;AAC7B,eAAKC,QAAL,CAAelC,MAAf,GAAwBiC,QAAxB;AACH;;AAED5B,QAAAA,gBAAgB,GAAG;AACf,eAAKK,YAAL,CAAkB,KAAlB;AACA,eAAKyB,YAAL,CAAmBX,MAAnB,GAA4BY,MAAM,CAAC;AAAA;AAAA,kCAAQC,aAAR,CAAsBC,QAAvB,CAAlC;AACH;;AAED/B,QAAAA,kBAAkB,GAAG;AACjB,eAAKG,YAAL,CAAkB,IAAlB;AACH;;AAEDA,QAAAA,YAAY,CAACV,MAAD,EAAkB;AAC1B,eAAKuC,OAAL,CAAcvC,MAAd,GAAuBA,MAAvB;AACH;;AAEDwC,QAAAA,aAAa,CAACC,OAAD,EAAmB;AAC5B,cAAI,KAAK7B,QAAT,EAAkB;AACd,iBAAKA,QAAL,CAAcZ,MAAd,GAAuB,CAACyC,OAAxB;AACH;;AACD,eAAKC,WAAL,CAAiBD,OAAjB;AACH;;AAEDE,QAAAA,iBAAiB,GAAG;AAChB;AAAA;AAAA,kCAAQC,gBAAR,CAAyBC,SAAzB;AACA;AAAA;AAAA,8BAAMC,MAAN;AAAA;AAAA;AAEH;;AAEDJ,QAAAA,WAAW,CAACD,OAAD,EAAmB;AAC1B,eAAK5B,IAAL,CAAWb,MAAX,GAAoB,CAACyC,OAArB;;AACA,cAAI,CAACA,OAAL,EAAc;AACV,gBAAIM,SAAS,GAAG,KAAKlC,IAAL,CAAWM,YAAX,CAAwBlC,SAAxB,CAAhB;AACA8D,YAAAA,SAAS,CAAEC,OAAX,GAAqB,CAArB;AACAjE,YAAAA,KAAK,CAACkE,eAAN,CAAsBF,SAAtB;AACA/D,YAAAA,KAAK,CAAC+D,SAAD,CAAL,CACKG,EADL,CACQ,GADR,EACa;AAAEF,cAAAA,OAAO,EAAE;AAAX,aADb,EAEKG,KAFL;AAGH;AACJ;;AAvHsC,O,UAyBhCtD,Q;;;;;iBAtBgB,I;;;;;;;iBAEC,I;;;;;;;iBAEJ,I;;;;;;;iBAGO,I;;;;;;;iBAEG,I;;;;;;;iBAEF,I;;;;;;;iBAEA,I;;;;;;;iBAEC,I;;;;;;;iBAGL,I;;;;;;;iBAEC,I", "sourcesContent": ["import { _decorator, Component, find, isValid, Label, Node, Tween, tween, UIOpacity, UITransform } from 'cc';\r\nimport EventManager from '../../../event/EventManager';\r\nimport { GameEvent } from '../../event/GameEvent';\r\nimport { GameIns } from '../../GameIns';\r\nimport { UIMgr } from 'db://assets/scripts/core/base/UIMgr';\r\nimport { GamePauseUI } from '../../../ui/gameui/game/GamePauseUI';\r\nimport { getI18StrByKey } from 'db://i18n/LanguageData';\r\nimport PlaneBase from '../plane/PlaneBase';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('GameFightUI')\r\nexport class GameFightUI extends Component {\r\n\r\n    @property(Node)\r\n    tipNode: Node | null = null\r\n    @property(Node)\r\n    btnPause: Node | null = null\r\n    @property(Node)\r\n    Mask: Node | null = null\r\n\r\n    @property(Node)\r\n    playerHpBar: Node | null = null\r\n    @property(Node)\r\n    playerLevelBar: Node | null = null\r\n    @property(Label)\r\n    labPlayerHp: Label | null = null\r\n    @property(Label)\r\n    labPlayerLv: Label | null = null\r\n    @property(Label)\r\n    labChapterLv: Label | null = null\r\n    \r\n    @property(Node)\r\n    NodeBoss: Node | null = null\r\n    @property(Node)\r\n    bossHpBar: Node | null = null\r\n\r\n    static instance: GameFightUI;\r\n    protected onLoad(): void {\r\n        GameFightUI.instance = this;\r\n        this.reset();\r\n        this.node.active = false;\r\n    }\r\n\r\n    protected onEnable(): void {\r\n        EventManager.Instance.on(GameEvent.GameStart, this.onEventGameStart, this);\r\n        EventManager.Instance.on(GameEvent.GameMainPlaneIn, this.onEventGamePlaneIn, this);\r\n    }\r\n\r\n    protected onDisable(): void {\r\n        EventManager.Instance.off(GameEvent.GameStart, this.onEventGameStart, this);\r\n        EventManager.Instance.off(GameEvent.GameMainPlaneIn, this.onEventGamePlaneIn, this);\r\n    }\r\n\r\n    reset(){\r\n        this.setTipActive(false);\r\n        this.setBossUIActive(false);\r\n        this.btnPause!.active = false;\r\n        this.Mask!.active = false;\r\n    }\r\n\r\n    updatePlayerUI(){\r\n        let mainPlane = GameIns.mainPlaneManager.mainPlane;\r\n        if (!isValid(mainPlane)){\r\n            return;\r\n        }\r\n\r\n        let maxBarWidth:number = 0;\r\n        //hp\r\n        if (this.playerHpBar){\r\n            maxBarWidth = find(\"hp_bar\",this.playerHpBar)!.getComponent(UITransform)!.width;\r\n            this.playerHpBar!.getComponent(UITransform)!.width = mainPlane.curHp/mainPlane.maxHp*maxBarWidth;\r\n            this.labPlayerHp!.string = `${mainPlane.curHp}/${mainPlane.maxHp}`\r\n        }\r\n\r\n        //lv\r\n        if (this.playerLevelBar){\r\n            maxBarWidth = find(\"hp_bar\",this.playerLevelBar)!.getComponent(UITransform)!.width;\r\n            this.playerLevelBar!.getComponent(UITransform)!.width = GameIns.mainPlaneManager.curExp/GameIns.mainPlaneManager.curMaxExp*maxBarWidth;\r\n            this.labPlayerLv!.string = getI18StrByKey(\"FIGHT_LV\")+GameIns.mainPlaneManager.curLv;\r\n        }\r\n    }\r\n\r\n    updateBossUI(bossPlane:PlaneBase){\r\n        //hp\r\n        if (this.bossHpBar){\r\n            let maxBarWidth = find(\"hp_bar\",this.bossHpBar)!.getComponent(UITransform)!.width;\r\n            this.bossHpBar!.getComponent(UITransform)!.width = bossPlane.curHp/bossPlane.maxHp*maxBarWidth;\r\n        }\r\n    }\r\n\r\n    setBossUIActive(isActive:boolean){\r\n        this.NodeBoss!.active = isActive;\r\n    }\r\n\r\n    onEventGameStart() {\r\n        this.setTipActive(false);\r\n        this.labChapterLv!.string = String(GameIns.battleManager.curLevel);\r\n    }\r\n\r\n    onEventGamePlaneIn() {\r\n        this.setTipActive(true);\r\n    }\r\n\r\n    setTipActive(active: boolean) {\r\n        this.tipNode!.active = active;\r\n    }\r\n\r\n    setTouchState(isTouch: boolean) {\r\n        if (this.btnPause){\r\n            this.btnPause.active = !isTouch;\r\n        }\r\n        this.showMaskAni(isTouch);\r\n    }\r\n\r\n    onBtnPauseClicked() {\r\n        GameIns.gameStateManager.gamePause();\r\n        UIMgr.openUI(GamePauseUI);\r\n\r\n    }\r\n\r\n    showMaskAni(isTouch: boolean) {\r\n        this.Mask!.active = !isTouch;\r\n        if (!isTouch) {\r\n            let uIOpacity = this.Mask!.getComponent(UIOpacity);\r\n            uIOpacity!.opacity = 0;\r\n            Tween.stopAllByTarget(uIOpacity!);\r\n            tween(uIOpacity!)\r\n                .to(0.3, { opacity: 160 })\r\n                .start();\r\n        }\r\n    }\r\n}\r\n\r\n\r\n"]}