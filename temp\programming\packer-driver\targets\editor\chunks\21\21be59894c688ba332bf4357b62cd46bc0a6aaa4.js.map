{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/task/TaskTipUI.ts"], "names": ["_decorator", "Label", "ProgressBar", "csproto", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "logError", "MyApp", "ResTaskClass", "BundleName", "DataMgr", "BottomUIEvent", "DataEvent", "EventMgr", "ButtonPlus", "BottomTab", "TaskUI", "ccclass", "property", "TaskTipUI", "_isHomeUIHide", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Background", "getBundleName", "HomeTask", "onLoad", "on", "TaskRefresh", "onTaskRefresh", "SwitchPanel", "onSwitchPanel", "taskJumpBtn", "addClick", "onTaskJump", "onShow", "DAILY_TASK", "onHide", "onClose", "targetOff", "from", "to", "Home", "node", "active", "taskClass", "taskList", "task", "getTaskListByClass", "filter", "t", "status", "comm", "TASK_STATUS", "TASK_STATUS_NORMAL", "sort", "a", "b", "task_id", "length", "taskInfo", "resTask", "lubanTables", "TbResTask", "get", "info", "getTaskDescAndProgress", "taskDesc", "string", "desc", "taskProgress", "progress", "progressMax", "progressDesc", "openUI"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,W,OAAAA,W;;AACrBC,MAAAA,O;;AACEC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,S,kBAAAA,S;;AACAC,MAAAA,Q,kBAAAA,Q;;AACAC,MAAAA,U,kBAAAA,U;;AACAC,MAAAA,S,kBAAAA,S;;AACAC,MAAAA,M,kBAAAA,M;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBnB,U;;2BAGjBoB,S,WADZF,OAAO,CAAC,WAAD,C,UAKHC,QAAQ,CAAClB,KAAD,C,UAERkB,QAAQ,CAACjB,WAAD,C,UAERiB,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ,CAAClB,KAAD,C,2BAXb,MACamB,SADb;AAAA;AAAA,4BACsC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAY1BC,aAZ0B,GAYD,KAZC;AAAA;;AACd,eAANC,MAAM,GAAW;AAAE,iBAAO,qBAAP;AAA+B;;AAC1C,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,UAAf;AAA2B;;AACpC,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,QAAlB;AAA4B;;AAW1DC,QAAAA,MAAM,GAAS;AACrB;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,sCAAUC,WAAtB,EAAmC,KAAKC,aAAxC,EAAuD,IAAvD;AACA;AAAA;AAAA,oCAASF,EAAT,CAAY;AAAA;AAAA,8CAAcG,WAA1B,EAAuC,KAAKC,aAA5C,EAA2D,IAA3D;AACA,eAAKC,WAAL,CAAkBC,QAAlB,CAA2B,KAAKC,UAAhC,EAA4C,IAA5C;AACH;;AAEW,cAANC,MAAM,GAAG;AACX,eAAKN,aAAL,CAAmB;AAAA;AAAA,4CAAaO,UAAhC;AACH;;AAEW,cAANC,MAAM,CAACjB,aAAsB,GAAG,KAA1B,EAAgD;AACxD,eAAKA,aAAL,GAAqBA,aAArB;AACH;;AAEY,cAAPkB,OAAO,GAAkB;AAC3B;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AAEOR,QAAAA,aAAa,CAACS,IAAD,EAAkBC,EAAlB,EAAiC;AAClD,cAAIA,EAAE,KAAK;AAAA;AAAA,sCAAUC,IAArB,EAA2B;AACvB,iBAAKC,IAAL,CAAUC,MAAV,GAAmB,KAAnB;AACA;AACH;;AACD,cAAI,KAAKD,IAAL,CAAUC,MAAd,EAAsB;AACtB,eAAKD,IAAL,CAAUC,MAAV,GAAmB,IAAnB;AACH;;AAEOf,QAAAA,aAAa,CAACgB,SAAD,EAA0B;AAC3C,cAAIA,SAAS,KAAK;AAAA;AAAA,4CAAaT,UAA3B,IAAyC,KAAKhB,aAAlD,EAAiE;AAC7D;AACH;;AACD,gBAAM0B,QAAQ,GAAG;AAAA;AAAA,kCAAQC,IAAR,CAAaC,kBAAb,CAAgCH,SAAhC,EAA2CI,MAA3C,CAAkDC,CAAC,IAAIA,CAAC,CAACC,MAAF,KAAa;AAAA;AAAA,kCAAQC,IAAR,CAAaC,WAAb,CAAyBC,kBAA7F,EAAiHC,IAAjH,CAAsH,CAACC,CAAD,EAAIC,CAAJ,KAAUA,CAAC,CAACC,OAAF,GAAaF,CAAC,CAACE,OAA/I,CAAjB;;AACA,cAAIZ,QAAQ,CAACa,MAAT,IAAmB,CAAvB,EAA0B;AACtB,iBAAKhB,IAAL,CAAUC,MAAV,GAAmB,KAAnB;AACA;AACH;;AACD,gBAAMgB,QAAQ,GAAGd,QAAQ,CAAC,CAAD,CAAzB;AACA,gBAAMe,OAAO,GAAG;AAAA;AAAA,8BAAMC,WAAN,CAAkBC,SAAlB,CAA4BC,GAA5B,CAAgCJ,QAAQ,CAACF,OAAzC,CAAhB;;AACA,cAAI,CAACG,OAAL,EAAc;AACV;AAAA;AAAA,sCAAS,WAAT,EAAuB,WAAUD,QAAQ,CAACF,OAAQ,YAAlD;AACA;AACH;;AACD,eAAKf,IAAL,CAAUC,MAAV,GAAmB,IAAnB;AACA,gBAAMqB,IAAI,GAAG;AAAA;AAAA,kCAAQlB,IAAR,CAAamB,sBAAb,CAAoCL,OAApC,CAAb;AACA,eAAKM,QAAL,CAAeC,MAAf,GAAwBH,IAAI,CAACI,IAA7B;AACA,eAAKC,YAAL,CAAmBC,QAAnB,GAA8BX,QAAQ,CAACW,QAAT,GAAqBN,IAAI,CAACO,WAAxD;AACA,eAAKC,YAAL,CAAmBL,MAAnB,GAA6B,GAAER,QAAQ,CAACW,QAAU,IAAGN,IAAI,CAACO,WAAY,EAAtE;AACH;;AACOtC,QAAAA,UAAU,GAAG;AACjB;AAAA;AAAA,8BAAMwC,MAAN;AAAA;AAAA;AACH;;AAhEiC,O;;;;;iBAKD,I;;;;;;;iBAEU,I;;;;;;;iBAEF,I;;;;;;;iBAEJ,I", "sourcesContent": ["import { _decorator, Label, ProgressBar } from 'cc';\nimport csproto from 'db://assets/bundles/common/script/autogen/pb/cs_proto.js';\nimport { BaseUI, UILayer, UIMgr } from \"db://assets/scripts/core/base/UIMgr\";\nimport { logError } from 'db://assets/scripts/utils/Logger';\nimport { MyApp } from '../../app/MyApp';\nimport { ResTaskClass } from '../../autogen/luban/schema';\nimport { BundleName } from '../../const/BundleConst';\nimport { DataMgr } from '../../data/DataManager';\nimport { BottomUIEvent } from '../../event/BottomUIEvent';\nimport { DataEvent } from '../../event/DataEvent';\nimport { EventMgr } from '../../event/EventManager';\nimport { ButtonPlus } from '../common/components/button/ButtonPlus';\nimport { BottomTab } from '../home/<USER>';\nimport { TaskUI } from './TaskUI';\n\nconst { ccclass, property } = _decorator;\n\n@ccclass('TaskTipUI')\nexport class TaskTipUI extends BaseUI {\n    public static getUrl(): string { return \"prefab/ui/TaskTipUI\"; }\n    public static getLayer(): UILayer { return UILayer.Background }\n    public static getBundleName(): string { return BundleName.HomeTask }\n    @property(Label)\n    private taskDesc: Label | null = null;\n    @property(ProgressBar)\n    private taskProgress: ProgressBar | null = null;\n    @property(ButtonPlus)\n    private taskJumpBtn: ButtonPlus | null = null;\n    @property(Label)\n    private progressDesc: Label | null = null;\n    private _isHomeUIHide: boolean = false;\n\n    protected onLoad(): void {\n        EventMgr.on(DataEvent.TaskRefresh, this.onTaskRefresh, this);\n        EventMgr.on(BottomUIEvent.SwitchPanel, this.onSwitchPanel, this);\n        this.taskJumpBtn!.addClick(this.onTaskJump, this);\n    }\n\n    async onShow() {\n        this.onTaskRefresh(ResTaskClass.DAILY_TASK);\n    }\n\n    async onHide(_isHomeUIHide: boolean = false): Promise<void> {\n        this._isHomeUIHide = _isHomeUIHide\n    }\n\n    async onClose(): Promise<void> {\n        EventMgr.targetOff(this);\n    }\n\n    private onSwitchPanel(from: BottomTab, to: BottomTab) {\n        if (to !== BottomTab.Home) {\n            this.node.active = false;\n            return\n        }\n        if (this.node.active) return\n        this.node.active = true;\n    }\n\n    private onTaskRefresh(taskClass: ResTaskClass) {\n        if (taskClass !== ResTaskClass.DAILY_TASK || this._isHomeUIHide) {\n            return;\n        }\n        const taskList = DataMgr.task.getTaskListByClass(taskClass).filter(t => t.status === csproto.comm.TASK_STATUS.TASK_STATUS_NORMAL).sort((a, b) => b.task_id! - a.task_id!);\n        if (taskList.length == 0) {\n            this.node.active = false\n            return;\n        }\n        const taskInfo = taskList[0];\n        const resTask = MyApp.lubanTables.TbResTask.get(taskInfo.task_id!);\n        if (!resTask) {\n            logError(\"TaskTipUI\", `task id ${taskInfo.task_id} not found`);\n            return;\n        }\n        this.node.active = true;\n        const info = DataMgr.task.getTaskDescAndProgress(resTask);\n        this.taskDesc!.string = info.desc;\n        this.taskProgress!.progress = taskInfo.progress! / info.progressMax;\n        this.progressDesc!.string = `${taskInfo.progress!}/${info.progressMax}`;\n    }\n    private onTaskJump() {\n        UIMgr.openUI(TaskUI)\n    }\n}\n"]}