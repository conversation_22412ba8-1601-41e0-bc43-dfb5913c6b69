import { _decorator, Component, instantiate, Node, Prefab, UITransform, view } from "cc";
import { MyApp } from 'db://assets/bundles/common/script/app/MyApp';
import { LayerSplicingMode, LevelDataEvent, LevelDataLayer } from "../../../leveldata/leveldata";
import GameMapRun from "./GameMapRun";
import { LevelEventRun } from "./LevelEventRun"
import { Tools } from "../../utils/Tools";
import { GameIns } from "../../GameIns";

const { ccclass } = _decorator;

const TerrainsNodeName = "terrains";
const DynamicNodeName = "dynamic";
const ScrollsNodeName = "scrolls";
const EmittiersNodeName = "emittiers";

@ccclass('LevelLayerUI')
export class LevelLayerUI extends Component {
    public backgrounds: Prefab[] = [];
    private _offSetY: number = 0; // 当前关卡的偏移量
    private _bTrackBackground: boolean = true; // 是否跟随背景移动（预加载关卡未在显示区域的时候跟随）

    private terrainsNode: Node | null = null;
    private dynamicNode: Node | null = null;
    private scrollsNode: Node | null = null;
    private emittiersNode: Node | null = null;
    private events: LevelDataEvent[] = [];
    private eventRunners: LevelEventRun[] = [];

    public get TrackBackground(): boolean {
        return this._bTrackBackground;
    }
    public set TrackBackground(value: boolean) {
        this._bTrackBackground = value;
    }

    onLoad(): void {

    }

    public async initByLevelData(data: LevelDataLayer, offSetY: number): Promise<void> {
        this._offSetY = offSetY;
        this.node.setPosition(0, offSetY, 0);

        this.terrainsNode = this._getOrAddNode(this.node, TerrainsNodeName);
        this.dynamicNode = this._getOrAddNode(this.node, DynamicNodeName);
        this.scrollsNode = this._getOrAddNode(this.node, ScrollsNodeName);
        this.emittiersNode = this._getOrAddNode(this.node, EmittiersNodeName);

        this.backgrounds = [];

        data.terrains?.forEach((terrain) => {
            const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrain.uuid)
            MyApp.resMgr.load(path, (err: Error | null, prefab: Prefab) => {
                if (err) {
                    console.error('LevelLayerUI', " initByLevelData load terrain prefab err", err);
                    return;
                }
                var terrainNode = instantiate(prefab);
                terrainNode.setPosition(terrain.position.x, terrain.position.y, 0);
                terrainNode.setScale(terrain.scale.x, terrain.scale.y, 1);
                terrainNode.setRotationFromEuler(0, 0, terrain.rotation);
                this.terrainsNode!.addChild(terrainNode);
            });
        });

        await this._initDynamicsByLevelData(data);
        await this._initEmittierLevelData(data);
        await this._initScorllsByLevelData(data);
        
        this.events = [...data.events]
        this.events.sort((a, b) => a.position.y - b.position.y);
        this.events.forEach((event) => {
            this.eventRunners.push(new LevelEventRun(event, this));
        });
    }

    private async _initDynamicsByLevelData(data: LevelDataLayer): Promise<void> {
        if (!data || this.dynamicNode === null || data.dynamics.length === 0) { return; } 

        const loadPromises: Promise<void>[] = [];
        data.dynamics?.forEach((dynamics,index) => {  
            var dynaNode = this._getOrAddNode(this.dynamicNode!, `dyna_${index}`);
            let weights: number[] = [];
            dynamics.group.forEach((dynamic) => {
                weights.push(dynamic.weight);
            });
            const dynaIndex = Tools.getRandomIndexByWeights(weights, GameIns.battleManager.random());
            const dynamic = dynamics.group[dynaIndex];
             
            dynaNode.setPosition(dynamic.position.x, dynamic.position.y, 0);
            dynaNode.setScale(dynamic.scale.x, dynamic.scale.y, 1);
            dynaNode.setRotationFromEuler(0, 0, dynamic.rotation);

            weights = [];
            dynamic.terrains.forEach((terrain) => {
                weights.push(terrain.weight);
            });
            const terrainIndex = Tools.getRandomIndexByWeights(weights, GameIns.battleManager.random());
            const terrain = dynamic.terrains[terrainIndex];
            
            const loadPromise = new Promise<void>((resolve) => {
                const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrain.uuid)
                MyApp.resMgr.load(path, (err: Error | null, prefab: Prefab) => {
                    if (err) {
                        console.error("LevelEditorLayerUI initByLevelData load dynamic prefab err", err);
                        resolve();
                        return
                    } 
                    var dynamicNode = instantiate(prefab);
                    dynamicNode.name = `rand_${dynaIndex}_${terrainIndex}`;
                    dynaNode!.addChild(dynamicNode); 
                    const randomOffsetX = GameIns.battleManager.random() * (terrain.offSetX.max - terrain.offSetX.min) + terrain.offSetX.min;   
                    const randomOffsetY = GameIns.battleManager.random() * (terrain.offSetY.max - terrain.offSetY.min) + terrain.offSetY.min;
                    dynamicNode.setPosition(randomOffsetX, randomOffsetY, 0);
                    resolve();
                });
            });
            loadPromises.push(loadPromise);
        });

        await Promise.all(loadPromises);
    }

    private async _initEmittierLevelData(data: LevelDataLayer): Promise<void> {
        if (!data || this.emittiersNode === null || data.emittiers.length === 0) { return; } 

        const loadPromises: Promise<void>[] = [];    
        data.emittiers?.forEach((emittier, index) => {
            const loadPromise = new Promise<void>((resolve) => {
                const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, emittier.uuid);
                MyApp.resMgr.load(path, (err: Error | null, prefab: Prefab) => {
                    if (err) {
                        resolve();
                        return
                    }           
                    var emittierNode = instantiate(prefab);
                    emittierNode.name = `emittier_${index}`;
                    emittierNode.setPosition(emittier.position.x, emittier.position.y, 0);
                    emittierNode.setScale(emittier.scale.x, emittier.scale.y, 1);
                    emittierNode.setRotationFromEuler(0, 0, emittier.rotation);
                    this.emittiersNode!.addChild(emittierNode); 
                    resolve();
                });
            });
            loadPromises.push(loadPromise);
        });

        await Promise.all(loadPromises);  
    }

    public async _initScorllsByLevelData(data: LevelDataLayer):Promise<void> {
        if (!data || this.scrollsNode === null || data.scrolls.length === 0) { return; } 

        // 根据权重随机出一个滚动组
        const weights: number[] = [];
        data.scrolls.forEach(element => {
            weights.push(element.weight);
        });
        const srocllIndex = Tools.getRandomIndexByWeights(weights, GameIns.battleManager.random());
        const scroll = data.scrolls[srocllIndex];

        const loadPromises: Promise<void>[] = [];
        let prefabList: Prefab[] = [];
        // 先加载成功所有预制体
        scroll.uuids.forEach((uuid) => {
            const loadPromise = new Promise<void>((resolve) => {
                const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, uuid);
                MyApp.resMgr.load(path, (err: Error | null, prefab: Prefab) => {
                    if (err) {
                        resolve();
                        return;
                    }        
                    prefabList.push(prefab);   
                    resolve();
                });
            });
            loadPromises.push(loadPromise);
        });

        await Promise.all(loadPromises); 
        
        const scrollsNode = this._getOrAddNode(this.scrollsNode!, `scroll_${srocllIndex}`);
        var totalHeight = data.speed * data.totalTime / 1000;
        var posOffsetY = 0;
        var height = 0;
        let prefabIndex = 0; // 当前使用的 prefab 索引
        while (height < totalHeight) {
            // 循环使用 prefab
            const curPrefab = prefabList[prefabIndex];
            const child = instantiate(curPrefab);
            const randomOffsetX = GameIns.battleManager.random() * (scroll.offSetX!.max - scroll.offSetX!.min) + scroll.offSetX!.min;
            child.setPosition(randomOffsetX, posOffsetY, 0);
            var offY = 0;
            if (scroll.splicingMode === LayerSplicingMode.node_height) {    
                offY = child.getComponent(UITransform)!.contentSize.height;
            } else if (scroll.splicingMode === LayerSplicingMode.fix_height) {
                offY = 1334;
            } else if (scroll.splicingMode === LayerSplicingMode.random_height) {
                offY = Math.max(scroll.offSetY!.min,scroll.offSetY!.max) + child.getComponent(UITransform)!.contentSize.height;
            }
            scrollsNode.addChild(child);
            posOffsetY += offY;
            height += offY;
            prefabIndex = (prefabIndex + 1) % scroll.uuids.length;
        }
    }

    public tick(deltaTime: number, speed: number): void {
        let posY = this.node.position.y;
        if (this.TrackBackground === true) {
            const topPosY = view.getVisibleSize().height / 2;
            if (posY < topPosY) {
                this._bTrackBackground = false;
            }
        }
        posY -= deltaTime * speed;
        this.node.setPosition(0, posY, 0);

        // 说明: event的激活，是从进入世界范围开始。
        // 又：因为游戏是从上往下滚动，因为我们用一个固定大小变量定义上边界，一旦进入这个范围，就激活。
        const scrollY =  -posY + this._offSetY + GameMapRun.VIEWPORT_TOP;
        // console.log('LevelLayerUI tick scrollY', scrollY, -posY, this._offSetY, GameMapRun.VIEWPORT_TOP);
        for (let i = 0; i < this.eventRunners.length; i++) {
            const eventRunner = this.eventRunners[i];
            eventRunner.tick(scrollY);
            if (eventRunner.isTriggered) {
                // 条件已触发
                this.eventRunners.splice(i, 1);
                i--;
            }
        }
    }

    // 动态实例化场景元素，当元素的位置在在一个屏幕以上位置是，就实例化
    private _instantiateTerrain() {
        
    }

    private _getOrAddNode(node_parent: Node, name: string): Node {
        var node = node_parent.getChildByName(name);
        if (node == null) {
            node = new Node(name);
            node_parent.addChild(node);
        }
        return node;
    }

    public getEventByElemID(elemID: string): LevelDataEvent | null {
        for (let event of this.events) {
            if (event.elemID == elemID) {
                return event;
            }
        }
        return null;
    }
}