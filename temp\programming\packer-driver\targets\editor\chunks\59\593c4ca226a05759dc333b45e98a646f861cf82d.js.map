{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/move/CameraMove.ts"], "names": ["_decorator", "CCFloat", "Component", "Vec3", "view", "logInfo", "GameConst", "GameIns", "GameEnum", "ccclass", "property", "CameraMove", "_screenRatio", "_battleWidth", "_currentPosition", "_targetPosition", "start", "node", "getPosition", "set", "_calculateScreenRatio", "update", "deltaTime", "gameStateManager", "gameState", "GameState", "Battle", "mainPlane", "mainPlaneManager", "planePos", "position", "targetX", "calculateTargetPosition", "x", "smoothMoveCamera", "distanceToTarget", "desiredMove", "followFactor", "maxMove", "maxMoveSpeed", "actualMove", "Math", "abs", "sign", "newX", "setPosition", "planeX", "relativeX", "cameraRatio", "max", "min", "setCameraPosition", "forceMoveToTarget", "resetCamera", "visibleSize", "getVisibleSize", "width", "designWidth", "ViewBattleWidth"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,O,OAAAA,O;AAASC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;;AACtCC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Q,iBAAAA,Q;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBV,U;;4BAGjBW,U,WADZF,OAAO,CAAC,YAAD,C,UAWHC,QAAQ,CAACT,OAAD,C,UAGRS,QAAQ,CAACT,OAAD,C,2BAdb,MACaU,UADb,SACgCT,SADhC,CAC0C;AAAA;AAAA;;AAEtC;AACJ;AACA;AACA;AACA;AACA;AACA;AAR0C;;AAWX;AAXW;;AAcX;AAdW,eAgB9BU,YAhB8B,GAgBP,CAhBO;AAAA,eAiB9BC,YAjB8B,GAiBP,CAjBO;AAAA,eAmB9BC,gBAnB8B,GAmBL,IAAIX,IAAJ,EAnBK;AAmBO;AAnBP,eAoB9BY,eApB8B,GAoBN,IAAIZ,IAAJ,EApBM;AAAA;;AAoBM;AAE5Ca,QAAAA,KAAK,GAAG;AACJ;AACA,eAAKC,IAAL,CAAUC,WAAV,CAAsB,KAAKJ,gBAA3B;;AACA,eAAKC,eAAL,CAAqBI,GAArB,CAAyB,KAAKL,gBAA9B;;AACA,eAAKM,qBAAL;AACH;;AAEDC,QAAAA,MAAM,CAACC,SAAD,EAAoB;AACtB,cAAI;AAAA;AAAA,kCAAQC,gBAAR,CAAyBC,SAAzB,IAAsC;AAAA;AAAA,oCAASC,SAAT,CAAmBC,MAA7D,EAAqE;AACjE,mBADiE,CACzD;AACX;;AAED,cAAIC,SAAS,GAAG;AAAA;AAAA,kCAAQC,gBAAR,CAAyBD,SAAzC;AACA,cAAI,CAACA,SAAL,EAAgB,OANM,CAQtB;;AACA,gBAAME,QAAQ,GAAGF,SAAS,CAACV,IAAV,CAAea,QAAhC,CATsB,CAWtB;;AACA,gBAAMC,OAAO,GAAG,KAAKC,uBAAL,CAA6BH,QAAQ,CAACI,CAAtC,CAAhB;;AACA,eAAKlB,eAAL,CAAqBI,GAArB,CAAyB,KAAKL,gBAA9B;;AACA,eAAKC,eAAL,CAAqBkB,CAArB,GAAyBF,OAAzB,CAdsB,CAgBtB;;AACA,eAAKG,gBAAL,CAAsBZ,SAAtB;AACH;AAED;AACJ;AACA;AACA;;;AACYY,QAAAA,gBAAgB,CAACZ,SAAD,EAAoB;AACxC;AACA,eAAKL,IAAL,CAAUC,WAAV,CAAsB,KAAKJ,gBAA3B,EAFwC,CAIxC;;AACA,gBAAMqB,gBAAgB,GAAG,KAAKpB,eAAL,CAAqBkB,CAArB,GAAyB,KAAKnB,gBAAL,CAAsBmB,CAAxE,CALwC,CAOxC;;AACA,gBAAMG,WAAW,GAAGD,gBAAgB,GAAG,KAAKE,YAA5C,CARwC,CAUxC;;AACA,gBAAMC,OAAO,GAAG,KAAKC,YAAL,GAAoBjB,SAApC,CAXwC,CAaxC;;AACA,cAAIkB,UAAU,GAAGJ,WAAjB;;AACA,cAAIK,IAAI,CAACC,GAAL,CAASN,WAAT,IAAwBE,OAA5B,EAAqC;AACjCE,YAAAA,UAAU,GAAGC,IAAI,CAACE,IAAL,CAAUP,WAAV,IAAyBE,OAAtC;AACH,WAjBuC,CAmBxC;;;AACA,gBAAMM,IAAI,GAAG,KAAK9B,gBAAL,CAAsBmB,CAAtB,GAA0BO,UAAvC,CApBwC,CAsBxC;;AACA,eAAK1B,gBAAL,CAAsBmB,CAAtB,GAA0BW,IAA1B;AACA,eAAK3B,IAAL,CAAU4B,WAAV,CAAsB,KAAK/B,gBAA3B;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACYkB,QAAAA,uBAAuB,CAACc,MAAD,EAAyB;AACpD;AACA,gBAAMC,SAAS,GAAGD,MAAM,GAAI,CAAC,KAAKjC,YAAN,GAAqB,CAAjD,CAFoD,CAEC;AAErD;;AACA,cAAImC,WAAW,GAAGD,SAAS,GAAG,KAAKlC,YAAnC,CALoD,CAOpD;;AACAmC,UAAAA,WAAW,GAAGP,IAAI,CAACQ,GAAL,CAAS,GAAT,EAAcR,IAAI,CAACS,GAAL,CAAS,GAAT,EAAcF,WAAd,CAAd,CAAd,CARoD,CAUpD;;AACA,iBAAQA,WAAW,GAAG,GAAf,GAAsB,GAA7B;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACYG,QAAAA,iBAAiB,CAACpB,OAAD,EAAkBe,MAAlB,EAAkC;AACvD;AACA,eAAK7B,IAAL,CAAUC,WAAV,CAAsB,KAAKJ,gBAA3B,EAFuD,CAIvD;;AACA,gBAAM8B,IAAI,GAAG,KAAK9B,gBAAL,CAAsBmB,CAAtB,IAA2B,IAAI,KAAKI,YAApC,IACTN,OAAO,GAAG,KAAKM,YADnB,CALuD,CAQvD;;AACA,eAAKvB,gBAAL,CAAsBmB,CAAtB,GAA0BW,IAA1B;AACA,eAAK3B,IAAL,CAAU4B,WAAV,CAAsB,KAAK/B,gBAA3B,EAVuD,CAWvD;AAEA;AACA;AACH;AACD;AACJ;AACA;;;AACWsC,QAAAA,iBAAiB,GAAG;AACvB,eAAKnC,IAAL,CAAU4B,WAAV,CAAsB,KAAK9B,eAA3B;;AACA,eAAKD,gBAAL,CAAsBK,GAAtB,CAA0B,KAAKJ,eAA/B;AACH;AAED;AACJ;AACA;;;AACWsC,QAAAA,WAAW,GAAG;AACjB;AACA,eAAKpC,IAAL,CAAU4B,WAAV,CAAsB,CAAtB,EAAyB,CAAzB,EAA4B,CAA5B;;AACA,eAAK/B,gBAAL,CAAsBK,GAAtB,CAA0B,CAA1B,EAA6B,CAA7B,EAAgC,CAAhC;;AACA,eAAKJ,eAAL,CAAqBI,GAArB,CAAyB,CAAzB,EAA4B,CAA5B,EAA+B,CAA/B;AACH;AAED;AACJ;AACA;;;AACYC,QAAAA,qBAAqB,GAAG;AAC5B,gBAAMkC,WAAW,GAAGlD,IAAI,CAACmD,cAAL,EAApB;AACA,eAAK3C,YAAL,GAAoB0C,WAAW,CAACE,KAAZ,GAAoB;AAAA;AAAA,sCAAUC,WAAlD,CAF4B,CAI5B;;AACA,eAAK5C,YAAL,GAAoB;AAAA;AAAA,sCAAU6C,eAAV,GAA4B,KAAK9C,YAArD;AACA;AAAA;AAAA,kCAAQ,YAAR,EAAuB,SAAQ,KAAKA,YAAa,WAAU,KAAKC,YAAa,EAA7E;AACH;;AApJqC,O;;;;;iBAWhB,G;;;;;;;iBAGA,G", "sourcesContent": ["import { _decorator, CCFloat, Component, Vec3, view } from \"cc\";\r\nimport { logInfo } from \"db://assets/scripts/utils/Logger\";\r\nimport { GameConst } from \"../../../../../scripts/core/base/GameConst\";\r\nimport { GameIns } from \"../GameIns\";\r\nimport { GameEnum } from \"../const/GameEnum\";\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('CameraMove')\r\nexport class CameraMove extends Component {\r\n\r\n    /* 参数组合效果示例\r\n     * 场景\t    followFactor  maxMoveSpeed  摄像机行为\r\n     * 平滑跟随\t0.3\t          100\t        平滑移动，不会突然加速\r\n     * 快速响应\t0.7\t          200\t        快速接近目标，但不会跳跃\r\n     * 精确控制\t0.5\t          80\t        平衡响应速度和平滑度\r\n     * 电影效果\t0.2\t          50\t        非常平滑的移动，适合过场动画\r\n    */\r\n\r\n    @property(CCFloat)\r\n    public followFactor = 0.7; // 跟随比例因子 (0.0-1.0)\r\n\r\n    @property(CCFloat)\r\n    public maxMoveSpeed = 200; // 摄像机最大移动速度（单位：世界单位/秒）\r\n\r\n    private _screenRatio: number = 1;\r\n    private _battleWidth: number = 0;\r\n\r\n    private _currentPosition: Vec3 = new Vec3(); // 当前位置\r\n    private _targetPosition: Vec3 = new Vec3(); // 目标位置\r\n\r\n    start() {\r\n        // 初始化位置\r\n        this.node.getPosition(this._currentPosition);\r\n        this._targetPosition.set(this._currentPosition);\r\n        this._calculateScreenRatio();\r\n    }\r\n\r\n    update(deltaTime: number) {\r\n        if (GameIns.gameStateManager.gameState != GameEnum.GameState.Battle) {\r\n            return; // 游戏未进入战斗状态时不处理\r\n        }\r\n\r\n        let mainPlane = GameIns.mainPlaneManager.mainPlane;\r\n        if (!mainPlane) return;\r\n\r\n        // 获取飞机位置\r\n        const planePos = mainPlane.node.position;\r\n\r\n        // 计算摄像机应该跟随的目标位置\r\n        const targetX = this.calculateTargetPosition(planePos.x);\r\n        this._targetPosition.set(this._currentPosition);\r\n        this._targetPosition.x = targetX;\r\n\r\n        // 平滑移动摄像机（考虑最大移动速度）\r\n        this.smoothMoveCamera(deltaTime);\r\n    }\r\n\r\n    /**\r\n     * 平滑移动摄像机（考虑最大移动速度）\r\n     * @param deltaTime 帧时间（秒）\r\n     */\r\n    private smoothMoveCamera(deltaTime: number) {\r\n        // 获取当前位置\r\n        this.node.getPosition(this._currentPosition);\r\n\r\n        // 计算当前位置到目标位置的距离\r\n        const distanceToTarget = this._targetPosition.x - this._currentPosition.x;\r\n\r\n        // 计算期望移动距离（基于平滑因子）\r\n        const desiredMove = distanceToTarget * this.followFactor;\r\n\r\n        // 计算最大允许移动距离（基于最大速度）\r\n        const maxMove = this.maxMoveSpeed * deltaTime;\r\n\r\n        // 应用移动速度限制\r\n        let actualMove = desiredMove;\r\n        if (Math.abs(desiredMove) > maxMove) {\r\n            actualMove = Math.sign(desiredMove) * maxMove;\r\n        }\r\n\r\n        // 计算新位置\r\n        const newX = this._currentPosition.x + actualMove;\r\n\r\n        // 设置新位置（只修改X轴）\r\n        this._currentPosition.x = newX;\r\n        this.node.setPosition(this._currentPosition);\r\n    }\r\n\r\n    /**\r\n     * 计算摄像机目标位置\r\n     * @param planeX 飞机X坐标\r\n     * @returns 摄像机目标X坐标\r\n     */\r\n    private calculateTargetPosition(planeX: number): number {\r\n        // 计算飞机在战斗区域中的位置比例\r\n        const relativeX = planeX - (-this._battleWidth / 2); // 左边界为 -battleWidth/2\r\n\r\n        // 计算摄像机偏移比例 (0.0 到 1.0)\r\n        let cameraRatio = relativeX / this._battleWidth;\r\n\r\n        // 限制在 0.0 到 1.0 范围内\r\n        cameraRatio = Math.max(0.0, Math.min(1.0, cameraRatio));\r\n\r\n        // 返回摄像机目标位置（-100% 到 100%）\r\n        return (cameraRatio * 200) - 100;\r\n    }\r\n\r\n    /**\r\n     * 设置摄像机位置\r\n     * @param targetX 目标X坐标\r\n     * @param planeX 飞机X坐标（用于日志记录）\r\n     */\r\n    private setCameraPosition(targetX: number, planeX: number) {\r\n        // 获取当前位置\r\n        this.node.getPosition(this._currentPosition);\r\n\r\n        // 计算新位置（按比例平滑移动）\r\n        const newX = this._currentPosition.x * (1 - this.followFactor) +\r\n            targetX * this.followFactor;\r\n\r\n        // 设置新位置（只修改X轴）\r\n        this._currentPosition.x = newX;\r\n        this.node.setPosition(this._currentPosition);\r\n        // logInfo('CameraMove', `摄像机当前位置: ${this._currentPosition.x} name: ${this.node.name}`);\r\n\r\n        // // 调试日志（现在可以正确使用 planeX）\r\n        // logInfo('CameraMove', ` 飞机位置:${planeX}, 目标位置:${targetX}, 新位置:${newX}`);\r\n    }\r\n    /**\r\n     * 强制移动摄像机到目标位置\r\n     */\r\n    public forceMoveToTarget() {\r\n        this.node.setPosition(this._targetPosition);\r\n        this._currentPosition.set(this._targetPosition);\r\n    }\r\n\r\n    /**\r\n     * 重置摄像机状态\r\n     */\r\n    public resetCamera() {\r\n        // 重置位置到初始状态\r\n        this.node.setPosition(0, 0, 0);\r\n        this._currentPosition.set(0, 0, 0);\r\n        this._targetPosition.set(0, 0, 0);\r\n    }\r\n\r\n    /**\r\n     * 计算屏幕适配比例\r\n     */\r\n    private _calculateScreenRatio() {\r\n        const visibleSize = view.getVisibleSize();\r\n        this._screenRatio = visibleSize.width / GameConst.designWidth;\r\n\r\n        // 计算实际战斗宽度（考虑宽屏适配）\r\n        this._battleWidth = GameConst.ViewBattleWidth * this._screenRatio;\r\n        logInfo('CameraMove', `屏幕比例: ${this._screenRatio}, 战斗宽度: ${this._battleWidth}`);\r\n    }\r\n}"]}