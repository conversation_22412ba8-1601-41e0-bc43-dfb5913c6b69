import { _decorator, ccenum, Component, director, LabelComponent, ProgressBar } from 'cc';
import { WECHAT } from "cc/env";

import "../AAA/init_cs_proto.js";
import { initBundle } from '../core/base/Bundle';
import { UIMgr } from "../core/base/UIMgr";
import { logDebug } from "../utils/Logger";
const { ccclass, property } = _decorator;

if (WECHAT) {
    var warnCustom = console.warn;
    console.warn = function (res) {
        if (typeof res == "string" && res.indexOf("文件路径在真机上可能无法读取") > -1) {
            return;
        } else {
            warnCustom(res)
        }


    }
    var groupCustom = console.group;
    console.group = function (res) {
        if (typeof res == "string" && res.indexOf("读取文件/文件夹警告") > -1) {
            return;
        } else {
            groupCustom(res)
        }
    }
}

enum GameLogLevel {
    TRACE = 0,
    DEBUG = 1,
    LOG = 2,
    INFO = 3,
    WARN = 4,
    ERROR = 5,
}
ccenum(GameLogLevel)

@ccclass("ResUpdate")
export class ResUpdate extends Component {
    /* class member could be defined like this */
    // dummy = '';

    @property(LabelComponent)
    private countLabel: LabelComponent | null = null;
    @property(LabelComponent)
    private perLabel: LabelComponent | null = null;
    @property(LabelComponent)
    private versionLabel: LabelComponent | null = null;
    @property(ProgressBar)
    private loadingBar: ProgressBar | null = null;

    async start() {
        UIMgr.initializeLayers();

        //!todo 获取资源版本号
        director.preloadScene("Main", this.OnLoadProgress.bind(this), async () => {
            // dev 先load login UI
            await initBundle("common")
            director.loadScene("Main")
        });
    }

    OnLoadProgress(completedCount: number, totalCount: number) {
        let progress = (completedCount / totalCount)
        logDebug("ResUpdate", `load main scene, ${completedCount}, ${totalCount}, ${(progress * 100).toFixed(2)}`)
        if (this.node == null) {
            return;
        }
        this.perLabel!.string = (progress * 100).toFixed(2) + "%"
        this.countLabel!.string = '加载中...(' + completedCount + '/' + totalCount + ')'
        this.loadingBar!.progress = progress
    }

    onLoad() {
    }
    onDestroy() {
    }

    update(deltaTime: number) {
        //if (this.isMainSceneLoaded) //&& window.MyApp && window.MyApp.getLogin().isLogined)
        //{
        //    director.loadScene("MainScene")
        //}
        // Your update function goes here.
    }
}
