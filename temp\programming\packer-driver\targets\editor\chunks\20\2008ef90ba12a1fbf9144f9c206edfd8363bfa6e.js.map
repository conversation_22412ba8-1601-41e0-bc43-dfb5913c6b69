{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/wave/FormationPointEditor.ts"], "names": ["_decorator", "Color", "Component", "RichText", "Vec3", "Graphics", "VerticalTextAlignment", "HorizontalTextAlignment", "FormationPoint", "ccclass", "playOnFocus", "executeInEditMode", "property", "disallowMultiple", "menu", "requireComponent", "FormationPointEditor", "_graphics", "_richText", "_cachedIndex", "selected", "graphics", "node", "getComponent", "addComponent", "richText", "fontSize", "verticalAlign", "CENTER", "horizontalAlign", "fontColor", "BLACK", "onFocusInEditor", "onLostFocusInEditor", "formationPoint", "point", "x", "position", "y", "value", "update", "dt", "clear", "color", "YELLOW", "WHITE", "fillColor", "lineWidth", "circle", "fill", "stroke", "siblingIndex", "getSiblingIndex", "name", "string"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAyBC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,S,OAAAA,S;AAAsBC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,Q,OAAAA,Q;AAAoBC,MAAAA,qB,OAAAA,qB;AAAuBC,MAAAA,uB,OAAAA,uB;;AAGjGC,MAAAA,c,iBAAAA,c;;;;;;;;;OAFnB;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,WAAX;AAAwBC,QAAAA,iBAAxB;AAA2CC,QAAAA,QAA3C;AAAqDC,QAAAA,gBAArD;AAAuEC,QAAAA,IAAvE;AAA6EC,QAAAA;AAA7E,O,GAAmGf,U;;sCAS5FgB,oB,WALZP,OAAO,CAAC,sBAAD,C,UACPK,IAAI,CAAC,YAAD,C,UACJC,gBAAgB,CAACV,QAAD,C,UAChBM,iBAAiB,CAAC,IAAD,C,UACjBE,gBAAgB,CAAC,IAAD,C,4EAJjB,MAKaG,oBALb,SAK0Cd,SAL1C,CAKoD;AAAA;AAAA;AAAA,eACxCe,SADwC,GACb,IADa;AAAA,eAUxCC,SAVwC,GAUb,IAVa;AAAA,eAwBxCC,YAxBwC,GAwBjB,CAAC,CAxBgB;AAAA,eAyBxCC,QAzBwC,GAyBpB,KAzBoB;AAAA;;AAE7B,YAARC,QAAQ,GAAa;AAC5B,cAAI,CAAC,KAAKJ,SAAV,EAAqB;AACjB,iBAAKA,SAAL,GAAiB,KAAKK,IAAL,CAAUC,YAAV,CAAuBlB,QAAvB,KAAoC,KAAKiB,IAAL,CAAUE,YAAV,CAAuBnB,QAAvB,CAArD;AACH;;AAED,iBAAO,KAAKY,SAAZ;AACH;;AAGkB,YAARQ,QAAQ,GAAa;AAC5B,cAAI,CAAC,KAAKP,SAAV,EAAqB;AACjB,iBAAKA,SAAL,GAAiB,KAAKI,IAAL,CAAUC,YAAV,CAAuBpB,QAAvB,KAAoC,KAAKmB,IAAL,CAAUE,YAAV,CAAuBrB,QAAvB,CAArD,CADiB,CAEjB;;AACA,iBAAKe,SAAL,CAAeQ,QAAf,GAA0B,EAA1B;AACA,iBAAKR,SAAL,CAAeS,aAAf,GAA+BrB,qBAAqB,CAACsB,MAArD;AACA,iBAAKV,SAAL,CAAeW,eAAf,GAAiCtB,uBAAuB,CAACqB,MAAzD;AACA,iBAAKV,SAAL,CAAeY,SAAf,GAA2B7B,KAAK,CAAC8B,KAAjC;AACH;;AAED,iBAAO,KAAKb,SAAZ;AACH;;AAIMc,QAAAA,eAAe,GAAS;AAC3B,eAAKZ,QAAL,GAAgB,IAAhB;AACH;;AACMa,QAAAA,mBAAmB,GAAS;AAC/B,eAAKb,QAAL,GAAgB,KAAhB;AACH;;AAEwB,YAAdc,cAAc,GAAmB;AACxC,cAAIC,KAAK,GAAG;AAAA;AAAA,iDAAZ;AACAA,UAAAA,KAAK,CAACC,CAAN,GAAU,KAAKd,IAAL,CAAUe,QAAV,CAAmBD,CAA7B;AACAD,UAAAA,KAAK,CAACG,CAAN,GAAU,KAAKhB,IAAL,CAAUe,QAAV,CAAmBC,CAA7B;AACA,iBAAOH,KAAP;AACH;;AAEwB,YAAdD,cAAc,CAACK,KAAD,EAAwB;AAC7C,eAAKjB,IAAL,CAAUe,QAAV,GAAqB,IAAIjC,IAAJ,CAASmC,KAAK,CAACH,CAAf,EAAkBG,KAAK,CAACD,CAAxB,EAA2B,CAA3B,CAArB;AACH;;AAEME,QAAAA,MAAM,CAACC,EAAD,EAAa;AACtB,gBAAMpB,QAAQ,GAAG,KAAKA,QAAtB;AACAA,UAAAA,QAAQ,CAACqB,KAAT;AAEA,gBAAMC,KAAK,GAAG,KAAKvB,QAAL,GAAgBnB,KAAK,CAAC2C,MAAtB,GAA+B3C,KAAK,CAAC4C,KAAnD;AACAxB,UAAAA,QAAQ,CAACyB,SAAT,GAAqBH,KAArB;AACAtB,UAAAA,QAAQ,CAAC0B,SAAT,GAAqB,CAArB;AACA1B,UAAAA,QAAQ,CAAC2B,MAAT,CAAgB,CAAhB,EAAmB,CAAnB,EAAsB,EAAtB;AACA3B,UAAAA,QAAQ,CAAC4B,IAAT;AACA5B,UAAAA,QAAQ,CAAC6B,MAAT;AAEA,gBAAMC,YAAY,GAAG,KAAK7B,IAAL,CAAU8B,eAAV,EAArB;;AACA,cAAID,YAAY,KAAK,KAAKhC,YAA1B,EAAwC;AACpC,iBAAKA,YAAL,GAAoBgC,YAApB;AACA,iBAAK7B,IAAL,CAAU+B,IAAV,GAAkB,SAAQF,YAAa,EAAvC;AACA,iBAAK1B,QAAL,CAAc6B,MAAd,GAAwB,GAAEH,YAAa,EAAvC;AACH;AACJ;;AA7D+C,O", "sourcesContent": ["import { _decorator, instantiate, Color, Component, JsonAsset, RichText, Vec3, Graphics, CCObject, VerticalTextAlignment, HorizontalTextAlignment } from 'cc';\r\nconst { ccclass, playOnFocus, executeInEditMode, property, disallowMultiple, menu, requireComponent  } = _decorator;\r\nimport { EDITOR } from 'cc/env';\r\nimport { FormationGroup, FormationPoint, SpawnGroup } from 'db://assets/bundles/common/script/game/data/WaveData';\r\n\r\n@ccclass('FormationPointEditor')\r\n@menu(\"怪物/编辑器/阵型点\")\r\n@requireComponent(Graphics)\r\n@executeInEditMode(true)\r\n@disallowMultiple(true)\r\nexport class FormationPointEditor extends Component {\r\n    private _graphics: Graphics|null = null;\r\n    public get graphics(): Graphics {\r\n        if (!this._graphics) {\r\n            this._graphics = this.node.getComponent(Graphics) || this.node.addComponent(Graphics);\r\n        }\r\n\r\n        return this._graphics;\r\n    }\r\n\r\n    private _richText: RichText|null = null;\r\n    public get richText(): RichText {\r\n        if (!this._richText) {\r\n            this._richText = this.node.getComponent(RichText) || this.node.addComponent(RichText);\r\n            // this._richText.hideFlags = CCObject.Flags.AllHideMasks;\r\n            this._richText.fontSize = 16;\r\n            this._richText.verticalAlign = VerticalTextAlignment.CENTER;\r\n            this._richText.horizontalAlign = HorizontalTextAlignment.CENTER;\r\n            this._richText.fontColor = Color.BLACK;\r\n        }\r\n\r\n        return this._richText;\r\n    }\r\n\r\n    private _cachedIndex: number = -1;    \r\n    private selected: boolean = false;\r\n    public onFocusInEditor(): void {\r\n        this.selected = true;\r\n    }\r\n    public onLostFocusInEditor(): void {\r\n        this.selected = false;\r\n    }\r\n\r\n    public get formationPoint(): FormationPoint {\r\n        let point = new FormationPoint();\r\n        point.x = this.node.position.x;\r\n        point.y = this.node.position.y;\r\n        return point;\r\n    }\r\n\r\n    public set formationPoint(value: FormationPoint) {\r\n        this.node.position = new Vec3(value.x, value.y, 0);\r\n    }\r\n\r\n    public update(dt: number) {\r\n        const graphics = this.graphics;\r\n        graphics.clear();\r\n        \r\n        const color = this.selected ? Color.YELLOW : Color.WHITE;\r\n        graphics.fillColor = color;\r\n        graphics.lineWidth = 2;\r\n        graphics.circle(0, 0, 10);\r\n        graphics.fill();\r\n        graphics.stroke();\r\n\r\n        const siblingIndex = this.node.getSiblingIndex();\r\n        if (siblingIndex !== this._cachedIndex) {\r\n            this._cachedIndex = siblingIndex;\r\n            this.node.name = `Point_${siblingIndex}`;\r\n            this.richText.string = `${siblingIndex}`;\r\n        }\r\n    }\r\n}"]}