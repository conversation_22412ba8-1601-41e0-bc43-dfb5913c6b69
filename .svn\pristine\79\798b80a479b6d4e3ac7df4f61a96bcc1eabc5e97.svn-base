import { _decorator, CCBoolean, CCFloat, CC<PERSON><PERSON>ger, Component } from "cc";
import PlaneBase from "./PlaneBase";
const { ccclass, property } = _decorator;

@ccclass("PlaneBaseDebug")
export default class PlaneBaseDebug extends Component{
    protected _planeBase: PlaneBase | null = null;
    start() {
        this._planeBase = this.node.getComponent(PlaneBase);
    }
    @property(CCFloat)
    get maxHP():number{
        return this._planeBase?.maxHp || 0;
    }
    @property(CCFloat)
    get hpRecovery():number {
        return this._planeBase?.attribute.getHPRecovery()||0;
    }
    @property(CCFloat)
    get curHP():number {
        return this._planeBase?.curHp || 0;
    }
    @property(CCBoolean)
    get isDead():boolean {
        return this._planeBase?.isDead || false;
    }
    @property(CCFloat)
    get attack() {
        return this._planeBase?.getAttack() || 0;
    }

    @property({
        type: [CCInteger]
    })
    get bufferList():number[] {
        if (!this._planeBase || !this._planeBase.buffComp) {
            return [];
        }
        let list:number[] = [];
        this._planeBase.buffComp.buffs.forEach((buff) => {
            list.push(buff.res.id);
        })
        return list;
    }

    @property(CCInteger)
    applyBuffID:number = 0;
    @property(CCBoolean)
    get applyBuff() {
        return false;
    }
    set applyBuff(value:boolean) {
        if (!value) {
            return
        }
        this._planeBase?.buffComp.ApplyBuff(false, this.applyBuffID);
    }
}