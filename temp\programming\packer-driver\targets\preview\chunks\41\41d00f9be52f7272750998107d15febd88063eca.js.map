{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/GameIns.ts"], "names": ["_GameIns", "_battleManager", "_boss<PERSON><PERSON><PERSON>", "_enemyManager", "_gameStateManager", "_hurtEffectManager", "_mainPlaneManager", "_gamePlaneManager", "_waveManager", "_fColliderManager", "_gameDataManager", "battleManager", "boss<PERSON><PERSON><PERSON>", "enemyManager", "gameStateManager", "hurtEffectManager", "mainPlaneManager", "gamePlaneManager", "waveManager", "fColliderManager", "gameDataManager", "GameIns"], "mappings": ";;;8BAWMA,Q;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAAA,MAAAA,Q,GAAN,MAAMA,QAAN,CAAe;AAAA;AAAA,eACXC,cADW,GAC4B,IAD5B;AAAA,eAGXC,YAHW,GAGwB,IAHxB;AAAA,eAKXC,aALW,GAK0B,IAL1B;AAAA,eAOXC,iBAPW,GAOkC,IAPlC;AAAA,eASXC,kBATW,GASoC,IATpC;AAAA,eAWXC,iBAXW,GAWkC,IAXlC;AAAA,eAaXC,iBAbW,GAakC,IAblC;AAAA,eAeXC,YAfW,GAewB,IAfxB;AAAA,eAiBXC,iBAjBW,GAiBkC,IAjBlC;AAAA,eAmBXC,gBAnBW,GAmBgC,IAnBhC;AAAA;;AAEM,YAAbC,aAAa,GAAG;AAAE,iBAAO,KAAKV,cAAZ;AAA8B;;AAErC,YAAXW,WAAW,GAAG;AAAE,iBAAO,KAAKV,YAAZ;AAA4B;;AAEhC,YAAZW,YAAY,GAAG;AAAE,iBAAO,KAAKV,aAAZ;AAA6B;;AAE9B,YAAhBW,gBAAgB,GAAG;AAAE,iBAAO,KAAKV,iBAAZ;AAAiC;;AAErC,YAAjBW,iBAAiB,GAAG;AAAE,iBAAO,KAAKV,kBAAZ;AAAkC;;AAExC,YAAhBW,gBAAgB,GAAG;AAAE,iBAAO,KAAKV,iBAAZ;AAAiC;;AAEtC,YAAhBW,gBAAgB,GAAG;AAAE,iBAAO,KAAKV,iBAAZ;AAAiC;;AAE3C,YAAXW,WAAW,GAAG;AAAE,iBAAO,KAAKV,YAAZ;AAA4B;;AAE5B,YAAhBW,gBAAgB,GAAG;AAAE,iBAAO,KAAKV,iBAAZ;AAAiC;;AAEvC,YAAfW,eAAe,GAAG;AAAE,iBAAO,KAAKV,gBAAZ;AAAgC;;AApB7C,O;;yBAuBFW,O,GAAoB,IAAIrB,QAAJ,E", "sourcesContent": ["import type FColliderManager from \"./collider-system/FColliderManager\";\r\nimport { type BattleManager } from \"./manager/BattleManager\";\r\nimport { type BossManager } from \"./manager/BossManager\";\r\nimport { type EnemyManager } from \"./manager/EnemyManager\";\r\nimport { GameDataManager } from \"./manager/GameDataManager\";\r\nimport { type GamePlaneManager } from \"./manager/GamePlaneManager\";\r\nimport { type GameStateManager } from \"./manager/GameStateManager\";\r\nimport { type HurtEffectManager } from \"./manager/HurtEffectManager\";\r\nimport { type MainPlaneManager } from \"./manager/MainPlaneManager\";\r\nimport type WaveManager from \"./manager/WaveManager\";\r\n\r\nclass _GameIns {\r\n    _battleManager: BattleManager | null = null;\r\n    get battleManager() { return this._battleManager!; }\r\n    _bossManager: BossManager | null = null;\r\n    get bossManager() { return this._bossManager!; }\r\n    _enemyManager: EnemyManager | null = null;\r\n    get enemyManager() { return this._enemyManager!; }\r\n    _gameStateManager: GameStateManager | null = null;\r\n    get gameStateManager() { return this._gameStateManager!; }\r\n    _hurtEffectManager: HurtEffectManager | null = null;\r\n    get hurtEffectManager() { return this._hurtEffectManager!; }\r\n    _mainPlaneManager: MainPlaneManager | null = null;\r\n    get mainPlaneManager() { return this._mainPlaneManager!; }\r\n    _gamePlaneManager: GamePlaneManager | null = null;\r\n    get gamePlaneManager() { return this._gamePlaneManager!; }\r\n    _waveManager: WaveManager | null = null;\r\n    get waveManager() { return this._waveManager!; }\r\n    _fColliderManager: FColliderManager | null = null;\r\n    get fColliderManager() { return this._fColliderManager!; }\r\n    _gameDataManager: GameDataManager | null = null;\r\n    get gameDataManager() { return this._gameDataManager!; }\r\n}\r\n\r\nexport const GameIns: _GameIns = new _GameIns();"]}