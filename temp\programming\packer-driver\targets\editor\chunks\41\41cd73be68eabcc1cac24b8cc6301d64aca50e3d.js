System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Vec2, instantiate, assetManager, LubanMgr, EnemyPlane, LevelEditorUtils, EnemyData, eMoveEvent, _dec, _dec2, _dec3, _class, _class2, _crd, ccclass, property, executeInEditMode, menu, WavePreview;

  function _reportPossibleCrUseOfWave(extras) {
    _reporterNs.report("Wave", "db://assets/bundles/common/script/game/wave/Wave", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLubanMgr(extras) {
    _reporterNs.report("LubanMgr", "db://assets/bundles/common/script/luban/LubanMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyPlane(extras) {
    _reporterNs.report("EnemyPlane", "db://assets/bundles/common/script/game/ui/plane/enemy/EnemyPlane", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelEditorUtils(extras) {
    _reporterNs.report("LevelEditorUtils", "../utils", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyData(extras) {
    _reporterNs.report("EnemyData", "db://assets/bundles/common/script/game/data/EnemyData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeMoveEvent(extras) {
    _reporterNs.report("eMoveEvent", "db://assets/bundles/common/script/game/move/IMovable", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Vec2 = _cc.Vec2;
      instantiate = _cc.instantiate;
      assetManager = _cc.assetManager;
    }, function (_unresolved_2) {
      LubanMgr = _unresolved_2.LubanMgr;
    }, function (_unresolved_3) {
      EnemyPlane = _unresolved_3.default;
    }, function (_unresolved_4) {
      LevelEditorUtils = _unresolved_4.LevelEditorUtils;
    }, function (_unresolved_5) {
      EnemyData = _unresolved_5.EnemyData;
    }, function (_unresolved_6) {
      eMoveEvent = _unresolved_6.eMoveEvent;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "e6727UuHORMv49xH6m7Wz2U", "WavePreview", undefined);

      __checkObsolete__(['_decorator', 'Node', 'Prefab', 'CCBoolean', 'CCFloat', 'CCInteger', 'Component', 'Vec2', 'instantiate', 'assetManager']);

      ({
        ccclass,
        property,
        executeInEditMode,
        menu
      } = _decorator);

      /// 用来创建和管理波次的所有飞机对象
      _export("WavePreview", WavePreview = (_dec = ccclass('WavePreview'), _dec2 = menu("怪物/编辑器/波次预览"), _dec3 = executeInEditMode(), _dec(_class = _dec2(_class = _dec3(_class = (_class2 = class WavePreview extends Component {
        constructor(...args) {
          super(...args);
          this._luban = null;
          // 这里的wave是编辑时的示意
          this._bindingMap = new Map();
          // 这里的wave时编辑器play时，用来动态创建小怪的wave。
          this.previewWave = [];
          this.planePool = [];
          this.activePlane = [];
        }

        static get instance() {
          return this._instance;
        }

        get luban() {
          if (this._luban == null) {
            this._luban = new (_crd && LubanMgr === void 0 ? (_reportPossibleCrUseOfLubanMgr({
              error: Error()
            }), LubanMgr) : LubanMgr)();
          }

          return this._luban;
        }

        onLoad() {
          if (WavePreview._instance == null) {
            WavePreview._instance = this;
          } else {
            console.warn("WavePreview multiple instance");
          }
        }

        reset() {
          this.node.removeAllChildren();

          this._bindingMap.clear();
        }

        update(dt) {
          this._bindingMap.forEach((nodes, wave) => {
            this.updateWave(wave, nodes);
          });

          this.tickPreview(dt);
        }

        setupWave(wave) {
          const waveData = wave.waveData;

          if (waveData.spawnGroups && waveData.spawnGroups.length > 0) {
            var _this$luban;

            let planeId = 0;

            for (let i = 0; i < waveData.spawnGroups.length; i++) {
              if (waveData.spawnGroups[i].planeID > 0) {
                planeId = waveData.spawnGroups[i].planeID;
                break;
              }
            }

            if (planeId == 0) {
              console.warn("WavePreview createPlane no valid planeId in spawnGroups");
              return;
            }

            if (((_this$luban = this.luban) == null ? void 0 : _this$luban.table) == null) {
              var _this$luban2;

              (_this$luban2 = this.luban) == null || _this$luban2.initInEditor().then(() => {
                this.createPlane(wave, planeId);
              });
            }
          }
        }

        createPlane(wave, planeId) {
          var _this$luban3;

          const planeData = (_this$luban3 = this.luban) == null ? void 0 : _this$luban3.table.TbResEnemy.get(planeId);

          if (planeData == null) {
            console.warn("WavePreview createPlane no planeData for id:", planeId);
            return;
          }

          const fullPath = "db://assets/resources/" + planeData.prefab + ".prefab";
          (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
            error: Error()
          }), LevelEditorUtils) : LevelEditorUtils).loadByPath(fullPath).then(prefab => {
            if (prefab) {
              const node = instantiate(prefab);

              if (node) {
                this.node.addChild(node);

                let nodes = this._bindingMap.get(wave);

                if (nodes == null) {
                  nodes = [];

                  this._bindingMap.set(wave, nodes);
                }

                nodes.push(node);
              }
            }
          });
        }

        updateWave(wave, nodes) {
          const wavePos = wave.node.worldPosition;
          const waveData = wave.waveData;
          const nodePos = new Vec2(wavePos.x + waveData.spawnPosX.eval(), wavePos.y + waveData.spawnPosY.eval());
          const nodeAngle = waveData.spawnAngle.eval();

          for (let i = 0; i < nodes.length; i++) {
            const node = nodes[i];
            node.setWorldPosition(nodePos.x, nodePos.y, 0);
            node.setRotationFromEuler(0, 0, nodeAngle);
          }
        }

        addPreview(wave, posX, posY) {
          wave.setCreatePlaneDelegate((planeId, pos, angle) => {
            this.createDummyPlane(wave, planeId, pos, angle);
          });
          wave.trigger(posX, posY);
          this.previewWave.push(wave);
        }

        tickPreview(dt) {
          if (this.previewWave.length == 0) {
            return;
          }

          const dtInMiliseconds = dt * 1000;
          this.previewWave.forEach(wave => {
            // console.log('isWaveCompleted: ', wave.isCompleted);
            wave.tick(dtInMiliseconds);
          });
          this.activePlane.forEach(plane => {
            plane.moveCom.tick(dt);
          });
        }

        clearPreview() {
          // destroy preivewWave
          this.previewWave.forEach(wave => {
            wave.node.destroy();
          });
          this.activePlane.forEach(plane => {
            plane.node.destroy();
          });
          this.planePool.forEach(plane => {
            plane.node.destroy();
          });
          this.activePlane = [];
          this.planePool = [];
          this.previewWave = [];
        }

        createDummyPlane(wave, planeId, pos, angle) {
          // 对应"assets/editor/level/prefab/dummy_plane";
          // console.log('createDummyPlane: ');
          const dummy_plane_uuid = "698c56c6-6603-4e69-abaf-421b721ef307";
          assetManager.loadAny({
            uuid: dummy_plane_uuid
          }, (err, prefab) => {
            if (err) {
              console.error("WavePreview createDummyPlane load prefab err", err);
              return;
            } // 从对象池里拿一个dummy plane


            let plane = null;

            if (this.planePool.length > 0) {
              plane = this.planePool.pop();
              plane.node.active = true;
            } else {
              const planeNode = instantiate(prefab);
              const plane = planeNode.getComponent(_crd && EnemyPlane === void 0 ? (_reportPossibleCrUseOfEnemyPlane({
                error: Error()
              }), EnemyPlane) : EnemyPlane);

              if (plane) {
                plane.setPos(pos.x, pos.y);
                plane.initPlane(new (_crd && EnemyData === void 0 ? (_reportPossibleCrUseOfEnemyData({
                  error: Error()
                }), EnemyData) : EnemyData)());
                plane.moveCom.removeAllListeners();
                plane.moveCom.on((_crd && eMoveEvent === void 0 ? (_reportPossibleCrUseOfeMoveEvent({
                  error: Error()
                }), eMoveEvent) : eMoveEvent).onBecomeInvisible, () => {
                  plane.node.active = false;
                  this.planePool.push(plane);
                });
                plane.initMove(angle);
                this.node.addChild(planeNode);
                this.activePlane.push(plane);
              } else {
                planeNode.destroy();
              }
            }
          });
        }

      }, _class2._instance = null, _class2)) || _class) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=41cd73be68eabcc1cac24b8cc6301d64aca50e3d.js.map