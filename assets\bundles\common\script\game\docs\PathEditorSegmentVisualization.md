# PathEditor 细分段可视化功能

## 功能概述

在PathEditor中新增了`showSegments`开关，用于可视化路径的细分情况。当开启时，每个细分的小段都会用不同的颜色绘制，颜色从绿色渐变到红色，帮助开发者直观地了解自适应细分算法的工作效果和细分密度分布。

## 使用方法

### 1. 启用功能
在PathEditor组件的Inspector面板中，勾选"显示细分线段"选项。

### 2. 颜色编码
系统使用绿色到红色的渐变来表示路径的进度：
- 🟢 **绿色**: 路径起始部分的细分段
- 🟡 **黄绿色**: 路径前段的细分段
- 🟠 **橙色**: 路径中段的细分段
- 🔴 **红色**: 路径末尾部分的细分段

颜色变化是连续的，每个细分段都有独特的颜色，能够清晰地看出：
- **细分密度**: 相邻颜色变化越小，说明该区域细分越密集
- **路径进度**: 从绿到红的颜色变化表示沿路径的进度
- **段数分布**: 直观看到整条路径被分成了多少个细分段

## 技术实现

### 核心方法

#### `drawSegmentedPath()`
负责绘制每个细分段的不同颜色：
```typescript
private drawSegmentedPath(graphics: Graphics, subdivided: PathPoint[]) {
    // 为每个细分段绘制不同的颜色
    for (let i = 0; i < subdivided.length - 1; i++) {
        const t = i / (subdivided.length - 1); // 计算归一化位置参数

        // 从绿色到红色的颜色插值
        const color = this.interpolateColor(Color.GREEN, Color.RED, t);
        graphics.strokeColor = color;

        // 绘制当前细分段
        graphics.moveTo(subdivided[i].x, subdivided[i].y);
        graphics.lineTo(subdivided[i + 1].x, subdivided[i + 1].y);
        graphics.stroke();
    }
}
```

#### `interpolateColor()`
颜色插值函数，实现平滑的颜色过渡：
```typescript
private interpolateColor(color1: Color, color2: Color, t: number): Color {
    const r = color1.r + (color2.r - color1.r) * t;
    const g = color1.g + (color2.g - color1.g) * t;
    const b = color1.b + (color2.b - color1.b) * t;
    return new Color(r, g, b, color1.a);
}
```

### 渐变算法

1. **位置计算**: 每个细分段的位置参数 `t = i / (总段数 - 1)`
2. **颜色插值**: 使用线性插值在绿色和红色之间计算中间色
3. **独立绘制**: 每个细分段单独绘制，确保颜色的精确控制

## 应用场景

### 1. 调试自适应细分
- **密度可视化**: 颜色变化密集的区域表示细分密度高
- **曲率验证**: 急转弯处应该看到更多的颜色变化
- **直线检查**: 直线段应该显示较少的颜色变化

### 2. 性能分析
- **段数统计**: 通过颜色数量直观了解总细分段数
- **分布均匀性**: 颜色变化的均匀程度反映细分的合理性
- **过度细分识别**: 过于密集的颜色变化可能表示过度细分

### 3. 视觉调试
- **进度追踪**: 绿到红的渐变清晰显示路径进度
- **细分质量**: 每个小段的独立着色便于精确分析
- **参数调优**: 帮助调整平滑度参数以获得理想的细分效果

## 使用建议

1. **开发阶段**: 建议开启此功能来调试路径细分效果
2. **发布版本**: 关闭此功能以使用统一的路径颜色
3. **复杂路径**: 对于有很多转角的路径，此功能特别有用
4. **性能测试**: 结合细分点数量信息，评估路径的性能影响

## 注意事项

- 颜色分配基于原始路径段的顺序
- 闭合路径会正确处理最后一段回到起点的连接
- 段分割算法使用距离阈值，可能在极端情况下不够精确
- 此功能仅在编辑器中可用，不影响运行时性能

## 未来改进

- 可以考虑添加更精确的段分割算法
- 支持自定义颜色方案
- 添加细分点密度的数值显示
- 支持导出细分分析报告
