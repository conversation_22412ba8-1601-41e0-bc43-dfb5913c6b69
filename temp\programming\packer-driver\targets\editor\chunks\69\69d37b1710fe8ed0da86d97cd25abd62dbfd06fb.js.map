{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/StatisticsHurtCell.ts"], "names": ["_decorator", "Component", "Label", "ProgressBar", "Sprite", "tween", "ccclass", "property", "StatisticsHurtCell", "_expTween", "start", "update", "deltaTime", "onDestroy", "stop", "setType", "stat", "totalScore", "txtType", "string", "type", "txt2", "score", "bar", "progress", "txt", "toFixed", "gap", "exp", "Number", "to", "onUpdate"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;;;;;;;;;OACtD;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBP,U;;oCAGjBQ,kB,WADZF,OAAO,CAAC,oBAAD,C,UAGHC,QAAQ,CAACH,MAAD,C,UAERG,QAAQ,CAACL,KAAD,C,UAERK,QAAQ,CAACJ,WAAD,C,UAERI,QAAQ,CAACL,KAAD,C,UAERK,QAAQ,CAACL,KAAD,C,2BAXb,MACaM,kBADb,SACwCP,SADxC,CACkD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAatCQ,SAbsC;AAAA;;AAapB;AAE1BC,QAAAA,KAAK,GAAG,CAEP;;AAEDC,QAAAA,MAAM,CAACC,SAAD,EAAoB,CAEzB;;AAEDC,QAAAA,SAAS,GAAG;AACR,cAAI,KAAKJ,SAAT,EAAoB;AAChB,iBAAKA,SAAL,CAAeK,IAAf;;AACA,iBAAKL,SAAL,GAAiB,IAAjB;AACH;AACJ;;AAEMM,QAAAA,OAAO,CAACC,IAAD,EAAwCC,UAAxC,EAA4D;AACtE,eAAKC,OAAL,CAAcC,MAAd,GAAuBH,IAAI,CAACI,IAA5B;AACA,eAAKC,IAAL,CAAWF,MAAX,GAAoBH,IAAI,CAACM,KAAzB;AACA,eAAKC,GAAL,CAAUC,QAAV,GAAqB,CAArB;AACA,eAAKC,GAAL,CAAUN,MAAV,GAAmB,KAAKI,GAAL,CAAUC,QAAV,CAAmBE,OAAnB,CAA2B,CAA3B,IAAgC,GAAnD;;AACA,cAAIT,UAAU,GAAG,CAAjB,EAAoB;AAChB,kBAAMU,GAAG,GAAG,GAAZ;AACA,kBAAMC,GAAG,GAAGC,MAAM,CAACb,IAAI,CAACM,KAAN,CAAN,GAAqBL,UAAjC;AACA,iBAAKR,SAAL,GAAiBJ,KAAK,CAAC,KAAKkB,GAAN,CAAL,CACZO,EADY,CACTH,GADS,EACJ;AAAEH,cAAAA,QAAQ,EAAEI;AAAZ,aADI,EACe;AACxBG,cAAAA,QAAQ,EAAE,MAAM;AACZ,oBAAI,CAAC,KAAKR,GAAV,EAAe;AACf,qBAAKE,GAAL,CAAUN,MAAV,GAAmB,CAAC,KAAKI,GAAL,CAAUC,QAAV,GAAqB,GAAtB,EAA2BE,OAA3B,CAAmC,CAAnC,IAAwC,GAA3D;AACH;AAJuB,aADf,EAOZhB,KAPY,EAAjB;AAQH;AACJ;;AA/C6C,O;;;;;iBAGxB,I;;;;;;;iBAEE,I;;;;;;;iBAEE,I;;;;;;;iBAEN,I;;;;;;;iBAEC,I", "sourcesContent": ["import { _decorator, Component, Label, ProgressBar, Sprite, tween } from 'cc';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('StatisticsHurtCell')\r\nexport class StatisticsHurtCell extends Component {\r\n\r\n    @property(Sprite)\r\n    icon: Sprite | null = null;\r\n    @property(Label)\r\n    txtType: Label | null = null;\r\n    @property(ProgressBar)\r\n    bar: ProgressBar | null = null;\r\n    @property(Label)\r\n    txt: Label | null = null;\r\n    @property(Label)\r\n    txt2: Label | null = null;\r\n\r\n    private _expTween: any;   // 经验条动画的 tween 引用\r\n\r\n    start() {\r\n\r\n    }\r\n\r\n    update(deltaTime: number) {\r\n\r\n    }\r\n\r\n    onDestroy() {\r\n        if (this._expTween) {\r\n            this._expTween.stop();\r\n            this._expTween = null;\r\n        }\r\n    }\r\n\r\n    public setType(stat: { type: string; score: string }, totalScore: number) {\r\n        this.txtType!.string = stat.type;\r\n        this.txt2!.string = stat.score;\r\n        this.bar!.progress = 0;\r\n        this.txt!.string = this.bar!.progress.toFixed(1) + \"%\";\r\n        if (totalScore > 0) {\r\n            const gap = 0.5;\r\n            const exp = Number(stat.score) / totalScore;\r\n            this._expTween = tween(this.bar!)\r\n                .to(gap, { progress: exp }, {\r\n                    onUpdate: () => {\r\n                        if (!this.bar) return;\r\n                        this.txt!.string = (this.bar!.progress * 100).toFixed(1) + \"%\";\r\n                    }\r\n                })\r\n                .start();\r\n        }\r\n    }\r\n\r\n}\r\n\r\n\r\n"]}