System.register(["__unresolved_0", "cc"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Vec2, _dec, _dec2, _class, _crd, ccclass, property, executeInEditMode, LevelPrefabParse;

  function _reportPossibleCrUseOfLevelDataTerrain(extras) {
    _reporterNs.report("LevelDataTerrain", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Vec2 = _cc.Vec2;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "9c583z+n7ZMY4Oviyb41RVe", "LevelPrefabParse", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Vec2']);

      ({
        ccclass,
        property,
        executeInEditMode
      } = _decorator);

      _export("LevelPrefabParse", LevelPrefabParse = (_dec = ccclass('LevelPrefabParse'), _dec2 = executeInEditMode(), _dec(_class = _dec2(_class = class LevelPrefabParse extends Component {
        onLoad() {
          console.log("LevelPrefabParse onLoad");
        }

        onDisable() {
          console.log("LevelPrefabParse onDisable");
          let data = [];
          this.node.children.forEach(node => {
            data.push({
              // @ts-ignore
              uuid: node._prefab.asset._uuid,
              position: new Vec2(node.position.x, node.position.y),
              scale: new Vec2(node.scale.x, node.scale.y),
              rotation: node.rotation.z
            });
          });

          this._exportDataAsJson(data);
        }
        /**
         * 将数据导出为JSON文件
         * @param data 要导出的数据
         */


        async _exportDataAsJson(data) {
          const jsonData = JSON.stringify(data, null, 2);
          const fileName = `${this.node.name}.json`;
          const assetPath = `db://assets/resources/game/level/background/Prefab/Config/${fileName}`;
          console.log("LevelPrefabParse _exportDataAsJson", assetPath); // @ts-ignore

          const sourceAssetInfo = await Editor.Message.request('asset-db', 'query-asset-info', assetPath);

          if (sourceAssetInfo === null) {
            console.error('查询资源信息失败:');

            this._createAsset(assetPath, jsonData);
          } else {
            console.log('导出预制体配置信息:', sourceAssetInfo);

            this._saveAsset(sourceAssetInfo.uuid, jsonData);
          }
        }
        /**
         * 创建新资源
         */


        async _createAsset(assetPath, jsonData) {
          // @ts-ignore
          var createRsp = await Editor.Message.request('asset-db', 'create-asset', assetPath, jsonData);
        }
        /**
         * 更新现有资源
         */


        async _saveAsset(uuid, jsonData) {
          // @ts-ignore
          const rsp = await Editor.Message.send('asset-db', 'save-asset', uuid, jsonData);

          this._refreshAssetDb();
        }
        /**
         * 刷新资源数据库
         */


        async _refreshAssetDb() {
          // @ts-ignore
          Editor.Message.send('asset-db', 'refresh-asset', `db://assets/resources/game/level/background/Prefab/Config`);
        }

      }) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=935e22721775bc8193e4b386d0ee2bbfd0bb5fee.js.map