System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, instantiate, NodePool, Prefab, Plane, IMgr, MyApp, BundleName, PlaneManager, _crd;

  function _reportPossibleCrUseOfPlaneData(extras) {
    _reporterNs.report("PlaneData", "db://assets/bundles/common/script/data/plane/PlaneData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlane(extras) {
    _reporterNs.report("Plane", "db://assets/bundles/common/script/ui/Plane", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIMgr(extras) {
    _reporterNs.report("IMgr", "db://assets/scripts/core/base/IMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "../app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBundleName(extras) {
    _reporterNs.report("BundleName", "../const/BundleConst", _context.meta, extras);
  }

  _export("PlaneManager", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      instantiate = _cc.instantiate;
      NodePool = _cc.NodePool;
      Prefab = _cc.Prefab;
    }, function (_unresolved_2) {
      Plane = _unresolved_2.Plane;
    }, function (_unresolved_3) {
      IMgr = _unresolved_3.IMgr;
    }, function (_unresolved_4) {
      MyApp = _unresolved_4.MyApp;
    }, function (_unresolved_5) {
      BundleName = _unresolved_5.BundleName;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "3d827VGFVROcIe/VyRqG/cO", "PlaneManager", undefined);

      __checkObsolete__(['instantiate', 'Node', 'NodePool', 'Prefab']);

      _export("PlaneManager", PlaneManager = class PlaneManager extends (_crd && IMgr === void 0 ? (_reportPossibleCrUseOfIMgr({
        error: Error()
      }), IMgr) : IMgr) {
        constructor(...args) {
          super(...args);
          this._planePreFab = null;
          this._planePoor = new NodePool();
        }

        async load() {
          let plane = await (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.loadAsync((_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).Common, "prefab/Plane", Prefab);
          this.initPlanePreFab(plane);
        }

        initPlanePreFab(prefab) {
          if (!prefab) {
            throw new Error("Invalid prefab: prefab is null or undefined.");
          }

          this._planePreFab = prefab;
        }

        getPlane(planeData) {
          let planeNode;

          if (this._planePoor.size() > 0) {
            const node = this._planePoor.get();

            if (!node) {
              throw new Error("NodePool returned null despite having available nodes.");
            }

            planeNode = node;
          } else {
            if (!this._planePreFab) {
              throw new Error("Plane prefab is not initialized. Call initPlanePreFab first.");
            }

            planeNode = instantiate(this._planePreFab);
          }

          const planeComponent = planeNode.getComponent(_crd && Plane === void 0 ? (_reportPossibleCrUseOfPlane({
            error: Error()
          }), Plane) : Plane);
          planeComponent.initPlane(planeData);
          return planeNode;
        } //回收英雄，不直接调用，调用英雄的remove方法


        recyclePlane(planeNode) {
          planeNode.active = true;
          planeNode.setPosition(0, 0);
          planeNode.setSiblingIndex(0);
          planeNode.setScale(1, 1);

          this._planePoor.put(planeNode);
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=66b41c840f31f93c922e35659624520b3f26a7db.js.map