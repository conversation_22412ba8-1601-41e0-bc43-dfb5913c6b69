import { Vec2 } from "cc"; // 注意：这里的分号是必须的，因为下面的代码是压缩过的
import { LevelDataEventCondtion } from "./condition/LevelDataEventCondtion";
import { LevelDataEventTrigger } from "./trigger/LevelDataEventTrigger";
import { newCondition } from "./condition/newCondition";
import { newTrigger } from "./trigger/newTrigger";

export enum LayerType {
    Background = 1,
    Random,
    Scroll,
    Emittier
}

// 层级拼接方式
export enum LayerSplicingMode {
    /**
     * @zh 节点高度拼接
     */
    node_height = 1,
    /**
     * @zh 屏幕高度拼接
     */
    fix_height,
    /**
     * @zh 随机间隔拼接
     */
    random_height
}

// 地形发射体类型
export enum LayerEmittierType {
    // 无限
    Infinite = 1,
    // 持续时间
    Duration,
    // 发射次数
    Count,
    // 监听事件
    Event
}

// 地形发射策略
export enum LayerEmittierStrategy {
    Random, // 随机
    Sequence // 顺序
}

export class LevelDataTerrain {
    public uuid: string = ""; // 如果有后缀json，需额外解析
    public position: Vec2 = new Vec2();
    public scale: Vec2 = new Vec2();
    public rotation: number = 0;
}

export class LayerRandomRange {
    public min: number = 0;
    public max: number = 0;

    constructor (min: number = 0, max: number = 0) {
        this.min = min;
        this.max = max;
    }
}

export class LevelDataScroll {
    public weight: number = 100;
    public uuids: string[] = [];
    public splicingMode: LayerSplicingMode = LayerSplicingMode.node_height;
    public offSetX: LayerRandomRange = new LayerRandomRange();
    public offSetY: LayerRandomRange = new LayerRandomRange();
}

export class LevelDataRandTerrain {
    public weight: number = 0;
    public uuid: string = "";
    public offSetX: LayerRandomRange = new LayerRandomRange();
    public offSetY: LayerRandomRange = new LayerRandomRange();
}

export class LevelDataRandTerrains extends LevelDataTerrain {
    public weight: number = 100;
    public terrains: LevelDataRandTerrain[] = [];
}

export class LevelDataRandTerrainsGroup {
    public group: LevelDataRandTerrains[] = [];
}

export class LevelDataElem {
    public elemID: string = "";
    public position: Vec2 = new Vec2();
    public name: string = "default";
}

export class LevelDataWaveGroup {
    public waveUUID: string[] = [];
    public weight: number = 50;
}

export class LevelDataWave extends LevelDataElem {
    public waveGroup: LevelDataWaveGroup[] = [];

    static fromJSON(json: any): LevelDataWave {
        const wave = new LevelDataWave();
        if (!json) return wave;
        Object.assign(wave, json);
        return wave;
    }
}

export class LevelDataEvent extends LevelDataElem {
    public conditions: LevelDataEventCondtion[] = [];
    public triggers: LevelDataEventTrigger[] = [];

    static fromJSON(json: any): LevelDataEvent {
        const event = new LevelDataEvent();
        if (!json) return event;
        
        Object.assign(event, json);
        event.conditions = json.conditions?.map((condition: any) => {
            return newCondition(condition);
        }) || [];
        event.triggers = json.triggers?.map((trigger: any) => {
            return newTrigger(trigger);
        }) || [];
        
        return event;
    }
}

export class LevelDataLayer {
    public remark: string = "";
    public zIndex: number = 0;
    public totalTime: number = 0;
    public speed: number = 200;
    public type: number = 0; // 对应LayerType
    public terrains: LevelDataTerrain[] = [];
    public scrolls: LevelDataScroll[] = [];
    public dynamics: LevelDataRandTerrainsGroup[] = [];
    public emittiers: LevelDataTerrain[] = [];
    public waves: LevelDataWave[] = [];
    public events: LevelDataEvent[] = [];

    protected assign(json: any):void {
        Object.assign(this, json);

        this.terrains = json.terrains?.map((terrain: any) => 
            Object.assign(new LevelDataTerrain(), terrain)) || [];
        this.scrolls = json.scrolls?.map((scroll: any) => 
            Object.assign(new LevelDataScroll(), scroll)) || [];
        this.dynamics = json.dynamics?.map((dynamic: any) => 
            Object.assign(new LevelDataRandTerrainsGroup(), dynamic)) || [];
        this.emittiers = json.emittiers?.map((emittier: any) => 
            Object.assign(new LevelDataTerrain(), emittier)) || [];
        this.waves = json.waves?.map((wave: any) => 
            LevelDataWave.fromJSON(wave)) || [];
        this.events = json.events?.map((event: any) => 
            LevelDataEvent.fromJSON(event)) || [];
    }

    static fromJSON(json: any): LevelDataLayer {
        const layer = new LevelDataLayer();
        if (!json) return layer;

        layer.assign(json);
        
        return layer;
    }
}

export class LevelDataBackgroundLayer extends LevelDataLayer {
    public backgrounds: string[] = [];
    
    static fromJSON(json: any): LevelDataBackgroundLayer {
        const layer = new LevelDataBackgroundLayer();
        if (!json) return layer;
       
        layer.assign(json);
        
        return layer;
    }
}

export class LevelData {
    public name: string = "";
    public totalTime: number = 0;
    public backgroundLayer: LevelDataBackgroundLayer = new LevelDataBackgroundLayer();
    public floorLayers: LevelDataLayer[] = [];
    public skyLayers: LevelDataLayer[] = [];

    static fromJSON(json: any): LevelData {
        const levelData = new LevelData();
        if (!json) return levelData;
        
        Object.assign(levelData, json);
        levelData.backgroundLayer = LevelDataBackgroundLayer.fromJSON(json.backgroundLayer);
        levelData.floorLayers = json.floorLayers?.map((layer: any) =>
            LevelDataLayer.fromJSON(layer)) || [];
        levelData.skyLayers = json.skyLayers?.map((layer: any) => 
            LevelDataLayer.fromJSON(layer)) || [];
        
        return levelData;
    }
}
