# 更新日志

## [1.1.0] - 2025-09-27

### 新增功能

#### 右键菜单编辑功能
- 新增阵型编辑右键菜单：当右键点击 `assets/resources/game/level/wave/formation/` 目录下的JSON文件时，显示"编辑阵型"选项
- 新增路径编辑右键菜单：当右键点击 `assets/resources/game/level/wave/path/` 目录下的JSON文件时，显示"编辑路径"选项
- 自动场景切换：点击编辑选项后自动打开对应的编辑器场景（FormationEditor或PathEditor）
- 自动数据加载：场景打开后自动加载选中的JSON文件数据到对应的编辑器组件

### 技术改进

#### 文件修改
- `assets-menu.ts`: 
  - 重构了 `onAssetMenu` 函数，添加了路径检测逻辑
  - 新增 `editFormation` 和 `editPath` 函数
- `scene.ts`:
  - 新增 `loadFormationData` 方法用于加载阵型数据
  - 新增 `loadPathData` 方法用于加载路径数据

#### 实现细节
- 使用路径匹配来检测特定目录下的JSON文件
- 通过 `Editor.Message.request` API 实现场景切换
- 使用 `assetManager.loadAny` 加载JSON资源
- 通过 `getComponentInChildren` 查找编辑器组件

### 使用说明

1. 在资源管理器中找到对应的JSON文件
2. 右键点击文件
3. 选择"飞机游戏"菜单
4. 选择对应的编辑选项
5. 系统自动打开编辑器并加载数据

### 兼容性

- 保持了原有的菜单功能不变
- 新功能只在特定路径下的JSON文件上显示
- 不影响其他文件类型的右键菜单

### 测试

- 已验证阵型编辑功能
- 已验证路径编辑功能
- 已验证路径检测逻辑
- 已验证场景切换和数据加载
