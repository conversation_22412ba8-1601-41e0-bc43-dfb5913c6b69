System.register(["__unresolved_0", "cc", "cc/env", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, ccenum, Component, director, LabelComponent, ProgressBar, ResolutionPolicy, view, WECHAT, initBundle, GameConst, UIMgr, logDebug, _dec, _dec2, _dec3, _dec4, _dec5, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _crd, ccclass, property, warnCustom, groupCustom, GameLogLevel, ResUpdate;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfinitBundle(extras) {
    _reporterNs.report("initBundle", "../core/base/Bundle", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../core/base/GameConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "../core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogDebug(extras) {
    _reporterNs.report("logDebug", "../utils/Logger", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      ccenum = _cc.ccenum;
      Component = _cc.Component;
      director = _cc.director;
      LabelComponent = _cc.LabelComponent;
      ProgressBar = _cc.ProgressBar;
      ResolutionPolicy = _cc.ResolutionPolicy;
      view = _cc.view;
    }, function (_ccEnv) {
      WECHAT = _ccEnv.WECHAT;
    }, function (_unresolved_2) {}, function (_unresolved_3) {
      initBundle = _unresolved_3.initBundle;
    }, function (_unresolved_4) {
      GameConst = _unresolved_4.GameConst;
    }, function (_unresolved_5) {
      UIMgr = _unresolved_5.UIMgr;
    }, function (_unresolved_6) {
      logDebug = _unresolved_6.logDebug;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "30734ypWehPU5ZyhV2WDXQn", "ResUpdate", undefined);

      __checkObsolete__(['_decorator', 'ccenum', 'Component', 'director', 'LabelComponent', 'ProgressBar', 'ResolutionPolicy', 'view']);

      ({
        ccclass,
        property
      } = _decorator);

      if (WECHAT) {
        warnCustom = console.warn;

        console.warn = function (res) {
          if (typeof res == "string" && res.indexOf("文件路径在真机上可能无法读取") > -1) {
            return;
          } else {
            warnCustom(res);
          }
        };

        groupCustom = console.group;

        console.group = function (res) {
          if (typeof res == "string" && res.indexOf("读取文件/文件夹警告") > -1) {
            return;
          } else {
            groupCustom(res);
          }
        };
      }

      GameLogLevel = /*#__PURE__*/function (GameLogLevel) {
        GameLogLevel[GameLogLevel["TRACE"] = 0] = "TRACE";
        GameLogLevel[GameLogLevel["DEBUG"] = 1] = "DEBUG";
        GameLogLevel[GameLogLevel["LOG"] = 2] = "LOG";
        GameLogLevel[GameLogLevel["INFO"] = 3] = "INFO";
        GameLogLevel[GameLogLevel["WARN"] = 4] = "WARN";
        GameLogLevel[GameLogLevel["ERROR"] = 5] = "ERROR";
        return GameLogLevel;
      }(GameLogLevel || {});

      ccenum(GameLogLevel);

      _export("ResUpdate", ResUpdate = (_dec = ccclass("ResUpdate"), _dec2 = property(LabelComponent), _dec3 = property(LabelComponent), _dec4 = property(LabelComponent), _dec5 = property(ProgressBar), _dec(_class = (_class2 = class ResUpdate extends Component {
        constructor(...args) {
          super(...args);

          /* class member could be defined like this */
          // dummy = '';
          _initializerDefineProperty(this, "countLabel", _descriptor, this);

          _initializerDefineProperty(this, "perLabel", _descriptor2, this);

          _initializerDefineProperty(this, "versionLabel", _descriptor3, this);

          _initializerDefineProperty(this, "loadingBar", _descriptor4, this);
        }

        async start() {
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).initializeLayers(); //!todo 获取资源版本号

          director.preloadScene("Main", this.OnLoadProgress.bind(this), async () => {
            // dev 先load login UI
            await (_crd && initBundle === void 0 ? (_reportPossibleCrUseOfinitBundle({
              error: Error()
            }), initBundle) : initBundle)("common");
            director.loadScene("Main");
          });
        }

        OnLoadProgress(completedCount, totalCount) {
          let progress = completedCount / totalCount;
          (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
            error: Error()
          }), logDebug) : logDebug)("ResUpdate", `load main scene, ${completedCount}, ${totalCount}, ${(progress * 100).toFixed(2)}`);

          if (this.node == null) {
            return;
          }

          this.perLabel.string = (progress * 100).toFixed(2) + "%";
          this.countLabel.string = '加载中...(' + completedCount + '/' + totalCount + ')';
          this.loadingBar.progress = progress;
        }

        onLoad() {
          let fitType = ResolutionPolicy.SHOW_ALL;

          if (view.getVisibleSize().height / view.getVisibleSize().width * (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).designWidth >= (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).designHeight) {
            //高度大于 16:9
            fitType = ResolutionPolicy.FIXED_WIDTH; //宽度全部显示，高度做适配拉长
          } else {
            //宽屏,比如平板或者电脑
            fitType = ResolutionPolicy.SHOW_ALL; //两边黑边       //FIXED_HEIGHT// 全部铺满
          }

          view.setResolutionPolicy(fitType);
          view.resizeWithBrowserSize(true);
        }

        onDestroy() {}

        update(deltaTime) {//if (this.isMainSceneLoaded) //&& window.MyApp && window.MyApp.getLogin().isLogined)
          //{
          //    director.loadScene("MainScene")
          //}
          // Your update function goes here.
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "countLabel", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "perLabel", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "versionLabel", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "loadingBar", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=406fe018c5000ba08084b27d94fbd4bef2f34f3c.js.map