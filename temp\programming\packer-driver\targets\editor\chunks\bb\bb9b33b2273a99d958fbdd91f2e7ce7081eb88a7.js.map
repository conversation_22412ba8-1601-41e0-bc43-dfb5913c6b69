{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/data/PathData.ts"], "names": ["_decorator", "Vec2", "CCFloat", "CCInteger", "Enum", "eOrientationType", "ccclass", "property", "PathPoint", "type", "displayName", "range", "slide", "tooltip", "constructor", "x", "y", "position", "value", "PathData", "editor<PERSON><PERSON><PERSON>", "_cachedSubdividedPoints", "catmullRomPoint", "t", "p0", "p1", "p2", "p3", "smoothness", "lerp", "t2", "t3", "catmullRom", "linear", "getSubdividedPoints", "regen", "generateSubdividedPointsInternal", "effectivePoints", "getPoints", "length", "subdivided", "pointCount", "firstPoint", "push", "segmentCount", "closed", "i", "getControlPoint", "point", "pointNext", "startSmoothness", "endSmoothness", "segmentPoints", "adaptiveSubdivision", "lastPoint", "distance", "pop", "points", "startIndex", "Math", "max", "min", "startIdx", "endIndex", "endIdx", "slice", "point1", "point2", "max<PERSON><PERSON><PERSON>", "avgSmoothness", "subdivideRecursive", "t1", "depth", "createCurveInterpolatedPoint", "tMid", "startPos", "midPos", "endPos", "linearMid", "error", "curvature", "calculateCurvature", "baseThreshold", "curvatureT<PERSON>eshold", "leftPoints", "rightPoints", "v1", "subtract", "v2", "len1", "len2", "normalize", "dot", "clampedDot", "angle", "acos", "PI", "pos", "newPoint", "speed", "orientationType", "orientationParam", "index", "wrappedIndex", "add", "toJSON", "name", "fromJSON", "data", "pathData"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,O,OAAAA,O;AAASC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;;AACtCC,MAAAA,gB,iBAAAA,gB;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBP,U;AAE9B;AACA;AACA;;2BAEaQ,S,WADZF,OAAO,CAAC,WAAD,C,UAEHC,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEP,OAAR;AAAiBQ,QAAAA,WAAW,EAAE;AAA9B,OAAD,C,UAGRH,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEP,OAAR;AAAiBQ,QAAAA,WAAW,EAAE;AAA9B,OAAD,C,UAGRH,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEP,OAAR;AAAiBQ,QAAAA,WAAW,EAAE,MAA9B;AAAsCC,QAAAA,KAAK,EAAE,CAAC,CAAD,EAAI,CAAJ,CAA7C;AAAqDC,QAAAA,KAAK,EAAE,IAA5D;AAAkEC,QAAAA,OAAO,EAAE;AAA3E,OAAD,C,UAGRN,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEN,SAAR;AAAmBO,QAAAA,WAAW,EAAE,IAAhC;AAAsCG,QAAAA,OAAO,EAAE;AAA/C,OAAD,C,UAGRN,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEN,SAAR;AAAmBO,QAAAA,WAAW,EAAE,MAAhC;AAAwCG,QAAAA,OAAO,EAAE;AAAjD,OAAD,C,UAGRN,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEL,IAAI;AAAA;AAAA,iDAAZ;AAAgCM,QAAAA,WAAW,EAAE,MAA7C;AAAqDG,QAAAA,OAAO,EAAE;AAA9D,OAAD,C,UAGRN,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEN,SAAR;AAAmBO,QAAAA,WAAW,EAAE,MAAhC;AAAwCG,QAAAA,OAAO,EAAE;AAAjD,OAAD,C,2BApBb,MACaL,SADb,CACuB;AAsBnBM,QAAAA,WAAW,CAACC,CAAS,GAAG,CAAb,EAAgBC,CAAS,GAAG,CAA5B,EAA+B;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AACtC,eAAKD,CAAL,GAASA,CAAT;AACA,eAAKC,CAAL,GAASA,CAAT;AACH;;AAEkB,YAARC,QAAQ,GAAS;AACxB,iBAAO,IAAIhB,IAAJ,CAAS,KAAKc,CAAd,EAAiB,KAAKC,CAAtB,CAAP;AACH;;AAEkB,YAARC,QAAQ,CAACC,KAAD,EAAc;AAC7B,eAAKH,CAAL,GAASG,KAAK,CAACH,CAAf;AACA,eAAKC,CAAL,GAASE,KAAK,CAACF,CAAf;AACH;;AAlCkB,O;;;;;iBAEA,C;;;;;;;iBAGA,C;;;;;;;iBAGS,C;;;;;;;iBAGL,G;;;;;;;iBAGO,C;;;;;;;iBAGa,C;;;;;;;iBAGT,C;;;AAiBtC;AACA;AACA;;;0BAEaG,Q,YADZb,OAAO,CAAC,UAAD,C,WAEHC,QAAQ,CAAC;AAAEG,QAAAA,WAAW,EAAE,MAAf;AAAuBU,QAAAA,UAAU,EAAE;AAAnC,OAAD,C,WAGRb,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEN,SAAR;AAAmBO,QAAAA,WAAW,EAAE;AAAhC,OAAD,C,WAGRH,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEN,SAAR;AAAmBO,QAAAA,WAAW,EAAE;AAAhC,OAAD,C,WAGRH,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAE,CAACD,SAAD,CAAR;AAAqBE,QAAAA,WAAW,EAAE;AAAlC,OAAD,C,WAGRH,QAAQ,CAAC;AAAEG,QAAAA,WAAW,EAAE,QAAf;AAAyBG,QAAAA,OAAO,EAAE;AAAlC,OAAD,C,6BAdb,MACaM,QADb,CACsB;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAgBlB;AAhBkB,eAiBVE,uBAjBU,GAiBoC,IAjBpC;AAAA;;AAmBlB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACiC,eAAfC,eAAe,CAACC,CAAD,EAAYC,EAAZ,EAAsBC,EAAtB,EAAgCC,EAAhC,EAA0CC,EAA1C,EAAoDC,UAAkB,GAAG,GAAzE,EAAoF;AAC7G;AACA,cAAIA,UAAU,KAAK,CAAnB,EAAsB;AAClB,mBAAO3B,IAAI,CAAC4B,IAAL,CAAU,IAAI5B,IAAJ,EAAV,EAAsBwB,EAAtB,EAA0BC,EAA1B,EAA8BH,CAA9B,CAAP;AACH;;AAED,gBAAMO,EAAE,GAAGP,CAAC,GAAGA,CAAf;AACA,gBAAMQ,EAAE,GAAGD,EAAE,GAAGP,CAAhB,CAP6G,CAS7G;;AACA,gBAAMS,UAAU,GAAG,IAAI/B,IAAJ,EAAnB;AACA+B,UAAAA,UAAU,CAACjB,CAAX,GAAe,OACV,IAAIU,EAAE,CAACV,CAAR,GACA,CAAC,CAACS,EAAE,CAACT,CAAJ,GAAQW,EAAE,CAACX,CAAZ,IAAiBQ,CADjB,GAEA,CAAC,IAAIC,EAAE,CAACT,CAAP,GAAW,IAAIU,EAAE,CAACV,CAAlB,GAAsB,IAAIW,EAAE,CAACX,CAA7B,GAAiCY,EAAE,CAACZ,CAArC,IAA0Ce,EAF1C,GAGA,CAAC,CAACN,EAAE,CAACT,CAAJ,GAAQ,IAAIU,EAAE,CAACV,CAAf,GAAmB,IAAIW,EAAE,CAACX,CAA1B,GAA8BY,EAAE,CAACZ,CAAlC,IAAuCgB,EAJ5B,CAAf;AAOAC,UAAAA,UAAU,CAAChB,CAAX,GAAe,OACV,IAAIS,EAAE,CAACT,CAAR,GACA,CAAC,CAACQ,EAAE,CAACR,CAAJ,GAAQU,EAAE,CAACV,CAAZ,IAAiBO,CADjB,GAEA,CAAC,IAAIC,EAAE,CAACR,CAAP,GAAW,IAAIS,EAAE,CAACT,CAAlB,GAAsB,IAAIU,EAAE,CAACV,CAA7B,GAAiCW,EAAE,CAACX,CAArC,IAA0Cc,EAF1C,GAGA,CAAC,CAACN,EAAE,CAACR,CAAJ,GAAQ,IAAIS,EAAE,CAACT,CAAf,GAAmB,IAAIU,EAAE,CAACV,CAA1B,GAA8BW,EAAE,CAACX,CAAlC,IAAuCe,EAJ5B,CAAf,CAlB6G,CAyB7G;;AACA,cAAIH,UAAU,GAAG,CAAjB,EAAoB;AAChB,kBAAMK,MAAM,GAAGhC,IAAI,CAAC4B,IAAL,CAAU,IAAI5B,IAAJ,EAAV,EAAsBwB,EAAtB,EAA0BC,EAA1B,EAA8BH,CAA9B,CAAf;AACA,mBAAOtB,IAAI,CAAC4B,IAAL,CAAU,IAAI5B,IAAJ,EAAV,EAAsBgC,MAAtB,EAA8BD,UAA9B,EAA0CJ,UAA1C,CAAP;AACH;;AAED,iBAAOI,UAAP;AACH;AAED;AACJ;AACA;AACA;;;AACWE,QAAAA,mBAAmB,CAACC,KAAc,GAAG,KAAlB,EAAsC;AAC5D,cAAI,CAAC,KAAKd,uBAAN,IAAiCc,KAArC,EAA4C;AACxC,iBAAKd,uBAAL,GAA+B,KAAKe,gCAAL,EAA/B;AACH;;AACD,iBAAO,KAAKf,uBAAZ;AACH;AAED;AACJ;AACA;;;AACYe,QAAAA,gCAAgC,GAAgB;AACpD,gBAAMC,eAAe,GAAG,KAAKC,SAAL,EAAxB;;AAEA,cAAID,eAAe,CAACE,MAAhB,GAAyB,CAA7B,EAAgC;AAC5B,mBAAOF,eAAP;AACH;;AAED,gBAAMG,UAAuB,GAAG,EAAhC;AACA,gBAAMC,UAAU,GAAGJ,eAAe,CAACE,MAAnC,CARoD,CAUpD;;AACA,gBAAMG,UAAU,GAAGL,eAAe,CAAC,CAAD,CAAlC;AACAG,UAAAA,UAAU,CAACG,IAAX,CAAgBD,UAAhB,EAZoD,CAcpD;;AACA,gBAAME,YAAY,GAAG,KAAKC,MAAL,GAAcJ,UAAd,GAA2BA,UAAU,GAAG,CAA7D,CAfoD,CAiBpD;;AACA,eAAK,IAAIK,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,YAApB,EAAkCE,CAAC,EAAnC,EAAuC;AACnC,kBAAMtB,EAAE,GAAG,KAAKuB,eAAL,CAAqBV,eAArB,EAAsCS,CAAC,GAAG,CAA1C,CAAX;AACA,kBAAMrB,EAAE,GAAGY,eAAe,CAACS,CAAD,CAAf,CAAmB7B,QAA9B;AACA,kBAAMS,EAAE,GAAG,KAAKqB,eAAL,CAAqBV,eAArB,EAAsCS,CAAC,GAAG,CAA1C,CAAX;AACA,kBAAMnB,EAAE,GAAG,KAAKoB,eAAL,CAAqBV,eAArB,EAAsCS,CAAC,GAAG,CAA1C,CAAX;AAEA,kBAAME,KAAK,GAAGX,eAAe,CAACS,CAAD,CAA7B;AACA,kBAAMG,SAAS,GAAGZ,eAAe,CAAC,CAACS,CAAC,GAAG,CAAL,IAAUL,UAAX,CAAjC;AAEA,kBAAMS,eAAe,GAAGF,KAAK,CAACpB,UAA9B;AACA,kBAAMuB,aAAa,GAAGF,SAAS,CAACrB,UAAhC,CAVmC,CAYnC;;AACA,gBAAIsB,eAAe,KAAK,CAApB,IAAyBC,aAAa,KAAK,CAA/C,EAAkD;AAC9C;AACAX,cAAAA,UAAU,CAACG,IAAX,CAAgBM,SAAhB;AACH,aAHD,MAGO;AACH;AACA,oBAAMG,aAAa,GAAG,KAAKC,mBAAL,CAAyB7B,EAAzB,EAA6BC,EAA7B,EAAiCC,EAAjC,EAAqCC,EAArC,EAAyCqB,KAAzC,EAAgDC,SAAhD,CAAtB;AACAT,cAAAA,UAAU,CAACG,IAAX,CAAgB,GAAGS,aAAnB;AACH;AACJ,WAvCmD,CAyCpD;;;AACA,cAAI,KAAKP,MAAL,IAAeL,UAAU,CAACD,MAAX,GAAoB,CAAvC,EAA0C;AACtC,kBAAMG,UAAU,GAAGF,UAAU,CAAC,CAAD,CAA7B;AACA,kBAAMc,SAAS,GAAGd,UAAU,CAACA,UAAU,CAACD,MAAX,GAAoB,CAArB,CAA5B;AACA,kBAAMgB,QAAQ,GAAGtD,IAAI,CAACsD,QAAL,CAAcb,UAAU,CAACzB,QAAzB,EAAmCqC,SAAS,CAACrC,QAA7C,CAAjB;;AAEA,gBAAIsC,QAAQ,GAAG,GAAf,EAAoB;AAChBf,cAAAA,UAAU,CAACgB,GAAX;AACH;AACJ;;AAED,iBAAOhB,UAAP;AACH;AAED;AACJ;AACA;;;AACYF,QAAAA,SAAS,GAAgB;AAC7B,cAAI,KAAKmB,MAAL,CAAYlB,MAAZ,KAAuB,CAA3B,EAA8B,OAAO,EAAP;AAE9B,gBAAMmB,UAAU,GAAGC,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYD,IAAI,CAACE,GAAL,CAAS,KAAKC,QAAd,EAAwB,KAAKL,MAAL,CAAYlB,MAAZ,GAAqB,CAA7C,CAAZ,CAAnB;AACA,gBAAMwB,QAAQ,GAAG,KAAKC,MAAL,KAAgB,CAAC,CAAjB,GAAqB,KAAKP,MAAL,CAAYlB,MAAZ,GAAqB,CAA1C,GAA8CoB,IAAI,CAACC,GAAL,CAASF,UAAT,EAAqBC,IAAI,CAACE,GAAL,CAAS,KAAKG,MAAd,EAAsB,KAAKP,MAAL,CAAYlB,MAAZ,GAAqB,CAA3C,CAArB,CAA/D;AAEA,iBAAO,KAAKkB,MAAL,CAAYQ,KAAZ,CAAkBP,UAAlB,EAA8BK,QAAQ,GAAG,CAAzC,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACYV,QAAAA,mBAAmB,CACvB7B,EADuB,EACbC,EADa,EACHC,EADG,EACOC,EADP,EAEvBuC,MAFuB,EAEJC,MAFI,EAGvBC,QAAgB,GAAG,CAHI,EAIZ;AACX,gBAAMC,aAAa,GAAG,CAACH,MAAM,CAACtC,UAAP,GAAoBuC,MAAM,CAACvC,UAA5B,IAA0C,CAAhE,CADW,CAGX;;AACA,cAAIyC,aAAa,KAAK,CAAtB,EAAyB;AACrB,mBAAO,CAACF,MAAD,CAAP;AACH,WANU,CAQX;;;AACA,iBAAO,KAAKG,kBAAL,CAAwB9C,EAAxB,EAA4BC,EAA5B,EAAgCC,EAAhC,EAAoCC,EAApC,EAAwCuC,MAAxC,EAAgDC,MAAhD,EAAwD,CAAxD,EAA2D,CAA3D,EAA8D,CAA9D,EAAiEC,QAAjE,EAA2EC,aAA3E,CAAP;AACH;AAED;AACJ;AACA;;;AACYC,QAAAA,kBAAkB,CACtB9C,EADsB,EACZC,EADY,EACFC,EADE,EACQC,EADR,EAEtBuC,MAFsB,EAEHC,MAFG,EAGtBI,EAHsB,EAGVzC,EAHU,EAGE0C,KAHF,EAGiBJ,QAHjB,EAGmCxC,UAHnC,EAIX;AACX;AACA,cAAI4C,KAAK,IAAIJ,QAAb,EAAuB;AACnB,mBAAO,CAAC,KAAKK,4BAAL,CAAkCjD,EAAlC,EAAsCC,EAAtC,EAA0CC,EAA1C,EAA8CC,EAA9C,EAAkDuC,MAAlD,EAA0DC,MAA1D,EAAkErC,EAAlE,EAAsEF,UAAtE,CAAD,CAAP;AACH;;AAED,gBAAM8C,IAAI,GAAG,CAACH,EAAE,GAAGzC,EAAN,IAAY,CAAzB,CANW,CAQX;;AACA,gBAAM6C,QAAQ,GAAGxD,QAAQ,CAACG,eAAT,CAAyBiD,EAAzB,EAA6B/C,EAA7B,EAAiCC,EAAjC,EAAqCC,EAArC,EAAyCC,EAAzC,EAA6CC,UAA7C,CAAjB;AACA,gBAAMgD,MAAM,GAAGzD,QAAQ,CAACG,eAAT,CAAyBoD,IAAzB,EAA+BlD,EAA/B,EAAmCC,EAAnC,EAAuCC,EAAvC,EAA2CC,EAA3C,EAA+CC,UAA/C,CAAf;AACA,gBAAMiD,MAAM,GAAG1D,QAAQ,CAACG,eAAT,CAAyBQ,EAAzB,EAA6BN,EAA7B,EAAiCC,EAAjC,EAAqCC,EAArC,EAAyCC,EAAzC,EAA6CC,UAA7C,CAAf,CAXW,CAaX;;AACA,gBAAMkD,SAAS,GAAG7E,IAAI,CAAC4B,IAAL,CAAU,IAAI5B,IAAJ,EAAV,EAAsB0E,QAAtB,EAAgCE,MAAhC,EAAwC,GAAxC,CAAlB,CAdW,CAgBX;;AACA,gBAAME,KAAK,GAAG9E,IAAI,CAACsD,QAAL,CAAcqB,MAAd,EAAsBE,SAAtB,CAAd,CAjBW,CAmBX;;AACA,gBAAME,SAAS,GAAG,KAAKC,kBAAL,CAAwBN,QAAxB,EAAkCC,MAAlC,EAA0CC,MAA1C,CAAlB,CApBW,CAsBX;;AACA,gBAAMtB,QAAQ,GAAGtD,IAAI,CAACsD,QAAL,CAAcoB,QAAd,EAAwBE,MAAxB,CAAjB;AACA,gBAAMK,aAAa,GAAGvB,IAAI,CAACC,GAAL,CAAS,GAAT,EAAcL,QAAQ,GAAG,IAAzB,CAAtB,CAxBW,CAwB2C;;AACtD,gBAAM4B,kBAAkB,GAAGD,aAAa,IAAI,IAAIF,SAAS,GAAG,EAApB,CAAxC,CAzBW,CAyBsD;AACjE;AACA;;AACA,cAAID,KAAK,GAAGI,kBAAZ,EAAgC;AAC5B,mBAAO,CAAC,KAAKV,4BAAL,CAAkCjD,EAAlC,EAAsCC,EAAtC,EAA0CC,EAA1C,EAA8CC,EAA9C,EAAkDuC,MAAlD,EAA0DC,MAA1D,EAAkErC,EAAlE,EAAsEF,UAAtE,CAAD,CAAP;AACH,WA9BU,CAgCX;;;AACA,gBAAMwD,UAAU,GAAG,KAAKd,kBAAL,CAAwB9C,EAAxB,EAA4BC,EAA5B,EAAgCC,EAAhC,EAAoCC,EAApC,EAAwCuC,MAAxC,EAAgDC,MAAhD,EAAwDI,EAAxD,EAA4DG,IAA5D,EAAkEF,KAAK,GAAG,CAA1E,EAA6EJ,QAA7E,EAAuFxC,UAAvF,CAAnB;AACA,gBAAMyD,WAAW,GAAG,KAAKf,kBAAL,CAAwB9C,EAAxB,EAA4BC,EAA5B,EAAgCC,EAAhC,EAAoCC,EAApC,EAAwCuC,MAAxC,EAAgDC,MAAhD,EAAwDO,IAAxD,EAA8D5C,EAA9D,EAAkE0C,KAAK,GAAG,CAA1E,EAA6EJ,QAA7E,EAAuFxC,UAAvF,CAApB;AAEA,iBAAO,CAAC,GAAGwD,UAAJ,EAAgB,GAAGC,WAAnB,CAAP;AACH;AAED;AACJ;AACA;;;AACYJ,QAAAA,kBAAkB,CAACxD,EAAD,EAAWC,EAAX,EAAqBC,EAArB,EAAuC;AAC7D,gBAAM2D,EAAE,GAAGrF,IAAI,CAACsF,QAAL,CAAc,IAAItF,IAAJ,EAAd,EAA0ByB,EAA1B,EAA8BD,EAA9B,CAAX;AACA,gBAAM+D,EAAE,GAAGvF,IAAI,CAACsF,QAAL,CAAc,IAAItF,IAAJ,EAAd,EAA0B0B,EAA1B,EAA8BD,EAA9B,CAAX,CAF6D,CAI7D;;AACA,gBAAM+D,IAAI,GAAGH,EAAE,CAAC/C,MAAH,EAAb;AACA,gBAAMmD,IAAI,GAAGF,EAAE,CAACjD,MAAH,EAAb;AACA,cAAIkD,IAAI,GAAG,KAAP,IAAgBC,IAAI,GAAG,KAA3B,EAAkC,OAAO,CAAP;AAElCJ,UAAAA,EAAE,CAACK,SAAH;AACAH,UAAAA,EAAE,CAACG,SAAH,GAV6D,CAY7D;;AACA,gBAAMC,GAAG,GAAG3F,IAAI,CAAC2F,GAAL,CAASN,EAAT,EAAaE,EAAb,CAAZ;AACA,gBAAMK,UAAU,GAAGlC,IAAI,CAACC,GAAL,CAAS,CAAC,CAAV,EAAaD,IAAI,CAACE,GAAL,CAAS,CAAT,EAAY+B,GAAZ,CAAb,CAAnB;AACA,gBAAME,KAAK,GAAGnC,IAAI,CAACoC,IAAL,CAAUF,UAAV,CAAd,CAf6D,CAiB7D;;AACA,iBAAOC,KAAK,GAAGnC,IAAI,CAACqC,EAApB;AACH;AAED;AACJ;AACA;;;AACYvB,QAAAA,4BAA4B,CAChCjD,EADgC,EACtBC,EADsB,EACZC,EADY,EACFC,EADE,EAEhCuC,MAFgC,EAEbC,MAFa,EAGhC5C,CAHgC,EAGrBK,UAHqB,EAIvB;AACT;AACA,gBAAMqE,GAAG,GAAG9E,QAAQ,CAACG,eAAT,CAAyBC,CAAzB,EAA4BC,EAA5B,EAAgCC,EAAhC,EAAoCC,EAApC,EAAwCC,EAAxC,EAA4CC,UAA5C,CAAZ;AACA,gBAAMsE,QAAQ,GAAG,IAAI1F,SAAJ,CAAcyF,GAAG,CAAClF,CAAlB,EAAqBkF,GAAG,CAACjF,CAAzB,CAAjB,CAHS,CAKT;;AACAkF,UAAAA,QAAQ,CAACC,KAAT,GAAiBjC,MAAM,CAACiC,KAAP,GAAe,CAAChC,MAAM,CAACgC,KAAP,GAAejC,MAAM,CAACiC,KAAvB,IAAgC5E,CAAhE;AACA2E,UAAAA,QAAQ,CAACtE,UAAT,GAAsBsC,MAAM,CAACtC,UAAP,GAAoB,CAACuC,MAAM,CAACvC,UAAP,GAAoBsC,MAAM,CAACtC,UAA5B,IAA0CL,CAApF;AACA2E,UAAAA,QAAQ,CAACE,eAAT,GAA2BlC,MAAM,CAACkC,eAAlC;AACAF,UAAAA,QAAQ,CAACG,gBAAT,GAA4BnC,MAAM,CAACmC,gBAAP,GAA0B,CAAClC,MAAM,CAACkC,gBAAP,GAA0BnC,MAAM,CAACmC,gBAAlC,IAAsD9E,CAA5G;AAEA,iBAAO2E,QAAP;AACH;AAED;AACJ;AACA;;;AACYnD,QAAAA,eAAe,CAACV,eAAD,EAA+BiE,KAA/B,EAAoD;AACvE,gBAAM7D,UAAU,GAAGJ,eAAe,CAACE,MAAnC;;AAEA,cAAI,KAAKM,MAAT,EAAiB;AACb;AACA,kBAAM0D,YAAY,GAAG,CAAED,KAAK,GAAG7D,UAAT,GAAuBA,UAAxB,IAAsCA,UAA3D;AACA,mBAAOJ,eAAe,CAACkE,YAAD,CAAf,CAA8BtF,QAArC;AACH,WAJD,MAIO;AACH;AACA,gBAAIqF,KAAK,GAAG,CAAZ,EAAe;AACX;AACA,oBAAM9E,EAAE,GAAGa,eAAe,CAAC,CAAD,CAAf,CAAmBpB,QAA9B;AACA,oBAAMQ,EAAE,GAAGY,eAAe,CAAC,CAAD,CAAf,CAAmBpB,QAA9B;AACA,qBAAOhB,IAAI,CAACsF,QAAL,CAAc,IAAItF,IAAJ,EAAd,EAA0BuB,EAA1B,EAA8BvB,IAAI,CAACsF,QAAL,CAAc,IAAItF,IAAJ,EAAd,EAA0BwB,EAA1B,EAA8BD,EAA9B,CAA9B,CAAP;AACH,aALD,MAKO,IAAI8E,KAAK,IAAI7D,UAAb,EAAyB;AAC5B;AACA,oBAAMjB,EAAE,GAAGa,eAAe,CAACI,UAAU,GAAG,CAAd,CAAf,CAAgCxB,QAA3C;AACA,oBAAMQ,EAAE,GAAGY,eAAe,CAACI,UAAU,GAAG,CAAd,CAAf,CAAgCxB,QAA3C;AACA,qBAAOhB,IAAI,CAACuG,GAAL,CAAS,IAAIvG,IAAJ,EAAT,EAAqBwB,EAArB,EAAyBxB,IAAI,CAACsF,QAAL,CAAc,IAAItF,IAAJ,EAAd,EAA0BwB,EAA1B,EAA8BD,EAA9B,CAAzB,CAAP;AACH,aALM,MAKA;AACH,qBAAOa,eAAe,CAACiE,KAAD,CAAf,CAAuBrF,QAA9B;AACH;AACJ;AACJ;AAED;AACJ;AACA;AACI;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACJ;AACA;;;AACWwF,QAAAA,MAAM,GAAQ;AACjB,iBAAO;AACHC,YAAAA,IAAI,EAAE,KAAKA,IADR;AAEH5C,YAAAA,QAAQ,EAAE,KAAKA,QAFZ;AAGHE,YAAAA,MAAM,EAAE,KAAKA,MAHV;AAIHP,YAAAA,MAAM,EAAE,KAAKA,MAJV;AAKHZ,YAAAA,MAAM,EAAE,KAAKA;AALV,WAAP;AAOH;AAED;AACJ;AACA;;;AACW8D,QAAAA,QAAQ,CAACC,IAAD,EAAkB;AAC7B,eAAKF,IAAL,GAAYE,IAAI,CAACF,IAAL,IAAa,EAAzB;AACA,eAAK5C,QAAL,GAAgB8C,IAAI,CAAC9C,QAAL,IAAiB,CAAjC;AACA,eAAKE,MAAL,GAAc4C,IAAI,CAAC5C,MAAL,IAAe,CAAC,CAA9B;AACA,eAAKP,MAAL,GAAcmD,IAAI,CAACnD,MAAL,IAAe,EAA7B;AACA,eAAKZ,MAAL,GAAc+D,IAAI,CAAC/D,MAAL,IAAe,KAA7B,CAL6B,CAO7B;;AACA,eAAKxB,uBAAL,GAA+B,IAA/B;AACH;AAED;AACJ;AACA;;;AAC0B,eAARsF,QAAQ,CAACC,IAAD,EAAsB;AACxC,gBAAMC,QAAQ,GAAG,IAAI1F,QAAJ,EAAjB;AACA0F,UAAAA,QAAQ,CAACF,QAAT,CAAkBC,IAAlB;AACA,iBAAOC,QAAP;AACH;;AA9ViB,O;;;;;iBAEI,E;;;;;;;iBAGI,C;;;;;;;iBAGF,CAAC,C;;;;;;;iBAGI,E;;;;;;;iBAGJ,K", "sourcesContent": ["import { _decorator, Vec2, CCFloat, CCInteger, Enum } from 'cc';\r\nimport { eOrientationType } from './WaveData';\r\nconst { ccclass, property } = _decorator;\r\n\r\n/**\r\n * 路径点数据\r\n */\r\n@ccclass(\"PathPoint\")\r\nexport class PathPoint {\r\n    @property({ type: CCFloat, displayName: \"X坐标\" })\r\n    public x: number = 0;\r\n\r\n    @property({ type: CCFloat, displayName: \"Y坐标\" })\r\n    public y: number = 0;\r\n\r\n    @property({ type: CCFloat, displayName: \"平滑程度\", range: [0, 1], slide: true, tooltip: \"0=直线连接, 1=最大平滑曲线\" })\r\n    public smoothness: number = 1;\r\n\r\n    @property({ type: CCInteger, displayName: \"速度\", tooltip: \"飞机在此点的速度\" })\r\n    public speed: number = 500;\r\n\r\n    @property({ type: CCInteger, displayName: \"停留时间\", tooltip: \"飞机到达此点后停留时间（毫秒）\" })\r\n    public stayDuration: number = 0;\r\n\r\n    @property({ type: Enum(eOrientationType), displayName: \"朝向类型\", tooltip: \"飞机在此点的朝向\" })\r\n    public orientationType: eOrientationType = 0;\r\n\r\n    @property({ type: CCInteger, displayName: \"朝向参数\", tooltip: \"根据朝向类型不同而不同\" })\r\n    public orientationParam: number = 0;\r\n\r\n    constructor(x: number = 0, y: number = 0) {\r\n        this.x = x;\r\n        this.y = y;\r\n    }\r\n\r\n    public get position(): Vec2 {\r\n        return new Vec2(this.x, this.y);\r\n    }\r\n\r\n    public set position(value: Vec2) {\r\n        this.x = value.x;\r\n        this.y = value.y;\r\n    }\r\n}\r\n\r\n/**\r\n * 路径数据\r\n */\r\n@ccclass(\"PathData\")\r\nexport class PathData {\r\n    @property({ displayName: '路径名称', editorOnly: true })\r\n    public name: string = \"\";\r\n\r\n    @property({ type: CCInteger, displayName: '起始点(默认0)'})\r\n    public startIdx: number = 0;\r\n\r\n    @property({ type: CCInteger, displayName: '结束点(-1代表使用路径终点)'})\r\n    public endIdx: number = -1;\r\n\r\n    @property({ type: [PathPoint], displayName: '路径点' })\r\n    public points: PathPoint[] = [];\r\n\r\n    @property({ displayName: \"是否闭合路径\", tooltip: \"路径是否形成闭环\" })\r\n    public closed: boolean = false;\r\n\r\n    // 缓存的路径数据（不参与序列化）\r\n    private _cachedSubdividedPoints: PathPoint[] | null = null;\r\n\r\n    /**\r\n     * 获取Catmull-Rom曲线上的点\r\n     * @param t 参数值 [0, 1]\r\n     * @param p0 前一个控制点（用于计算切线）\r\n     * @param p1 起始点（曲线经过此点）\r\n     * @param p2 结束点（曲线经过此点）\r\n     * @param p3 后一个控制点（用于计算切线）\r\n     * @param smoothness 平滑程度 [0, 1]，0=直线，1=最平滑曲线\r\n     */\r\n    public static catmullRomPoint(t: number, p0: Vec2, p1: Vec2, p2: Vec2, p3: Vec2, smoothness: number = 0.5): Vec2 {\r\n        // 当smoothness为0时，直接返回线性插值（直线）\r\n        if (smoothness === 0) {\r\n            return Vec2.lerp(new Vec2(), p1, p2, t);\r\n        }\r\n\r\n        const t2 = t * t;\r\n        const t3 = t2 * t;\r\n\r\n        // 标准Catmull-Rom插值公式\r\n        const catmullRom = new Vec2();\r\n        catmullRom.x = 0.5 * (\r\n            (2 * p1.x) +\r\n            (-p0.x + p2.x) * t +\r\n            (2 * p0.x - 5 * p1.x + 4 * p2.x - p3.x) * t2 +\r\n            (-p0.x + 3 * p1.x - 3 * p2.x + p3.x) * t3\r\n        );\r\n\r\n        catmullRom.y = 0.5 * (\r\n            (2 * p1.y) +\r\n            (-p0.y + p2.y) * t +\r\n            (2 * p0.y - 5 * p1.y + 4 * p2.y - p3.y) * t2 +\r\n            (-p0.y + 3 * p1.y - 3 * p2.y + p3.y) * t3\r\n        );\r\n\r\n        // 当smoothness不为1时，在线性插值和Catmull-Rom之间混合\r\n        if (smoothness < 1) {\r\n            const linear = Vec2.lerp(new Vec2(), p1, p2, t);\r\n            return Vec2.lerp(new Vec2(), linear, catmullRom, smoothness);\r\n        }\r\n\r\n        return catmullRom;\r\n    }\r\n\r\n    /**\r\n     * 获取细分后的路径点（包含完整的PathPoint信息）\r\n     * 这是推荐的新方法，替代generateCurvePoints + 重新采样的方式\r\n     */\r\n    public getSubdividedPoints(regen: boolean = false): PathPoint[] {\r\n        if (!this._cachedSubdividedPoints || regen) {\r\n            this._cachedSubdividedPoints = this.generateSubdividedPointsInternal();\r\n        }\r\n        return this._cachedSubdividedPoints!;\r\n    }\r\n\r\n    /**\r\n     * 内部方法：生成细分后的PathPoint数组\r\n     */\r\n    private generateSubdividedPointsInternal(): PathPoint[] {\r\n        const effectivePoints = this.getPoints();\r\n\r\n        if (effectivePoints.length < 2) {\r\n            return effectivePoints;\r\n        }\r\n\r\n        const subdivided: PathPoint[] = [];\r\n        const pointCount = effectivePoints.length;\r\n\r\n        // 添加第一个点\r\n        const firstPoint = effectivePoints[0];\r\n        subdivided.push(firstPoint);\r\n\r\n        // 计算需要处理的段数\r\n        const segmentCount = this.closed ? pointCount : pointCount - 1;\r\n\r\n        // 为每一段生成细分点\r\n        for (let i = 0; i < segmentCount; i++) {\r\n            const p0 = this.getControlPoint(effectivePoints, i - 1);\r\n            const p1 = effectivePoints[i].position;\r\n            const p2 = this.getControlPoint(effectivePoints, i + 1);\r\n            const p3 = this.getControlPoint(effectivePoints, i + 2);\r\n\r\n            const point = effectivePoints[i];\r\n            const pointNext = effectivePoints[(i + 1) % pointCount];\r\n\r\n            const startSmoothness = point.smoothness;\r\n            const endSmoothness = pointNext.smoothness;\r\n\r\n            // 如果任一端点的smoothness为0，则整段使用直线\r\n            if (startSmoothness === 0 || endSmoothness === 0) {\r\n                // 直线连接：只需要添加终点\r\n                subdivided.push(pointNext);\r\n            } else {\r\n                // 使用自适应细分算法\r\n                const segmentPoints = this.adaptiveSubdivision(p0, p1, p2, p3, point, pointNext);\r\n                subdivided.push(...segmentPoints);\r\n            }\r\n        }\r\n\r\n        // 处理闭合路径的重复点\r\n        if (this.closed && subdivided.length > 1) {\r\n            const firstPoint = subdivided[0];\r\n            const lastPoint = subdivided[subdivided.length - 1];\r\n            const distance = Vec2.distance(firstPoint.position, lastPoint.position);\r\n\r\n            if (distance < 0.1) {\r\n                subdivided.pop();\r\n            }\r\n        }\r\n\r\n        return subdivided;\r\n    }\r\n\r\n    /**\r\n     * 获取有效的路径点范围（考虑startIdx和endIdx）\r\n     */\r\n    private getPoints(): PathPoint[] {\r\n        if (this.points.length === 0) return [];\r\n\r\n        const startIndex = Math.max(0, Math.min(this.startIdx, this.points.length - 1));\r\n        const endIndex = this.endIdx === -1 ? this.points.length - 1 : Math.max(startIndex, Math.min(this.endIdx, this.points.length - 1));\r\n\r\n        return this.points.slice(startIndex, endIndex + 1);\r\n    }\r\n\r\n    /**\r\n     * 自适应细分算法 - 基于曲率和误差的智能细分\r\n     * @param p0 前一个控制点\r\n     * @param p1 起始点\r\n     * @param p2 结束点\r\n     * @param p3 后一个控制点\r\n     * @param point1 起始PathPoint\r\n     * @param point2 结束PathPoint\r\n     * @returns 细分后的PathPoint数组\r\n     */\r\n    private adaptiveSubdivision(\r\n        p0: Vec2, p1: Vec2, p2: Vec2, p3: Vec2,\r\n        point1: PathPoint, point2: PathPoint,\r\n        maxDepth: number = 6\r\n    ): PathPoint[] {\r\n        const avgSmoothness = (point1.smoothness + point2.smoothness) / 2;\r\n\r\n        // 如果平滑度为0，直接返回终点\r\n        if (avgSmoothness === 0) {\r\n            return [point2];\r\n        }\r\n\r\n        // 递归细分（从深度0开始）\r\n        return this.subdivideRecursive(p0, p1, p2, p3, point1, point2, 0, 1, 0, maxDepth, avgSmoothness);\r\n    }\r\n\r\n    /**\r\n     * 递归细分方法\r\n     */\r\n    private subdivideRecursive(\r\n        p0: Vec2, p1: Vec2, p2: Vec2, p3: Vec2,\r\n        point1: PathPoint, point2: PathPoint,\r\n        t1: number, t2: number, depth: number, maxDepth: number, smoothness: number\r\n    ): PathPoint[] {\r\n        // 达到最大深度，停止细分\r\n        if (depth >= maxDepth) {\r\n            return [this.createCurveInterpolatedPoint(p0, p1, p2, p3, point1, point2, t2, smoothness)];\r\n        }\r\n\r\n        const tMid = (t1 + t2) / 2;\r\n\r\n        // 计算三个点：起点、中点、终点\r\n        const startPos = PathData.catmullRomPoint(t1, p0, p1, p2, p3, smoothness);\r\n        const midPos = PathData.catmullRomPoint(tMid, p0, p1, p2, p3, smoothness);\r\n        const endPos = PathData.catmullRomPoint(t2, p0, p1, p2, p3, smoothness);\r\n\r\n        // 计算线性插值的中点\r\n        const linearMid = Vec2.lerp(new Vec2(), startPos, endPos, 0.5);\r\n\r\n        // 计算误差（曲线中点与线性中点的距离）\r\n        const error = Vec2.distance(midPos, linearMid);\r\n\r\n        // 计算曲率（使用三点法）\r\n        const curvature = this.calculateCurvature(startPos, midPos, endPos);\r\n\r\n        // 动态误差阈值：考虑距离和曲率\r\n        const distance = Vec2.distance(startPos, endPos);\r\n        const baseThreshold = Math.max(0.5, distance * 0.01); // 基础阈值\r\n        const curvatureThreshold = baseThreshold * (1 + curvature * 10); // 曲率调整\r\n        // console.log('error:', error, 'curvatureThreshold:', curvatureThreshold);\r\n        // 如果误差小于阈值，不需要进一步细分\r\n        if (error < curvatureThreshold) {\r\n            return [this.createCurveInterpolatedPoint(p0, p1, p2, p3, point1, point2, t2, smoothness)];\r\n        }\r\n\r\n        // 需要细分：递归处理两个子段\r\n        const leftPoints = this.subdivideRecursive(p0, p1, p2, p3, point1, point2, t1, tMid, depth + 1, maxDepth, smoothness);\r\n        const rightPoints = this.subdivideRecursive(p0, p1, p2, p3, point1, point2, tMid, t2, depth + 1, maxDepth, smoothness);\r\n\r\n        return [...leftPoints, ...rightPoints];\r\n    }\r\n\r\n    /**\r\n     * 计算三点的曲率\r\n     */\r\n    private calculateCurvature(p1: Vec2, p2: Vec2, p3: Vec2): number {\r\n        const v1 = Vec2.subtract(new Vec2(), p2, p1);\r\n        const v2 = Vec2.subtract(new Vec2(), p3, p2);\r\n\r\n        // 避免除零\r\n        const len1 = v1.length();\r\n        const len2 = v2.length();\r\n        if (len1 < 0.001 || len2 < 0.001) return 0;\r\n\r\n        v1.normalize();\r\n        v2.normalize();\r\n\r\n        // 计算角度变化\r\n        const dot = Vec2.dot(v1, v2);\r\n        const clampedDot = Math.max(-1, Math.min(1, dot));\r\n        const angle = Math.acos(clampedDot);\r\n\r\n        // 归一化曲率值\r\n        return angle / Math.PI;\r\n    }\r\n\r\n    /**\r\n     * 创建曲线插值的PathPoint（使用Catmull-Rom曲线）\r\n     */\r\n    private createCurveInterpolatedPoint(\r\n        p0: Vec2, p1: Vec2, p2: Vec2, p3: Vec2,\r\n        point1: PathPoint, point2: PathPoint,\r\n        t: number, smoothness: number\r\n    ): PathPoint {\r\n        // 使用Catmull-Rom曲线计算位置\r\n        const pos = PathData.catmullRomPoint(t, p0, p1, p2, p3, smoothness);\r\n        const newPoint = new PathPoint(pos.x, pos.y);\r\n\r\n        // 插值其他属性\r\n        newPoint.speed = point1.speed + (point2.speed - point1.speed) * t;\r\n        newPoint.smoothness = point1.smoothness + (point2.smoothness - point1.smoothness) * t;\r\n        newPoint.orientationType = point1.orientationType;\r\n        newPoint.orientationParam = point1.orientationParam + (point2.orientationParam - point1.orientationParam) * t;\r\n\r\n        return newPoint;\r\n    }\r\n\r\n    /**\r\n     * 获取有效路径点的控制点（处理边界情况）\r\n     */\r\n    private getControlPoint(effectivePoints: PathPoint[], index: number): Vec2 {\r\n        const pointCount = effectivePoints.length;\r\n\r\n        if (this.closed) {\r\n            // 闭合路径，使用循环索引\r\n            const wrappedIndex = ((index % pointCount) + pointCount) % pointCount;\r\n            return effectivePoints[wrappedIndex].position;\r\n        } else {\r\n            // 开放路径，边界处理\r\n            if (index < 0) {\r\n                // 延伸第一个点\r\n                const p0 = effectivePoints[0].position;\r\n                const p1 = effectivePoints[1].position;\r\n                return Vec2.subtract(new Vec2(), p0, Vec2.subtract(new Vec2(), p1, p0));\r\n            } else if (index >= pointCount) {\r\n                // 延伸最后一个点\r\n                const p0 = effectivePoints[pointCount - 2].position;\r\n                const p1 = effectivePoints[pointCount - 1].position;\r\n                return Vec2.add(new Vec2(), p1, Vec2.subtract(new Vec2(), p1, p0));\r\n            } else {\r\n                return effectivePoints[index].position;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 获取控制点（处理边界情况）- 保留用于兼容性\r\n     */\r\n    // private getControlPoint(index: number): Vec2 {\r\n    //     const pointCount = this.points.length;\r\n\r\n    //     if (this.closed) {\r\n    //         // 闭合路径，使用循环索引\r\n    //         const wrappedIndex = ((index % pointCount) + pointCount) % pointCount;\r\n    //         return this.points[wrappedIndex].position;\r\n    //     } else {\r\n    //         // 开放路径，边界处理\r\n    //         if (index < 0) {\r\n    //             // 延伸第一个点\r\n    //             const p0 = this.points[0].position;\r\n    //             const p1 = this.points[1].position;\r\n    //             return Vec2.subtract(new Vec2(), p0, Vec2.subtract(new Vec2(), p1, p0));\r\n    //         } else if (index >= pointCount) {\r\n    //             // 延伸最后一个点\r\n    //             const p0 = this.points[pointCount - 2].position;\r\n    //             const p1 = this.points[pointCount - 1].position;\r\n    //             return Vec2.add(new Vec2(), p1, Vec2.subtract(new Vec2(), p1, p0));\r\n    //         } else {\r\n    //             return this.points[index].position;\r\n    //         }\r\n    //     }\r\n    // }\r\n\r\n    /**\r\n     * 自定义序列化 - 排除缓存数据\r\n     */\r\n    public toJSON(): any {\r\n        return {\r\n            name: this.name,\r\n            startIdx: this.startIdx,\r\n            endIdx: this.endIdx,\r\n            points: this.points,\r\n            closed: this.closed\r\n        };\r\n    }\r\n\r\n    /**\r\n     * 自定义反序列化 - 清除缓存确保重新计算\r\n     */\r\n    public fromJSON(data: any): void {\r\n        this.name = data.name || \"\";\r\n        this.startIdx = data.startIdx || 0;\r\n        this.endIdx = data.endIdx || -1;\r\n        this.points = data.points || [];\r\n        this.closed = data.closed || false;\r\n\r\n        // 清除缓存，确保使用新数据重新计算\r\n        this._cachedSubdividedPoints = null;\r\n    }\r\n\r\n    /**\r\n     * 静态工厂方法 - 从JSON创建PathData实例\r\n     */\r\n    public static fromJSON(data: any): PathData {\r\n        const pathData = new PathData();\r\n        pathData.fromJSON(data);\r\n        return pathData;\r\n    }\r\n}"]}