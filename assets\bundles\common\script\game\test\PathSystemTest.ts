import { _decorator, Component, log } from 'cc';
import { PathData, PathPoint } from '../data/PathData';

const { ccclass, property } = _decorator;

/**
 * 路径系统测试类 - 验证新的getSubdividedPoints方法
 */
@ccclass('PathSystemTest')
export class PathSystemTest extends Component {

    onLoad() {
        this.testRecursiveDepth();
        this.testCurveGeneration();
        this.testSubdividedPoints();
        this.testAdaptiveSubdivision();
        this.testSpeedSampling();
    }

    /**
     * 测试递归深度是否正常工作
     */
    private testRecursiveDepth() {
        log("=== 测试递归深度修复 ===");

        const pathData = new PathData();

        // 创建一个需要大量细分的急转弯
        const point1 = new PathPoint(0, 0);
        point1.speed = 300;
        point1.smoothness = 1.0; // 最高平滑度

        const point2 = new PathPoint(50, 0);
        point2.speed = 300;
        point2.smoothness = 1.0;

        const point3 = new PathPoint(50, 50);
        point3.speed = 300;
        point3.smoothness = 1.0;

        const point4 = new PathPoint(0, 50);
        point4.speed = 300;
        point4.smoothness = 1.0;

        pathData.points.length = 0;
        pathData.points.push(point1, point2, point3, point4);

        const subdivided = pathData.getSubdividedPoints();
        log(`急转弯路径细分点数量: ${subdivided.length}`);

        if (subdivided.length > 4) {
            log("✓ 递归细分正常工作，产生了额外的细分点");

            // 检查细分点是否形成平滑曲线
            let hasSmoothing = false;
            for (let i = 1; i < subdivided.length - 1; i++) {
                const prev = subdivided[i - 1];
                const curr = subdivided[i];
                const next = subdivided[i + 1];

                // 检查是否有曲线特征（不在直线上）
                const linearX = prev.x + (next.x - prev.x) * 0.5;
                const linearY = prev.y + (next.y - prev.y) * 0.5;
                const deviation = Math.sqrt(Math.pow(curr.x - linearX, 2) + Math.pow(curr.y - linearY, 2));

                if (deviation > 0.5) {
                    hasSmoothing = true;
                    break;
                }
            }

            if (hasSmoothing) {
                log("✓ 检测到曲线平滑效果");
            } else {
                log("⚠ 细分点可能仍然在直线上");
            }
        } else {
            log("⚠ 递归细分可能仍有问题，细分点数量过少");
        }
    }

    /**
     * 测试曲线生成是否正常
     */
    private testCurveGeneration() {
        log("=== 测试曲线生成 ===");

        const pathData = new PathData();

        // 创建一个简单的弯曲路径
        const point1 = new PathPoint(0, 0);
        point1.speed = 300;
        point1.smoothness = 1.0; // 高平滑度

        const point2 = new PathPoint(100, 0);
        point2.speed = 400;
        point2.smoothness = 1.0;

        const point3 = new PathPoint(100, 100);
        point3.speed = 300;
        point3.smoothness = 1.0;

        pathData.points.length = 0;
        pathData.points.push(point1, point2, point3);

        const subdivided = pathData.getSubdividedPoints();
        log(`曲线路径点数量: ${subdivided.length}`);

        // 检查是否真的是曲线（中间点不应该在直线上）
        if (subdivided.length >= 3) {
            const start = subdivided[0];
            const mid = subdivided[Math.floor(subdivided.length / 2)];
            const end = subdivided[subdivided.length - 1];

            log(`起点: (${start.x.toFixed(1)}, ${start.y.toFixed(1)})`);
            log(`中点: (${mid.x.toFixed(1)}, ${mid.y.toFixed(1)})`);
            log(`终点: (${end.x.toFixed(1)}, ${end.y.toFixed(1)})`);

            // 检查中点是否偏离直线
            const linearMidX = (start.x + end.x) / 2;
            const linearMidY = (start.y + end.y) / 2;
            const deviation = Math.sqrt(Math.pow(mid.x - linearMidX, 2) + Math.pow(mid.y - linearMidY, 2));

            log(`中点偏离直线距离: ${deviation.toFixed(2)}`);

            if (deviation > 1) {
                log("✓ 曲线生成正常");
            } else {
                log("⚠ 曲线可能退化为直线");
            }
        }
    }

    /**
     * 测试新的细分点方法
     */
    private testSubdividedPoints() {
        log("=== 测试getSubdividedPoints方法 ===");
        
        const pathData = new PathData();
        
        // 创建具有不同速度的路径点
        const point1 = new PathPoint(0, 0);
        point1.speed = 200;
        point1.smoothness = 1.0;
        
        const point2 = new PathPoint(100, 0);
        point2.speed = 600;
        point2.smoothness = 1.0;
        
        const point3 = new PathPoint(200, 100);
        point3.speed = 300;
        point3.smoothness = 0.5;
        
        pathData.points.length = 0;
        pathData.points.push(point1, point2, point3);
        
        // 获取细分点
        const subdivided = pathData.getSubdividedPoints();
        log(`原始路径点数量: ${pathData.points.length}`);
        log(`细分后点数量: ${subdivided.length}`);
        
        // 检查前几个细分点的信息
        for (let i = 0; i < Math.min(5, subdivided.length); i++) {
            const point = subdivided[i];
            log(`细分点 ${i}: 位置=(${point.x.toFixed(1)}, ${point.y.toFixed(1)}), 速度=${point.speed.toFixed(1)}, 平滑度=${point.smoothness.toFixed(2)}`);
        }
    }

    /**
     * 测试自适应细分算法的效果
     */
    private testAdaptiveSubdivision() {
        log("=== 测试自适应细分算法效果 ===");

        // 测试案例1：水平直线（高平滑度但低曲率）
        log("案例1: 水平直线 (高平滑度，低曲率)");
        const pathData1 = new PathData();
        const point1a = new PathPoint(0, 0);
        point1a.smoothness = 1.0; // 高平滑度
        const point1b = new PathPoint(200, 0);
        point1b.smoothness = 1.0; // 高平滑度
        pathData1.points.length = 0;
        pathData1.points.push(point1a, point1b);

        const subdivided1 = pathData1.getSubdividedPoints();
        log(`  原始点: 2, 细分后: ${subdivided1.length}, 倍率: ${(subdivided1.length / 2).toFixed(2)}`);

        // 测试案例2：急转弯（高平滑度且高曲率）
        log("案例2: 急转弯 (高平滑度，高曲率)");
        const pathData2 = new PathData();
        const point2a = new PathPoint(0, 0);
        point2a.smoothness = 1.0;
        const point2b = new PathPoint(100, 0);
        point2b.smoothness = 1.0;
        const point2c = new PathPoint(100, 100);
        point2c.smoothness = 1.0;
        pathData2.points.length = 0;
        pathData2.points.push(point2a, point2b, point2c);

        const subdivided2 = pathData2.getSubdividedPoints();
        log(`  原始点: 3, 细分后: ${subdivided2.length}, 倍率: ${(subdivided2.length / 3).toFixed(2)}`);

        // 测试案例3：低平滑度路径
        log("案例3: 低平滑度路径");
        const pathData3 = new PathData();
        const point3a = new PathPoint(0, 0);
        point3a.smoothness = 0.2; // 低平滑度
        const point3b = new PathPoint(100, 50);
        point3b.smoothness = 0.2;
        const point3c = new PathPoint(200, 0);
        point3c.smoothness = 0.2;
        pathData3.points.length = 0;
        pathData3.points.push(point3a, point3b, point3c);

        const subdivided3 = pathData3.getSubdividedPoints();
        log(`  原始点: 3, 细分后: ${subdivided3.length}, 倍率: ${(subdivided3.length / 3).toFixed(2)}`);

        // 测试案例4：混合平滑度
        log("案例4: 混合平滑度路径");
        const pathData4 = new PathData();
        const point4a = new PathPoint(0, 0);
        point4a.smoothness = 0.0; // 直线
        const point4b = new PathPoint(100, 0);
        point4b.smoothness = 1.0; // 高平滑度
        const point4c = new PathPoint(200, 100);
        point4c.smoothness = 1.0;
        pathData4.points.length = 0;
        pathData4.points.push(point4a, point4b, point4c);

        const subdivided4 = pathData4.getSubdividedPoints();
        log(`  原始点: 3, 细分后: ${subdivided4.length}, 倍率: ${(subdivided4.length / 3).toFixed(2)}`);

        log("✓ 自适应细分算法能够根据实际曲率智能调整细分密度");
    }

    /**
     * 测试速度采样的准确性
     */
    private testSpeedSampling() {
        log("=== 测试速度采样准确性 ===");
        
        const pathData = new PathData();
        
        // 创建速度变化明显的路径
        const point1 = new PathPoint(0, 0);
        point1.speed = 100;
        
        const point2 = new PathPoint(150, 0);
        point2.speed = 500;
        
        const point3 = new PathPoint(300, 0);
        point3.speed = 200;
        
        pathData.points = [point1, point2, point3];
        
        const subdivided = pathData.getSubdividedPoints();
        
        // 检查速度插值的合理性
        log("速度变化检查:");
        const checkIndices = [0, Math.floor(subdivided.length * 0.25), Math.floor(subdivided.length * 0.5), Math.floor(subdivided.length * 0.75), subdivided.length - 1];
        
        for (const index of checkIndices) {
            if (index < subdivided.length) {
                const point = subdivided[index];
                const progress = (index / (subdivided.length - 1) * 100).toFixed(1);
                log(`  进度${progress}%: 位置=(${point.x.toFixed(1)}, ${point.y.toFixed(1)}), 速度=${point.speed.toFixed(1)}`);
            }
        }
        
        // 验证速度插值的连续性
        let speedJumps = 0;
        for (let i = 1; i < subdivided.length; i++) {
            const speedDiff = Math.abs(subdivided[i].speed - subdivided[i-1].speed);
            if (speedDiff > 50) { // 如果速度跳跃超过50，认为是异常
                speedJumps++;
            }
        }
        log(`检测到的速度跳跃次数: ${speedJumps}`);
        
        if (speedJumps === 0) {
            log("✓ 速度插值连续性良好");
        } else {
            log("⚠ 速度插值存在跳跃，可能需要调整");
        }
    }
}
