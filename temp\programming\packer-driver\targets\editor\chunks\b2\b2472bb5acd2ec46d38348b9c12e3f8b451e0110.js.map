{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/main/MainUI.ts"], "names": ["_decorator", "Component", "Prefab", "UIMgr", "logInfo", "MyApp", "csproto", "DevLoginUI", "WECHAT", "BundleName", "ccclass", "property", "MainUI", "onLoad", "start", "netMgr", "registerHandler", "cs", "CS_CMD", "CS_CMD_KICK_OFF", "onKickOff", "needLogin", "connect", "openUI", "plane", "resMgr", "loadAsync", "Common", "planeMgr", "initPlanePreFab", "onDestroy", "unregister<PERSON><PERSON><PERSON>", "msg", "disableReconnect", "update", "deltaTime"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;;AAEvBC,MAAAA,K,iBAAAA,K;;AACUC,MAAAA,O,iBAAAA,O;;AACVC,MAAAA,K,iBAAAA,K;;AACFC,MAAAA,O;;AACEC,MAAAA,U,iBAAAA,U;;AAEAC,MAAAA,M,UAAAA,M;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;;;OAGH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;;wBAGjBY,M,WADZF,OAAO,CAAC,QAAD,C,gBAAR,MACaE,MADb,SAC4BX,SAD5B,CACsC;AAClCY,QAAAA,MAAM,GAAS,CACd;;AAEU,cAALC,KAAK,GAAG;AACV;AAAA;AAAA,8BAAMC,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBC,eAA/C,EAAgE,KAAKC,SAArE,EAAgF,IAAhF;;AACA,cAAI;AAAA;AAAA,wCAAWC,SAAf,EAA0B;AACtB,gBAAIb,MAAJ,EAAY;AACR;AAAA;AAAA,kCAAMO,MAAN,CAAaO,OAAb;AACH,aAFD,MAEO;AACH,oBAAM;AAAA;AAAA,kCAAMC,MAAN;AAAA;AAAA,2CAAN;AACH;AACJ;;AACD,cAAIC,KAAK,GAAG,MAAM;AAAA;AAAA,8BAAMC,MAAN,CAAaC,SAAb,CAAuB;AAAA;AAAA,wCAAWC,MAAlC,EAA0C,cAA1C,EAA0DzB,MAA1D,CAAlB;AACA;AAAA;AAAA,8BAAM0B,QAAN,CAAeC,eAAf,CAA+BL,KAA/B;AACH;;AAESM,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,8BAAMf,MAAN,CAAagB,iBAAb,CAA+B;AAAA;AAAA,kCAAQd,EAAR,CAAWC,MAAX,CAAkBC,eAAjD,EAAkE,KAAKC,SAAvE,EAAkF,IAAlF;AACH;;AACDA,QAAAA,SAAS,CAACY,GAAD,EAA0B;AAC/B;AAAA;AAAA,kCAAQ,QAAR,EAAkB,WAAlB;AACA;AAAA;AAAA,8BAAMjB,MAAN,CAAakB,gBAAb;;AACA,cAAIzB,MAAJ,EAAY,CACX,CADD,MACO;AACH;AAAA;AAAA,gCAAMe,MAAN;AAAA;AAAA;AACH;AACJ;;AAEDW,QAAAA,MAAM,CAACC,SAAD,EAAoB,CAEzB;;AA/BiC,O", "sourcesContent": ["import { _decorator, Component, Prefab } from 'cc';\n\nimport { UIMgr } from 'db://assets/scripts/core/base/UIMgr';\nimport { logError, logInfo } from 'db://assets/scripts/utils/Logger';\nimport { MyApp } from '../../app/MyApp';\nimport csproto from '../../autogen/pb/cs_proto.js';\nimport { DevLoginUI } from '../gameui/DevLoginUI';\n\nimport { WECHAT } from 'cc/env';\nimport { BundleName } from 'db://assets/bundles/common/script/const/BundleConst';\n\n\nconst { ccclass, property } = _decorator;\n\n@ccclass('MainUI')\nexport class MainUI extends Component {\n    onLoad(): void {\n    }\n\n    async start() {\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_KICK_OFF, this.onKickOff, this)\n        if (DevLoginUI.needLogin) {\n            if (WECHAT) {\n                MyApp.netMgr.connect();\n            } else {\n                await UIMgr.openUI(DevLoginUI)\n            }\n        }\n        let plane = await MyApp.resMgr.loadAsync(BundleName.Common, \"prefab/Plane\", Prefab)\n        MyApp.planeMgr.initPlanePreFab(plane)\n    }\n\n    protected onDestroy(): void {\n        MyApp.netMgr.unregisterHandler(csproto.cs.CS_CMD.CS_CMD_KICK_OFF, this.onKickOff, this)\n    }\n    onKickOff(msg: csproto.cs.IS2CMsg) {\n        logInfo(\"MainUI\", \"onKickOff\")\n        MyApp.netMgr.disableReconnect()\n        if (WECHAT) {\n        } else {\n            UIMgr.openUI(DevLoginUI)\n        }\n    }\n\n    update(deltaTime: number) {\n\n    }\n}\n\n"]}