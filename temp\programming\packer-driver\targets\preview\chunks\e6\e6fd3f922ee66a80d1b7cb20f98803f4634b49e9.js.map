{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/Plane.ts"], "names": ["_decorator", "Component", "instantiate", "<PERSON><PERSON><PERSON><PERSON>", "Material", "Prefab", "sp", "UITransform", "MyApp", "StateEvent", "StateMachine", "ccclass", "property", "Plane", "Skeleton", "_prefabeNode", "_planeData", "_curEvent", "IDLE", "_aniCallFunc", "undefined", "_isInit", "_stateMachine", "_hurtActDuration", "onLoad", "update", "dt", "resetRoleEffect", "reset", "removeFromParent", "initPlane", "planeData", "path", "recoursePrefab", "resMgr", "loadAsync", "then", "prefab", "node", "<PERSON><PERSON><PERSON><PERSON>", "spine", "getComponentInChildren", "premultipliedAlpha", "setPosition", "getComponent", "height", "setPlaneState", "initializeStateMachine", "playFlashAnim", "material", "customMaterial", "event", "callback", "handleEvent", "onEnter", "ENTER", "onMoveCommand", "isLeft", "MOVE_LEFT_COMMAND", "MOVE_RIGHT_COMMAND", "onMoveEnd", "MOVE_END", "onAttackCommand", "ATTACK_COMMAND", "onGetHit", "GET_HIT", "onDie", "DIE", "onDodgeCommand", "DODGE_COMMAND", "setAnimSpeed", "speed", "timeScale"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,O,OAAAA,O;AAASC,MAAAA,Q,OAAAA,Q;AAA8BC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,E,OAAAA,E;AAAIC,MAAAA,W,OAAAA,W;;AACvFC,MAAAA,K,iBAAAA,K;;AAEeC,MAAAA,U,iBAAAA,U;;AACfC,MAAAA,Y,iBAAAA,Y;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBZ,U;;uBAGjBa,K,WADZF,OAAO,CAAC,OAAD,C,UAGHC,QAAQ,CAACN,EAAE,CAACQ,QAAJ,C,2BAHb,MACaD,KADb,SAC2BZ,SAD3B,CACqC;AAAA;AAAA;;AAAA;;AAAA,eAIzBc,YAJyB,GAIG,IAJH;AAAA,eAMjCC,UANiC,GAMF,IANE;AAMG;AANH,eAOjCC,SAPiC,GAOT;AAAA;AAAA,wCAAWC,IAPF;AAOO;AAPP,eAQjCC,YARiC,GAQSC,SART;AAQmB;AARnB,eASjCC,OATiC,GASd,KATc;AASR;AATQ,eAUjCC,aAViC,GAUhB;AAAA;AAAA,6CAVgB;AAAA,eAWjCC,gBAXiC,GAWd,GAXc;AAAA;;AAWT;AAEdC,QAAAA,MAAM,GAAS,CACrB;AACA;AACA;AACH;;AAESC,QAAAA,MAAM,CAACC,EAAD,EAAmB;AAC/B,cAAI,KAAKH,gBAAL,GAAwB,CAA5B,EAA+B;AAC3B,iBAAKA,gBAAL,IAAyBG,EAAzB;;AACA,gBAAI,KAAKH,gBAAL,IAAyB,CAA7B,EAAgC;AAC5B,mBAAKI,eAAL;AACH;AACJ;AACJ;;AAEDC,QAAAA,KAAK,GAAG;AAAA;;AACJ,eAAKN,aAAL,CAAmBM,KAAnB;;AACA,eAAKX,SAAL,GAAiB;AAAA;AAAA,wCAAWC,IAA5B;AACA,eAAKS,eAAL;AACA,qCAAKZ,YAAL,gCAAmBc,gBAAnB;AACA,eAAKd,YAAL,GAAoB,IAApB;AACH;;AAEDe,QAAAA,SAAS,CAACC,SAAD,EAAuB;AAC5B;AACA,eAAKf,UAAL,GAAkBe,SAAlB;AACA,eAAKV,OAAL,GAAe,KAAf;AACA,cAAIW,IAAI,GAAG,KAAKhB,UAAL,CAAgBiB,cAA3B;AACA;AAAA;AAAA,8BAAMC,MAAN,CAAaC,SAAb,CAAuBH,IAAvB,EAA6B3B,MAA7B,EAAqC+B,IAArC,CAA2CC,MAAD,IAAY;AAClD,iBAAKtB,YAAL,GAAoBb,WAAW,CAACmC,MAAD,CAA/B;AACA,iBAAKC,IAAL,CAAUC,QAAV,CAAmB,KAAKxB,YAAxB;AACA,iBAAKyB,KAAL,GAAa,KAAKzB,YAAL,CAAkB0B,sBAAlB,CAAyCnC,EAAE,CAACQ,QAA5C,CAAb;;AACA,gBAAI,KAAK0B,KAAT,EAAgB;AACZ,mBAAKA,KAAL,CAAWE,kBAAX,GAAgC,KAAhC;AACA,mBAAKrB,OAAL,GAAe,IAAf;AACA,mBAAKmB,KAAL,CAAWF,IAAX,CAAgBK,WAAhB,CAA4B,CAA5B,EAA+B,CAAC,KAAKH,KAAL,CAAWF,IAAX,CAAgBM,YAAhB,CAA6BrC,WAA7B,EAA2CsC,MAA5C,GAAqD,CAApF;AACA,mBAAKC,aAAL,CAAmB,KAAK7B,SAAxB,EAAmC,KAAKE,YAAxC;AACH;;AACD,iBAAKG,aAAL,CAAmByB,sBAAnB,CAA0C,KAAKP,KAA/C;AACH,WAXD;AAYH,SArDgC,CAuDjC;;;AACMQ,QAAAA,aAAa,GAAG;AAAA;;AAAA;AAClB,gBAAI,CAAC,KAAI,CAACR,KAAV,EAAiB;AACb;AACH;;AACD,gBAAIS,QAAQ,SAAS;AAAA;AAAA,gCAAMf,MAAN,CAAaC,SAAb,CAAuB,oBAAvB,EAA6C/B,QAA7C,CAArB;;AACA,gBAAI6C,QAAJ,EAAa;AACT,cAAA,KAAI,CAACT,KAAL,CAAWU,cAAX,GAA4BD,QAA5B;AACH;;AACD,YAAA,KAAI,CAAC1B,gBAAL,GAAwB,GAAxB;AARkB;AASrB;;AAEDI,QAAAA,eAAe,GAAE;AACb,eAAKJ,gBAAL,GAAwB,CAAxB;;AACA,cAAI,CAAC,KAAKiB,KAAV,EAAiB;AACb;AACH;;AACD,eAAKA,KAAL,CAAYU,cAAZ,GAA6B,IAA7B;AACH;;AAGDJ,QAAAA,aAAa,CAACK,KAAD,EAAmBC,QAAnB,EAAqD;AAC9D,cAAI,CAAC,KAAK/B,OAAV,EAAmB;AACf,iBAAKJ,SAAL,GAAiBkC,KAAjB;AACA,iBAAKhC,YAAL,GAAoBiC,QAApB;AACA,mBAAO,KAAP;AACH;;AACD,iBAAO,KAAK9B,aAAL,CAAmB+B,WAAnB,CAA+BF,KAA/B,EAAqCC,QAArC,CAAP;AACH,SAnFgC,CAqFjC;;;AACOE,QAAAA,OAAO,CAACF,QAAD,EAA2B;AACrC,iBAAO,KAAKN,aAAL,CAAmB;AAAA;AAAA,wCAAWS,KAA9B,EAAqCH,QAArC,CAAP;AACH,SAxFgC,CA0FjC;;;AACOI,QAAAA,aAAa,CAACC,MAAD,EAAyBL,QAAzB,EAAmD;AAAA,cAAlDK,MAAkD;AAAlDA,YAAAA,MAAkD,GAAjC,KAAiC;AAAA;;AACnE,cAAIN,KAAK,GAAGM,MAAM,GAAG;AAAA;AAAA,wCAAWC,iBAAd,GAAkC;AAAA;AAAA,wCAAWC,kBAA/D;AACA,iBAAO,KAAKb,aAAL,CAAmBK,KAAnB,EAA0BC,QAA1B,CAAP;AACH,SA9FgC,CAgGjC;;;AACOQ,QAAAA,SAAS,CAACR,QAAD,EAA2B;AACvC,iBAAO,KAAKN,aAAL,CAAmB;AAAA;AAAA,wCAAWe,QAA9B,EAAwCT,QAAxC,CAAP;AACH,SAnGgC,CAqGjC;;;AACOU,QAAAA,eAAe,CAACV,QAAD,EAA2B;AAC7C,iBAAO,KAAKN,aAAL,CAAmB;AAAA;AAAA,wCAAWiB,cAA9B,EAA8CX,QAA9C,CAAP;AACH,SAxGgC,CA0GjC;;;AACOY,QAAAA,QAAQ,CAACZ,QAAD,EAA2B;AACtC,eAAKJ,aAAL;AACA,iBAAO,KAAKF,aAAL,CAAmB;AAAA;AAAA,wCAAWmB,OAA9B,EAAuCb,QAAvC,CAAP;AACH,SA9GgC,CAgHjC;;;AACOc,QAAAA,KAAK,CAACd,QAAD,EAA2B;AACnC,iBAAO,KAAKN,aAAL,CAAmB;AAAA;AAAA,wCAAWqB,GAA9B,EAAmCf,QAAnC,CAAP;AACH;;AAEMgB,QAAAA,cAAc,CAAChB,QAAD,EAA2B;AAC5C,iBAAO,KAAKN,aAAL,CAAmB;AAAA;AAAA,wCAAWuB,aAA9B,EAA6CjB,QAA7C,CAAP;AACH;;AAEDkB,QAAAA,YAAY,CAACC,KAAD,EAAgB;AACxB,cAAI,KAAK/B,KAAL,IAAcrC,OAAO,CAAC,KAAKqC,KAAN,CAAzB,EAAsC;AAClC,iBAAKA,KAAL,CAAWgC,SAAX,GAAuBD,KAAvB;AACH;AACJ;;AA7HgC,O;;;;;iBAGL,I", "sourcesContent": ["import { _decorator, Component, instantiate, isValid, Material, MeshRenderer, Node, Prefab, sp, UITransform } from 'cc';\r\nimport { MyApp } from 'db://assets/bundles/common/script/app/MyApp';\r\nimport { PlaneData } from '../data/plane/PlaneData';\r\nimport { StateCallback, StateEvent } from '../plane/StateDefine';\r\nimport { StateMachine } from '../plane/StateMachine';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('Plane')\r\nexport class Plane extends Component {\r\n\r\n    @property(sp.Skeleton)\r\n    spine: sp.Skeleton | null = null\r\n    private _prefabeNode: Node | null = null;\r\n\r\n    _planeData: PlaneData | null = null;//飞机数据\r\n    _curEvent: StateEvent = StateEvent.IDLE;//当前动画名称\r\n    _aniCallFunc: StateCallback | undefined = undefined;//动画回调\r\n    _isInit: boolean = false;//是否初始化完成\r\n    _stateMachine =  new StateMachine();\r\n    _hurtActDuration = 0.2; // 受伤动画持续时间\r\n\r\n    protected onLoad(): void {\r\n        // if (!this.spine) {\r\n        //     this.spine = this.getComponentInChildren(sp.Skeleton);\r\n        // }\r\n    }\r\n\r\n    protected update(dt: number): void {\r\n        if (this._hurtActDuration > 0) {\r\n            this._hurtActDuration -= dt;\r\n            if (this._hurtActDuration <= 0) {\r\n                this.resetRoleEffect();\r\n            }\r\n        }\r\n    }\r\n\r\n    reset() {\r\n        this._stateMachine.reset();\r\n        this._curEvent = StateEvent.IDLE;\r\n        this.resetRoleEffect();\r\n        this._prefabeNode?.removeFromParent()\r\n        this._prefabeNode = null\r\n    }\r\n\r\n    initPlane(planeData: PlaneData) {\r\n        // 初始化飞机数据\r\n        this._planeData = planeData;\r\n        this._isInit = false;\r\n        let path = this._planeData.recoursePrefab;\r\n        MyApp.resMgr.loadAsync(path, Prefab).then((prefab) => {\r\n            this._prefabeNode = instantiate(prefab)\r\n            this.node.addChild(this._prefabeNode)\r\n            this.spine = this._prefabeNode.getComponentInChildren(sp.Skeleton);\r\n            if (this.spine) {\r\n                this.spine.premultipliedAlpha = false;\r\n                this._isInit = true;\r\n                this.spine.node.setPosition(0, -this.spine.node.getComponent(UITransform)!.height / 2);\r\n                this.setPlaneState(this._curEvent, this._aniCallFunc);\r\n            }\r\n            this._stateMachine.initializeStateMachine(this.spine!);\r\n        });\r\n    }\r\n\r\n    // 播放闪白动画\r\n    async playFlashAnim() {\r\n        if (!this.spine) {\r\n            return\r\n        }\r\n        let material = await MyApp.resMgr.loadAsync(\"effect/flash/flash\", Material);\r\n        if (material){\r\n            this.spine.customMaterial = material;\r\n        }\r\n        this._hurtActDuration = 0.2;\r\n    }\r\n\r\n    resetRoleEffect(){\r\n        this._hurtActDuration = 0;\r\n        if (!this.spine) {\r\n            return\r\n        }\r\n        this.spine!.customMaterial = null;\r\n    }\r\n\r\n\r\n    setPlaneState(event: StateEvent,callback?: StateCallback):boolean {\r\n        if (!this._isInit) {\r\n            this._curEvent = event;\r\n            this._aniCallFunc = callback;\r\n            return false;\r\n        }\r\n        return this._stateMachine.handleEvent(event,callback);\r\n    }\r\n    \r\n    // 移动命令（通用移动）\r\n    public onEnter(callback?: StateCallback) {\r\n        return this.setPlaneState(StateEvent.ENTER, callback);\r\n    }\r\n\r\n    // 移动命令\r\n    public onMoveCommand(isLeft:boolean = false, callback?: StateCallback) {\r\n        let event = isLeft ? StateEvent.MOVE_LEFT_COMMAND : StateEvent.MOVE_RIGHT_COMMAND;\r\n        return this.setPlaneState(event, callback);\r\n    }\r\n\r\n    // 移动结束\r\n    public onMoveEnd(callback?: StateCallback) {\r\n        return this.setPlaneState(StateEvent.MOVE_END, callback);\r\n    }\r\n\r\n    // 攻击命令\r\n    public onAttackCommand(callback?: StateCallback) {\r\n        return this.setPlaneState(StateEvent.ATTACK_COMMAND, callback);\r\n    }\r\n\r\n    // 受击事件\r\n    public onGetHit(callback?: StateCallback) {\r\n        this.playFlashAnim();\r\n        return this.setPlaneState(StateEvent.GET_HIT, callback);\r\n    }\r\n\r\n    // 死亡事件\r\n    public onDie(callback?: StateCallback) {\r\n        return this.setPlaneState(StateEvent.DIE, callback);\r\n    }\r\n\r\n    public onDodgeCommand(callback?: StateCallback) {\r\n        return this.setPlaneState(StateEvent.DODGE_COMMAND, callback);\r\n    }\r\n\r\n    setAnimSpeed(speed: number) {\r\n        if (this.spine && isValid(this.spine)){\r\n            this.spine.timeScale = speed;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n"]}