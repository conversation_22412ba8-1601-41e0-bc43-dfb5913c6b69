{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/WXLogin.ts"], "names": ["WXLogin", "logError", "logInfo", "csproto", "authButton", "login", "cb", "wx", "success", "res", "errMsg", "code", "accountType", "cs", "ACCOUNT_TYPE", "ACCOUNT_TYPE_WXMINIGAME", "serverAddr", "showUserInfoButton", "show", "hideUserInfoButton", "hide", "getStatusBarHeight", "systemInfo", "getSystemInfoSync", "safeAreaTop", "safeArea", "top", "statusBarHeight", "navBarHeight", "console", "log", "getUserInfo", "param", "THIS", "getSetting", "error", "authSetting", "sysInfo", "button", "createUserInfoButton", "type", "text", "plain", "style", "left", "screenWidth", "x", "screenHeight", "y", "width", "w", "height", "h", "lineHeight", "backgroundColor", "color", "textAlign", "fontSize", "borderRadius", "onTap", "userInfo", "destroy", "getUserInfo__", "getUserInfo_", "hasTap", "name", "nick<PERSON><PERSON>", "icon", "avatarUrl", "complete"], "mappings": ";;;0DAMaA,O;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AALJC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,O,iBAAAA,O;;AACZC,MAAAA,O;;;;;;;yBAIMH,O,GAAN,MAAMA,OAAN,CAAsC;AAAA;AAAA,eACjCI,UADiC,GACpB,IADoB;AAAA;;AAEzCC,QAAAA,KAAK,CAACC,EAAD,EAAiD;AAClD;AAAA;AAAA,kCAAQ,OAAR,EAAiB,gBAAjB,EADkD,CAElD;;AACAC,UAAAA,EAAE,CAACF,KAAH,CAAS;AACL;AACAG,YAAAA,OAAO,CAACC,GAAD,EAAM;AACT;AAAA;AAAA,wCAAS,OAAT,yBAAuCA,GAAG,CAACC,MAA3C,SAAqDD,GAAG,CAACE,IAAzD;AACAL,cAAAA,EAAE,CAAC,IAAD,EAAO;AACLM,gBAAAA,WAAW,EAAE;AAAA;AAAA,wCAAQC,EAAR,CAAWC,YAAX,CAAwBC,uBADhC;AAELJ,gBAAAA,IAAI,EAAEF,GAAG,CAACE,IAFL;AAGLK,gBAAAA,UAAU,EAAE;AAHP,eAAP,CAAF;AAKH;;AATI,WAAT;AAWH;;AAEDC,QAAAA,kBAAkB,GAAG;AACjB,cAAI,KAAKb,UAAT,EAAqB;AACjB;AACA,iBAAKA,UAAL,CAAgBc,IAAhB;AACH;AACJ;;AAEDC,QAAAA,kBAAkB,GAAG;AACjB,cAAI,KAAKf,UAAT,EAAqB;AACjB;AACA,iBAAKA,UAAL,CAAgBgB,IAAhB;AACH;AACJ;;AAEDC,QAAAA,kBAAkB,GAAG;AACjB;AACA,cAAMC,UAAU,GAAGf,EAAE,CAACgB,iBAAH,EAAnB;AACA,cAAMC,WAAW,GAAGF,UAAU,CAACG,QAAX,CAAoBC,GAAxC,CAHiB,CAG4B;;AAC7C,cAAMC,eAAe,GAAGL,UAAU,CAACK,eAAnC,CAJiB,CAImC;;AACpD,cAAMC,YAAY,GAAGJ,WAAW,GAAGG,eAAnC,CALiB,CAKmC;;AACpDE,UAAAA,OAAO,CAACC,GAAR,CAAY,gBAAZ,EAA8BN,WAA9B;AACAK,UAAAA,OAAO,CAACC,GAAR,CAAY,cAAZ,EAA4BH,eAA5B;AACAE,UAAAA,OAAO,CAACC,GAAR,CAAY,UAAZ,EAAwBF,YAAxB;AACA,iBAAOD,eAAP;AACH;;AAEDI,QAAAA,WAAW,CAACzB,EAAD,EAA8E0B,KAA9E,EAA0F;AACjG,cAAIC,IAAI,GAAG,IAAX,CADiG,CAEjG;;AACA1B,UAAAA,EAAE,CAAC2B,UAAH,CAAc;AACV;AACA1B,YAAAA,OAAO,CAACC,GAAD,EAAM;AACToB,cAAAA,OAAO,CAACM,KAAR,CAAc,mCAAd,EAAmD1B,GAAG,CAAC2B,WAAvD;;AACA,kBAAI,CAAC3B,GAAG,CAAC2B,WAAJ,CAAgB,gBAAhB,CAAL,EAAwC;AACpCP,gBAAAA,OAAO,CAACM,KAAR,CAAc,kCAAd,EADoC,CAEpC;;AACA,oBAAIE,OAAO,GAAG9B,EAAE,CAACgB,iBAAH,EAAd,CAHoC,CAIpC;;AACA,oBAAIe,MAAM,GAAG/B,EAAE,CAACgC,oBAAH,CAAwB;AACjCC,kBAAAA,IAAI,EAAE,MAD2B;AAEjCC,kBAAAA,IAAI,EAAE,EAF2B;AAGjC;AACAC,kBAAAA,KAAK,EAAE,IAJ0B;AAKjCC,kBAAAA,KAAK,EAAE;AACHC,oBAAAA,IAAI,EAAEP,OAAO,CAACQ,WAAR,GAAsBb,KAAK,CAACc,CAD/B;AACiC;AACpCpB,oBAAAA,GAAG,EAAEW,OAAO,CAACU,YAAR,IAAwB,IAAIf,KAAK,CAACgB,CAAlC,KAAwCX,OAAO,CAACZ,QAAR,CAAiBC,GAAjB,GAAuB,EAA/D,CAFF;AAEqE;AACxEuB,oBAAAA,KAAK,EAAEZ,OAAO,CAACQ,WAAR,GAAsBb,KAAK,CAACkB,CAHhC;AAGkC;AACrCC,oBAAAA,MAAM,EAAEd,OAAO,CAACU,YAAR,GAAuBf,KAAK,CAACoB,CAJlC;AAIoC;AACvCC,oBAAAA,UAAU,EAAE,EALT;AAMHC,oBAAAA,eAAe,EAAE,SANd;AAOHC,oBAAAA,KAAK,EAAE,SAPJ;AAQHC,oBAAAA,SAAS,EAAE,QARR;AASHC,oBAAAA,QAAQ,EAAE,EATP;AAUHC,oBAAAA,YAAY,EAAE;AAVX;AAL0B,iBAAxB,CAAb;AAkBApB,gBAAAA,MAAM,CAACpB,IAAP,GAvBoC,CAwBpC;;AACAoB,gBAAAA,MAAM,CAACqB,KAAP,CAAclD,GAAD,IAAS;AAClB,sBAAIA,GAAG,CAACmD,QAAR,EAAkB;AACdtB,oBAAAA,MAAM,CAACuB,OAAP;AACA5B,oBAAAA,IAAI,CAAC7B,UAAL,GAAkB,IAAlB;AACAyB,oBAAAA,OAAO,CAACC,GAAR,CAAY,iCAAZ,EAA+CrB,GAAG,CAACmD,QAAnD;AACA3B,oBAAAA,IAAI,CAAC6B,aAAL,CAAmBrD,GAAG,CAACmD,QAAvB,EAAiCtD,EAAjC,EAAqC,IAArC;AACH;AACJ,iBAPD;AAQA2B,gBAAAA,IAAI,CAAC7B,UAAL,GAAkBkC,MAAlB;AACH,eAlCD,MAmCK;AACDL,gBAAAA,IAAI,CAAC8B,YAAL,CAAkBzD,EAAlB,EAAsB,KAAtB;AACH;AACJ;;AA1CS,WAAd;AA4CH;;AACOwD,QAAAA,aAAa,CAACF,QAAD,EAAgBtD,EAAhB,EAAsF0D,MAAtF,EAAuG;AACxH1D,UAAAA,EAAE,CAAC,EAAD,EAAK;AACH2D,YAAAA,IAAI,EAAEL,QAAQ,CAACM,QADZ;AAEHC,YAAAA,IAAI,EAAEP,QAAQ,CAACQ;AAFZ,WAAL,EAGC,IAHD,CAAF;AAIH;;AACOL,QAAAA,YAAY,CAACzD,EAAD,EAA8E0D,MAA9E,EAA+F;AAC/G,cAAI/B,IAAI,GAAG,IAAX,CAD+G,CAE/G;;AACA1B,UAAAA,EAAE,CAACwB,WAAH,CAAe;AACX;AACAsC,YAAAA,QAAQ,CAAC5D,GAAD,EAAM;AACVoB,cAAAA,OAAO,CAACM,KAAR,CAAc,8BAAd;;AACA,kBAAI1B,GAAG,CAACmD,QAAR,EAAkB;AACd3B,gBAAAA,IAAI,CAAC6B,aAAL,CAAmBrD,GAAG,CAACmD,QAAvB,EAAiCtD,EAAjC,EAAqC0D,MAArC;AACH,eAFD,MAGK;AACD1D,gBAAAA,EAAE,CAAC,oBAAD,EAAuB,IAAvB,EAA6B,KAA7B,CAAF;AACH;AACJ;;AAVU,WAAf;AAYH;;AAjHwC,O", "sourcesContent": ["\r\nimport { logError, logInfo } from '../../../../scripts/utils/Logger';\r\nimport csproto from '../autogen/pb/cs_proto.js';\r\nimport { LoginInfo } from '../network/NetMgr';\r\nimport { IPlatformSDK, PlatformSDKUserInfo } from './IPlatformSDK.js';\r\n\r\nexport class WXLogin implements IPlatformSDK {\r\n    private authButton = null;\r\n    login(cb: (err: string|null, req: LoginInfo) => void) {\r\n        logInfo(\"Login\", \"start wx login\")\r\n        // @ts-ignore\r\n        wx.login({\r\n            // @ts-ignore\r\n            success(res) {\r\n                logError(\"Login\", `complete wx login ${res.errMsg} ${res.code}`)\r\n                cb(null, {\r\n                    accountType: csproto.cs.ACCOUNT_TYPE.ACCOUNT_TYPE_WXMINIGAME,\r\n                    code: res.code,\r\n                    serverAddr: \"wss://m2test.5600.online:9101/\",\r\n                })\r\n            }\r\n        })\r\n    }\r\n\r\n    showUserInfoButton() {\r\n        if (this.authButton) {\r\n            // @ts-ignore\r\n            this.authButton.show();\r\n        }\r\n    }\r\n\r\n    hideUserInfoButton() {\r\n        if (this.authButton) {\r\n            // @ts-ignore\r\n            this.authButton.hide();\r\n        }\r\n    }\r\n    \r\n    getStatusBarHeight() {\r\n        // @ts-ignore\r\n        const systemInfo = wx.getSystemInfoSync();\r\n        const safeAreaTop = systemInfo.safeArea.top; // 安全区域顶部到屏幕顶部的距离\r\n        const statusBarHeight = systemInfo.statusBarHeight; // 状态栏高度（仅原生刘海）\r\n        const navBarHeight = safeAreaTop - statusBarHeight; // 小程序导航栏高度\r\n        console.log('安全区域顶部到屏幕顶部的距离', safeAreaTop)\r\n        console.log('状态栏高度（仅原生刘海）', statusBarHeight)\r\n        console.log('小程序导航栏高度', navBarHeight)\r\n        return statusBarHeight;\r\n    }\r\n\r\n    getUserInfo(cb: (err: string, req: PlatformSDKUserInfo | null, hasTap: boolean) => void, param: any) {\r\n        let THIS = this;\r\n        // @ts-ignore\r\n        wx.getSetting({\r\n            // @ts-ignore\r\n            success(res) {\r\n                console.error('WXLogin get userinfo auth setting', res.authSetting)\r\n                if (!res.authSetting['scope.userInfo']) {\r\n                    console.error('WXLogin start authorize userinfo')\r\n                    // @ts-ignore\r\n                    let sysInfo = wx.getSystemInfoSync();\r\n                    // @ts-ignore\r\n                    var button = wx.createUserInfoButton({\r\n                        type: 'text',\r\n                        text: '',\r\n                        //image : \"SDK/WXGetUserInfo.png\",\r\n                        plain: true,\r\n                        style: {\r\n                            left: sysInfo.screenWidth * param.x,//0,//buttonPosition.x,\r\n                            top: sysInfo.screenHeight * (1 - param.y) + (sysInfo.safeArea.top - 20),//1334-100,//buttonPosition.y+buttonPosition.height,\r\n                            width: sysInfo.screenWidth * param.w,//750,//buttonPosition.width,\r\n                            height: sysInfo.screenHeight * param.h,//100,//buttonPosition.height,\r\n                            lineHeight: 40,\r\n                            backgroundColor: '#ff0000',\r\n                            color: '#ffffff',\r\n                            textAlign: 'center',\r\n                            fontSize: 16,\r\n                            borderRadius: 4\r\n                        }\r\n                    })\r\n                    button.show();\r\n                    // @ts-ignore\r\n                    button.onTap((res) => {\r\n                        if (res.userInfo) {\r\n                            button.destroy();\r\n                            THIS.authButton = null;\r\n                            console.log('WXLogin get wx userinfo success', res.userInfo)\r\n                            THIS.getUserInfo__(res.userInfo, cb, true)\r\n                        }\r\n                    })\r\n                    THIS.authButton = button;\r\n                }\r\n                else {\r\n                    THIS.getUserInfo_(cb, false);\r\n                }\r\n            }\r\n        })\r\n    }\r\n    private getUserInfo__(userInfo: any, cb: (err: string, req: PlatformSDKUserInfo, hasTap: boolean) => void, hasTap: boolean) {\r\n        cb(\"\", {\r\n            name: userInfo.nickName,\r\n            icon: userInfo.avatarUrl,\r\n        }, true)\r\n    }\r\n    private getUserInfo_(cb: (err: string, req: PlatformSDKUserInfo | null, hasTap: boolean) => void, hasTap: boolean) {\r\n        let THIS = this\r\n        // @ts-ignore\r\n        wx.getUserInfo({\r\n            // @ts-ignore\r\n            complete(res) {\r\n                console.error(\"WXLogin getUserInfo complete\")\r\n                if (res.userInfo) {\r\n                    THIS.getUserInfo__(res.userInfo, cb, hasTap)\r\n                }\r\n                else {\r\n                    cb(\"get userinfo error\", null, false)\r\n                }\r\n            }\r\n        })\r\n    }\r\n}"]}