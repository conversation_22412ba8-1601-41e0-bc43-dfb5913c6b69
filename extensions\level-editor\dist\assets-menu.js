"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.onCreateMenu = onCreateMenu;
exports.onAssetMenu = onAssetMenu;
function onCreateMenu(assetInfo) {
    return [
        {
            label: '飞机游戏',
            submenu: [
                {
                    label: '创建波次',
                    click() {
                        console.log('wave');
                        console.log(assetInfo);
                    },
                },
                {
                    label: '创建阵型',
                    click() {
                        console.log('formation');
                        console.log(assetInfo);
                    },
                },
            ],
        },
    ];
}
;
function onAssetMenu(assetInfo) {
    const submenu = [
        {
            label: '创建关卡Prefab',
            //enabled: assetInfo.isDirectory,
            click() {
                createLevelPrefab(assetInfo);
            },
        },
        {
            label: '创建子弹prefab',
            //enabled: !assetInfo.isDirectory,
            click() {
                createBulletPrefabs(assetInfo);
            },
        },
    ];
    // 检查是否是阵型JSON文件
    if (!assetInfo.isDirectory &&
        assetInfo.url.includes('assets/resources/game/level/wave/formation') &&
        assetInfo.url.endsWith('.json')) {
        submenu.push({
            label: '编辑阵型',
            click() {
                editFormation(assetInfo);
            },
        });
    }
    // 检查是否是路径JSON文件
    if (!assetInfo.isDirectory &&
        assetInfo.url.includes('assets/resources/game/level/wave/path') &&
        assetInfo.url.endsWith('.json')) {
        submenu.push({
            label: '编辑路径',
            click() {
                editPath(assetInfo);
            },
        });
    }
    return [
        {
            label: '飞机游戏',
            submenu: submenu,
        },
    ];
}
;
function getAssetUuidByPath(path) {
    return new Promise((resolve, reject) => {
        // @ts-ignore
        Editor.Message.request('asset-db', 'query-uuid', path).then((res) => {
            resolve(res);
        }).catch((err) => {
            console.error('Failed to query uuid:', err);
            reject(err);
        });
    });
}
function getAssetUuidsByPath(path) {
    return new Promise((resolve, reject) => {
        // @ts-ignore
        Editor.Message.request('asset-db', 'query-assets', { pattern: path + '/**' }).then((res) => {
            const arr = Array.isArray(res) ? res : (Array.isArray(res === null || res === void 0 ? void 0 : res[0]) ? res[0] : []);
            const assets = arr
                .filter((a) => a && !a.isDirectory)
                .map((a) => ({
                name: String(a.name || ''),
                path: a.path || '',
                uuid: a.uuid || ''
            }))
                .filter(p => p.name)
                .sort((a, b) => a.name.localeCompare(b.name));
            resolve(assets.map(a => a.uuid)); // 只需要uuid即可，不需要其他信息了。
        }).catch((err) => {
            console.error('Failed to query assets:', err);
            reject(err);
        });
    });
}
function createLevelPrefab(assetInfo) {
    console.log(assetInfo);
    if (assetInfo.isDirectory) {
        getAssetUuidsByPath(assetInfo.url).then((uuids) => {
            uuids.forEach((uuid) => {
                // @ts-ignore
                Editor.Message.request('scene', 'execute-scene-script', {
                    name: 'level-editor',
                    method: 'createLevelPrefab',
                    args: [uuid]
                });
            });
        });
    }
    else {
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'level-editor',
            method: 'createLevelPrefab',
            args: [assetInfo.uuid]
        });
    }
}
function createBulletPrefabs(assetInfo) {
    if (assetInfo.isDirectory) {
        getAssetUuidsByPath(assetInfo.url).then((uuids) => {
            uuids.forEach((uuid) => {
                // @ts-ignore
                Editor.Message.request('scene', 'execute-scene-script', {
                    name: 'level-editor',
                    method: 'createBulletPrefab',
                    args: [uuid]
                });
            });
        });
    }
    else {
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'level-editor',
            method: 'createBulletPrefab',
            args: [assetInfo.uuid]
        });
    }
}
function editFormation(assetInfo) {
    console.log('编辑阵型:', assetInfo);
    // 打开FormationEditor场景 Uuid = aa842f3a-8aea-42c2-a480-968aade3dfef
    getAssetUuidByPath('db://assets/scenes/FormationEditor.scene').then((uuid) => {
        // @ts-ignore
        Editor.Message.request('scene', 'open-scene', uuid).then(() => {
            // 场景打开后，加载选中的阵型JSON文件到FormationEditor组件
            // @ts-ignore
            Editor.Message.request('scene', 'execute-scene-script', {
                name: 'level-editor',
                method: 'loadFormationData',
                args: [assetInfo.uuid]
            });
        }).catch((err) => {
            console.error('Failed to open FormationEditor scene:', err);
        });
    });
}
function editPath(assetInfo) {
    console.log('编辑路径:', assetInfo);
    // 打开PathEditor场景
    getAssetUuidByPath('db://assets/scenes/PathEditor.scene').then((uuid) => {
        // @ts-ignore
        Editor.Message.request('scene', 'open-scene', uuid).then(() => {
            // 场景打开后，加载选中的路径JSON文件到PathEditor组件
            // @ts-ignore
            Editor.Message.request('scene', 'execute-scene-script', {
                name: 'level-editor',
                method: 'loadPathData',
                args: [assetInfo.uuid]
            });
        }).catch((err) => {
            console.error('Failed to open PathEditor scene:', err);
        });
    });
}
//# sourceMappingURL=data:application/json;base64,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