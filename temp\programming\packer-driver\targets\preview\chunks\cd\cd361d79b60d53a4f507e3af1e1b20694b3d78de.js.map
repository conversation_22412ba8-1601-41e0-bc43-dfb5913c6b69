{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/test/PathCurveTest.ts"], "names": ["_decorator", "Component", "log", "PathData", "PathPoint", "ccclass", "property", "PathCurveTest", "onLoad", "testCurveVsLine", "pathData", "point1", "speed", "smoothness", "point2", "point3", "points", "length", "push", "subdivided", "getSubdividedPoints", "segment1Points", "i", "point", "x", "y", "segment2Points", "cornerPoints", "filter", "p", "for<PERSON>ach", "toFixed", "hasYVariation", "some", "Math", "abs", "min"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,G,OAAAA,G;;AACvBC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,S,iBAAAA,S;;;;;;;;;OAEb;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBN,U;AAE9B;AACA;AACA;;+BAEaO,a,WADZF,OAAO,CAAC,eAAD,C,gBAAR,MACaE,aADb,SACmCN,SADnC,CAC6C;AAEzCO,QAAAA,MAAM,GAAG;AACL,eAAKC,eAAL;AACH;AAED;AACJ;AACA;;;AACYA,QAAAA,eAAe,GAAG;AACtBP,UAAAA,GAAG,CAAC,oBAAD,CAAH,CADsB,CAGtB;;AACA,cAAMQ,QAAQ,GAAG;AAAA;AAAA,qCAAjB;AAEA,cAAMC,MAAM,GAAG;AAAA;AAAA,sCAAc,CAAd,EAAiB,CAAjB,CAAf;AACAA,UAAAA,MAAM,CAACC,KAAP,GAAe,GAAf;AACAD,UAAAA,MAAM,CAACE,UAAP,GAAoB,GAApB,CARsB,CAQG;;AAEzB,cAAMC,MAAM,GAAG;AAAA;AAAA,sCAAc,GAAd,EAAmB,CAAnB,CAAf;AACAA,UAAAA,MAAM,CAACF,KAAP,GAAe,GAAf;AACAE,UAAAA,MAAM,CAACD,UAAP,GAAoB,GAApB;AAEA,cAAME,MAAM,GAAG;AAAA;AAAA,sCAAc,GAAd,EAAmB,GAAnB,CAAf;AACAA,UAAAA,MAAM,CAACH,KAAP,GAAe,GAAf;AACAG,UAAAA,MAAM,CAACF,UAAP,GAAoB,GAApB;AAEAH,UAAAA,QAAQ,CAACM,MAAT,CAAgBC,MAAhB,GAAyB,CAAzB;AACAP,UAAAA,QAAQ,CAACM,MAAT,CAAgBE,IAAhB,CAAqBP,MAArB,EAA6BG,MAA7B,EAAqCC,MAArC;AAEA,cAAMI,UAAU,GAAGT,QAAQ,CAACU,mBAAT,EAAnB;AACAlB,UAAAA,GAAG,sCAAWiB,UAAU,CAACF,MAAtB,CAAH,CAtBsB,CAwBtB;;AACA,cAAII,cAAc,GAAG,EAArB;;AACA,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,UAAU,CAACF,MAA/B,EAAuCK,CAAC,EAAxC,EAA4C;AACxC,gBAAMC,KAAK,GAAGJ,UAAU,CAACG,CAAD,CAAxB;;AACA,gBAAIC,KAAK,CAACC,CAAN,IAAW,GAAX,IAAkBD,KAAK,CAACE,CAAN,IAAW,EAAjC,EAAqC;AAAE;AACnCJ,cAAAA,cAAc,CAACH,IAAf,CAAoBK,KAApB;AACH;AACJ;;AAEDrB,UAAAA,GAAG,sCAAWmB,cAAc,CAACJ,MAA1B,CAAH,CAjCsB,CAmCtB;;AACA,cAAIS,cAAc,GAAG,EAArB;;AACA,eAAK,IAAIJ,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAGH,UAAU,CAACF,MAA/B,EAAuCK,EAAC,EAAxC,EAA4C;AACxC,gBAAMC,MAAK,GAAGJ,UAAU,CAACG,EAAD,CAAxB;;AACA,gBAAIC,MAAK,CAACC,CAAN,IAAW,EAAX,IAAiBD,MAAK,CAACE,CAAN,IAAW,CAAhC,EAAmC;AAAE;AACjCC,cAAAA,cAAc,CAACR,IAAf,CAAoBK,MAApB;AACH;AACJ;;AAEDrB,UAAAA,GAAG,sCAAWwB,cAAc,CAACT,MAA1B,CAAH,CA5CsB,CA8CtB;;AACA,cAAMU,YAAY,GAAGR,UAAU,CAACS,MAAX,CAAkBC,CAAC,IACpCA,CAAC,CAACL,CAAF,GAAM,EAAN,IAAYK,CAAC,CAACL,CAAF,GAAM,GAAlB,IAAyBK,CAAC,CAACJ,CAAF,GAAM,CAAC,EAAhC,IAAsCI,CAAC,CAACJ,CAAF,GAAM,EAD3B,CAArB;AAIAvB,UAAAA,GAAG,4CAAYyB,YAAY,CAACV,MAAzB,CAAH;;AAEA,cAAIU,YAAY,CAACV,MAAb,GAAsB,CAA1B,EAA6B;AACzBf,YAAAA,GAAG,CAAC,SAAD,CAAH;AACAyB,YAAAA,YAAY,CAACG,OAAb,CAAqB,CAACD,CAAD,EAAIP,CAAJ,KAAU;AAC3BpB,cAAAA,GAAG,cAAOoB,CAAP,WAAcO,CAAC,CAACL,CAAF,CAAIO,OAAJ,CAAY,CAAZ,CAAd,UAAiCF,CAAC,CAACJ,CAAF,CAAIM,OAAJ,CAAY,CAAZ,CAAjC,OAAH;AACH,aAFD,EAFyB,CAMzB;;AACA,gBAAMC,aAAa,GAAGL,YAAY,CAACM,IAAb,CAAkBJ,CAAC,IAAIK,IAAI,CAACC,GAAL,CAASN,CAAC,CAACJ,CAAX,IAAgB,GAAvC,CAAtB;;AACA,gBAAIO,aAAJ,EAAmB;AACf9B,cAAAA,GAAG,CAAC,aAAD,CAAH;AACH,aAFD,MAEO;AACHA,cAAAA,GAAG,CAAC,cAAD,CAAH;AACH;AACJ,WAlEqB,CAoEtB;;;AACAA,UAAAA,GAAG,CAAC,aAAD,CAAH;;AACA,eAAK,IAAIoB,GAAC,GAAG,CAAb,EAAgBA,GAAC,GAAGY,IAAI,CAACE,GAAL,CAAS,EAAT,EAAajB,UAAU,CAACF,MAAxB,CAApB,EAAqDK,GAAC,EAAtD,EAA0D;AACtD,gBAAMO,CAAC,GAAGV,UAAU,CAACG,GAAD,CAApB;AACApB,YAAAA,GAAG,cAAOoB,GAAP,WAAcO,CAAC,CAACL,CAAF,CAAIO,OAAJ,CAAY,CAAZ,CAAd,UAAiCF,CAAC,CAACJ,CAAF,CAAIM,OAAJ,CAAY,CAAZ,CAAjC,uBAAuDF,CAAC,CAACjB,KAAF,CAAQmB,OAAR,CAAgB,CAAhB,CAAvD,CAAH;AACH;AACJ;;AAnFwC,O", "sourcesContent": ["import { _decorator, Component, log } from 'cc';\nimport { PathData, PathPoint } from '../data/PathData';\n\nconst { ccclass, property } = _decorator;\n\n/**\n * 专门测试路径曲线生成的组件\n */\n@ccclass('PathCurveTest')\nexport class PathCurveTest extends Component {\n\n    onLoad() {\n        this.testCurveVsLine();\n    }\n\n    /**\n     * 测试曲线与直线的区别\n     */\n    private testCurveVsLine() {\n        log(\"=== 测试曲线与直线的区别 ===\");\n        \n        // 创建一个L形路径\n        const pathData = new PathData();\n        \n        const point1 = new PathPoint(0, 0);\n        point1.speed = 300;\n        point1.smoothness = 1.0; // 高平滑度，应该产生曲线\n        \n        const point2 = new PathPoint(100, 0);\n        point2.speed = 300;\n        point2.smoothness = 1.0;\n        \n        const point3 = new PathPoint(100, 100);\n        point3.speed = 300;\n        point3.smoothness = 1.0;\n        \n        pathData.points.length = 0;\n        pathData.points.push(point1, point2, point3);\n        \n        const subdivided = pathData.getSubdividedPoints();\n        log(`总细分点数: ${subdivided.length}`);\n        \n        // 分析第一段（从point1到point2）\n        let segment1Points = [];\n        for (let i = 0; i < subdivided.length; i++) {\n            const point = subdivided[i];\n            if (point.x <= 100 && point.y <= 10) { // 第一段的点\n                segment1Points.push(point);\n            }\n        }\n        \n        log(`第一段点数: ${segment1Points.length}`);\n        \n        // 分析第二段（从point2到point3）\n        let segment2Points = [];\n        for (let i = 0; i < subdivided.length; i++) {\n            const point = subdivided[i];\n            if (point.x >= 90 && point.y >= 0) { // 第二段的点\n                segment2Points.push(point);\n            }\n        }\n        \n        log(`第二段点数: ${segment2Points.length}`);\n        \n        // 检查转角处是否有平滑过渡\n        const cornerPoints = subdivided.filter(p => \n            p.x > 80 && p.x < 120 && p.y > -20 && p.y < 20\n        );\n        \n        log(`转角区域点数: ${cornerPoints.length}`);\n        \n        if (cornerPoints.length > 2) {\n            log(\"转角区域的点:\");\n            cornerPoints.forEach((p, i) => {\n                log(`  点${i}: (${p.x.toFixed(2)}, ${p.y.toFixed(2)})`);\n            });\n            \n            // 检查是否有平滑过渡（Y坐标应该有变化）\n            const hasYVariation = cornerPoints.some(p => Math.abs(p.y) > 0.1);\n            if (hasYVariation) {\n                log(\"✓ 检测到曲线平滑过渡\");\n            } else {\n                log(\"⚠ 转角处可能仍然是直线\");\n            }\n        }\n        \n        // 输出前几个点的详细信息\n        log(\"前10个点的详细信息:\");\n        for (let i = 0; i < Math.min(10, subdivided.length); i++) {\n            const p = subdivided[i];\n            log(`  点${i}: (${p.x.toFixed(2)}, ${p.y.toFixed(2)}) 速度:${p.speed.toFixed(1)}`);\n        }\n    }\n}\n"]}