import { _decorator, Node, Color, Component, JsonAsset, CCInteger, Graphics } from 'cc';
const { ccclass, executeInEditMode, property, disallowMultiple, menu, requireComponent } = _decorator;
import { EDITOR } from 'cc/env';
import { PathData, PathPoint } from 'db://assets/bundles/common/script/game/data/PathData';
import { PathPointEditor } from './PathPointEditor';

@ccclass('PathEditor')
@menu("怪物/编辑器/路径编辑")
@requireComponent(Graphics)
@executeInEditMode(true)
@disallowMultiple(true)
export class PathEditor extends Component {
    private _graphics: Graphics | null = null;
    public get graphics(): Graphics {
        if (!this._graphics) {
            this._graphics = this.node.getComponent(Graphics) || this.node.addComponent(Graphics);
        }
        return this._graphics;
    }

    @property({ type: JsonAsset, displayName: "路径数据" })
    public set pathData(value: JsonAsset) {
        this._pathData = value;
        this.reload();
    }
    public get pathData(): JsonAsset | null {
        return this._pathData;
    }

    @property({ displayName: "路径名称" })
    public get pathName(): string {
        return this._pathDataObj.name;
    }
    public set pathName(value: string) {
        this._pathDataObj.name = value;
    }

    @property({ type: CCInteger, displayName: '起始点'})
    public get startIdx() {
        return this._pathDataObj.startIdx;
    }
    public set startIdx(value: number) {
        this._pathDataObj.startIdx = value;
    }

    @property({ type: CCInteger, displayName: '结束点(-1代表默认最后个点)'})
    public get endIdx() {
        return this._pathDataObj.endIdx;
    }
    public set endIdx(value: number) {
        this._pathDataObj.endIdx = value;
    }

    @property({ displayName: "是否闭合", visible() {
        // @ts-ignore
        return this._pathDataObj.points.length >= 3;
    }})
    public get isClosed(): boolean {
        return this._pathDataObj.closed;
    }
    public set isClosed(value: boolean) {
        this._pathDataObj.closed = value;
    }

    @property({ displayName: "曲线颜色" })
    public curveColor: Color = Color.WHITE;

    @property({ displayName: "显示细分线段" })
    public showSegments: boolean = false; // 是否使用不同颜色来绘制不同的细分线段

    private _showDirectionArrow: boolean = true;
    private _pathData: JsonAsset | null = null;
    private _pathDataObj: PathData = new PathData();
    private _cachedChildrenCount: number = 0;

    public reload() {
        if (!this._pathData) return;

        const pathData = new PathData();
        Object.assign(pathData, this._pathData.json);
        this._pathDataObj = pathData;

        this.node.removeAllChildren();
        if (this._pathDataObj && this._pathDataObj.points.length > 0) {
            this._pathDataObj.points.forEach((point) => {
                this.addPoint(point);
            });
        }
        this.updateCurve();
    }

    public save(): string {
        // 收集所有路径点数据
        const pointEditors = this.getComponentsInChildren(PathPointEditor);
        this._pathDataObj.points = pointEditors.map((editor) => editor.pathPoint);
        return JSON.stringify(this._pathDataObj, null, 2);
    }

    public addPoint(point: PathPoint) {
        const pointNode = new Node();
        pointNode.parent = this.node;
        pointNode.setPosition(point.x, point.y, 0);

        const pointEditor = pointNode.addComponent(PathPointEditor);
        pointEditor.pathPoint = point;
    }

    public addNewPoint(x: number, y: number) {
        const point = new PathPoint(x, y);
        this.addPoint(point);
        this.updateCurve();
    }

    public updateCurve() {
        // 收集当前所有点的数据
        const pointEditors = this.getComponentsInChildren(PathPointEditor);
        this._pathDataObj.points = pointEditors.map((editor) => editor.pathPoint);
    }

    private drawPath() {
        const graphics = this.graphics;
        graphics.clear();

        if (this._pathDataObj.points.length < 2) return;

        const subdivided = this._pathDataObj.getSubdividedPoints(true);
        if (subdivided.length > 1) {
            if (this.showSegments) {
                this.drawSegmentedPath(graphics, subdivided);
            } else {
                this.drawUniformPath(graphics, subdivided);
            }

            // 绘制路径终点的方向箭头（仅对非闭合路径）
            if (this._showDirectionArrow && !this._pathDataObj.closed) {
                this.drawPathDirectionArrow(graphics, subdivided);
            }
        }

        // console.log('subdivided points length: ', subdivided.length);
    }

    /**
     * 绘制统一颜色的路径
     */
    private drawUniformPath(graphics: Graphics, subdivided: PathPoint[]) {
        graphics.strokeColor = this.curveColor;
        graphics.lineWidth = 5;

        graphics.moveTo(subdivided[0].x, subdivided[0].y);
        for (let i = 1; i < subdivided.length; i++) {
            graphics.lineTo(subdivided[i].x, subdivided[i].y);
        }

        // 如果是闭合路径，连接回起点
        if (this._pathDataObj.closed) {
            graphics.lineTo(subdivided[0].x, subdivided[0].y);
        }

        graphics.stroke();
    }

    /**
     * 绘制分段着色的路径 - 每个细分段用不同颜色
     */
    private drawSegmentedPath(graphics: Graphics, subdivided: PathPoint[]) {
        graphics.lineWidth = 5;

        if (subdivided.length < 2) return;

        // 为每个细分段绘制不同的颜色
        for (let i = 0; i < subdivided.length - 1; i++) {
            const t = i / (subdivided.length - 1); // 计算当前位置的归一化参数 [0,1]

            // 从绿色到红色的颜色插值
            const color = this.interpolateColor(Color.GREEN, Color.RED, t);
            graphics.strokeColor = color;

            // 绘制当前段
            graphics.moveTo(subdivided[i].x, subdivided[i].y);
            graphics.lineTo(subdivided[i + 1].x, subdivided[i + 1].y);
            graphics.stroke();
        }

        // 如果是闭合路径，绘制最后一段回到起点
        if (this._pathDataObj.closed && subdivided.length > 2) {
            const lastColor = this.interpolateColor(Color.GREEN, Color.RED, 1.0);
            graphics.strokeColor = lastColor;
            graphics.moveTo(subdivided[subdivided.length - 1].x, subdivided[subdivided.length - 1].y);
            graphics.lineTo(subdivided[0].x, subdivided[0].y);
            graphics.stroke();
        }
    }

    /**
     * 颜色插值函数
     * @param color1 起始颜色
     * @param color2 结束颜色
     * @param t 插值参数 [0,1]
     */
    private interpolateColor(color1: Color, color2: Color, t: number): Color {
        t = Math.max(0, Math.min(1, t)); // 确保t在[0,1]范围内

        const r = color1.r + (color2.r - color1.r) * t;
        const g = color1.g + (color2.g - color1.g) * t;
        const b = color1.b + (color2.b - color1.b) * t;
        const a = color1.a + (color2.a - color1.a) * t;

        return new Color(r, g, b, a);
    }

    public update(_dt: number) {
        const childrenCount = this.node.children.length;
        if (childrenCount !== this._cachedChildrenCount) {
            this._cachedChildrenCount = childrenCount;
        }
        this.updateCurve();
        this.drawPath();
    }

    /**
     * 绘制路径方向箭头
     */
    private drawPathDirectionArrow(graphics: Graphics, subdivided: any[]) {
        if (subdivided.length < 2) return;

        // 如果是闭合路径，不绘制箭头（因为没有明确的终点）
        if (this._pathDataObj.closed) return;

        // 计算终点的方向（使用最后几个点来获得更准确的方向）
        const endPoint = subdivided[subdivided.length - 1];
        let prevPoint = subdivided[subdivided.length - 2];

        // 如果有足够的点，使用更远的点来计算方向，获得更平滑的方向
        if (subdivided.length >= 5) {
            prevPoint = subdivided[subdivided.length - 5];
        }

        // 计算方向角度
        const direction = Math.atan2(endPoint.y - prevPoint.y, endPoint.x - prevPoint.x);

        // 箭头参数
        const arrowLength = 40;
        const arrowHeadLength = 20;
        const arrowHeadAngle = Math.PI / 5; // 36度，更尖锐的箭头

        // 设置箭头样式
        graphics.strokeColor = Color.RED;
        graphics.fillColor = Color.RED;
        graphics.lineWidth = 3;

        // 计算箭头起点（从路径终点开始）
        const arrowStartX = endPoint.x;
        const arrowStartY = endPoint.y;

        // 计算箭头终点
        const arrowEndX = arrowStartX + Math.cos(direction) * arrowLength;
        const arrowEndY = arrowStartY + Math.sin(direction) * arrowLength;

        // 绘制箭头主线
        graphics.moveTo(arrowStartX, arrowStartY);
        graphics.lineTo(arrowEndX, arrowEndY);
        graphics.stroke();

        // 绘制箭头头部（填充三角形）
        const leftX = arrowEndX - Math.cos(direction - arrowHeadAngle) * arrowHeadLength;
        const leftY = arrowEndY - Math.sin(direction - arrowHeadAngle) * arrowHeadLength;
        const rightX = arrowEndX - Math.cos(direction + arrowHeadAngle) * arrowHeadLength;
        const rightY = arrowEndY - Math.sin(direction + arrowHeadAngle) * arrowHeadLength;

        // 绘制填充的箭头头部
        graphics.moveTo(arrowEndX, arrowEndY);
        graphics.lineTo(leftX, leftY);
        graphics.lineTo(rightX, rightY);
        graphics.close();
        graphics.fill();
        graphics.stroke();
    }
}