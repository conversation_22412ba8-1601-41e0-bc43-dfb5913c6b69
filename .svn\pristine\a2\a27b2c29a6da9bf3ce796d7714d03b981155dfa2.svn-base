
import { _decorator, Node, Prefab, CCBoolean, CCFloat, CCInteger, Component, Vec2, instantiate, assetManager } from 'cc';
const { ccclass, property, executeInEditMode, menu } = _decorator;
import { WaveData, eSpawnOrder, eWaveCompletion } from 'db://assets/bundles/common/script/game/data/WaveData';
import { Wave } from 'db://assets/bundles/common/script/game/wave/Wave';
import { LubanMgr } from 'db://assets/bundles/common/script/luban/LubanMgr';
import { ObjectPool } from 'db://assets/bundles/common/script/game/bullet/ObjectPool';
import EnemyPlane from 'db://assets/bundles/common/script/game/ui/plane/enemy/EnemyPlane';
import { LevelEditorUtils } from '../utils';
import { EnemyData } from 'db://assets/bundles/common/script/game/data/EnemyData';
import { eMoveEvent } from 'db://assets/bundles/common/script/game/move/IMovable';

/// 用来创建和管理波次的所有飞机对象
@ccclass('WavePreview')
@menu("怪物/编辑器/波次预览")
@executeInEditMode()
export class WavePreview extends Component {

    private static _instance: WavePreview|null = null;
    public static get instance(): WavePreview|null {
        return this._instance;
    }

    private _luban: LubanMgr|null = null;
    public get luban(): LubanMgr|null {
        if (this._luban == null) {
            this._luban = new LubanMgr();
        }
        return this._luban;
    }

    // 这里的wave是编辑时的示意
    private _bindingMap: Map<Wave, Node[]> = new Map();

    onLoad() {
        if (WavePreview._instance == null) {
            WavePreview._instance = this;
        } else {
            console.warn("WavePreview multiple instance");
        }
    }

    reset() {
        this.node.removeAllChildren();
        this._bindingMap.clear();
    }

    update(dt: number) {
        this._bindingMap.forEach((nodes, wave) => {
            this.updateWave(wave, nodes);
        });

        this.tickPreview(dt);
    }

    setupWave(wave: Wave) {
        const waveData = wave.waveData;
        if (waveData.spawnGroups && waveData.spawnGroups.length > 0) {
            let planeId = 0;
            for (let i = 0; i < waveData.spawnGroups.length; i++) {
                if (waveData.spawnGroups[i].planeID > 0) {
                    planeId = waveData.spawnGroups[i].planeID;
                    break;
                }
            }

            if (planeId == 0) {
                console.warn("WavePreview createPlane no valid planeId in spawnGroups");
                return;
            }

            if (this.luban?.table == null) {
                this.luban?.initInEditor().then(() => {
                    this.createPlane(wave, planeId);
                });
            }
        }
    }

    private createPlane(wave: Wave, planeId: number) {
        const planeData = this.luban?.table.TbResEnemy.get(planeId);
        if (planeData == null) {
            console.warn("WavePreview createPlane no planeData for id:", planeId);
            return;
        }

        const fullPath = "db://assets/resources/" + planeData.prefab + ".prefab";
        LevelEditorUtils.loadByPath<Prefab>(fullPath).then((prefab) => {
            if (prefab) {
                const node = instantiate(prefab);
                if (node) {
                    this.node.addChild(node);
                    let nodes = this._bindingMap.get(wave);
                    if (nodes == null) {
                        nodes = [];
                        this._bindingMap.set(wave, nodes);
                    }
                    nodes.push(node);
                }
            }
        });
    }

    updateWave(wave: Wave, nodes: Node[]) {
        const wavePos = wave.node.worldPosition;
        const waveData = wave.waveData;
        const nodePos = new Vec2(wavePos.x + waveData.spawnPosX.eval(), wavePos.y + waveData.spawnPosY.eval());
        const nodeAngle = waveData.spawnAngle.eval();

        for (let i = 0; i < nodes.length; i++) {
            const node = nodes[i];
            node.setWorldPosition(nodePos.x, nodePos.y, 0);
            node.setRotationFromEuler(0, 0, nodeAngle);
        }
    }

    // 这里的wave时编辑器play时，用来动态创建小怪的wave。
    private previewWave: Wave[] = [];
    private planePool: EnemyPlane[] = []
    private activePlane: EnemyPlane[] = [];

    addPreview(wave: Wave, posX: number, posY: number) {
        wave.setCreatePlaneDelegate((planeId: number, pos: Vec2, angle: number) => {
            this.createDummyPlane(wave, planeId, pos, angle);
        });
        wave.trigger(posX, posY);
        this.previewWave.push(wave);
    }

    tickPreview(dt: number) {
        if (this.previewWave.length == 0) {
            return;
        }

        const dtInMiliseconds = dt * 1000;
        this.previewWave.forEach((wave) => {
            // console.log('isWaveCompleted: ', wave.isCompleted);
            wave.tick(dtInMiliseconds);
        });

        this.activePlane.forEach((plane) => {
            plane.moveCom!.tick(dt);
        });
    }

    clearPreview() {
        // destroy preivewWave
        this.previewWave.forEach((wave) => {
            wave.node.destroy();
        });
        this.activePlane.forEach((plane) => {
            plane.node.destroy();
        });
        this.planePool.forEach((plane) => {
            plane.node.destroy();
        });

        this.activePlane = [];
        this.planePool = [];
        this.previewWave = [];
    }

    private createDummyPlane(wave: Wave, planeId: number, pos: Vec2, angle: number) {
        // 对应"assets/editor/level/prefab/dummy_plane";
        // console.log('createDummyPlane: ');
        const dummy_plane_uuid: string = "698c56c6-6603-4e69-abaf-421b721ef307";
        assetManager.loadAny({uuid:dummy_plane_uuid}, (err, prefab:Prefab) => {
            if (err) {
                console.error("WavePreview createDummyPlane load prefab err", err);
                return;
            }

            // 从对象池里拿一个dummy plane
            let plane: EnemyPlane|null = null;
            if (this.planePool.length > 0) {
                plane = this.planePool.pop()!;
                plane.node.active = true;
            }
            else {
                const planeNode = instantiate(prefab);
                const plane = planeNode!.getComponent(EnemyPlane);
                if (plane) {
                    plane.setPos(pos.x, pos.y);
                    plane.initPlane(new EnemyData());
                    plane.moveCom!.removeAllListeners();
                    plane.moveCom!.on(eMoveEvent.onBecomeInvisible, () => {
                        plane.node.active = false;
                        this.planePool.push(plane);
                    });
                    plane.initMove(angle);
                    this.node.addChild(planeNode);
                    this.activePlane.push(plane);
                } else {
                    planeNode.destroy();
                }
            }
        });
    }
}