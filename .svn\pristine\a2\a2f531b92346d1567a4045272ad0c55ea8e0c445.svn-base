import { _decorator, Label, Node, ProgressBar, RichText, Sprite, tween } from 'cc';
import csproto from 'db://assets/bundles/common/script/autogen/pb/cs_proto.js';
import { BaseUI, UILayer, UIMgr, UIOpt } from "db://assets/scripts/core/base/UIMgr";
import { MyApp } from '../../app/MyApp';
import { BundleName } from '../../const/BundleConst';
import { DataMgr } from '../../data/DataManager';
import { UITools } from '../../game/utils/UITools';
import { StateSprite } from './components/base/StateSprite';
import { ButtonPlus } from './components/button/ButtonPlus';
import { StatisticsUI } from './StatisticsUI';

const { ccclass, property } = _decorator;


@ccclass('SettlementUI')
export class SettlementUI extends BaseUI {

    @property(ButtonPlus)
    btnOK: ButtonPlus | null = null;
    @property(ButtonPlus)
    btnNext: ButtonPlus | null = null;

    @property(Label)
    lblScore: Label | null = null;
    @property(Label)
    scoreAdd: Label | null = null;
    @property(Label)
    scoreHigh: Label | null = null;

    @property(Label)
    lblLevel: Label | null = null;
    @property(ProgressBar)
    expProBar: ProgressBar | null = null;
    @property(Label)
    lblExp: Label | null = null;

    @property(Node)
    nodeItem1: Node | null = null;
    @property(Node)
    nodeItem2: Node | null = null;
    @property(Node)
    nodeItem3: Node | null = null;

    @property(Node)
    nodeBestWeek: Node | null = null;

    @property(RichText)
    passText: RichText | null = null;

    @property(Node)
    nodePass: Node | null = null;

    @property(Label)
    lblTimeCost: Label | null = null;

    game_stats: csproto.comm.IGameStatItem[] = [];

    private _scoreTween: any; // 分数动画的 tween 引用
    private _expTween: any;   // 经验条动画的 tween 引用

    public static getUrl(): string { return "prefab/ui/SettlementUI"; }
    public static getLayer(): UILayer { return UILayer.Top }
    public static getBundleName(): string { return BundleName.Home }
    public static getUIOption(): UIOpt {
        return { isClickBgCloseUI: false }
    }

    protected onLoad(): void {
        this.btnOK!.addClick(this.onOKClick, this);
        this.btnNext!.addClick(this.onNextClick, this);

        this.scoreAdd!.string = "分数加成 " + 123 + "%";
        this.lblScore!.string = "0";
        this.scoreHigh!.string = "历史最高分 " + 10000;

        const gap = 0.5;
        // 分数动画
        const score = 1000;
        this._scoreTween = tween({ value: 0 })
            .to(gap, { value: score }, {
                onUpdate: (target) => {
                    if (!this.lblScore || target === undefined) return;
                    this.lblScore.string = Math.round(target.value).toString();
                }
            })
            .start();

        //  GM命令 //setattr xp 1000    
        let curLevel = DataMgr.role.curLevel;
        let curExp = DataMgr.role.curExp;
        let maxExp = DataMgr.role.maxExp;
        let exp = curExp / maxExp;
        if (exp > 1) {
            exp = 1;
        }

        this.expProBar!.progress = 0;
        if (maxExp > 0) {
            // 经验条动画
            this._expTween = tween(this.expProBar!)
                .to(gap, { progress: exp }, {
                    onUpdate: () => {
                        if (!this.expProBar) return;
                        this.lblExp!.string = `${Math.round(maxExp * this.expProBar.progress)}/${maxExp}`;
                    }
                })
                .start();
        }
        this.lblLevel!.string = "lv" + curLevel;

        this.setNodeItem(this.nodeItem1!, 80000101, 100);
        this.setNodeItem(this.nodeItem2!, 80100101, 0);
        this.setNodeItem(this.nodeItem3!, 89999998, 200);

        this.nodePass!.active = false;
    }
    async onOKClick() {
        // 停止所有动画
        if (this._scoreTween) this._scoreTween.stop();
        if (this._expTween) this._expTween.stop();
        UIMgr.closeUI(SettlementUI);
    }
    async onNextClick() {
        // 停止所有动画
        if (this._scoreTween) this._scoreTween.stop();
        if (this._expTween) this._expTween.stop();
        await UIMgr.openUI(StatisticsUI);
        UIMgr.closeUI(SettlementUI);
    }

    setNodeItem(node: Node, itemID: number, limit: number) {
        let res = MyApp.lubanTables.TbResItem.get(itemID);
        if (res === null) {
            return;
        }

        let nodeQuaIcon = node!.getChildByName("ItemQuaIcon");
        let icon = nodeQuaIcon!.getChildByName("icon");
        let quaSprite = nodeQuaIcon!.getChildByName("QualityTypeSprite");
        quaSprite?.getComponent(StateSprite)?.setState(quaSprite!.getComponent(Sprite)!, res!.quality);
        let labelNum = node!.getChildByName("LabelNum");
        UITools.modifyNumber(labelNum!.getComponent(Label)!, 111);
        let labelLimit = node!.getChildByName("LabelLimit");
        if (limit === 0) {
            labelLimit!.getComponent(Label)!.string = "";
        } else {
            UITools.modifyNumber(labelLimit!.getComponent(Label)!, limit);
        }
        const labelNumY = labelNum!.y;
        const labelLimitY = labelLimit!.y;
        const middleY = (labelNumY + labelLimitY) / 2;
        if (labelLimit!.getComponent(Label)!.string || labelLimit!.getComponent(Label)!.string.trim() === "") {
            labelNum!.y = middleY;
        }
    }
    async onShow(result: csproto.comm.IGameResult): Promise<void> {
        if (result == null) return;
        UITools.modifyColorTag(this.passText!, 1, { value: result.max_levels?.toString() ?? "0" });
        this.nodePass!.active = (result.is_pass == 1);
        this.lblScore!.string = result.total_score!.toString();
        this.nodeBestWeek!.active = (result.is_week_bset == 1);
        UITools.modifyNumber(this.scoreHigh!, result.history_bese_score!);
        this.lblTimeCost!.string = "用时 " + UITools.formatTime(result.total_time_cost!);
        result.reward_items?.forEach(item => {
            this.setNodeItem(this.nodeItem1!, item.item_id!, item.expiration!);
        });
        this.game_stats = result.game_stats!;
    }

    async onHide(): Promise<void> {
    }
    async onClose(): Promise<void> {
        // 停止所有动画
        if (this._scoreTween) this._scoreTween.stop();
        if (this._expTween) this._expTween.stop();
    }
}
