{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/data/mail/Mail.ts"], "names": ["Mail", "csproto", "log<PERSON>arn", "MyApp", "DataEvent", "EventMgr", "mail_list", "init", "netMgr", "registerHandler", "cs", "CS_CMD", "CS_CMD_MAIL_GET_LIST", "onMailGetList", "CS_CMD_MAIL_NEW_MAIL_NOTIFY", "onMailNewMailNotify", "CS_CMD_MAIL_UPDATE_READED", "onMailUpdateReaded", "CS_CMD_MAIL_GET_ATTACHMENT", "onMailGetAttachment", "CS_CMD_MAIL_DELETE", "onMailDelete", "cmdMailGetListAll", "update", "getMailByIndex", "index", "length", "undefined", "msg", "ret_code", "comm", "RET_CODE", "RET_CODE_NO_ERROR", "data", "body", "mail_get_list", "emit", "MailRefresh", "mail_new_mail_notify", "guids", "cmdMailGetList", "mail_update_readed", "mail_get_attachment", "mail_delete", "ids", "form", "to", "sendMessage", "index_from", "index_to", "cmdMailUpdateReaded", "cmdMailGetAttachment", "cmdMailDelete"], "mappings": ";;;4EAMaA,I;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AANNC,MAAAA,O;;AACEC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,Q,iBAAAA,Q;;;;;;;sBAEIL,I,GAAN,MAAMA,IAAN,CAA4B;AAAA;AAAA,eAE/BM,SAF+B,GAEG,EAFH;AAAA;;AAIxBC,QAAAA,IAAI,GAAS;AAChB;AAAA;AAAA,8BAAMC,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBC,oBAA/C,EAAqE,KAAKC,aAA1E,EAAyF,IAAzF;AACA;AAAA;AAAA,8BAAML,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBG,2BAA/C,EAA4E,KAAKC,mBAAjF,EAAsG,IAAtG;AACA;AAAA;AAAA,8BAAMP,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBK,yBAA/C,EAA0E,KAAKC,kBAA/E,EAAmG,IAAnG;AACA;AAAA;AAAA,8BAAMT,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBO,0BAA/C,EAA2E,KAAKC,mBAAhF,EAAqG,IAArG;AACA;AAAA;AAAA,8BAAMX,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBS,kBAA/C,EAAmE,KAAKC,YAAxE,EAAsF,IAAtF;AACA,eAAKC,iBAAL;AACH;;AACMC,QAAAA,MAAM,GAAS,CACrB;;AACMC,QAAAA,cAAc,CAACC,KAAD,EAAgD;AACjE,cAAIA,KAAK,IAAI,CAAT,IAAcA,KAAK,GAAG,KAAKnB,SAAL,CAAeoB,MAAzC,EAAiD;AAC7C,mBAAO,KAAKpB,SAAL,CAAemB,KAAf,CAAP;AACH;;AACD,iBAAOE,SAAP;AACH,SAnB8B,CAoB/B;;;AACAd,QAAAA,aAAa,CAACe,GAAD,EAA0B;AAAA;;AACnC,cAAIA,GAAG,CAACC,QAAJ,IAAgB;AAAA;AAAA,kCAAQC,IAAR,CAAaC,QAAb,CAAsBC,iBAA1C,EAA6D;AACzD;AAAA;AAAA,oCAAQ,QAAR,EAAmB,wBAAuBJ,GAAG,CAACC,QAAS,EAAvD;AACA;AACH;;AACD,cAAII,IAAI,gBAAGL,GAAG,CAACM,IAAP,qBAAG,UAAUC,aAArB;;AACA,cAAI,CAACF,IAAL,EAAW;AACP;AAAA;AAAA,oCAAQ,QAAR,EAAkB,4BAAlB;AACA;AACH;;AACD,eAAK3B,SAAL,GAAiB2B,IAAI,CAAC3B,SAAtB;AACA;AAAA;AAAA,oCAAS8B,IAAT,CAAc;AAAA;AAAA,sCAAUC,WAAxB,EAXmC,CAYnC;AACH;;AACDtB,QAAAA,mBAAmB,CAACa,GAAD,EAA0B;AAAA;;AACzC,cAAIA,GAAG,CAACC,QAAJ,IAAgB;AAAA;AAAA,kCAAQC,IAAR,CAAaC,QAAb,CAAsBC,iBAA1C,EAA6D;AACzD;AAAA;AAAA,oCAAQ,QAAR,EAAmB,8BAA6BJ,GAAG,CAACC,QAAS,EAA7D;AACA;AACH;;AACD,cAAII,IAAI,iBAAGL,GAAG,CAACM,IAAP,qBAAG,WAAUI,oBAArB;;AACA,cAAI,CAACL,IAAL,EAAW;AACP;AAAA;AAAA,oCAAQ,QAAR,EAAkB,kCAAlB;AACA;AACH;;AACD,cAAIM,KAAK,GAAGN,IAAI,CAACM,KAAjB;AACA,eAAKC,cAAL,CAAoBD,KAApB,EAA2B,CAA3B,EAA8BA,KAAK,CAACb,MAApC;AACH;;AACDT,QAAAA,kBAAkB,CAACW,GAAD,EAA0B;AAAA;;AACxC,cAAIA,GAAG,CAACC,QAAJ,IAAgB;AAAA;AAAA,kCAAQC,IAAR,CAAaC,QAAb,CAAsBC,iBAA1C,EAA6D;AACzD;AAAA;AAAA,oCAAQ,QAAR,EAAmB,6BAA4BJ,GAAG,CAACC,QAAS,EAA5D;AACA;AACH;;AACD,cAAII,IAAI,iBAAGL,GAAG,CAACM,IAAP,qBAAG,WAAUO,kBAArB;;AACA,cAAI,CAACR,IAAL,EAAW;AACP;AAAA;AAAA,oCAAQ,QAAR,EAAkB,iCAAlB;AACA;AACH;;AACD;AAAA;AAAA,oCAASG,IAAT,CAAc;AAAA;AAAA,sCAAUC,WAAxB,EAAqCJ,IAAI,CAACM,KAA1C;AACH;;AACDpB,QAAAA,mBAAmB,CAACS,GAAD,EAA0B;AAAA;;AACzC,cAAIA,GAAG,CAACC,QAAJ,IAAgB;AAAA;AAAA,kCAAQC,IAAR,CAAaC,QAAb,CAAsBC,iBAA1C,EAA6D;AACzD;AAAA;AAAA,oCAAQ,QAAR,EAAmB,8BAA6BJ,GAAG,CAACC,QAAS,EAA7D;AACA;AACH;;AACD,cAAII,IAAI,iBAAGL,GAAG,CAACM,IAAP,qBAAG,WAAUQ,mBAArB;;AACA,cAAI,CAACT,IAAL,EAAW;AACP;AAAA;AAAA,oCAAQ,QAAR,EAAkB,kCAAlB;AACA;AACH;AACJ;;AACDZ,QAAAA,YAAY,CAACO,GAAD,EAA0B;AAAA;;AAClC,cAAIA,GAAG,CAACC,QAAJ,IAAgB;AAAA;AAAA,kCAAQC,IAAR,CAAaC,QAAb,CAAsBC,iBAA1C,EAA6D;AACzD;AAAA;AAAA,oCAAQ,QAAR,EAAmB,uBAAsBJ,GAAG,CAACC,QAAS,EAAtD;AACA;AACH;;AACD,cAAII,IAAI,iBAAGL,GAAG,CAACM,IAAP,qBAAG,WAAUS,WAArB;;AACA,cAAI,CAACV,IAAL,EAAW;AACP;AAAA;AAAA,oCAAQ,QAAR,EAAkB,2BAAlB;AACA;AACH;AACJ,SAjF8B,CAkF/B;AACA;;;AACAX,QAAAA,iBAAiB,GAAG;AAChB,eAAKkB,cAAL,CAAoB,EAApB,EAAwB,CAAxB,EAA2B,CAA3B;AACH;;AAEDA,QAAAA,cAAc,CAACI,GAAD,EAAcC,IAAd,EAA4BC,EAA5B,EAAwC;AAClD;AAAA;AAAA,8BAAMtC,MAAN,CAAauC,WAAb,CAAyB;AAAA;AAAA,kCAAQrC,EAAR,CAAWC,MAAX,CAAkBC,oBAA3C,EAAiE;AAC7DuB,YAAAA,aAAa,EAAE;AACXI,cAAAA,KAAK,EAAEK,GADI;AAEXI,cAAAA,UAAU,EAAEH,IAFD;AAGXI,cAAAA,QAAQ,EAAEH;AAHC;AAD8C,WAAjE;AAOH;;AACDI,QAAAA,mBAAmB,CAACN,GAAD,EAAc;AAC7B;AAAA;AAAA,8BAAMpC,MAAN,CAAauC,WAAb,CAAyB;AAAA;AAAA,kCAAQrC,EAAR,CAAWC,MAAX,CAAkBK,yBAA3C,EAAsE;AAClEyB,YAAAA,kBAAkB,EAAE;AAChBF,cAAAA,KAAK,EAAEK;AADS;AAD8C,WAAtE;AAKH;;AACDO,QAAAA,oBAAoB,CAACP,GAAD,EAAc;AAC9B;AAAA;AAAA,8BAAMpC,MAAN,CAAauC,WAAb,CAAyB;AAAA;AAAA,kCAAQrC,EAAR,CAAWC,MAAX,CAAkBO,0BAA3C,EAAuE;AACnEwB,YAAAA,mBAAmB,EAAE;AACjBH,cAAAA,KAAK,EAAEK;AADU;AAD8C,WAAvE;AAKH;;AACDQ,QAAAA,aAAa,CAACR,GAAD,EAAc;AACvB;AAAA;AAAA,8BAAMpC,MAAN,CAAauC,WAAb,CAAyB;AAAA;AAAA,kCAAQrC,EAAR,CAAWC,MAAX,CAAkBS,kBAA3C,EAA+D;AAC3DuB,YAAAA,WAAW,EAAE;AACTJ,cAAAA,KAAK,EAAEK;AADE;AAD8C,WAA/D;AAKH,SArH8B,CAsH/B;;;AAtH+B,O", "sourcesContent": ["import csproto from 'db://assets/bundles/common/script/autogen/pb/cs_proto.js';\nimport { logWarn } from 'db://assets/scripts/utils/Logger';\nimport { MyApp } from \"../../app/MyApp\";\nimport { DataEvent } from '../../event/DataEvent';\nimport { EventMgr } from '../../event/EventManager';\nimport { IData } from \"../DataManager\";\nexport class Mail implements IData {\n\n    mail_list: csproto.comm.IMail[] = [];\n\n    public init(): void {\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_MAIL_GET_LIST, this.onMailGetList, this);\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_MAIL_NEW_MAIL_NOTIFY, this.onMailNewMailNotify, this);\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_MAIL_UPDATE_READED, this.onMailUpdateReaded, this);\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_MAIL_GET_ATTACHMENT, this.onMailGetAttachment, this);\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_MAIL_DELETE, this.onMailDelete, this);\n        this.cmdMailGetListAll();\n    }\n    public update(): void {\n    }\n    public getMailByIndex(index: number): csproto.comm.IMail | undefined {\n        if (index >= 0 && index < this.mail_list.length) {\n            return this.mail_list[index];\n        }\n        return undefined;\n    }\n    //#region 收协议\n    onMailGetList(msg: csproto.cs.IS2CMsg) {\n        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {\n            logWarn(\"NetMgr\", `onMailGetList failed ${msg.ret_code}`);\n            return;\n        }\n        var data = msg.body?.mail_get_list;\n        if (!data) {\n            logWarn(\"NetMgr\", \"onMailGetList data is null\");\n            return;\n        }\n        this.mail_list = data.mail_list!;\n        EventMgr.emit(DataEvent.MailRefresh);\n        //MessageBox.show(`onMailGetList ${JSON.stringify(data)}`);\n    }\n    onMailNewMailNotify(msg: csproto.cs.IS2CMsg) {\n        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {\n            logWarn(\"NetMgr\", `onMailNewMailNotify failed ${msg.ret_code}`);\n            return;\n        }\n        var data = msg.body?.mail_new_mail_notify;\n        if (!data) {\n            logWarn(\"NetMgr\", \"onMailNewMailNotify data is null\");\n            return;\n        }\n        let guids = data.guids!;\n        this.cmdMailGetList(guids, 0, guids.length);\n    }\n    onMailUpdateReaded(msg: csproto.cs.IS2CMsg) {\n        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {\n            logWarn(\"NetMgr\", `onMailUpdateReaded failed ${msg.ret_code}`);\n            return;\n        }\n        var data = msg.body?.mail_update_readed;\n        if (!data) {\n            logWarn(\"NetMgr\", \"onMailUpdateReaded data is null\");\n            return;\n        }\n        EventMgr.emit(DataEvent.MailRefresh, data.guids);\n    }\n    onMailGetAttachment(msg: csproto.cs.IS2CMsg) {\n        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {\n            logWarn(\"NetMgr\", `onMailGetAttachment failed ${msg.ret_code}`);\n            return;\n        }\n        var data = msg.body?.mail_get_attachment;\n        if (!data) {\n            logWarn(\"NetMgr\", \"onMailGetAttachment data is null\");\n            return;\n        }\n    }\n    onMailDelete(msg: csproto.cs.IS2CMsg) {\n        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {\n            logWarn(\"NetMgr\", `onMailDelete failed ${msg.ret_code}`);\n            return;\n        }\n        var data = msg.body?.mail_delete;\n        if (!data) {\n            logWarn(\"NetMgr\", \"onMailDelete data is null\");\n            return;\n        }\n    }\n    //#endregion\n    //#region 发协议\n    cmdMailGetListAll() {\n        this.cmdMailGetList([], 0, 0);\n    }\n\n    cmdMailGetList(ids: Long[], form: number, to: number) {\n        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_MAIL_GET_LIST, {\n            mail_get_list: {\n                guids: ids,\n                index_from: form,\n                index_to: to,\n            }\n        });\n    }\n    cmdMailUpdateReaded(ids: Long[]) {\n        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_MAIL_UPDATE_READED, {\n            mail_update_readed: {\n                guids: ids\n            }\n        });\n    }\n    cmdMailGetAttachment(ids: Long[]) {\n        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_MAIL_GET_ATTACHMENT, {\n            mail_get_attachment: {\n                guids: ids\n            }\n        });\n    }\n    cmdMailDelete(ids: Long[]) {\n        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_MAIL_DELETE, {\n            mail_delete: {\n                guids: ids\n            }\n        });\n    }\n    //#endregion\n}\n"]}