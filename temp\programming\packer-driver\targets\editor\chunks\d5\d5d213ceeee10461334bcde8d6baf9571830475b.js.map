{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/data/plane/PlaneData.ts"], "names": ["PlaneData", "error", "MyApp", "PlanePropType", "AttributeConst", "AttributeData", "DataMgr", "PATH_SPINE", "id", "_planeId", "config", "planeId", "value", "updateConfig", "recourseSpine", "prefab", "lubanTables", "TbPlane", "get", "updateData", "addBaseAttribute", "MaxHPOutAdd", "property", "MaxHP", "AttackOutAdd", "Attack", "HPRecoveryOutAdd", "HPRecovery", "FortunateOutAdd", "Fortunate", "MissRateOut", "Miss", "BulletHurtResistanceOutPer", "BulletHurtResistance", "CollisionHurtResistanceOutPer", "CollisionHurtResistance", "PickRadiusOutAdd", "PickRadius", "FinalScoreRateOut", "FinalScore", "NuclearMax", "MaxEnergyOutAdd", "MaxEnergy", "EnergyRecoveryOutAdd", "EnergyRecovery", "equip", "eqSlots", "slots", "for<PERSON>ach", "slot", "equip_id", "level", "equip_class", "equipConfig", "TbResEquip", "equipUpgradeConfigs", "TbEquipUpgrade", "getDataList", "element", "equipClass", "levelFrom", "push", "sort", "a", "b", "levelTo", "equipUpgradeProps", "Map", "equipUpgradeConfig", "i", "propInc", "prop", "propType", "NONE", "set", "prop<PERSON><PERSON><PERSON>", "props", "AllBulletAttack", "BulletAttackOutAdd", "ExplosiveBulletAttack", "ExplosiveBulletAttackOutAdd", "NormalBulletAttack", "NormalBulletAttackOutAdd", "EnergeticBulletAttack", "EnergeticBulletAttackOutAdd", "PhysicalBulletAttack", "PhysicsBulletAttackOutAdd", "getAttributeList"], "mappings": ";;;oJASaA,S;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AATJC,MAAAA,K,OAAAA,K;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,c,iBAAAA,c;;AACAC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,O,iBAAAA,O;;;;;;;;;AAEHC,MAAAA,U,GAAa,wB;;2BAENP,S,GAAN,MAAMA,SAAN;AAAA;AAAA,0CAAsC;AAAA;AAAA;AAAA,eACzCQ,EADyC,GAC5B,CAD4B;AAC1B;AAD0B,eAEzCC,QAFyC,GAEtB,CAFsB;AAEpB;AAFoB,eAIzCC,MAJyC,GAIf,IAJe;AAAA;;AAIV;AAEpB,YAAPC,OAAO,GAAG;AACV,iBAAO,KAAKF,QAAZ;AACH;;AAEU,YAAPE,OAAO,CAACC,KAAD,EAAQ;AACf,cAAIA,KAAK,IAAI,KAAKH,QAAlB,EAA4B;AACxB,iBAAKA,QAAL,GAAgBG,KAAhB;AACA,iBAAKC,YAAL;AACH;AACJ;;AAEgB,YAAbC,aAAa,GAAG;AAChB,cAAI,CAAC,KAAKJ,MAAV,EAAkB;AACd,mBAAO,EAAP;AACH;;AACD,iBAAOH,UAAU,GAAG,KAAKG,MAAL,CAAYK,MAAzB,GAAkC,GAAlC,GAAwC,KAAKL,MAAL,CAAYK,MAA3D;AACH;;AAEDF,QAAAA,YAAY,GAAG;AACX,cAAI,CAAC,KAAKJ,QAAV,EAAoB;AAChBR,YAAAA,KAAK,CAAC,+CAAD,CAAL;AACA;AACH;;AACD,eAAKS,MAAL,GAAc;AAAA;AAAA,8BAAMM,WAAN,CAAkBC,OAAlB,CAA0BC,GAA1B,CAA8B,KAAKT,QAAnC,KAA8C,IAA5D;AACA,eAAKU,UAAL;AACH;;AAEDA,QAAAA,UAAU,GAAG;AACT,cAAI,CAAC,KAAKT,MAAV,EAAkB;AACdT,YAAAA,KAAK,CAAE,SAAQ,KAAKQ,QAAS,4CAAxB,CAAL;AACA;AACH,WAJQ,CAMT;;;AACA,eAAKW,gBAAL,CAAsB;AAAA;AAAA,gDAAeC,WAArC,EAAkD,KAAKX,MAAL,CAAYY,QAAZ,CAAqBC,KAAvE;AACA,eAAKH,gBAAL,CAAsB;AAAA;AAAA,gDAAeI,YAArC,EAAmD,KAAKd,MAAL,CAAYY,QAAZ,CAAqBG,MAAxE;AACA,eAAKL,gBAAL,CAAsB;AAAA;AAAA,gDAAeM,gBAArC,EAAuD,KAAKhB,MAAL,CAAYY,QAAZ,CAAqBK,UAA5E;AACA,eAAKP,gBAAL,CAAsB;AAAA;AAAA,gDAAeQ,eAArC,EAAsD,KAAKlB,MAAL,CAAYY,QAAZ,CAAqBO,SAA3E;AACA,eAAKT,gBAAL,CAAsB;AAAA;AAAA,gDAAeU,WAArC,EAAkD,KAAKpB,MAAL,CAAYY,QAAZ,CAAqBS,IAAvE;AACA,eAAKX,gBAAL,CAAsB;AAAA;AAAA,gDAAeY,0BAArC,EAAiE,KAAKtB,MAAL,CAAYY,QAAZ,CAAqBW,oBAAtF;AACA,eAAKb,gBAAL,CAAsB;AAAA;AAAA,gDAAec,6BAArC,EAAoE,KAAKxB,MAAL,CAAYY,QAAZ,CAAqBa,uBAAzF;AACA,eAAKf,gBAAL,CAAsB;AAAA;AAAA,gDAAegB,gBAArC,EAAuD,KAAK1B,MAAL,CAAYY,QAAZ,CAAqBe,UAA5E;AACA,eAAKjB,gBAAL,CAAsB;AAAA;AAAA,gDAAekB,iBAArC,EAAwD,KAAK5B,MAAL,CAAYY,QAAZ,CAAqBiB,UAA7E;AACA,eAAKnB,gBAAL,CAAsB;AAAA;AAAA,gDAAeoB,UAArC,EAAiD,KAAK9B,MAAL,CAAYY,QAAZ,CAAqBkB,UAAtE;AACA,eAAKpB,gBAAL,CAAsB;AAAA;AAAA,gDAAeqB,eAArC,EAAsD,KAAK/B,MAAL,CAAYY,QAAZ,CAAqBoB,SAA3E;AACA,eAAKtB,gBAAL,CAAsB;AAAA;AAAA,gDAAeuB,oBAArC,EAA2D,KAAKjC,MAAL,CAAYY,QAAZ,CAAqBsB,cAAhF,EAlBS,CAoBT;;AACA;AAAA;AAAA,kCAAQC,KAAR,CAAcC,OAAd,CAAsBC,KAAtB,CAA4BC,OAA5B,CAAoCC,IAAI,IAAI;AACxC,gBAAI,CAACA,IAAI,CAACC,QAAN,IAAkB,CAACD,IAAI,CAACE,KAAxB,IAAiC,CAACF,IAAI,CAACG,WAA3C,EAAwD;AACpD;AACH;;AACD,kBAAMC,WAAW,GAAG;AAAA;AAAA,gCAAMrC,WAAN,CAAkBsC,UAAlB,CAA6BpC,GAA7B,CAAiC+B,IAAI,CAACC,QAAtC,CAApB;;AACA,gBAAI,CAACG,WAAL,EAAkB;AACd;AACH;;AACD,kBAAME,mBAAsC,GAAG,EAA/C;AACA;AAAA;AAAA,gCAAMvC,WAAN,CAAkBwC,cAAlB,CAAiCC,WAAjC,GAA+CT,OAA/C,CAAuDU,OAAO,IAAI;AAC9D,kBAAIA,OAAO,CAACC,UAAR,IAAsBV,IAAI,CAACG,WAA3B,IAA0CM,OAAO,CAACE,SAAR,IAAqBX,IAAI,CAACE,KAAxE,EAAgF;AAC5EI,gBAAAA,mBAAmB,CAACM,IAApB,CAAyBH,OAAzB;AACH;AACJ,aAJD;AAKAH,YAAAA,mBAAmB,CAACO,IAApB,CAAyB,CAACC,CAAD,EAAIC,CAAJ,KAAUD,CAAC,CAACE,OAAF,GAAYD,CAAC,CAACC,OAAjD;AACA,kBAAMC,iBAA6C,GAAG,IAAIC,GAAJ,EAAtD;;AACA,iBAAK,IAAIC,kBAAT,IAA+Bb,mBAA/B,EAAoD;AAChD,mBAAK,IAAIc,CAAC,GAAGD,kBAAkB,CAACR,SAAhC,EAA2CS,CAAC,IAAIpB,IAAI,CAACE,KAAV,IAAoBkB,CAAC,IAAID,kBAAkB,CAACH,OAAvF,EAAgGI,CAAC,EAAjG,EAAqG;AACjGD,gBAAAA,kBAAkB,CAACE,OAAnB,CAA2BtB,OAA3B,CAAmCuB,IAAI,IAAI;AACvC,sBAAIA,IAAI,CAACC,QAAL,IAAiB;AAAA;AAAA,sDAAcC,IAAnC,EAAyC;AACrC;AACH;;AACDP,kBAAAA,iBAAiB,CAACQ,GAAlB,CAAsBH,IAAI,CAACC,QAA3B,EAAqC,CAACN,iBAAiB,CAAChD,GAAlB,CAAsBqD,IAAI,CAACC,QAA3B,KAAwC,CAAzC,KAA+C,QAAQD,IAAI,CAACI,SAA5D,IAAuE,KAA5G;AACH,iBALD;AAMH;AACJ;;AACDtB,YAAAA,WAAW,CAACuB,KAAZ,CAAkB5B,OAAlB,CAA0BuB,IAAI,IAAI;AAC9B,oBAAM3D,KAAK,GAAG2D,IAAI,CAACI,SAAL,IAAkBT,iBAAiB,CAAChD,GAAlB,CAAsBqD,IAAI,CAACC,QAA3B,KAAwC,CAA1D,CAAd;;AACA,sBAAOD,IAAI,CAACC,QAAZ;AACI,qBAAK;AAAA;AAAA,oDAAcjD,KAAnB;AACI,uBAAKH,gBAAL,CAAsB;AAAA;AAAA,wDAAeC,WAArC,EAAkDT,KAAlD;AACA;;AACJ,qBAAK;AAAA;AAAA,oDAAce,UAAnB;AACI,uBAAKP,gBAAL,CAAsB;AAAA;AAAA,wDAAeM,gBAArC,EAAuDd,KAAvD;AACA;;AACJ,qBAAK;AAAA;AAAA,oDAAca,MAAnB;AACI,uBAAKL,gBAAL,CAAsB;AAAA;AAAA,wDAAeI,YAArC,EAAmDZ,KAAnD;AACA;;AACJ,qBAAK;AAAA;AAAA,oDAAciB,SAAnB;AACI,uBAAKT,gBAAL,CAAsB;AAAA;AAAA,wDAAeQ,eAArC,EAAsDhB,KAAtD;AACA;;AACJ,qBAAK;AAAA;AAAA,oDAAcmB,IAAnB;AACI,uBAAKX,gBAAL,CAAsB;AAAA;AAAA,wDAAeU,WAArC,EAAkDlB,KAAlD;AACA;;AACJ,qBAAK;AAAA;AAAA,oDAAcqB,oBAAnB;AACI,uBAAKb,gBAAL,CAAsB;AAAA;AAAA,wDAAeY,0BAArC,EAAiEpB,KAAjE;AACA;;AACJ,qBAAK;AAAA;AAAA,oDAAcuB,uBAAnB;AACI,uBAAKf,gBAAL,CAAsB;AAAA;AAAA,wDAAec,6BAArC,EAAoEtB,KAApE;AACA;;AACJ,qBAAK;AAAA;AAAA,oDAAcyB,UAAnB;AACI,uBAAKjB,gBAAL,CAAsB;AAAA;AAAA,wDAAegB,gBAArC,EAAuDxB,KAAvD;AACA;;AACJ,qBAAK;AAAA;AAAA,oDAAc2B,UAAnB;AACI,uBAAKnB,gBAAL,CAAsB;AAAA;AAAA,wDAAekB,iBAArC,EAAwD1B,KAAxD;AACA;;AACJ,qBAAK;AAAA;AAAA,oDAAc4B,UAAnB;AACI,uBAAKpB,gBAAL,CAAsB;AAAA;AAAA,wDAAeoB,UAArC,EAAiD5B,KAAjD;AACA;;AACJ,qBAAK;AAAA;AAAA,oDAAc8B,SAAnB;AACI,uBAAKtB,gBAAL,CAAsB;AAAA;AAAA,wDAAeqB,eAArC,EAAsD7B,KAAtD;AACA;;AACJ,qBAAK;AAAA;AAAA,oDAAcgC,cAAnB;AACI,uBAAKxB,gBAAL,CAAsB;AAAA;AAAA,wDAAeuB,oBAArC,EAA2D/B,KAA3D;AACA;;AACJ,qBAAK;AAAA;AAAA,oDAAciE,eAAnB;AACI,uBAAKzD,gBAAL,CAAsB;AAAA;AAAA,wDAAe0D,kBAArC,EAAyDlE,KAAzD;AACA;;AACJ,qBAAK;AAAA;AAAA,oDAAcmE,qBAAnB;AACI,uBAAK3D,gBAAL,CAAsB;AAAA;AAAA,wDAAe4D,2BAArC,EAAkEpE,KAAlE;AACA;;AACJ,qBAAK;AAAA;AAAA,oDAAcqE,kBAAnB;AACI,uBAAK7D,gBAAL,CAAsB;AAAA;AAAA,wDAAe8D,wBAArC,EAA+DtE,KAA/D;AACA;;AACJ,qBAAK;AAAA;AAAA,oDAAcuE,qBAAnB;AACI,uBAAK/D,gBAAL,CAAsB;AAAA;AAAA,wDAAegE,2BAArC,EAAkExE,KAAlE;AACA;;AACJ,qBAAK;AAAA;AAAA,oDAAcyE,oBAAnB;AACI,uBAAKjE,gBAAL,CAAsB;AAAA;AAAA,wDAAekE,yBAArC,EAAgE1E,KAAhE;AACA;AAnDR;AAqDH,aAvDD;AAwDH,WAlFD;AAmFH;;AAED2E,QAAAA,gBAAgB,GAAG,CACf;AACH;;AA7IwC,O", "sourcesContent": ["import { error } from \"cc\";\r\nimport { MyApp } from \"db://assets/bundles/common/script/app/MyApp\";\r\nimport { PlanePropType, ResEquipUpgrade, ResPlane } from \"db://assets/bundles/common/script/autogen/luban/schema\";\r\nimport { AttributeConst } from \"../../const/AttributeConst\";\r\nimport { AttributeData } from \"../base/AttributeData\";\r\nimport { DataMgr } from \"../DataManager\";\r\n\r\nconst PATH_SPINE = \"spine/plane/mainplane/\"\r\n\r\nexport class PlaneData extends AttributeData {\r\n    id: number = 0;//唯一id\r\n    _planeId: number = 0;//飞机id\r\n\r\n    config: ResPlane | null = null;//飞机静态配置\r\n\r\n    get planeId() {\r\n        return this._planeId;\r\n    }\r\n\r\n    set planeId(value) {\r\n        if (value != this._planeId) {\r\n            this._planeId = value;\r\n            this.updateConfig();\r\n        }\r\n    }\r\n\r\n    get recourseSpine() {\r\n        if (!this.config) {\r\n            return \"\";\r\n        }\r\n        return PATH_SPINE + this.config.prefab + \"/\" + this.config.prefab\r\n    }\r\n\r\n    updateConfig() {\r\n        if (!this._planeId) {\r\n            error(\"Plane id or level is 0, cannot update config.\");\r\n            return;\r\n        }\r\n        this.config = MyApp.lubanTables.TbPlane.get(this._planeId)||null;\r\n        this.updateData();\r\n    }\r\n\r\n    updateData() {\r\n        if (!this.config) {\r\n            error(`Plane ${this._planeId} config is null, cannot update attributes.`);\r\n            return;\r\n        }\r\n\r\n        //根据飞机基础配置表，获取基础属性\r\n        this.addBaseAttribute(AttributeConst.MaxHPOutAdd, this.config.property.MaxHP);\r\n        this.addBaseAttribute(AttributeConst.AttackOutAdd, this.config.property.Attack);\r\n        this.addBaseAttribute(AttributeConst.HPRecoveryOutAdd, this.config.property.HPRecovery);\r\n        this.addBaseAttribute(AttributeConst.FortunateOutAdd, this.config.property.Fortunate);\r\n        this.addBaseAttribute(AttributeConst.MissRateOut, this.config.property.Miss);\r\n        this.addBaseAttribute(AttributeConst.BulletHurtResistanceOutPer, this.config.property.BulletHurtResistance);\r\n        this.addBaseAttribute(AttributeConst.CollisionHurtResistanceOutPer, this.config.property.CollisionHurtResistance);\r\n        this.addBaseAttribute(AttributeConst.PickRadiusOutAdd, this.config.property.PickRadius);\r\n        this.addBaseAttribute(AttributeConst.FinalScoreRateOut, this.config.property.FinalScore);\r\n        this.addBaseAttribute(AttributeConst.NuclearMax, this.config.property.NuclearMax);\r\n        this.addBaseAttribute(AttributeConst.MaxEnergyOutAdd, this.config.property.MaxEnergy);\r\n        this.addBaseAttribute(AttributeConst.EnergyRecoveryOutAdd, this.config.property.EnergyRecovery);\r\n\r\n        // 装备\r\n        DataMgr.equip.eqSlots.slots.forEach(slot => {\r\n            if (!slot.equip_id || !slot.level || !slot.equip_class) {\r\n                return\r\n            }\r\n            const equipConfig = MyApp.lubanTables.TbResEquip.get(slot.equip_id);\r\n            if (!equipConfig) {\r\n                return\r\n            }\r\n            const equipUpgradeConfigs: ResEquipUpgrade[] = []\r\n            MyApp.lubanTables.TbEquipUpgrade.getDataList().forEach(element => {\r\n                if (element.equipClass == slot.equip_class && element.levelFrom <= slot.level!) {\r\n                    equipUpgradeConfigs.push(element);\r\n                }\r\n            });\r\n            equipUpgradeConfigs.sort((a, b) => a.levelTo - b.levelTo);\r\n            const equipUpgradeProps: Map<PlanePropType, number> = new Map()\r\n            for (let equipUpgradeConfig of equipUpgradeConfigs) {\r\n                for (let i = equipUpgradeConfig.levelFrom; i <= slot.level! && i <= equipUpgradeConfig.levelTo; i++) {\r\n                    equipUpgradeConfig.propInc.forEach(prop => {\r\n                        if (prop.propType == PlanePropType.NONE) {\r\n                            return\r\n                        }\r\n                        equipUpgradeProps.set(prop.propType, (equipUpgradeProps.get(prop.propType) || 1) * (10000 + prop.propParam)/10000)\r\n                    })\r\n                }\r\n            }\r\n            equipConfig.props.forEach(prop => {\r\n                const value = prop.propParam * (equipUpgradeProps.get(prop.propType) || 1);\r\n                switch(prop.propType) {\r\n                    case PlanePropType.MaxHP:\r\n                        this.addBaseAttribute(AttributeConst.MaxHPOutAdd, value);\r\n                        break;\r\n                    case PlanePropType.HPRecovery:\r\n                        this.addBaseAttribute(AttributeConst.HPRecoveryOutAdd, value);\r\n                        break;\r\n                    case PlanePropType.Attack:\r\n                        this.addBaseAttribute(AttributeConst.AttackOutAdd, value);\r\n                        break;\r\n                    case PlanePropType.Fortunate:\r\n                        this.addBaseAttribute(AttributeConst.FortunateOutAdd, value);\r\n                        break;\r\n                    case PlanePropType.Miss:\r\n                        this.addBaseAttribute(AttributeConst.MissRateOut, value);\r\n                        break;\r\n                    case PlanePropType.BulletHurtResistance:\r\n                        this.addBaseAttribute(AttributeConst.BulletHurtResistanceOutPer, value);\r\n                        break;\r\n                    case PlanePropType.CollisionHurtResistance:\r\n                        this.addBaseAttribute(AttributeConst.CollisionHurtResistanceOutPer, value);\r\n                        break;\r\n                    case PlanePropType.PickRadius:\r\n                        this.addBaseAttribute(AttributeConst.PickRadiusOutAdd, value);\r\n                        break;\r\n                    case PlanePropType.FinalScore:\r\n                        this.addBaseAttribute(AttributeConst.FinalScoreRateOut, value);\r\n                        break;\r\n                    case PlanePropType.NuclearMax:\r\n                        this.addBaseAttribute(AttributeConst.NuclearMax, value);\r\n                        break;\r\n                    case PlanePropType.MaxEnergy:\r\n                        this.addBaseAttribute(AttributeConst.MaxEnergyOutAdd, value);\r\n                        break;\r\n                    case PlanePropType.EnergyRecovery:\r\n                        this.addBaseAttribute(AttributeConst.EnergyRecoveryOutAdd, value);\r\n                        break;\r\n                    case PlanePropType.AllBulletAttack:\r\n                        this.addBaseAttribute(AttributeConst.BulletAttackOutAdd, value);\r\n                        break;\r\n                    case PlanePropType.ExplosiveBulletAttack:\r\n                        this.addBaseAttribute(AttributeConst.ExplosiveBulletAttackOutAdd, value);\r\n                        break;\r\n                    case PlanePropType.NormalBulletAttack:\r\n                        this.addBaseAttribute(AttributeConst.NormalBulletAttackOutAdd, value);\r\n                        break;\r\n                    case PlanePropType.EnergeticBulletAttack:\r\n                        this.addBaseAttribute(AttributeConst.EnergeticBulletAttackOutAdd, value);\r\n                        break;\r\n                    case PlanePropType.PhysicalBulletAttack:\r\n                        this.addBaseAttribute(AttributeConst.PhysicsBulletAttackOutAdd, value);\r\n                        break;\r\n                }\r\n            })\r\n        })\r\n    }\r\n\r\n    getAttributeList() {\r\n        // 获取装备，技能，buff等属性\r\n    }\r\n}"]}