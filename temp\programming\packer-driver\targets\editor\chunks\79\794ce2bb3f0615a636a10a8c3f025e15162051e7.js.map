{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/move/PathMove.ts"], "names": ["_decorator", "misc", "Vec2", "JsonAsset", "MoveBase", "PathData", "degreesToRadians", "radiansToDegrees", "ccclass", "property", "PathMovable", "type", "displayName", "tooltip", "_pathData", "_subdivided", "_totalDistance", "_distances", "_currentDistance", "_currentPointIndex", "_segmentT", "_tiltTime", "_speedMultiplier", "onLoad", "loadPathData", "pathAsset", "fromJSON", "json", "getSubdividedPoints", "calculateDistances", "i", "length", "distance", "position", "push", "tick", "dt", "_isMovable", "currentSpeed", "getCurrentSpeed", "deltaDistance", "reverse", "loop", "updateCurrentPointIndex", "updatePosition", "Math", "max", "searchStart", "found", "segmentLength", "min", "speed", "startSpeed", "endSpeed", "interpolatedSpeed", "getPositionAtDistance", "tiltSpeed", "tiltOffset", "direction", "getDirectionAtDistance", "lengthSqr", "perpX", "y", "perpY", "x", "tiltAmount", "sin", "node", "setPosition", "_visibility<PERSON><PERSON><PERSON><PERSON>ounter", "VISIBILITY_CHECK_INTERVAL", "checkVisibility", "abs", "getCurrentPosition", "clone", "segmentStart", "segmentEnd", "t", "lerp", "startPos", "endPos", "epsilon", "pos1", "pos2", "subtract", "normalize", "setMovable", "movable", "setProgress", "progress", "getProgress", "resetToStart", "moveToEnd", "getCurrentPathPointData", "getCurrentPathInfo", "currentPoint", "pathPoint", "totalDistance", "setSpeedMultiplier", "multiplier", "getSpeedMultiplier"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAA6BC,MAAAA,I,OAAAA,I;AAAyBC,MAAAA,I,OAAAA,I;AAAYC,MAAAA,S,OAAAA,S;;AAElEC,MAAAA,Q,iBAAAA,Q;;AAEAC,MAAAA,Q,iBAAAA,Q;;;;;;;;;OAEH;AAAEC,QAAAA,gBAAF;AAAoBC,QAAAA;AAApB,O,GAAyCN,I;OACzC;AAAEO,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;;6BAGjBU,W,WADZF,OAAO,CAAC,aAAD,C,UAGHC,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAER,SAAR;AAAmBS,QAAAA,WAAW,EAAE;AAAhC,OAAD,C,UAGRH,QAAQ,CAAC;AAAEG,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAGRH,QAAQ,CAAC;AAAEG,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAGRH,QAAQ,CAAC;AAAEG,QAAAA,WAAW,EAAE,QAAf;AAAyBC,QAAAA,OAAO,EAAE;AAAlC,OAAD,C,UAGRJ,QAAQ,CAAC;AAAEG,QAAAA,WAAW,EAAE,QAAf;AAAyBC,QAAAA,OAAO,EAAE;AAAlC,OAAD,C,2BAfb,MACaH,WADb;AAAA;AAAA,gCAC0C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAiBtC;AAjBsC,eAkB9BI,SAlB8B,GAkBD,IAlBC;AAAA,eAmB9BC,WAnB8B,GAmBH,EAnBG;AAmBC;AAnBD,eAoB9BC,cApB8B,GAoBL,CApBK;AAAA,eAqB9BC,UArB8B,GAqBP,EArBO;AAuBtC;AAvBsC,eAwB9BC,gBAxB8B,GAwBH,CAxBG;AAAA,eAyB9BC,kBAzB8B,GAyBD,CAzBC;AAyBE;AAzBF,eA0B9BC,SA1B8B,GA0BV,CA1BU;AA0BP;AA1BO,eA2B9BC,SA3B8B,GA2BV,CA3BU;;AA6VtC;AACJ;AACA;AA/V0C,eAgW9BC,gBAhW8B,GAgWH,GAhWG;AAAA;;AA6BtCC,QAAAA,MAAM,GAAG;AACL,gBAAMA,MAAN;AACA,eAAKC,YAAL;AACH;AAED;AACJ;AACA;;;AACYA,QAAAA,YAAY,GAAG;AACnB,cAAI,CAAC,KAAKC,SAAV,EAAqB,OADF,CAGnB;;AACA,eAAKX,SAAL,GAAiB;AAAA;AAAA,oCAASY,QAAT,CAAkB,KAAKD,SAAL,CAAeE,IAAjC,CAAjB,CAJmB,CAMnB;;AACA,eAAKZ,WAAL,GAAmB,KAAKD,SAAL,CAAec,mBAAf,EAAnB,CAPmB,CASnB;;AACA,eAAKC,kBAAL;AACH;AAED;AACJ;AACA;;;AACYA,QAAAA,kBAAkB,GAAG;AACzB,eAAKZ,UAAL,GAAkB,CAAC,CAAD,CAAlB;AACA,eAAKD,cAAL,GAAsB,CAAtB;;AAEA,eAAK,IAAIc,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKf,WAAL,CAAiBgB,MAArC,EAA6CD,CAAC,EAA9C,EAAkD;AAC9C,kBAAME,QAAQ,GAAG9B,IAAI,CAAC8B,QAAL,CAAc,KAAKjB,WAAL,CAAiBe,CAAC,GAAG,CAArB,EAAwBG,QAAtC,EAAgD,KAAKlB,WAAL,CAAiBe,CAAjB,EAAoBG,QAApE,CAAjB;AACA,iBAAKjB,cAAL,IAAuBgB,QAAvB;;AACA,iBAAKf,UAAL,CAAgBiB,IAAhB,CAAqB,KAAKlB,cAA1B;AACH;AACJ;AAED;AACJ;AACA;;;AACWmB,QAAAA,IAAI,CAACC,EAAD,EAAmB;AAC1B,cAAI,CAAC,KAAKC,UAAN,IAAoB,KAAKtB,WAAL,CAAiBgB,MAAjB,GAA0B,CAAlD,EAAqD,OAD3B,CAG1B;;AACA,gBAAMO,YAAY,GAAG,KAAKC,eAAL,EAArB,CAJ0B,CAM1B;;AACA,gBAAMC,aAAa,GAAGF,YAAY,GAAGF,EAArC;;AAEA,cAAI,KAAKK,OAAT,EAAkB;AACd,iBAAKvB,gBAAL,IAAyBsB,aAAzB;;AACA,gBAAI,KAAKtB,gBAAL,GAAwB,CAA5B,EAA+B;AAC3B,kBAAI,KAAKwB,IAAT,EAAe;AACX,qBAAKxB,gBAAL,GAAwB,KAAKF,cAAL,GAAsB,KAAKE,gBAAnD;AACH,eAFD,MAEO;AACH,qBAAKA,gBAAL,GAAwB,CAAxB;AACH;AACJ;AACJ,WATD,MASO;AACH,iBAAKA,gBAAL,IAAyBsB,aAAzB;;AACA,gBAAI,KAAKtB,gBAAL,GAAwB,KAAKF,cAAjC,EAAiD;AAC7C,kBAAI,KAAK0B,IAAT,EAAe;AACX,qBAAKxB,gBAAL,GAAwB,KAAKA,gBAAL,GAAwB,KAAKF,cAArD;AACH,eAFD,MAEO;AACH,qBAAKE,gBAAL,GAAwB,KAAKF,cAA7B;AACH;AACJ;AACJ;;AAED,eAAK2B,uBAAL;AACA,eAAKC,cAAL,CAAoBR,EAApB;AACH;AAED;AACJ;AACA;;;AACYO,QAAAA,uBAAuB,GAAG;AAC9B,cAAI,KAAK5B,WAAL,CAAiBgB,MAAjB,KAA4B,CAAhC,EAAmC,OADL,CAG9B;;AACA,cAAI,KAAKb,gBAAL,IAAyB,CAA7B,EAAgC;AAC5B,iBAAKC,kBAAL,GAA0B,CAA1B;AACA,iBAAKC,SAAL,GAAiB,CAAjB;AACA;AACH;;AAED,cAAI,KAAKF,gBAAL,IAAyB,KAAKF,cAAlC,EAAkD;AAC9C,iBAAKG,kBAAL,GAA0B0B,IAAI,CAACC,GAAL,CAAS,CAAT,EAAY,KAAK/B,WAAL,CAAiBgB,MAAjB,GAA0B,CAAtC,CAA1B;AACA,iBAAKX,SAAL,GAAiB,CAAjB;AACA;AACH,WAd6B,CAgB9B;;;AACA,cAAI2B,WAAW,GAAGF,IAAI,CAACC,GAAL,CAAS,CAAT,EAAY,KAAK3B,kBAAL,GAA0B,CAAtC,CAAlB;AACA,cAAI6B,KAAK,GAAG,KAAZ,CAlB8B,CAoB9B;;AACA,eAAK,IAAIlB,CAAC,GAAGiB,WAAb,EAA0BjB,CAAC,GAAG,KAAKb,UAAL,CAAgBc,MAAhB,GAAyB,CAAvD,EAA0DD,CAAC,EAA3D,EAA+D;AAC3D,gBAAI,KAAKZ,gBAAL,IAAyB,KAAKD,UAAL,CAAgBa,CAAhB,CAAzB,IAA+C,KAAKZ,gBAAL,IAAyB,KAAKD,UAAL,CAAgBa,CAAC,GAAG,CAApB,CAA5E,EAAoG;AAChG,mBAAKX,kBAAL,GAA0BW,CAA1B;AACA,oBAAMmB,aAAa,GAAG,KAAKhC,UAAL,CAAgBa,CAAC,GAAG,CAApB,IAAyB,KAAKb,UAAL,CAAgBa,CAAhB,CAA/C;AACA,mBAAKV,SAAL,GAAiB6B,aAAa,GAAG,CAAhB,GAAoB,CAAC,KAAK/B,gBAAL,GAAwB,KAAKD,UAAL,CAAgBa,CAAhB,CAAzB,IAA+CmB,aAAnE,GAAmF,CAApG;AACAD,cAAAA,KAAK,GAAG,IAAR;AACA;AACH;AACJ,WA7B6B,CA+B9B;;;AACA,cAAI,CAACA,KAAL,EAAY;AACR,iBAAK,IAAIlB,CAAC,GAAGe,IAAI,CAACK,GAAL,CAASH,WAAT,EAAsB,KAAK9B,UAAL,CAAgBc,MAAhB,GAAyB,CAA/C,CAAb,EAAgED,CAAC,IAAI,CAArE,EAAwEA,CAAC,EAAzE,EAA6E;AACzE,kBAAI,KAAKZ,gBAAL,IAAyB,KAAKD,UAAL,CAAgBa,CAAhB,CAAzB,IAA+C,KAAKZ,gBAAL,IAAyB,KAAKD,UAAL,CAAgBa,CAAC,GAAG,CAApB,CAA5E,EAAoG;AAChG,qBAAKX,kBAAL,GAA0BW,CAA1B;AACA,sBAAMmB,aAAa,GAAG,KAAKhC,UAAL,CAAgBa,CAAC,GAAG,CAApB,IAAyB,KAAKb,UAAL,CAAgBa,CAAhB,CAA/C;AACA,qBAAKV,SAAL,GAAiB6B,aAAa,GAAG,CAAhB,GAAoB,CAAC,KAAK/B,gBAAL,GAAwB,KAAKD,UAAL,CAAgBa,CAAhB,CAAzB,IAA+CmB,aAAnE,GAAmF,CAApG;AACA;AACH;AACJ;AACJ;AACJ;AAED;AACJ;AACA;;;AACYV,QAAAA,eAAe,GAAW;AAC9B,cAAI,KAAKxB,WAAL,CAAiBgB,MAAjB,KAA4B,CAAhC,EAAmC;AAC/B,mBAAO,KAAKoB,KAAZ,CAD+B,CACZ;AACtB,WAH6B,CAK9B;;;AACA,cAAI,KAAKhC,kBAAL,IAA2B,KAAKJ,WAAL,CAAiBgB,MAAjB,GAA0B,CAAzD,EAA4D;AACxD,mBAAO,KAAKhB,WAAL,CAAiB,KAAKA,WAAL,CAAiBgB,MAAjB,GAA0B,CAA3C,EAA8CoB,KAA9C,GAAsD,KAAK7B,gBAAlE;AACH,WAR6B,CAU9B;;;AACA,gBAAM8B,UAAU,GAAG,KAAKrC,WAAL,CAAiB,KAAKI,kBAAtB,EAA0CgC,KAA7D;AACA,gBAAME,QAAQ,GAAG,KAAKtC,WAAL,CAAiB,KAAKI,kBAAL,GAA0B,CAA3C,EAA8CgC,KAA/D,CAZ8B,CAc9B;;AACA,gBAAMG,iBAAiB,GAAGF,UAAU,GAAG,CAACC,QAAQ,GAAGD,UAAZ,IAA0B,KAAKhC,SAAtE;AACA,iBAAOkC,iBAAiB,GAAG,KAAKhC,gBAAhC;AACH;AAED;AACJ;AACA;;;AACYsB,QAAAA,cAAc,CAACR,EAAD,EAAa;AAC/B,gBAAMH,QAAQ,GAAG,KAAKsB,qBAAL,CAA2B,KAAKrC,gBAAhC,CAAjB,CAD+B,CAG/B;;AACA,cAAI,KAAKsC,SAAL,GAAiB,CAAjB,IAAsB,KAAKC,UAAL,GAAkB,CAA5C,EAA+C;AAC3C,iBAAKpC,SAAL,IAAkBe,EAAlB;AAEA,kBAAMsB,SAAS,GAAG,KAAKC,sBAAL,CAA4B,KAAKzC,gBAAjC,CAAlB;;AACA,gBAAIwC,SAAS,CAACE,SAAV,KAAwB,KAA5B,EAAmC;AAC/B;AACA,oBAAMC,KAAK,GAAG,CAACH,SAAS,CAACI,CAAzB;AACA,oBAAMC,KAAK,GAAGL,SAAS,CAACM,CAAxB,CAH+B,CAK/B;;AACA,oBAAMC,UAAU,GAAGpB,IAAI,CAACqB,GAAL,CAAS,KAAK7C,SAAL,GAAiB,KAAKmC,SAA/B,IAA4C,KAAKC,UAApE;AAEAxB,cAAAA,QAAQ,CAAC+B,CAAT,IAAcH,KAAK,GAAGI,UAAtB;AACAhC,cAAAA,QAAQ,CAAC6B,CAAT,IAAcC,KAAK,GAAGE,UAAtB;AACH;AACJ;;AAED,eAAKE,IAAL,CAAUC,WAAV,CAAsBnC,QAAQ,CAAC+B,CAA/B,EAAkC/B,QAAQ,CAAC6B,CAA3C,EAA8C,CAA9C,EArB+B,CAuB/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;AACA,cAAI,EAAE,KAAKO,uBAAP,IAAkC3D,WAAW,CAAC4D,yBAAlD,EAA6E;AACzE,iBAAKD,uBAAL,GAA+B,CAA/B;AACA,iBAAKE,eAAL;AACH;AACJ;AAED;AACJ;AACA;;;AACYhB,QAAAA,qBAAqB,CAACvB,QAAD,EAAyB;AAClD;AACA,cAAIa,IAAI,CAAC2B,GAAL,CAASxC,QAAQ,GAAG,KAAKd,gBAAzB,IAA6C,KAAjD,EAAwD;AACpD,mBAAO,KAAKuD,kBAAL,EAAP;AACH,WAJiD,CAMlD;;;AACA,cAAI,KAAK1D,WAAL,CAAiBgB,MAAjB,KAA4B,CAAhC,EAAmC,OAAO,IAAI7B,IAAJ,EAAP;AACnC,cAAI8B,QAAQ,IAAI,CAAhB,EAAmB,OAAO,KAAKjB,WAAL,CAAiB,CAAjB,EAAoBkB,QAApB,CAA6ByC,KAA7B,EAAP;AACnB,cAAI1C,QAAQ,IAAI,KAAKhB,cAArB,EAAqC,OAAO,KAAKD,WAAL,CAAiB,KAAKA,WAAL,CAAiBgB,MAAjB,GAA0B,CAA3C,EAA8CE,QAA9C,CAAuDyC,KAAvD,EAAP;;AAErC,eAAK,IAAI5C,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKb,UAAL,CAAgBc,MAApC,EAA4CD,CAAC,EAA7C,EAAiD;AAC7C,gBAAIE,QAAQ,IAAI,KAAKf,UAAL,CAAgBa,CAAhB,CAAhB,EAAoC;AAChC,oBAAM6C,YAAY,GAAG,KAAK1D,UAAL,CAAgBa,CAAC,GAAG,CAApB,CAArB;AACA,oBAAM8C,UAAU,GAAG,KAAK3D,UAAL,CAAgBa,CAAhB,CAAnB;AACA,oBAAMmB,aAAa,GAAG2B,UAAU,GAAGD,YAAnC;AAEA,kBAAI1B,aAAa,KAAK,CAAtB,EAAyB,OAAO,KAAKlC,WAAL,CAAiBe,CAAC,GAAG,CAArB,EAAwBG,QAAxB,CAAiCyC,KAAjC,EAAP;AAEzB,oBAAMG,CAAC,GAAG,CAAC7C,QAAQ,GAAG2C,YAAZ,IAA4B1B,aAAtC;AACA,qBAAO/C,IAAI,CAAC4E,IAAL,CAAU,IAAI5E,IAAJ,EAAV,EAAsB,KAAKa,WAAL,CAAiBe,CAAC,GAAG,CAArB,EAAwBG,QAA9C,EAAwD,KAAKlB,WAAL,CAAiBe,CAAjB,EAAoBG,QAA5E,EAAsF4C,CAAtF,CAAP;AACH;AACJ;;AAED,iBAAO,KAAK9D,WAAL,CAAiB,KAAKA,WAAL,CAAiBgB,MAAjB,GAA0B,CAA3C,EAA8CE,QAA9C,CAAuDyC,KAAvD,EAAP;AACH;AAED;AACJ;AACA;;;AACYD,QAAAA,kBAAkB,GAAS;AAC/B,cAAI,KAAK1D,WAAL,CAAiBgB,MAAjB,KAA4B,CAAhC,EAAmC,OAAO,IAAI7B,IAAJ,EAAP,CADJ,CAG/B;;AACA,cAAI,KAAKiB,kBAAL,IAA2B,KAAKJ,WAAL,CAAiBgB,MAAjB,GAA0B,CAAzD,EAA4D;AACxD,mBAAO,KAAKhB,WAAL,CAAiB,KAAKA,WAAL,CAAiBgB,MAAjB,GAA0B,CAA3C,EAA8CE,QAA9C,CAAuDyC,KAAvD,EAAP;AACH,WAN8B,CAQ/B;;;AACA,gBAAMK,QAAQ,GAAG,KAAKhE,WAAL,CAAiB,KAAKI,kBAAtB,EAA0Cc,QAA3D;AACA,gBAAM+C,MAAM,GAAG,KAAKjE,WAAL,CAAiB,KAAKI,kBAAL,GAA0B,CAA3C,EAA8Cc,QAA7D;AAEA,iBAAO/B,IAAI,CAAC4E,IAAL,CAAU,IAAI5E,IAAJ,EAAV,EAAsB6E,QAAtB,EAAgCC,MAAhC,EAAwC,KAAK5D,SAA7C,CAAP;AACH;AAED;AACJ;AACA;;;AACYuC,QAAAA,sBAAsB,CAAC3B,QAAD,EAAyB;AACnD,gBAAMiD,OAAO,GAAG,CAAhB;AACA,gBAAMC,IAAI,GAAG,KAAK3B,qBAAL,CAA2BvB,QAA3B,CAAb;AACA,gBAAMmD,IAAI,GAAG,KAAK5B,qBAAL,CAA2BvB,QAAQ,GAAGiD,OAAtC,CAAb;AAEA,iBAAO/E,IAAI,CAACkF,QAAL,CAAc,IAAIlF,IAAJ,EAAd,EAA0BiF,IAA1B,EAAgCD,IAAhC,EAAsCG,SAAtC,EAAP;AACH,SA7QqC,CA+QtC;;;AACOC,QAAAA,UAAU,CAACC,OAAD,EAAgC;AAC7C,eAAKlD,UAAL,GAAkBkD,OAAlB;AACA,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACWC,QAAAA,WAAW,CAACC,QAAD,EAAgC;AAC9C,eAAKvE,gBAAL,GAAwB2B,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYD,IAAI,CAACK,GAAL,CAAS,CAAT,EAAYuC,QAAZ,CAAZ,IAAqC,KAAKzE,cAAlE;AACA,eAAK2B,uBAAL,GAF8C,CAEd;;AAChC,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACW+C,QAAAA,WAAW,GAAW;AACzB,iBAAO,KAAK1E,cAAL,GAAsB,CAAtB,GAA0B,KAAKE,gBAAL,GAAwB,KAAKF,cAAvD,GAAwE,CAA/E;AACH;AAED;AACJ;AACA;;;AACW2E,QAAAA,YAAY,GAAgB;AAC/B,eAAKzE,gBAAL,GAAwB,CAAxB;AACA,eAAKC,kBAAL,GAA0B,CAA1B;AACA,eAAKC,SAAL,GAAiB,CAAjB;AACA,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACWwE,QAAAA,SAAS,GAAgB;AAC5B,eAAK1E,gBAAL,GAAwB,KAAKF,cAA7B;AACA,eAAK2B,uBAAL,GAF4B,CAEI;;AAChC,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACWkD,QAAAA,uBAAuB,GAAqB;AAC/C,cAAI,KAAK9E,WAAL,CAAiBgB,MAAjB,KAA4B,CAAhC,EAAmC,OAAO,IAAP,CADY,CAG/C;;AACA,cAAI,KAAKZ,kBAAL,IAA2B,KAAKJ,WAAL,CAAiBgB,MAAjB,GAA0B,CAAzD,EAA4D;AACxD,mBAAO,KAAKhB,WAAL,CAAiB,KAAKA,WAAL,CAAiBgB,MAAjB,GAA0B,CAA3C,CAAP;AACH,WAN8C,CAQ/C;;;AACA,iBAAO,KAAKX,SAAL,GAAiB,GAAjB,GACH,KAAKL,WAAL,CAAiB,KAAKI,kBAAtB,CADG,GAEH,KAAKJ,WAAL,CAAiB,KAAKI,kBAAL,GAA0B,CAA3C,CAFJ;AAGH;AAED;AACJ;AACA;;;AACW2E,QAAAA,kBAAkB,GAAG;AACxB,cAAI,KAAK/E,WAAL,CAAiBgB,MAAjB,KAA4B,CAAhC,EAAmC,OAAO,IAAP;AAEnC,gBAAMgE,YAAY,GAAG,KAAKF,uBAAL,EAArB;AACA,gBAAMvD,YAAY,GAAG,KAAKC,eAAL,EAArB;AAEA,iBAAO;AACHY,YAAAA,KAAK,EAAEb,YADJ;AAEHL,YAAAA,QAAQ,EAAE,KAAKsB,qBAAL,CAA2B,KAAKrC,gBAAhC,CAFP;AAGHwC,YAAAA,SAAS,EAAE,KAAKC,sBAAL,CAA4B,KAAKzC,gBAAjC,CAHR;AAIH8E,YAAAA,SAAS,EAAED,YAJR;AAKHN,YAAAA,QAAQ,EAAE,KAAKC,WAAL,EALP;AAMH1D,YAAAA,QAAQ,EAAE,KAAKd,gBANZ;AAOH+E,YAAAA,aAAa,EAAE,KAAKjF;AAPjB,WAAP;AASH;;AAOMkF,QAAAA,kBAAkB,CAACC,UAAD,EAAkC;AACvD,eAAK7E,gBAAL,GAAwBuB,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYqD,UAAZ,CAAxB;AACA,iBAAO,IAAP;AACH;;AAEMC,QAAAA,kBAAkB,GAAW;AAChC,iBAAO,KAAK9E,gBAAZ;AACH;;AAzWqC,O;;;;;iBAGD,I;;;;;;;iBAGd,K;;;;;;;iBAGG,K;;;;;;;iBAGC,C;;;;;;;iBAGC,C", "sourcesContent": ["import { _decorator, Component, Enum, misc, Node, UITransform, Vec2, Vec3, JsonAsset } from 'cc';\nimport { BulletSystem } from '../bullet/BulletSystem';\nimport { MoveBase, eMoveEvent } from './IMovable';\nimport Entity from '../ui/base/Entity';\nimport { PathData, PathPoint } from '../data/PathData';\n\nconst { degreesToRadians, radiansToDegrees } = misc;\nconst { ccclass, property } = _decorator;\n\n@ccclass('PathMovable')\nexport class PathMovable extends MoveBase {\n\n    @property({ type: JsonAsset, displayName: \"路径数据\" })\n    public pathAsset: JsonAsset | null = null;\n\n    @property({ displayName: \"循环移动\" })\n    public loop: boolean = false;\n\n    @property({ displayName: \"反向移动\" })\n    public reverse: boolean = false;\n\n    @property({ displayName: \"振荡偏移速度\", tooltip: \"控制倾斜振荡的频率\" })\n    public tiltSpeed: number = 0;\n\n    @property({ displayName: \"振荡偏移幅度\", tooltip: \"控制倾斜振荡的幅度\" })\n    public tiltOffset: number = 0;\n\n    // 路径相关数据\n    private _pathData: PathData | null = null;\n    private _subdivided: PathPoint[] = []; // 细分后的路径点（包含完整信息）\n    private _totalDistance: number = 0;\n    private _distances: number[] = [];\n\n    // 移动状态\n    private _currentDistance: number = 0;\n    private _currentPointIndex: number = 0; // 当前所在的细分点索引\n    private _segmentT: number = 0; // 在当前段内的插值参数 [0,1]\n    private _tiltTime: number = 0;\n\n    onLoad() {\n        super.onLoad();\n        this.loadPathData();\n    }\n\n    /**\n     * 加载路径数据（使用新的细分点方法）\n     */\n    private loadPathData() {\n        if (!this.pathAsset) return;\n\n        // 创建PathData实例并加载数据\n        this._pathData = PathData.fromJSON(this.pathAsset.json);\n\n        // 使用新的细分点方法，直接获取包含完整信息的PathPoint数组\n        this._subdivided = this._pathData.getSubdividedPoints();\n\n        // 计算距离信息\n        this.calculateDistances();\n    }\n\n    /**\n     * 计算距离信息\n     */\n    private calculateDistances() {\n        this._distances = [0];\n        this._totalDistance = 0;\n\n        for (let i = 1; i < this._subdivided.length; i++) {\n            const distance = Vec2.distance(this._subdivided[i - 1].position, this._subdivided[i].position);\n            this._totalDistance += distance;\n            this._distances.push(this._totalDistance);\n        }\n    }\n\n    /**\n     * 主要的移动更新逻辑\n     */\n    public tick(dt: number): void {\n        if (!this._isMovable || this._subdivided.length < 2) return;\n\n        // 获取当前位置的速度（从细分点直接获取）\n        const currentSpeed = this.getCurrentSpeed();\n\n        // 更新沿路径的距离\n        const deltaDistance = currentSpeed * dt;\n\n        if (this.reverse) {\n            this._currentDistance -= deltaDistance;\n            if (this._currentDistance < 0) {\n                if (this.loop) {\n                    this._currentDistance = this._totalDistance + this._currentDistance;\n                } else {\n                    this._currentDistance = 0;\n                }\n            }\n        } else {\n            this._currentDistance += deltaDistance;\n            if (this._currentDistance > this._totalDistance) {\n                if (this.loop) {\n                    this._currentDistance = this._currentDistance - this._totalDistance;\n                } else {\n                    this._currentDistance = this._totalDistance;\n                }\n            }\n        }\n\n        this.updateCurrentPointIndex();\n        this.updatePosition(dt);\n    }\n\n    /**\n     * 根据当前距离更新点索引和段内插值参数\n     */\n    private updateCurrentPointIndex() {\n        if (this._subdivided.length === 0) return;\n\n        // 边界情况处理\n        if (this._currentDistance <= 0) {\n            this._currentPointIndex = 0;\n            this._segmentT = 0;\n            return;\n        }\n\n        if (this._currentDistance >= this._totalDistance) {\n            this._currentPointIndex = Math.max(0, this._subdivided.length - 2);\n            this._segmentT = 1;\n            return;\n        }\n\n        // 从当前索引开始搜索，利用时间连续性\n        let searchStart = Math.max(0, this._currentPointIndex - 1);\n        let found = false;\n\n        // 向前搜索\n        for (let i = searchStart; i < this._distances.length - 1; i++) {\n            if (this._currentDistance >= this._distances[i] && this._currentDistance <= this._distances[i + 1]) {\n                this._currentPointIndex = i;\n                const segmentLength = this._distances[i + 1] - this._distances[i];\n                this._segmentT = segmentLength > 0 ? (this._currentDistance - this._distances[i]) / segmentLength : 0;\n                found = true;\n                break;\n            }\n        }\n\n        // 如果向前搜索没找到，向后搜索（处理反向移动）\n        if (!found) {\n            for (let i = Math.min(searchStart, this._distances.length - 2); i >= 0; i--) {\n                if (this._currentDistance >= this._distances[i] && this._currentDistance <= this._distances[i + 1]) {\n                    this._currentPointIndex = i;\n                    const segmentLength = this._distances[i + 1] - this._distances[i];\n                    this._segmentT = segmentLength > 0 ? (this._currentDistance - this._distances[i]) / segmentLength : 0;\n                    break;\n                }\n            }\n        }\n    }\n\n    /**\n     * 获取当前速度（使用索引优化）\n     */\n    private getCurrentSpeed(): number {\n        if (this._subdivided.length === 0) {\n            return this.speed; // 回退到基础速度\n        }\n\n        // 边界情况\n        if (this._currentPointIndex >= this._subdivided.length - 1) {\n            return this._subdivided[this._subdivided.length - 1].speed * this._speedMultiplier;\n        }\n\n        // 使用预计算的索引和插值参数\n        const startSpeed = this._subdivided[this._currentPointIndex].speed;\n        const endSpeed = this._subdivided[this._currentPointIndex + 1].speed;\n\n        // 在两个细分点之间插值速度\n        const interpolatedSpeed = startSpeed + (endSpeed - startSpeed) * this._segmentT;\n        return interpolatedSpeed * this._speedMultiplier;\n    }\n\n    /**\n     * 更新节点位置和朝向\n     */\n    private updatePosition(dt: number) {\n        const position = this.getPositionAtDistance(this._currentDistance);\n        \n        // 应用倾斜偏移\n        if (this.tiltSpeed > 0 && this.tiltOffset > 0) {\n            this._tiltTime += dt;\n            \n            const direction = this.getDirectionAtDistance(this._currentDistance);\n            if (direction.lengthSqr() > 0.001) {\n                // 计算垂直于移动方向的向量\n                const perpX = -direction.y;\n                const perpY = direction.x;\n                \n                // 计算倾斜偏移\n                const tiltAmount = Math.sin(this._tiltTime * this.tiltSpeed) * this.tiltOffset;\n                \n                position.x += perpX * tiltAmount;\n                position.y += perpY * tiltAmount;\n            }\n        }\n\n        this.node.setPosition(position.x, position.y, 0);\n\n        // // 更新朝向\n        // if (this.isFacingMoveDir) {\n        //     const direction = this.getDirectionAtDistance(this._currentDistance);\n        //     if (direction.lengthSqr() > 0.001) {\n        //         const angle = Math.atan2(direction.y, direction.x) * 180 / Math.PI;\n        //         const finalAngle = angle + this.defaultFacing;\n        //         this.node.setRotationFromEuler(0, 0, finalAngle);\n        //     }\n        // }\n\n        // 可见性检查\n        if (++this._visibilityCheckCounter >= PathMovable.VISIBILITY_CHECK_INTERVAL) {\n            this._visibilityCheckCounter = 0;\n            this.checkVisibility();\n        }\n    }\n\n    /**\n     * 根据距离获取位置（优化版本，主要用于当前位置）\n     */\n    private getPositionAtDistance(distance: number): Vec2 {\n        // 如果查询的是当前距离，使用优化的索引方法\n        if (Math.abs(distance - this._currentDistance) < 0.001) {\n            return this.getCurrentPosition();\n        }\n\n        // 其他距离仍使用原来的方法（用于方向计算等）\n        if (this._subdivided.length === 0) return new Vec2();\n        if (distance <= 0) return this._subdivided[0].position.clone();\n        if (distance >= this._totalDistance) return this._subdivided[this._subdivided.length - 1].position.clone();\n\n        for (let i = 1; i < this._distances.length; i++) {\n            if (distance <= this._distances[i]) {\n                const segmentStart = this._distances[i - 1];\n                const segmentEnd = this._distances[i];\n                const segmentLength = segmentEnd - segmentStart;\n\n                if (segmentLength === 0) return this._subdivided[i - 1].position.clone();\n\n                const t = (distance - segmentStart) / segmentLength;\n                return Vec2.lerp(new Vec2(), this._subdivided[i - 1].position, this._subdivided[i].position, t);\n            }\n        }\n\n        return this._subdivided[this._subdivided.length - 1].position.clone();\n    }\n\n    /**\n     * 获取当前位置（使用索引优化）\n     */\n    private getCurrentPosition(): Vec2 {\n        if (this._subdivided.length === 0) return new Vec2();\n\n        // 边界情况\n        if (this._currentPointIndex >= this._subdivided.length - 1) {\n            return this._subdivided[this._subdivided.length - 1].position.clone();\n        }\n\n        // 使用预计算的索引和插值参数\n        const startPos = this._subdivided[this._currentPointIndex].position;\n        const endPos = this._subdivided[this._currentPointIndex + 1].position;\n\n        return Vec2.lerp(new Vec2(), startPos, endPos, this._segmentT);\n    }\n\n    /**\n     * 根据距离获取移动方向\n     */\n    private getDirectionAtDistance(distance: number): Vec2 {\n        const epsilon = 1;\n        const pos1 = this.getPositionAtDistance(distance);\n        const pos2 = this.getPositionAtDistance(distance + epsilon);\n\n        return Vec2.subtract(new Vec2(), pos2, pos1).normalize();\n    }\n\n    // 公共API方法\n    public setMovable(movable: boolean): PathMovable {\n        this._isMovable = movable;\n        return this;\n    }\n\n    /**\n     * 设置路径进度 [0-1]\n     */\n    public setProgress(progress: number): PathMovable {\n        this._currentDistance = Math.max(0, Math.min(1, progress)) * this._totalDistance;\n        this.updateCurrentPointIndex(); // 更新索引\n        return this;\n    }\n\n    /**\n     * 获取当前进度 [0-1]\n     */\n    public getProgress(): number {\n        return this._totalDistance > 0 ? this._currentDistance / this._totalDistance : 0;\n    }\n\n    /**\n     * 重置到路径起点\n     */\n    public resetToStart(): PathMovable {\n        this._currentDistance = 0;\n        this._currentPointIndex = 0;\n        this._segmentT = 0;\n        return this;\n    }\n\n    /**\n     * 移动到路径终点\n     */\n    public moveToEnd(): PathMovable {\n        this._currentDistance = this._totalDistance;\n        this.updateCurrentPointIndex(); // 更新索引\n        return this;\n    }\n\n    /**\n     * 获取当前位置对应的路径点数据（使用索引优化）\n     */\n    public getCurrentPathPointData(): PathPoint | null {\n        if (this._subdivided.length === 0) return null;\n\n        // 边界情况\n        if (this._currentPointIndex >= this._subdivided.length - 1) {\n            return this._subdivided[this._subdivided.length - 1];\n        }\n\n        // 使用预计算的索引，返回更接近的那个点\n        return this._segmentT < 0.5 ?\n            this._subdivided[this._currentPointIndex] :\n            this._subdivided[this._currentPointIndex + 1];\n    }\n\n    /**\n     * 获取当前位置的详细路径信息（包括速度、朝向等）\n     */\n    public getCurrentPathInfo() {\n        if (this._subdivided.length === 0) return null;\n\n        const currentPoint = this.getCurrentPathPointData();\n        const currentSpeed = this.getCurrentSpeed();\n\n        return {\n            speed: currentSpeed,\n            position: this.getPositionAtDistance(this._currentDistance),\n            direction: this.getDirectionAtDistance(this._currentDistance),\n            pathPoint: currentPoint,\n            progress: this.getProgress(),\n            distance: this._currentDistance,\n            totalDistance: this._totalDistance\n        };\n    }\n\n    /**\n     * 设置速度倍率（用于临时调整速度）\n     */\n    private _speedMultiplier: number = 1.0;\n\n    public setSpeedMultiplier(multiplier: number): PathMovable {\n        this._speedMultiplier = Math.max(0, multiplier);\n        return this;\n    }\n\n    public getSpeedMultiplier(): number {\n        return this._speedMultiplier;\n    }\n}\n"]}