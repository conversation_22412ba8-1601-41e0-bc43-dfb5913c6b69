{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/move/PathMove.ts"], "names": ["_decorator", "misc", "Vec3", "JsonAsset", "MoveBase", "PathData", "degreesToRadians", "radiansToDegrees", "ccclass", "property", "executeInEditMode", "PathMove", "type", "displayName", "tooltip", "_pathAsset", "_pathData", "_subdivided", "_currentPosition", "_currentPointIndex", "_nextPointIndex", "_remainDistance", "_tiltTime", "_stayTimer", "_stayPointIndex", "_updateInEditor", "pathAsset", "value", "set<PERSON>ath", "fromJSON", "json", "onFocusInEditor", "_isMovable", "onLostFocusInEditor", "resetToStart", "update", "dt", "tick", "pathData", "getSubdividedPoints", "tickMovement", "tickTilting", "moveToNextPoint", "v0", "speed", "s", "acceleration", "angleRad", "speedAngle", "deltaX", "Math", "cos", "deltaY", "sin", "x", "y", "node", "setPosition", "onReachPoint", "tiltSpeed", "tiltOffset", "PI", "perpX", "perpY", "tiltAmount", "finalX", "finalY", "pointIndex", "currentPoint", "getPathPoint", "console", "log", "stayDuration", "nextIndex", "length", "loop", "setNext", "pathPointIndex", "nextPoint", "dirX", "dirY", "sqrt", "atan2", "v1", "setMovable", "movable", "isStaying", "getRemainingStayTime"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAuBC,MAAAA,I,OAAAA,I;AAAqCC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,S,OAAAA,S;;AAElEC,MAAAA,Q,iBAAAA,Q;;AAEAC,MAAAA,Q,iBAAAA,Q;;;;;;;;;OAEH;AAAEC,QAAAA,gBAAF;AAAoBC,QAAAA;AAApB,O,GAAyCN,I;OACzC;AAAEO,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CV,U;;0BAIpCW,Q,WAFZH,OAAO,CAAC,UAAD,C,UACPE,iBAAiB,E,UAGbD,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAET,SAAR;AAAmBU,QAAAA,WAAW,EAAE;AAAhC,OAAD,C,UAWRJ,QAAQ,CAAC;AAAEI,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAGRJ,QAAQ,CAAC;AAAEI,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAGRJ,QAAQ,CAAC;AAAEI,QAAAA,WAAW,EAAE,QAAf;AAAyBC,QAAAA,OAAO,EAAE;AAAlC,OAAD,C,UAGRL,QAAQ,CAAC;AAAEI,QAAAA,WAAW,EAAE,QAAf;AAAyBC,QAAAA,OAAO,EAAE;AAAlC,OAAD,C,0CAxBb,MAEaH,QAFb;AAAA;AAAA,gCAEuC;AAAA;AAAA;AAAA,eAC5BI,UAD4B,GACG,IADH;;AAAA;;AAAA;;AAAA;;AAAA;;AAyBnC;AAzBmC,eA0B3BC,SA1B2B,GA0BE,IA1BF;AAAA,eA2B3BC,WA3B2B,GA2BA,EA3BA;AA2BI;AAEvC;AA7BmC,eA8B3BC,gBA9B2B,GA8BF,IAAIhB,IAAJ,EA9BE;AAAA,eA+B3BiB,kBA/B2B,GA+BE,CA/BF;AA+BK;AA/BL,eAgC3BC,eAhC2B,GAgCD,CAhCC;AAAA,eAiC3BC,eAjC2B,GAiCD,CAjCC;AAiCK;AAjCL,eAmC3BC,SAnC2B,GAmCP,CAnCO;AAqCnC;AArCmC,eAsC3BC,UAtC2B,GAsCN,CAtCM;AAsCH;AAtCG,eAuC3BC,eAvC2B,GAuCD,CAAC,CAvCA;AAuCG;AAvCH,eAyC3BC,eAzC2B,GAyCA,KAzCA;AAAA;;AAGf,YAATC,SAAS,GAAqB;AACrC,iBAAO,KAAKX,UAAZ;AACH;;AACmB,YAATW,SAAS,CAACC,KAAD,EAAmB;AACnC,eAAKZ,UAAL,GAAkBY,KAAlB;;AACA,cAAIA,KAAJ,EAAW;AACP,iBAAKC,OAAL,CAAa;AAAA;AAAA,sCAASC,QAAT,CAAkBF,KAAK,CAACG,IAAxB,CAAb;AACH;AACJ;;AA+BMC,QAAAA,eAAe,GAAG;AACrB,eAAKN,eAAL,GAAuB,IAAvB;AACA,eAAKO,UAAL,GAAkB,IAAlB;AACH;;AAEMC,QAAAA,mBAAmB,GAAG;AACzB,eAAKR,eAAL,GAAuB,KAAvB;AACA,eAAKO,UAAL,GAAkB,KAAlB;AACA,eAAKE,YAAL;AACH;;AAEMC,QAAAA,MAAM,CAACC,EAAD,EAAa;AACtB,cAAI,KAAKX,eAAT,EAA0B;AACtB,iBAAKY,IAAL,CAAUD,EAAV;AACH;AACJ;AAED;AACJ;AACA;;;AACWR,QAAAA,OAAO,CAACU,QAAD,EAAqB;AAC/B,eAAKtB,SAAL,GAAiBsB,QAAjB,CAD+B,CAE/B;;AACA,eAAKrB,WAAL,GAAmB,KAAKD,SAAL,CAAeuB,mBAAf,EAAnB;AACA,eAAKL,YAAL;AACH;AAED;AACJ;AACA;;;AACWG,QAAAA,IAAI,CAACD,EAAD,EAAmB;AAC1B,cAAI,CAAC,KAAKJ,UAAV,EAAsB;;AAEtB,cAAI,KAAKZ,eAAL,KAAyB,KAAKD,kBAAlC,EAAsD;AAClD,iBAAKqB,YAAL,CAAkBJ,EAAlB;AACH;;AAED,eAAKK,WAAL,CAAiBL,EAAjB;AACH;;AAEOI,QAAAA,YAAY,CAACJ,EAAD,EAAa;AAC7B;AACA,cAAI,KAAKb,UAAL,GAAkB,CAAtB,EAAyB;AACrB,iBAAKA,UAAL,IAAmBa,EAAnB;;AACA,gBAAI,KAAKb,UAAL,IAAmB,CAAvB,EAA0B;AACtB,mBAAKC,eAAL,GAAuB,CAAC,CAAxB,CADsB,CAEtB;;AACA,mBAAKkB,eAAL;AACH;;AACD;AACH,WAV4B,CAY7B;;;AACA,gBAAMC,EAAE,GAAG,KAAKC,KAAhB,CAb6B,CAc7B;;AACA,gBAAMC,CAAC,GAAGF,EAAE,GAAGP,EAAL,GAAU,MAAM,KAAKU,YAAX,GAA0BV,EAA1B,GAA+BA,EAAnD;AACA,eAAKQ,KAAL,IAAc,KAAKE,YAAL,GAAoBV,EAAlC,CAhB6B,CAkB7B;AAEA;;AACA,gBAAMW,QAAQ,GAAG,KAAKC,UAAtB;AACA,gBAAMC,MAAM,GAAGC,IAAI,CAACC,GAAL,CAASJ,QAAT,IAAqBF,CAApC;AACA,gBAAMO,MAAM,GAAGF,IAAI,CAACG,GAAL,CAASN,QAAT,IAAqBF,CAApC,CAvB6B,CAyB7B;;AACA,eAAK3B,gBAAL,CAAsBoC,CAAtB,IAA2BL,MAA3B;AACA,eAAK/B,gBAAL,CAAsBqC,CAAtB,IAA2BH,MAA3B,CA3B6B,CA6B7B;;AACA,eAAKI,IAAL,CAAUC,WAAV,CAAsB,KAAKvC,gBAA3B,EA9B6B,CAgC7B;;AACA,cAAI,KAAKG,eAAL,GAAuB,CAA3B,EAA8B;AAC1B,iBAAKA,eAAL,IAAwBwB,CAAxB;;AACA,gBAAI,KAAKxB,eAAL,IAAwB,CAA5B,EAA+B;AAC3B,mBAAKqC,YAAL,CAAkB,KAAKtC,eAAvB;AACH;AACJ;AACJ;;AAEOqB,QAAAA,WAAW,CAACL,EAAD,EAAa;AAC5B;AACA,cAAI,KAAKuB,SAAL,GAAiB,CAAjB,IAAsB,KAAKC,UAAL,GAAkB,CAA5C,EAA+C;AAC3C,iBAAKtC,SAAL,IAAkBc,EAAlB,CAD2C,CAG3C;;AACA,kBAAMW,QAAQ,GAAG,KAAKC,UAAL,GAAkBE,IAAI,CAACW,EAAvB,GAA4B,GAA7C;AACA,kBAAMC,KAAK,GAAG,CAACZ,IAAI,CAACG,GAAL,CAASN,QAAT,CAAf;AACA,kBAAMgB,KAAK,GAAGb,IAAI,CAACC,GAAL,CAASJ,QAAT,CAAd,CAN2C,CAQ3C;;AACA,kBAAMiB,UAAU,GAAGd,IAAI,CAACG,GAAL,CAAS,KAAK/B,SAAL,GAAiB,KAAKqC,SAA/B,IAA4C,KAAKC,UAApE,CAT2C,CAW3C;;AACA,kBAAMK,MAAM,GAAG,KAAK/C,gBAAL,CAAsBoC,CAAtB,GAA0BQ,KAAK,GAAGE,UAAjD;AACA,kBAAME,MAAM,GAAG,KAAKhD,gBAAL,CAAsBqC,CAAtB,GAA0BQ,KAAK,GAAGC,UAAjD;AACA,iBAAKR,IAAL,CAAUC,WAAV,CAAsBQ,MAAtB,EAA8BC,MAA9B,EAAsC,CAAtC;AACH;AACJ;;AAEOR,QAAAA,YAAY,CAACS,UAAD,EAAqB;AACrC;AACA,eAAKhD,kBAAL,GAA0BgD,UAA1B,CAFqC,CAGrC;;AACA,gBAAMC,YAAY,GAAG,KAAKC,YAAL,CAAkBF,UAAlB,CAArB;;AACA,cAAIC,YAAJ,EAAkB;AACd,iBAAKlD,gBAAL,CAAsBoC,CAAtB,GAA0Bc,YAAY,CAACd,CAAvC;AACA,iBAAKpC,gBAAL,CAAsBqC,CAAtB,GAA0Ba,YAAY,CAACb,CAAvC;AACA,iBAAKC,IAAL,CAAUC,WAAV,CAAsB,KAAKvC,gBAA3B;AAEAoD,YAAAA,OAAO,CAACC,GAAR,CAAa,OAAMJ,UAAW,WAAUC,YAAY,CAACI,YAAa,IAAlE;;AACA,gBAAIJ,YAAY,CAACI,YAAb,GAA4B,CAAhC,EAAmC;AAC/B,mBAAKjD,UAAL,GAAkB6C,YAAY,CAACI,YAAb,GAA4B,MAA9C;AACA,mBAAKhD,eAAL,GAAuB2C,UAAvB;AACA;AACH;AACJ,WAhBoC,CAkBrC;;;AACA,eAAKzB,eAAL;AACH;;AAEOA,QAAAA,eAAe,GAAG;AACtB,gBAAM+B,SAAS,GAAG,KAAKtD,kBAAL,GAA0B,CAA5C;;AAEA,cAAIsD,SAAS,IAAI,KAAKxD,WAAL,CAAiByD,MAAlC,EAA0C;AACtC;AACA,gBAAI,KAAKC,IAAT,EAAe;AACX;AACA,mBAAKC,OAAL,CAAa,CAAb;AACH,aAHD,MAGO;AACH;AACA,mBAAKxD,eAAL,GAAuB,KAAKD,kBAA5B;AACH;AACJ,WATD,MASO;AACH;AACA,iBAAKyD,OAAL,CAAaH,SAAb;AACH;AACJ;;AAEOG,QAAAA,OAAO,CAACC,cAAD,EAAyB;AACpC,eAAKzD,eAAL,GAAuByD,cAAvB;AAEA,gBAAMT,YAAY,GAAG,KAAKC,YAAL,CAAkB,KAAKlD,kBAAvB,CAArB;AACA,gBAAM2D,SAAS,GAAG,KAAKT,YAAL,CAAkB,KAAKjD,eAAvB,CAAlB;;AACA,cAAIgD,YAAY,IAAIU,SAApB,EAA+B;AAC3B,kBAAMC,IAAI,GAAGD,SAAS,CAACxB,CAAV,GAAcc,YAAY,CAACd,CAAxC;AACA,kBAAM0B,IAAI,GAAGF,SAAS,CAACvB,CAAV,GAAca,YAAY,CAACb,CAAxC;AACA,iBAAKlC,eAAL,GAAuB6B,IAAI,CAAC+B,IAAL,CAAUF,IAAI,GAAGA,IAAP,GAAcC,IAAI,GAAGA,IAA/B,CAAvB;;AAEA,gBAAI,KAAK3D,eAAL,GAAuB,CAA3B,EAA8B;AAC1B;AACA,mBAAK2B,UAAL,GAAkBE,IAAI,CAACgC,KAAL,CAAWF,IAAX,EAAiBD,IAAjB,CAAlB,CAF0B,CAI1B;AACA;;AACA,oBAAMpC,EAAE,GAAGyB,YAAY,CAACxB,KAAxB;AACA,oBAAMuC,EAAE,GAAGL,SAAS,CAAClC,KAArB;AACA,mBAAKE,YAAL,GAAoB,CAACqC,EAAE,GAAGA,EAAL,GAAUxC,EAAE,GAAGA,EAAhB,KAAuB,IAAI,KAAKtB,eAAhC,CAApB,CAR0B,CAU1B;;AACA,mBAAKuB,KAAL,GAAaD,EAAb,CAX0B,CAa1B;AACH;AACJ;AACJ;;AAEO0B,QAAAA,YAAY,CAACQ,cAAD,EAA2C;AAC3D,cAAIA,cAAc,GAAG,CAAjB,IAAsBA,cAAc,IAAI,KAAK5D,WAAL,CAAiByD,MAA7D,EAAqE;AACjE,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAKzD,WAAL,CAAiB4D,cAAjB,CAAP;AACH,SAxNkC,CA0NnC;;;AACOO,QAAAA,UAAU,CAACC,OAAD,EAA6B;AAC1C,eAAKrD,UAAL,GAAkBqD,OAAlB;AACA,iBAAO,IAAP;AACH;;AAEOnD,QAAAA,YAAY,GAAG;AACnB,eAAKf,kBAAL,GAA0B,CAA1B;AACA,eAAKC,eAAL,GAAuB,CAAvB;AACA,eAAKG,UAAL,GAAkB,CAAlB;AACA,eAAKC,eAAL,GAAuB,CAAC,CAAxB;AACA,eAAKF,SAAL,GAAiB,CAAjB;AAEA,eAAKoC,YAAL,CAAkB,CAAlB;AACH;;AAEM4B,QAAAA,SAAS,GAAY;AACxB,iBAAO,KAAK/D,UAAL,GAAkB,CAAzB;AACH;;AAEMgE,QAAAA,oBAAoB,GAAW;AAClC,iBAAO,KAAKhE,UAAZ;AACH;;AAhPkC,O;;;;;iBAcZ,K;;;;;;;iBAGG,K;;;;;;;iBAGC,C;;;;;;;iBAGC,C", "sourcesContent": ["import { _decorator, Component, misc, Enum, Node, UITransform, Vec2, Vec3, JsonAsset } from 'cc';\nimport { BulletSystem } from '../bullet/BulletSystem';\nimport { MoveBase, eMoveEvent } from './IMovable';\nimport Entity from '../ui/base/Entity';\nimport { PathData, PathPoint } from '../data/PathData';\n\nconst { degreesToRadians, radiansToDegrees } = misc;\nconst { ccclass, property, executeInEditMode } = _decorator;\n\n@ccclass('PathMove')\n@executeInEditMode()\nexport class PathMove extends MoveBase {\n    public _pathAsset: JsonAsset | null = null;\n    @property({ type: JsonAsset, displayName: \"路径数据(预览用)\" })\n    public get pathAsset(): JsonAsset | null {\n        return this._pathAsset;\n    }\n    public set pathAsset(value: JsonAsset) {\n        this._pathAsset = value;\n        if (value) {\n            this.setPath(PathData.fromJSON(value.json));\n        }\n    }\n\n    @property({ displayName: \"循环移动\" })\n    public loop: boolean = false;\n\n    @property({ displayName: \"反向移动\" })\n    public reverse: boolean = false;\n\n    @property({ displayName: \"振荡偏移速度\", tooltip: \"控制倾斜振荡的频率\" })\n    public tiltSpeed: number = 0;\n\n    @property({ displayName: \"振荡偏移幅度\", tooltip: \"控制倾斜振荡的幅度\" })\n    public tiltOffset: number = 0;\n\n    // 路径相关数据\n    private _pathData: PathData | null = null;\n    private _subdivided: PathPoint[] = []; // 细分后的路径点（包含完整信息）\n\n    // 移动状态\n    private _currentPosition: Vec3 = new Vec3();\n    private _currentPointIndex: number = 0; // 当前所在的细分点索引\n    private _nextPointIndex: number = 0;\n    private _remainDistance: number = 0;    // 距离下一个点的剩余距离\n\n    private _tiltTime: number = 0;\n\n    // 停留状态\n    private _stayTimer: number = 0; // 停留计时器（秒）\n    private _stayPointIndex: number = -1; // 停留的点索引\n\n    private _updateInEditor: boolean = false;\n    public onFocusInEditor() {\n        this._updateInEditor = true;\n        this._isMovable = true;\n    }\n\n    public onLostFocusInEditor() {\n        this._updateInEditor = false;\n        this._isMovable = false;\n        this.resetToStart();\n    }\n\n    public update(dt: number) {\n        if (this._updateInEditor) {\n            this.tick(dt);\n        }\n    }\n\n    /**\n     * 加载路径数据（使用新的细分点方法）\n     */\n    public setPath(pathData: PathData) {\n        this._pathData = pathData;\n        // 使用新的细分点方法，直接获取包含完整信息的PathPoint数组\n        this._subdivided = this._pathData.getSubdividedPoints();\n        this.resetToStart();\n    }\n\n    /**\n     * 主要的移动更新逻辑\n     */\n    public tick(dt: number): void {\n        if (!this._isMovable) return;\n        \n        if (this._nextPointIndex !== this._currentPointIndex) {\n            this.tickMovement(dt);\n        }\n\n        this.tickTilting(dt);\n    }\n\n    private tickMovement(dt: number) {\n        // 处理停留逻辑\n        if (this._stayTimer > 0) {\n            this._stayTimer -= dt;\n            if (this._stayTimer <= 0) {\n                this._stayPointIndex = -1;\n                // 停留结束，继续移动到下一个点\n                this.moveToNextPoint();\n            }\n            return;\n        }\n\n        // 使用匀加速直线运动更新位置\n        const v0 = this.speed;\n        // s = v0*t + 0.5*a*t^2\n        const s = v0 * dt + 0.5 * this.acceleration * dt * dt;\n        this.speed += this.acceleration * dt;\n\n        // console.log(`v0: ${v0.toFixed(2)}, a: ${this.acceleration.toFixed(2)}, s: ${s.toFixed(2)}`);\n\n        // 计算移动向量\n        const angleRad = this.speedAngle;\n        const deltaX = Math.cos(angleRad) * s;\n        const deltaY = Math.sin(angleRad) * s;\n\n        // 更新位置\n        this._currentPosition.x += deltaX;\n        this._currentPosition.y += deltaY;\n\n        // 设置节点位置\n        this.node.setPosition(this._currentPosition);\n\n        // 检查是否到达目标点\n        if (this._remainDistance > 0) {\n            this._remainDistance -= s;\n            if (this._remainDistance <= 0) {\n                this.onReachPoint(this._nextPointIndex);\n            }\n        }\n    }\n\n    private tickTilting(dt: number) {\n        // 应用倾斜偏移\n        if (this.tiltSpeed > 0 && this.tiltOffset > 0) {\n            this._tiltTime += dt;\n\n            // 计算垂直于移动方向的向量\n            const angleRad = this.speedAngle * Math.PI / 180;\n            const perpX = -Math.sin(angleRad);\n            const perpY = Math.cos(angleRad);\n\n            // 计算倾斜偏移\n            const tiltAmount = Math.sin(this._tiltTime * this.tiltSpeed) * this.tiltOffset;\n\n            // 应用倾斜偏移到节点位置\n            const finalX = this._currentPosition.x + perpX * tiltAmount;\n            const finalY = this._currentPosition.y + perpY * tiltAmount;\n            this.node.setPosition(finalX, finalY, 0);\n        }\n    }\n\n    private onReachPoint(pointIndex: number) {\n        // 更新当前点索引\n        this._currentPointIndex = pointIndex;\n        // 检查是否需要停留\n        const currentPoint = this.getPathPoint(pointIndex);\n        if (currentPoint) { \n            this._currentPosition.x = currentPoint.x;\n            this._currentPosition.y = currentPoint.y;\n            this.node.setPosition(this._currentPosition);\n\n            console.log(`到达点 ${pointIndex}, 停留时间: ${currentPoint.stayDuration}ms`);\n            if (currentPoint.stayDuration > 0) {\n                this._stayTimer = currentPoint.stayDuration / 1000.0;\n                this._stayPointIndex = pointIndex;\n                return;\n            }\n        }\n\n        // 继续移动到下一个点\n        this.moveToNextPoint();\n    }\n\n    private moveToNextPoint() {\n        const nextIndex = this._currentPointIndex + 1;\n\n        if (nextIndex >= this._subdivided.length) {\n            // 到达路径终点\n            if (this.loop) {\n                // 循环模式，回到起点\n                this.setNext(0);\n            } else {\n                // 停止移动\n                this._nextPointIndex = this._currentPointIndex;\n            }\n        } else {\n            // 移动到下一个点\n            this.setNext(nextIndex);\n        }\n    }\n\n    private setNext(pathPointIndex: number) {\n        this._nextPointIndex = pathPointIndex;\n\n        const currentPoint = this.getPathPoint(this._currentPointIndex);\n        const nextPoint = this.getPathPoint(this._nextPointIndex);\n        if (currentPoint && nextPoint) {\n            const dirX = nextPoint.x - currentPoint.x;\n            const dirY = nextPoint.y - currentPoint.y;\n            this._remainDistance = Math.sqrt(dirX * dirX + dirY * dirY);\n\n            if (this._remainDistance > 1) {\n                // 计算移动角度\n                this.speedAngle = Math.atan2(dirY, dirX);\n\n                // 计算加速度：使用匀加速直线运动公式 v1^2 = v0^2 + 2*a*x\n                // 解出 a = (v1^2 - v0^2) / (2*x)\n                const v0 = currentPoint.speed;\n                const v1 = nextPoint.speed;\n                this.acceleration = (v1 * v1 - v0 * v0) / (2 * this._remainDistance);\n\n                // 设置初始速度\n                this.speed = v0;\n\n                // console.log(`设置下一段: ${this._currentPointIndex} -> ${this._nextPointIndex}, 距离: ${this._remainDistance.toFixed(2)}, 初速度: ${v0}, 末速度: ${v1}, 加速度: ${this.acceleration.toFixed(2)}, 角度: ${radiansToDegrees(this.speedAngle).toFixed(2)}`);\n            }\n        }\n    }\n\n    private getPathPoint(pathPointIndex: number): PathPoint | null {\n        if (pathPointIndex < 0 || pathPointIndex >= this._subdivided.length) {\n            return null;\n        }\n        return this._subdivided[pathPointIndex];\n    }\n\n    // 公共API方法\n    public setMovable(movable: boolean): PathMove {\n        this._isMovable = movable;\n        return this;\n    }\n\n    private resetToStart() {\n        this._currentPointIndex = 0;\n        this._nextPointIndex = 0;\n        this._stayTimer = 0;\n        this._stayPointIndex = -1;\n        this._tiltTime = 0;\n\n        this.onReachPoint(0);\n    }\n\n    public isStaying(): boolean {\n        return this._stayTimer > 0;\n    }\n\n    public getRemainingStayTime(): number {\n        return this._stayTimer;\n    }\n}\n"]}