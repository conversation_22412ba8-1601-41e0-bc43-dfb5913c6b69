{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/move/PathMove.ts"], "names": ["_decorator", "misc", "JsonAsset", "MoveBase", "PathData", "degreesToRadians", "radiansToDegrees", "ccclass", "property", "executeInEditMode", "PathMove", "type", "displayName", "tooltip", "_pathAsset", "_pathData", "_subdivided", "_currentPointIndex", "_nextPointIndex", "_segmentT", "_tiltTime", "_isStaying", "_stayTimer", "_stayPointIndex", "_updateInEditor", "pathAsset", "value", "set<PERSON>ath", "fromJSON", "json", "onFocusInEditor", "_isMovable", "onLostFocusInEditor", "resetToStart", "update", "dt", "tick", "pathData", "getSubdividedPoints", "tickMovement", "tickTilting", "setNext", "pathPointIndex"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAA6BC,MAAAA,I,OAAAA,I;AAAqCC,MAAAA,S,OAAAA,S;;AAElEC,MAAAA,Q,iBAAAA,Q;;AAEAC,MAAAA,Q,iBAAAA,Q;;;;;;;;;OAEH;AAAEC,QAAAA,gBAAF;AAAoBC,QAAAA;AAApB,O,GAAyCL,I;OACzC;AAAEM,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CT,U;;0BAIpCU,Q,WAFZH,OAAO,CAAC,UAAD,C,UACPE,iBAAiB,E,UAGbD,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAET,SAAR;AAAmBU,QAAAA,WAAW,EAAE;AAAhC,OAAD,C,UAWRJ,QAAQ,CAAC;AAAEI,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAGRJ,QAAQ,CAAC;AAAEI,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAGRJ,QAAQ,CAAC;AAAEI,QAAAA,WAAW,EAAE,QAAf;AAAyBC,QAAAA,OAAO,EAAE;AAAlC,OAAD,C,UAGRL,QAAQ,CAAC;AAAEI,QAAAA,WAAW,EAAE,QAAf;AAAyBC,QAAAA,OAAO,EAAE;AAAlC,OAAD,C,0CAxBb,MAEaH,QAFb;AAAA;AAAA,gCAEuC;AAAA;AAAA;AAAA,eAC5BI,UAD4B,GACG,IADH;;AAAA;;AAAA;;AAAA;;AAAA;;AAyBnC;AAzBmC,eA0B3BC,SA1B2B,GA0BE,IA1BF;AAAA,eA2B3BC,WA3B2B,GA2BA,EA3BA;AA2BI;AAEvC;AA7BmC,eA8B3BC,kBA9B2B,GA8BE,CA9BF;AA8BK;AA9BL,eA+B3BC,eA/B2B,GA+BD,CA/BC;AAAA,eAiC3BC,SAjC2B,GAiCP,CAjCO;AAiCJ;AAjCI,eAkC3BC,SAlC2B,GAkCP,CAlCO;AAoCnC;AApCmC,eAqC3BC,UArC2B,GAqCL,KArCK;AAqCE;AArCF,eAsC3BC,UAtC2B,GAsCN,CAtCM;AAsCH;AAtCG,eAuC3BC,eAvC2B,GAuCD,CAAC,CAvCA;AAuCG;AAvCH,eAyC3BC,eAzC2B,GAyCA,KAzCA;AAAA;;AAGf,YAATC,SAAS,GAAqB;AACrC,iBAAO,KAAKX,UAAZ;AACH;;AACmB,YAATW,SAAS,CAACC,KAAD,EAAmB;AACnC,eAAKZ,UAAL,GAAkBY,KAAlB;;AACA,cAAIA,KAAJ,EAAW;AACP,iBAAKC,OAAL,CAAa;AAAA;AAAA,sCAASC,QAAT,CAAkBF,KAAK,CAACG,IAAxB,CAAb;AACH;AACJ;;AA+BMC,QAAAA,eAAe,GAAG;AACrB,eAAKN,eAAL,GAAuB,IAAvB;AACA,eAAKO,UAAL,GAAkB,IAAlB;AACH;;AAEMC,QAAAA,mBAAmB,GAAG;AACzB,eAAKR,eAAL,GAAuB,KAAvB;AACA,eAAKO,UAAL,GAAkB,KAAlB;AACA,eAAKE,YAAL;AACH;;AAEMC,QAAAA,MAAM,CAACC,EAAD,EAAa;AACtB,cAAI,KAAKX,eAAT,EAA0B;AACtB,iBAAKY,IAAL,CAAUD,EAAV;AACH;AACJ;AAED;AACJ;AACA;;;AACWR,QAAAA,OAAO,CAACU,QAAD,EAAqB;AAC/B,eAAKtB,SAAL,GAAiBsB,QAAjB,CAD+B,CAE/B;;AACA,eAAKrB,WAAL,GAAmB,KAAKD,SAAL,CAAeuB,mBAAf,EAAnB;AACH;AAED;AACJ;AACA;;;AACWF,QAAAA,IAAI,CAACD,EAAD,EAAmB;AAC1B,cAAI,CAAC,KAAKJ,UAAV,EAAsB;;AAEtB,cAAI,KAAKb,eAAL,KAAyB,KAAKD,kBAAlC,EAAsD;AAClD,iBAAKsB,YAAL,CAAkBJ,EAAlB;AACH;;AAED,eAAKK,WAAL,CAAiBL,EAAjB;AACH;;AAEOI,QAAAA,YAAY,CAACJ,EAAD,EAAa,CAEhC;;AAEOK,QAAAA,WAAW,CAACL,EAAD,EAAa,CAE/B;;AAEOM,QAAAA,OAAO,CAACC,cAAD,EAAyB,CAEvC;;AA3FkC,O;;;;;iBAcZ,K;;;;;;;iBAGG,K;;;;;;;iBAGC,C;;;;;;;iBAGC,C", "sourcesContent": ["import { _decorator, Component, Enum, misc, Node, UITransform, Vec2, Vec3, JsonAsset } from 'cc';\nimport { BulletSystem } from '../bullet/BulletSystem';\nimport { MoveBase, eMoveEvent } from './IMovable';\nimport Entity from '../ui/base/Entity';\nimport { PathData, PathPoint } from '../data/PathData';\n\nconst { degreesToRadians, radiansToDegrees } = misc;\nconst { ccclass, property, executeInEditMode } = _decorator;\n\n@ccclass('PathMove')\n@executeInEditMode()\nexport class PathMove extends MoveBase {\n    public _pathAsset: JsonAsset | null = null;\n    @property({ type: JsonAsset, displayName: \"路径数据(预览用)\" })\n    public get pathAsset(): JsonAsset | null {\n        return this._pathAsset;\n    }\n    public set pathAsset(value: JsonAsset) {\n        this._pathAsset = value;\n        if (value) {\n            this.setPath(PathData.fromJSON(value.json));\n        }\n    }\n\n    @property({ displayName: \"循环移动\" })\n    public loop: boolean = false;\n\n    @property({ displayName: \"反向移动\" })\n    public reverse: boolean = false;\n\n    @property({ displayName: \"振荡偏移速度\", tooltip: \"控制倾斜振荡的频率\" })\n    public tiltSpeed: number = 0;\n\n    @property({ displayName: \"振荡偏移幅度\", tooltip: \"控制倾斜振荡的幅度\" })\n    public tiltOffset: number = 0;\n\n    // 路径相关数据\n    private _pathData: PathData | null = null;\n    private _subdivided: PathPoint[] = []; // 细分后的路径点（包含完整信息）\n\n    // 移动状态\n    private _currentPointIndex: number = 0; // 当前所在的细分点索引\n    private _nextPointIndex: number = 0;\n\n    private _segmentT: number = 0; // 在当前段内的插值参数 [0,1]\n    private _tiltTime: number = 0;\n\n    // 停留状态\n    private _isStaying: boolean = false; // 是否正在停留\n    private _stayTimer: number = 0; // 停留计时器（秒）\n    private _stayPointIndex: number = -1; // 停留的点索引\n\n    private _updateInEditor: boolean = false;\n    public onFocusInEditor() {\n        this._updateInEditor = true;\n        this._isMovable = true;\n    }\n\n    public onLostFocusInEditor() {\n        this._updateInEditor = false;\n        this._isMovable = false;\n        this.resetToStart();\n    }\n\n    public update(dt: number) {\n        if (this._updateInEditor) {\n            this.tick(dt);\n        }\n    }\n\n    /**\n     * 加载路径数据（使用新的细分点方法）\n     */\n    public setPath(pathData: PathData) {\n        this._pathData = pathData;\n        // 使用新的细分点方法，直接获取包含完整信息的PathPoint数组\n        this._subdivided = this._pathData.getSubdividedPoints();\n    }\n\n    /**\n     * 主要的移动更新逻辑\n     */\n    public tick(dt: number): void {\n        if (!this._isMovable) return;\n        \n        if (this._nextPointIndex !== this._currentPointIndex) {\n            this.tickMovement(dt);\n        }\n\n        this.tickTilting(dt);\n    }\n\n    private tickMovement(dt: number) {\n        \n    }\n\n    private tickTilting(dt: number) {\n        \n    }\n\n    private setNext(pathPointIndex: number) {\n        \n    }\n\n}\n"]}