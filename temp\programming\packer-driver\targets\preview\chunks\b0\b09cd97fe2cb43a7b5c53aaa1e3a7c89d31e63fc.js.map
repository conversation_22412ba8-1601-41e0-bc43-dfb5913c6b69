{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/wave/PathEditor.ts"], "names": ["_decorator", "Node", "Color", "Component", "JsonAsset", "CCInteger", "Graphics", "PathData", "PathPoint", "PathPointEditor", "ccclass", "executeInEditMode", "property", "disallowMultiple", "menu", "requireComponent", "PathEditor", "type", "displayName", "visible", "_pathDataObj", "points", "length", "_graphics", "_showDirectionArrow", "_pathData", "_cachedChildrenCount", "graphics", "node", "getComponent", "addComponent", "pathData", "value", "reload", "pathName", "name", "startIdx", "endIdx", "isClosed", "closed", "Object", "assign", "json", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "point", "addPoint", "updateCurve", "save", "pointEditors", "getComponentsInChildren", "map", "editor", "pathPoint", "JSON", "stringify", "pointNode", "parent", "setPosition", "x", "y", "pointEditor", "addNewPoint", "drawPath", "clear", "subdivided", "getSubdividedPoints", "showSegments", "drawSegmentedPath", "drawUniformPath", "drawPathDirectionArrow", "strokeColor", "curveColor", "lineWidth", "moveTo", "i", "lineTo", "stroke", "t", "color", "interpolateColor", "GREEN", "RED", "lastColor", "color1", "color2", "Math", "max", "min", "r", "g", "b", "a", "update", "_dt", "childrenCount", "children", "endPoint", "prevPoint", "direction", "atan2", "<PERSON><PERSON><PERSON><PERSON>", "arrowHeadLength", "arrowHeadAngle", "PI", "fillColor", "arrowStartX", "arrowStartY", "arrowEndX", "cos", "arrowEndY", "sin", "leftX", "leftY", "rightX", "rightY", "close", "fill", "WHITE"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,Q,OAAAA,Q;;AAG1DC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,S,iBAAAA,S;;AACVC,MAAAA,e,iBAAAA,e;;;;;;;;;OAHH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,iBAAX;AAA8BC,QAAAA,QAA9B;AAAwCC,QAAAA,gBAAxC;AAA0DC,QAAAA,IAA1D;AAAgEC,QAAAA;AAAhE,O,GAAqFf,U;;4BAU9EgB,U,WALZN,OAAO,CAAC,YAAD,C,UACPI,IAAI,CAAC,aAAD,C,UACJC,gBAAgB,CAACT,QAAD,C,UAChBK,iBAAiB,CAAC,IAAD,C,UACjBE,gBAAgB,CAAC,IAAD,C,UAUZD,QAAQ,CAAC;AAAEK,QAAAA,IAAI,EAAEb,SAAR;AAAmBc,QAAAA,WAAW,EAAE;AAAhC,OAAD,C,UASRN,QAAQ,CAAC;AAAEM,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAQRN,QAAQ,CAAC;AAAEK,QAAAA,IAAI,EAAEZ,SAAR;AAAmBa,QAAAA,WAAW,EAAE;AAAhC,OAAD,C,UAQRN,QAAQ,CAAC;AAAEK,QAAAA,IAAI,EAAEZ,SAAR;AAAmBa,QAAAA,WAAW,EAAE;AAAhC,OAAD,C,WAQRN,QAAQ,CAAC;AAAEM,QAAAA,WAAW,EAAE,MAAf;;AAAuBC,QAAAA,OAAO,GAAG;AACvC;AACA,iBAAO,KAAKC,YAAL,CAAkBC,MAAlB,CAAyBC,MAAzB,IAAmC,CAA1C;AACH;;AAHS,OAAD,C,WAWRV,QAAQ,CAAC;AAAEM,QAAAA,WAAW,EAAE;AAAf,OAAD,C,WAGRN,QAAQ,CAAC;AAAEM,QAAAA,WAAW,EAAE;AAAf,OAAD,C,uFA7Db,MAKaF,UALb,SAKgCb,SALhC,CAK0C;AAAA;AAAA;AAAA,eAC9BoB,SAD8B,GACD,IADC;;AAAA;;AAAA;;AAyDA;AAzDA,eA2D9BC,mBA3D8B,GA2DC,IA3DD;AAAA,eA4D9BC,SA5D8B,GA4DA,IA5DA;AAAA,eA6D9BL,YA7D8B,GA6DL;AAAA;AAAA,qCA7DK;AAAA,eA8D9BM,oBA9D8B,GA8DC,CA9DD;AAAA;;AAEnB,YAARC,QAAQ,GAAa;AAC5B,cAAI,CAAC,KAAKJ,SAAV,EAAqB;AACjB,iBAAKA,SAAL,GAAiB,KAAKK,IAAL,CAAUC,YAAV,CAAuBvB,QAAvB,KAAoC,KAAKsB,IAAL,CAAUE,YAAV,CAAuBxB,QAAvB,CAArD;AACH;;AACD,iBAAO,KAAKiB,SAAZ;AACH;;AAGkB,YAARQ,QAAQ,CAACC,KAAD,EAAmB;AAClC,eAAKP,SAAL,GAAiBO,KAAjB;AACA,eAAKC,MAAL;AACH;;AACkB,YAARF,QAAQ,GAAqB;AACpC,iBAAO,KAAKN,SAAZ;AACH;;AAGkB,YAARS,QAAQ,GAAW;AAC1B,iBAAO,KAAKd,YAAL,CAAkBe,IAAzB;AACH;;AACkB,YAARD,QAAQ,CAACF,KAAD,EAAgB;AAC/B,eAAKZ,YAAL,CAAkBe,IAAlB,GAAyBH,KAAzB;AACH;;AAGkB,YAARI,QAAQ,GAAG;AAClB,iBAAO,KAAKhB,YAAL,CAAkBgB,QAAzB;AACH;;AACkB,YAARA,QAAQ,CAACJ,KAAD,EAAgB;AAC/B,eAAKZ,YAAL,CAAkBgB,QAAlB,GAA6BJ,KAA7B;AACH;;AAGgB,YAANK,MAAM,GAAG;AAChB,iBAAO,KAAKjB,YAAL,CAAkBiB,MAAzB;AACH;;AACgB,YAANA,MAAM,CAACL,KAAD,EAAgB;AAC7B,eAAKZ,YAAL,CAAkBiB,MAAlB,GAA2BL,KAA3B;AACH;;AAMkB,YAARM,QAAQ,GAAY;AAC3B,iBAAO,KAAKlB,YAAL,CAAkBmB,MAAzB;AACH;;AACkB,YAARD,QAAQ,CAACN,KAAD,EAAiB;AAChC,eAAKZ,YAAL,CAAkBmB,MAAlB,GAA2BP,KAA3B;AACH;;AAaMC,QAAAA,MAAM,GAAG;AACZ,cAAI,CAAC,KAAKR,SAAV,EAAqB;AAErB,cAAMM,QAAQ,GAAG;AAAA;AAAA,qCAAjB;AACAS,UAAAA,MAAM,CAACC,MAAP,CAAcV,QAAd,EAAwB,KAAKN,SAAL,CAAeiB,IAAvC;AACA,eAAKtB,YAAL,GAAoBW,QAApB;AAEA,eAAKH,IAAL,CAAUe,iBAAV;;AACA,cAAI,KAAKvB,YAAL,IAAqB,KAAKA,YAAL,CAAkBC,MAAlB,CAAyBC,MAAzB,GAAkC,CAA3D,EAA8D;AAC1D,iBAAKF,YAAL,CAAkBC,MAAlB,CAAyBuB,OAAzB,CAAkCC,KAAD,IAAW;AACxC,mBAAKC,QAAL,CAAcD,KAAd;AACH,aAFD;AAGH;;AACD,eAAKE,WAAL;AACH;;AAEMC,QAAAA,IAAI,GAAW;AAClB;AACA,cAAMC,YAAY,GAAG,KAAKC,uBAAL;AAAA;AAAA,iDAArB;AACA,eAAK9B,YAAL,CAAkBC,MAAlB,GAA2B4B,YAAY,CAACE,GAAb,CAAkBC,MAAD,IAAYA,MAAM,CAACC,SAApC,CAA3B;AACA,iBAAOC,IAAI,CAACC,SAAL,CAAe,KAAKnC,YAApB,EAAkC,IAAlC,EAAwC,CAAxC,CAAP;AACH;;AAEM0B,QAAAA,QAAQ,CAACD,KAAD,EAAmB;AAC9B,cAAMW,SAAS,GAAG,IAAIvD,IAAJ,EAAlB;AACAuD,UAAAA,SAAS,CAACC,MAAV,GAAmB,KAAK7B,IAAxB;AACA4B,UAAAA,SAAS,CAACE,WAAV,CAAsBb,KAAK,CAACc,CAA5B,EAA+Bd,KAAK,CAACe,CAArC,EAAwC,CAAxC;AAEA,cAAMC,WAAW,GAAGL,SAAS,CAAC1B,YAAV;AAAA;AAAA,iDAApB;AACA+B,UAAAA,WAAW,CAACR,SAAZ,GAAwBR,KAAxB;AACH;;AAEMiB,QAAAA,WAAW,CAACH,CAAD,EAAYC,CAAZ,EAAuB;AACrC,cAAMf,KAAK,GAAG;AAAA;AAAA,sCAAcc,CAAd,EAAiBC,CAAjB,CAAd;AACA,eAAKd,QAAL,CAAcD,KAAd;AACA,eAAKE,WAAL;AACH;;AAEMA,QAAAA,WAAW,GAAG;AACjB;AACA,cAAME,YAAY,GAAG,KAAKC,uBAAL;AAAA;AAAA,iDAArB;AACA,eAAK9B,YAAL,CAAkBC,MAAlB,GAA2B4B,YAAY,CAACE,GAAb,CAAkBC,MAAD,IAAYA,MAAM,CAACC,SAApC,CAA3B;AACH;;AAEOU,QAAAA,QAAQ,GAAG;AACf,cAAMpC,QAAQ,GAAG,KAAKA,QAAtB;AACAA,UAAAA,QAAQ,CAACqC,KAAT;AAEA,cAAI,KAAK5C,YAAL,CAAkBC,MAAlB,CAAyBC,MAAzB,GAAkC,CAAtC,EAAyC;;AAEzC,cAAM2C,UAAU,GAAG,KAAK7C,YAAL,CAAkB8C,mBAAlB,CAAsC,IAAtC,CAAnB;;AACA,cAAID,UAAU,CAAC3C,MAAX,GAAoB,CAAxB,EAA2B;AACvB,gBAAI,KAAK6C,YAAT,EAAuB;AACnB,mBAAKC,iBAAL,CAAuBzC,QAAvB,EAAiCsC,UAAjC;AACH,aAFD,MAEO;AACH,mBAAKI,eAAL,CAAqB1C,QAArB,EAA+BsC,UAA/B;AACH,aALsB,CAOvB;;;AACA,gBAAI,KAAKzC,mBAAL,IAA4B,CAAC,KAAKJ,YAAL,CAAkBmB,MAAnD,EAA2D;AACvD,mBAAK+B,sBAAL,CAA4B3C,QAA5B,EAAsCsC,UAAtC;AACH;AACJ,WAlBc,CAoBf;;AACH;AAED;AACJ;AACA;;;AACYI,QAAAA,eAAe,CAAC1C,QAAD,EAAqBsC,UAArB,EAA8C;AACjEtC,UAAAA,QAAQ,CAAC4C,WAAT,GAAuB,KAAKC,UAA5B;AACA7C,UAAAA,QAAQ,CAAC8C,SAAT,GAAqB,CAArB;AAEA9C,UAAAA,QAAQ,CAAC+C,MAAT,CAAgBT,UAAU,CAAC,CAAD,CAAV,CAAcN,CAA9B,EAAiCM,UAAU,CAAC,CAAD,CAAV,CAAcL,CAA/C;;AACA,eAAK,IAAIe,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGV,UAAU,CAAC3C,MAA/B,EAAuCqD,CAAC,EAAxC,EAA4C;AACxChD,YAAAA,QAAQ,CAACiD,MAAT,CAAgBX,UAAU,CAACU,CAAD,CAAV,CAAchB,CAA9B,EAAiCM,UAAU,CAACU,CAAD,CAAV,CAAcf,CAA/C;AACH,WAPgE,CASjE;;;AACA,cAAI,KAAKxC,YAAL,CAAkBmB,MAAtB,EAA8B;AAC1BZ,YAAAA,QAAQ,CAACiD,MAAT,CAAgBX,UAAU,CAAC,CAAD,CAAV,CAAcN,CAA9B,EAAiCM,UAAU,CAAC,CAAD,CAAV,CAAcL,CAA/C;AACH;;AAEDjC,UAAAA,QAAQ,CAACkD,MAAT;AACH;AAED;AACJ;AACA;;;AACYT,QAAAA,iBAAiB,CAACzC,QAAD,EAAqBsC,UAArB,EAA8C;AACnEtC,UAAAA,QAAQ,CAAC8C,SAAT,GAAqB,CAArB;AAEA,cAAIR,UAAU,CAAC3C,MAAX,GAAoB,CAAxB,EAA2B,OAHwC,CAKnE;;AACA,eAAK,IAAIqD,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGV,UAAU,CAAC3C,MAAX,GAAoB,CAAxC,EAA2CqD,CAAC,EAA5C,EAAgD;AAC5C,gBAAMG,CAAC,GAAGH,CAAC,IAAIV,UAAU,CAAC3C,MAAX,GAAoB,CAAxB,CAAX,CAD4C,CACL;AAEvC;;AACA,gBAAMyD,KAAK,GAAG,KAAKC,gBAAL,CAAsB9E,KAAK,CAAC+E,KAA5B,EAAmC/E,KAAK,CAACgF,GAAzC,EAA8CJ,CAA9C,CAAd;AACAnD,YAAAA,QAAQ,CAAC4C,WAAT,GAAuBQ,KAAvB,CAL4C,CAO5C;;AACApD,YAAAA,QAAQ,CAAC+C,MAAT,CAAgBT,UAAU,CAACU,CAAD,CAAV,CAAchB,CAA9B,EAAiCM,UAAU,CAACU,CAAD,CAAV,CAAcf,CAA/C;AACAjC,YAAAA,QAAQ,CAACiD,MAAT,CAAgBX,UAAU,CAACU,CAAC,GAAG,CAAL,CAAV,CAAkBhB,CAAlC,EAAqCM,UAAU,CAACU,CAAC,GAAG,CAAL,CAAV,CAAkBf,CAAvD;AACAjC,YAAAA,QAAQ,CAACkD,MAAT;AACH,WAjBkE,CAmBnE;;;AACA,cAAI,KAAKzD,YAAL,CAAkBmB,MAAlB,IAA4B0B,UAAU,CAAC3C,MAAX,GAAoB,CAApD,EAAuD;AACnD,gBAAM6D,SAAS,GAAG,KAAKH,gBAAL,CAAsB9E,KAAK,CAAC+E,KAA5B,EAAmC/E,KAAK,CAACgF,GAAzC,EAA8C,GAA9C,CAAlB;AACAvD,YAAAA,QAAQ,CAAC4C,WAAT,GAAuBY,SAAvB;AACAxD,YAAAA,QAAQ,CAAC+C,MAAT,CAAgBT,UAAU,CAACA,UAAU,CAAC3C,MAAX,GAAoB,CAArB,CAAV,CAAkCqC,CAAlD,EAAqDM,UAAU,CAACA,UAAU,CAAC3C,MAAX,GAAoB,CAArB,CAAV,CAAkCsC,CAAvF;AACAjC,YAAAA,QAAQ,CAACiD,MAAT,CAAgBX,UAAU,CAAC,CAAD,CAAV,CAAcN,CAA9B,EAAiCM,UAAU,CAAC,CAAD,CAAV,CAAcL,CAA/C;AACAjC,YAAAA,QAAQ,CAACkD,MAAT;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACYG,QAAAA,gBAAgB,CAACI,MAAD,EAAgBC,MAAhB,EAA+BP,CAA/B,EAAiD;AACrEA,UAAAA,CAAC,GAAGQ,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYD,IAAI,CAACE,GAAL,CAAS,CAAT,EAAYV,CAAZ,CAAZ,CAAJ,CADqE,CACpC;;AAEjC,cAAMW,CAAC,GAAGL,MAAM,CAACK,CAAP,GAAW,CAACJ,MAAM,CAACI,CAAP,GAAWL,MAAM,CAACK,CAAnB,IAAwBX,CAA7C;AACA,cAAMY,CAAC,GAAGN,MAAM,CAACM,CAAP,GAAW,CAACL,MAAM,CAACK,CAAP,GAAWN,MAAM,CAACM,CAAnB,IAAwBZ,CAA7C;AACA,cAAMa,CAAC,GAAGP,MAAM,CAACO,CAAP,GAAW,CAACN,MAAM,CAACM,CAAP,GAAWP,MAAM,CAACO,CAAnB,IAAwBb,CAA7C;AACA,cAAMc,CAAC,GAAGR,MAAM,CAACQ,CAAP,GAAW,CAACP,MAAM,CAACO,CAAP,GAAWR,MAAM,CAACQ,CAAnB,IAAwBd,CAA7C;AAEA,iBAAO,IAAI5E,KAAJ,CAAUuF,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmBC,CAAnB,CAAP;AACH;;AAEMC,QAAAA,MAAM,CAACC,GAAD,EAAc;AACvB,cAAMC,aAAa,GAAG,KAAKnE,IAAL,CAAUoE,QAAV,CAAmB1E,MAAzC;;AACA,cAAIyE,aAAa,KAAK,KAAKrE,oBAA3B,EAAiD;AAC7C,iBAAKA,oBAAL,GAA4BqE,aAA5B;AACH;;AACD,eAAKhD,WAAL;AACA,eAAKgB,QAAL;AACH;AAED;AACJ;AACA;;;AACYO,QAAAA,sBAAsB,CAAC3C,QAAD,EAAqBsC,UAArB,EAAwC;AAClE,cAAIA,UAAU,CAAC3C,MAAX,GAAoB,CAAxB,EAA2B,OADuC,CAGlE;;AACA,cAAI,KAAKF,YAAL,CAAkBmB,MAAtB,EAA8B,OAJoC,CAMlE;;AACA,cAAM0D,QAAQ,GAAGhC,UAAU,CAACA,UAAU,CAAC3C,MAAX,GAAoB,CAArB,CAA3B;AACA,cAAI4E,SAAS,GAAGjC,UAAU,CAACA,UAAU,CAAC3C,MAAX,GAAoB,CAArB,CAA1B,CARkE,CAUlE;;AACA,cAAI2C,UAAU,CAAC3C,MAAX,IAAqB,CAAzB,EAA4B;AACxB4E,YAAAA,SAAS,GAAGjC,UAAU,CAACA,UAAU,CAAC3C,MAAX,GAAoB,CAArB,CAAtB;AACH,WAbiE,CAelE;;;AACA,cAAM6E,SAAS,GAAGb,IAAI,CAACc,KAAL,CAAWH,QAAQ,CAACrC,CAAT,GAAasC,SAAS,CAACtC,CAAlC,EAAqCqC,QAAQ,CAACtC,CAAT,GAAauC,SAAS,CAACvC,CAA5D,CAAlB,CAhBkE,CAkBlE;;AACA,cAAM0C,WAAW,GAAG,EAApB;AACA,cAAMC,eAAe,GAAG,EAAxB;AACA,cAAMC,cAAc,GAAGjB,IAAI,CAACkB,EAAL,GAAU,CAAjC,CArBkE,CAqB9B;AAEpC;;AACA7E,UAAAA,QAAQ,CAAC4C,WAAT,GAAuBrE,KAAK,CAACgF,GAA7B;AACAvD,UAAAA,QAAQ,CAAC8E,SAAT,GAAqBvG,KAAK,CAACgF,GAA3B;AACAvD,UAAAA,QAAQ,CAAC8C,SAAT,GAAqB,CAArB,CA1BkE,CA4BlE;;AACA,cAAMiC,WAAW,GAAGT,QAAQ,CAACtC,CAA7B;AACA,cAAMgD,WAAW,GAAGV,QAAQ,CAACrC,CAA7B,CA9BkE,CAgClE;;AACA,cAAMgD,SAAS,GAAGF,WAAW,GAAGpB,IAAI,CAACuB,GAAL,CAASV,SAAT,IAAsBE,WAAtD;AACA,cAAMS,SAAS,GAAGH,WAAW,GAAGrB,IAAI,CAACyB,GAAL,CAASZ,SAAT,IAAsBE,WAAtD,CAlCkE,CAoClE;;AACA1E,UAAAA,QAAQ,CAAC+C,MAAT,CAAgBgC,WAAhB,EAA6BC,WAA7B;AACAhF,UAAAA,QAAQ,CAACiD,MAAT,CAAgBgC,SAAhB,EAA2BE,SAA3B;AACAnF,UAAAA,QAAQ,CAACkD,MAAT,GAvCkE,CAyClE;;AACA,cAAMmC,KAAK,GAAGJ,SAAS,GAAGtB,IAAI,CAACuB,GAAL,CAASV,SAAS,GAAGI,cAArB,IAAuCD,eAAjE;AACA,cAAMW,KAAK,GAAGH,SAAS,GAAGxB,IAAI,CAACyB,GAAL,CAASZ,SAAS,GAAGI,cAArB,IAAuCD,eAAjE;AACA,cAAMY,MAAM,GAAGN,SAAS,GAAGtB,IAAI,CAACuB,GAAL,CAASV,SAAS,GAAGI,cAArB,IAAuCD,eAAlE;AACA,cAAMa,MAAM,GAAGL,SAAS,GAAGxB,IAAI,CAACyB,GAAL,CAASZ,SAAS,GAAGI,cAArB,IAAuCD,eAAlE,CA7CkE,CA+ClE;;AACA3E,UAAAA,QAAQ,CAAC+C,MAAT,CAAgBkC,SAAhB,EAA2BE,SAA3B;AACAnF,UAAAA,QAAQ,CAACiD,MAAT,CAAgBoC,KAAhB,EAAuBC,KAAvB;AACAtF,UAAAA,QAAQ,CAACiD,MAAT,CAAgBsC,MAAhB,EAAwBC,MAAxB;AACAxF,UAAAA,QAAQ,CAACyF,KAAT;AACAzF,UAAAA,QAAQ,CAAC0F,IAAT;AACA1F,UAAAA,QAAQ,CAACkD,MAAT;AACH;;AA1QqC,O;;;;;iBAsDX3E,KAAK,CAACoH,K;;;;;;;iBAGF,K", "sourcesContent": ["import { _decorator, Node, Color, Component, JsonAsset, CCInteger, Graphics } from 'cc';\r\nconst { ccclass, executeInEditMode, property, disallowMultiple, menu, requireComponent } = _decorator;\r\nimport { EDITOR } from 'cc/env';\r\nimport { PathData, PathPoint } from 'db://assets/bundles/common/script/game/data/PathData';\r\nimport { PathPointEditor } from './PathPointEditor';\r\n\r\n@ccclass('PathEditor')\r\n@menu(\"怪物/编辑器/路径编辑\")\r\n@requireComponent(Graphics)\r\n@executeInEditMode(true)\r\n@disallowMultiple(true)\r\nexport class PathEditor extends Component {\r\n    private _graphics: Graphics | null = null;\r\n    public get graphics(): Graphics {\r\n        if (!this._graphics) {\r\n            this._graphics = this.node.getComponent(Graphics) || this.node.addComponent(Graphics);\r\n        }\r\n        return this._graphics;\r\n    }\r\n\r\n    @property({ type: JsonAsset, displayName: \"路径数据\" })\r\n    public set pathData(value: JsonAsset) {\r\n        this._pathData = value;\r\n        this.reload();\r\n    }\r\n    public get pathData(): JsonAsset | null {\r\n        return this._pathData;\r\n    }\r\n\r\n    @property({ displayName: \"路径名称\" })\r\n    public get pathName(): string {\r\n        return this._pathDataObj.name;\r\n    }\r\n    public set pathName(value: string) {\r\n        this._pathDataObj.name = value;\r\n    }\r\n\r\n    @property({ type: CCInteger, displayName: '起始点'})\r\n    public get startIdx() {\r\n        return this._pathDataObj.startIdx;\r\n    }\r\n    public set startIdx(value: number) {\r\n        this._pathDataObj.startIdx = value;\r\n    }\r\n\r\n    @property({ type: CCInteger, displayName: '结束点(-1代表默认最后个点)'})\r\n    public get endIdx() {\r\n        return this._pathDataObj.endIdx;\r\n    }\r\n    public set endIdx(value: number) {\r\n        this._pathDataObj.endIdx = value;\r\n    }\r\n\r\n    @property({ displayName: \"是否闭合\", visible() {\r\n        // @ts-ignore\r\n        return this._pathDataObj.points.length >= 3;\r\n    }})\r\n    public get isClosed(): boolean {\r\n        return this._pathDataObj.closed;\r\n    }\r\n    public set isClosed(value: boolean) {\r\n        this._pathDataObj.closed = value;\r\n    }\r\n\r\n    @property({ displayName: \"曲线颜色\" })\r\n    public curveColor: Color = Color.WHITE;\r\n\r\n    @property({ displayName: \"显示细分线段\" })\r\n    public showSegments: boolean = false; // 是否使用不同颜色来绘制不同的细分线段\r\n\r\n    private _showDirectionArrow: boolean = true;\r\n    private _pathData: JsonAsset | null = null;\r\n    private _pathDataObj: PathData = new PathData();\r\n    private _cachedChildrenCount: number = 0;\r\n\r\n    public reload() {\r\n        if (!this._pathData) return;\r\n\r\n        const pathData = new PathData();\r\n        Object.assign(pathData, this._pathData.json);\r\n        this._pathDataObj = pathData;\r\n\r\n        this.node.removeAllChildren();\r\n        if (this._pathDataObj && this._pathDataObj.points.length > 0) {\r\n            this._pathDataObj.points.forEach((point) => {\r\n                this.addPoint(point);\r\n            });\r\n        }\r\n        this.updateCurve();\r\n    }\r\n\r\n    public save(): string {\r\n        // 收集所有路径点数据\r\n        const pointEditors = this.getComponentsInChildren(PathPointEditor);\r\n        this._pathDataObj.points = pointEditors.map((editor) => editor.pathPoint);\r\n        return JSON.stringify(this._pathDataObj, null, 2);\r\n    }\r\n\r\n    public addPoint(point: PathPoint) {\r\n        const pointNode = new Node();\r\n        pointNode.parent = this.node;\r\n        pointNode.setPosition(point.x, point.y, 0);\r\n\r\n        const pointEditor = pointNode.addComponent(PathPointEditor);\r\n        pointEditor.pathPoint = point;\r\n    }\r\n\r\n    public addNewPoint(x: number, y: number) {\r\n        const point = new PathPoint(x, y);\r\n        this.addPoint(point);\r\n        this.updateCurve();\r\n    }\r\n\r\n    public updateCurve() {\r\n        // 收集当前所有点的数据\r\n        const pointEditors = this.getComponentsInChildren(PathPointEditor);\r\n        this._pathDataObj.points = pointEditors.map((editor) => editor.pathPoint);\r\n    }\r\n\r\n    private drawPath() {\r\n        const graphics = this.graphics;\r\n        graphics.clear();\r\n\r\n        if (this._pathDataObj.points.length < 2) return;\r\n\r\n        const subdivided = this._pathDataObj.getSubdividedPoints(true);\r\n        if (subdivided.length > 1) {\r\n            if (this.showSegments) {\r\n                this.drawSegmentedPath(graphics, subdivided);\r\n            } else {\r\n                this.drawUniformPath(graphics, subdivided);\r\n            }\r\n\r\n            // 绘制路径终点的方向箭头（仅对非闭合路径）\r\n            if (this._showDirectionArrow && !this._pathDataObj.closed) {\r\n                this.drawPathDirectionArrow(graphics, subdivided);\r\n            }\r\n        }\r\n\r\n        // console.log('subdivided points length: ', subdivided.length);\r\n    }\r\n\r\n    /**\r\n     * 绘制统一颜色的路径\r\n     */\r\n    private drawUniformPath(graphics: Graphics, subdivided: PathPoint[]) {\r\n        graphics.strokeColor = this.curveColor;\r\n        graphics.lineWidth = 5;\r\n\r\n        graphics.moveTo(subdivided[0].x, subdivided[0].y);\r\n        for (let i = 1; i < subdivided.length; i++) {\r\n            graphics.lineTo(subdivided[i].x, subdivided[i].y);\r\n        }\r\n\r\n        // 如果是闭合路径，连接回起点\r\n        if (this._pathDataObj.closed) {\r\n            graphics.lineTo(subdivided[0].x, subdivided[0].y);\r\n        }\r\n\r\n        graphics.stroke();\r\n    }\r\n\r\n    /**\r\n     * 绘制分段着色的路径 - 每个细分段用不同颜色\r\n     */\r\n    private drawSegmentedPath(graphics: Graphics, subdivided: PathPoint[]) {\r\n        graphics.lineWidth = 5;\r\n\r\n        if (subdivided.length < 2) return;\r\n\r\n        // 为每个细分段绘制不同的颜色\r\n        for (let i = 0; i < subdivided.length - 1; i++) {\r\n            const t = i / (subdivided.length - 1); // 计算当前位置的归一化参数 [0,1]\r\n\r\n            // 从绿色到红色的颜色插值\r\n            const color = this.interpolateColor(Color.GREEN, Color.RED, t);\r\n            graphics.strokeColor = color;\r\n\r\n            // 绘制当前段\r\n            graphics.moveTo(subdivided[i].x, subdivided[i].y);\r\n            graphics.lineTo(subdivided[i + 1].x, subdivided[i + 1].y);\r\n            graphics.stroke();\r\n        }\r\n\r\n        // 如果是闭合路径，绘制最后一段回到起点\r\n        if (this._pathDataObj.closed && subdivided.length > 2) {\r\n            const lastColor = this.interpolateColor(Color.GREEN, Color.RED, 1.0);\r\n            graphics.strokeColor = lastColor;\r\n            graphics.moveTo(subdivided[subdivided.length - 1].x, subdivided[subdivided.length - 1].y);\r\n            graphics.lineTo(subdivided[0].x, subdivided[0].y);\r\n            graphics.stroke();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 颜色插值函数\r\n     * @param color1 起始颜色\r\n     * @param color2 结束颜色\r\n     * @param t 插值参数 [0,1]\r\n     */\r\n    private interpolateColor(color1: Color, color2: Color, t: number): Color {\r\n        t = Math.max(0, Math.min(1, t)); // 确保t在[0,1]范围内\r\n\r\n        const r = color1.r + (color2.r - color1.r) * t;\r\n        const g = color1.g + (color2.g - color1.g) * t;\r\n        const b = color1.b + (color2.b - color1.b) * t;\r\n        const a = color1.a + (color2.a - color1.a) * t;\r\n\r\n        return new Color(r, g, b, a);\r\n    }\r\n\r\n    public update(_dt: number) {\r\n        const childrenCount = this.node.children.length;\r\n        if (childrenCount !== this._cachedChildrenCount) {\r\n            this._cachedChildrenCount = childrenCount;\r\n        }\r\n        this.updateCurve();\r\n        this.drawPath();\r\n    }\r\n\r\n    /**\r\n     * 绘制路径方向箭头\r\n     */\r\n    private drawPathDirectionArrow(graphics: Graphics, subdivided: any[]) {\r\n        if (subdivided.length < 2) return;\r\n\r\n        // 如果是闭合路径，不绘制箭头（因为没有明确的终点）\r\n        if (this._pathDataObj.closed) return;\r\n\r\n        // 计算终点的方向（使用最后几个点来获得更准确的方向）\r\n        const endPoint = subdivided[subdivided.length - 1];\r\n        let prevPoint = subdivided[subdivided.length - 2];\r\n\r\n        // 如果有足够的点，使用更远的点来计算方向，获得更平滑的方向\r\n        if (subdivided.length >= 5) {\r\n            prevPoint = subdivided[subdivided.length - 5];\r\n        }\r\n\r\n        // 计算方向角度\r\n        const direction = Math.atan2(endPoint.y - prevPoint.y, endPoint.x - prevPoint.x);\r\n\r\n        // 箭头参数\r\n        const arrowLength = 40;\r\n        const arrowHeadLength = 20;\r\n        const arrowHeadAngle = Math.PI / 5; // 36度，更尖锐的箭头\r\n\r\n        // 设置箭头样式\r\n        graphics.strokeColor = Color.RED;\r\n        graphics.fillColor = Color.RED;\r\n        graphics.lineWidth = 3;\r\n\r\n        // 计算箭头起点（从路径终点开始）\r\n        const arrowStartX = endPoint.x;\r\n        const arrowStartY = endPoint.y;\r\n\r\n        // 计算箭头终点\r\n        const arrowEndX = arrowStartX + Math.cos(direction) * arrowLength;\r\n        const arrowEndY = arrowStartY + Math.sin(direction) * arrowLength;\r\n\r\n        // 绘制箭头主线\r\n        graphics.moveTo(arrowStartX, arrowStartY);\r\n        graphics.lineTo(arrowEndX, arrowEndY);\r\n        graphics.stroke();\r\n\r\n        // 绘制箭头头部（填充三角形）\r\n        const leftX = arrowEndX - Math.cos(direction - arrowHeadAngle) * arrowHeadLength;\r\n        const leftY = arrowEndY - Math.sin(direction - arrowHeadAngle) * arrowHeadLength;\r\n        const rightX = arrowEndX - Math.cos(direction + arrowHeadAngle) * arrowHeadLength;\r\n        const rightY = arrowEndY - Math.sin(direction + arrowHeadAngle) * arrowHeadLength;\r\n\r\n        // 绘制填充的箭头头部\r\n        graphics.moveTo(arrowEndX, arrowEndY);\r\n        graphics.lineTo(leftX, leftY);\r\n        graphics.lineTo(rightX, rightY);\r\n        graphics.close();\r\n        graphics.fill();\r\n        graphics.stroke();\r\n    }\r\n}"]}