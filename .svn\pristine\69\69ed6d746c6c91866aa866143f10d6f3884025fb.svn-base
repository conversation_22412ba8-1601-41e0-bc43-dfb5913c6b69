import { error } from "cc";
import { MyApp } from "db://assets/bundles/common/script/app/MyApp";
import { PlanePropType, ResEquipUpgrade, ResPlane } from "db://assets/bundles/common/script/autogen/luban/schema";
import { AttributeConst } from "../../const/AttributeConst";
import { AttributeData } from "../base/AttributeData";
import { DataMgr } from "../DataManager";

const PATH_SPINE = "spine/plane/mainplane/"

export class PlaneData extends AttributeData {
    id: number = 0;//唯一id
    _planeId: number = 0;//飞机id

    config: ResPlane | null = null;//飞机静态配置

    get planeId() {
        return this._planeId;
    }

    set planeId(value) {
        if (value != this._planeId) {
            this._planeId = value;
            this.updateConfig();
        }
    }

    get recourseSpine() {
        if (!this.config) {
            return "";
        }
        return PATH_SPINE + this.config.prefab + "/" + this.config.prefab
    }

    updateConfig() {
        if (!this._planeId) {
            error("Plane id or level is 0, cannot update config.");
            return;
        }
        this.config = MyApp.lubanTables.TbPlane.get(this._planeId)||null;
        this.updateData();
    }

    updateData() {
        if (!this.config) {
            error(`Plane ${this._planeId} config is null, cannot update attributes.`);
            return;
        }

        //根据飞机基础配置表，获取基础属性
        this.addBaseAttribute(AttributeConst.MaxHPOutAdd, this.config.property.MaxHP);
        this.addBaseAttribute(AttributeConst.AttackOutAdd, this.config.property.Attack);
        this.addBaseAttribute(AttributeConst.HPRecoveryOutAdd, this.config.property.HPRecovery);
        this.addBaseAttribute(AttributeConst.FortunateOutAdd, this.config.property.Fortunate);
        this.addBaseAttribute(AttributeConst.MissRateOut, this.config.property.Miss);
        this.addBaseAttribute(AttributeConst.BulletHurtResistanceOutPer, this.config.property.BulletHurtResistance);
        this.addBaseAttribute(AttributeConst.CollisionHurtResistanceOutPer, this.config.property.CollisionHurtResistance);
        this.addBaseAttribute(AttributeConst.PickRadiusOutAdd, this.config.property.PickRadius);
        this.addBaseAttribute(AttributeConst.FinalScoreRateOut, this.config.property.FinalScore);
        this.addBaseAttribute(AttributeConst.NuclearMax, this.config.property.NuclearMax);
        this.addBaseAttribute(AttributeConst.MaxEnergyOutAdd, this.config.property.MaxEnergy);
        this.addBaseAttribute(AttributeConst.EnergyRecoveryOutAdd, this.config.property.EnergyRecovery);

        // 装备
        DataMgr.equip.eqSlots.slots.forEach(slot => {
            if (!slot.equip_id || !slot.level || !slot.equip_class) {
                return
            }
            const equipConfig = MyApp.lubanTables.TbResEquip.get(slot.equip_id);
            if (!equipConfig) {
                return
            }
            const equipUpgradeConfigs: ResEquipUpgrade[] = []
            MyApp.lubanTables.TbEquipUpgrade.getDataList().forEach(element => {
                if (element.equipClass == slot.equip_class && element.levelFrom <= slot.level!) {
                    equipUpgradeConfigs.push(element);
                }
            });
            equipUpgradeConfigs.sort((a, b) => a.levelTo - b.levelTo);
            const equipUpgradeProps: Map<PlanePropType, number> = new Map()
            for (let equipUpgradeConfig of equipUpgradeConfigs) {
                for (let i = equipUpgradeConfig.levelFrom; i <= slot.level! && i <= equipUpgradeConfig.levelTo; i++) {
                    equipUpgradeConfig.propInc.forEach(prop => {
                        if (prop.propType == PlanePropType.NONE) {
                            return
                        }
                        equipUpgradeProps.set(prop.propType, (equipUpgradeProps.get(prop.propType) || 1) * (10000 + prop.propParam)/10000)
                    })
                }
            }
            equipConfig.props.forEach(prop => {
                const value = prop.propParam * (equipUpgradeProps.get(prop.propType) || 1);
                switch(prop.propType) {
                    case PlanePropType.MaxHP:
                        this.addBaseAttribute(AttributeConst.MaxHPOutAdd, value);
                        break;
                    case PlanePropType.HPRecovery:
                        this.addBaseAttribute(AttributeConst.HPRecoveryOutAdd, value);
                        break;
                    case PlanePropType.Attack:
                        this.addBaseAttribute(AttributeConst.AttackOutAdd, value);
                        break;
                    case PlanePropType.Fortunate:
                        this.addBaseAttribute(AttributeConst.FortunateOutAdd, value);
                        break;
                    case PlanePropType.Miss:
                        this.addBaseAttribute(AttributeConst.MissRateOut, value);
                        break;
                    case PlanePropType.BulletHurtResistance:
                        this.addBaseAttribute(AttributeConst.BulletHurtResistanceOutPer, value);
                        break;
                    case PlanePropType.CollisionHurtResistance:
                        this.addBaseAttribute(AttributeConst.CollisionHurtResistanceOutPer, value);
                        break;
                    case PlanePropType.PickRadius:
                        this.addBaseAttribute(AttributeConst.PickRadiusOutAdd, value);
                        break;
                    case PlanePropType.FinalScore:
                        this.addBaseAttribute(AttributeConst.FinalScoreRateOut, value);
                        break;
                    case PlanePropType.NuclearMax:
                        this.addBaseAttribute(AttributeConst.NuclearMax, value);
                        break;
                    case PlanePropType.MaxEnergy:
                        this.addBaseAttribute(AttributeConst.MaxEnergyOutAdd, value);
                        break;
                    case PlanePropType.EnergyRecovery:
                        this.addBaseAttribute(AttributeConst.EnergyRecoveryOutAdd, value);
                        break;
                    case PlanePropType.AllBulletAttack:
                        this.addBaseAttribute(AttributeConst.BulletAttackOutAdd, value);
                        break;
                    case PlanePropType.ExplosiveBulletAttack:
                        this.addBaseAttribute(AttributeConst.ExplosiveBulletAttackOutAdd, value);
                        break;
                    case PlanePropType.NormalBulletAttack:
                        this.addBaseAttribute(AttributeConst.NormalBulletAttackOutAdd, value);
                        break;
                    case PlanePropType.EnergeticBulletAttack:
                        this.addBaseAttribute(AttributeConst.EnergeticBulletAttackOutAdd, value);
                        break;
                    case PlanePropType.PhysicalBulletAttack:
                        this.addBaseAttribute(AttributeConst.PhysicsBulletAttackOutAdd, value);
                        break;
                }
            })
        })
    }

    getAttributeList() {
        // 获取装备，技能，buff等属性
    }
}