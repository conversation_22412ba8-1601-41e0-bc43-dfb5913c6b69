System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, Vec2, BoolOpType, CondOPType, SkillConditionType, TargetType, MyApp, PlaneEventType, forEachEntityByTargetType, ExCondition, ExConditionEvent, ExConditionNum, GameIns, Buff, _crd;

  function _reportPossibleCrUseOfBoolOpType(extras) {
    _reporterNs.report("BoolOpType", "../../../../autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfCondOPType(extras) {
    _reporterNs.report("CondOPType", "../../../../autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfResBuffer(extras) {
    _reporterNs.report("ResBuffer", "../../../../autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfResSkillCondition(extras) {
    _reporterNs.report("ResSkillCondition", "../../../../autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfResSkillConditionElem(extras) {
    _reporterNs.report("ResSkillConditionElem", "../../../../autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfSkillConditionType(extras) {
    _reporterNs.report("SkillConditionType", "../../../../autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTargetType(extras) {
    _reporterNs.report("TargetType", "../../../../autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "../../../../app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneEventType(extras) {
    _reporterNs.report("PlaneEventType", "../event/PlaneEventType", _context.meta, extras);
  }

  function _reportPossibleCrUseOfforEachEntityByTargetType(extras) {
    _reporterNs.report("forEachEntityByTargetType", "./SearchTarget", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneBase(extras) {
    _reporterNs.report("PlaneBase", "../PlaneBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfExCondition(extras) {
    _reporterNs.report("ExCondition", "./ExCondition", _context.meta, extras);
  }

  function _reportPossibleCrUseOfExConditionEvent(extras) {
    _reporterNs.report("ExConditionEvent", "./ExCondition", _context.meta, extras);
  }

  function _reportPossibleCrUseOfExConditionNum(extras) {
    _reporterNs.report("ExConditionNum", "./ExCondition", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../../GameIns", _context.meta, extras);
  }

  _export("Buff", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      Vec2 = _cc.Vec2;
    }, function (_unresolved_2) {
      BoolOpType = _unresolved_2.BoolOpType;
      CondOPType = _unresolved_2.CondOPType;
      SkillConditionType = _unresolved_2.SkillConditionType;
      TargetType = _unresolved_2.TargetType;
    }, function (_unresolved_3) {
      MyApp = _unresolved_3.MyApp;
    }, function (_unresolved_4) {
      PlaneEventType = _unresolved_4.PlaneEventType;
    }, function (_unresolved_5) {
      forEachEntityByTargetType = _unresolved_5.default;
    }, function (_unresolved_6) {
      ExCondition = _unresolved_6.ExCondition;
      ExConditionEvent = _unresolved_6.ExConditionEvent;
      ExConditionNum = _unresolved_6.ExConditionNum;
    }, function (_unresolved_7) {
      GameIns = _unresolved_7.GameIns;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "32585OKpDdIorY8SqujjCos", "Buff", undefined);

      __checkObsolete__(['Vec2']);

      _export("Buff", Buff = class Buff {
        constructor(isOutside, data, self) {
          this.id = void 0;
          this.res = void 0;
          this.removeConditionRes = undefined;
          this.removeConditionElems = null;
          this.forbinConditionRes = undefined;
          this.forbinConditionElems = null;
          this.triggerConditionRes = undefined;
          this.triggerConditionElems = null;
          this.time = 0;
          this.cycleTimes = 0;
          this.isOutside = void 0;
          this.forbin = false;
          this.stack = 0;
          this.self = void 0;
          this.id = Buff.incID++;
          this.res = data;
          this.isOutside = isOutside;
          this.self = self;

          if (this.res.removeCondition) {
            this.removeConditionRes = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).lubanTables.TbResSkillCondition.get(this.res.removeCondition);
            this.removeConditionElems = this.conditionRes2Elems(this.removeConditionRes);
          }

          if (this.res.forbinCondition) {
            this.forbinConditionRes = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).lubanTables.TbResSkillCondition.get(this.res.forbinCondition);
            this.forbinConditionElems = this.conditionRes2Elems(this.forbinConditionRes);
          }

          if (this.res.triggerCondition) {
            this.triggerConditionRes = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).lubanTables.TbResSkillCondition.get(this.res.triggerCondition);
            this.triggerConditionElems = this.conditionRes2Elems(this.triggerConditionRes);
          }
        }

        conditionRes2Elems(res) {
          if (!res) {
            return null;
          }

          let target = null;
          (_crd && forEachEntityByTargetType === void 0 ? (_reportPossibleCrUseOfforEachEntityByTargetType({
            error: Error()
          }), forEachEntityByTargetType) : forEachEntityByTargetType)(this.self, res.target, entity => {
            target = entity;
          });

          if (!target) {
            return null;
          }

          const validTarget = target;
          const elems = Array.from(res.conditions);

          for (let i = 0; i < elems.length; i++) {
            switch (elems[i].type) {
              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).FatalInjuryHurt:
                {
                  let elem = new (_crd && ExConditionEvent === void 0 ? (_reportPossibleCrUseOfExConditionEvent({
                    error: Error()
                  }), ExConditionEvent) : ExConditionEvent)(elems[i], () => {
                    elem.has = true;
                    this.update(0);
                  });
                  elems[i] = elem; // NOTE FatalInjuryHurt 只支持self， 要不Unregister不好删除

                  this.self.PlaneEventRegister((_crd && PlaneEventType === void 0 ? (_reportPossibleCrUseOfPlaneEventType({
                    error: Error()
                  }), PlaneEventType) : PlaneEventType).FatalInjuryHurt, elem.cb);
                }
                break;

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).PickDiamond:
                elems[i] = new (_crd && ExConditionNum === void 0 ? (_reportPossibleCrUseOfExConditionNum({
                  error: Error()
                }), ExConditionNum) : ExConditionNum)(elems[i], validTarget.pickDiamondNum);
                break;

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).KillEnemyNum:
                elems[i] = new (_crd && ExConditionNum === void 0 ? (_reportPossibleCrUseOfExConditionNum({
                  error: Error()
                }), ExConditionNum) : ExConditionNum)(elems[i], validTarget.killEnemyNum);
                break;

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).UseNuclear:
                elems[i] = new (_crd && ExConditionNum === void 0 ? (_reportPossibleCrUseOfExConditionNum({
                  error: Error()
                }), ExConditionNum) : ExConditionNum)(elems[i], validTarget.usedNuclearNum);
                break;

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).UserSuper:
                elems[i] = new (_crd && ExConditionNum === void 0 ? (_reportPossibleCrUseOfExConditionNum({
                  error: Error()
                }), ExConditionNum) : ExConditionNum)(elems[i], validTarget.usedSuperNum);
                break;
            }
          }

          return elems;
        }

        static checkCondition(self, res, elems) {
          let target = null;
          (_crd && forEachEntityByTargetType === void 0 ? (_reportPossibleCrUseOfforEachEntityByTargetType({
            error: Error()
          }), forEachEntityByTargetType) : forEachEntityByTargetType)(self, res.target, entity => {
            target = entity;
          });

          if (!target) {
            return false;
          }

          if (res.conditions.length == 0) {
            return true;
          }

          let ret = res.boolType == (_crd && BoolOpType === void 0 ? (_reportPossibleCrUseOfBoolOpType({
            error: Error()
          }), BoolOpType) : BoolOpType).AND ? true : false;
          elems == null || elems.forEach(condition => {
            let value = 0;

            switch (condition.type) {
              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).BuffStack:
                {
                  var _GetBuff;

                  if (condition.params.length < 1) {
                    break;
                  }

                  value = ((_GetBuff = target.buffComp.GetBuff(condition.params[0])) == null ? void 0 : _GetBuff.stack) || 0;
                }
                break;

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).CurHPPer:
                value = target.curHp / target.maxHp * 10000;
                break;

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).CurHP:
                value = target.curHp;
                break;

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).MaxHP:
                value = target.maxHp;
                break;

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).BeAttackTime:
                value = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                  error: Error()
                }), GameIns) : GameIns).gameDataManager.gameTime - target.hurtTime;
                break;

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).FatalInjuryHurt:
                if (condition instanceof (_crd && ExConditionEvent === void 0 ? (_reportPossibleCrUseOfExConditionEvent({
                  error: Error()
                }), ExConditionEvent) : ExConditionEvent)) {
                  value = condition.has ? 1 : 0;
                }

                break;

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).PickDiamond:
                if (condition instanceof (_crd && ExConditionNum === void 0 ? (_reportPossibleCrUseOfExConditionNum({
                  error: Error()
                }), ExConditionNum) : ExConditionNum)) {
                  value = target.pickDiamondNum - condition.num;
                }

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).KillEnemyNum:
              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).KillEnemy:
                if (condition instanceof (_crd && ExConditionNum === void 0 ? (_reportPossibleCrUseOfExConditionNum({
                  error: Error()
                }), ExConditionNum) : ExConditionNum)) {
                  value = target.killEnemyNum - condition.num;
                }

                break;

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).RemainNuclearNum:
                value = target.nuclearNum;
                break;

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).UsedNuclearNum:
                value = target.usedNuclearNum;
                break;

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).LevelStart:
                value = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                  error: Error()
                }), GameIns) : GameIns).gameDataManager.gameTime == 0 ? 1 : 0;
                break;

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).BossBeKilled:
                // TODO ybgg
                break;

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).UseNuclear:
                if (condition instanceof (_crd && ExConditionNum === void 0 ? (_reportPossibleCrUseOfExConditionNum({
                  error: Error()
                }), ExConditionNum) : ExConditionNum)) {
                  value = target.usedNuclearNum - condition.num;
                }

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).UserSuper:
                if (condition instanceof (_crd && ExConditionNum === void 0 ? (_reportPossibleCrUseOfExConditionNum({
                  error: Error()
                }), ExConditionNum) : ExConditionNum)) {
                  value = target.usedSuperNum - condition.num;
                }

                break;

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).GameTime:
                value = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                  error: Error()
                }), GameIns) : GameIns).gameDataManager.gameTime;
                break;

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).EnemyCount:
                if (condition.params.length >= 2) {
                  var _target;

                  const radiusSqr = condition.params[1] * condition.params[1];

                  if ((_target = target) != null && _target.enemy) {
                    if (Vec2.squaredDistance(target.node.position, (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                      error: Error()
                    }), GameIns) : GameIns).mainPlaneManager.mainPlane.node.position) <= radiusSqr) {
                      value++;
                    }
                  } else {
                    (_crd && forEachEntityByTargetType === void 0 ? (_reportPossibleCrUseOfforEachEntityByTargetType({
                      error: Error()
                    }), forEachEntityByTargetType) : forEachEntityByTargetType)(target, (_crd && TargetType === void 0 ? (_reportPossibleCrUseOfTargetType({
                      error: Error()
                    }), TargetType) : TargetType).Enemy, entity => {
                      if (entity.isDead && Vec2.squaredDistance(entity.node.position, target.node.position) <= radiusSqr) {
                        value++;
                      }
                    });
                  }
                }

                break;

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).WaveNo:
                // TODO ybgg
                // value = GameIns.battleManager.waveNo;
                break;

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).Distance:
                value = Vec2.distance(target.node.position, (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                  error: Error()
                }), GameIns) : GameIns).mainPlaneManager.mainPlane.node.position);
                break;

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).KillEnemy:
                if (condition instanceof (_crd && ExConditionNum === void 0 ? (_reportPossibleCrUseOfExConditionNum({
                  error: Error()
                }), ExConditionNum) : ExConditionNum)) {
                  value = target.killEnemyNum - condition.num;
                }

                break;
            }

            let ret2 = false;

            switch (condition.op) {
              case (_crd && CondOPType === void 0 ? (_reportPossibleCrUseOfCondOPType({
                error: Error()
              }), CondOPType) : CondOPType).EQ:
                ret2 = value == condition.value;
                break;

              case (_crd && CondOPType === void 0 ? (_reportPossibleCrUseOfCondOPType({
                error: Error()
              }), CondOPType) : CondOPType).GE:
                ret2 = value >= condition.value;
                break;

              case (_crd && CondOPType === void 0 ? (_reportPossibleCrUseOfCondOPType({
                error: Error()
              }), CondOPType) : CondOPType).GT:
                ret2 = value > condition.value;
                break;

              case (_crd && CondOPType === void 0 ? (_reportPossibleCrUseOfCondOPType({
                error: Error()
              }), CondOPType) : CondOPType).LE:
                ret2 = value <= condition.value;
                break;

              case (_crd && CondOPType === void 0 ? (_reportPossibleCrUseOfCondOPType({
                error: Error()
              }), CondOPType) : CondOPType).LT:
                ret2 = value < condition.value;
                break;

              case (_crd && CondOPType === void 0 ? (_reportPossibleCrUseOfCondOPType({
                error: Error()
              }), CondOPType) : CondOPType).NE:
                ret2 = value != condition.value;
                break;
            }

            if (res.boolType == (_crd && BoolOpType === void 0 ? (_reportPossibleCrUseOfBoolOpType({
              error: Error()
            }), BoolOpType) : BoolOpType).AND) {
              ret = ret && ret2;
            } else {
              ret = ret || ret2;
            }
          });
          return ret;
        }

        checkRemoveCondition() {
          if (this.res.duration != -1 && this.time >= this.res.duration) {
            return true;
          }

          if (!this.removeConditionRes) {
            return false;
          }

          return Buff.checkCondition(this.self, this.removeConditionRes, this.removeConditionElems);
        }

        resetTriggerConditionNum() {
          var _this$triggerConditio;

          (_this$triggerConditio = this.triggerConditionElems) == null || _this$triggerConditio.forEach(condition => {
            if (condition instanceof (_crd && ExCondition === void 0 ? (_reportPossibleCrUseOfExCondition({
              error: Error()
            }), ExCondition) : ExCondition)) {
              condition.reset();
            }
          });
        }

        resetConditionElems(elems) {
          elems == null || elems.forEach(condition => {
            switch (condition.type) {
              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).FatalInjuryHurt:
                if (condition instanceof (_crd && ExConditionEvent === void 0 ? (_reportPossibleCrUseOfExConditionEvent({
                  error: Error()
                }), ExConditionEvent) : ExConditionEvent)) {
                  this.self.PlaneEventUnRegister((_crd && PlaneEventType === void 0 ? (_reportPossibleCrUseOfPlaneEventType({
                    error: Error()
                  }), PlaneEventType) : PlaneEventType).FatalInjuryHurt, condition.cb);
                }

                break;
            }
          });
        }

        onRemove() {
          this.res.effects.forEach(applyEffect => {
            (_crd && forEachEntityByTargetType === void 0 ? (_reportPossibleCrUseOfforEachEntityByTargetType({
              error: Error()
            }), forEachEntityByTargetType) : forEachEntityByTargetType)(this.self, applyEffect.target, entity => {
              entity.RemoveBuffEffect(this, applyEffect);
            });
          });
          this.resetConditionElems(this.removeConditionElems);
        }

        update(dt) {
          this.time += dt * 1000;

          if (this.checkRemoveCondition()) {
            this.self.buffComp.removeBuff(this, this.res.id);
            return;
          }

          if (this.forbinConditionRes && Buff.checkCondition(this.self, this.forbinConditionRes, this.forbinConditionElems)) {
            if (!this.forbin) {
              this.res.effects.forEach(applyEffect => {
                (_crd && forEachEntityByTargetType === void 0 ? (_reportPossibleCrUseOfforEachEntityByTargetType({
                  error: Error()
                }), forEachEntityByTargetType) : forEachEntityByTargetType)(this.self, applyEffect.target, entity => {
                  entity.RemoveBuffEffect(this, applyEffect);
                });
              });
            }

            this.forbin = true;
          } else {
            if (this.forbin) {
              this.applyEffect();
            }

            this.forbin = false;
          }

          if (this.res.cycle > 0 && this.time >= (this.cycleTimes + 1) * this.res.cycle && (this.res.cycleTimes == 0 || this.cycleTimes < this.res.cycleTimes) || this.triggerConditionElems && Buff.checkCondition(this.self, this.triggerConditionRes, this.triggerConditionElems)) {
            this.cycleTimes++;
            this.resetTriggerConditionNum();
            this.applyEffect();
          }
        }

        applyEffect() {
          this.res.effects.forEach(applyEffect => {
            (_crd && forEachEntityByTargetType === void 0 ? (_reportPossibleCrUseOfforEachEntityByTargetType({
              error: Error()
            }), forEachEntityByTargetType) : forEachEntityByTargetType)(this.self, applyEffect.target, entity => {
              entity.ApplyBuffEffect(this, applyEffect);
            });
          });
        }

      });

      Buff.incID = 1;

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=c0f311f5f84efc46088b1336a7cbfa0b13e01b0b.js.map