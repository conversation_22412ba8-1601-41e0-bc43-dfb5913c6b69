System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, log, PathData, PathPoint, _dec, _class, _crd, ccclass, property, PathCurveTest;

  function _reportPossibleCrUseOfPathData(extras) {
    _reporterNs.report("PathData", "../data/PathData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathPoint(extras) {
    _reporterNs.report("PathPoint", "../data/PathData", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      log = _cc.log;
    }, function (_unresolved_2) {
      PathData = _unresolved_2.PathData;
      PathPoint = _unresolved_2.PathPoint;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "dcc63siMgNByp7mNZhfC2t7", "PathCurveTest", undefined);

      __checkObsolete__(['_decorator', 'Component', 'log']);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * 专门测试路径曲线生成的组件
       */

      _export("PathCurveTest", PathCurveTest = (_dec = ccclass('PathCurveTest'), _dec(_class = class PathCurveTest extends Component {
        onLoad() {
          this.testCurveVsLine();
        }
        /**
         * 测试曲线与直线的区别
         */


        testCurveVsLine() {
          log("=== 测试曲线与直线的区别 ==="); // 创建一个L形路径

          const pathData = new (_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
            error: Error()
          }), PathData) : PathData)();
          const point1 = new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(0, 0);
          point1.speed = 300;
          point1.smoothness = 1.0; // 高平滑度，应该产生曲线

          const point2 = new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(100, 0);
          point2.speed = 300;
          point2.smoothness = 1.0;
          const point3 = new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(100, 100);
          point3.speed = 300;
          point3.smoothness = 1.0;
          pathData.points.length = 0;
          pathData.points.push(point1, point2, point3);
          const subdivided = pathData.getSubdividedPoints();
          log(`总细分点数: ${subdivided.length}`); // 分析第一段（从point1到point2）

          let segment1Points = [];

          for (let i = 0; i < subdivided.length; i++) {
            const point = subdivided[i];

            if (point.x <= 100 && point.y <= 10) {
              // 第一段的点
              segment1Points.push(point);
            }
          }

          log(`第一段点数: ${segment1Points.length}`); // 分析第二段（从point2到point3）

          let segment2Points = [];

          for (let i = 0; i < subdivided.length; i++) {
            const point = subdivided[i];

            if (point.x >= 90 && point.y >= 0) {
              // 第二段的点
              segment2Points.push(point);
            }
          }

          log(`第二段点数: ${segment2Points.length}`); // 检查转角处是否有平滑过渡

          const cornerPoints = subdivided.filter(p => p.x > 80 && p.x < 120 && p.y > -20 && p.y < 20);
          log(`转角区域点数: ${cornerPoints.length}`);

          if (cornerPoints.length > 2) {
            log("转角区域的点:");
            cornerPoints.forEach((p, i) => {
              log(`  点${i}: (${p.x.toFixed(2)}, ${p.y.toFixed(2)})`);
            }); // 检查是否有平滑过渡（Y坐标应该有变化）

            const hasYVariation = cornerPoints.some(p => Math.abs(p.y) > 0.1);

            if (hasYVariation) {
              log("✓ 检测到曲线平滑过渡");
            } else {
              log("⚠ 转角处可能仍然是直线");
            }
          } // 输出前几个点的详细信息


          log("前10个点的详细信息:");

          for (let i = 0; i < Math.min(10, subdivided.length); i++) {
            const p = subdivided[i];
            log(`  点${i}: (${p.x.toFixed(2)}, ${p.y.toFixed(2)}) 速度:${p.speed.toFixed(1)}`);
          }
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=cd361d79b60d53a4f507e3af1e1b20694b3d78de.js.map