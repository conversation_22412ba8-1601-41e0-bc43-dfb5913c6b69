System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Button, Component, Label, Sprite, MyApp, MessageBox, DataMgr, _dec, _dec2, _dec3, _dec4, _dec5, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _crd, ccclass, property, MailCellUI;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/bundles/common/script/app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfResItem(extras) {
    _reporterNs.report("ResItem", "db://assets/bundles/common/script/autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfcsproto(extras) {
    _reporterNs.report("csproto", "db://assets/bundles/common/script/autogen/pb/cs_proto.js", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMessageBox(extras) {
    _reporterNs.report("MessageBox", "db://assets/bundles/common/script/ui/common/components/messagebox/MessageBox", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDataMgr(extras) {
    _reporterNs.report("DataMgr", "../../data/DataManager", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Button = _cc.Button;
      Component = _cc.Component;
      Label = _cc.Label;
      Sprite = _cc.Sprite;
    }, function (_unresolved_2) {
      MyApp = _unresolved_2.MyApp;
    }, function (_unresolved_3) {
      MessageBox = _unresolved_3.MessageBox;
    }, function (_unresolved_4) {
      DataMgr = _unresolved_4.DataMgr;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "037ecasVAtGQLeU6IMKSXVm", "MailCellUI", undefined);

      __checkObsolete__(['_decorator', 'Button', 'Component', 'Label', 'Sprite']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("MailCellUI", MailCellUI = (_dec = ccclass('MailCellUI'), _dec2 = property(Sprite), _dec3 = property(Label), _dec4 = property(Label), _dec5 = property(Button), _dec(_class = (_class2 = class MailCellUI extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "mailIcon", _descriptor, this);

          _initializerDefineProperty(this, "mailTitle", _descriptor2, this);

          _initializerDefineProperty(this, "mailContent", _descriptor3, this);

          _initializerDefineProperty(this, "btnClick", _descriptor4, this);

          this.itemID = 0;
          this.mailInfo = null;
        }

        start() {}

        update(deltaTime) {}

        onButtonClick() {
          (_crd && MessageBox === void 0 ? (_reportPossibleCrUseOfMessageBox({
            error: Error()
          }), MessageBox) : MessageBox).show('物品ID：' + this.itemID);

          if (this.itemID > 0) {
            (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
              error: Error()
            }), DataMgr) : DataMgr).mail.cmdMailGetAttachment([this.mailInfo.guid]);
          } else {
            (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
              error: Error()
            }), DataMgr) : DataMgr).mail.cmdMailDelete([this.mailInfo.guid]);
          }
        }

        setData(mailInfo) {
          this.mailInfo = mailInfo;

          if (!mailInfo.attachments || mailInfo.attachments.length === 0) {
            this.itemID = 0;
          } else {
            this.itemID = mailInfo.attachments[0].item_id;
          }

          var item = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanTables.TbResItem.get(this.itemID);
          this.mailTitle.string = (mailInfo == null ? void 0 : mailInfo.title) || ""; //this.mailContent!.string = mailInfo?.content || "";

          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.loadCoin(this.mailIcon);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "mailIcon", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "mailTitle", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "mailContent", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "btnClick", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=1093ac4567dcb083d0ee94ef221924b98d5315af.js.map