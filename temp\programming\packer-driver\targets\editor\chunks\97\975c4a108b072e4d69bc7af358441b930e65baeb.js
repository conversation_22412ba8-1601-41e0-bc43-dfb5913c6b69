System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9", "__unresolved_10", "__unresolved_11", "__unresolved_12", "__unresolved_13", "__unresolved_14", "__unresolved_15", "__unresolved_16", "__unresolved_17", "__unresolved_18", "__unresolved_19", "__unresolved_20", "__unresolved_21", "__unresolved_22"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, MyApp, BundleName, IBundleEntry, ResManager, UIMgr, logDebug, EventMgr, HomeUIEvent, FriendUI, DevLoginUI, BottomTab, BottomUI, HomeUI, TopUI, MailUI, PKUI, PlaneUI, ShopUI, SkyIslandUI, StoryUI, TalentUI, TTFUtils, _dec, _class, _crd, ccclass, CommonEntry;

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/bundles/common/script/app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBundleName(extras) {
    _reporterNs.report("BundleName", "db://assets/bundles/common/script/const/BundleConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIBundleEntry(extras) {
    _reporterNs.report("IBundleEntry", "db://assets/scripts/core/base/Bundle", _context.meta, extras);
  }

  function _reportPossibleCrUseOfResManager(extras) {
    _reporterNs.report("ResManager", "db://assets/scripts/core/base/ResManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogDebug(extras) {
    _reporterNs.report("logDebug", "db://assets/scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventMgr(extras) {
    _reporterNs.report("EventMgr", "./event/EventManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfHomeUIEvent(extras) {
    _reporterNs.report("HomeUIEvent", "./event/HomeUIEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFriendUI(extras) {
    _reporterNs.report("FriendUI", "./ui/friend/FriendUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDevLoginUI(extras) {
    _reporterNs.report("DevLoginUI", "./ui/gameui/DevLoginUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBottomTab(extras) {
    _reporterNs.report("BottomTab", "./ui/home/<USER>", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBottomUI(extras) {
    _reporterNs.report("BottomUI", "./ui/home/<USER>", _context.meta, extras);
  }

  function _reportPossibleCrUseOfHomeUI(extras) {
    _reporterNs.report("HomeUI", "./ui/home/<USER>", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTopUI(extras) {
    _reporterNs.report("TopUI", "./ui/home/<USER>", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMailUI(extras) {
    _reporterNs.report("MailUI", "./ui/mail/MailUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPKUI(extras) {
    _reporterNs.report("PKUI", "./ui/pk/PKUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneUI(extras) {
    _reporterNs.report("PlaneUI", "./ui/plane/PlaneUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfShopUI(extras) {
    _reporterNs.report("ShopUI", "./ui/shop/ShopUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfSkyIslandUI(extras) {
    _reporterNs.report("SkyIslandUI", "./ui/skyisland/SkyIslandUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfStoryUI(extras) {
    _reporterNs.report("StoryUI", "./ui/story/StoryUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTalentUI(extras) {
    _reporterNs.report("TalentUI", "./ui/talent/TalentUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTTFUtils(extras) {
    _reporterNs.report("TTFUtils", "./utils/TTFUtils", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_unresolved_2) {
      MyApp = _unresolved_2.MyApp;
    }, function (_unresolved_3) {
      BundleName = _unresolved_3.BundleName;
    }, function (_unresolved_4) {
      IBundleEntry = _unresolved_4.IBundleEntry;
    }, function (_unresolved_5) {
      ResManager = _unresolved_5.ResManager;
    }, function (_unresolved_6) {
      UIMgr = _unresolved_6.UIMgr;
    }, function (_unresolved_7) {
      logDebug = _unresolved_7.logDebug;
    }, function (_unresolved_8) {
      EventMgr = _unresolved_8.EventMgr;
    }, function (_unresolved_9) {
      HomeUIEvent = _unresolved_9.HomeUIEvent;
    }, function (_unresolved_10) {
      FriendUI = _unresolved_10.FriendUI;
    }, function (_unresolved_11) {
      DevLoginUI = _unresolved_11.DevLoginUI;
    }, function (_unresolved_12) {
      BottomTab = _unresolved_12.BottomTab;
    }, function (_unresolved_13) {
      BottomUI = _unresolved_13.BottomUI;
    }, function (_unresolved_14) {
      HomeUI = _unresolved_14.HomeUI;
    }, function (_unresolved_15) {
      TopUI = _unresolved_15.TopUI;
    }, function (_unresolved_16) {
      MailUI = _unresolved_16.MailUI;
    }, function (_unresolved_17) {
      PKUI = _unresolved_17.PKUI;
    }, function (_unresolved_18) {
      PlaneUI = _unresolved_18.PlaneUI;
    }, function (_unresolved_19) {
      ShopUI = _unresolved_19.ShopUI;
    }, function (_unresolved_20) {
      SkyIslandUI = _unresolved_20.SkyIslandUI;
    }, function (_unresolved_21) {
      StoryUI = _unresolved_21.StoryUI;
    }, function (_unresolved_22) {
      TalentUI = _unresolved_22.TalentUI;
    }, function (_unresolved_23) {
      TTFUtils = _unresolved_23.TTFUtils;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "da61ekdwgZH35AizU/MwbIF", "CommonEntry", undefined);

      __checkObsolete__(['_decorator']);

      ({
        ccclass
      } = _decorator);

      _export("CommonEntry", CommonEntry = (_dec = ccclass('CommonEntry'), _dec(_class = class CommonEntry extends (_crd && IBundleEntry === void 0 ? (_reportPossibleCrUseOfIBundleEntry({
        error: Error()
      }), IBundleEntry) : IBundleEntry) {
        async initEntry(...args) {
          (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
            error: Error()
          }), logDebug) : logDebug)("CommonEntry", "initEntry");
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).GetInstance().init();
          (_crd && TTFUtils === void 0 ? (_reportPossibleCrUseOfTTFUtils({
            error: Error()
          }), TTFUtils) : TTFUtils).getInstance().init((_crd && TTFUtils === void 0 ? (_reportPossibleCrUseOfTTFUtils({
            error: Error()
          }), TTFUtils) : TTFUtils).CUSTOM_TYPE.TEXT, "font/gamefont");
          await (_crd && ResManager === void 0 ? (_reportPossibleCrUseOfResManager({
            error: Error()
          }), ResManager) : ResManager).instance.loadBundle((_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).Luban); //优先加载完配置

          await (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanMgr.load();
          await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).loadUI(_crd && DevLoginUI === void 0 ? (_reportPossibleCrUseOfDevLoginUI({
            error: Error()
          }), DevLoginUI) : DevLoginUI);
          await (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.loadBundle((_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).Home);
          await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && HomeUI === void 0 ? (_reportPossibleCrUseOfHomeUI({
            error: Error()
          }), HomeUI) : HomeUI);
          await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && BottomUI === void 0 ? (_reportPossibleCrUseOfBottomUI({
            error: Error()
          }), BottomUI) : BottomUI);
          await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && TopUI === void 0 ? (_reportPossibleCrUseOfTopUI({
            error: Error()
          }), TopUI) : TopUI); //暂时这样 后面再优化

          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).on((_crd && HomeUIEvent === void 0 ? (_reportPossibleCrUseOfHomeUIEvent({
            error: Error()
          }), HomeUIEvent) : HomeUIEvent).Leave, () => {
            (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).closeUI(_crd && HomeUI === void 0 ? (_reportPossibleCrUseOfHomeUI({
              error: Error()
            }), HomeUI) : HomeUI);
            (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).closeUI(_crd && BottomUI === void 0 ? (_reportPossibleCrUseOfBottomUI({
              error: Error()
            }), BottomUI) : BottomUI); //这里会把下面的主界面都关闭  

            (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).closeUI(_crd && TopUI === void 0 ? (_reportPossibleCrUseOfTopUI({
              error: Error()
            }), TopUI) : TopUI);
          });
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).emit((_crd && HomeUIEvent === void 0 ? (_reportPossibleCrUseOfHomeUIEvent({
            error: Error()
          }), HomeUIEvent) : HomeUIEvent).BottomTabRegister, (_crd && BottomTab === void 0 ? (_reportPossibleCrUseOfBottomTab({
            error: Error()
          }), BottomTab) : BottomTab).Home, (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).get(_crd && HomeUI === void 0 ? (_reportPossibleCrUseOfHomeUI({
            error: Error()
          }), HomeUI) : HomeUI));
          this.preloadHomeSubBundles();
        }

        preloadHomeSubBundles() {
          //异步加载其他bundle
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.loadBundle((_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).HomePlane).then(async () => {
            await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).loadUI(_crd && PlaneUI === void 0 ? (_reportPossibleCrUseOfPlaneUI({
              error: Error()
            }), PlaneUI) : PlaneUI);
            (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
              error: Error()
            }), EventMgr) : EventMgr).emit((_crd && HomeUIEvent === void 0 ? (_reportPossibleCrUseOfHomeUIEvent({
              error: Error()
            }), HomeUIEvent) : HomeUIEvent).BottomTabRegister, (_crd && BottomTab === void 0 ? (_reportPossibleCrUseOfBottomTab({
              error: Error()
            }), BottomTab) : BottomTab).Plane, (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).get(_crd && PlaneUI === void 0 ? (_reportPossibleCrUseOfPlaneUI({
              error: Error()
            }), PlaneUI) : PlaneUI));
          });
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.loadBundle((_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).HomeTalent).then(async () => {
            await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).loadUI(_crd && TalentUI === void 0 ? (_reportPossibleCrUseOfTalentUI({
              error: Error()
            }), TalentUI) : TalentUI);
            (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
              error: Error()
            }), EventMgr) : EventMgr).emit((_crd && HomeUIEvent === void 0 ? (_reportPossibleCrUseOfHomeUIEvent({
              error: Error()
            }), HomeUIEvent) : HomeUIEvent).BottomTabRegister, (_crd && BottomTab === void 0 ? (_reportPossibleCrUseOfBottomTab({
              error: Error()
            }), BottomTab) : BottomTab).Talent, (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).get(_crd && TalentUI === void 0 ? (_reportPossibleCrUseOfTalentUI({
              error: Error()
            }), TalentUI) : TalentUI));
          });
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.loadBundle((_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).HomeShop).then(async () => {
            await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).loadUI(_crd && ShopUI === void 0 ? (_reportPossibleCrUseOfShopUI({
              error: Error()
            }), ShopUI) : ShopUI);
            (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
              error: Error()
            }), EventMgr) : EventMgr).emit((_crd && HomeUIEvent === void 0 ? (_reportPossibleCrUseOfHomeUIEvent({
              error: Error()
            }), HomeUIEvent) : HomeUIEvent).BottomTabRegister, (_crd && BottomTab === void 0 ? (_reportPossibleCrUseOfBottomTab({
              error: Error()
            }), BottomTab) : BottomTab).Shop, (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).get(_crd && ShopUI === void 0 ? (_reportPossibleCrUseOfShopUI({
              error: Error()
            }), ShopUI) : ShopUI));
          });
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.loadBundle((_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).HomeSkyIsland).then(async () => {
            await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).loadUI(_crd && SkyIslandUI === void 0 ? (_reportPossibleCrUseOfSkyIslandUI({
              error: Error()
            }), SkyIslandUI) : SkyIslandUI);
            (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
              error: Error()
            }), EventMgr) : EventMgr).emit((_crd && HomeUIEvent === void 0 ? (_reportPossibleCrUseOfHomeUIEvent({
              error: Error()
            }), HomeUIEvent) : HomeUIEvent).BottomTabRegister, (_crd && BottomTab === void 0 ? (_reportPossibleCrUseOfBottomTab({
              error: Error()
            }), BottomTab) : BottomTab).SkyIsLand, (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).get(_crd && SkyIslandUI === void 0 ? (_reportPossibleCrUseOfSkyIslandUI({
              error: Error()
            }), SkyIslandUI) : SkyIslandUI));
          });
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.loadBundle((_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).HomeFriend).then(async () => {
            await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).loadUI(_crd && FriendUI === void 0 ? (_reportPossibleCrUseOfFriendUI({
              error: Error()
            }), FriendUI) : FriendUI);
          });
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.loadBundle((_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).HomeMail).then(async () => {
            await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).loadUI(_crd && MailUI === void 0 ? (_reportPossibleCrUseOfMailUI({
              error: Error()
            }), MailUI) : MailUI);
          });
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.loadBundle((_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).HomePK).then(async () => {
            await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).loadUI(_crd && PKUI === void 0 ? (_reportPossibleCrUseOfPKUI({
              error: Error()
            }), PKUI) : PKUI);
          });
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.loadBundle((_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).HomeStory).then(async () => {
            await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).loadUI(_crd && StoryUI === void 0 ? (_reportPossibleCrUseOfStoryUI({
              error: Error()
            }), StoryUI) : StoryUI);
          });
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=975c4a108b072e4d69bc7af358441b930e65baeb.js.map