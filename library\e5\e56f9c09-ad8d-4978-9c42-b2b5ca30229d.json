[{"__type__": "cc.Prefab", "_name": "RandNod_mount_01", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "RandNod_mount_01", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 7}, {"__id__": 12}, {"__id__": 17}, {"__id__": 23}, {"__id__": 28}, {"__id__": 33}, {"__id__": 38}], "_active": true, "_components": [], "_prefab": {"__id__": 43}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 3}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 2}, "asset": {"__uuid__": "85577113-9f65-4a7c-9860-e1c7186552c3", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 4}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "d1uhbpfLdJ94taUZPwDl4f", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 5}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 6}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 216.881, "y": 197.279, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 8}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "d06534c6-6dd5-40ed-99b2-07b5fd76e8bf", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 9}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "d07S3LjJJIhJGBQj6YS8j4", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 10}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 11}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 173.734, "y": 653.698, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 13}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 12}, "asset": {"__uuid__": "0c493656-1d04-41e8-a3ff-62d8dc3db8a3", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 14}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "d5mppJbxFPHb6aEHj1d5O7", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 15}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 16}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 47.933, "y": 540.892, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 18}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 17}, "asset": {"__uuid__": "0c493656-1d04-41e8-a3ff-62d8dc3db8a3", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 19}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "e2+vwf6bFPJ7YUocnfibE2", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 20}, {"__id__": 22}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 21}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 125.389, "y": 608.471, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 21}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -1.635042447107558e-05}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 24}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 23}, "asset": {"__uuid__": "0c493656-1d04-41e8-a3ff-62d8dc3db8a3", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 25}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "2ezq3dKQVCZaHyPtD+TE8H", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 26}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 27}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -130.892, "y": 272.135, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 29}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 28}, "asset": {"__uuid__": "a4a203cb-0beb-4a25-8db7-91384aa74193", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 30}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "4c9YAQNRFPmqmZQWuTNvif", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 31}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 32}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -30.043, "y": 79.275, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 34}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 33}, "asset": {"__uuid__": "3f0eabf3-2b26-4403-8582-1818c448726d", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 35}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "7cwIbeYphCsa2q3NrC9JA1", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 36}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 37}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -327.394, "y": -454.061, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 39}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 38}, "asset": {"__uuid__": "9093ff23-4245-43e6-9095-f558379b7375", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 40}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "8dpNcbg8ZMPo03eS8Ograw", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 41}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 42}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -16.008, "y": -332.438, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "beMgVq4CxCRb8wqr6JFTc9", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": [{"__id__": 38}, {"__id__": 33}, {"__id__": 28}, {"__id__": 23}, {"__id__": 17}, {"__id__": 12}, {"__id__": 7}, {"__id__": 2}]}]