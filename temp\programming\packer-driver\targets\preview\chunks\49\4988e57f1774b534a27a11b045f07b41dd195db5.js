System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Layout, UITransform, BundleName, EventMgr, HomeUIEvent, ButtonPlus, BottomTab, BaseUI, UILayer, UIMgr, logDebug, BottomUIEvent, _dec, _dec2, _class, _class2, _descriptor, _crd, ccclass, property, BottomUI;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfBundleName(extras) {
    _reporterNs.report("BundleName", "db://assets/bundles/common/script/const/BundleConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventMgr(extras) {
    _reporterNs.report("EventMgr", "db://assets/bundles/common/script/event/EventManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfHomeUIEvent(extras) {
    _reporterNs.report("HomeUIEvent", "db://assets/bundles/common/script/event/HomeUIEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfButtonPlus(extras) {
    _reporterNs.report("ButtonPlus", "db://assets/bundles/common/script/ui/common/components/button/ButtonPlus", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBottomTab(extras) {
    _reporterNs.report("BottomTab", "db://assets/bundles/common/script/ui/home/<USER>", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBaseUI(extras) {
    _reporterNs.report("BaseUI", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUILayer(extras) {
    _reporterNs.report("UILayer", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogDebug(extras) {
    _reporterNs.report("logDebug", "db://assets/scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBottomUIEvent(extras) {
    _reporterNs.report("BottomUIEvent", "../../event/BottomUIEvent", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Layout = _cc.Layout;
      UITransform = _cc.UITransform;
    }, function (_unresolved_2) {
      BundleName = _unresolved_2.BundleName;
    }, function (_unresolved_3) {
      EventMgr = _unresolved_3.EventMgr;
    }, function (_unresolved_4) {
      HomeUIEvent = _unresolved_4.HomeUIEvent;
    }, function (_unresolved_5) {
      ButtonPlus = _unresolved_5.ButtonPlus;
    }, function (_unresolved_6) {
      BottomTab = _unresolved_6.BottomTab;
    }, function (_unresolved_7) {
      BaseUI = _unresolved_7.BaseUI;
      UILayer = _unresolved_7.UILayer;
      UIMgr = _unresolved_7.UIMgr;
    }, function (_unresolved_8) {
      logDebug = _unresolved_8.logDebug;
    }, function (_unresolved_9) {
      BottomUIEvent = _unresolved_9.BottomUIEvent;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "8ae4e9gWoVAipSQy6/1/OO1", "BottomUI", undefined);

      __checkObsolete__(['_decorator', 'EventTouch', 'Layout', 'math', 'UITransform']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("BottomUI", BottomUI = (_dec = ccclass('BottomUI'), _dec2 = property(Layout), _dec(_class = (_class2 = class BottomUI extends (_crd && BaseUI === void 0 ? (_reportPossibleCrUseOfBaseUI({
        error: Error()
      }), BaseUI) : BaseUI) {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "bottomLayer", _descriptor, this);

          this._moduleBtns = [];
          this._curClickTab = (_crd && BottomTab === void 0 ? (_reportPossibleCrUseOfBottomTab({
            error: Error()
          }), BottomTab) : BottomTab).Home;
          this._originSize = null;
          this._tabUIs = new Map();
        }

        static getUrl() {
          return "prefab/ui/BottomUI";
        }

        static getLayer() {
          return (_crd && UILayer === void 0 ? (_reportPossibleCrUseOfUILayer({
            error: Error()
          }), UILayer) : UILayer).Default;
        }

        static getBundleName() {
          return (_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).Home;
        }

        onLoad() {
          var moduleBtns = this.bottomLayer.node.getComponentsInChildren(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
            error: Error()
          }), ButtonPlus) : ButtonPlus);

          for (var i = 0; i < moduleBtns.length; i++) {
            var element = moduleBtns[i];
            element.addClick(this.onClick, this);

            this._moduleBtns.push(element);
          }

          this._originSize = this._moduleBtns[0].node.getComponent(UITransform).contentSize;
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).on((_crd && HomeUIEvent === void 0 ? (_reportPossibleCrUseOfHomeUIEvent({
            error: Error()
          }), HomeUIEvent) : HomeUIEvent).BottomTabRegister, this.onTabUIRegister, this);
          this.updateLayout(true);
          (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
            error: Error()
          }), logDebug) : logDebug)("BottomUI onLoad", "aaaaa");
        }

        updateLayout(immediate) {
          /*暂时不做表现，等策划定
          this._moduleBtns.forEach((btn, i) => {
              const isSelected = i === this._curClickTab;
              const targetScale = isSelected ? 1.2 : 0.8;
              if (immediate) {
                  btn.node.setScale(v3(targetScale, targetScale, 1));
              } else {
                  tween(btn.node)
                      .to(.3,
                          { scale: v3(targetScale, targetScale, 1) },
                          { easing: 'sineOut' }
                      )
                      .start();
              }
          });
           // 延迟更新Layout以确保动画完成后重新计算位置
          this.scheduleOnce(() => {
              this.bottomLayer?.updateLayout();
          }, immediate ? 0 : .3);
          */

          if (immediate === void 0) {
            immediate = false;
          }
        }

        updateClickState() {
          var curTab = this._tabUIs.get(this._curClickTab);

          if (!curTab) {
            return;
          }

          Object.values(_crd && BottomTab === void 0 ? (_reportPossibleCrUseOfBottomTab({
            error: Error()
          }), BottomTab) : BottomTab).forEach(v => {
            if (v == this._curClickTab) return;

            var baseUI = this._tabUIs.get(Number(v));

            if (!baseUI) {
              return;
            }

            (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).hideUI(baseUI.uiClass);
          });
          this.updateLayout(true);
        }

        onClick(event) {
          var _this = this;

          return _asyncToGenerator(function* () {
            var index = _this._moduleBtns.findIndex(v => {
              return v.node === event.target;
            });

            var oldTab = _this._curClickTab;
            if (index == _this._curClickTab) return;
            _this._curClickTab = index;

            var curTab = _this._tabUIs.get(_this._curClickTab);

            if (!curTab) {
              return;
            }

            yield (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).openUI(curTab.uiClass);
            (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
              error: Error()
            }), EventMgr) : EventMgr).emit((_crd && BottomUIEvent === void 0 ? (_reportPossibleCrUseOfBottomUIEvent({
              error: Error()
            }), BottomUIEvent) : BottomUIEvent).SwitchPanel, oldTab, _this._curClickTab);

            _this.updateClickState();
          })();
        }

        onTabUIRegister(tab, baseUI) {
          this._tabUIs.set(tab, baseUI);
        }

        onShow() {
          return _asyncToGenerator(function* () {})();
        }

        onHide() {
          var _this2 = this;

          return _asyncToGenerator(function* () {
            _this2._tabUIs.forEach(v => {
              (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
                error: Error()
              }), UIMgr) : UIMgr).hideUI(v.uiClass);
            });
          })();
        }

        onClose() {
          var _this3 = this;

          return _asyncToGenerator(function* () {
            _this3._tabUIs.forEach(v => {
              (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
                error: Error()
              }), UIMgr) : UIMgr).closeUI(v.uiClass);
            });
          })();
        }

        update(dt) {}

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "bottomLayer", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=4988e57f1774b534a27a11b045f07b41dd195db5.js.map