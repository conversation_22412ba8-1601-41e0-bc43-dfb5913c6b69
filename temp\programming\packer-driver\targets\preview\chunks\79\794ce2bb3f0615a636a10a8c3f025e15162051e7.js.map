{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/move/PathMove.ts"], "names": ["_decorator", "misc", "Vec2", "JsonAsset", "MoveBase", "PathData", "degreesToRadians", "radiansToDegrees", "ccclass", "property", "executeInEditMode", "PathMove", "type", "displayName", "tooltip", "_pathAsset", "_pathData", "_subdivided", "_totalDistance", "_distances", "_currentDistance", "_currentPointIndex", "_segmentT", "_tiltTime", "_isStaying", "_stayTimer", "_stayPointIndex", "_updateInEditor", "_speedMultiplier", "pathAsset", "value", "set<PERSON>ath", "fromJSON", "json", "onFocusInEditor", "resetToStart", "onLostFocusInEditor", "update", "dt", "tick", "pathData", "getSubdividedPoints", "calculateDistances", "i", "length", "distance", "position", "push", "_isMovable", "updatePosition", "currentSpeed", "getCurrentSpeed", "deltaDistance", "reverse", "loop", "updateCurrentPointIndex", "checkForStayPoint", "currentPoint", "getCurrentPathPointData", "stayDuration", "tolerance", "currentPos", "getCurrentPosition", "pointIndex", "indexOf", "Math", "max", "searchStart", "found", "segmentLength", "min", "speed", "startSpeed", "endSpeed", "interpolatedSpeed", "getPositionAtDistance", "tiltSpeed", "tiltOffset", "direction", "getDirectionAtDistance", "lengthSqr", "perpX", "y", "perpY", "x", "tiltAmount", "sin", "node", "setPosition", "_visibility<PERSON><PERSON><PERSON><PERSON>ounter", "VISIBILITY_CHECK_INTERVAL", "checkVisibility", "abs", "clone", "segmentStart", "segmentEnd", "t", "lerp", "startPos", "endPos", "epsilon", "pos1", "pos2", "subtract", "normalize", "setMovable", "movable", "setProgress", "progress", "getProgress", "moveToEnd", "getCurrentPathInfo", "pathPoint", "totalDistance", "setSpeedMultiplier", "multiplier", "getSpeedMultiplier", "isStaying", "getRemainingStayTime", "endStay"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAA6BC,MAAAA,I,OAAAA,I;AAAyBC,MAAAA,I,OAAAA,I;AAAYC,MAAAA,S,OAAAA,S;;AAElEC,MAAAA,Q,iBAAAA,Q;;AAEAC,MAAAA,Q,iBAAAA,Q;;;;;;;;;OAEH;AAAEC,QAAAA,gBAAF;AAAoBC,QAAAA;AAApB,O,GAAyCN,I;OACzC;AAAEO,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CV,U;;0BAIpCW,Q,WAFZH,OAAO,CAAC,UAAD,C,UACPE,iBAAiB,E,UAGbD,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAET,SAAR;AAAmBU,QAAAA,WAAW,EAAE;AAAhC,OAAD,C,UAWRJ,QAAQ,CAAC;AAAEI,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAGRJ,QAAQ,CAAC;AAAEI,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAGRJ,QAAQ,CAAC;AAAEI,QAAAA,WAAW,EAAE,QAAf;AAAyBC,QAAAA,OAAO,EAAE;AAAlC,OAAD,C,UAGRL,QAAQ,CAAC;AAAEI,QAAAA,WAAW,EAAE,QAAf;AAAyBC,QAAAA,OAAO,EAAE;AAAlC,OAAD,C,0CAxBb,MAEaH,QAFb;AAAA;AAAA,gCAEuC;AAAA;AAAA;AAAA,eAC5BI,UAD4B,GACG,IADH;;AAAA;;AAAA;;AAAA;;AAAA;;AAyBnC;AAzBmC,eA0B3BC,SA1B2B,GA0BE,IA1BF;AAAA,eA2B3BC,WA3B2B,GA2BA,EA3BA;AA2BI;AA3BJ,eA4B3BC,cA5B2B,GA4BF,CA5BE;AAAA,eA6B3BC,UA7B2B,GA6BJ,EA7BI;AA+BnC;AA/BmC,eAgC3BC,gBAhC2B,GAgCA,CAhCA;AAAA,eAiC3BC,kBAjC2B,GAiCE,CAjCF;AAiCK;AAjCL,eAkC3BC,SAlC2B,GAkCP,CAlCO;AAkCJ;AAlCI,eAmC3BC,SAnC2B,GAmCP,CAnCO;AAqCnC;AArCmC,eAsC3BC,UAtC2B,GAsCL,KAtCK;AAsCE;AAtCF,eAuC3BC,UAvC2B,GAuCN,CAvCM;AAuCH;AAvCG,eAwC3BC,eAxC2B,GAwCD,CAAC,CAxCA;AAwCG;AAxCH,eA0C3BC,eA1C2B,GA0CA,KA1CA;;AAianC;AACJ;AACA;AAnauC,eAoa3BC,gBApa2B,GAoaA,GApaA;AAAA;;AAGf,YAATC,SAAS,GAAqB;AACrC,iBAAO,KAAKd,UAAZ;AACH;;AACmB,YAATc,SAAS,CAACC,KAAD,EAAmB;AACnC,eAAKf,UAAL,GAAkBe,KAAlB;;AACA,cAAIA,KAAJ,EAAW;AACP,iBAAKC,OAAL,CAAa;AAAA;AAAA,sCAASC,QAAT,CAAkBF,KAAK,CAACG,IAAxB,CAAb;AACH;AACJ;;AAgCMC,QAAAA,eAAe,GAAG;AACrB,eAAKP,eAAL,GAAuB,IAAvB;AACA,eAAKQ,YAAL;AACH;;AAEMC,QAAAA,mBAAmB,GAAG;AACzB,eAAKT,eAAL,GAAuB,KAAvB;AACH;;AAEMU,QAAAA,MAAM,CAACC,EAAD,EAAa;AACtB,cAAI,KAAKX,eAAT,EAA0B;AACtB,iBAAKY,IAAL,CAAUD,EAAV;AACH;AACJ;AAED;AACJ;AACA;;;AACWP,QAAAA,OAAO,CAACS,QAAD,EAAqB;AAC/B,eAAKxB,SAAL,GAAiBwB,QAAjB,CAD+B,CAE/B;;AACA,eAAKvB,WAAL,GAAmB,KAAKD,SAAL,CAAeyB,mBAAf,EAAnB,CAH+B,CAI/B;;AACA,eAAKC,kBAAL;AACH;AAED;AACJ;AACA;;;AACYA,QAAAA,kBAAkB,GAAG;AACzB,eAAKvB,UAAL,GAAkB,CAAC,CAAD,CAAlB;AACA,eAAKD,cAAL,GAAsB,CAAtB;;AAEA,eAAK,IAAIyB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK1B,WAAL,CAAiB2B,MAArC,EAA6CD,CAAC,EAA9C,EAAkD;AAC9C,gBAAME,QAAQ,GAAG3C,IAAI,CAAC2C,QAAL,CAAc,KAAK5B,WAAL,CAAiB0B,CAAC,GAAG,CAArB,EAAwBG,QAAtC,EAAgD,KAAK7B,WAAL,CAAiB0B,CAAjB,EAAoBG,QAApE,CAAjB;AACA,iBAAK5B,cAAL,IAAuB2B,QAAvB;;AACA,iBAAK1B,UAAL,CAAgB4B,IAAhB,CAAqB,KAAK7B,cAA1B;AACH;AACJ;AAED;AACJ;AACA;;;AACWqB,QAAAA,IAAI,CAACD,EAAD,EAAmB;AAC1B,cAAI,CAAC,KAAKU,UAAN,IAAoB,KAAK/B,WAAL,CAAiB2B,MAAjB,GAA0B,CAAlD,EAAqD,OAD3B,CAG1B;;AACA,cAAI,KAAKpB,UAAT,EAAqB;AACjB,iBAAKC,UAAL,IAAmBa,EAAnB;;AACA,gBAAI,KAAKb,UAAL,IAAmB,CAAvB,EAA0B;AACtB,mBAAKD,UAAL,GAAkB,KAAlB;AACA,mBAAKE,eAAL,GAAuB,CAAC,CAAxB;AACH,aAHD,MAGO;AACH;AACA,mBAAKuB,cAAL,CAAoBX,EAApB;AACA;AACH;AACJ,WAdyB,CAgB1B;;;AACA,cAAMY,YAAY,GAAG,KAAKC,eAAL,EAArB,CAjB0B,CAmB1B;;AACA,cAAMC,aAAa,GAAGF,YAAY,GAAGZ,EAArC;;AAEA,cAAI,KAAKe,OAAT,EAAkB;AACd,iBAAKjC,gBAAL,IAAyBgC,aAAzB;;AACA,gBAAI,KAAKhC,gBAAL,GAAwB,CAA5B,EAA+B;AAC3B,kBAAI,KAAKkC,IAAT,EAAe;AACX,qBAAKlC,gBAAL,GAAwB,KAAKF,cAAL,GAAsB,KAAKE,gBAAnD;AACH,eAFD,MAEO;AACH,qBAAKA,gBAAL,GAAwB,CAAxB;AACH;AACJ;AACJ,WATD,MASO;AACH,iBAAKA,gBAAL,IAAyBgC,aAAzB;;AACA,gBAAI,KAAKhC,gBAAL,GAAwB,KAAKF,cAAjC,EAAiD;AAC7C,kBAAI,KAAKoC,IAAT,EAAe;AACX,qBAAKlC,gBAAL,GAAwB,KAAKA,gBAAL,GAAwB,KAAKF,cAArD;AACH,eAFD,MAEO;AACH,qBAAKE,gBAAL,GAAwB,KAAKF,cAA7B;AACH;AACJ;AACJ;;AAED,eAAKqC,uBAAL;AACA,eAAKC,iBAAL;AACA,eAAKP,cAAL,CAAoBX,EAApB;AACH;AAED;AACJ;AACA;;;AACYkB,QAAAA,iBAAiB,GAAS;AAC9B,cAAI,KAAKhC,UAAL,IAAmB,KAAKP,WAAL,CAAiB2B,MAAjB,KAA4B,CAAnD,EAAsD,OADxB,CAG9B;;AACA,cAAMa,YAAY,GAAG,KAAKC,uBAAL,EAArB;AACA,cAAI,CAACD,YAAD,IAAiBA,YAAY,CAACE,YAAb,IAA6B,CAAlD,EAAqD,OALvB,CAO9B;;AACA,cAAMC,SAAS,GAAG,GAAlB,CAR8B,CAQP;;AACvB,cAAMC,UAAU,GAAG,KAAKC,kBAAL,EAAnB;AACA,cAAMjB,QAAQ,GAAG3C,IAAI,CAAC2C,QAAL,CAAcgB,UAAd,EAA0BJ,YAAY,CAACX,QAAvC,CAAjB,CAV8B,CAY9B;;AACA,cAAMiB,UAAU,GAAG,KAAK9C,WAAL,CAAiB+C,OAAjB,CAAyBP,YAAzB,CAAnB;;AAEA,cAAIZ,QAAQ,IAAIe,SAAZ,IAAyB,KAAKlC,eAAL,KAAyBqC,UAAtD,EAAkE;AAC9D;AACA,iBAAKvC,UAAL,GAAkB,IAAlB;AACA,iBAAKC,UAAL,GAAkBgC,YAAY,CAACE,YAAb,GAA4B,MAA9C,CAH8D,CAGR;;AACtD,iBAAKjC,eAAL,GAAuBqC,UAAvB;AACH;AACJ;AAED;AACJ;AACA;;;AACYR,QAAAA,uBAAuB,GAAG;AAC9B,cAAI,KAAKtC,WAAL,CAAiB2B,MAAjB,KAA4B,CAAhC,EAAmC,OADL,CAG9B;;AACA,cAAI,KAAKxB,gBAAL,IAAyB,CAA7B,EAAgC;AAC5B,iBAAKC,kBAAL,GAA0B,CAA1B;AACA,iBAAKC,SAAL,GAAiB,CAAjB;AACA;AACH;;AAED,cAAI,KAAKF,gBAAL,IAAyB,KAAKF,cAAlC,EAAkD;AAC9C,iBAAKG,kBAAL,GAA0B4C,IAAI,CAACC,GAAL,CAAS,CAAT,EAAY,KAAKjD,WAAL,CAAiB2B,MAAjB,GAA0B,CAAtC,CAA1B;AACA,iBAAKtB,SAAL,GAAiB,CAAjB;AACA;AACH,WAd6B,CAgB9B;;;AACA,cAAI6C,WAAW,GAAGF,IAAI,CAACC,GAAL,CAAS,CAAT,EAAY,KAAK7C,kBAAL,GAA0B,CAAtC,CAAlB;AACA,cAAI+C,KAAK,GAAG,KAAZ,CAlB8B,CAoB9B;;AACA,eAAK,IAAIzB,CAAC,GAAGwB,WAAb,EAA0BxB,CAAC,GAAG,KAAKxB,UAAL,CAAgByB,MAAhB,GAAyB,CAAvD,EAA0DD,CAAC,EAA3D,EAA+D;AAC3D,gBAAI,KAAKvB,gBAAL,IAAyB,KAAKD,UAAL,CAAgBwB,CAAhB,CAAzB,IAA+C,KAAKvB,gBAAL,IAAyB,KAAKD,UAAL,CAAgBwB,CAAC,GAAG,CAApB,CAA5E,EAAoG;AAChG,mBAAKtB,kBAAL,GAA0BsB,CAA1B;AACA,kBAAM0B,aAAa,GAAG,KAAKlD,UAAL,CAAgBwB,CAAC,GAAG,CAApB,IAAyB,KAAKxB,UAAL,CAAgBwB,CAAhB,CAA/C;AACA,mBAAKrB,SAAL,GAAiB+C,aAAa,GAAG,CAAhB,GAAoB,CAAC,KAAKjD,gBAAL,GAAwB,KAAKD,UAAL,CAAgBwB,CAAhB,CAAzB,IAA+C0B,aAAnE,GAAmF,CAApG;AACAD,cAAAA,KAAK,GAAG,IAAR;AACA;AACH;AACJ,WA7B6B,CA+B9B;;;AACA,cAAI,CAACA,KAAL,EAAY;AACR,iBAAK,IAAIzB,EAAC,GAAGsB,IAAI,CAACK,GAAL,CAASH,WAAT,EAAsB,KAAKhD,UAAL,CAAgByB,MAAhB,GAAyB,CAA/C,CAAb,EAAgED,EAAC,IAAI,CAArE,EAAwEA,EAAC,EAAzE,EAA6E;AACzE,kBAAI,KAAKvB,gBAAL,IAAyB,KAAKD,UAAL,CAAgBwB,EAAhB,CAAzB,IAA+C,KAAKvB,gBAAL,IAAyB,KAAKD,UAAL,CAAgBwB,EAAC,GAAG,CAApB,CAA5E,EAAoG;AAChG,qBAAKtB,kBAAL,GAA0BsB,EAA1B;;AACA,oBAAM0B,cAAa,GAAG,KAAKlD,UAAL,CAAgBwB,EAAC,GAAG,CAApB,IAAyB,KAAKxB,UAAL,CAAgBwB,EAAhB,CAA/C;;AACA,qBAAKrB,SAAL,GAAiB+C,cAAa,GAAG,CAAhB,GAAoB,CAAC,KAAKjD,gBAAL,GAAwB,KAAKD,UAAL,CAAgBwB,EAAhB,CAAzB,IAA+C0B,cAAnE,GAAmF,CAApG;AACA;AACH;AACJ;AACJ;AACJ;AAED;AACJ;AACA;;;AACYlB,QAAAA,eAAe,GAAW;AAC9B,cAAI,KAAKlC,WAAL,CAAiB2B,MAAjB,KAA4B,CAAhC,EAAmC;AAC/B,mBAAO,KAAK2B,KAAZ,CAD+B,CACZ;AACtB,WAH6B,CAK9B;;;AACA,cAAI,KAAKlD,kBAAL,IAA2B,KAAKJ,WAAL,CAAiB2B,MAAjB,GAA0B,CAAzD,EAA4D;AACxD,mBAAO,KAAK3B,WAAL,CAAiB,KAAKA,WAAL,CAAiB2B,MAAjB,GAA0B,CAA3C,EAA8C2B,KAA9C,GAAsD,KAAK3C,gBAAlE;AACH,WAR6B,CAU9B;;;AACA,cAAM4C,UAAU,GAAG,KAAKvD,WAAL,CAAiB,KAAKI,kBAAtB,EAA0CkD,KAA7D;AACA,cAAME,QAAQ,GAAG,KAAKxD,WAAL,CAAiB,KAAKI,kBAAL,GAA0B,CAA3C,EAA8CkD,KAA/D,CAZ8B,CAc9B;;AACA,cAAMG,iBAAiB,GAAGF,UAAU,GAAG,CAACC,QAAQ,GAAGD,UAAZ,IAA0B,KAAKlD,SAAtE;AACA,iBAAOoD,iBAAiB,GAAG,KAAK9C,gBAAhC;AACH;AAED;AACJ;AACA;;;AACYqB,QAAAA,cAAc,CAACX,EAAD,EAAa;AAC/B,cAAMQ,QAAQ,GAAG,KAAK6B,qBAAL,CAA2B,KAAKvD,gBAAhC,CAAjB,CAD+B,CAG/B;;AACA,cAAI,KAAKwD,SAAL,GAAiB,CAAjB,IAAsB,KAAKC,UAAL,GAAkB,CAA5C,EAA+C;AAC3C,iBAAKtD,SAAL,IAAkBe,EAAlB;AAEA,gBAAMwC,SAAS,GAAG,KAAKC,sBAAL,CAA4B,KAAK3D,gBAAjC,CAAlB;;AACA,gBAAI0D,SAAS,CAACE,SAAV,KAAwB,KAA5B,EAAmC;AAC/B;AACA,kBAAMC,KAAK,GAAG,CAACH,SAAS,CAACI,CAAzB;AACA,kBAAMC,KAAK,GAAGL,SAAS,CAACM,CAAxB,CAH+B,CAK/B;;AACA,kBAAMC,UAAU,GAAGpB,IAAI,CAACqB,GAAL,CAAS,KAAK/D,SAAL,GAAiB,KAAKqD,SAA/B,IAA4C,KAAKC,UAApE;AAEA/B,cAAAA,QAAQ,CAACsC,CAAT,IAAcH,KAAK,GAAGI,UAAtB;AACAvC,cAAAA,QAAQ,CAACoC,CAAT,IAAcC,KAAK,GAAGE,UAAtB;AACH;AACJ;;AAED,eAAKE,IAAL,CAAUC,WAAV,CAAsB1C,QAAQ,CAACsC,CAA/B,EAAkCtC,QAAQ,CAACoC,CAA3C,EAA8C,CAA9C,EArB+B,CAuB/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;AACA,cAAI,EAAE,KAAKO,uBAAP,IAAkC9E,QAAQ,CAAC+E,yBAA/C,EAA0E;AACtE,iBAAKD,uBAAL,GAA+B,CAA/B;AACA,iBAAKE,eAAL;AACH;AACJ;AAED;AACJ;AACA;;;AACYhB,QAAAA,qBAAqB,CAAC9B,QAAD,EAAyB;AAClD;AACA,cAAIoB,IAAI,CAAC2B,GAAL,CAAS/C,QAAQ,GAAG,KAAKzB,gBAAzB,IAA6C,KAAjD,EAAwD;AACpD,mBAAO,KAAK0C,kBAAL,EAAP;AACH,WAJiD,CAMlD;;;AACA,cAAI,KAAK7C,WAAL,CAAiB2B,MAAjB,KAA4B,CAAhC,EAAmC,OAAO,IAAI1C,IAAJ,EAAP;AACnC,cAAI2C,QAAQ,IAAI,CAAhB,EAAmB,OAAO,KAAK5B,WAAL,CAAiB,CAAjB,EAAoB6B,QAApB,CAA6B+C,KAA7B,EAAP;AACnB,cAAIhD,QAAQ,IAAI,KAAK3B,cAArB,EAAqC,OAAO,KAAKD,WAAL,CAAiB,KAAKA,WAAL,CAAiB2B,MAAjB,GAA0B,CAA3C,EAA8CE,QAA9C,CAAuD+C,KAAvD,EAAP;;AAErC,eAAK,IAAIlD,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKxB,UAAL,CAAgByB,MAApC,EAA4CD,CAAC,EAA7C,EAAiD;AAC7C,gBAAIE,QAAQ,IAAI,KAAK1B,UAAL,CAAgBwB,CAAhB,CAAhB,EAAoC;AAChC,kBAAMmD,YAAY,GAAG,KAAK3E,UAAL,CAAgBwB,CAAC,GAAG,CAApB,CAArB;AACA,kBAAMoD,UAAU,GAAG,KAAK5E,UAAL,CAAgBwB,CAAhB,CAAnB;AACA,kBAAM0B,aAAa,GAAG0B,UAAU,GAAGD,YAAnC;AAEA,kBAAIzB,aAAa,KAAK,CAAtB,EAAyB,OAAO,KAAKpD,WAAL,CAAiB0B,CAAC,GAAG,CAArB,EAAwBG,QAAxB,CAAiC+C,KAAjC,EAAP;AAEzB,kBAAMG,CAAC,GAAG,CAACnD,QAAQ,GAAGiD,YAAZ,IAA4BzB,aAAtC;AACA,qBAAOnE,IAAI,CAAC+F,IAAL,CAAU,IAAI/F,IAAJ,EAAV,EAAsB,KAAKe,WAAL,CAAiB0B,CAAC,GAAG,CAArB,EAAwBG,QAA9C,EAAwD,KAAK7B,WAAL,CAAiB0B,CAAjB,EAAoBG,QAA5E,EAAsFkD,CAAtF,CAAP;AACH;AACJ;;AAED,iBAAO,KAAK/E,WAAL,CAAiB,KAAKA,WAAL,CAAiB2B,MAAjB,GAA0B,CAA3C,EAA8CE,QAA9C,CAAuD+C,KAAvD,EAAP;AACH;AAED;AACJ;AACA;;;AACY/B,QAAAA,kBAAkB,GAAS;AAC/B,cAAI,KAAK7C,WAAL,CAAiB2B,MAAjB,KAA4B,CAAhC,EAAmC,OAAO,IAAI1C,IAAJ,EAAP,CADJ,CAG/B;;AACA,cAAI,KAAKmB,kBAAL,IAA2B,KAAKJ,WAAL,CAAiB2B,MAAjB,GAA0B,CAAzD,EAA4D;AACxD,mBAAO,KAAK3B,WAAL,CAAiB,KAAKA,WAAL,CAAiB2B,MAAjB,GAA0B,CAA3C,EAA8CE,QAA9C,CAAuD+C,KAAvD,EAAP;AACH,WAN8B,CAQ/B;;;AACA,cAAMK,QAAQ,GAAG,KAAKjF,WAAL,CAAiB,KAAKI,kBAAtB,EAA0CyB,QAA3D;AACA,cAAMqD,MAAM,GAAG,KAAKlF,WAAL,CAAiB,KAAKI,kBAAL,GAA0B,CAA3C,EAA8CyB,QAA7D;AAEA,iBAAO5C,IAAI,CAAC+F,IAAL,CAAU,IAAI/F,IAAJ,EAAV,EAAsBgG,QAAtB,EAAgCC,MAAhC,EAAwC,KAAK7E,SAA7C,CAAP;AACH;AAED;AACJ;AACA;;;AACYyD,QAAAA,sBAAsB,CAAClC,QAAD,EAAyB;AACnD,cAAMuD,OAAO,GAAG,CAAhB;AACA,cAAMC,IAAI,GAAG,KAAK1B,qBAAL,CAA2B9B,QAA3B,CAAb;AACA,cAAMyD,IAAI,GAAG,KAAK3B,qBAAL,CAA2B9B,QAAQ,GAAGuD,OAAtC,CAAb;AAEA,iBAAOlG,IAAI,CAACqG,QAAL,CAAc,IAAIrG,IAAJ,EAAd,EAA0BoG,IAA1B,EAAgCD,IAAhC,EAAsCG,SAAtC,EAAP;AACH,SAxUkC,CA0UnC;;;AACOC,QAAAA,UAAU,CAACC,OAAD,EAA6B;AAC1C,eAAK1D,UAAL,GAAkB0D,OAAlB;AACA,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACWC,QAAAA,WAAW,CAACC,QAAD,EAA6B;AAC3C,eAAKxF,gBAAL,GAAwB6C,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYD,IAAI,CAACK,GAAL,CAAS,CAAT,EAAYsC,QAAZ,CAAZ,IAAqC,KAAK1F,cAAlE;AACA,eAAKqC,uBAAL,GAF2C,CAEX;;AAChC,eAAK/B,UAAL,GAAkB,KAAlB;AACA,eAAKC,UAAL,GAAkB,CAAlB;AACA,eAAKC,eAAL,GAAuB,CAAC,CAAxB;AACA,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACWmF,QAAAA,WAAW,GAAW;AACzB,iBAAO,KAAK3F,cAAL,GAAsB,CAAtB,GAA0B,KAAKE,gBAAL,GAAwB,KAAKF,cAAvD,GAAwE,CAA/E;AACH;AAED;AACJ;AACA;;;AACWiB,QAAAA,YAAY,GAAa;AAC5B,eAAKf,gBAAL,GAAwB,CAAxB;AACA,eAAKC,kBAAL,GAA0B,CAA1B;AACA,eAAKC,SAAL,GAAiB,CAAjB;AACA,eAAKE,UAAL,GAAkB,KAAlB;AACA,eAAKC,UAAL,GAAkB,CAAlB;AACA,eAAKC,eAAL,GAAuB,CAAC,CAAxB;AACA,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACWoF,QAAAA,SAAS,GAAa;AACzB,eAAK1F,gBAAL,GAAwB,KAAKF,cAA7B;AACA,eAAKqC,uBAAL,GAFyB,CAEO;;AAChC,eAAK/B,UAAL,GAAkB,KAAlB;AACA,eAAKC,UAAL,GAAkB,CAAlB;AACA,eAAKC,eAAL,GAAuB,CAAC,CAAxB;AACA,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACWgC,QAAAA,uBAAuB,GAAqB;AAC/C,cAAI,KAAKzC,WAAL,CAAiB2B,MAAjB,KAA4B,CAAhC,EAAmC,OAAO,IAAP,CADY,CAG/C;;AACA,cAAI,KAAKvB,kBAAL,IAA2B,KAAKJ,WAAL,CAAiB2B,MAAjB,GAA0B,CAAzD,EAA4D;AACxD,mBAAO,KAAK3B,WAAL,CAAiB,KAAKA,WAAL,CAAiB2B,MAAjB,GAA0B,CAA3C,CAAP;AACH,WAN8C,CAQ/C;;;AACA,iBAAO,KAAKtB,SAAL,GAAiB,GAAjB,GACH,KAAKL,WAAL,CAAiB,KAAKI,kBAAtB,CADG,GAEH,KAAKJ,WAAL,CAAiB,KAAKI,kBAAL,GAA0B,CAA3C,CAFJ;AAGH;AAED;AACJ;AACA;;;AACW0F,QAAAA,kBAAkB,GAAG;AACxB,cAAI,KAAK9F,WAAL,CAAiB2B,MAAjB,KAA4B,CAAhC,EAAmC,OAAO,IAAP;AAEnC,cAAMa,YAAY,GAAG,KAAKC,uBAAL,EAArB;AACA,cAAMR,YAAY,GAAG,KAAKC,eAAL,EAArB;AAEA,iBAAO;AACHoB,YAAAA,KAAK,EAAErB,YADJ;AAEHJ,YAAAA,QAAQ,EAAE,KAAK6B,qBAAL,CAA2B,KAAKvD,gBAAhC,CAFP;AAGH0D,YAAAA,SAAS,EAAE,KAAKC,sBAAL,CAA4B,KAAK3D,gBAAjC,CAHR;AAIH4F,YAAAA,SAAS,EAAEvD,YAJR;AAKHmD,YAAAA,QAAQ,EAAE,KAAKC,WAAL,EALP;AAMHhE,YAAAA,QAAQ,EAAE,KAAKzB,gBANZ;AAOH6F,YAAAA,aAAa,EAAE,KAAK/F;AAPjB,WAAP;AASH;;AAOMgG,QAAAA,kBAAkB,CAACC,UAAD,EAA+B;AACpD,eAAKvF,gBAAL,GAAwBqC,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYiD,UAAZ,CAAxB;AACA,iBAAO,IAAP;AACH;;AAEMC,QAAAA,kBAAkB,GAAW;AAChC,iBAAO,KAAKxF,gBAAZ;AACH;AAED;AACJ;AACA;;;AACWyF,QAAAA,SAAS,GAAY;AACxB,iBAAO,KAAK7F,UAAZ;AACH;AAED;AACJ;AACA;;;AACW8F,QAAAA,oBAAoB,GAAW;AAClC,iBAAO,KAAK9F,UAAL,GAAkB,KAAKC,UAAvB,GAAoC,CAA3C;AACH;AAED;AACJ;AACA;;;AACW8F,QAAAA,OAAO,GAAa;AACvB,eAAK/F,UAAL,GAAkB,KAAlB;AACA,eAAKC,UAAL,GAAkB,CAAlB;AACA,eAAKC,eAAL,GAAuB,CAAC,CAAxB;AACA,iBAAO,IAAP;AACH;;AArckC,O;;;;;iBAcZ,K;;;;;;;iBAGG,K;;;;;;;iBAGC,C;;;;;;;iBAGC,C", "sourcesContent": ["import { _decorator, Component, Enum, misc, Node, UITransform, Vec2, Vec3, JsonAsset } from 'cc';\nimport { BulletSystem } from '../bullet/BulletSystem';\nimport { MoveBase, eMoveEvent } from './IMovable';\nimport Entity from '../ui/base/Entity';\nimport { PathData, PathPoint } from '../data/PathData';\n\nconst { degreesToRadians, radiansToDegrees } = misc;\nconst { ccclass, property, executeInEditMode } = _decorator;\n\n@ccclass('PathMove')\n@executeInEditMode()\nexport class PathMove extends MoveBase {\n    public _pathAsset: JsonAsset | null = null;\n    @property({ type: JsonAsset, displayName: \"路径数据(预览用)\" })\n    public get pathAsset(): JsonAsset | null {\n        return this._pathAsset;\n    }\n    public set pathAsset(value: JsonAsset) {\n        this._pathAsset = value;\n        if (value) {\n            this.setPath(PathData.fromJSON(value.json));\n        }\n    }\n\n    @property({ displayName: \"循环移动\" })\n    public loop: boolean = false;\n\n    @property({ displayName: \"反向移动\" })\n    public reverse: boolean = false;\n\n    @property({ displayName: \"振荡偏移速度\", tooltip: \"控制倾斜振荡的频率\" })\n    public tiltSpeed: number = 0;\n\n    @property({ displayName: \"振荡偏移幅度\", tooltip: \"控制倾斜振荡的幅度\" })\n    public tiltOffset: number = 0;\n\n    // 路径相关数据\n    private _pathData: PathData | null = null;\n    private _subdivided: PathPoint[] = []; // 细分后的路径点（包含完整信息）\n    private _totalDistance: number = 0;\n    private _distances: number[] = [];\n\n    // 移动状态\n    private _currentDistance: number = 0;\n    private _currentPointIndex: number = 0; // 当前所在的细分点索引\n    private _segmentT: number = 0; // 在当前段内的插值参数 [0,1]\n    private _tiltTime: number = 0;\n\n    // 停留状态\n    private _isStaying: boolean = false; // 是否正在停留\n    private _stayTimer: number = 0; // 停留计时器（秒）\n    private _stayPointIndex: number = -1; // 停留的点索引\n\n    private _updateInEditor: boolean = false;\n    public onFocusInEditor() {\n        this._updateInEditor = true;\n        this.resetToStart();\n    }\n\n    public onLostFocusInEditor() {\n        this._updateInEditor = false;\n    }\n\n    public update(dt: number) {\n        if (this._updateInEditor) {\n            this.tick(dt);\n        }\n    }\n\n    /**\n     * 加载路径数据（使用新的细分点方法）\n     */\n    public setPath(pathData: PathData) {\n        this._pathData = pathData;\n        // 使用新的细分点方法，直接获取包含完整信息的PathPoint数组\n        this._subdivided = this._pathData.getSubdividedPoints();\n        // 计算距离信息\n        this.calculateDistances();\n    }\n\n    /**\n     * 计算距离信息\n     */\n    private calculateDistances() {\n        this._distances = [0];\n        this._totalDistance = 0;\n\n        for (let i = 1; i < this._subdivided.length; i++) {\n            const distance = Vec2.distance(this._subdivided[i - 1].position, this._subdivided[i].position);\n            this._totalDistance += distance;\n            this._distances.push(this._totalDistance);\n        }\n    }\n\n    /**\n     * 主要的移动更新逻辑\n     */\n    public tick(dt: number): void {\n        if (!this._isMovable || this._subdivided.length < 2) return;\n\n        // 处理停留逻辑\n        if (this._isStaying) {\n            this._stayTimer -= dt;\n            if (this._stayTimer <= 0) {\n                this._isStaying = false;\n                this._stayPointIndex = -1;\n            } else {\n                // 停留期间只更新位置（倾斜效果等），不移动\n                this.updatePosition(dt);\n                return;\n            }\n        }\n\n        // 获取当前位置的速度（从细分点直接获取）\n        const currentSpeed = this.getCurrentSpeed();\n\n        // 更新沿路径的距离\n        const deltaDistance = currentSpeed * dt;\n\n        if (this.reverse) {\n            this._currentDistance -= deltaDistance;\n            if (this._currentDistance < 0) {\n                if (this.loop) {\n                    this._currentDistance = this._totalDistance + this._currentDistance;\n                } else {\n                    this._currentDistance = 0;\n                }\n            }\n        } else {\n            this._currentDistance += deltaDistance;\n            if (this._currentDistance > this._totalDistance) {\n                if (this.loop) {\n                    this._currentDistance = this._currentDistance - this._totalDistance;\n                } else {\n                    this._currentDistance = this._totalDistance;\n                }\n            }\n        }\n\n        this.updateCurrentPointIndex();\n        this.checkForStayPoint();\n        this.updatePosition(dt);\n    }\n\n    /**\n     * 检查是否到达需要停留的点\n     */\n    private checkForStayPoint(): void {\n        if (this._isStaying || this._subdivided.length === 0) return;\n\n        // 获取当前路径点数据\n        const currentPoint = this.getCurrentPathPointData();\n        if (!currentPoint || currentPoint.stayDuration <= 0) return;\n\n        // 检查是否刚好到达这个点（通过检查是否在点的精确位置附近）\n        const tolerance = 5.0; // 容差值\n        const currentPos = this.getCurrentPosition();\n        const distance = Vec2.distance(currentPos, currentPoint.position);\n\n        // 使用点的引用作为唯一标识，避免重复触发同一个点的停留\n        const pointIndex = this._subdivided.indexOf(currentPoint);\n\n        if (distance <= tolerance && this._stayPointIndex !== pointIndex) {\n            // 开始停留\n            this._isStaying = true;\n            this._stayTimer = currentPoint.stayDuration / 1000.0; // 转换为秒\n            this._stayPointIndex = pointIndex;\n        }\n    }\n\n    /**\n     * 根据当前距离更新点索引和段内插值参数\n     */\n    private updateCurrentPointIndex() {\n        if (this._subdivided.length === 0) return;\n\n        // 边界情况处理\n        if (this._currentDistance <= 0) {\n            this._currentPointIndex = 0;\n            this._segmentT = 0;\n            return;\n        }\n\n        if (this._currentDistance >= this._totalDistance) {\n            this._currentPointIndex = Math.max(0, this._subdivided.length - 2);\n            this._segmentT = 1;\n            return;\n        }\n\n        // 从当前索引开始搜索，利用时间连续性\n        let searchStart = Math.max(0, this._currentPointIndex - 1);\n        let found = false;\n\n        // 向前搜索\n        for (let i = searchStart; i < this._distances.length - 1; i++) {\n            if (this._currentDistance >= this._distances[i] && this._currentDistance <= this._distances[i + 1]) {\n                this._currentPointIndex = i;\n                const segmentLength = this._distances[i + 1] - this._distances[i];\n                this._segmentT = segmentLength > 0 ? (this._currentDistance - this._distances[i]) / segmentLength : 0;\n                found = true;\n                break;\n            }\n        }\n\n        // 如果向前搜索没找到，向后搜索（处理反向移动）\n        if (!found) {\n            for (let i = Math.min(searchStart, this._distances.length - 2); i >= 0; i--) {\n                if (this._currentDistance >= this._distances[i] && this._currentDistance <= this._distances[i + 1]) {\n                    this._currentPointIndex = i;\n                    const segmentLength = this._distances[i + 1] - this._distances[i];\n                    this._segmentT = segmentLength > 0 ? (this._currentDistance - this._distances[i]) / segmentLength : 0;\n                    break;\n                }\n            }\n        }\n    }\n\n    /**\n     * 获取当前速度（使用索引优化）\n     */\n    private getCurrentSpeed(): number {\n        if (this._subdivided.length === 0) {\n            return this.speed; // 回退到基础速度\n        }\n\n        // 边界情况\n        if (this._currentPointIndex >= this._subdivided.length - 1) {\n            return this._subdivided[this._subdivided.length - 1].speed * this._speedMultiplier;\n        }\n\n        // 使用预计算的索引和插值参数\n        const startSpeed = this._subdivided[this._currentPointIndex].speed;\n        const endSpeed = this._subdivided[this._currentPointIndex + 1].speed;\n\n        // 在两个细分点之间插值速度\n        const interpolatedSpeed = startSpeed + (endSpeed - startSpeed) * this._segmentT;\n        return interpolatedSpeed * this._speedMultiplier;\n    }\n\n    /**\n     * 更新节点位置和朝向\n     */\n    private updatePosition(dt: number) {\n        const position = this.getPositionAtDistance(this._currentDistance);\n        \n        // 应用倾斜偏移\n        if (this.tiltSpeed > 0 && this.tiltOffset > 0) {\n            this._tiltTime += dt;\n            \n            const direction = this.getDirectionAtDistance(this._currentDistance);\n            if (direction.lengthSqr() > 0.001) {\n                // 计算垂直于移动方向的向量\n                const perpX = -direction.y;\n                const perpY = direction.x;\n                \n                // 计算倾斜偏移\n                const tiltAmount = Math.sin(this._tiltTime * this.tiltSpeed) * this.tiltOffset;\n                \n                position.x += perpX * tiltAmount;\n                position.y += perpY * tiltAmount;\n            }\n        }\n\n        this.node.setPosition(position.x, position.y, 0);\n\n        // // 更新朝向\n        // if (this.isFacingMoveDir) {\n        //     const direction = this.getDirectionAtDistance(this._currentDistance);\n        //     if (direction.lengthSqr() > 0.001) {\n        //         const angle = Math.atan2(direction.y, direction.x) * 180 / Math.PI;\n        //         const finalAngle = angle + this.defaultFacing;\n        //         this.node.setRotationFromEuler(0, 0, finalAngle);\n        //     }\n        // }\n\n        // 可见性检查\n        if (++this._visibilityCheckCounter >= PathMove.VISIBILITY_CHECK_INTERVAL) {\n            this._visibilityCheckCounter = 0;\n            this.checkVisibility();\n        }\n    }\n\n    /**\n     * 根据距离获取位置（优化版本，主要用于当前位置）\n     */\n    private getPositionAtDistance(distance: number): Vec2 {\n        // 如果查询的是当前距离，使用优化的索引方法\n        if (Math.abs(distance - this._currentDistance) < 0.001) {\n            return this.getCurrentPosition();\n        }\n\n        // 其他距离仍使用原来的方法（用于方向计算等）\n        if (this._subdivided.length === 0) return new Vec2();\n        if (distance <= 0) return this._subdivided[0].position.clone();\n        if (distance >= this._totalDistance) return this._subdivided[this._subdivided.length - 1].position.clone();\n\n        for (let i = 1; i < this._distances.length; i++) {\n            if (distance <= this._distances[i]) {\n                const segmentStart = this._distances[i - 1];\n                const segmentEnd = this._distances[i];\n                const segmentLength = segmentEnd - segmentStart;\n\n                if (segmentLength === 0) return this._subdivided[i - 1].position.clone();\n\n                const t = (distance - segmentStart) / segmentLength;\n                return Vec2.lerp(new Vec2(), this._subdivided[i - 1].position, this._subdivided[i].position, t);\n            }\n        }\n\n        return this._subdivided[this._subdivided.length - 1].position.clone();\n    }\n\n    /**\n     * 获取当前位置（使用索引优化）\n     */\n    private getCurrentPosition(): Vec2 {\n        if (this._subdivided.length === 0) return new Vec2();\n\n        // 边界情况\n        if (this._currentPointIndex >= this._subdivided.length - 1) {\n            return this._subdivided[this._subdivided.length - 1].position.clone();\n        }\n\n        // 使用预计算的索引和插值参数\n        const startPos = this._subdivided[this._currentPointIndex].position;\n        const endPos = this._subdivided[this._currentPointIndex + 1].position;\n\n        return Vec2.lerp(new Vec2(), startPos, endPos, this._segmentT);\n    }\n\n    /**\n     * 根据距离获取移动方向\n     */\n    private getDirectionAtDistance(distance: number): Vec2 {\n        const epsilon = 1;\n        const pos1 = this.getPositionAtDistance(distance);\n        const pos2 = this.getPositionAtDistance(distance + epsilon);\n\n        return Vec2.subtract(new Vec2(), pos2, pos1).normalize();\n    }\n\n    // 公共API方法\n    public setMovable(movable: boolean): PathMove {\n        this._isMovable = movable;\n        return this;\n    }\n\n    /**\n     * 设置路径进度 [0-1]\n     */\n    public setProgress(progress: number): PathMove {\n        this._currentDistance = Math.max(0, Math.min(1, progress)) * this._totalDistance;\n        this.updateCurrentPointIndex(); // 更新索引\n        this._isStaying = false;\n        this._stayTimer = 0;\n        this._stayPointIndex = -1;\n        return this;\n    }\n\n    /**\n     * 获取当前进度 [0-1]\n     */\n    public getProgress(): number {\n        return this._totalDistance > 0 ? this._currentDistance / this._totalDistance : 0;\n    }\n\n    /**\n     * 重置到路径起点\n     */\n    public resetToStart(): PathMove {\n        this._currentDistance = 0;\n        this._currentPointIndex = 0;\n        this._segmentT = 0;\n        this._isStaying = false;\n        this._stayTimer = 0;\n        this._stayPointIndex = -1;\n        return this;\n    }\n\n    /**\n     * 移动到路径终点\n     */\n    public moveToEnd(): PathMove {\n        this._currentDistance = this._totalDistance;\n        this.updateCurrentPointIndex(); // 更新索引\n        this._isStaying = false;\n        this._stayTimer = 0;\n        this._stayPointIndex = -1;\n        return this;\n    }\n\n    /**\n     * 获取当前位置对应的路径点数据（使用索引优化）\n     */\n    public getCurrentPathPointData(): PathPoint | null {\n        if (this._subdivided.length === 0) return null;\n\n        // 边界情况\n        if (this._currentPointIndex >= this._subdivided.length - 1) {\n            return this._subdivided[this._subdivided.length - 1];\n        }\n\n        // 使用预计算的索引，返回更接近的那个点\n        return this._segmentT < 0.5 ?\n            this._subdivided[this._currentPointIndex] :\n            this._subdivided[this._currentPointIndex + 1];\n    }\n\n    /**\n     * 获取当前位置的详细路径信息（包括速度、朝向等）\n     */\n    public getCurrentPathInfo() {\n        if (this._subdivided.length === 0) return null;\n\n        const currentPoint = this.getCurrentPathPointData();\n        const currentSpeed = this.getCurrentSpeed();\n\n        return {\n            speed: currentSpeed,\n            position: this.getPositionAtDistance(this._currentDistance),\n            direction: this.getDirectionAtDistance(this._currentDistance),\n            pathPoint: currentPoint,\n            progress: this.getProgress(),\n            distance: this._currentDistance,\n            totalDistance: this._totalDistance\n        };\n    }\n\n    /**\n     * 设置速度倍率（用于临时调整速度）\n     */\n    private _speedMultiplier: number = 1.0;\n\n    public setSpeedMultiplier(multiplier: number): PathMove {\n        this._speedMultiplier = Math.max(0, multiplier);\n        return this;\n    }\n\n    public getSpeedMultiplier(): number {\n        return this._speedMultiplier;\n    }\n\n    /**\n     * 获取是否正在停留\n     */\n    public isStaying(): boolean {\n        return this._isStaying;\n    }\n\n    /**\n     * 获取剩余停留时间（秒）\n     */\n    public getRemainingStayTime(): number {\n        return this._isStaying ? this._stayTimer : 0;\n    }\n\n    /**\n     * 强制结束停留状态\n     */\n    public endStay(): PathMove {\n        this._isStaying = false;\n        this._stayTimer = 0;\n        this._stayPointIndex = -1;\n        return this;\n    }\n}\n"]}