{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/move/PathMove.ts"], "names": ["_decorator", "misc", "Enum", "JsonAsset", "eOrientationType", "PathData", "<PERSON><PERSON>ult<PERSON>ove", "degreesToRadians", "radiansToDegrees", "ccclass", "property", "executeInEditMode", "PathMove", "type", "displayName", "_pathAsset", "_pathData", "_subdivided", "_offsetX", "_offsetY", "_currentPointIndex", "_nextPointIndex", "_remainDistance", "_stayTimer", "_updateInEditor", "pathAsset", "value", "set<PERSON>ath", "fromJSON", "json", "editor_orientationType", "orientationType", "setOrientation", "editor_<PERSON><PERSON><PERSON><PERSON>", "orientationParam", "onFocusInEditor", "_isMovable", "onLostFocusInEditor", "resetToStart", "update", "dt", "tick", "setOffset", "x", "y", "pathData", "getSubdividedPoints", "moveToNextPoint", "tickMovement", "updateTilting", "speedAngle", "_position", "node", "setPosition", "checkVisibility", "updateOrientation", "v0", "speed", "s", "acceleration", "angleRad", "deltaX", "Math", "cos", "deltaY", "sin", "onReachPoint", "Path", "orientation", "lerp", "kLerpFactor", "setRotationFromEuler", "forwardOrientation", "pointIndex", "currentPoint", "getPathPoint", "_basePosition", "set", "stayDuration", "nextIndex", "length", "loop", "setNext", "pathPointIndex", "nextPoint", "dirX", "dirY", "sqrt", "atan2", "v1", "_tiltTime", "isStaying", "getRemainingStayTime", "a", "b", "decay", "exp"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAuBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAqCC,MAAAA,S,OAAAA,S;;AAE5CC,MAAAA,gB,iBAAAA,gB;;AAEtBC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,W,iBAAAA,W;;;;;;;;;OAEH;AAAEC,QAAAA,gBAAF;AAAoBC,QAAAA;AAApB,O,GAAyCP,I;OACzC;AAAEQ,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CX,U;;0BAIpCY,Q,WAFZH,OAAO,CAAC,UAAD,C,UACPE,iBAAiB,E,UAGbD,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAEV,SAAR;AAAmBW,QAAAA,WAAW,EAAE;AAAhC,OAAD,C,UAWRJ,QAAQ,CAAC;AAAEI,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAGRJ,QAAQ,CAAC;AAAEI,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAGRJ,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAEX,IAAI;AAAA;AAAA,iDAAZ;AAAgCY,QAAAA,WAAW,EAAC;AAA5C,OAAD,C,UAORJ,QAAQ,CAAC;AAAEI,QAAAA,WAAW,EAAE;AAAf,OAAD,C,qDA5Bb,MAEaF,QAFb;AAAA;AAAA,sCAE0C;AAAA;AAAA;AAAA,eAC/BG,UAD+B,GACA,IADA;;AAAA;;AAAA;;AAkCtC;AAlCsC,eAmC9BC,SAnC8B,GAmCD,IAnCC;AAAA,eAoC9BC,WApC8B,GAoCH,EApCG;AAoCC;AACvC;AArCsC,eAsC9BC,QAtC8B,GAsCX,CAtCW;AAAA,eAuC9BC,QAvC8B,GAuCX,CAvCW;AA2CtC;AA3CsC,eA4C9BC,kBA5C8B,GA4CD,CA5CC;AA4CE;AA5CF,eA6C9BC,eA7C8B,GA6CJ,CA7CI;AAAA,eA8C9BC,eA9C8B,GA8CJ,CA9CI;AA8CE;AAExC;AAhDsC,eAiD9BC,UAjD8B,GAiDT,CAjDS;AAiDN;AAjDM,eAmD9BC,eAnD8B,GAmDH,KAnDG;AAAA;;AAGlB,YAATC,SAAS,GAAqB;AACrC,iBAAO,KAAKV,UAAZ;AACH;;AACmB,YAATU,SAAS,CAACC,KAAD,EAAmB;AACnC,eAAKX,UAAL,GAAkBW,KAAlB;;AACA,cAAIA,KAAJ,EAAW;AACP,iBAAKC,OAAL,CAAa;AAAA;AAAA,sCAASC,QAAT,CAAkBF,KAAK,CAACG,IAAxB,CAAb;AACH;AACJ;;AASgC,YAAtBC,sBAAsB,GAAqB;AAClD,iBAAO,KAAKC,eAAZ;AACH;;AACgC,YAAtBD,sBAAsB,CAACJ,KAAD,EAA0B;AACvD,eAAKM,cAAL,CAAoBN,KAApB,EAA2B,KAAKO,uBAAhC;AACH;;AAEiC,YAAvBA,uBAAuB,GAAW;AACzC,iBAAO,KAAKC,gBAAZ;AACH;;AACiC,YAAvBD,uBAAuB,CAACP,KAAD,EAAgB;AAC9C,eAAKM,cAAL,CAAoB,KAAKD,eAAzB,EAA0CL,KAA1C;AACH;;AAoBMS,QAAAA,eAAe,GAAG;AACrB,eAAKX,eAAL,GAAuB,IAAvB;AACA,eAAKY,UAAL,GAAkB,IAAlB;AACH;;AACMC,QAAAA,mBAAmB,GAAG;AACzB,eAAKb,eAAL,GAAuB,KAAvB;AACA,eAAKY,UAAL,GAAkB,KAAlB;AACA,eAAKE,YAAL;AACH;;AAEMC,QAAAA,MAAM,CAACC,EAAD,EAAa;AACtB,cAAI,KAAKhB,eAAT,EAA0B;AACtB,iBAAKiB,IAAL,CAAUD,EAAV;AACH;AACJ,SAlEqC,CAoEtC;;;AACOE,QAAAA,SAAS,CAACC,CAAD,EAAYC,CAAZ,EAAiC;AAC7C,eAAK1B,QAAL,GAAgByB,CAAhB;AACA,eAAKxB,QAAL,GAAgByB,CAAhB;AACA,iBAAO,IAAP;AACH;;AACMjB,QAAAA,OAAO,CAACkB,QAAD,EAA+B;AACzC,eAAK7B,SAAL,GAAiB6B,QAAjB,CADyC,CAEzC;;AACA,eAAK5B,WAAL,GAAmB,KAAKD,SAAL,CAAe8B,mBAAf,EAAnB;AACA,eAAKR,YAAL;AAEA,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACWG,QAAAA,IAAI,CAACD,EAAD,EAAmB;AAC1B,cAAI,CAAC,KAAKJ,UAAV,EAAsB;;AACtB,cAAI,CAAC,KAAKpB,SAAV,EAAqB;AACjB,kBAAMyB,IAAN,CAAWD,EAAX;AACA;AACH,WALyB,CAO1B;;;AACA,cAAI,KAAKjB,UAAL,GAAkB,CAAtB,EAAyB;AACrB,iBAAKA,UAAL,IAAmBiB,EAAnB;;AACA,gBAAI,KAAKjB,UAAL,IAAmB,CAAvB,EAA0B;AACtB,mBAAKA,UAAL,GAAkB,CAAlB,CADsB,CAEtB;;AACA,mBAAKwB,eAAL;AACH;AACJ,WAPD,MAOO,IAAI,KAAK1B,eAAL,KAAyB,KAAKD,kBAAlC,EAAsD;AACzD,iBAAK4B,YAAL,CAAkBR,EAAlB;AACH;;AAED,eAAKS,aAAL,CAAmB,KAAKC,UAAxB,EAAoCV,EAApC,EAAwC,KAAKW,SAA7C,EAnB0B,CAqB1B;;AACA,eAAKC,IAAL,CAAUC,WAAV,CAAsB,KAAKF,SAA3B;AACA,eAAKG,eAAL,GAvB0B,CAyB1B;;AACA,eAAKC,iBAAL,CAAuBf,EAAvB;AACH;;AAEOQ,QAAAA,YAAY,CAACR,EAAD,EAAa;AAC7B;AACA,cAAMgB,EAAE,GAAG,KAAKC,KAAhB,CAF6B,CAG7B;;AACA,cAAMC,CAAC,GAAGF,EAAE,GAAGhB,EAAL,GAAU,MAAM,KAAKmB,YAAX,GAA0BnB,EAA1B,GAA+BA,EAAnD;AACA,eAAKiB,KAAL,IAAc,KAAKE,YAAL,GAAoBnB,EAAlC,CAL6B,CAO7B;AAEA;;AACA,cAAMoB,QAAQ,GAAG,KAAKV,UAAtB;AACA,cAAMW,MAAM,GAAGC,IAAI,CAACC,GAAL,CAASH,QAAT,IAAqBF,CAApC;AACA,cAAMM,MAAM,GAAGF,IAAI,CAACG,GAAL,CAASL,QAAT,IAAqBF,CAApC,CAZ6B,CAc7B;;AACA,eAAKP,SAAL,CAAeR,CAAf,IAAoBkB,MAApB;AACA,eAAKV,SAAL,CAAeP,CAAf,IAAoBoB,MAApB,CAhB6B,CAkB7B;;AACA,cAAI,KAAK1C,eAAL,GAAuB,CAA3B,EAA8B;AAC1B,iBAAKA,eAAL,IAAwBoC,CAAxB;;AACA,gBAAI,KAAKpC,eAAL,IAAwB,CAA5B,EAA+B;AAC3B,mBAAK4C,YAAL,CAAkB,KAAK7C,eAAvB;AACH;AACJ;AACJ;;AAESkC,QAAAA,iBAAiB,CAACf,EAAD,EAAmB;AAC1C,cAAI,KAAKT,eAAL,KAAyB;AAAA;AAAA,oDAAiBoC,IAA9C,EAAoD;AAChD;AACA,iBAAKC,WAAL,GAAmB5D,gBAAgB,CAACI,QAAQ,CAACyD,IAAT,CAAc9D,gBAAgB,CAAC,KAAK6D,WAAN,CAA9B,EAAkD,KAAKlB,UAAvD,EAAmEtC,QAAQ,CAAC0D,WAA5E,EAAyF9B,EAAzF,CAAD,CAAnC;AACA,iBAAKY,IAAL,CAAUmB,oBAAV,CAA+B,CAA/B,EAAkC,CAAlC,EAAqC,KAAKH,WAAL,GAAmB,KAAKI,kBAA7D;AACA;AACH;;AACD,gBAAMjB,iBAAN,CAAwBf,EAAxB;AACH;;AAEO0B,QAAAA,YAAY,CAACO,UAAD,EAAqB;AACrC;AACA,eAAKrD,kBAAL,GAA0BqD,UAA1B,CAFqC,CAGrC;;AACA,cAAMC,YAAY,GAAG,KAAKC,YAAL,CAAkBF,UAAlB,CAArB;;AACA,cAAIC,YAAJ,EAAkB;AACd,iBAAKE,aAAL,CAAmBjC,CAAnB,GAAuB+B,YAAY,CAAC/B,CAAb,GAAiB,KAAKzB,QAA7C;AACA,iBAAK0D,aAAL,CAAmBhC,CAAnB,GAAuB8B,YAAY,CAAC9B,CAAb,GAAiB,KAAKzB,QAA7C;;AACA,iBAAKgC,SAAL,CAAe0B,GAAf,CAAmB,KAAKD,aAAxB;;AACA,iBAAKxB,IAAL,CAAUC,WAAV,CAAsB,KAAKF,SAA3B,EAJc,CAKd;;AACA,iBAAKM,KAAL,GAAaiB,YAAY,CAACjB,KAA1B,CANc,CAQd;;AACA,gBAAIiB,YAAY,CAACI,YAAb,GAA4B,CAAhC,EAAmC;AAC/B,mBAAKvD,UAAL,GAAkBmD,YAAY,CAACI,YAAb,GAA4B,MAA9C;AACA;AACH;AACJ,WAlBoC,CAoBrC;;;AACA,eAAK/B,eAAL;AACH;;AAEOA,QAAAA,eAAe,GAAG;AACtB,cAAMgC,SAAS,GAAG,KAAK3D,kBAAL,GAA0B,CAA5C;;AACA,cAAI2D,SAAS,IAAI,KAAK9D,WAAL,CAAiB+D,MAAlC,EAA0C;AACtC;AACA,gBAAI,KAAKC,IAAT,EAAe;AACX;AACA,mBAAKC,OAAL,CAAa,CAAb;AACH,aAHD,MAGO;AACH;AACA,mBAAK7D,eAAL,GAAuB,KAAKD,kBAA5B;AACH;AACJ,WATD,MASO;AACH;AACA,iBAAK8D,OAAL,CAAaH,SAAb;AACH;AACJ;;AAEOG,QAAAA,OAAO,CAACC,cAAD,EAAyB;AACpC,eAAK9D,eAAL,GAAuB8D,cAAvB;AAEA,cAAMT,YAAY,GAAG,KAAKC,YAAL,CAAkB,KAAKvD,kBAAvB,CAArB;AACA,cAAMgE,SAAS,GAAG,KAAKT,YAAL,CAAkB,KAAKtD,eAAvB,CAAlB;;AACA,cAAIqD,YAAY,IAAIU,SAApB,EAA+B;AAC3B,gBAAMC,IAAI,GAAGD,SAAS,CAACzC,CAAV,GAAc+B,YAAY,CAAC/B,CAAxC;AACA,gBAAM2C,IAAI,GAAGF,SAAS,CAACxC,CAAV,GAAc8B,YAAY,CAAC9B,CAAxC;AACA,iBAAKtB,eAAL,GAAuBwC,IAAI,CAACyB,IAAL,CAAUF,IAAI,GAAGA,IAAP,GAAcC,IAAI,GAAGA,IAA/B,CAAvB;;AAEA,gBAAI,KAAKhE,eAAL,GAAuB,CAA3B,EAA8B;AAC1B;AACA,mBAAK4B,UAAL,GAAkBY,IAAI,CAAC0B,KAAL,CAAWF,IAAX,EAAiBD,IAAjB,CAAlB,CAF0B,CAG1B;AACA;;AACA,kBAAM7B,EAAE,GAAGkB,YAAY,CAACjB,KAAxB;AACA,kBAAMgC,EAAE,GAAGL,SAAS,CAAC3B,KAArB;AACA,mBAAKE,YAAL,GAAoB,CAAC8B,EAAE,GAAGA,EAAL,GAAUjC,EAAE,GAAGA,EAAhB,KAAuB,IAAI,KAAKlC,eAAhC,CAApB,CAP0B,CAQ1B;AACH;AACJ;AACJ;;AAEOqD,QAAAA,YAAY,CAACQ,cAAD,EAA2C;AAC3D,cAAIA,cAAc,GAAG,CAAjB,IAAsBA,cAAc,IAAI,KAAKlE,WAAL,CAAiB+D,MAA7D,EAAqE;AACjE,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAK/D,WAAL,CAAiBkE,cAAjB,CAAP;AACH;;AAEO7C,QAAAA,YAAY,GAAG;AACnB,eAAKlB,kBAAL,GAA0B,CAA1B;AACA,eAAKC,eAAL,GAAuB,CAAvB;AACA,eAAKE,UAAL,GAAkB,CAAlB;AACA,eAAKmE,SAAL,GAAiB,CAAjB;AAEA,eAAKxB,YAAL,CAAkB,CAAlB;AACH;;AAEMyB,QAAAA,SAAS,GAAY;AACxB,iBAAO,KAAKpE,UAAL,GAAkB,CAAzB;AACH;;AAEMqE,QAAAA,oBAAoB,GAAW;AAClC,iBAAO,KAAKrE,UAAZ;AACH;;AAEU,eAAJ8C,IAAI,CAACwB,CAAD,EAAYC,CAAZ,EAAuBC,KAAvB,EAAsCvD,EAAtC,EAA0D;AACjE,iBAAO,CAACqD,CAAC,GAAGC,CAAL,IAAUhC,IAAI,CAACkC,GAAL,CAAS,CAACD,KAAD,GAASvD,EAAlB,CAAV,GAAkCsD,CAAzC;AACH;;AAlPqC,O,UAyCdxB,W,GAAc,C;;;;;iBA3Bf,K;;;;;;;iBAGG,K", "sourcesContent": ["import { _decorator, Component, misc, Enum, Node, UITransform, Vec2, Vec3, JsonAsset } from 'cc';\nimport { BulletSystem } from '../bullet/BulletSystem';\nimport { MoveBase, eMoveEvent, eOrientationType } from './IMovable';\nimport Entity from '../ui/base/Entity';\nimport { PathData, PathPoint } from '../data/PathData';\nimport { DefaultMove } from './DefaultMove';\n\nconst { degreesToRadians, radiansToDegrees } = misc;\nconst { ccclass, property, executeInEditMode } = _decorator;\n\n@ccclass('PathMove')\n@executeInEditMode()\nexport class PathMove extends DefaultMove {\n    public _pathAsset: JsonAsset | null = null;\n    @property({ type: JsonAsset, displayName: \"路径数据(预览用)\" })\n    public get pathAsset(): JsonAsset | null {\n        return this._pathAsset;\n    }\n    public set pathAsset(value: JsonAsset) {\n        this._pathAsset = value;\n        if (value) {\n            this.setPath(PathData.fromJSON(value.json));\n        }\n    }\n\n    @property({ displayName: \"循环移动\" })\n    public loop: boolean = false;\n\n    @property({ displayName: \"反向移动\" })\n    public reverse: boolean = false;\n\n    @property({ type: Enum(eOrientationType), displayName:\"朝向类型\" })\n    public get editor_orientationType(): eOrientationType {\n        return this.orientationType;\n    }\n    public set editor_orientationType(value: eOrientationType) {\n        this.setOrientation(value, this.editor_orientationParam);\n    }\n    @property({ displayName: \"朝向参数\" })\n    public get editor_orientationParam(): number {\n        return this.orientationParam;\n    }\n    public set editor_orientationParam(value: number) {\n        this.setOrientation(this.orientationType, value);\n    }\n\n    // 路径相关数据\n    private _pathData: PathData | null = null;\n    private _subdivided: PathPoint[] = []; // 细分后的路径点（包含完整信息）\n    // 路径偏移\n    private _offsetX: number = 0;\n    private _offsetY: number = 0;\n    // 平滑过渡参数\n    private static readonly kLerpFactor = 5;\n\n    // 移动状态\n    private _currentPointIndex: number = 0; // 当前所在的细分点索引\n    private _nextPointIndex: number = 0;\n    private _remainDistance: number = 0;    // 距离下一个点的剩余距离\n\n    // 停留状态\n    private _stayTimer: number = 0; // 停留计时器（秒）\n\n    private _updateInEditor: boolean = false;\n    public onFocusInEditor() {\n        this._updateInEditor = true;\n        this._isMovable = true;\n    }\n    public onLostFocusInEditor() {\n        this._updateInEditor = false;\n        this._isMovable = false;\n        this.resetToStart();\n    }\n\n    public update(dt: number) {\n        if (this._updateInEditor) {\n            this.tick(dt);\n        }\n    }\n\n    // 注意调用顺序,先调用setOffset,再调用setPath\n    public setOffset(x: number, y: number): PathMove {\n        this._offsetX = x;\n        this._offsetY = y;\n        return this;\n    }\n    public setPath(pathData: PathData): PathMove {\n        this._pathData = pathData;\n        // 使用新的细分点方法，直接获取包含完整信息的PathPoint数组\n        this._subdivided = this._pathData.getSubdividedPoints();\n        this.resetToStart();\n\n        return this;\n    }\n\n    /**\n     * 主要的移动更新逻辑\n     */\n    public tick(dt: number): void {\n        if (!this._isMovable) return;\n        if (!this._pathData) {\n            super.tick(dt);\n            return;\n        }\n        \n        // 处理停留逻辑\n        if (this._stayTimer > 0) {\n            this._stayTimer -= dt;\n            if (this._stayTimer <= 0) {\n                this._stayTimer = 0;\n                // 停留结束，继续移动到下一个点\n                this.moveToNextPoint();\n            }\n        } else if (this._nextPointIndex !== this._currentPointIndex) {\n            this.tickMovement(dt);\n        }\n\n        this.updateTilting(this.speedAngle, dt, this._position);\n        \n        // 设置节点位置\n        this.node.setPosition(this._position);\n        this.checkVisibility();\n\n        // 更新朝向\n        this.updateOrientation(dt);\n    }\n\n    private tickMovement(dt: number) {\n        // 使用匀加速直线运动更新位置\n        const v0 = this.speed;\n        // s = v0*t + 0.5*a*t^2\n        const s = v0 * dt + 0.5 * this.acceleration * dt * dt;\n        this.speed += this.acceleration * dt;\n\n        // console.log(`v0: ${v0.toFixed(2)}, a: ${this.acceleration.toFixed(2)}, s: ${s.toFixed(2)}`);\n\n        // 计算移动向量\n        const angleRad = this.speedAngle;\n        const deltaX = Math.cos(angleRad) * s;\n        const deltaY = Math.sin(angleRad) * s;\n\n        // 更新位置\n        this._position.x += deltaX;\n        this._position.y += deltaY;\n\n        // 检查是否到达目标点\n        if (this._remainDistance > 0) {\n            this._remainDistance -= s;\n            if (this._remainDistance <= 0) {\n                this.onReachPoint(this._nextPointIndex);\n            }\n        }\n    }\n\n    protected updateOrientation(dt: number): void {\n        if (this.orientationType === eOrientationType.Path) {\n            // PathMove这里speedAngle是radians\n            this.orientation = radiansToDegrees(PathMove.lerp(degreesToRadians(this.orientation), this.speedAngle, PathMove.kLerpFactor, dt));\n            this.node.setRotationFromEuler(0, 0, this.orientation + this.forwardOrientation);\n            return;\n        }\n        super.updateOrientation(dt);\n    }\n\n    private onReachPoint(pointIndex: number) {\n        // 更新当前点索引\n        this._currentPointIndex = pointIndex;\n        // 检查是否需要停留\n        const currentPoint = this.getPathPoint(pointIndex);\n        if (currentPoint) { \n            this._basePosition.x = currentPoint.x + this._offsetX;\n            this._basePosition.y = currentPoint.y + this._offsetY;\n            this._position.set(this._basePosition);\n            this.node.setPosition(this._position);\n            // 设置初始速度\n            this.speed = currentPoint.speed;\n\n            // console.log(`到达点 ${pointIndex}, 停留时间: ${currentPoint.stayDuration}ms`);\n            if (currentPoint.stayDuration > 0) {\n                this._stayTimer = currentPoint.stayDuration / 1000.0;\n                return;\n            }\n        }\n\n        // 继续移动到下一个点\n        this.moveToNextPoint();\n    }\n\n    private moveToNextPoint() {\n        const nextIndex = this._currentPointIndex + 1;\n        if (nextIndex >= this._subdivided.length) {\n            // 到达路径终点\n            if (this.loop) {\n                // 循环模式，回到起点\n                this.setNext(0);\n            } else {\n                // 停止移动\n                this._nextPointIndex = this._currentPointIndex;\n            }\n        } else {\n            // 移动到下一个点\n            this.setNext(nextIndex);\n        }\n    }\n\n    private setNext(pathPointIndex: number) {\n        this._nextPointIndex = pathPointIndex;\n\n        const currentPoint = this.getPathPoint(this._currentPointIndex);\n        const nextPoint = this.getPathPoint(this._nextPointIndex);\n        if (currentPoint && nextPoint) {\n            const dirX = nextPoint.x - currentPoint.x;\n            const dirY = nextPoint.y - currentPoint.y;\n            this._remainDistance = Math.sqrt(dirX * dirX + dirY * dirY);\n\n            if (this._remainDistance > 1) {\n                // 计算目标移动角度\n                this.speedAngle = Math.atan2(dirY, dirX);\n                // 计算加速度：使用匀加速直线运动公式 v1^2 = v0^2 + 2*a*x\n                // 解出 a = (v1^2 - v0^2) / (2*x)\n                const v0 = currentPoint.speed;\n                const v1 = nextPoint.speed;\n                this.acceleration = (v1 * v1 - v0 * v0) / (2 * this._remainDistance);\n                // console.log(`设置下一段: ${this._currentPointIndex} -> ${this._nextPointIndex}, 距离: ${this._remainDistance.toFixed(2)}, 初速度: ${v0}, 末速度: ${v1}, 加速度: ${this.acceleration.toFixed(2)}, 角度: ${radiansToDegrees(targetAngle).toFixed(2)}`);\n            }\n        }\n    }\n\n    private getPathPoint(pathPointIndex: number): PathPoint | null {\n        if (pathPointIndex < 0 || pathPointIndex >= this._subdivided.length) {\n            return null;\n        }\n        return this._subdivided[pathPointIndex];\n    }\n\n    private resetToStart() {\n        this._currentPointIndex = 0;\n        this._nextPointIndex = 0;\n        this._stayTimer = 0;\n        this._tiltTime = 0;\n\n        this.onReachPoint(0);\n    }\n\n    public isStaying(): boolean {\n        return this._stayTimer > 0;\n    }\n\n    public getRemainingStayTime(): number {\n        return this._stayTimer;\n    }\n\n    static lerp(a: number, b: number, decay: number, dt: number): number {\n        return (a - b) * Math.exp(-decay * dt) + b;\n    }\n}\n"]}