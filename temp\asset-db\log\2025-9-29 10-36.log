2025-9-29 10:36:55-log: Cannot access game frame or container.
2025-9-29 10:36:55-debug: asset-db:require-engine-code (410ms)
2025-9-29 10:36:55-log: meshopt wasm decoder initialized
2025-9-29 10:36:55-log: [bullet]:bullet wasm lib loaded.
2025-9-29 10:36:55-log: [box2d]:box2d wasm lib loaded.
2025-9-29 10:36:55-log: Cocos Creator v3.8.6
2025-9-29 10:36:55-log: Using legacy pipeline
2025-9-29 10:36:55-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:30.77MB, end 80.14MB, increase: 49.37MB
2025-9-29 10:36:56-debug: [Assets Memory track]: asset-db-plugin-register: builder start:84.17MB, end 289.00MB, increase: 204.84MB
2025-9-29 10:36:55-debug: [Assets Memory track]: asset-db-plugin-register: programming start:81.25MB, end 84.13MB, increase: 2.88MB
2025-9-29 10:36:55-log: Forward render pipeline initialized.
2025-9-29 10:36:56-debug: [Assets Memory track]: asset-db-plugin-register: project start:81.10MB, end 287.73MB, increase: 206.63MB
2025-9-29 10:36:56-debug: run package(google-play) handler(enable) start
2025-9-29 10:36:56-debug: run package(google-play) handler(enable) success!
2025-9-29 10:36:56-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:80.17MB, end 287.76MB, increase: 207.59MB
2025-9-29 10:36:56-debug: run package(harmonyos-next) handler(enable) start
2025-9-29 10:36:56-debug: run package(honor-mini-game) handler(enable) start
2025-9-29 10:36:56-debug: run package(harmonyos-next) handler(enable) success!
2025-9-29 10:36:56-debug: run package(huawei-agc) handler(enable) start
2025-9-29 10:36:56-debug: run package(honor-mini-game) handler(enable) success!
2025-9-29 10:36:56-debug: run package(huawei-quick-game) handler(enable) start
2025-9-29 10:36:56-debug: run package(huawei-agc) handler(enable) success!
2025-9-29 10:36:56-debug: run package(huawei-quick-game) handler(enable) success!
2025-9-29 10:36:56-debug: run package(ios) handler(enable) start
2025-9-29 10:36:56-debug: run package(ios) handler(enable) success!
2025-9-29 10:36:56-debug: run package(linux) handler(enable) start
2025-9-29 10:36:56-debug: run package(mac) handler(enable) start
2025-9-29 10:36:56-debug: run package(migu-mini-game) handler(enable) success!
2025-9-29 10:36:56-debug: run package(linux) handler(enable) success!
2025-9-29 10:36:56-debug: run package(mac) handler(enable) success!
2025-9-29 10:36:56-debug: run package(migu-mini-game) handler(enable) start
2025-9-29 10:36:56-debug: run package(native) handler(enable) start
2025-9-29 10:36:56-debug: run package(ohos) handler(enable) start
2025-9-29 10:36:56-debug: run package(native) handler(enable) success!
2025-9-29 10:36:56-debug: run package(ohos) handler(enable) success!
2025-9-29 10:36:56-debug: run package(runtime-dev-tools) handler(enable) start
2025-9-29 10:36:56-debug: run package(oppo-mini-game) handler(enable) success!
2025-9-29 10:36:56-debug: run package(oppo-mini-game) handler(enable) start
2025-9-29 10:36:56-debug: run package(runtime-dev-tools) handler(enable) success!
2025-9-29 10:36:56-debug: run package(taobao-mini-game) handler(enable) start
2025-9-29 10:36:56-debug: run package(vivo-mini-game) handler(enable) success!
2025-9-29 10:36:56-debug: run package(taobao-mini-game) handler(enable) success!
2025-9-29 10:36:56-debug: run package(web-desktop) handler(enable) start
2025-9-29 10:36:56-debug: run package(vivo-mini-game) handler(enable) start
2025-9-29 10:36:56-debug: run package(web-desktop) handler(enable) success!
2025-9-29 10:36:56-debug: run package(web-mobile) handler(enable) success!
2025-9-29 10:36:56-debug: run package(wechatgame) handler(enable) start
2025-9-29 10:36:56-debug: run package(web-mobile) handler(enable) start
2025-9-29 10:36:56-debug: run package(wechatgame) handler(enable) success!
2025-9-29 10:36:56-debug: run package(wechatprogram) handler(enable) start
2025-9-29 10:36:56-debug: run package(windows) handler(enable) start
2025-9-29 10:36:56-debug: run package(windows) handler(enable) success!
2025-9-29 10:36:56-debug: run package(cocos-service) handler(enable) start
2025-9-29 10:36:56-debug: run package(xiaomi-quick-game) handler(enable) start
2025-9-29 10:36:56-debug: run package(cocos-service) handler(enable) success!
2025-9-29 10:36:56-debug: run package(wechatprogram) handler(enable) success!
2025-9-29 10:36:56-debug: run package(xiaomi-quick-game) handler(enable) success!
2025-9-29 10:36:56-debug: run package(fix-asset-default-userdata) handler(enable) start
2025-9-29 10:36:56-debug: run package(im-plugin) handler(enable) start
2025-9-29 10:36:56-debug: run package(fix-asset-default-userdata) handler(enable) success!
2025-9-29 10:36:56-debug: run package(im-plugin) handler(enable) success!
2025-9-29 10:36:56-debug: run package(emitter-editor) handler(enable) start
2025-9-29 10:36:56-debug: run package(emitter-editor) handler(enable) success!
2025-9-29 10:36:56-debug: start refresh asset from db://assets/editor/enum-gen/EmitterEnum.ts...
2025-9-29 10:36:56-debug: refresh asset db://assets/editor/enum-gen success
2025-9-29 10:36:56-debug: run package(i18n) handler(enable) start
2025-9-29 10:36:56-debug: refresh asset db://assets/editor/enum-gen success
2025-9-29 10:36:56-debug: start refresh asset from db://assets/editor/enum-gen/EnemyEnum.ts...
2025-9-29 10:36:56-debug: run package(i18n) handler(enable) success!
2025-9-29 10:36:56-debug: asset-db:worker-init: initPlugin (1066ms)
2025-9-29 10:36:56-debug: run package(level-editor) handler(enable) start
2025-9-29 10:36:56-debug: run package(level-editor) handler(enable) success!
2025-9-29 10:36:56-debug: Run asset db hook programming:beforePreStart ...
2025-9-29 10:36:56-debug: Run asset db hook programming:beforePreStart success!
2025-9-29 10:36:56-debug: Run asset db hook engine-extends:beforePreStart ...
2025-9-29 10:36:56-debug: Run asset db hook engine-extends:beforePreStart success!
2025-9-29 10:36:56-debug: [Assets Memory track]: asset-db:worker-init start:30.76MB, end 290.15MB, increase: 259.39MB
2025-9-29 10:36:57-debug: run package(localization-editor) handler(enable) start
2025-9-29 10:36:57-debug: run package(localization-editor) handler(enable) success!
2025-9-29 10:36:57-debug: asset-db:worker-init (1659ms)
2025-9-29 10:36:57-debug: asset-db-hook-programming-beforePreStart (110ms)
2025-9-29 10:36:57-debug: asset-db-hook-engine-extends-beforePreStart (110ms)
2025-9-29 10:36:57-debug: run package(placeholder) handler(enable) start
2025-9-29 10:36:57-debug: run package(placeholder) handler(enable) success!
2025-9-29 10:36:57-debug: Preimport db internal success
2025-9-29 10:36:57-debug: %cImport%c: E:\M2Game\Client\assets\editor\enum-gen\EmitterEnum.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 10:36:57-debug: %cImport%c: E:\M2Game\Client\assets\editor\enum-gen\EnemyEnum.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 10:36:57-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 10:36:57-debug: Preimport db assets success
2025-9-29 10:36:57-debug: Preimport db i18n success
2025-9-29 10:36:57-debug: Run asset db hook programming:afterPreStart ...
2025-9-29 10:36:57-debug: starting packer-driver...
2025-9-29 10:37:04-debug: initialize scripting environment...
2025-9-29 10:37:04-debug: [[Executor]] prepare before lock
2025-9-29 10:37:04-debug: Set detail map pack:///resolution-detail-map.json: {}
2025-9-29 10:37:04-debug: [[Executor]] prepare after unlock
2025-9-29 10:37:04-debug: Run asset db hook programming:afterPreStart success!
2025-9-29 10:37:04-debug: Run asset db hook engine-extends:afterPreStart success!
2025-9-29 10:37:04-debug: Run asset db hook engine-extends:afterPreStart ...
2025-9-29 10:37:04-debug: Start up the 'internal' database...
2025-9-29 10:37:04-debug: [Assets Memory track]: asset-db:worker-init: preStart start:290.17MB, end 305.54MB, increase: 15.37MB
2025-9-29 10:37:04-debug: asset-db-hook-programming-afterPreStart (7343ms)
2025-9-29 10:37:04-debug: asset-db:worker-effect-data-processing (241ms)
2025-9-29 10:37:04-debug: asset-db-hook-engine-extends-afterPreStart (242ms)
2025-9-29 10:37:04-debug: Start up the 'assets' database...
2025-9-29 10:37:04-debug: asset-db:worker-startup-database[internal] (7716ms)
2025-9-29 10:37:04-debug: Start up the 'i18n' database...
2025-9-29 10:37:04-debug: asset-db:worker-startup-database[assets] (7636ms)
2025-9-29 10:37:04-debug: lazy register asset handler *
2025-9-29 10:37:04-debug: lazy register asset handler text
2025-9-29 10:37:04-debug: lazy register asset handler spine-data
2025-9-29 10:37:04-debug: lazy register asset handler directory
2025-9-29 10:37:04-debug: lazy register asset handler json
2025-9-29 10:37:04-debug: lazy register asset handler dragonbones-atlas
2025-9-29 10:37:04-debug: lazy register asset handler terrain
2025-9-29 10:37:04-debug: lazy register asset handler dragonbones
2025-9-29 10:37:04-debug: lazy register asset handler typescript
2025-9-29 10:37:04-debug: lazy register asset handler javascript
2025-9-29 10:37:04-debug: lazy register asset handler sprite-frame
2025-9-29 10:37:04-debug: lazy register asset handler scene
2025-9-29 10:37:04-debug: lazy register asset handler prefab
2025-9-29 10:37:04-debug: lazy register asset handler tiled-map
2025-9-29 10:37:04-debug: lazy register asset handler buffer
2025-9-29 10:37:04-debug: lazy register asset handler alpha-image
2025-9-29 10:37:04-debug: lazy register asset handler image
2025-9-29 10:37:04-debug: lazy register asset handler sign-image
2025-9-29 10:37:04-debug: lazy register asset handler texture-cube
2025-9-29 10:37:04-debug: lazy register asset handler erp-texture-cube
2025-9-29 10:37:04-debug: lazy register asset handler render-texture
2025-9-29 10:37:04-debug: lazy register asset handler texture
2025-9-29 10:37:04-debug: lazy register asset handler texture-cube-face
2025-9-29 10:37:04-debug: lazy register asset handler gltf
2025-9-29 10:37:04-debug: lazy register asset handler rt-sprite-frame
2025-9-29 10:37:04-debug: lazy register asset handler gltf-mesh
2025-9-29 10:37:04-debug: lazy register asset handler gltf-animation
2025-9-29 10:37:04-debug: lazy register asset handler gltf-skeleton
2025-9-29 10:37:04-debug: lazy register asset handler fbx
2025-9-29 10:37:04-debug: lazy register asset handler gltf-material
2025-9-29 10:37:04-debug: lazy register asset handler gltf-embeded-image
2025-9-29 10:37:04-debug: lazy register asset handler gltf-scene
2025-9-29 10:37:04-debug: lazy register asset handler material
2025-9-29 10:37:04-debug: lazy register asset handler physics-material
2025-9-29 10:37:04-debug: lazy register asset handler effect
2025-9-29 10:37:04-debug: lazy register asset handler effect-header
2025-9-29 10:37:04-debug: lazy register asset handler animation-clip
2025-9-29 10:37:04-debug: lazy register asset handler animation-graph
2025-9-29 10:37:04-debug: lazy register asset handler animation-graph-variant
2025-9-29 10:37:04-debug: lazy register asset handler animation-mask
2025-9-29 10:37:04-debug: lazy register asset handler ttf-font
2025-9-29 10:37:04-debug: lazy register asset handler bitmap-font
2025-9-29 10:37:04-debug: lazy register asset handler sprite-atlas
2025-9-29 10:37:04-debug: lazy register asset handler audio-clip
2025-9-29 10:37:04-debug: lazy register asset handler particle
2025-9-29 10:37:04-debug: lazy register asset handler auto-atlas
2025-9-29 10:37:04-debug: lazy register asset handler render-pipeline
2025-9-29 10:37:04-debug: lazy register asset handler label-atlas
2025-9-29 10:37:04-debug: lazy register asset handler render-flow
2025-9-29 10:37:04-debug: lazy register asset handler instantiation-material
2025-9-29 10:37:04-debug: lazy register asset handler render-stage
2025-9-29 10:37:04-debug: lazy register asset handler instantiation-skeleton
2025-9-29 10:37:04-debug: lazy register asset handler instantiation-mesh
2025-9-29 10:37:04-debug: lazy register asset handler instantiation-animation
2025-9-29 10:37:04-debug: lazy register asset handler video-clip
2025-9-29 10:37:04-debug: asset-db:worker-startup-database[i18n] (7569ms)
2025-9-29 10:37:04-debug: asset-db:start-database (7807ms)
2025-9-29 10:37:04-debug: fix the bug of updateDefaultUserData
2025-9-29 10:37:04-debug: init worker message success
2025-9-29 10:37:04-debug: asset-db:ready (10907ms)
2025-9-29 10:37:04-debug: programming:execute-script (3ms)
2025-9-29 10:37:05-debug: [Build Memory track]: builder:worker-init start:195.05MB, end 213.85MB, increase: 18.80MB
2025-9-29 10:37:05-debug: builder:worker-init (310ms)
2025-9-29 10:37:48-debug: refresh db internal success
2025-9-29 10:37:48-debug: refresh db assets success
2025-9-29 10:37:48-debug: refresh db i18n success
2025-9-29 10:37:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 10:37:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 10:37:48-debug: asset-db:refresh-all-database (137ms)
2025-9-29 10:37:52-debug: refresh db internal success
2025-9-29 10:37:52-debug: refresh db assets success
2025-9-29 10:37:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 10:37:52-debug: refresh db i18n success
2025-9-29 10:37:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 10:37:52-debug: asset-db:refresh-all-database (127ms)
2025-9-29 10:37:52-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 10:37:52-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 10:46:18-debug: refresh db internal success
2025-9-29 10:46:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 10:46:18-debug: refresh db assets success
2025-9-29 10:46:18-debug: refresh db i18n success
2025-9-29 10:46:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 10:46:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 10:46:18-debug: asset-db:refresh-all-database (162ms)
2025-9-29 10:46:27-debug: refresh db internal success
2025-9-29 10:46:27-debug: refresh db assets success
2025-9-29 10:46:27-debug: refresh db i18n success
2025-9-29 10:46:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 10:46:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 10:46:27-debug: asset-db:refresh-all-database (128ms)
2025-9-29 10:46:27-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 10:46:27-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 10:46:29-debug: refresh db internal success
2025-9-29 10:46:29-debug: refresh db assets success
2025-9-29 10:46:29-debug: refresh db i18n success
2025-9-29 10:46:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 10:46:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 10:46:29-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 10:46:29-debug: asset-db:refresh-all-database (123ms)
2025-9-29 10:46:29-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 10:48:10-debug: refresh db internal success
2025-9-29 10:48:10-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 10:48:10-debug: refresh db assets success
2025-9-29 10:48:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 10:48:10-debug: refresh db i18n success
2025-9-29 10:48:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 10:48:10-debug: asset-db:refresh-all-database (177ms)
2025-9-29 10:48:10-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 10:48:10-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 10:48:22-debug: refresh db internal success
2025-9-29 10:48:22-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 10:48:22-debug: refresh db assets success
2025-9-29 10:48:22-debug: refresh db i18n success
2025-9-29 10:48:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 10:48:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 10:48:22-debug: asset-db:refresh-all-database (174ms)
2025-9-29 10:50:52-debug: refresh db internal success
2025-9-29 10:50:52-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 10:50:52-debug: refresh db assets success
2025-9-29 10:50:52-debug: refresh db i18n success
2025-9-29 10:50:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 10:50:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 10:50:52-debug: asset-db:refresh-all-database (154ms)
2025-9-29 10:50:52-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 10:50:52-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 10:51:28-debug: refresh db internal success
2025-9-29 10:51:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 10:51:28-debug: refresh db assets success
2025-9-29 10:51:28-debug: refresh db i18n success
2025-9-29 10:51:28-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 10:51:28-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 10:51:28-debug: asset-db:refresh-all-database (125ms)
2025-9-29 10:53:35-debug: refresh db internal success
2025-9-29 10:53:36-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 10:53:36-debug: refresh db assets success
2025-9-29 10:53:36-debug: refresh db i18n success
2025-9-29 10:53:36-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 10:53:36-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 10:53:36-debug: asset-db:refresh-all-database (158ms)
2025-9-29 10:53:41-debug: refresh db internal success
2025-9-29 10:53:41-debug: refresh db assets success
2025-9-29 10:53:41-debug: refresh db i18n success
2025-9-29 10:53:41-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 10:53:41-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 10:53:41-debug: asset-db:refresh-all-database (134ms)
2025-9-29 10:53:44-debug: refresh db internal success
2025-9-29 10:53:44-log: 资源数据库已锁定，资源操作(bound _reimportAsset)将会延迟响应，请稍侯
2025-9-29 10:53:44-debug: refresh db assets success
2025-9-29 10:53:44-debug: refresh db i18n success
2025-9-29 10:53:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 10:53:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 10:53:44-debug: asset-db:refresh-all-database (120ms)
2025-9-29 10:53:44-debug: %cImport%c: E:\M2Game\Client\assets\scenes\PathEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-29 10:53:44-debug: asset-db:reimport-assetf87e7412-cf27-4eb5-a664-0071cad67cf8 (3ms)
2025-9-29 10:56:51-debug: refresh db internal success
2025-9-29 10:56:52-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 10:56:52-debug: refresh db assets success
2025-9-29 10:56:52-debug: refresh db i18n success
2025-9-29 10:56:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 10:56:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 10:56:52-debug: asset-db:refresh-all-database (167ms)
2025-9-29 10:56:52-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 10:56:52-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 10:56:57-debug: refresh db internal success
2025-9-29 10:56:57-debug: refresh db assets success
2025-9-29 10:56:57-debug: refresh db i18n success
2025-9-29 10:56:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 10:56:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 10:56:57-debug: asset-db:refresh-all-database (124ms)
2025-9-29 10:56:57-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 10:56:57-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 10:58:21-debug: refresh db internal success
2025-9-29 10:58:21-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 10:58:21-debug: refresh db assets success
2025-9-29 10:58:21-debug: refresh db i18n success
2025-9-29 10:58:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 10:58:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 10:58:21-debug: asset-db:refresh-all-database (159ms)
2025-9-29 10:58:21-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 10:58:21-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 10:59:46-debug: refresh db internal success
2025-9-29 10:59:46-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 10:59:46-debug: refresh db assets success
2025-9-29 10:59:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 10:59:46-debug: refresh db i18n success
2025-9-29 10:59:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 10:59:46-debug: asset-db:refresh-all-database (169ms)
2025-9-29 10:59:46-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 10:59:46-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 10:59:51-debug: refresh db internal success
2025-9-29 10:59:51-debug: refresh db assets success
2025-9-29 10:59:51-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 10:59:51-debug: refresh db i18n success
2025-9-29 10:59:51-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 10:59:51-debug: asset-db:refresh-all-database (120ms)
2025-9-29 10:59:51-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 10:59:51-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 10:59:53-debug: refresh db internal success
2025-9-29 10:59:53-debug: refresh db assets success
2025-9-29 10:59:53-debug: refresh db i18n success
2025-9-29 10:59:53-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 10:59:53-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 10:59:53-debug: asset-db:refresh-all-database (126ms)
2025-9-29 11:01:04-debug: refresh db internal success
2025-9-29 11:01:04-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 11:01:04-debug: refresh db assets success
2025-9-29 11:01:04-debug: refresh db i18n success
2025-9-29 11:01:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:01:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:01:04-debug: asset-db:refresh-all-database (152ms)
2025-9-29 11:01:04-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:01:08-debug: refresh db internal success
2025-9-29 11:01:08-debug: refresh db assets success
2025-9-29 11:01:08-debug: refresh db i18n success
2025-9-29 11:01:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:01:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:01:08-debug: asset-db:refresh-all-database (155ms)
2025-9-29 11:01:08-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:01:11-debug: refresh db internal success
2025-9-29 11:01:12-debug: refresh db assets success
2025-9-29 11:01:12-debug: refresh db i18n success
2025-9-29 11:01:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:01:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:01:12-debug: asset-db:refresh-all-database (121ms)
2025-9-29 11:02:02-debug: refresh db internal success
2025-9-29 11:02:02-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 11:02:02-debug: refresh db assets success
2025-9-29 11:02:02-debug: refresh db i18n success
2025-9-29 11:02:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:02:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:02:02-debug: asset-db:refresh-all-database (158ms)
2025-9-29 11:02:02-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:02:02-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 11:02:07-debug: refresh db internal success
2025-9-29 11:02:08-debug: refresh db assets success
2025-9-29 11:02:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:02:08-debug: refresh db i18n success
2025-9-29 11:02:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:02:08-debug: asset-db:refresh-all-database (124ms)
2025-9-29 11:02:08-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:02:08-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:02:09-debug: refresh db internal success
2025-9-29 11:02:09-debug: refresh db assets success
2025-9-29 11:02:09-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:02:09-debug: refresh db i18n success
2025-9-29 11:02:09-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:02:09-debug: asset-db:refresh-all-database (132ms)
2025-9-29 11:02:09-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:02:09-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:03:02-debug: refresh db internal success
2025-9-29 11:03:02-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 11:03:02-debug: refresh db assets success
2025-9-29 11:03:02-debug: refresh db i18n success
2025-9-29 11:03:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:03:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:03:02-debug: asset-db:refresh-all-database (153ms)
2025-9-29 11:03:02-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:03:02-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:03:07-debug: refresh db internal success
2025-9-29 11:03:07-debug: refresh db assets success
2025-9-29 11:03:07-debug: refresh db i18n success
2025-9-29 11:03:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:03:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:03:07-debug: asset-db:refresh-all-database (133ms)
2025-9-29 11:03:07-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:03:07-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:03:08-debug: refresh db internal success
2025-9-29 11:03:08-debug: refresh db assets success
2025-9-29 11:03:08-debug: refresh db i18n success
2025-9-29 11:03:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:03:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:03:08-debug: asset-db:refresh-all-database (118ms)
2025-9-29 11:03:08-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:03:08-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:03:49-debug: refresh db internal success
2025-9-29 11:03:49-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 11:03:49-debug: refresh db assets success
2025-9-29 11:03:49-debug: refresh db i18n success
2025-9-29 11:03:49-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:03:49-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:03:49-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:03:49-debug: asset-db:refresh-all-database (152ms)
2025-9-29 11:03:49-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:03:55-debug: refresh db internal success
2025-9-29 11:03:55-debug: refresh db assets success
2025-9-29 11:03:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:03:55-debug: refresh db i18n success
2025-9-29 11:03:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:03:55-debug: asset-db:refresh-all-database (125ms)
2025-9-29 11:03:55-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:03:55-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 11:03:57-debug: refresh db internal success
2025-9-29 11:03:58-debug: refresh db assets success
2025-9-29 11:03:58-debug: refresh db i18n success
2025-9-29 11:03:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:03:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:03:58-debug: asset-db:refresh-all-database (128ms)
2025-9-29 11:03:58-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-29 11:03:58-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 11:05:37-debug: refresh db internal success
2025-9-29 11:05:37-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 11:05:37-debug: refresh db assets success
2025-9-29 11:05:37-debug: refresh db i18n success
2025-9-29 11:05:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:05:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:05:37-debug: asset-db:refresh-all-database (158ms)
2025-9-29 11:05:37-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:05:37-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:05:42-debug: refresh db internal success
2025-9-29 11:05:42-debug: refresh db assets success
2025-9-29 11:05:42-debug: refresh db i18n success
2025-9-29 11:05:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:05:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:05:42-debug: asset-db:refresh-all-database (116ms)
2025-9-29 11:05:43-debug: refresh db internal success
2025-9-29 11:05:43-debug: refresh db assets success
2025-9-29 11:05:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:05:43-debug: refresh db i18n success
2025-9-29 11:05:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:05:43-debug: asset-db:refresh-all-database (119ms)
2025-9-29 11:05:43-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:05:43-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:07:27-debug: refresh db internal success
2025-9-29 11:07:27-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 11:07:27-debug: refresh db assets success
2025-9-29 11:07:27-debug: refresh db i18n success
2025-9-29 11:07:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:07:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:07:27-debug: asset-db:refresh-all-database (156ms)
2025-9-29 11:07:27-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:07:27-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:10:04-debug: refresh db internal success
2025-9-29 11:10:04-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 11:10:04-debug: refresh db assets success
2025-9-29 11:10:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:10:04-debug: refresh db i18n success
2025-9-29 11:10:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:10:04-debug: asset-db:refresh-all-database (233ms)
2025-9-29 11:10:04-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:10:04-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:10:08-debug: refresh db internal success
2025-9-29 11:10:08-debug: refresh db assets success
2025-9-29 11:10:08-debug: refresh db i18n success
2025-9-29 11:10:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:10:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:10:08-debug: asset-db:refresh-all-database (154ms)
2025-9-29 11:10:08-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:10:09-debug: refresh db internal success
2025-9-29 11:10:09-debug: refresh db assets success
2025-9-29 11:10:09-debug: refresh db i18n success
2025-9-29 11:10:09-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:10:09-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:10:09-debug: asset-db:refresh-all-database (120ms)
2025-9-29 11:10:37-debug: refresh db internal success
2025-9-29 11:10:37-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 11:10:37-debug: refresh db assets success
2025-9-29 11:10:37-debug: refresh db i18n success
2025-9-29 11:10:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:10:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:10:37-debug: asset-db:refresh-all-database (155ms)
2025-9-29 11:10:43-debug: refresh db internal success
2025-9-29 11:10:43-debug: refresh db assets success
2025-9-29 11:10:43-debug: refresh db i18n success
2025-9-29 11:10:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:10:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:10:43-debug: asset-db:refresh-all-database (117ms)
2025-9-29 11:10:44-debug: refresh db internal success
2025-9-29 11:10:44-debug: refresh db assets success
2025-9-29 11:10:44-debug: refresh db i18n success
2025-9-29 11:10:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:10:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:10:44-debug: asset-db:refresh-all-database (123ms)
2025-9-29 11:10:44-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:13:14-debug: refresh db internal success
2025-9-29 11:13:14-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 11:13:14-debug: refresh db assets success
2025-9-29 11:13:14-debug: refresh db i18n success
2025-9-29 11:13:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:13:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:13:14-debug: asset-db:refresh-all-database (152ms)
2025-9-29 11:13:20-debug: refresh db internal success
2025-9-29 11:13:20-debug: refresh db assets success
2025-9-29 11:13:20-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:13:20-debug: refresh db i18n success
2025-9-29 11:13:20-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:13:20-debug: asset-db:refresh-all-database (119ms)
2025-9-29 11:13:22-debug: refresh db internal success
2025-9-29 11:13:22-debug: refresh db assets success
2025-9-29 11:13:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:13:22-debug: refresh db i18n success
2025-9-29 11:13:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:13:22-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:13:22-debug: asset-db:refresh-all-database (129ms)
2025-9-29 11:13:22-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:14:36-debug: refresh db internal success
2025-9-29 11:14:36-debug: Query all assets info in project
2025-9-29 11:14:36-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-29 11:14:36-debug: Skip compress image, progress: 0%
2025-9-29 11:14:36-debug: Init all bundles start..., progress: 0%
2025-9-29 11:14:36-debug: Num of bundles: 17..., progress: 0%
2025-9-29 11:14:36-debug: 查询 Asset Bundle start, progress: 0%
2025-9-29 11:14:36-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 11:14:36-debug: Init bundle root assets start..., progress: 0%
2025-9-29 11:14:36-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,511d2633-09a7-4bdd-ac42-f778032124b3,5d45aa00-e064-4938-b314-4265f0c2258c,9361fd90-ba52-4f84-aa93-6e878fd576ca,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,ff9be190-20a4-4e48-b68c-76e3c7cff085,0835f102-5471-47a3-9a76-01c07ac9cdb2,b5475517-23b9-4873-bc1a-968d96616081,0ed97c56-390e-4dd1-96b7-e7f2d93a98ed,b23391b6-52eb-46a6-8da1-6244d9d315fb,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-29 11:14:36-debug:   Number of all scripts: 278
2025-9-29 11:14:36-debug:   Number of other assets: 2155
2025-9-29 11:14:36-debug: Init bundle root assets success..., progress: 0%
2025-9-29 11:14:36-debug:   Number of all scenes: 11
2025-9-29 11:14:36-debug: // ---- build task 查询 Asset Bundle ---- (32ms)
2025-9-29 11:14:36-log: run build task 查询 Asset Bundle success in 32 ms√, progress: 5%
2025-9-29 11:14:36-debug: [Build Memory track]: 查询 Asset Bundle start:206.81MB, end 209.99MB, increase: 3.18MB
2025-9-29 11:14:36-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 11:14:36-debug: 查询 Asset Bundle start, progress: 5%
2025-9-29 11:14:36-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-9-29 11:14:36-debug: [Build Memory track]: 查询 Asset Bundle start:210.02MB, end 210.55MB, increase: 545.73KB
2025-9-29 11:14:36-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 11:14:36-log: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-9-29 11:14:36-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-29 11:14:36-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-29 11:14:36-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-29 11:14:36-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:210.58MB, end 210.64MB, increase: 58.68KB
2025-9-29 11:14:36-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-29 11:14:36-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-29 11:14:36-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-29 11:14:36-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-29 11:14:36-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-29 11:14:36-debug: [Build Memory track]: 填充脚本数据到 settings.json start:210.67MB, end 207.90MB, increase: -2835.03KB
2025-9-29 11:14:36-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 11:14:36-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (5ms)
2025-9-29 11:14:36-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:207.93MB, end 208.47MB, increase: 559.38KB
2025-9-29 11:14:36-log: run build task 整理部分构建选项内数据到 settings.json success in 5 ms√, progress: 15%
2025-9-29 11:14:36-debug: refresh db assets success
2025-9-29 11:14:36-debug: refresh db i18n success
2025-9-29 11:14:36-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:14:36-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:14:36-debug: asset-db:refresh-all-database (207ms)
2025-9-29 11:14:36-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:14:36-debug: Query all assets info in project
2025-9-29 11:14:36-debug: Query all assets info in project
2025-9-29 11:14:36-debug: Query all assets info in project
2025-9-29 11:14:36-debug: Query all assets info in project
2025-9-29 11:14:36-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-29 11:14:36-debug: Skip compress image, progress: 0%
2025-9-29 11:14:36-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-29 11:14:36-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-29 11:14:36-debug: Skip compress image, progress: 0%
2025-9-29 11:14:36-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-29 11:14:36-debug: Skip compress image, progress: 0%
2025-9-29 11:14:36-debug: Skip compress image, progress: 0%
2025-9-29 11:14:36-debug: Init all bundles start..., progress: 0%
2025-9-29 11:14:36-debug: 查询 Asset Bundle start, progress: 0%
2025-9-29 11:14:36-debug: Num of bundles: 17..., progress: 0%
2025-9-29 11:14:36-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 11:14:36-debug: Init bundle root assets start..., progress: 0%
2025-9-29 11:14:36-debug: Num of bundles: 17..., progress: 0%
2025-9-29 11:14:36-debug: Init all bundles start..., progress: 0%
2025-9-29 11:14:36-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 11:14:36-debug: 查询 Asset Bundle start, progress: 0%
2025-9-29 11:14:36-debug: Init bundle root assets start..., progress: 0%
2025-9-29 11:14:36-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,511d2633-09a7-4bdd-ac42-f778032124b3,5d45aa00-e064-4938-b314-4265f0c2258c,9361fd90-ba52-4f84-aa93-6e878fd576ca,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,ff9be190-20a4-4e48-b68c-76e3c7cff085,0835f102-5471-47a3-9a76-01c07ac9cdb2,b5475517-23b9-4873-bc1a-968d96616081,0ed97c56-390e-4dd1-96b7-e7f2d93a98ed,b23391b6-52eb-46a6-8da1-6244d9d315fb,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-29 11:14:36-debug:   Number of all scripts: 278
2025-9-29 11:14:36-debug:   Number of all scenes: 11
2025-9-29 11:14:36-debug:   Number of other assets: 2155
2025-9-29 11:14:36-debug: Num of bundles: 17..., progress: 0%
2025-9-29 11:14:36-debug: Init bundle root assets success..., progress: 0%
2025-9-29 11:14:36-debug: 查询 Asset Bundle start, progress: 0%
2025-9-29 11:14:36-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 11:14:36-debug: Init bundle root assets start..., progress: 0%
2025-9-29 11:14:36-debug: Init all bundles start..., progress: 0%
2025-9-29 11:14:36-debug: Num of bundles: 17..., progress: 0%
2025-9-29 11:14:36-debug: Init all bundles start..., progress: 0%
2025-9-29 11:14:36-debug: 查询 Asset Bundle start, progress: 0%
2025-9-29 11:14:36-debug: Init bundle root assets start..., progress: 0%
2025-9-29 11:14:36-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 11:14:36-debug: // ---- build task 查询 Asset Bundle ---- (30ms)
2025-9-29 11:14:36-log: run build task 查询 Asset Bundle success in 30 ms√, progress: 5%
2025-9-29 11:14:36-debug: [Build Memory track]: 查询 Asset Bundle start:211.10MB, end 211.14MB, increase: 34.78KB
2025-9-29 11:14:36-debug: 查询 Asset Bundle start, progress: 5%
2025-9-29 11:14:36-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 11:14:36-debug:   Number of all scripts: 278
2025-9-29 11:14:36-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,511d2633-09a7-4bdd-ac42-f778032124b3,5d45aa00-e064-4938-b314-4265f0c2258c,9361fd90-ba52-4f84-aa93-6e878fd576ca,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,ff9be190-20a4-4e48-b68c-76e3c7cff085,0835f102-5471-47a3-9a76-01c07ac9cdb2,b5475517-23b9-4873-bc1a-968d96616081,0ed97c56-390e-4dd1-96b7-e7f2d93a98ed,b23391b6-52eb-46a6-8da1-6244d9d315fb,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-29 11:14:36-debug:   Number of other assets: 2155
2025-9-29 11:14:36-debug:   Number of all scenes: 11
2025-9-29 11:14:36-debug: Init bundle root assets success..., progress: 0%
2025-9-29 11:14:36-debug: // ---- build task 查询 Asset Bundle ---- (25ms)
2025-9-29 11:14:36-debug: [Build Memory track]: 查询 Asset Bundle start:211.17MB, end 211.69MB, increase: 539.71KB
2025-9-29 11:14:36-log: run build task 查询 Asset Bundle success in 25 ms√, progress: 5%
2025-9-29 11:14:36-debug: 查询 Asset Bundle start, progress: 5%
2025-9-29 11:14:36-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 11:14:36-debug: [Build Memory track]: 查询 Asset Bundle start:211.72MB, end 211.75MB, increase: 26.69KB
2025-9-29 11:14:36-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-9-29 11:14:36-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 11:14:36-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-29 11:14:36-debug:   Number of all scenes: 11
2025-9-29 11:14:36-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,511d2633-09a7-4bdd-ac42-f778032124b3,5d45aa00-e064-4938-b314-4265f0c2258c,9361fd90-ba52-4f84-aa93-6e878fd576ca,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,ff9be190-20a4-4e48-b68c-76e3c7cff085,0835f102-5471-47a3-9a76-01c07ac9cdb2,b5475517-23b9-4873-bc1a-968d96616081,0ed97c56-390e-4dd1-96b7-e7f2d93a98ed,b23391b6-52eb-46a6-8da1-6244d9d315fb,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-29 11:14:36-debug:   Number of other assets: 2155
2025-9-29 11:14:36-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,511d2633-09a7-4bdd-ac42-f778032124b3,5d45aa00-e064-4938-b314-4265f0c2258c,9361fd90-ba52-4f84-aa93-6e878fd576ca,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,ff9be190-20a4-4e48-b68c-76e3c7cff085,0835f102-5471-47a3-9a76-01c07ac9cdb2,b5475517-23b9-4873-bc1a-968d96616081,0ed97c56-390e-4dd1-96b7-e7f2d93a98ed,b23391b6-52eb-46a6-8da1-6244d9d315fb,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-29 11:14:36-debug:   Number of all scripts: 278
2025-9-29 11:14:36-debug: Init bundle root assets success..., progress: 0%
2025-9-29 11:14:36-debug:   Number of all scripts: 278
2025-9-29 11:14:36-debug:   Number of other assets: 2155
2025-9-29 11:14:36-debug: Init bundle root assets success..., progress: 0%
2025-9-29 11:14:36-debug:   Number of all scenes: 11
2025-9-29 11:14:36-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:211.80MB, end 215.39MB, increase: 3.59MB
2025-9-29 11:14:36-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-9-29 11:14:36-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-29 11:14:36-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-29 11:14:36-debug: // ---- build task 查询 Asset Bundle ---- (40ms)
2025-9-29 11:14:36-log: run build task 查询 Asset Bundle success in 40 ms√, progress: 5%
2025-9-29 11:14:36-debug: 查询 Asset Bundle start, progress: 5%
2025-9-29 11:14:36-log: run build task 查询 Asset Bundle success in √, progress: 5%
2025-9-29 11:14:36-debug: [Build Memory track]: 查询 Asset Bundle start:215.74MB, end 215.75MB, increase: 12.07KB
2025-9-29 11:14:36-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 11:14:36-debug: 查询 Asset Bundle start, progress: 5%
2025-9-29 11:14:36-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-29 11:14:36-debug: [Build Memory track]: 填充脚本数据到 settings.json start:215.42MB, end 215.79MB, increase: 384.18KB
2025-9-29 11:14:36-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 11:14:36-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 11:14:36-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-29 11:14:36-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-9-29 11:14:36-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 10%
2025-9-29 11:14:36-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-29 11:14:36-debug: [Build Memory track]: 查询 Asset Bundle start:215.78MB, end 215.86MB, increase: 82.49KB
2025-9-29 11:14:36-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 11:14:36-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-9-29 11:14:36-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:215.89MB, end 216.46MB, increase: 585.32KB
2025-9-29 11:14:36-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-29 11:14:36-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-29 11:14:36-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-29 11:14:36-debug: [Build Memory track]: 填充脚本数据到 settings.json start:216.49MB, end 216.52MB, increase: 26.81KB
2025-9-29 11:14:36-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-29 11:14:36-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 11:14:36-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-9-29 11:14:36-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-29 11:14:36-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 11:14:36-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-9-29 11:14:36-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-29 11:14:36-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 11:14:36-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-29 11:14:36-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-29 11:14:36-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:216.61MB, end 216.87MB, increase: 261.17KB
2025-9-29 11:14:36-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-29 11:14:36-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-29 11:14:36-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-29 11:14:36-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-29 11:14:36-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-29 11:14:36-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-29 11:14:36-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-29 11:14:36-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-29 11:14:36-debug: [Build Memory track]: 填充脚本数据到 settings.json start:216.93MB, end 216.97MB, increase: 32.44KB
2025-9-29 11:14:36-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-29 11:14:36-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 11:14:36-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-29 11:14:36-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-29 11:14:36-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 11:14:36-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-29 11:14:36-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-9-29 11:14:36-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:217.03MB, end 217.31MB, increase: 291.46KB
2025-9-29 11:14:36-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-9-29 11:14:36-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-9-29 11:14:36-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-9-29 11:14:47-debug: Query all assets info in project
2025-9-29 11:14:47-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-29 11:14:47-debug: Skip compress image, progress: 0%
2025-9-29 11:14:47-debug: Init all bundles start..., progress: 0%
2025-9-29 11:14:47-debug: 查询 Asset Bundle start, progress: 0%
2025-9-29 11:14:47-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 11:14:47-debug: Num of bundles: 17..., progress: 0%
2025-9-29 11:14:47-debug: Init bundle root assets start..., progress: 0%
2025-9-29 11:14:47-debug:   Number of all scenes: 11
2025-9-29 11:14:47-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,511d2633-09a7-4bdd-ac42-f778032124b3,5d45aa00-e064-4938-b314-4265f0c2258c,9361fd90-ba52-4f84-aa93-6e878fd576ca,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,ff9be190-20a4-4e48-b68c-76e3c7cff085,0835f102-5471-47a3-9a76-01c07ac9cdb2,b5475517-23b9-4873-bc1a-968d96616081,0ed97c56-390e-4dd1-96b7-e7f2d93a98ed,b23391b6-52eb-46a6-8da1-6244d9d315fb,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-29 11:14:47-debug:   Number of all scripts: 278
2025-9-29 11:14:47-debug: Init bundle root assets success..., progress: 0%
2025-9-29 11:14:47-debug:   Number of other assets: 2155
2025-9-29 11:14:47-debug: // ---- build task 查询 Asset Bundle ---- (21ms)
2025-9-29 11:14:47-debug: 查询 Asset Bundle start, progress: 5%
2025-9-29 11:14:47-log: run build task 查询 Asset Bundle success in 21 ms√, progress: 5%
2025-9-29 11:14:47-debug: [Build Memory track]: 查询 Asset Bundle start:215.14MB, end 214.77MB, increase: -383.98KB
2025-9-29 11:14:47-debug: // ---- build task 查询 Asset Bundle ----
2025-9-29 11:14:47-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-9-29 11:14:47-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-9-29 11:14:47-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-29 11:14:47-debug: [Build Memory track]: 查询 Asset Bundle start:214.80MB, end 215.11MB, increase: 323.22KB
2025-9-29 11:14:47-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 11:14:47-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-9-29 11:14:47-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:215.14MB, end 215.16MB, increase: 17.00KB
2025-9-29 11:14:47-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-29 11:14:47-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-29 11:14:47-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-29 11:14:47-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-29 11:14:47-debug: [Build Memory track]: 填充脚本数据到 settings.json start:215.18MB, end 215.20MB, increase: 16.84KB
2025-9-29 11:14:47-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-29 11:14:47-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-9-29 11:14:47-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-29 11:14:47-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:215.23MB, end 215.52MB, increase: 292.67KB
2025-9-29 11:15:07-debug: refresh db internal success
2025-9-29 11:15:07-debug: refresh db assets success
2025-9-29 11:15:07-debug: refresh db i18n success
2025-9-29 11:15:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:15:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:15:07-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:15:07-debug: asset-db:refresh-all-database (119ms)
2025-9-29 11:15:07-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 11:15:08-debug: refresh db internal success
2025-9-29 11:15:08-debug: refresh db assets success
2025-9-29 11:15:08-debug: refresh db i18n success
2025-9-29 11:15:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:15:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:15:08-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-29 11:15:08-debug: asset-db:refresh-all-database (125ms)
2025-9-29 11:15:08-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 11:21:39-debug: refresh db internal success
2025-9-29 11:21:39-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 11:21:39-debug: refresh db assets success
2025-9-29 11:21:39-debug: refresh db i18n success
2025-9-29 11:21:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:21:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:21:39-debug: asset-db:refresh-all-database (156ms)
2025-9-29 11:21:39-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-29 11:21:39-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 11:21:43-debug: refresh db internal success
2025-9-29 11:21:44-debug: refresh db assets success
2025-9-29 11:21:44-debug: refresh db i18n success
2025-9-29 11:21:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:21:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:21:44-debug: asset-db:refresh-all-database (143ms)
2025-9-29 11:21:44-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:21:44-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:21:45-debug: refresh db internal success
2025-9-29 11:21:45-debug: refresh db assets success
2025-9-29 11:21:45-debug: refresh db i18n success
2025-9-29 11:21:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:21:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:21:45-debug: asset-db:refresh-all-database (133ms)
2025-9-29 11:21:45-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:21:45-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:22:25-debug: refresh db internal success
2025-9-29 11:22:25-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 11:22:25-debug: refresh db assets success
2025-9-29 11:22:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:22:25-debug: refresh db i18n success
2025-9-29 11:22:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:22:25-debug: asset-db:refresh-all-database (155ms)
2025-9-29 11:22:25-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:22:25-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:22:30-debug: refresh db internal success
2025-9-29 11:22:30-debug: refresh db assets success
2025-9-29 11:22:30-debug: refresh db i18n success
2025-9-29 11:22:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:22:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:22:30-debug: asset-db:refresh-all-database (118ms)
2025-9-29 11:22:30-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:22:30-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:22:31-debug: refresh db internal success
2025-9-29 11:22:31-debug: refresh db assets success
2025-9-29 11:22:31-debug: refresh db i18n success
2025-9-29 11:22:31-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:22:31-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:22:31-debug: asset-db:refresh-all-database (118ms)
2025-9-29 11:22:31-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:22:51-debug: refresh db internal success
2025-9-29 11:22:51-debug: refresh db assets success
2025-9-29 11:22:51-debug: refresh db i18n success
2025-9-29 11:22:51-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:22:51-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:22:51-debug: asset-db:refresh-all-database (118ms)
2025-9-29 11:22:51-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:22:51-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:22:52-debug: refresh db internal success
2025-9-29 11:22:52-debug: refresh db assets success
2025-9-29 11:22:52-debug: refresh db i18n success
2025-9-29 11:22:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:22:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:22:52-debug: asset-db:refresh-all-database (122ms)
2025-9-29 11:22:52-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:22:52-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:23:10-debug: %cImport%c: E:\M2Game\Client\assets\scenes\PathEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-29 11:23:10-debug: asset-db:reimport-assetf87e7412-cf27-4eb5-a664-0071cad67cf8 (2ms)
2025-9-29 11:27:22-debug: refresh db internal success
2025-9-29 11:27:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 11:27:22-debug: refresh db assets success
2025-9-29 11:27:22-debug: refresh db i18n success
2025-9-29 11:27:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:27:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:27:22-debug: asset-db:refresh-all-database (178ms)
2025-9-29 11:27:22-debug: asset-db:worker-effect-data-processing (3ms)
2025-9-29 11:27:22-debug: asset-db-hook-engine-extends-afterRefresh (4ms)
2025-9-29 11:27:28-debug: refresh db internal success
2025-9-29 11:27:28-debug: refresh db assets success
2025-9-29 11:27:28-debug: refresh db i18n success
2025-9-29 11:27:28-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:27:28-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:27:28-debug: asset-db:refresh-all-database (122ms)
2025-9-29 11:27:28-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:27:28-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:27:29-debug: refresh db internal success
2025-9-29 11:27:29-debug: refresh db assets success
2025-9-29 11:27:29-debug: refresh db i18n success
2025-9-29 11:27:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:27:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:27:29-debug: asset-db:refresh-all-database (120ms)
2025-9-29 11:29:13-debug: refresh db internal success
2025-9-29 11:29:13-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 11:29:13-debug: refresh db assets success
2025-9-29 11:29:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:29:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:29:13-debug: refresh db i18n success
2025-9-29 11:29:13-debug: asset-db:refresh-all-database (152ms)
2025-9-29 11:29:13-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:29:17-debug: refresh db internal success
2025-9-29 11:29:18-debug: refresh db assets success
2025-9-29 11:29:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:29:18-debug: refresh db i18n success
2025-9-29 11:29:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:29:18-debug: asset-db:refresh-all-database (158ms)
2025-9-29 11:29:18-debug: refresh db internal success
2025-9-29 11:29:18-debug: refresh db assets success
2025-9-29 11:29:18-debug: refresh db i18n success
2025-9-29 11:29:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:29:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:29:18-debug: asset-db:refresh-all-database (119ms)
2025-9-29 11:30:48-debug: refresh db internal success
2025-9-29 11:30:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 11:30:48-debug: refresh db assets success
2025-9-29 11:30:48-debug: refresh db i18n success
2025-9-29 11:30:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:30:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:30:48-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:30:48-debug: asset-db:refresh-all-database (148ms)
2025-9-29 11:30:48-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 11:30:51-debug: refresh db internal success
2025-9-29 11:30:51-debug: refresh db assets success
2025-9-29 11:30:51-debug: refresh db i18n success
2025-9-29 11:30:51-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:30:51-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:30:51-debug: asset-db:refresh-all-database (150ms)
2025-9-29 11:30:51-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:30:51-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:30:54-debug: refresh db internal success
2025-9-29 11:30:55-debug: refresh db assets success
2025-9-29 11:30:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:30:55-debug: refresh db i18n success
2025-9-29 11:30:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:30:55-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:30:55-debug: asset-db:refresh-all-database (131ms)
2025-9-29 11:30:55-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:33:45-debug: refresh db internal success
2025-9-29 11:33:45-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 11:33:45-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 11:33:45-debug: refresh db assets success
2025-9-29 11:33:45-debug: refresh db i18n success
2025-9-29 11:33:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:33:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:33:45-debug: asset-db:refresh-all-database (154ms)
2025-9-29 11:33:59-debug: refresh db internal success
2025-9-29 11:33:59-debug: refresh db assets success
2025-9-29 11:33:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:33:59-debug: refresh db i18n success
2025-9-29 11:33:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:33:59-debug: asset-db:refresh-all-database (157ms)
2025-9-29 11:34:01-debug: refresh db internal success
2025-9-29 11:34:01-debug: refresh db assets success
2025-9-29 11:34:01-debug: refresh db i18n success
2025-9-29 11:34:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:34:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:34:01-debug: asset-db:refresh-all-database (150ms)
2025-9-29 11:34:01-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:34:02-debug: refresh db internal success
2025-9-29 11:34:02-debug: refresh db assets success
2025-9-29 11:34:02-debug: refresh db i18n success
2025-9-29 11:34:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:34:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:34:02-debug: asset-db:refresh-all-database (150ms)
2025-9-29 11:35:53-debug: refresh db internal success
2025-9-29 11:35:53-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 11:35:53-debug: refresh db assets success
2025-9-29 11:35:53-debug: refresh db i18n success
2025-9-29 11:35:53-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:35:53-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:35:53-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-29 11:35:53-debug: asset-db:refresh-all-database (162ms)
2025-9-29 11:35:53-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 11:35:56-debug: refresh db internal success
2025-9-29 11:35:57-debug: refresh db assets success
2025-9-29 11:35:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:35:57-debug: refresh db i18n success
2025-9-29 11:35:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:35:57-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:35:57-debug: asset-db:refresh-all-database (158ms)
2025-9-29 11:35:57-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:35:58-debug: refresh db internal success
2025-9-29 11:35:58-debug: refresh db assets success
2025-9-29 11:35:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:35:58-debug: refresh db i18n success
2025-9-29 11:35:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:35:58-debug: asset-db:refresh-all-database (120ms)
2025-9-29 11:35:58-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:35:58-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:36:47-debug: refresh db internal success
2025-9-29 11:36:47-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 11:36:47-debug: refresh db assets success
2025-9-29 11:36:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:36:47-debug: refresh db i18n success
2025-9-29 11:36:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:36:47-debug: asset-db:refresh-all-database (163ms)
2025-9-29 11:36:47-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:36:47-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 11:36:51-debug: refresh db internal success
2025-9-29 11:36:51-debug: refresh db assets success
2025-9-29 11:36:51-debug: refresh db i18n success
2025-9-29 11:36:51-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:36:51-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:36:51-debug: asset-db:refresh-all-database (149ms)
2025-9-29 11:36:53-debug: refresh db internal success
2025-9-29 11:36:53-debug: refresh db assets success
2025-9-29 11:36:53-debug: refresh db i18n success
2025-9-29 11:36:53-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:36:53-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:36:53-debug: asset-db:refresh-all-database (124ms)
2025-9-29 11:36:53-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:36:53-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:38:59-debug: %cImport%c: E:\M2Game\Client\assets\scenes\PathEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-29 11:38:59-debug: asset-db:reimport-assetf87e7412-cf27-4eb5-a664-0071cad67cf8 (3ms)
2025-9-29 11:40:11-debug: refresh db internal success
2025-9-29 11:40:11-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 11:40:11-debug: refresh db assets success
2025-9-29 11:40:11-debug: refresh db i18n success
2025-9-29 11:40:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:40:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:40:11-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:40:11-debug: asset-db:refresh-all-database (161ms)
2025-9-29 11:40:11-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:40:14-debug: refresh db internal success
2025-9-29 11:40:14-debug: refresh db assets success
2025-9-29 11:40:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:40:14-debug: refresh db i18n success
2025-9-29 11:40:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:40:14-debug: asset-db:refresh-all-database (151ms)
2025-9-29 11:40:14-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:40:14-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 11:40:15-debug: refresh db internal success
2025-9-29 11:40:15-debug: refresh db assets success
2025-9-29 11:40:15-debug: refresh db i18n success
2025-9-29 11:40:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:40:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:40:15-debug: asset-db:refresh-all-database (142ms)
2025-9-29 11:40:15-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:40:15-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:40:52-debug: refresh db internal success
2025-9-29 11:40:52-debug: refresh db assets success
2025-9-29 11:40:52-debug: refresh db i18n success
2025-9-29 11:40:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:40:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:40:52-debug: asset-db:refresh-all-database (126ms)
2025-9-29 11:40:52-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-29 11:40:52-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 11:40:53-debug: start refresh asset from E:\M2Game\Client\assets\resources\game\level\wave\path\p_01.json...
2025-9-29 11:40:53-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\path\p_01.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 11:40:53-debug: refresh asset E:\M2Game\Client\assets\resources\game\level\wave\path success
2025-9-29 11:40:53-debug: refresh db internal success
2025-9-29 11:40:53-debug: refresh db assets success
2025-9-29 11:40:53-debug: refresh db i18n success
2025-9-29 11:40:53-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:40:53-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:40:53-debug: asset-db:refresh-all-database (123ms)
2025-9-29 11:40:53-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:40:53-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:40:57-debug: refresh db internal success
2025-9-29 11:40:57-debug: refresh db assets success
2025-9-29 11:40:57-debug: refresh db i18n success
2025-9-29 11:40:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:40:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:40:57-debug: asset-db:refresh-all-database (125ms)
2025-9-29 11:40:57-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:40:57-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:40:59-debug: refresh db internal success
2025-9-29 11:41:00-debug: refresh db assets success
2025-9-29 11:41:00-debug: refresh db i18n success
2025-9-29 11:41:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:41:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:41:00-debug: asset-db:refresh-all-database (120ms)
2025-9-29 11:41:00-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:41:00-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:43:17-debug: refresh db internal success
2025-9-29 11:43:17-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 11:43:17-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 11:43:17-debug: refresh db assets success
2025-9-29 11:43:17-debug: refresh db i18n success
2025-9-29 11:43:17-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:43:17-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:43:17-debug: asset-db:refresh-all-database (166ms)
2025-9-29 11:43:17-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:43:36-debug: %cImport%c: E:\M2Game\Client\assets\scenes\PathEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-29 11:43:36-debug: asset-db:reimport-assetf87e7412-cf27-4eb5-a664-0071cad67cf8 (2ms)
2025-9-29 11:44:50-debug: refresh db internal success
2025-9-29 11:44:50-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 11:44:50-debug: refresh db assets success
2025-9-29 11:44:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:44:50-debug: refresh db i18n success
2025-9-29 11:44:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:44:50-debug: asset-db:refresh-all-database (159ms)
2025-9-29 11:44:50-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:44:50-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:45:01-debug: refresh db internal success
2025-9-29 11:45:01-debug: refresh db assets success
2025-9-29 11:45:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:45:01-debug: refresh db i18n success
2025-9-29 11:45:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:45:01-debug: asset-db:refresh-all-database (123ms)
2025-9-29 11:45:01-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:45:02-debug: start refresh asset from E:\M2Game\Client\assets\resources\game\level\wave\path\p_01.json...
2025-9-29 11:45:02-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\path\p_01.json
background: #aaff85; color: #000;
color: #000;
2025-9-29 11:45:02-debug: refresh asset E:\M2Game\Client\assets\resources\game\level\wave\path success
2025-9-29 11:45:02-debug: refresh db internal success
2025-9-29 11:45:02-debug: refresh db assets success
2025-9-29 11:45:02-debug: refresh db i18n success
2025-9-29 11:45:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:45:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:45:02-debug: asset-db:refresh-all-database (121ms)
2025-9-29 11:45:02-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:45:02-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:45:06-debug: refresh db internal success
2025-9-29 11:45:06-debug: refresh db assets success
2025-9-29 11:45:06-debug: refresh db i18n success
2025-9-29 11:45:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:45:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:45:06-debug: asset-db:refresh-all-database (115ms)
2025-9-29 11:45:06-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:45:06-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:45:09-debug: refresh db internal success
2025-9-29 11:45:09-debug: refresh db assets success
2025-9-29 11:45:09-debug: refresh db i18n success
2025-9-29 11:45:09-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:45:09-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:45:09-debug: asset-db:refresh-all-database (118ms)
2025-9-29 11:45:09-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:45:09-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:47:41-debug: refresh db internal success
2025-9-29 11:47:41-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 11:47:41-debug: refresh db assets success
2025-9-29 11:47:41-debug: refresh db i18n success
2025-9-29 11:47:41-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:47:41-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:47:41-debug: asset-db:refresh-all-database (150ms)
2025-9-29 11:47:41-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:47:41-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:47:45-debug: refresh db internal success
2025-9-29 11:47:45-debug: refresh db assets success
2025-9-29 11:47:45-debug: refresh db i18n success
2025-9-29 11:47:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:47:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:47:45-debug: asset-db:refresh-all-database (145ms)
2025-9-29 11:47:45-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:47:45-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:47:47-debug: refresh db internal success
2025-9-29 11:47:47-debug: refresh db assets success
2025-9-29 11:47:47-debug: refresh db i18n success
2025-9-29 11:47:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:47:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:47:47-debug: asset-db:refresh-all-database (122ms)
2025-9-29 11:47:47-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:48:14-debug: refresh db internal success
2025-9-29 11:48:15-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 11:48:15-debug: refresh db assets success
2025-9-29 11:48:15-debug: refresh db i18n success
2025-9-29 11:48:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:48:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:48:15-debug: asset-db:refresh-all-database (155ms)
2025-9-29 11:48:15-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:48:33-debug: refresh db internal success
2025-9-29 11:48:33-debug: refresh db assets success
2025-9-29 11:48:33-debug: refresh db i18n success
2025-9-29 11:48:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:48:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:48:33-debug: asset-db:refresh-all-database (148ms)
2025-9-29 11:48:33-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:48:33-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:48:40-debug: refresh db internal success
2025-9-29 11:48:40-debug: refresh db assets success
2025-9-29 11:48:40-debug: refresh db i18n success
2025-9-29 11:48:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:48:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:48:40-debug: asset-db:refresh-all-database (122ms)
2025-9-29 11:48:40-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:48:40-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 11:48:42-debug: refresh db internal success
2025-9-29 11:48:42-debug: refresh db assets success
2025-9-29 11:48:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:48:42-debug: refresh db i18n success
2025-9-29 11:48:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:48:42-debug: asset-db:refresh-all-database (130ms)
2025-9-29 11:48:42-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-29 11:48:42-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-29 11:49:42-debug: refresh db internal success
2025-9-29 11:49:42-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 11:49:42-debug: refresh db assets success
2025-9-29 11:49:42-debug: refresh db i18n success
2025-9-29 11:49:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 11:49:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 11:49:42-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 11:49:42-debug: asset-db:refresh-all-database (151ms)
2025-9-29 11:49:42-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 12:01:16-debug: refresh db internal success
2025-9-29 12:01:16-debug: refresh db assets success
2025-9-29 12:01:16-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 12:01:16-debug: refresh db i18n success
2025-9-29 12:01:16-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 12:01:16-debug: asset-db:refresh-all-database (121ms)
2025-9-29 12:01:17-debug: refresh db internal success
2025-9-29 12:01:18-debug: refresh db assets success
2025-9-29 12:01:18-debug: refresh db i18n success
2025-9-29 12:01:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 12:01:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 12:01:18-debug: asset-db:refresh-all-database (118ms)
2025-9-29 12:03:51-debug: refresh db internal success
2025-9-29 12:03:51-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-29 12:03:51-debug: refresh db assets success
2025-9-29 12:03:51-debug: refresh db i18n success
2025-9-29 12:03:51-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 12:03:51-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 12:03:51-debug: asset-db:refresh-all-database (157ms)
2025-9-29 12:03:51-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 12:03:51-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-29 12:03:55-debug: refresh db internal success
2025-9-29 12:03:55-debug: refresh db assets success
2025-9-29 12:03:55-debug: refresh db i18n success
2025-9-29 12:03:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 12:03:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 12:03:55-debug: asset-db:refresh-all-database (156ms)
2025-9-29 12:03:56-debug: refresh db internal success
2025-9-29 12:03:56-debug: refresh db assets success
2025-9-29 12:03:56-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-29 12:03:56-debug: refresh db i18n success
2025-9-29 12:03:56-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-29 12:03:56-debug: asset-db:refresh-all-database (116ms)
2025-9-29 12:03:56-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-29 12:03:56-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
