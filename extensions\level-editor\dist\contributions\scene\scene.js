"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.methods = void 0;
exports.load = load;
exports.unload = unload;
const path_1 = require("path");
// @ts-ignore
module.paths.push((0, path_1.join)(Editor.App.path, 'node_modules'));
// 当前版本需要在 module.paths 修改后才能正常使用 cc 模块
// 并且如果希望正常显示 cc 的定义，需要手动将 engine 文件夹里的 cc.d.ts 添加到插件的 tsconfig 里
// 当前版本的 cc 定义文件可以在当前项目的 temp/declarations/cc.d.ts 找到
const cc_1 = require("cc");
const { _utils } = cc_1.Prefab;
function load() { }
;
function unload() { }
;
exports.methods = {
    saveLevel() {
        console.log('saveLevel in scene');
        const { director } = require('cc');
        let levelEditorUI = director.getScene().getComponentInChildren("LevelEditorUI");
        if (levelEditorUI) {
            levelEditorUI.save = true;
        }
    },
    playLevel() {
        console.log('playLevel in scene');
        const { director } = require('cc');
        let levelEditorUI = director.getScene().getComponentInChildren("LevelEditorUI");
        if (levelEditorUI) {
            var play = levelEditorUI.play;
            levelEditorUI.play = !play;
        }
    },
    levelStart() {
        console.log('levelStart in scene');
        const { director } = require('cc');
        let levelEditorUI = director.getScene().getComponentInChildren("LevelEditorUI");
        if (levelEditorUI) {
            levelEditorUI.progress = 0;
        }
    },
    levelEnd() {
        console.log('levelEnd in scene');
        const { director } = require('cc');
        let levelEditorUI = director.getScene().getComponentInChildren("LevelEditorUI");
        if (levelEditorUI) {
            levelEditorUI.progress = 1;
        }
    },
    instantiatePrefab(component_uuid, prefabUuid) {
        // console.log('instantiatePrefab:', component_uuid, prefabUuid);
        var _a, _b, _c;
        let targetNode = (_a = cc_1.director.getScene()) === null || _a === void 0 ? void 0 : _a.getChildByUuid(component_uuid);
        if (!targetNode) {
            targetNode = (_b = cc_1.director.getScene()) === null || _b === void 0 ? void 0 : _b.getChildByName('Canvas');
        }
        if (targetNode) {
            // console.log("Canvas node found: ", targetNode.getComponent("EmitterEditor"));
            // @ts-ignore
            Editor.Message.request('scene', 'execute-component-method', {
                uuid: (_c = targetNode.getComponent("EmitterEditor")) === null || _c === void 0 ? void 0 : _c.uuid,
                name: 'instantiatePrefab',
                args: [prefabUuid]
            });
        }
    },
    // async saveToPrefab(component_uuid:string, nodeName: string, prefabUuid: string) {
    //     // console.log('saveToPrefab:', component_uuid, nodeName, prefabUuid);
    //     const scene = director.getScene();
    //     const target = scene!.getChildByPath(`Canvas/${nodeName}`);
    //     if (!target) {
    //         console.error("node not found:", nodeName);
    //         return;
    //     }
    //     cce.Prefab.createPrefabAssetFromNode(target.uuid, prefabUuid);
    // },
    async createBulletPrefab(sourceAssetUuid) {
        // @ts-ignore
        const sourceAssetInfo = await Editor.Message.request('asset-db', 'query-asset-info', sourceAssetUuid);
        if (!sourceAssetInfo)
            return;
        // 需要判断是spriteList还是单个sprite
        // TODO: 当sourceAssetUuid是一个目录的时候，下面这里的路径可能存在bug。还没有验证。
        // TODO: 这个目录后续可能调整到bundle目录
        const targetPrefabDir = 'db://assets/resources/game/prefabs/bullet/';
        if (sourceAssetInfo.importer === 'sprite-atlas') {
            cc_1.assetManager.loadAny({ uuid: sourceAssetUuid }, (err, asset) => {
                if (err) {
                    console.error(err);
                    return;
                }
                const spriteList = asset;
                for (let spriteFrame of spriteList.getSpriteFrames()) {
                    const targetPrefabName = spriteFrame.name;
                    const targetPrefabPath = targetPrefabDir + targetPrefabName + '.prefab';
                    createNewBullet(targetPrefabName, targetPrefabPath, spriteFrame, spriteList);
                }
            });
        }
        else if (sourceAssetInfo.importer === 'sprite-frame') {
            cc_1.assetManager.loadAny({ uuid: sourceAssetUuid }, (err, asset) => {
                if (err) {
                    console.error(err);
                    return;
                }
                const spriteFrame = asset;
                const targetPrefabName = spriteFrame.name;
                const targetPrefabPath = targetPrefabDir + targetPrefabName + '.prefab';
                createNewBullet(targetPrefabName, targetPrefabPath, spriteFrame, null);
            });
        }
        else {
            console.warn('Skipping unknown asset type:', sourceAssetInfo.importer);
        }
    },
    async createLevelPrefab(sourceAssetUuid) {
        // @ts-ignore
        const sourceAssetInfo = await Editor.Message.request('asset-db', 'query-asset-info', sourceAssetUuid);
        if (!sourceAssetInfo)
            return;
        // remove /x/x/*.* -> /x/x/
        let targetPrefabDir = sourceAssetInfo.path.replace('/Texture/', '/Prefab/');
        const targetPrefabPath = targetPrefabDir.substring(0, targetPrefabDir.lastIndexOf('/')) + '.prefab';
        const targetPrefabName = targetPrefabDir.substring(targetPrefabDir.lastIndexOf('/') + 1);
        if (sourceAssetInfo.importer === 'sprite-frame') {
            cc_1.assetManager.loadAny({ uuid: sourceAssetUuid }, (err, asset) => {
                if (err) {
                    console.error(err);
                    return;
                }
                // console.log(`Level Prefab will be created at path ${targetPrefabPath}`);
                const spriteFrame = asset;
                if (spriteFrame) {
                    createNewLevelObject(targetPrefabName, targetPrefabPath, spriteFrame); // 这个是创建prefab，不是prefab里面的节点，所以不需要parent
                }
                else {
                    console.warn('Asset is not a sprite-frame:', sourceAssetInfo);
                }
            });
        }
        else {
            console.warn('Skipping unknown asset type:', sourceAssetInfo.importer);
        }
    },
    async createMainPlanePrefab(sourceAssetUuid) {
        console.log(`createMainPlanePrefab uuid:[${sourceAssetUuid}]`);
        // @ts-ignore
        const sourceAssetInfo = await Editor.Message.request('asset-db', 'query-asset-info', sourceAssetUuid);
        if (!sourceAssetInfo) {
            console.error('query-asset-info failed:', sourceAssetUuid);
            return;
        }
        const planeId = cc_1.path.basename(sourceAssetInfo.path);
        const prefabPath = `db://assets/resources/game/prefabs/plane/mainplane/${planeId}.prefab`;
        let image = `db://assets/resources/game/spine/plane/mainplane/${planeId}.png`;
        let ske = `db://assets/resources/game/spine/plane/mainplane/${planeId}.json`;
        let atlas = `db://assets/resources/game/spine/plane/mainplane/${planeId}.atlas`;
        const temp = new cc_1.Node("temp");
        const scene = cc_1.director.getScene().addChild(temp);
        const rootNode = new cc_1.Node(planeId);
        temp.addChild(rootNode);
        const planeNode = new cc_1.Node("plane");
        rootNode.addChild(planeNode);
        const spine = planeNode.addComponent(cc_1.sp.Skeleton);
        cc_1.assetManager.loadAny({ uuid: sourceAssetUuid }, async (err, asset) => {
            //resources.load(`game/spine/plane/mainplane/${planeId}/${planeId}.json`, sp.SkeletonData, (err:Error|null, asset:sp.SkeletonData) => {
            spine.skeletonData = asset;
            try {
                await cce.Prefab.createPrefabAssetFromNode(rootNode.uuid, prefabPath);
                console.log(`main plane prefab created: ${prefabPath}`);
                temp.removeFromParent();
                Editor.Message.request('asset-db', 'refresh-asset', prefabPath);
            }
            catch (e) {
                console.error('Failed to create level prefab:', e, ', on asset: ', prefabPath);
            }
        });
    },
    // 阵型相关: addFormationPoint & saveFormationGroup
    addFormationPoint(formationGroupUuid) {
        const { director } = require('cc');
        let formationEditor = findComponentByUuid(director.getScene(), formationGroupUuid, 'FormationEditor');
        if (formationEditor) {
            // @ts-ignore
            formationEditor.addNewPoint(0, 0);
        }
        else {
            console.error('Formation group not found:', formationGroupUuid);
        }
    },
    async saveFormationGroup(formationGroupUuid) {
        const { director } = require('cc');
        let formationEditor = findComponentByUuid(director.getScene(), formationGroupUuid, 'FormationEditor');
        if (formationEditor) {
            // @ts-ignore
            const jsonData = formationEditor.formationData;
            // save this as JsonAsset
            if (jsonData === null) {
                // @ts-ignore
                let file = await Editor.Dialog.save({
                    path: Editor.Project.path + '/assets/resources/game/level/wave/formation/',
                    filters: [
                        { name: 'Json', extensions: ['json'] },
                    ],
                });
                if (file.canceled || !file.filePath) {
                    return;
                }
                // @ts-ignore
                Editor.Message.request('asset-db', 'create-asset', file.filePath, formationEditor.save()).then((res) => {
                    if (res) {
                        cc_1.assetManager.loadAny({ uuid: res.uuid }, (err, asset) => {
                            if (err) {
                                console.error(err);
                            }
                            else {
                                // @ts-ignore 
                                formationEditor.formationData = asset;
                            }
                        });
                    }
                });
            }
            else {
                // @ts-ignore
                Editor.Message.request('asset-db', 'save-asset', jsonData.uuid, formationEditor.save()).then((res) => {
                    if (res) {
                        cc_1.assetManager.loadAny({ uuid: res.uuid }, (err, asset) => {
                            if (err) {
                                console.error(err);
                            }
                            else {
                                // @ts-ignore 
                                formationEditor.formationData = asset;
                            }
                        });
                    }
                });
            }
        }
        else {
            console.error('Formation group not found:', formationGroupUuid);
        }
    },
    // 路径相关: addPathPoint & savePath & clearPath
    addPathPoint(pathEditorUuid) {
        const { director } = require('cc');
        let pathEditor = findComponentByUuid(director.getScene(), pathEditorUuid, 'PathEditor');
        if (pathEditor) {
            // @ts-ignore
            pathEditor.addNewPoint(0, 0);
        }
        else {
            console.error('Path editor not found:', pathEditorUuid);
        }
    },
    async savePath(pathEditorUuid) {
        const { director } = require('cc');
        let pathEditor = findComponentByUuid(director.getScene(), pathEditorUuid, 'PathEditor');
        if (pathEditor) {
            // @ts-ignore
            const jsonData = pathEditor.pathData;
            // save this as JsonAsset
            if (jsonData === null) {
                // @ts-ignore
                let file = await Editor.Dialog.save({
                    path: Editor.Project.path + '/assets/resources/game/level/wave/path/',
                    filters: [
                        { name: 'Json', extensions: ['json'] },
                    ],
                });
                if (file.canceled || !file.filePath) {
                    return;
                }
                // @ts-ignore
                Editor.Message.request('asset-db', 'create-asset', file.filePath, pathEditor.save()).then((res) => {
                    if (res) {
                        cc_1.assetManager.loadAny({ uuid: res.uuid }, (err, asset) => {
                            if (err) {
                                console.error(err);
                            }
                            else {
                                // @ts-ignore
                                pathEditor.pathData = asset;
                            }
                        });
                    }
                });
            }
            else {
                // @ts-ignore
                Editor.Message.request('asset-db', 'save-asset', jsonData.uuid, pathEditor.save()).then((res) => {
                    if (res) {
                        cc_1.assetManager.loadAny({ uuid: res.uuid }, (err, asset) => {
                            if (err) {
                                console.error(err);
                            }
                            else {
                                // @ts-ignore
                                pathEditor.pathData = asset;
                            }
                        });
                    }
                });
            }
        }
        else {
            console.error('Path editor not found:', pathEditorUuid);
        }
    },
    clearPath(pathEditorUuid) {
        const { director } = require('cc');
        let pathEditor = findComponentByUuid(director.getScene(), pathEditorUuid, 'PathEditor');
        if (pathEditor) {
            // @ts-ignore
            pathEditor.node.removeAllChildren();
            // @ts-ignore
            pathEditor.updateCurve();
        }
        else {
            console.error('Path editor not found:', pathEditorUuid);
        }
    },
    // 加载阵型数据到FormationEditor
    loadFormationData(assetUuid) {
        const { director } = require('cc');
        const scene = director.getScene();
        // 查找FormationEditor组件
        let formationEditor = scene === null || scene === void 0 ? void 0 : scene.getComponentInChildren('FormationEditor');
        if (formationEditor) {
            // 加载资源并设置到FormationEditor
            cc_1.assetManager.loadAny({ uuid: assetUuid }, (err, asset) => {
                if (err) {
                    console.error('Failed to load formation asset:', err);
                }
                else {
                    // @ts-ignore
                    formationEditor.formationData = asset;
                    console.log('Formation data loaded:', asset);
                }
            });
        }
        else {
            console.error('FormationEditor component not found in scene');
        }
    },
    // 加载路径数据到PathEditor
    loadPathData(assetUuid) {
        const { director } = require('cc');
        const scene = director.getScene();
        // 查找PathEditor组件
        let pathEditor = scene === null || scene === void 0 ? void 0 : scene.getComponentInChildren('PathEditor');
        if (pathEditor) {
            // 加载资源并设置到PathEditor
            cc_1.assetManager.loadAny({ uuid: assetUuid }, (err, asset) => {
                if (err) {
                    console.error('Failed to load path asset:', err);
                }
                else {
                    // @ts-ignore
                    pathEditor.pathData = asset;
                    console.log('Path data loaded:', asset);
                }
            });
        }
        else {
            console.error('PathEditor component not found in scene');
        }
    },
};
function findComponentByUuid(node, uuid, className) {
    if (!node)
        return null;
    // 检查当前节点是否有指定的组件，如果有且UUID匹配则返回
    const component = node.getComponent(className);
    if (component) {
        // console.log(`${node.name} has component ${className}, uuid: ${component.uuid}, target: ${uuid}`);
        if (component.uuid === uuid) {
            return component;
        }
    }
    // 无论当前节点是否有组件，都要搜索子节点
    for (let i = 0; i < node.children.length; i++) {
        let found = findComponentByUuid(node.children[i], uuid, className);
        if (found) {
            return found;
        }
    }
    return null;
}
async function createNewBullet(prefabName, prefabPath, sourceSprite, sourceSpriteList) {
    const scene = cc_1.director.getScene();
    const target = scene.getChildByName('Canvas');
    if (!target) {
        console.error("Canvas node not found");
        return;
    }
    let node = new cc_1.Node(prefabName);
    node.parent = target;
    node.setPosition(new cc_1.Vec3(0, 0, 0));
    let uiTransform = node.addComponent(cc_1.UITransform); // Ensure it has a transform component
    node.addComponent('FBoxCollider');
    let movable = node.addComponent('DefaultMove');
    node.addComponent('Bullet');
    node.addComponent('cc.BoxCollider2D');
    let sprite = node.addComponent(cc_1.Sprite);
    if (sourceSpriteList) {
        sprite.spriteFrame = sourceSprite;
        sprite.spriteAtlas = sourceSpriteList;
    }
    if (sourceSprite) {
        uiTransform.contentSize = new cc_1.Size(sourceSprite.rect.width, sourceSprite.rect.height);
    }
    const nodeUuid = node.uuid;
    try {
        await cce.Prefab.createPrefabAssetFromNode(nodeUuid, prefabPath);
        console.log(`Bullet prefab created: ${prefabPath}`);
        Editor.Message.request('asset-db', 'refresh-asset', prefabPath);
    }
    catch (e) {
        console.error('Failed to create bullet prefab:', e);
    }
}
async function createNewLevelObject(prefabName, prefabPath, sourceSprite) {
    const scene = cc_1.director.getScene();
    let node = new cc_1.Node(prefabName);
    scene.addChild(node);
    let uiTransform = node.addComponent(cc_1.UITransform); // Ensure it has a transform component
    let sprite = node.addComponent(cc_1.Sprite);
    if (sourceSprite) {
        sprite.spriteFrame = sourceSprite;
        uiTransform.contentSize = new cc_1.Size(sourceSprite.rect.width, sourceSprite.rect.height);
    }
    const nodeUuid = node.uuid;
    try {
        await cce.Prefab.createPrefabAssetFromNode(nodeUuid, prefabPath);
        console.log(`Level prefab created: ${prefabPath}`);
        Editor.Message.request('asset-db', 'refresh-asset', prefabPath);
    }
    catch (e) {
        console.error('Failed to create level prefab:', e, ', on asset: ', prefabPath);
    }
}
//# sourceMappingURL=data:application/json;base64,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