"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.methods = void 0;
exports.load = load;
exports.unload = unload;
const path_1 = require("path");
// @ts-ignore
module.paths.push((0, path_1.join)(Editor.App.path, 'node_modules'));
// 当前版本需要在 module.paths 修改后才能正常使用 cc 模块
// 并且如果希望正常显示 cc 的定义，需要手动将 engine 文件夹里的 cc.d.ts 添加到插件的 tsconfig 里
// 当前版本的 cc 定义文件可以在当前项目的 temp/declarations/cc.d.ts 找到
const cc_1 = require("cc");
const { _utils } = cc_1.Prefab;
function load() { }
;
function unload() { }
;
exports.methods = {
    saveLevel() {
        console.log('saveLevel in scene');
        const { director } = require('cc');
        let levelEditorUI = director.getScene().getComponentInChildren("LevelEditorUI");
        if (levelEditorUI) {
            levelEditorUI.save = true;
        }
    },
    playLevel() {
        console.log('playLevel in scene');
        const { director } = require('cc');
        let levelEditorUI = director.getScene().getComponentInChildren("LevelEditorUI");
        if (levelEditorUI) {
            var play = levelEditorUI.play;
            levelEditorUI.play = !play;
        }
    },
    levelStart() {
        console.log('levelStart in scene');
        const { director } = require('cc');
        let levelEditorUI = director.getScene().getComponentInChildren("LevelEditorUI");
        if (levelEditorUI) {
            levelEditorUI.progress = 0;
        }
    },
    levelEnd() {
        console.log('levelEnd in scene');
        const { director } = require('cc');
        let levelEditorUI = director.getScene().getComponentInChildren("LevelEditorUI");
        if (levelEditorUI) {
            levelEditorUI.progress = 1;
        }
    },
    instantiatePrefab(component_uuid, prefabUuid) {
        // console.log('instantiatePrefab:', component_uuid, prefabUuid);
        var _a, _b, _c;
        let targetNode = (_a = cc_1.director.getScene()) === null || _a === void 0 ? void 0 : _a.getChildByUuid(component_uuid);
        if (!targetNode) {
            targetNode = (_b = cc_1.director.getScene()) === null || _b === void 0 ? void 0 : _b.getChildByName('Canvas');
        }
        if (targetNode) {
            // console.log("Canvas node found: ", targetNode.getComponent("EmitterEditor"));
            // @ts-ignore
            Editor.Message.request('scene', 'execute-component-method', {
                uuid: (_c = targetNode.getComponent("EmitterEditor")) === null || _c === void 0 ? void 0 : _c.uuid,
                name: 'instantiatePrefab',
                args: [prefabUuid]
            });
        }
    },
    // async saveToPrefab(component_uuid:string, nodeName: string, prefabUuid: string) {
    //     // console.log('saveToPrefab:', component_uuid, nodeName, prefabUuid);
    //     const scene = director.getScene();
    //     const target = scene!.getChildByPath(`Canvas/${nodeName}`);
    //     if (!target) {
    //         console.error("node not found:", nodeName);
    //         return;
    //     }
    //     cce.Prefab.createPrefabAssetFromNode(target.uuid, prefabUuid);
    // },
    async createBulletPrefab(sourceAssetUuid) {
        // @ts-ignore
        const sourceAssetInfo = await Editor.Message.request('asset-db', 'query-asset-info', sourceAssetUuid);
        if (!sourceAssetInfo)
            return;
        // 需要判断是spriteList还是单个sprite
        // TODO: 当sourceAssetUuid是一个目录的时候，下面这里的路径可能存在bug。还没有验证。
        // TODO: 这个目录后续可能调整到bundle目录
        const targetPrefabDir = 'db://assets/resources/game/prefabs/bullet/';
        if (sourceAssetInfo.importer === 'sprite-atlas') {
            cc_1.assetManager.loadAny({ uuid: sourceAssetUuid }, (err, asset) => {
                if (err) {
                    console.error(err);
                    return;
                }
                const spriteList = asset;
                for (let spriteFrame of spriteList.getSpriteFrames()) {
                    const targetPrefabName = spriteFrame.name;
                    const targetPrefabPath = targetPrefabDir + targetPrefabName + '.prefab';
                    createNewBullet(targetPrefabName, targetPrefabPath, spriteFrame, spriteList);
                }
            });
        }
        else if (sourceAssetInfo.importer === 'sprite-frame') {
            cc_1.assetManager.loadAny({ uuid: sourceAssetUuid }, (err, asset) => {
                if (err) {
                    console.error(err);
                    return;
                }
                const spriteFrame = asset;
                const targetPrefabName = spriteFrame.name;
                const targetPrefabPath = targetPrefabDir + targetPrefabName + '.prefab';
                createNewBullet(targetPrefabName, targetPrefabPath, spriteFrame, null);
            });
        }
        else {
            console.warn('Skipping unknown asset type:', sourceAssetInfo.importer);
        }
    },
    async createLevelPrefab(sourceAssetUuid) {
        // @ts-ignore
        const sourceAssetInfo = await Editor.Message.request('asset-db', 'query-asset-info', sourceAssetUuid);
        if (!sourceAssetInfo)
            return;
        // remove /x/x/*.* -> /x/x/
        let targetPrefabDir = sourceAssetInfo.path.replace('/Texture/', '/Prefab/');
        const targetPrefabPath = targetPrefabDir.substring(0, targetPrefabDir.lastIndexOf('/')) + '.prefab';
        const targetPrefabName = targetPrefabDir.substring(targetPrefabDir.lastIndexOf('/') + 1);
        if (sourceAssetInfo.importer === 'sprite-frame') {
            cc_1.assetManager.loadAny({ uuid: sourceAssetUuid }, (err, asset) => {
                if (err) {
                    console.error(err);
                    return;
                }
                // console.log(`Level Prefab will be created at path ${targetPrefabPath}`);
                const spriteFrame = asset;
                if (spriteFrame) {
                    createNewLevelObject(targetPrefabName, targetPrefabPath, spriteFrame); // 这个是创建prefab，不是prefab里面的节点，所以不需要parent
                }
                else {
                    console.warn('Asset is not a sprite-frame:', sourceAssetInfo);
                }
            });
        }
        else {
            console.warn('Skipping unknown asset type:', sourceAssetInfo.importer);
        }
    },
    // 阵型相关: addFormationPoint & saveFormationGroup
    addFormationPoint(formationGroupUuid) {
        const { director } = require('cc');
        let formationEditor = findComponentByUuid(director.getScene(), formationGroupUuid, 'FormationEditor');
        if (formationEditor) {
            // @ts-ignore
            formationEditor.addNewPoint(0, 0);
        }
        else {
            console.error('Formation group not found:', formationGroupUuid);
        }
    },
    async saveFormationGroup(formationGroupUuid) {
        const { director } = require('cc');
        let formationEditor = findComponentByUuid(director.getScene(), formationGroupUuid, 'FormationEditor');
        if (formationEditor) {
            // @ts-ignore
            const jsonData = formationEditor.formationData;
            // save this as JsonAsset
            if (jsonData === null) {
                // @ts-ignore
                let file = await Editor.Dialog.save({
                    path: 'db://assets/resources/game/level/wave/formation/',
                    filters: [
                        { name: 'Json', extensions: ['json'] },
                    ],
                });
                if (file.canceled || !file.filePath) {
                    return;
                }
                // @ts-ignore
                Editor.Message.request('asset-db', 'create-asset', file.filePath, formationEditor.save()).then((res) => {
                    if (res) {
                        cc_1.assetManager.loadAny({ uuid: res.uuid }, (err, asset) => {
                            if (err) {
                                console.error(err);
                            }
                            else {
                                // @ts-ignore 
                                formationEditor.formationData = asset;
                            }
                        });
                    }
                });
            }
            else {
                // @ts-ignore
                Editor.Message.request('asset-db', 'save-asset', jsonData.uuid, formationEditor.save()).then((res) => {
                    if (res) {
                        cc_1.assetManager.loadAny({ uuid: res.uuid }, (err, asset) => {
                            if (err) {
                                console.error(err);
                            }
                            else {
                                // @ts-ignore 
                                formationEditor.formationData = asset;
                            }
                        });
                    }
                });
            }
        }
        else {
            console.error('Formation group not found:', formationGroupUuid);
        }
    },
    // 路径相关: addPathPoint & savePath & clearPath
    addPathPoint(pathEditorUuid) {
        const { director } = require('cc');
        let pathEditor = findComponentByUuid(director.getScene(), pathEditorUuid, 'PathEditor');
        if (pathEditor) {
            // @ts-ignore
            pathEditor.addNewPoint(0, 0);
        }
        else {
            console.error('Path editor not found:', pathEditorUuid);
        }
    },
    async savePath(pathEditorUuid) {
        const { director } = require('cc');
        let pathEditor = findComponentByUuid(director.getScene(), pathEditorUuid, 'PathEditor');
        if (pathEditor) {
            // @ts-ignore
            const jsonData = pathEditor.pathData;
            // save this as JsonAsset
            if (jsonData === null) {
                // @ts-ignore
                let file = await Editor.Dialog.save({
                    path: 'db://assets/resources/game/level/wave/path/',
                    filters: [
                        { name: 'Json', extensions: ['json'] },
                    ],
                });
                if (file.canceled || !file.filePath) {
                    return;
                }
                // @ts-ignore
                Editor.Message.request('asset-db', 'create-asset', file.filePath, pathEditor.save()).then((res) => {
                    if (res) {
                        cc_1.assetManager.loadAny({ uuid: res.uuid }, (err, asset) => {
                            if (err) {
                                console.error(err);
                            }
                            else {
                                // @ts-ignore
                                pathEditor.pathData = asset;
                            }
                        });
                    }
                });
            }
            else {
                // @ts-ignore
                Editor.Message.request('asset-db', 'save-asset', jsonData.uuid, pathEditor.save()).then((res) => {
                    if (res) {
                        cc_1.assetManager.loadAny({ uuid: res.uuid }, (err, asset) => {
                            if (err) {
                                console.error(err);
                            }
                            else {
                                // @ts-ignore
                                pathEditor.pathData = asset;
                            }
                        });
                    }
                });
            }
        }
        else {
            console.error('Path editor not found:', pathEditorUuid);
        }
    },
    clearPath(pathEditorUuid) {
        const { director } = require('cc');
        let pathEditor = findComponentByUuid(director.getScene(), pathEditorUuid, 'PathEditor');
        if (pathEditor) {
            // @ts-ignore
            pathEditor.node.removeAllChildren();
            // @ts-ignore
            pathEditor.updateCurve();
        }
        else {
            console.error('Path editor not found:', pathEditorUuid);
        }
    },
    // 加载阵型数据到FormationEditor
    loadFormationData(assetUuid) {
        const { director } = require('cc');
        const scene = director.getScene();
        // 查找FormationEditor组件
        let formationEditor = scene === null || scene === void 0 ? void 0 : scene.getComponentInChildren('FormationEditor');
        if (formationEditor) {
            // 加载资源并设置到FormationEditor
            cc_1.assetManager.loadAny({ uuid: assetUuid }, (err, asset) => {
                if (err) {
                    console.error('Failed to load formation asset:', err);
                }
                else {
                    // @ts-ignore
                    formationEditor.formationData = asset;
                    console.log('Formation data loaded:', asset);
                }
            });
        }
        else {
            console.error('FormationEditor component not found in scene');
        }
    },
    // 加载路径数据到PathEditor
    loadPathData(assetUuid) {
        const { director } = require('cc');
        const scene = director.getScene();
        // 查找PathEditor组件
        let pathEditor = scene === null || scene === void 0 ? void 0 : scene.getComponentInChildren('PathEditor');
        if (pathEditor) {
            // 加载资源并设置到PathEditor
            cc_1.assetManager.loadAny({ uuid: assetUuid }, (err, asset) => {
                if (err) {
                    console.error('Failed to load path asset:', err);
                }
                else {
                    // @ts-ignore
                    pathEditor.pathData = asset;
                    console.log('Path data loaded:', asset);
                }
            });
        }
        else {
            console.error('PathEditor component not found in scene');
        }
    },
};
function findComponentByUuid(node, uuid, className) {
    if (!node)
        return null;
    // 检查当前节点是否有指定的组件，如果有且UUID匹配则返回
    const component = node.getComponent(className);
    if (component) {
        // console.log(`${node.name} has component ${className}, uuid: ${component.uuid}, target: ${uuid}`);
        if (component.uuid === uuid) {
            return component;
        }
    }
    // 无论当前节点是否有组件，都要搜索子节点
    for (let i = 0; i < node.children.length; i++) {
        let found = findComponentByUuid(node.children[i], uuid, className);
        if (found) {
            return found;
        }
    }
    return null;
}
async function createNewBullet(prefabName, prefabPath, sourceSprite, sourceSpriteList) {
    const scene = cc_1.director.getScene();
    const target = scene.getChildByName('Canvas');
    if (!target) {
        console.error("Canvas node not found");
        return;
    }
    let node = new cc_1.Node(prefabName);
    node.parent = target;
    node.setPosition(new cc_1.Vec3(0, 0, 0));
    let uiTransform = node.addComponent(cc_1.UITransform); // Ensure it has a transform component
    node.addComponent('FBoxCollider');
    let movable = node.addComponent('DefaultMove');
    node.addComponent('Bullet');
    node.addComponent('cc.BoxCollider2D');
    let sprite = node.addComponent(cc_1.Sprite);
    if (sourceSpriteList) {
        sprite.spriteFrame = sourceSprite;
        sprite.spriteAtlas = sourceSpriteList;
    }
    if (sourceSprite) {
        uiTransform.contentSize = new cc_1.Size(sourceSprite.rect.width, sourceSprite.rect.height);
    }
    const nodeUuid = node.uuid;
    try {
        await cce.Prefab.createPrefabAssetFromNode(nodeUuid, prefabPath);
        console.log(`Bullet prefab created: ${prefabPath}`);
        Editor.Message.request('asset-db', 'refresh-asset', prefabPath);
    }
    catch (e) {
        console.error('Failed to create bullet prefab:', e);
    }
}
async function createNewLevelObject(prefabName, prefabPath, sourceSprite) {
    const scene = cc_1.director.getScene();
    let node = new cc_1.Node(prefabName);
    scene.addChild(node);
    let uiTransform = node.addComponent(cc_1.UITransform); // Ensure it has a transform component
    let sprite = node.addComponent(cc_1.Sprite);
    if (sourceSprite) {
        sprite.spriteFrame = sourceSprite;
        uiTransform.contentSize = new cc_1.Size(sourceSprite.rect.width, sourceSprite.rect.height);
    }
    const nodeUuid = node.uuid;
    try {
        await cce.Prefab.createPrefabAssetFromNode(nodeUuid, prefabPath);
        console.log(`Level prefab created: ${prefabPath}`);
        Editor.Message.request('asset-db', 'refresh-asset', prefabPath);
    }
    catch (e) {
        console.error('Failed to create level prefab:', e, ', on asset: ', prefabPath);
    }
}
//# sourceMappingURL=data:application/json;base64,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