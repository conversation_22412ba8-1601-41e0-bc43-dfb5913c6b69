"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.onCreateMenu = onCreateMenu;
exports.onAssetMenu = onAssetMenu;
function onCreateMenu(assetInfo) {
    return [
        {
            label: '飞机游戏',
            submenu: [
                {
                    label: '创建波次',
                    click() {
                        console.log('wave');
                        console.log(assetInfo);
                    },
                },
                {
                    label: '创建阵型',
                    click() {
                        console.log('formation');
                        console.log(assetInfo);
                    },
                },
            ],
        },
    ];
}
;
function onAssetMenu(assetInfo) {
    const submenu = [
        {
            label: '创建关卡Prefab',
            //enabled: assetInfo.isDirectory,
            click() {
                createLevelPrefab(assetInfo);
            },
        },
        {
            label: '创建子弹prefab',
            //enabled: !assetInfo.isDirectory,
            click() {
                createBulletPrefabs(assetInfo);
            },
        },
    ];
    // 检查是否是阵型JSON文件
    if (!assetInfo.isDirectory &&
        assetInfo.url.includes('assets/resources/game/level/wave/formation') &&
        assetInfo.url.endsWith('.json')) {
        submenu.push({
            label: '编辑阵型',
            click() {
                editFormation(assetInfo);
            },
        });
    }
    // 检查是否是路径JSON文件
    if (!assetInfo.isDirectory &&
        assetInfo.url.includes('assets/resources/game/level/wave/path') &&
        assetInfo.url.endsWith('.json')) {
        submenu.push({
            label: '编辑路径',
            click() {
                editPath(assetInfo);
            },
        });
    }
    if (!assetInfo.isDirectory &&
        assetInfo.url.includes('assets/resources/game/spine/plane/mainplane/') &&
        assetInfo.url.endsWith('.json')) {
        submenu.push({
            label: '创建自机prefab',
            click() {
                createMainPlanePrefab(assetInfo);
            },
        });
    }
    return [
        {
            label: '飞机游戏',
            submenu: submenu,
        },
    ];
}
;
function getAssetUuidByPath(path) {
    return new Promise((resolve, reject) => {
        // @ts-ignore
        Editor.Message.request('asset-db', 'query-uuid', path).then((res) => {
            resolve(res);
        }).catch((err) => {
            console.error('Failed to query uuid:', err);
            reject(err);
        });
    });
}
function getAssetUuidsByPath(path) {
    return new Promise((resolve, reject) => {
        // @ts-ignore
        Editor.Message.request('asset-db', 'query-assets', { pattern: path + '/**' }).then((res) => {
            const arr = Array.isArray(res) ? res : (Array.isArray(res === null || res === void 0 ? void 0 : res[0]) ? res[0] : []);
            const assets = arr
                .filter((a) => a && !a.isDirectory)
                .map((a) => ({
                name: String(a.name || ''),
                path: a.path || '',
                uuid: a.uuid || ''
            }))
                .filter(p => p.name)
                .sort((a, b) => a.name.localeCompare(b.name));
            resolve(assets.map(a => a.uuid)); // 只需要uuid即可，不需要其他信息了。
        }).catch((err) => {
            console.error('Failed to query assets:', err);
            reject(err);
        });
    });
}
function createLevelPrefab(assetInfo) {
    console.log(assetInfo);
    if (assetInfo.isDirectory) {
        getAssetUuidsByPath(assetInfo.url).then((uuids) => {
            uuids.forEach((uuid) => {
                // @ts-ignore
                Editor.Message.request('scene', 'execute-scene-script', {
                    name: 'level-editor',
                    method: 'createLevelPrefab',
                    args: [uuid]
                });
            });
        });
    }
    else {
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'level-editor',
            method: 'createLevelPrefab',
            args: [assetInfo.uuid]
        });
    }
}
function createBulletPrefabs(assetInfo) {
    if (assetInfo.isDirectory) {
        getAssetUuidsByPath(assetInfo.url).then((uuids) => {
            uuids.forEach((uuid) => {
                // @ts-ignore
                Editor.Message.request('scene', 'execute-scene-script', {
                    name: 'level-editor',
                    method: 'createBulletPrefab',
                    args: [uuid]
                });
            });
        });
    }
    else {
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'level-editor',
            method: 'createBulletPrefab',
            args: [assetInfo.uuid]
        });
    }
}
function createMainPlanePrefab(assetInfo) {
    console.log("create mainplane from spine assetInfo", assetInfo);
    // @ts-ignore
    Editor.Message.request('scene', 'execute-scene-script', {
        name: 'level-editor',
        method: 'createMainPlanePrefab',
        args: [assetInfo.uuid]
    });
}
function editFormation(assetInfo) {
    console.log('编辑阵型:', assetInfo);
    // 打开FormationEditor场景 Uuid = aa842f3a-8aea-42c2-a480-968aade3dfef
    getAssetUuidByPath('db://assets/scenes/FormationEditor.scene').then((uuid) => {
        // @ts-ignore
        Editor.Message.request('scene', 'open-scene', uuid).then(() => {
            // 场景打开后，加载选中的阵型JSON文件到FormationEditor组件
            // @ts-ignore
            Editor.Message.request('scene', 'execute-scene-script', {
                name: 'level-editor',
                method: 'loadFormationData',
                args: [assetInfo.uuid]
            });
        }).catch((err) => {
            console.error('Failed to open FormationEditor scene:', err);
        });
    });
}
function editPath(assetInfo) {
    console.log('编辑路径:', assetInfo);
    // 打开PathEditor场景
    getAssetUuidByPath('db://assets/scenes/PathEditor.scene').then((uuid) => {
        // @ts-ignore
        Editor.Message.request('scene', 'open-scene', uuid).then(() => {
            // 场景打开后，加载选中的路径JSON文件到PathEditor组件
            // @ts-ignore
            Editor.Message.request('scene', 'execute-scene-script', {
                name: 'level-editor',
                method: 'loadPathData',
                args: [assetInfo.uuid]
            });
        }).catch((err) => {
            console.error('Failed to open PathEditor scene:', err);
        });
    });
}
//# sourceMappingURL=data:application/json;base64,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