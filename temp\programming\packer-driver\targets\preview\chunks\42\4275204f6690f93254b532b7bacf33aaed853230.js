System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "crypto-js"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, logInfo, csproto, DevLoginData, CryptoJS, DevLogin, _crd;

  function _reportPossibleCrUseOflogInfo(extras) {
    _reporterNs.report("logInfo", "../../../../scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfcsproto(extras) {
    _reporterNs.report("csproto", "db://assets/bundles/common/script/autogen/pb/cs_proto.js", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLoginInfo(extras) {
    _reporterNs.report("LoginInfo", "../network/NetMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDevLoginData(extras) {
    _reporterNs.report("DevLoginData", "./DevLoginData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIPlatformSDK(extras) {
    _reporterNs.report("IPlatformSDK", "./IPlatformSDK.js", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlatformSDKUserInfo(extras) {
    _reporterNs.report("PlatformSDKUserInfo", "./IPlatformSDK.js", _context.meta, extras);
  }

  function _reportPossibleCrUseOfCryptoJS(extras) {
    _reporterNs.report("CryptoJS", "crypto-js", _context.meta, extras);
  }

  _export("DevLogin", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      logInfo = _unresolved_2.logInfo;
    }, function (_unresolved_3) {
      csproto = _unresolved_3.default;
    }, function (_unresolved_4) {
      DevLoginData = _unresolved_4.DevLoginData;
    }, function (_cryptoJs) {
      CryptoJS = _cryptoJs.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "fd7d0BedQpISZPdJyng8+O5", "DevLogin", undefined); // @ts-ignore


      _export("DevLogin", DevLogin = class DevLogin {
        showUserInfoButton() {}

        hideUserInfoButton() {}

        login(cb) {
          (_crd && logInfo === void 0 ? (_reportPossibleCrUseOflogInfo({
            error: Error()
          }), logInfo) : logInfo)("Login", "dev login");
          cb(null, {
            accountType: (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
              error: Error()
            }), csproto) : csproto).cs.ACCOUNT_TYPE.ACCOUNT_TYPE_NONE,
            code: (_crd && DevLoginData === void 0 ? (_reportPossibleCrUseOfDevLoginData({
              error: Error()
            }), DevLoginData) : DevLoginData).instance.user + "#" + (_crd && CryptoJS === void 0 ? (_reportPossibleCrUseOfCryptoJS({
              error: Error()
            }), CryptoJS) : CryptoJS).MD5((_crd && DevLoginData === void 0 ? (_reportPossibleCrUseOfDevLoginData({
              error: Error()
            }), DevLoginData) : DevLoginData).instance.password),
            serverAddr: (_crd && DevLoginData === void 0 ? (_reportPossibleCrUseOfDevLoginData({
              error: Error()
            }), DevLoginData) : DevLoginData).instance.getServerAddr()
          });
        }

        getUserInfo(cb, param) {
          cb("", {
            name: 'dev',
            icon: ""
          }, false);
        }

        getStatusBarHeight() {
          return 0;
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=4275204f6690f93254b532b7bacf33aaed853230.js.map