System.register(["__unresolved_0", "cc"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Vec2, _dec, _dec2, _class, _crd, ccclass, property, executeInEditMode, LevelPrefabParse;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _reportPossibleCrUseOfLevelDataTerrain(extras) {
    _reporterNs.report("LevelDataTerrain", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Vec2 = _cc.Vec2;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "9c583z+n7ZMY4Oviyb41RVe", "LevelPrefabParse", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Vec2']);

      ({
        ccclass,
        property,
        executeInEditMode
      } = _decorator);

      _export("LevelPrefabParse", LevelPrefabParse = (_dec = ccclass('LevelPrefabParse'), _dec2 = executeInEditMode(), _dec(_class = _dec2(_class = class LevelPrefabParse extends Component {
        onLoad() {
          console.log("LevelPrefabParse onLoad");
        }

        onDisable() {
          console.log("LevelPrefabParse onDisable");
          var data = [];
          this.node.children.forEach(node => {
            data.push({
              // @ts-ignore
              uuid: node._prefab.asset._uuid,
              position: new Vec2(node.position.x, node.position.y),
              scale: new Vec2(node.scale.x, node.scale.y),
              rotation: node.rotation.z
            });
          });

          this._exportDataAsJson(data);
        }
        /**
         * 将数据导出为JSON文件
         * @param data 要导出的数据
         */


        _exportDataAsJson(data) {
          var _this = this;

          return _asyncToGenerator(function* () {
            var jsonData = JSON.stringify(data, null, 2);
            var fileName = _this.node.name + ".json";
            var assetPath = "db://assets/resources/game/level/background/Prefab/Config/" + fileName;
            console.log("LevelPrefabParse _exportDataAsJson", assetPath); // @ts-ignore

            var sourceAssetInfo = yield Editor.Message.request('asset-db', 'query-asset-info', assetPath);

            if (sourceAssetInfo === null) {
              console.error('查询资源信息失败:');

              _this._createAsset(assetPath, jsonData);
            } else {
              console.log('导出预制体配置信息:', sourceAssetInfo);

              _this._saveAsset(sourceAssetInfo.uuid, jsonData);
            }
          })();
        }
        /**
         * 创建新资源
         */


        _createAsset(assetPath, jsonData) {
          return _asyncToGenerator(function* () {
            // @ts-ignore
            var createRsp = yield Editor.Message.request('asset-db', 'create-asset', assetPath, jsonData);
          })();
        }
        /**
         * 更新现有资源
         */


        _saveAsset(uuid, jsonData) {
          var _this2 = this;

          return _asyncToGenerator(function* () {
            // @ts-ignore
            var rsp = yield Editor.Message.send('asset-db', 'save-asset', uuid, jsonData);

            _this2._refreshAssetDb();
          })();
        }
        /**
         * 刷新资源数据库
         */


        _refreshAssetDb() {
          return _asyncToGenerator(function* () {
            // @ts-ignore
            Editor.Message.send('asset-db', 'refresh-asset', "db://assets/resources/game/level/background/Prefab/Config");
          })();
        }

      }) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=935e22721775bc8193e4b386d0ee2bbfd0bb5fee.js.map