[{"__type__": "cc.Prefab", "_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_HighSky_Clouds_Lot", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_HighSky_Clouds_Lot", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [{"__id__": 5}, {"__id__": 7}], "_prefab": {"__id__": 15}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 3}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 2}, "asset": {"__uuid__": "353c3f21-1aba-48d1-8ba9-7f8f4d4bd2d9", "__expectedType__": "cc.Prefab"}, "fileId": "f5ZM3pF2tByaLz0xZKaGHm", "instance": {"__id__": 4}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "5cd47++6NJYrdZ7Rc0KvyH", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 6}, "_contentSize": {"__type__": "cc.Size", "width": 125, "height": 117}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "90qaFyCRRLMYRob8KMgc/Q"}, {"__type__": "b1dcb5RE6ZN+7KA2ed/t53E", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 8}, "_bfollow": false, "emittier": {"__uuid__": "353c3f21-1aba-48d1-8ba9-7f8f4d4bd2d9", "__expectedType__": "cc.Prefab"}, "strategy": 0, "emittierElements": [{"__uuid__": "3b0ac7d3-b3e7-4ef7-9910-2eaaf0f37d74", "__expectedType__": "cc.Prefab"}, {"__uuid__": "e434e297-5320-4263-880d-aa2fc06b01cc", "__expectedType__": "cc.Prefab"}], "type": 1, "value": 0, "valueModify": {"__id__": 9}, "initDelay": 0, "delayModify": {"__id__": 10}, "interval": 0, "intervalModify": {"__id__": 11}, "angle": 270, "angleModify": {"__id__": 12}, "speed": 200, "speedModify": {"__id__": 13}, "offSetX": {"__id__": 14}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d8/LPR9HRIYLS5VT/Lzndb"}, {"__type__": "SerializableRandomRange", "min": 0, "max": 0}, {"__type__": "SerializableRandomRange", "min": 1000, "max": 3000}, {"__type__": "SerializableRandomRange", "min": 2000, "max": 3600}, {"__type__": "SerializableRandomRange", "min": 0, "max": 0}, {"__type__": "SerializableRandomRange", "min": 50, "max": 100}, {"__type__": "SerializableRandomRange", "min": -300, "max": 300}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3c4/l6yJJHU4M9UaoyAHFC", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": [{"__id__": 2}]}]