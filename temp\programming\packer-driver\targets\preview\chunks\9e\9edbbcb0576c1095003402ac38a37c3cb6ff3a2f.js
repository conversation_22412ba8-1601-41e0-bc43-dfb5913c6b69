System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, error, MyApp, AttributeConst, AttributeData, EnemyData, _crd;

  function _reportPossibleCrUseOfResEnemy(extras) {
    _reporterNs.report("ResEnemy", "../../autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/bundles/common/script/app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAttributeConst(extras) {
    _reporterNs.report("AttributeConst", "db://assets/bundles/common/script/const/AttributeConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAttributeData(extras) {
    _reporterNs.report("AttributeData", "db://assets/bundles/common/script/data/base/AttributeData", _context.meta, extras);
  }

  _export("EnemyData", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      error = _cc.error;
    }, function (_unresolved_2) {
      MyApp = _unresolved_2.MyApp;
    }, function (_unresolved_3) {
      AttributeConst = _unresolved_3.AttributeConst;
    }, function (_unresolved_4) {
      AttributeData = _unresolved_4.AttributeData;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "254ebYdpPJGcaAylvfAz4Zc", "EnemyData", undefined);

      __checkObsolete__(['error']);

      _export("EnemyData", EnemyData = class EnemyData extends (_crd && AttributeData === void 0 ? (_reportPossibleCrUseOfAttributeData({
        error: Error()
      }), AttributeData) : AttributeData) {
        constructor() {
          super(...arguments);
          this._planeId = 0;
          //飞机id
          this.config = null;
        }

        //飞机静态配置
        get planeId() {
          return this._planeId;
        }

        set planeId(value) {
          if (value != this._planeId) {
            this._planeId = value;
            this.config = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).lubanTables.TbResEnemy.get(this._planeId);
            this.updateData();
          }
        }

        get recourseSpine() {
          if (!this.config) {
            return "";
          }
        }

        updateData() {
          if (!this.config) {
            error("enemyPlane " + this._planeId + ": config is null, cannot update attributes.");
            return;
          }

          this.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).MaxHPOutAdd, this.config.baseHp);
          this.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).AttackOutAdd, this.config.baseAtk);
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=9edbbcb0576c1095003402ac38a37c3cb6ff3a2f.js.map