System.register(["__unresolved_0", "cc", "cc/env", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Graphics, SpriteFrame, assetManager, EDITOR, autoRegisterGizmoDrawers, _dec, _dec2, _dec3, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _class3, _crd, ccclass, property, executeInEditMode, menu, GizmoManager;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfGizmoDrawer(extras) {
    _reporterNs.report("GizmoDrawer", "./GizmoDrawer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfautoRegisterGizmoDrawers(extras) {
    _reporterNs.report("autoRegisterGizmoDrawers", "./GizmoDrawer", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Graphics = _cc.Graphics;
      SpriteFrame = _cc.SpriteFrame;
      assetManager = _cc.assetManager;
    }, function (_ccEnv) {
      EDITOR = _ccEnv.EDITOR;
    }, function (_unresolved_2) {
      autoRegisterGizmoDrawers = _unresolved_2.autoRegisterGizmoDrawers;
    }, function (_unresolved_3) {}, function (_unresolved_4) {}],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "35b7e0iBnFHtqqvAd1SurM7", "GizmoManager", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Graphics', 'Node', 'find', 'Color', 'SpriteFrame', 'ImageAsset', 'assetManager', 'Sprite']); // Import gizmo classes to ensure their decorators are executed


      ({
        ccclass,
        property,
        executeInEditMode,
        menu
      } = _decorator); // Scene events interface for editor integration

      /**
       * Global gizmo manager that handles all gizmo drawing
       * Should be attached to a global node in the scene
       * Implements ISceneEvents for automatic refresh on scene changes
       */
      _export("GizmoManager", GizmoManager = (_dec = ccclass('GizmoManager'), _dec2 = menu('Gizmos/GizmoManager'), _dec3 = executeInEditMode(true), _dec(_class = _dec2(_class = _dec3(_class = (_class2 = (_class3 = class GizmoManager extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "gizmosEnabled", _descriptor, this);

          _initializerDefineProperty(this, "drawInPlayMode", _descriptor2, this);

          _initializerDefineProperty(this, "refreshRate", _descriptor3, this);

          // FPS for gizmo updates
          _initializerDefineProperty(this, "maxDrawDistance", _descriptor4, this);

          // Maximum distance to draw gizmos
          // Graphics component for drawing
          this.graphics = null;
          // Update timer
          this.updateTimer = 0;
          this.updateInterval = 1 / 60;
          // Default 60 FPS
          // Scene change detection
          this.lastSceneNodeCount = 0;
          this.sceneChangeCheckInterval = 0.1;
          // Check every 100ms
          this.sceneChangeTimer = 0;
        }

        static get Instance() {
          return GizmoManager.instance;
        } // Sprite frame cache for icons


        onLoad() {
          if (!EDITOR && !this.drawInPlayMode) return; // Set as singleton instance

          GizmoManager.instance = this; // Ensure the gizmo manager node is positioned at origin for proper coordinate mapping

          this.node.setPosition(0, 0, 0);
          this.node.setRotation(0, 0, 0, 1);
          this.node.setScale(1, 1, 1); // Get or create Graphics component

          this.graphics = this.getComponent(Graphics) || this.addComponent(Graphics); // Update refresh interval

          this.updateInterval = 1 / this.refreshRate; // Auto-register all decorated gizmo drawers

          GizmoManager.autoRegisterDrawers(); // Initialize scene change detection

          var rootNodes = this.findAllRootNodes();
          this.lastSceneNodeCount = this.countAllNodesRecursive(rootNodes); // Register for editor scene events if available

          this.registerSceneEvents();
          this.registerSelectionEvents();
        }

        onDestroy() {
          if (GizmoManager.instance === this) {
            GizmoManager.instance = null;
          } // Unregister scene events


          this.unregisterSceneEvents();
        }

        update(deltaTime) {
          if (!EDITOR && !this.drawInPlayMode) return;
          if (!this.graphics) return; // Check for scene changes periodically

          this.sceneChangeTimer += deltaTime;
          var forceRedraw = false;

          if (this.sceneChangeTimer >= this.sceneChangeCheckInterval) {
            this.sceneChangeTimer = 0;
            forceRedraw = this.checkForSceneChanges();
          } // Throttle updates based on refresh rate (unless forced by scene change)


          this.updateTimer += deltaTime;
          if (!forceRedraw && this.updateTimer < this.updateInterval) return;
          this.updateTimer = 0;

          if (this.gizmosEnabled) {
            this.drawAllGizmos();
          } else {
            // Clear graphics when gizmos are disabled
            this.graphics.clear();
          }
        }
        /**
         * Draw all gizmos for all registered drawers
         */


        drawAllGizmos() {
          if (!this.graphics) return; // Clear previous drawings

          this.graphics.clear(); // Get all drawers sorted by priority

          var sortedDrawers = Array.from(GizmoManager.drawers.values()).filter(drawer => drawer.enabled).sort((a, b) => a.getPriority() - b.getPriority()); // Find all nodes in the scene

          var rootNodes = this.findAllRootNodes(); // Draw gizmos for each drawer

          for (var drawer of sortedDrawers) {
            this.drawGizmosForDrawer(drawer, rootNodes);
          }
        }
        /**
         * Draw gizmos for a specific drawer
         */


        drawGizmosForDrawer(drawer, rootNodes) {
          if (!this.graphics) return;
          var componentsToProcess = []; // Find all components of the drawer's type

          for (var rootNode of rootNodes) {
            this.findComponentsRecursive(rootNode, drawer, componentsToProcess);
          } // Draw gizmos for each component


          for (var {
            component,
            node: _node2
          } of componentsToProcess) {
            try {
              drawer.drawGizmos(component, this.graphics, _node2);
            } catch (error) {
              console.error("GizmoManager: Error drawing gizmos for " + drawer.drawerName + ":", error);
            }
          }
        }
        /**
         * Recursively find components that match the drawer's type
         */


        findComponentsRecursive(node, drawer, results) {
          // Check distance from gizmo manager
          var distance = node.worldPosition.subtract(this.node.worldPosition).length();
          if (distance > this.maxDrawDistance) return; // Check components on this node

          var components = node.getComponents(Component);

          for (var component of components) {
            if (drawer.canHandle(component)) {
              results.push({
                component,
                node
              });
            }
          } // Recursively check children


          for (var child of node.children) {
            this.findComponentsRecursive(child, drawer, results);
          }
        }
        /**
         * Check if the scene has changed (nodes added/removed)
         * Returns true if a redraw should be forced
         */


        checkForSceneChanges() {
          var rootNodes = this.findAllRootNodes();
          var currentNodeCount = this.countAllNodesRecursive(rootNodes);

          if (currentNodeCount !== this.lastSceneNodeCount) {
            this.lastSceneNodeCount = currentNodeCount;
            return true; // Force redraw
          }

          return false;
        }
        /**
         * Count all nodes recursively in the scene
         */


        countAllNodesRecursive(nodes) {
          var count = nodes.length;

          for (var _node3 of nodes) {
            count += this.countAllNodesRecursive(_node3.children);
          }

          return count;
        }
        /**
         * Find all root nodes in the scene
         */


        findAllRootNodes() {
          var scene = this.node.scene;
          if (!scene) return [];
          return scene.children.filter(child => child !== this.node);
        }
        /**
         * Register a gizmo drawer
         */


        static registerDrawer(drawer) {
          var key = drawer.drawerName;

          if (GizmoManager.drawers.has(key)) {
            console.warn("GizmoManager: Drawer " + key + " is already registered");
            return;
          }

          GizmoManager.drawers.set(key, drawer);
          drawer.onRegister();

          if (EDITOR) {
            console.log("GizmoManager: Registered drawer " + key);
          }
        }
        /**
         * Unregister a gizmo drawer
         */


        static unregisterDrawer(drawerName) {
          var drawer = GizmoManager.drawers.get(drawerName);
          if (!drawer) return false;
          drawer.onUnregister();
          GizmoManager.drawers.delete(drawerName);

          if (EDITOR) {
            console.log("GizmoManager: Unregistered drawer " + drawerName);
          }

          return true;
        }
        /**
         * Get a registered drawer by name
         */


        static getDrawer(drawerName) {
          return GizmoManager.drawers.get(drawerName) || null;
        }
        /**
         * Get all registered drawers
         */


        static getAllDrawers() {
          return Array.from(GizmoManager.drawers.values());
        }
        /**
         * Get the singleton instance
         */


        static getInstance() {
          return GizmoManager.instance;
        }
        /**
         * Auto-register all decorated gizmo drawers
         */


        static autoRegisterDrawers() {
          (_crd && autoRegisterGizmoDrawers === void 0 ? (_reportPossibleCrUseOfautoRegisterGizmoDrawers({
            error: Error()
          }), autoRegisterGizmoDrawers) : autoRegisterGizmoDrawers)(drawer => {
            GizmoManager.registerDrawer(drawer);
          });
        }
        /**
         * Force refresh all gizmos and reset scene change detection
         */


        static refresh() {
          var instance = GizmoManager.getInstance();

          if (instance) {
            // Reset scene change detection to force immediate update
            instance.lastSceneNodeCount = -1;
            instance.drawAllGizmos();
          }
        }
        /**
         * Force immediate gizmo refresh (useful after scene modifications)
         */


        static forceRefresh() {
          var instance = GizmoManager.getInstance();

          if (instance) {
            // Update node count and force redraw
            var rootNodes = instance.findAllRootNodes();
            instance.lastSceneNodeCount = instance.countAllNodesRecursive(rootNodes);
            instance.drawAllGizmos();
          }
        } // ========== Scene Events Implementation ==========

        /**
         * Register for editor scene events
         */


        registerSceneEvents() {
          if (!EDITOR) return;

          try {
            // Try to register with the editor's scene event system
            // This is a best-effort approach as the API may vary
            var editorExtends = globalThis.EditorExtends;

            if (editorExtends && editorExtends.on) {
              editorExtends.on('scene:node-added', this.onNodeAdded.bind(this));
              editorExtends.on('scene:node-removed', this.onNodeRemoved.bind(this));
              editorExtends.on('scene:component-added', this.onComponentAdded.bind(this));
              editorExtends.on('scene:component-removed', this.onComponentRemoved.bind(this));
            }
          } catch (error) {// Silent fallback - scene events not available
          }
        }
        /**
         * Unregister scene events
         */


        unregisterSceneEvents() {
          if (!EDITOR) return;

          try {
            var editorExtends = globalThis.EditorExtends;

            if (editorExtends && editorExtends.removeListener) {
              editorExtends.removeListener('scene:node-added', this.onNodeAdded.bind(this));
              editorExtends.removeListener('scene:node-removed', this.onNodeRemoved.bind(this));
              editorExtends.removeListener('scene:component-added', this.onComponentAdded.bind(this));
              editorExtends.removeListener('scene:component-removed', this.onComponentRemoved.bind(this));
            }
          } catch (error) {// Silent fallback
          }
        }

        registerSelectionEvents() {
          if (!EDITOR) return; // 这个暂时没法注册成功, 估计需要
          // try {
          //     // @ts-ignore
          //     Editor.Ipc.addListener('_selection:selected', (event, uuids) => {
          //         console.log('Selection changed:', uuids);
          //     });
          // } catch (error) {
          //     console.warn('GizmoManager: Failed to register selection events', error);
          // }
        }
        /**
         * Called when a node is added to the scene
         */


        onNodeAdded(_node, _opts) {
          // Force immediate refresh when nodes are added
          this.lastSceneNodeCount = -1; // Force count update

          GizmoManager.forceRefresh();
        }
        /**
         * Called when a node is removed from the scene
         */


        onNodeRemoved(_node, _opts) {
          // Force immediate refresh when nodes are removed
          this.lastSceneNodeCount = -1; // Force count update

          GizmoManager.forceRefresh();
        }
        /**
         * Called when a component is added to a node
         */


        onComponentAdded(comp, _opts) {
          // Check if it's a component type we care about
          for (var drawer of GizmoManager.drawers.values()) {
            if (drawer.canHandle(comp)) {
              // This is a component we draw gizmos for, force refresh
              GizmoManager.forceRefresh();
              break;
            }
          }
        }
        /**
         * Called when a component is removed from a node
         */


        onComponentRemoved(comp, _opts) {
          // Check if it's a component type we care about
          for (var drawer of GizmoManager.drawers.values()) {
            if (drawer.canHandle(comp)) {
              // This is a component we draw gizmos for, force refresh
              GizmoManager.forceRefresh();
              break;
            }
          }
        }
        /**
         * Load sprite frame with caching
         */


        static loadSpriteFrame(iconPath) {
          return _asyncToGenerator(function* () {
            // Check cache first
            if (GizmoManager.spriteFrameCache.has(iconPath)) {
              return GizmoManager.spriteFrameCache.get(iconPath) || null;
            } // Check if already loading


            if (GizmoManager.loadingPromises.has(iconPath)) {
              return GizmoManager.loadingPromises.get(iconPath) || null;
            } // Start loading


            var loadingPromise = GizmoManager.loadSpriteFrameInternal(iconPath);
            GizmoManager.loadingPromises.set(iconPath, loadingPromise);
            var result = yield loadingPromise; // Cache the result and remove from loading promises

            GizmoManager.spriteFrameCache.set(iconPath, result);
            GizmoManager.loadingPromises.delete(iconPath);
            return result;
          })();
        }
        /**
         * Internal method to load sprite frame
         */


        static loadSpriteFrameInternal(iconPath) {
          return _asyncToGenerator(function* () {
            try {
              var fullPath = "db://assets/editor/icons/" + iconPath; // @ts-ignore

              var uuid = yield Editor.Message.request('asset-db', 'query-uuid', fullPath);

              if (!uuid) {
                console.warn("Failed to query uuid for icon: " + fullPath);
                return null;
              }

              return new Promise(resolve => {
                assetManager.loadAny(uuid, (err, asset) => {
                  if (err) {
                    console.warn("Failed to load icon: " + fullPath, err);
                    resolve(null);
                    return;
                  }

                  var spriteFrame = SpriteFrame.createWithImage(asset);
                  resolve(spriteFrame);
                });
              });
            } catch (error) {
              console.warn("Failed to load icon: " + iconPath, error);
              return null;
            }
          })();
        }
        /**
         * Load and apply sprite frame to a sprite component
         */


        static loadAndApplySpriteFrame(iconPath, sprite) {
          return _asyncToGenerator(function* () {
            try {
              var spriteFrame = yield GizmoManager.loadSpriteFrame(iconPath); // Apply to sprite if still valid

              if (sprite && sprite.isValid && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
              }
            } catch (error) {
              console.warn("Failed to load and apply sprite frame for " + iconPath + ":", error);
            }
          })();
        }
        /**
         * Clear sprite frame cache
         */


        static clearSpriteFrameCache() {
          GizmoManager.spriteFrameCache.clear();
          GizmoManager.loadingPromises.clear();
        }

      }, _class3.drawers = new Map(), _class3.instance = null, _class3.spriteFrameCache = new Map(), _class3.loadingPromises = new Map(), _class3), (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "gizmosEnabled", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return true;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "drawInPlayMode", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return false;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "refreshRate", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 60;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "maxDrawDistance", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 2000;
        }
      })), _class2)) || _class) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=e3e9e6cc8498998ec9a359a29bbd67e5325f18a6.js.map