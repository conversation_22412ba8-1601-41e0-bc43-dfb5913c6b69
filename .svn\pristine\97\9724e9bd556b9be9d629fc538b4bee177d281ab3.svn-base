"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.methods = void 0;
exports.load = load;
exports.unload = unload;
const path_1 = require("path");
// @ts-ignore
module.paths.push((0, path_1.join)(Editor.App.path, 'node_modules'));
// 当前版本需要在 module.paths 修改后才能正常使用 cc 模块
// 并且如果希望正常显示 cc 的定义，需要手动将 engine 文件夹里的 cc.d.ts 添加到插件的 tsconfig 里
// 当前版本的 cc 定义文件可以在当前项目的 temp/declarations/cc.d.ts 找到
const cc_1 = require("cc");
const { _utils } = cc_1.Prefab;
function load() { }
;
function unload() { }
;
exports.methods = {
    saveLevel() {
        console.log('saveLevel in scene');
        const { director } = require('cc');
        let levelEditorUI = director.getScene().getComponentInChildren("LevelEditorUI");
        if (levelEditorUI) {
            levelEditorUI.save = true;
        }
    },
    playLevel() {
        console.log('playLevel in scene');
        const { director } = require('cc');
        let levelEditorUI = director.getScene().getComponentInChildren("LevelEditorUI");
        if (levelEditorUI) {
            var play = levelEditorUI.play;
            levelEditorUI.play = !play;
        }
    },
    levelStart() {
        console.log('levelStart in scene');
        const { director } = require('cc');
        let levelEditorUI = director.getScene().getComponentInChildren("LevelEditorUI");
        if (levelEditorUI) {
            levelEditorUI.progress = 0;
        }
    },
    levelEnd() {
        console.log('levelEnd in scene');
        const { director } = require('cc');
        let levelEditorUI = director.getScene().getComponentInChildren("LevelEditorUI");
        if (levelEditorUI) {
            levelEditorUI.progress = 1;
        }
    },
    instantiatePrefab(component_uuid, prefabUuid) {
        // console.log('instantiatePrefab:', component_uuid, prefabUuid);
        var _a, _b, _c;
        let targetNode = (_a = cc_1.director.getScene()) === null || _a === void 0 ? void 0 : _a.getChildByUuid(component_uuid);
        if (!targetNode) {
            targetNode = (_b = cc_1.director.getScene()) === null || _b === void 0 ? void 0 : _b.getChildByName('Canvas');
        }
        if (targetNode) {
            // console.log("Canvas node found: ", targetNode.getComponent("EmitterEditor"));
            // @ts-ignore
            Editor.Message.request('scene', 'execute-component-method', {
                uuid: (_c = targetNode.getComponent("EmitterEditor")) === null || _c === void 0 ? void 0 : _c.uuid,
                name: 'instantiatePrefab',
                args: [prefabUuid]
            });
        }
    },
    // async saveToPrefab(component_uuid:string, nodeName: string, prefabUuid: string) {
    //     // console.log('saveToPrefab:', component_uuid, nodeName, prefabUuid);
    //     const scene = director.getScene();
    //     const target = scene!.getChildByPath(`Canvas/${nodeName}`);
    //     if (!target) {
    //         console.error("node not found:", nodeName);
    //         return;
    //     }
    //     cce.Prefab.createPrefabAssetFromNode(target.uuid, prefabUuid);
    // },
    async createBulletPrefab(sourceAssetUuid) {
        // @ts-ignore
        const sourceAssetInfo = await Editor.Message.request('asset-db', 'query-asset-info', sourceAssetUuid);
        if (!sourceAssetInfo)
            return;
        // 需要判断是spriteList还是单个sprite
        // TODO: 当sourceAssetUuid是一个目录的时候，下面这里的路径可能存在bug。还没有验证。
        // TODO: 这个目录后续可能调整到bundle目录
        const targetPrefabDir = 'db://assets/resources/game/prefabs/bullet/';
        if (sourceAssetInfo.importer === 'sprite-atlas') {
            cc_1.assetManager.loadAny({ uuid: sourceAssetUuid }, (err, asset) => {
                if (err) {
                    console.error(err);
                    return;
                }
                const spriteList = asset;
                for (let spriteFrame of spriteList.getSpriteFrames()) {
                    const targetPrefabName = spriteFrame.name;
                    const targetPrefabPath = targetPrefabDir + targetPrefabName + '.prefab';
                    createNewBullet(targetPrefabName, targetPrefabPath, spriteFrame, spriteList);
                }
            });
        }
        else if (sourceAssetInfo.importer === 'sprite-frame') {
            cc_1.assetManager.loadAny({ uuid: sourceAssetUuid }, (err, asset) => {
                if (err) {
                    console.error(err);
                    return;
                }
                const spriteFrame = asset;
                const targetPrefabName = spriteFrame.name;
                const targetPrefabPath = targetPrefabDir + targetPrefabName + '.prefab';
                createNewBullet(targetPrefabName, targetPrefabPath, spriteFrame, null);
            });
        }
        else {
            console.warn('Skipping unknown asset type:', sourceAssetInfo.importer);
        }
    },
    async createLevelPrefab(sourceAssetUuid) {
        // @ts-ignore
        const sourceAssetInfo = await Editor.Message.request('asset-db', 'query-asset-info', sourceAssetUuid);
        if (!sourceAssetInfo)
            return;
        // remove /x/x/*.* -> /x/x/
        let targetPrefabDir = sourceAssetInfo.path.replace('/Texture/', '/Prefab/');
        const targetPrefabPath = targetPrefabDir.substring(0, targetPrefabDir.lastIndexOf('/')) + '.prefab';
        const targetPrefabName = targetPrefabDir.substring(targetPrefabDir.lastIndexOf('/') + 1);
        if (sourceAssetInfo.importer === 'sprite-frame') {
            cc_1.assetManager.loadAny({ uuid: sourceAssetUuid }, (err, asset) => {
                if (err) {
                    console.error(err);
                    return;
                }
                // console.log(`Level Prefab will be created at path ${targetPrefabPath}`);
                const spriteFrame = asset;
                if (spriteFrame) {
                    createNewLevelObject(targetPrefabName, targetPrefabPath, spriteFrame); // 这个是创建prefab，不是prefab里面的节点，所以不需要parent
                }
                else {
                    console.warn('Asset is not a sprite-frame:', sourceAssetInfo);
                }
            });
        }
        else {
            console.warn('Skipping unknown asset type:', sourceAssetInfo.importer);
        }
    },
    // 阵型相关: addFormationPoint & saveFormationGroup
    addFormationPoint(formationGroupUuid) {
        const { director } = require('cc');
        let formationEditor = findComponentByUuid(director.getScene(), formationGroupUuid, 'FormationEditor');
        if (formationEditor) {
            // @ts-ignore
            formationEditor.addNewPoint(0, 0);
        }
        else {
            console.error('Formation group not found:', formationGroupUuid);
        }
    },
    async saveFormationGroup(formationGroupUuid) {
        const { director } = require('cc');
        let formationEditor = findComponentByUuid(director.getScene(), formationGroupUuid, 'FormationEditor');
        if (formationEditor) {
            // @ts-ignore
            const jsonData = formationEditor.formationData;
            // save this as JsonAsset
            if (jsonData === null) {
                // @ts-ignore
                let file = await Editor.Dialog.save({
                    path: 'db://assets/resources/game/level/wave/formation/',
                    filters: [
                        { name: 'Json', extensions: ['json'] },
                    ],
                });
                if (file.canceled || !file.filePath) {
                    return;
                }
                // @ts-ignore
                Editor.Message.request('asset-db', 'create-asset', file.filePath, formationEditor.save()).then((res) => {
                    if (res) {
                        cc_1.assetManager.loadAny({ uuid: res.uuid }, (err, asset) => {
                            if (err) {
                                console.error(err);
                            }
                            else {
                                // @ts-ignore 
                                formationEditor.formationData = asset;
                            }
                        });
                    }
                });
            }
            else {
                // @ts-ignore
                Editor.Message.request('asset-db', 'save-asset', jsonData.uuid, formationEditor.save()).then((res) => {
                    if (res) {
                        cc_1.assetManager.loadAny({ uuid: res.uuid }, (err, asset) => {
                            if (err) {
                                console.error(err);
                            }
                            else {
                                // @ts-ignore 
                                formationEditor.formationData = asset;
                            }
                        });
                    }
                });
            }
        }
        else {
            console.error('Formation group not found:', formationGroupUuid);
        }
    },
    // 路径相关: addPathPoint & savePath & clearPath
    addPathPoint(pathEditorUuid) {
        const { director } = require('cc');
        let pathEditor = findComponentByUuid(director.getScene(), pathEditorUuid, 'PathEditor');
        if (pathEditor) {
            // @ts-ignore
            pathEditor.addNewPoint(0, 0);
        }
        else {
            console.error('Path editor not found:', pathEditorUuid);
        }
    },
    async savePath(pathEditorUuid) {
        const { director } = require('cc');
        let pathEditor = findComponentByUuid(director.getScene(), pathEditorUuid, 'PathEditor');
        if (pathEditor) {
            // @ts-ignore
            const jsonData = pathEditor.pathData;
            // save this as JsonAsset
            if (jsonData === null) {
                // @ts-ignore
                let file = await Editor.Dialog.save({
                    path: 'db://assets/resources/game/level/wave/path/',
                    filters: [
                        { name: 'Json', extensions: ['json'] },
                    ],
                });
                if (file.canceled || !file.filePath) {
                    return;
                }
                // @ts-ignore
                Editor.Message.request('asset-db', 'create-asset', file.filePath, pathEditor.save()).then((res) => {
                    if (res) {
                        cc_1.assetManager.loadAny({ uuid: res.uuid }, (err, asset) => {
                            if (err) {
                                console.error(err);
                            }
                            else {
                                // @ts-ignore
                                pathEditor.pathData = asset;
                            }
                        });
                    }
                });
            }
            else {
                // @ts-ignore
                Editor.Message.request('asset-db', 'save-asset', jsonData.uuid, pathEditor.save()).then((res) => {
                    if (res) {
                        cc_1.assetManager.loadAny({ uuid: res.uuid }, (err, asset) => {
                            if (err) {
                                console.error(err);
                            }
                            else {
                                // @ts-ignore
                                pathEditor.pathData = asset;
                            }
                        });
                    }
                });
            }
        }
        else {
            console.error('Path editor not found:', pathEditorUuid);
        }
    },
    clearPath(pathEditorUuid) {
        const { director } = require('cc');
        let pathEditor = findComponentByUuid(director.getScene(), pathEditorUuid, 'PathEditor');
        if (pathEditor) {
            // @ts-ignore
            pathEditor.node.removeAllChildren();
            // @ts-ignore
            pathEditor.updateCurve();
        }
        else {
            console.error('Path editor not found:', pathEditorUuid);
        }
    },
};
function findComponentByUuid(node, uuid, className) {
    if (!node)
        return null;
    // 检查当前节点是否有指定的组件，如果有且UUID匹配则返回
    const component = node.getComponent(className);
    if (component) {
        // console.log(`${node.name} has component ${className}, uuid: ${component.uuid}, target: ${uuid}`);
        if (component.uuid === uuid) {
            return component;
        }
    }
    // 无论当前节点是否有组件，都要搜索子节点
    for (let i = 0; i < node.children.length; i++) {
        let found = findComponentByUuid(node.children[i], uuid, className);
        if (found) {
            return found;
        }
    }
    return null;
}
async function createNewBullet(prefabName, prefabPath, sourceSprite, sourceSpriteList) {
    const scene = cc_1.director.getScene();
    const target = scene.getChildByName('Canvas');
    if (!target) {
        console.error("Canvas node not found");
        return;
    }
    let node = new cc_1.Node(prefabName);
    node.parent = target;
    node.setPosition(new cc_1.Vec3(0, 0, 0));
    let uiTransform = node.addComponent(cc_1.UITransform); // Ensure it has a transform component
    node.addComponent('FBoxCollider');
    let movable = node.addComponent('DefaultMove');
    node.addComponent('Bullet');
    node.addComponent('cc.BoxCollider2D');
    let sprite = node.addComponent(cc_1.Sprite);
    if (sourceSpriteList) {
        sprite.spriteFrame = sourceSprite;
        sprite.spriteAtlas = sourceSpriteList;
    }
    if (sourceSprite) {
        uiTransform.contentSize = new cc_1.Size(sourceSprite.rect.width, sourceSprite.rect.height);
    }
    const nodeUuid = node.uuid;
    try {
        await cce.Prefab.createPrefabAssetFromNode(nodeUuid, prefabPath);
        console.log(`Bullet prefab created: ${prefabPath}`);
        Editor.Message.request('asset-db', 'refresh-asset', prefabPath);
    }
    catch (e) {
        console.error('Failed to create bullet prefab:', e);
    }
}
async function createNewLevelObject(prefabName, prefabPath, sourceSprite) {
    const scene = cc_1.director.getScene();
    let node = new cc_1.Node(prefabName);
    scene.addChild(node);
    let uiTransform = node.addComponent(cc_1.UITransform); // Ensure it has a transform component
    let sprite = node.addComponent(cc_1.Sprite);
    if (sourceSprite) {
        sprite.spriteFrame = sourceSprite;
        uiTransform.contentSize = new cc_1.Size(sourceSprite.rect.width, sourceSprite.rect.height);
    }
    const nodeUuid = node.uuid;
    try {
        await cce.Prefab.createPrefabAssetFromNode(nodeUuid, prefabPath);
        console.log(`Level prefab created: ${prefabPath}`);
        Editor.Message.request('asset-db', 'refresh-asset', prefabPath);
    }
    catch (e) {
        console.error('Failed to create level prefab:', e, ', on asset: ', prefabPath);
    }
}
//# sourceMappingURL=data:application/json;base64,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