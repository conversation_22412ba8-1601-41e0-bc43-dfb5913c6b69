System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, UIToolMgr, _crd, UITools;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "99c161J6F5NM6S9meybVmGt", "UITools", undefined);

      __checkObsolete__(['Label', 'RichText']);

      UIToolMgr = class UIToolMgr {
        /**
         * 将秒数格式化为 "时:分:秒" 的字符串
         * @param totalSeconds 总秒数（非负数）
         * @returns 格式化后的时间字符串，如 "00:00:00"
         */
        formatTime(totalSeconds) {
          if (typeof totalSeconds !== 'number' || totalSeconds < 0) {
            return '00:00';
          }

          const hours = Math.floor(totalSeconds / 3600);
          const minutes = Math.floor(totalSeconds % 3600 / 60);
          const seconds = totalSeconds % 60;

          const pad = num => num.toString().padStart(2, '0');

          if (hours === 0) {
            return `${pad(minutes)}:${pad(seconds)}`;
          }

          return `${pad(hours)}:${pad(minutes)}:${pad(seconds)}`;
        }
        /**
         * 修改富文本原字符串中指定 <color> 标签内的数值
         * @param passText RichText
         * @param index 要修改的 <color> 标签的索引（从 0 开始）
         * @param newValue 新的数值
         */


        modifyColorTag(passText, index, options) {
          if (passText == null || passText.string == null) return;
          const originalString = passText.string;
          const parts = originalString.split(/(<color=[^>]+>)([^<]+)(<\/color>)/g);
          const filteredParts = parts.filter(part => part !== '');

          if (index < 0 || index >= filteredParts.length / 3) {
            throw new Error(`Invalid index: ${index}`);
          }

          const tagIndex = 3 * index;
          const valueIndex = tagIndex + 1;

          if (options.color) {
            filteredParts[tagIndex] = `<color=${options.color}>`;
          }

          if (options.value) {
            filteredParts[valueIndex] = options.value;
          }

          passText.string = filteredParts.join('');
        }
        /**
         * 修改 Label 文本中的数字部分为指定值
         * @param txt 目标 Label 组件
         * @param num 替换的数字（支持 number 或 string 类型）
         */


        modifyNumber(txt, num) {
          if (!txt || num === null || num === undefined) return;
          const replacement = num.toString();

          if (txt.string.match(/\d+/g)) {
            txt.string = txt.string.replace(/\d+/g, replacement);
          }
        }

      };

      _export("UITools", UITools = new UIToolMgr());

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=d3f17c80a7ba3ad703c0c8820e725fecd473d96a.js.map